{"name": "tailwagweb", "version": "1.0.0", "description": "", "private": "true", "scripts": {"build": "export NODE_ENV=production && webpack", "dev": "webpack serve --mode=development --open", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.18.5", "@babel/preset-env": "^7.18.2", "@iconify/json": "^2.2.185", "@iconify/tailwind": "^0.1.4", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/typography": "^0.5.2", "autoprefixer": "^10.4.7", "babel-loader": "^8.2.5", "css-loader": "^6.7.1", "cssnano": "^5.1.11", "daisyui": "^5.0.0", "mini-css-extract-plugin": "^2.6.0", "postcss": "^8.5.3", "postcss-loader": "^7.0.0", "webpack": "^5.89.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.9.2"}, "dependencies": {"@iconify/tailwind4": "^1.0.6", "@tailwindcss/postcss": "^4.0.9", "alpinejs": "^3.10.2", "chart.js": "^4.4.7", "htmx.org": "^1.7.0", "modern-screenshot": "^4.6.0", "tailwindcss": "^4.0.9"}}