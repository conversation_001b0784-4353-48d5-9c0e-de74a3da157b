from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.shortcuts import render, redirect
from django.utils.translation import gettext_lazy as _
from wechat.models import WechatUser

@login_required
def change_first_name(request):
    if request.method == 'POST':
        new_nickname = request.POST.get('nickname', '').strip()
        if new_nickname:
            if len(new_nickname) <= 12:
                # 更新用户昵称
                request.user.first_name = new_nickname
                request.user.save()
                
                # 同步更新微信昵称，但不要反向同步回用户昵称
                wechat_user = WechatUser.objects.filter(user=request.user).first()
                if wechat_user:
                    wechat_user.nickname = new_nickname
                    wechat_user.save(sync_to_user=False)  # 禁用同步避免循环
                
                messages.success(request, _('昵称修改成功！'), extra_tags='nickname_change')
                return redirect('account_change_first_name')
            else:
                messages.error(request, _('昵称长度不能超过12个字符'), extra_tags='nickname_change')
        else:
            messages.error(request, _('昵称不能为空'), extra_tags='nickname_change')
    
    return render(request, 'account/change_first_name.html') 