/*! For license information please see blbnlst.js.LICENSE.txt */
(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){"use strict";e=function(){return r};var r={},n=Object.prototype,o=n.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var o=e&&e.prototype instanceof p?e:p,i=Object.create(o.prototype),a=new k(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var c=S(a,r);if(c){if(c===h)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=f(t,e,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===h)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(t,r,a),i}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}r.wrap=l;var h={};function p(){}function d(){}function y(){}var v={};s(v,a,(function(){return this}));var m=Object.getPrototypeOf,g=m&&m(m(_([])));g&&g!==n&&o.call(g,a)&&(v=g);var w=y.prototype=p.prototype=Object.create(v);function b(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function x(e,r){function n(i,a,c,u){var s=f(e[i],e,a);if("throw"!==s.type){var l=s.arg,h=l.value;return h&&"object"==t(h)&&o.call(h,"__await")?r.resolve(h.__await).then((function(t){n("next",t,c,u)}),(function(t){n("throw",t,c,u)})):r.resolve(h).then((function(t){l.value=t,c(l)}),(function(t){return n("throw",t,c,u)}))}u(s.arg)}var i;this._invoke=function(t,e){function o(){return new r((function(r,o){n(t,e,r,o)}))}return i=i?i.then(o,o):o()}}function S(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,S(t,e),"throw"===e.method))return h;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var n=f(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,h;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function k(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function _(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function e(){for(;++r<t.length;)if(o.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:A}}function A(){return{value:void 0,done:!0}}return d.prototype=y,s(w,"constructor",y),s(y,"constructor",d),d.displayName=s(y,u,"GeneratorFunction"),r.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,s(t,u,"GeneratorFunction")),t.prototype=Object.create(w),t},r.awrap=function(t){return{__await:t}},b(x.prototype),s(x.prototype,c,(function(){return this})),r.AsyncIterator=x,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new x(l(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(w),s(w,u,"Generator"),s(w,a,(function(){return this})),s(w,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},r.values=_,k.prototype={constructor:k,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&o.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),L(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;L(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:_(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},r}function r(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function n(t){return function(){var e=this,n=arguments;return new Promise((function(o,i){var a=t.apply(e,n);function c(t){r(a,o,i,c,u,"next",t)}function u(t){r(a,o,i,c,u,"throw",t)}c(void 0)}))}}function o(){var t=null;if(document.cookie&&""!==document.cookie)for(var e=document.cookie.split(";"),r=0;r<e.length;r++){var n=e[r].trim();if("csrftoken="===n.substring(0,10)){t=decodeURIComponent(n.substring(10));break}}return t}document.addEventListener("alpine:init",(function(){Alpine.store("brewlogList",{currentStatus:null,filterByStatus:function(t){this.currentStatus===t?(this.currentStatus=null,this.showAllBeans()):(this.currentStatus=t,this.filterBeans(t))},filterBeans:function(t){var e=this;document.querySelectorAll(".grid-cols-1.grid-rows-1 > div").forEach((function(r){var n=e.checkBeanStatus(r,t);r.style.display=n?"":"none"}));var r=document.querySelector(".collapse-plus");r&&(r.style.display="none"),this.handleEmptyGroups()},showAllBeans:function(){document.querySelectorAll(".grid-cols-1.grid-rows-1 > div").forEach((function(t){t.style.display=""}));var t=document.querySelector(".collapse-plus");t&&(t.style.display=""),this.handleEmptyGroups()},checkBeanStatus:function(t,e){var r=t.querySelectorAll(".tooltip");switch(e){case"resting":return Array.from(r).some((function(t){return"养豆中"===t.getAttribute("data-tip")}));case"out_of_stock":return Array.from(r).some((function(t){return"已用完（未归档）"===t.getAttribute("data-tip")}));case"in_use":return Array.from(r).some((function(t){return"使用中"===t.getAttribute("data-tip")}));default:return!0}},handleEmptyGroups:function(){document.querySelectorAll(".grid-cols-1.grid-rows-1").forEach((function(t){var e=Array.from(t.children).filter((function(t){return"none"!==t.style.display})),r=t.previousElementSibling;r&&r.classList.contains("divider")&&(r.style.display=e.length?"":"none"),t.style.display=e.length?"":"none"}))},deleteBean:function(t){return n(e().mark((function r(){var n;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/my/brewlog/bean/".concat(t,"/delete/"),{method:"POST",headers:{"X-CSRFToken":o()}});case 3:return n=e.sent,e.next=6,n.json();case 6:"success"===e.sent.status&&(window.location.href="/my/brewlog/bean/"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("Error:",e.t0),alert("删除失败");case 14:case"end":return e.stop()}}),r,null,[[0,10]])})))()},archiveBean:function(t){return n(e().mark((function r(){var n;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/my/brewlog/bean/".concat(t,"/archive/"),{method:"POST",headers:{"X-CSRFToken":o()}});case 3:return n=e.sent,e.next=6,n.json();case 6:"success"===e.sent.status&&(window.location.href="/my/brewlog/bean/"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("Error:",e.t0),alert("归档失败");case 14:case"end":return e.stop()}}),r,null,[[0,10]])})))()},unarchiveBean:function(t){return n(e().mark((function r(){var n;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/my/brewlog/bean/".concat(t,"/unarchive/"),{method:"POST",headers:{"X-CSRFToken":o()}});case 3:return n=e.sent,e.next=6,n.json();case 6:"success"===e.sent.status&&(window.location.href="/my/brewlog/bean/"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("Error:",e.t0),alert("取消归档失败");case 14:case"end":return e.stop()}}),r,null,[[0,10]])})))()},toggleFavoriteBean:function(t){var r=this;return n(e().mark((function n(){var i,a;return e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!(i=document.querySelector('[data-bean-id="'.concat(t,'"]')))||null===i.closest(".collapse-content")){e.next=6;break}return e.next=6,r.unarchiveBean(t);case 6:return e.next=8,fetch("/my/brewlog/bean/".concat(t,"/toggle-favorite/"),{method:"POST",headers:{"X-CSRFToken":o()}});case 8:return a=e.sent,e.next=11,a.json();case 11:"success"===e.sent.status&&(window.location.href="/my/brewlog/bean/"),e.next=19;break;case 15:e.prev=15,e.t0=e.catch(0),console.error("Error:",e.t0),alert("操作失败，请稍后再试");case 19:case"end":return e.stop()}}),n,null,[[0,15]])})))()}})}))})();