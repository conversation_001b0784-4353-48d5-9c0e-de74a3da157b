document.addEventListener("DOMContentLoaded",(function(){if(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream){var e="ios_popup_shown";localStorage.getItem(e)||setTimeout((function(){var n=document.createElement("div");n.className="fixed inset-0 z-50 flex items-center justify-center",n.innerHTML='\n                    <div class="absolute inset-0 bg-black/50 z-40"></div> \x3c!-- 半透明背景 --\x3e\n                    <div class="bg-base-100 rounded-lg shadow-lg w-80 p-4 z-50"> \x3c!-- 弹框 --\x3e\n                        <div class="text-xl font-semibold text-center mb-4">添加 咖啡搭子 到桌面</div>\n                        <div class="mb-4">\n                            <div class="text-base font-medium mb-2">1. 点击浏览器分享图标</div>\n                            <img \n                                class="mx-auto" \n                                loading="lazy" \n                                width="227" \n                                height="51" \n                                decoding="async" \n                                src="https://pic.kafeidazi.com/base/add-to-screen-1%403x.png" \n                            />\n                        </div>\n                        <div class="mb-4">\n                            <div class="text-base font-medium mb-2">2. 选择添加到主屏幕</div>\n                            <img \n                                class="mx-auto" \n                                loading="lazy" \n                                width="227" \n                                height="40" \n                                decoding="async" \n                                src="https://pic.kafeidazi.com/base/add-to-screen-2%403x.png" \n                            />\n                        </div>\n                        <div \n                            class="btn hover:bg-primary-focus transition duration-200 w-full" \n                            id="popup-close">\n                            <span>知道了</span>\n                        </div>\n                    </div>\n                ',document.body.appendChild(n),n.addEventListener("click",(function(t){t.target!==n&&"popup-close"!==t.target.id||(n.remove(),localStorage.setItem(e,"true"))})),n.querySelector(".absolute").addEventListener("click",(function(){n.remove(),localStorage.setItem(e,"true")}))}),15e3)}}));