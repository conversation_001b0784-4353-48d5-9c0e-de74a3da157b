/*! For license information please see blrcplst.js.LICENSE.txt */
(()=>{"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){t=function(){return n};var n={},r=Object.prototype,o=r.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function l(e,t,n,r){var o=t&&t.prototype instanceof h?t:h,a=Object.create(o.prototype),i=new S(r||[]);return a._invoke=function(e,t,n){var r="suspendedStart";return function(o,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw a;return{value:void 0,done:!0}}for(n.method=o,n.arg=a;;){var i=n.delegate;if(i){var c=L(i,n);if(c){if(c===f)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=d(e,t,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===f)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}(e,n,i),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=l;var f={};function h(){}function p(){}function m(){}var g={};u(g,i,(function(){return this}));var w=Object.getPrototypeOf,v=w&&w(w(_([])));v&&v!==r&&o.call(v,i)&&(g=v);var y=m.prototype=h.prototype=Object.create(g);function b(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function x(t,n){function r(a,i,c,s){var u=d(t[a],t,i);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==e(f)&&o.call(f,"__await")?n.resolve(f.__await).then((function(e){r("next",e,c,s)}),(function(e){r("throw",e,c,s)})):n.resolve(f).then((function(e){l.value=e,c(l)}),(function(e){return r("throw",e,c,s)}))}s(u.arg)}var a;this._invoke=function(e,t){function o(){return new n((function(n,o){r(e,t,n,o)}))}return a=a?a.then(o,o):o()}}function L(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,L(e,t),"throw"===t.method))return f;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var r=d(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,f;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function _(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function t(){for(;++n<e.length;)if(o.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:E}}function E(){return{value:void 0,done:!0}}return p.prototype=m,u(y,"constructor",m),u(m,"constructor",p),p.displayName=u(m,s,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,u(e,s,"GeneratorFunction")),e.prototype=Object.create(y),e},n.awrap=function(e){return{__await:e}},b(x.prototype),u(x.prototype,c,(function(){return this})),n.AsyncIterator=x,n.async=function(e,t,r,o,a){void 0===a&&(a=Promise);var i=new x(l(e,t,r,o),a);return n.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},b(y),u(y,s,"Generator"),u(y,i,(function(){return this})),u(y,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},n.values=_,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,r){return i.type="throw",i.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],i=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=o.call(a,"catchLoc"),s=o.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,f):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;k(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:_(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},n}function n(e,t,n,r,o,a,i){try{var c=e[a](i),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,o)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(o,a){var i=e.apply(t,r);function c(e){n(i,o,a,c,s,"next",e)}function s(e){n(i,o,a,c,s,"throw",e)}c(void 0)}))}}function o(){var e=null;if(document.cookie&&""!==document.cookie)for(var t=document.cookie.split(";"),n=0;n<t.length;n++){var r=t[n].trim();if("csrftoken="===r.substring(0,10)){e=decodeURIComponent(r.substring(10));break}}return e}document.addEventListener("alpine:init",(function(){Alpine.store("filterState",{isOpen:window.innerWidth>=768,toggle:function(){this.isOpen=!this.isOpen},init:function(){var e,t=this;this.isOpen=window.innerWidth>=768,window.addEventListener("resize",(function(){clearTimeout(e),e=setTimeout((function(){t.isOpen=window.innerWidth>=768}),100)}))}}),Alpine.store("recipeList",{toggleRecipeTag:function(e,n){return r(t().mark((function r(){var a,i,c,s,u;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return(a=document.querySelector("#manage_recipe_tags_modal_".concat(e,' button[data-tag-id="').concat(n,'"]')))&&(a.classList.add("loading"),a.disabled=!0),t.prev=2,t.next=5,fetch("/my/brewlog/recipes/".concat(e,"/tag/").concat(n,"/toggle/"),{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded","X-CSRFToken":o()}});case 5:return i=t.sent,t.next=8,i.json();case 8:"success"===(c=t.sent).status&&(a&&("added"===c.action?(a.classList.remove("btn-outline","loading"),a.classList.add("btn-primary")):(a.classList.remove("btn-primary","loading"),a.classList.add("btn-outline")),a.disabled=!1),(s=document.querySelector("#recipe_".concat(e)))&&(u=s.querySelector(".tags-container"))&&(c.tags&&c.tags.length>0?u.innerHTML=c.tags.map((function(e){return'<span class="badge badge-soft rounded-full shadow-xs badge-info">'.concat(e.name,"</span>")})).join(""):u.innerHTML="")),t.next=16;break;case 12:t.prev=12,t.t0=t.catch(2),console.error("Error:",t.t0),a&&(a.classList.remove("loading"),a.disabled=!1);case 16:case"end":return t.stop()}}),r,null,[[2,12]])})))()},previewQuickBrew:function(e){return r(t().mark((function n(){var r,a;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/my/brewlog/recipes/".concat(e,"/preview/"),{method:"GET",headers:{"Content-Type":"application/x-www-form-urlencoded","X-CSRFToken":o()}});case 3:if(!(r=t.sent).ok){t.next=13;break}return t.next=7,r.text();case 7:a=t.sent,document.getElementById("preview_content").innerHTML=a,document.getElementById("preview_quick_brew_modal").showModal(),t.next=14;break;case 13:alert("预览加载失败");case 14:t.next=20;break;case 16:t.prev=16,t.t0=t.catch(0),console.error("Error:",t.t0),alert("预览加载失败");case 20:case"end":return t.stop()}}),n,null,[[0,16]])})))()},quickBrew:function(e){fetch("/my/brewlog/recipes/".concat(e,"/quick-brew/"),{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded","X-CSRFToken":o()}}).then((function(e){return e.json()})).then((function(e){"success"===e.status?window.location.href=e.redirect_url:alert(e.message||"操作失败")}))},renameRecipe:function(e,n){return r(t().mark((function r(){var a,i;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/my/brewlog/recipes/".concat(e,"/rename/"),{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded","X-CSRFToken":o()},body:new URLSearchParams({new_name:n})});case 3:return a=t.sent,t.next=6,a.json();case 6:if("success"!==(i=t.sent).status){t.next=11;break}window.location.reload(),t.next=12;break;case 11:throw new Error(i.message);case 12:t.next=17;break;case 14:throw t.prev=14,t.t0=t.catch(0),t.t0;case 17:case"end":return t.stop()}}),r,null,[[0,14]])})))()},editTag:function(e,n){return r(t().mark((function r(){var a,i,c;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=document.getElementById("manage_tags_modal"),i=a.querySelector('button[data-tag-id="'.concat(e,'"]')),t.prev=2,i&&(i.classList.add("loading"),i.disabled=!0),t.next=6,fetch("/my/brewlog/recipes/tag/manage/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded","X-CSRFToken":o()},body:new URLSearchParams({action:"edit",tag_id:e,tag_name:n})});case 6:return c=t.sent,t.next=9,c.json();case 9:"success"===t.sent.status&&window.location.reload(),t.next=17;break;case 13:t.prev=13,t.t0=t.catch(2),console.error("Error:",t.t0),alert("标签修改失败");case 17:return t.prev=17,i&&(i.classList.remove("loading"),i.disabled=!1),t.finish(17);case 20:case"end":return t.stop()}}),r,null,[[2,13,17,20]])})))()},deleteTag:function(e){return r(t().mark((function n(){var r,a,i;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(confirm("确定要删除这个标签吗？")){t.next=2;break}return t.abrupt("return");case 2:return r=document.getElementById("manage_tags_modal"),a=r.querySelector('button[data-delete-tag-id="'.concat(e,'"]')),t.prev=4,a&&(a.classList.add("loading"),a.disabled=!0),t.next=8,fetch("/my/brewlog/recipes/tag/manage/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded","X-CSRFToken":o()},body:new URLSearchParams({action:"delete",tag_id:e})});case 8:return i=t.sent,t.next=11,i.json();case 11:"success"===t.sent.status&&window.location.reload(),t.next=19;break;case 15:t.prev=15,t.t0=t.catch(4),console.error("Error:",t.t0),alert("删除标签失败");case 19:return t.prev=19,a&&(a.classList.remove("loading"),a.disabled=!1),t.finish(19);case 22:case"end":return t.stop()}}),n,null,[[4,15,19,22]])})))()}}),Alpine.data("tagManager",(function(){return{newTagName:"",isSubmitting:!1,addTag:function(){var e=this;return r(t().mark((function n(){var r;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.newTagName.trim()&&!e.isSubmitting){t.next=2;break}return t.abrupt("return");case 2:return e.isSubmitting=!0,t.prev=3,t.next=6,fetch("/my/brewlog/recipes/tag/manage/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded","X-CSRFToken":o()},body:new URLSearchParams({action:"add",tag_name:e.newTagName})});case 6:return r=t.sent,t.next=9,r.json();case 9:"success"===t.sent.status&&window.location.reload(),t.next=17;break;case 13:t.prev=13,t.t0=t.catch(3),console.error("Error:",t.t0),alert("添加标签失败");case 17:return t.prev=17,e.isSubmitting=!1,t.finish(17);case 20:e.newTagName="";case 21:case"end":return t.stop()}}),n,null,[[3,13,17,20]])})))()},editTag:function(e,t){fetch("/my/brewlog/recipes/tag/manage/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded","X-CSRFToken":o()},body:new URLSearchParams({action:"edit",tag_id:e,tag_name:t})}).then((function(e){return e.json()})).then((function(e){"success"===e.status&&window.location.reload()}))},deleteTag:function(e){confirm("确定要删除这个标签吗？")&&fetch("/my/brewlog/recipes/tag/manage/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded","X-CSRFToken":o()},body:new URLSearchParams({action:"delete",tag_id:e})}).then((function(e){return e.json()})).then((function(e){"success"===e.status&&window.location.reload()}))}}}))}))})();