/*! For license information please see bldtbn.js.LICENSE.txt */
(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function n(t){for(var n=1;n<arguments.length;n++){var o=null!=arguments[n]?arguments[n]:{};n%2?e(Object(o),!0).forEach((function(e){r(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):e(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}function r(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function o(){"use strict";o=function(){return e};var e={},n=Object.prototype,r=n.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof h?e:h,i=Object.create(o.prototype),a=new k(r||[]);return i._invoke=function(t,e,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=x(a,n);if(s){if(s===p)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var u=d(t,e,n);if("normal"===u.type){if(r=n.done?"completed":"suspendedYield",u.arg===p)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r="completed",n.method="throw",n.arg=u.arg)}}}(t,n,a),i}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p={};function h(){}function f(){}function v(){}var m={};c(m,a,(function(){return this}));var y=Object.getPrototypeOf,g=y&&y(y(T([])));g&&g!==n&&r.call(g,a)&&(m=g);var w=v.prototype=h.prototype=Object.create(m);function b(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function E(e,n){function o(i,a,s,u){var c=d(e[i],e,a);if("throw"!==c.type){var l=c.arg,p=l.value;return p&&"object"==t(p)&&r.call(p,"__await")?n.resolve(p.__await).then((function(t){o("next",t,s,u)}),(function(t){o("throw",t,s,u)})):n.resolve(p).then((function(t){l.value=t,s(l)}),(function(t){return o("throw",t,s,u)}))}u(c.arg)}var i;this._invoke=function(t,e){function r(){return new n((function(n,r){o(t,e,n,r)}))}return i=i?i.then(r,r):r()}}function x(t,e){var n=t.iterator[e.method];if(void 0===n){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,x(t,e),"throw"===e.method))return p;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var r=d(n,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var o=r.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function k(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function T(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:_}}function _(){return{value:void 0,done:!0}}return f.prototype=v,c(w,"constructor",v),c(v,"constructor",f),f.displayName=c(v,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,c(t,u,"GeneratorFunction")),t.prototype=Object.create(w),t},e.awrap=function(t){return{__await:t}},b(E.prototype),c(E.prototype,s,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new E(l(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(w),c(w,u,"Generator"),c(w,a,(function(){return this})),c(w,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=[];for(var n in t)e.push(n);return e.reverse(),function n(){for(;e.length;){var r=e.pop();if(r in t)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=T,k.prototype={constructor:k,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var s=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),L(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:T(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},e}function i(t,e,n,r,o,i,a){try{var s=t[i](a),u=s.value}catch(t){return void n(t)}s.done?e(u):Promise.resolve(u).then(r,o)}function a(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function s(t){i(a,r,o,s,u,"next",t)}function u(t){i(a,r,o,s,u,"throw",t)}s(void 0)}))}}function s(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var u=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.input=document.querySelector('input[name="flavor_tags"]'),this.hiddenInput=document.querySelector("#flavor-tags-data"),this.dropdown=document.querySelector("#tags-dropdown"),this.selectedTagsContainer=document.querySelector("#selected-tags"),this.selectedTags=new Set,this.allTags=[],this.input&&this.init()}var e,r,i,u,c,l;return e=t,r=[{key:"init",value:(l=a(o().mark((function t(){var e,n,r=this;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=this.input.dataset.initialTags||"",n=e.split(",").map((function(t){return t.trim()})).filter((function(t){return t})),this.setupEventListeners(),t.next=5,this.loadExistingTags();case 5:n.forEach((function(t){return r.addTag(t)})),this.input.value="",this.updateHiddenInput();case 8:case"end":return t.stop()}}),t,this)}))),function(){return l.apply(this,arguments)})},{key:"loadExistingTags",value:(c=a(o().mark((function t(){var e,r,i;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e=this.input.dataset.tagsUrl){t.next=4;break}return console.error("Tags URL not found"),t.abrupt("return");case 4:return t.prev=4,t.next=7,fetch(e);case 7:return r=t.sent,t.next=10,r.json();case 10:i=t.sent,this.allTags=i.map((function(t){return n(n({},t),{},{canDelete:0===t.usage_count})})),t.next=17;break;case 14:t.prev=14,t.t0=t.catch(4),console.error("Error loading tags:",t.t0);case 17:case"end":return t.stop()}}),t,this,[[4,14]])}))),function(){return c.apply(this,arguments)})},{key:"setupEventListeners",value:function(){var t=this;this.input.addEventListener("input",(function(){return t.handleInput()})),this.input.addEventListener("keydown",(function(e){return t.handleKeydown(e)})),document.addEventListener("click",(function(e){t.input.contains(e.target)||t.dropdown.contains(e.target)||t.hideDropdown()}))}},{key:"handleInput",value:function(){var t=this.input.value.trim();t?this.showDropdown(t):this.hideDropdown()}},{key:"handleKeydown",value:function(t){if("Enter"===t.key){t.preventDefault();var e=this.input.value.trim();if(e){var n=this.allTags.find((function(t){return t.name.toLowerCase()===e.toLowerCase()}));n?this.addTag(n.name):this.createNewTag(e),this.input.value="",this.hideDropdown()}}}},{key:"showDropdown",value:function(t){var e=this,n=this.allTags.filter((function(n){return n.name.toLowerCase().includes(t.toLowerCase())&&!e.selectedTags.has(n.name)}));n.length?(this.dropdown.innerHTML=n.map((function(t){return'<li>\n                    <div class="w-full flex items-center justify-between hover:bg-base-300">\n                        <a href="#" data-tag="'.concat(t.name,'" class="flex-grow">').concat(t.name,"</a>\n                        ").concat(t.canDelete?'<button type="button" class="px-3 py-2 text-error hover:bg-error hover:text-error-content" data-delete-tag="'.concat(t.name,'" data-tag-id="').concat(t.id,'">×</button>'):'<span class="w-10"></span>',"\n                    </div>\n                </li>")})).join(""),this.dropdown.querySelectorAll("a").forEach((function(t){t.addEventListener("click",(function(t){t.preventDefault(),e.addTag(t.target.dataset.tag),e.input.value="",e.hideDropdown()}))})),this.dropdown.querySelectorAll("button[data-delete-tag]").forEach((function(t){t.addEventListener("click",function(){var n=a(o().mark((function n(r){var i,a;return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(r.preventDefault(),r.stopPropagation(),i=t.dataset.deleteTag,!(a=t.dataset.tagId)){n.next=11;break}return n.next=7,e.deleteTag(a);case 7:if(n.sent){n.next=11;break}return window.dispatchEvent(new CustomEvent("show-warning",{detail:"无法删除该标签，可能已被其他冲煮记录使用"})),n.abrupt("return");case 11:e.allTags=e.allTags.filter((function(t){return t.name!==i})),e.showDropdown(e.input.value.trim());case 13:case"end":return n.stop()}}),n)})));return function(t){return n.apply(this,arguments)}}())})),this.dropdown.classList.remove("hidden")):this.hideDropdown()}},{key:"hideDropdown",value:function(){this.dropdown.classList.add("hidden")}},{key:"addTag",value:function(t){var e=this;if(!this.selectedTags.has(t)){var n=document.createElement("div");n.className="bg-primary/10 text-primary px-2 py-1 rounded-lg flex items-center gap-2",n.innerHTML="\n            <span>".concat(t,'</span>\n            <button type="button" class="hover:text-error">×</button>\n        '),n.querySelector("button").addEventListener("click",(function(){e.removeTag(t),n.remove()})),this.selectedTagsContainer.appendChild(n),this.selectedTags.add(t),this.updateHiddenInput()}}},{key:"removeTag",value:function(t){this.selectedTags.delete(t),this.updateHiddenInput()}},{key:"updateHiddenInput",value:function(){this.hiddenInput.value=Array.from(this.selectedTags).join(",")}},{key:"createNewTag",value:(u=a(o().mark((function t(e){var n,r;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/my/brewlog/flavor-tags/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":document.querySelector("[name=csrfmiddlewaretoken]").value},body:JSON.stringify({name:e})});case 3:if((n=t.sent).ok){t.next=6;break}throw new Error("Failed to create tag");case 6:return t.next=8,n.json();case 8:r=t.sent,this.allTags.push(r),this.addTag(r.name),t.next=16;break;case 13:t.prev=13,t.t0=t.catch(0),console.error("Error creating new tag:",t.t0);case 16:case"end":return t.stop()}}),t,this,[[0,13]])}))),function(t){return u.apply(this,arguments)})},{key:"deleteTag",value:(i=a(o().mark((function t(e){return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/my/brewlog/flavor-tags/".concat(e,"/"),{method:"DELETE",headers:{"X-CSRFToken":document.querySelector("[name=csrfmiddlewaretoken]").value}});case 3:if(t.sent.ok){t.next=6;break}throw new Error("Failed to delete tag");case 6:return t.abrupt("return",!0);case 9:return t.prev=9,t.t0=t.catch(0),console.error("Error deleting tag:",t.t0),t.abrupt("return",!1);case 13:case"end":return t.stop()}}),t,null,[[0,9]])}))),function(t){return i.apply(this,arguments)})}],r&&s(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();function c(){Alpine.data("beanForm",(function(){return{sections:{general:!0,product:!0,details:!1},validationError:!1,submitting:!1,restPeriodType:"SINGLE",roastDateEnabled:!1,initialRoastDate:"",init:function(){var t,e,n,r=this;this.typeValue=(null===(t=this.$el.querySelector('input[name="type"]:checked'))||void 0===t?void 0:t.value)||"SKIP","SKIP"!==this.typeValue&&(this.sections.details=!0),this.$watch("typeValue",(function(t){"SKIP"!==t&&(r.sections.details=!0)}));var o=this.$refs.roastDateWrapper;if(o){var i=o.querySelector('input[name="roast_date"]');i&&i.value&&(this.roastDateEnabled=!0,this.initialRoastDate=i.value)}this.$watch("roastDateEnabled",(function(t){var e=r.$refs.roastDateInput;if(e&&t&&!e.value){var n=(new Date).toISOString().split("T")[0];e.value=n}})),this.initialRestPeriodMin=(null===(e=this.$el.querySelector('input[name="rest_period_min"]'))||void 0===e?void 0:e.value)||"",this.initialRestPeriodMax=(null===(n=this.$el.querySelector('input[name="rest_period_max"]'))||void 0===n?void 0:n.value)||"",this.$watch("restPeriodType",(function(t){r.$el.querySelector('input[name="rest_period_min"]');var e=r.$el.querySelector('input[name="rest_period_max"]');"SINGLE"===t&&e&&(e.value="")})),[document.querySelector('input[name="rest_period_min"]'),document.querySelector('input[name="rest_period_max"]')].forEach((function(t){t&&t.addEventListener("input",(function(){var e=t.parentElement.querySelector(".error-message");e&&e.remove(),t.classList.remove("input-error"),r.validationError=!1}))}))},handleSubmit:function(t){var e,n,r=parseFloat((null===(e=document.querySelector('input[name="bag_weight"]'))||void 0===e?void 0:e.value)||0),o=parseFloat((null===(n=document.querySelector('input[name="bag_remain"]'))||void 0===n?void 0:n.value)||0);if(!this.roastDateEnabled){var i=this.$el.querySelector('input[name="roast_date"]'),a=this.$el.querySelector('input[name="rest_period_min"]'),s=this.$el.querySelector('input[name="rest_period_max"]');i&&(i.value=""),a&&(a.value=""),s&&(s.value="")}var u=document.querySelector('input[name="rest_period_min"]'),c=document.querySelector('input[name="rest_period_max"]');if(this.validationError=!1,c){c.classList.remove("input-error");var l=c.parentElement.querySelector(".error-message");l&&l.remove()}var d=document.querySelector('input[type="checkbox"].toggle[x-model="restPeriodType"]'),p=d&&d.checked;if(!p&&c&&(c.value=""),p&&u&&c&&u.value&&c.value){var h=parseInt(u.value);if(parseInt(c.value)<h){t.preventDefault(),this.validationError=!0,c.classList.add("input-error");var f=c.parentElement.querySelector(".error-message");return f||((f=document.createElement("div")).className="text-error text-sm mt-1 error-message",c.parentElement.appendChild(f)),f.textContent="最长养豆期不能小于最短养豆期",c.scrollIntoView({behavior:"smooth",block:"center"}),!1}}if(o>r){t.preventDefault(),this.validationError=!0;var v=document.querySelector('input[name="bag_remain"]');v.classList.add("input-error");var m=v.parentElement.querySelector(".error-message");return m||((m=document.createElement("div")).className="text-error text-sm mt-1 error-message",v.parentElement.appendChild(m)),m.textContent="库存余量不能大于包装规格",v.scrollIntoView({behavior:"smooth",block:"center"}),!1}var y=parseFloat(Alpine.store("blendComponents").totalRatio||0),g=this.typeValue,w=this.$store.blendComponents.components.length>1;return"BLEND"===g&&w&&Math.abs(y-100)>.02?(t.preventDefault(),window.dispatchEvent(new CustomEvent("show-warning",{detail:"拼配比例总和必须等于100%，请检查您的拼配方案。"})),!1):("BLEND"!==g||w||(this.$store.blendComponents.components[0].blend_ratio="100"),!this.validationError&&(this.submitting=!0,!0))},get showDetails(){return"SKIP"!==this.typeValue}}})),Alpine.data("blendComponents",(function(){return{addComponent:function(){var t=this;if(this.components.length<5){var e=this.components.length+1;this.components.push({id:e,blend_ratio:"0",roast_level:4,origin:"",region:"",finca:"",variety:"",altitude:"",process:"",isExpanded:!0}),this.$nextTick((function(){t.updateRatios()}))}},removeComponent:function(t){var e=this;this.components.length>1&&(this.components.splice(t,1),this.$nextTick((function(){e.updateRatios()})))},updateRatios:function(){var t,e,n,r,o,i=(100/((null===(t=this.$store)||void 0===t||null===(e=t.blendComponents)||void 0===e||null===(n=e.components)||void 0===n?void 0:n.length)||this.components.length)).toFixed(2);null!==(r=this.$store)&&void 0!==r&&null!==(o=r.blendComponents)&&void 0!==o&&o.components?(this.$store.blendComponents.components.forEach((function(t,e){t.blend_ratio=i})),this.$store.blendComponents.totalRatio=this.getTotalRatio()):this.components.forEach((function(t,e){var n=document.querySelector('input[name="blend_components-'.concat(e,'-blend_ratio"]'));n&&(n.value=i)}))},getTotalRatio:function(){var t,e,n,r=((null===(t=this.$store)||void 0===t||null===(e=t.blendComponents)||void 0===e?void 0:e.components)||this.components).reduce((function(t,e){return t+(parseFloat(e.blend_ratio)||0)}),0).toFixed(2);return null!==(n=this.$store)&&void 0!==n&&n.blendComponents&&(this.$store.blendComponents.totalRatio=r),r}}}))}document.addEventListener("DOMContentLoaded",(function(){new u;var t=document.querySelector('input[type="range"][data-field="roast_level"]'),e=document.querySelector('input[name="roast_level"]');t&&e&&(t.value=e.value||3,t.addEventListener("input",(function(){e.value=t.value})))})),window.Alpine?c():document.addEventListener("alpine:init",c)})();