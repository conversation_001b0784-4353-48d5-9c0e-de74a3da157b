/*! For license information please see blvwsht.js.LICENSE.txt */
(()=>{"use strict";const e="p".charCodeAt(0),t="H".charCodeAt(0),n="Y".charCodeAt(0),r="s".charCodeAt(0);let o;function a(a,i,c=!1){const s=new Uint8Array(13);i*=39.3701,s[0]=e,s[1]=t,s[2]=n,s[3]=r,s[4]=i>>>24,s[5]=i>>>16,s[6]=i>>>8,s[7]=255&i,s[8]=s[4],s[9]=s[5],s[10]=s[6],s[11]=s[7],s[12]=1;const l=function(e){let t=-1;o||(o=function(){const e=new Int32Array(256);for(let t=0;t<256;t++){let n=t;for(let e=0;e<8;e++)n=1&n?**********^n>>>1:n>>>1;e[t]=n}return e}());for(let n=0;n<e.length;n++)t=o[255&(t^e[n])]^t>>>8;return-1^t}(s),u=new Uint8Array(4);if(u[0]=l>>>24,u[1]=l>>>16,u[2]=l>>>8,u[3]=255&l,c){const o=function(o){for(let a=o.length-1;a>=4;a--)if(9===o[a-4]&&o[a-3]===e&&o[a-2]===t&&o[a-1]===n&&o[a]===r)return a-3;return 0}(a);return a.set(s,o),a.set(u,o+13),a}{const e=new Uint8Array(4);e[0]=0,e[1]=0,e[2]=0,e[3]=9;const t=new Uint8Array(54);return t.set(a,0),t.set(e,33),t.set(s,37),t.set(u,50),t}}const i="AAlwSFlz",c="AAAJcEhZ",s="AAAACXBI",l="[modern-screenshot]",u="undefined"!=typeof window,d=u&&"Worker"in window,f=u&&"atob"in window,h=u&&"btoa"in window,m=u?window.navigator?.userAgent:"",g=m.includes("Chrome"),p=m.includes("AppleWebKit")&&!g,w=m.includes("Firefox"),y=e=>e&&"__CONTEXT__"in e,v=e=>"CSSFontFaceRule"===e.constructor.name,b=e=>"CSSImportRule"===e.constructor.name,x=e=>1===e.nodeType,E=e=>"object"==typeof e.className,S=e=>"image"===e.tagName,A=e=>"use"===e.tagName,C=e=>x(e)&&void 0!==e.style&&!E(e),k=e=>8===e.nodeType,N=e=>3===e.nodeType,T=e=>"IMG"===e.tagName,L=e=>"VIDEO"===e.tagName,D=e=>"CANVAS"===e.tagName,P=e=>"TEXTAREA"===e.tagName,F=e=>"INPUT"===e.tagName,$=e=>"STYLE"===e.tagName,_=e=>"SCRIPT"===e.tagName,I=e=>"SELECT"===e.tagName,O=e=>"SLOT"===e.tagName,j=e=>"IFRAME"===e.tagName,M=(...e)=>console.warn(l,...e);function R(e){const t=e?.createElement?.("canvas");return t&&(t.height=t.width=1),Boolean(t)&&"toDataURL"in t&&Boolean(t.toDataURL("image/webp").includes("image/webp"))}const U=e=>e.startsWith("data:");function W(e,t){if(e.match(/^[a-z]+:\/\//i))return e;if(u&&e.match(/^\/\//))return window.location.protocol+e;if(e.match(/^[a-z]+:/i))return e;if(!u)return e;const n=B().implementation.createHTMLDocument(),r=n.createElement("base"),o=n.createElement("a");return n.head.appendChild(r),n.body.appendChild(o),t&&(r.href=t),o.href=e,o.href}function B(e){return(e&&x(e)?e?.ownerDocument:e)??window.document}const q="http://www.w3.org/2000/svg";const z=e=>function(e,t){return new Promise(((n,r)=>{const o=new FileReader;o.onload=()=>n(o.result),o.onerror=()=>r(o.error),o.onabort=()=>r(new Error(`Failed read blob to ${t}`)),"dataUrl"===t?o.readAsDataURL(e):"arrayBuffer"===t&&o.readAsArrayBuffer(e)}))}(e,"dataUrl");function H(e,t){const n=B(t).createElement("img");return n.decoding="sync",n.loading="eager",n.src=e,n}function V(e,t){return new Promise((n=>{const{timeout:r,ownerDocument:o,onError:a,onWarn:i}=t??{},c="string"==typeof e?H(e,B(o)):e;let s=null,l=null;function u(){n(c),s&&clearTimeout(s),l?.()}if(r&&(s=setTimeout(u,r)),L(c)){const e=c.currentSrc||c.src;if(!e)return c.poster?V(c.poster,t).then(n):u();if(c.readyState>=2)return u();const r=u,o=t=>{i?.("Failed video load",e,t),a?.(t),u()};l=()=>{c.removeEventListener("loadeddata",r),c.removeEventListener("error",o)},c.addEventListener("loadeddata",r,{once:!0}),c.addEventListener("error",o,{once:!0})}else{const e=S(c)?c.href.baseVal:c.currentSrc||c.src;if(!e)return u();const t=async()=>{if(T(c)&&"decode"in c)try{await c.decode()}catch(t){i?.("Failed to decode image, trying to render anyway",c.dataset.originalSrc||e,t)}u()},n=t=>{i?.("Failed image load",c.dataset.originalSrc||e,t),u()};if(T(c)&&c.complete)return t();l=()=>{c.removeEventListener("load",t),c.removeEventListener("error",n)},c.addEventListener("load",t,{once:!0}),c.addEventListener("error",n,{once:!0})}}))}const G=function(){let e=0;return()=>(e+=1,`u${`0000${(Math.random()*36**4<<0).toString(36)}`.slice(-4)}${e}`)}();function X(e){return e?.split(",").map((e=>e.trim().replace(/"|'/g,"").toLowerCase())).filter(Boolean)}let Y=0;function J(e){const t=`${l}[#${Y}]`;return Y++,{time:n=>e&&console.time(`${t} ${n}`),timeEnd:n=>e&&console.timeEnd(`${t} ${n}`),warn:(...t)=>e&&M(...t)}}async function K(e,t){return y(e)?e:async function(e,t){const{scale:n=1,workerUrl:r,workerNumber:o=1}=t||{},a=Boolean(t?.debug),i=t?.features??!0,c=e.ownerDocument??(u?window.document:void 0),s=e.ownerDocument?.defaultView??(u?window:void 0),l=new Map,f={width:0,height:0,quality:1,type:"image/png",scale:n,backgroundColor:null,style:null,filter:null,maximumCanvasSize:0,timeout:3e4,progress:null,debug:a,fetch:{requestInit:(h=t?.fetch?.bypassingCache,{cache:h?"no-cache":"force-cache"}),placeholderImage:"data:image/png;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",bypassingCache:!1,...t?.fetch},fetchFn:null,font:{},drawImageInterval:100,workerUrl:null,workerNumber:o,onCloneNode:null,onEmbedNode:null,onCreateForeignObjectSvg:null,includeStyleProperties:null,autoDestruct:!1,...t,__CONTEXT__:!0,log:J(a),node:e,ownerDocument:c,ownerWindow:s,dpi:1===n?null:96*n,svgStyleElement:Q(c),svgDefsElement:c?.createElementNS(q,"defs"),svgStyles:new Map,defaultComputedStyles:new Map,workers:[...Array.from({length:d&&r&&o?o:0})].map((()=>{try{const e=new Worker(r);return e.onmessage=async e=>{const{url:t,result:n}=e.data;n?l.get(t)?.resolve?.(n):l.get(t)?.reject?.(new Error(`Error receiving message from worker: ${t}`))},e.onmessageerror=e=>{const{url:t}=e.data;l.get(t)?.reject?.(new Error(`Error receiving message from worker: ${t}`))},e}catch(e){return f.log.warn("Failed to new Worker",e),null}})).filter(Boolean),fontFamilies:new Map,fontCssTexts:new Map,acceptOfImage:`${[R(c)&&"image/webp","image/svg+xml","image/*","*/*"].filter(Boolean).join(",")};q=0.8`,requests:l,drawImageCount:0,tasks:[],features:i,isEnable:e=>"restoreScrollPosition"===e?"boolean"!=typeof i&&(i[e]??!1):"boolean"==typeof i?i:i[e]??!0};var h;f.log.time("wait until load"),await async function(e,t){C(e)&&(T(e)||L(e)?await V(e,t):await Promise.all(["img","video"].flatMap((n=>Array.from(e.querySelectorAll(n)).map((e=>V(e,t)))))))}(e,{timeout:f.timeout,onWarn:f.log.warn}),f.log.timeEnd("wait until load");const{width:m,height:g}=function(e,t){let{width:n,height:r}=t;if(x(e)&&(!n||!r)){const t=e.getBoundingClientRect();n=n||t.width||Number(e.getAttribute("width"))||0,r=r||t.height||Number(e.getAttribute("height"))||0}return{width:n,height:r}}(e,f);return f.width=m,f.height=g,f}(e,{...t,autoDestruct:!0})}function Q(e){if(!e)return;const t=e.createElement("style"),n=t.ownerDocument.createTextNode("\n.______background-clip--text {\n  background-clip: text;\n  -webkit-background-clip: text;\n}\n");return t.appendChild(n),t}function Z(e,t){if(e.ownerDocument)try{const t=e.toDataURL();if("data:,"!==t)return H(t,e.ownerDocument)}catch(e){t.log.warn("Failed to clone canvas",e)}const n=e.cloneNode(!1),r=e.getContext("2d"),o=n.getContext("2d");try{return r&&o&&o.putImageData(r.getImageData(0,0,e.width,e.height),0,0),n}catch(e){t.log.warn("Failed to clone canvas",e)}return n}const ee=["width","height","-webkit-text-fill-color"],te=["stroke","fill"];function ne(e,t,n){const{defaultComputedStyles:r}=n,o=e.nodeName.toLowerCase(),a=E(e)&&"svg"!==o,i=a?te.map((t=>[t,e.getAttribute(t)])).filter((([,e])=>null!==e)):[],c=[a&&"svg",o,i.map(((e,t)=>`${e}=${t}`)).join(","),t].filter(Boolean).join(":");if(r.has(c))return r.get(c);const s=function(e){let t=e.sandbox;if(!t){const{ownerDocument:n}=e;try{n&&(t=n.createElement("iframe"),t.id=`__SANDBOX__-${G()}`,t.width="0",t.height="0",t.style.visibility="hidden",t.style.position="fixed",n.body.appendChild(t),t.contentWindow?.document.write('<!DOCTYPE html><meta charset="UTF-8"><title></title><body>'),e.sandbox=t)}catch(t){e.log.warn("Failed to getSandBox",t)}}return t}(n),l=s?.contentWindow;if(!l)return new Map;const u=l?.document;let d,f;a?(d=u.createElementNS(q,"svg"),f=d.ownerDocument.createElementNS(d.namespaceURI,o),i.forEach((([e,t])=>{f.setAttributeNS(null,e,t)})),d.appendChild(f)):d=f=u.createElement(o),f.textContent=" ",u.body.appendChild(d);const h=l.getComputedStyle(f,t),m=new Map;for(let e=h.length,t=0;t<e;t++){const e=h.item(t);ee.includes(e)||m.set(e,h.getPropertyValue(e))}return u.body.removeChild(d),r.set(c,m),m}function re(e,t,n){const r=new Map,o=[],a=new Map;if(n)for(const e of n)i(e);else for(let t=e.length,n=0;n<t;n++)i(e.item(n));for(let e=o.length,t=0;t<e;t++)a.get(o[t])?.forEach(((e,t)=>r.set(t,e)));function i(n){const i=e.getPropertyValue(n),c=e.getPropertyPriority(n),s=n.lastIndexOf("-"),l=s>-1?n.substring(0,s):void 0;if(l){let e=a.get(l);e||(e=new Map,a.set(l,e)),e.set(n,[i,c])}(t.get(n)!==i||c)&&(l?o.push(l):r.set(n,[i,c]))}return r}const oe=[":before",":after"],ae=[":-webkit-scrollbar",":-webkit-scrollbar-button",":-webkit-scrollbar-thumb",":-webkit-scrollbar-track",":-webkit-scrollbar-track-piece",":-webkit-scrollbar-corner",":-webkit-resizer"],ie=new Set(["symbol"]);async function ce(e,t,n,r,o){if(x(n)&&($(n)||_(n)))return;if(r.filter&&!r.filter(n))return;ie.has(t.nodeName)||ie.has(n.nodeName)?r.currentParentNodeStyle=void 0:r.currentParentNodeStyle=r.currentNodeStyle;const a=await ue(n,r,!1,o);r.isEnable("restoreScrollPosition")&&function(e,t){if(!C(e)||!C(t))return;const{scrollTop:n,scrollLeft:r}=e;if(!n&&!r)return;const{transform:o}=t.style,a=new DOMMatrix(o),{a:i,b:c,c:s,d:l}=a;a.a=1,a.b=0,a.c=0,a.d=1,a.translateSelf(-r,-n),a.a=i,a.b=c,a.c=s,a.d=l,t.style.transform=a.toString()}(e,a),t.appendChild(a)}async function se(e,t,n,r){for(let o=(x(e)?e.shadowRoot?.firstChild:void 0)??e.firstChild;o;o=o.nextSibling)if(!k(o))if(x(o)&&O(o)&&"function"==typeof o.assignedNodes){const a=o.assignedNodes();for(let o=0;o<a.length;o++)await ce(e,t,a[o],n,r)}else await ce(e,t,o,n,r)}const le=/^[\w-:]+$/;async function ue(e,t,n=!1,r){const{ownerDocument:o,ownerWindow:a,fontFamilies:i}=t;if(o&&N(e))return r&&/\S/.test(e.data)&&r(e.data),o.createTextNode(e.data);if(o&&a&&x(e)&&(C(e)||E(e))){const r=await function(e,t){return D(e)?Z(e,t):j(e)?function(e,t){try{if(e?.contentDocument?.body)return ue(e.contentDocument.body,t)}catch(e){t.log.warn("Failed to clone iframe",e)}return e.cloneNode(!1)}(e,t):T(e)?function(e){const t=e.cloneNode(!1);return e.currentSrc&&e.currentSrc!==e.src&&(t.src=e.currentSrc,t.srcset=""),"lazy"===t.loading&&(t.loading="eager"),t}(e):L(e)?async function(e,t){if(e.ownerDocument&&!e.currentSrc&&e.poster)return H(e.poster,e.ownerDocument);const n=e.cloneNode(!1);n.crossOrigin="anonymous",e.currentSrc&&e.currentSrc!==e.src&&(n.src=e.currentSrc);const r=n.ownerDocument;if(r){let o=!0;if(await V(n,{onError:()=>o=!1,onWarn:t.log.warn}),!o)return e.poster?H(e.poster,e.ownerDocument):n;n.currentTime=e.currentTime,await new Promise((e=>{n.addEventListener("seeked",e,{once:!0})}));const a=r.createElement("canvas");a.width=e.offsetWidth,a.height=e.offsetHeight;try{const e=a.getContext("2d");e&&e.drawImage(n,0,0,a.width,a.height)}catch(r){return t.log.warn("Failed to clone video",r),e.poster?H(e.poster,e.ownerDocument):n}return Z(a,t)}return n}(e,t):e.cloneNode(!1)}(e,t);if(t.isEnable("removeAbnormalAttributes")){const e=r.getAttributeNames();for(let t=e.length,n=0;n<t;n++){const t=e[n];le.test(t)||r.removeAttribute(t)}}const o=t.currentNodeStyle=function(e,t,n,r){const{ownerWindow:o,includeStyleProperties:a,currentParentNodeStyle:i}=r,c=t.style,s=o.getComputedStyle(e),l=ne(e,null,r);i?.forEach(((e,t)=>{l.delete(t)}));const u=re(s,l,a);u.delete("transition-property"),u.delete("all"),u.delete("d"),u.delete("content"),n&&(u.delete("margin-top"),u.delete("margin-right"),u.delete("margin-bottom"),u.delete("margin-left"),u.delete("margin-block-start"),u.delete("margin-block-end"),u.delete("margin-inline-start"),u.delete("margin-inline-end"),u.set("box-sizing",["border-box",""])),"text"===u.get("background-clip")?.[0]&&t.classList.add("______background-clip--text"),g&&(u.has("font-kerning")||u.set("font-kerning",["normal",""]),"hidden"!==u.get("overflow-x")?.[0]&&"hidden"!==u.get("overflow-y")?.[0]||"ellipsis"!==u.get("text-overflow")?.[0]||e.scrollWidth!==e.clientWidth||u.set("text-overflow",["clip",""]));for(let e=c.length,t=0;t<e;t++)c.removeProperty(c.item(t));return u.forEach((([e,t],n)=>{c.setProperty(n,e,t)})),u}(e,r,n,t);n&&function(e,t){const{backgroundColor:n,width:r,height:o,style:a}=t,i=e.style;if(n&&i.setProperty("background-color",n,"important"),r&&i.setProperty("width",`${r}px`,"important"),o&&i.setProperty("height",`${o}px`,"important"),a)for(const e in a)i[e]=a[e]}(r,t);let a=!1;if(t.isEnable("copyScrollbar")){const t=[o.get("overflow-x")?.[0],o.get("overflow-y")?.[0]];a=t.includes("scroll")||(t.includes("auto")||t.includes("overlay"))&&(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth)}const c=o.get("text-transform")?.[0],s=X(o.get("font-family")?.[0]),l=s?e=>{"uppercase"===c?e=e.toUpperCase():"lowercase"===c?e=e.toLowerCase():"capitalize"===c&&(e=e[0].toUpperCase()+e.substring(1)),s.forEach((t=>{let n=i.get(t);n||i.set(t,n=new Set),e.split("").forEach((e=>n.add(e)))}))}:void 0;return function(e,t,n,r,o){const{ownerWindow:a,svgStyleElement:i,svgStyles:c,currentNodeStyle:s}=r;function l(n){const i=a.getComputedStyle(e,n);let l=i.getPropertyValue("content");if(!l||"none"===l)return;o?.(l),l=l.replace(/(')|(")|(counter\(.+\))/g,"");const u=[G()],d=ne(e,n,r);s?.forEach(((e,t)=>{d.delete(t)}));const f=re(i,d,r.includeStyleProperties);f.delete("content"),f.delete("-webkit-locale"),"text"===f.get("background-clip")?.[0]&&t.classList.add("______background-clip--text");const h=[`content: '${l}';`];if(f.forEach((([e,t],n)=>{h.push(`${n}: ${e}${t?" !important":""};`)})),1===h.length)return;try{t.className=[t.className,...u].join(" ")}catch(e){return void r.log.warn("Failed to copyPseudoClass",e)}const m=h.join("\n  ");let g=c.get(m);g||(g=[],c.set(m,g)),g.push(`.${u[0]}:${n}`)}i&&a&&(oe.forEach(l),n&&ae.forEach(l))}(e,r,a,t,l),function(e,t){(P(e)||F(e)||I(e))&&t.setAttribute("value",e.value)}(e,r),L(e)||await se(e,r,t,l),r}const c=e.cloneNode(!1);return await se(e,c,t),c}function de(e,t){const{url:n,requestType:r="text",responseType:o="text",imageDom:a}=t;let i=n;const{timeout:c,acceptOfImage:s,requests:l,fetchFn:u,fetch:{requestInit:d,bypassingCache:f,placeholderImage:h},font:m,workers:g,fontFamilies:y}=e;"image"===r&&(p||w)&&e.drawImageCount++;let v=l.get(n);if(!v){f&&f instanceof RegExp&&f.test(i)&&(i+=(/\?/.test(i)?"&":"?")+(new Date).getTime());const t=r.startsWith("font")&&m&&m.minify,w=new Set;t&&r.split(";")[1].split(",").forEach((e=>{y.has(e)&&y.get(e).forEach((e=>w.add(e)))}));const b=t&&w.size,x={url:i,timeout:c,responseType:b?"arrayBuffer":o,headers:"image"===r?{accept:s}:void 0,...d};v={type:r,resolve:void 0,reject:void 0,response:null},v.response=(async()=>{if(u&&"image"===r){const e=await u(n);if(e)return e}return!p&&n.startsWith("http")&&g.length?new Promise(((e,t)=>{g[l.size&g.length-1].postMessage({rawUrl:n,...x}),v.resolve=e,v.reject=t})):function(e){const{url:t,timeout:n,responseType:r,...o}=e,a=new AbortController,i=n?setTimeout((()=>a.abort()),n):void 0;return fetch(t,{signal:a.signal,...o}).then((e=>{if(!e.ok)throw new Error("Failed fetch, not 2xx response",{cause:e});switch(r){case"arrayBuffer":return e.arrayBuffer();case"dataUrl":return e.blob().then(z);default:return e.text()}})).finally((()=>clearTimeout(i)))}(x)})().catch((t=>{if(l.delete(n),"image"===r&&h)return e.log.warn("Failed to fetch image base64, trying to use placeholder image",i),"string"==typeof h?h:h(a);throw t})),l.set(n,v)}return v.response}async function fe(e,t,n,r){if(!he(e))return e;for(const[o,a]of function(e,t){const n=[];return e.replace(me,((e,r,o)=>(n.push([o,W(o,t)]),e))),n.filter((([e])=>!U(e)))}(e,t))try{const t=await de(n,{url:a,requestType:r?"image":"text",responseType:"dataUrl"});e=e.replace(ge(o),`$1${t}$3`)}catch(e){n.log.warn("Failed to fetch css data url",o,e)}return e}function he(e){return/url\((['"]?)([^'"]+?)\1\)/.test(e)}const me=/url\((['"]?)([^'"]+?)\1\)/g;function ge(e){const t=e.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1");return new RegExp(`(url\\(['"]?)(${t})(['"]?\\))`,"g")}const pe=["background-image","border-image-source","-webkit-border-image","-webkit-mask-image","list-style-image"];function we(e,t){const{tasks:n}=t;x(e)&&((T(e)||S(e))&&n.push(...function(e,t){if(T(e)){const n=e.currentSrc||e.src;if(!U(n))return[de(t,{url:n,imageDom:e,requestType:"image",responseType:"dataUrl"}).then((t=>{t&&(e.srcset="",e.dataset.originalSrc=n,e.src=t||"")}))];(p||w)&&t.drawImageCount++}else if(E(e)&&!U(e.href.baseVal)){const n=e.href.baseVal;return[de(t,{url:n,imageDom:e,requestType:"image",responseType:"dataUrl"}).then((t=>{t&&(e.dataset.originalSrc=n,e.href.baseVal=t||"")}))]}return[]}(e,t)),A(e)&&n.push(...function(e,t){const{ownerDocument:n,svgDefsElement:r}=t,o=e.getAttribute("href")??e.getAttribute("xlink:href");if(!o)return[];const[a,i]=o.split("#");if(i){const o=`#${i}`,c=n?.querySelector(`svg ${o}`);if(a&&e.setAttribute("href",o),r?.querySelector(o))return[];if(c)return r?.appendChild(c.cloneNode(!0)),[];if(a)return[de(t,{url:a,responseType:"text"}).then((e=>{r?.insertAdjacentHTML("beforeend",e)}))]}return[]}(e,t))),C(e)&&n.push(...function(e,t){return pe.map((n=>{const r=e.getPropertyValue(n);return r&&"none"!==r?((p||w)&&t.drawImageCount++,fe(r,null,t,!0).then((t=>{t&&r!==t&&e.setProperty(n,t,e.getPropertyPriority(n))}))):null})).filter(Boolean)}(e.style,t)),e.childNodes.forEach((e=>{we(e,t)}))}const ye=/(\/\*[\s\S]*?\*\/)/g,ve=/((@.*?keyframes [\s\S]*?){([\s\S]*?}\s*?)})/gi,be=/url\([^)]+\)\s*format\((["']?)([^"']+)\1\)/g,xe=/src:\s*(?:url\([^)]+\)\s*format\([^)]+\)[,;]\s*)+/g;function Ee(e,t){const{font:n}=t,r=n?n?.preferredFormat:void 0;return r?e.replace(xe,(e=>{for(;;){const[t,,n]=be.exec(e)||[];if(!n)return"";if(n===r)return`src: ${t};`}})):e}async function Se(e,t){const n=await K(e,t);if(x(n.node)&&E(n.node))return n.node;const{ownerDocument:r,log:o,tasks:a,svgStyleElement:i,svgDefsElement:c,svgStyles:s,font:l,progress:u,autoDestruct:d,onCloneNode:f,onEmbedNode:h,onCreateForeignObjectSvg:m}=n;o.time("clone node");const g=await ue(n.node,n,!0);if(i&&r){let e="";s.forEach(((t,n)=>{e+=`${t.join(",\n")} {\n  ${n}\n}\n`})),i.appendChild(r.createTextNode(e))}o.timeEnd("clone node"),await(f?.(g)),!1!==l&&x(g)&&(o.time("embed web font"),await async function(e,t){const{ownerDocument:n,svgStyleElement:r,fontFamilies:o,fontCssTexts:a,tasks:i,font:c}=t;if(n&&r&&o.size)if(c&&c.cssText){const e=Ee(c.cssText,t);r.appendChild(n.createTextNode(`${e}\n`))}else{const e=Array.from(n.styleSheets).filter((e=>{try{return"cssRules"in e&&Boolean(e.cssRules.length)}catch(n){return t.log.warn(`Error while reading CSS rules from ${e.href}`,n),!1}}));await Promise.all(e.flatMap((e=>Array.from(e.cssRules).map((async(n,r)=>{if(b(n)){let o=r+1;const a=n.href;let i="";try{i=await de(t,{url:a,requestType:"text",responseType:"text"})}catch(e){t.log.warn(`Error fetch remote css import from ${a}`,e)}const c=i.replace(me,((e,t,n)=>e.replace(n,W(n,a))));for(const n of function(e){if(null==e)return[];const t=[];let n=e.replace(ye,"");for(;;){const e=ve.exec(n);if(!e)break;t.push(e[0])}n=n.replace(ve,"");const r=/@import[\s\S]*?url\([^)]*\)[\s\S]*?;/gi,o=new RegExp("((\\s*?(?:\\/\\*[\\s\\S]*?\\*\\/)?\\s*?@media[\\s\\S]*?){([\\s\\S]*?)}\\s*?})|(([\\s\\S]*?){([\\s\\S]*?)})","gi");for(;;){let e=r.exec(n);if(e)o.lastIndex=r.lastIndex;else{if(e=o.exec(n),!e)break;r.lastIndex=o.lastIndex}t.push(e[0])}return t}(c))try{e.insertRule(n,n.startsWith("@import")?o+=1:e.cssRules.length)}catch(e){t.log.warn("Error inserting rule from remote css import",{rule:n,error:e})}}}))))),e.flatMap((e=>Array.from(e.cssRules))).filter((e=>v(e)&&he(e.style.getPropertyValue("src"))&&X(e.style.getPropertyValue("font-family"))?.some((e=>o.has(e))))).forEach((e=>{const o=e,c=a.get(o.cssText);c?r.appendChild(n.createTextNode(`${c}\n`)):i.push(fe(o.cssText,o.parentStyleSheet?o.parentStyleSheet.href:null,t).then((e=>{e=Ee(e,t),a.set(o.cssText,e),r.appendChild(n.createTextNode(`${e}\n`))})))}))}}(0,n),o.timeEnd("embed web font")),o.time("embed node"),we(g,n);const p=a.length;let w=0;u?.(w,p),await Promise.all([...Array.from({length:4})].map((async()=>{for(;;){const e=a.pop();if(!e)break;try{await e}catch(e){n.log.warn("Failed to run task",e)}u?.(++w,p)}}))),o.timeEnd("embed node"),await(h?.(g));const y=function(e,t){const{width:n,height:r}=t,o=function(e,t,n){const r=B(n).createElementNS(q,"svg");return r.setAttributeNS(null,"width",e.toString()),r.setAttributeNS(null,"height",t.toString()),r.setAttributeNS(null,"viewBox",`0 0 ${e} ${t}`),r}(n,r,e.ownerDocument),a=o.ownerDocument.createElementNS(o.namespaceURI,"foreignObject");return a.setAttributeNS(null,"x","0%"),a.setAttributeNS(null,"y","0%"),a.setAttributeNS(null,"width","100%"),a.setAttributeNS(null,"height","100%"),a.append(e),o.appendChild(a),o}(g,n);return c&&y.insertBefore(c,y.children[0]),i&&y.insertBefore(i,y.children[0]),d&&function(e){if(e.ownerDocument=void 0,e.ownerWindow=void 0,e.svgStyleElement=void 0,e.svgDefsElement=void 0,e.svgStyles.clear(),e.defaultComputedStyles.clear(),e.sandbox){try{e.sandbox.remove()}catch(t){e.log.warn("Failed to destroyContext",t)}e.sandbox=void 0}e.workers=[],e.fontFamilies.clear(),e.fontCssTexts.clear(),e.requests.clear(),e.tasks=[]}(n),await(m?.(y)),y}async function Ae(e,t){const n=await K(e,t),r=await Se(n),o=function(e,t){let n=(new XMLSerializer).serializeToString(e);return t&&(n=n.replace(/[\u0000-\u0008\v\f\u000E-\u001F\uD800-\uDFFF\uFFFE\uFFFF]/gu,"")),`data:image/svg+xml;charset=utf-8,${encodeURIComponent(n)}`}(r,n.isEnable("removeControlCharacter"));n.autoDestruct||(n.svgStyleElement=Q(n.ownerDocument),n.svgDefsElement=n.ownerDocument?.createElementNS(q,"defs"),n.svgStyles.clear());const a=H(o,r.ownerDocument);return await async function(e,t){const{log:n,timeout:r,drawImageCount:o,drawImageInterval:a}=t;n.time("image to canvas");const i=await V(e,{timeout:r,onWarn:t.log.warn}),{canvas:c,context2d:s}=function(e,t){const{width:n,height:r,scale:o,backgroundColor:a,maximumCanvasSize:i}=t,c=e.createElement("canvas");c.width=Math.floor(n*o),c.height=Math.floor(r*o),c.style.width=`${n}px`,c.style.height=`${r}px`,i&&(c.width>i||c.height>i)&&(c.width>i&&c.height>i?c.width>c.height?(c.height*=i/c.width,c.width=i):(c.width*=i/c.height,c.height=i):c.width>i?(c.height*=i/c.width,c.width=i):(c.width*=i/c.height,c.height=i));const s=c.getContext("2d");return s&&a&&(s.fillStyle=a,s.fillRect(0,0,c.width,c.height)),{canvas:c,context2d:s}}(e.ownerDocument,t),l=()=>{try{s?.drawImage(i,0,0,c.width,c.height)}catch(e){t.log.warn("Failed to drawImage",e)}};if(l(),t.isEnable("fixSvgXmlDecode"))for(let e=0;e<o;e++)await new Promise((t=>{setTimeout((()=>{l(),t()}),e+a)}));return t.drawImageCount=0,n.timeEnd("image to canvas"),c}(a,n)}async function Ce(e,t){return async function(e,t){const n=await K(e,t),{log:r,quality:o,type:l,dpi:u}=n,d=await Ae(n);r.time("canvas to data url");let m=d.toDataURL(l,o);if(["image/png","image/jpeg"].includes(l)&&u&&f&&h){const[e,t]=m.split(",");let n=0,r=!1;if("image/png"===l){const e=function(e){let t=e.indexOf(i);return-1===t&&(t=e.indexOf(c)),-1===t&&(t=e.indexOf(s)),t}(t);e>=0?(n=4*Math.ceil((e+28)/3),r=!0):n=44}else"image/jpeg"===l&&(n=24);const o=t.substring(0,n),d=t.substring(n),f=window.atob(o),h=new Uint8Array(f.length);for(let e=0;e<h.length;e++)h[e]=f.charCodeAt(e);const g="image/png"===l?a(h,u,r):function(e,t){return e[13]=1,e[14]=t>>8,e[15]=255&t,e[16]=t>>8,e[17]=255&t,e}(h,u);m=[e,",",window.btoa(String.fromCharCode(...g)),d].join("")}return r.timeEnd("canvas to data url"),m}(await K(e,{...t,type:"image/png"}))}function ke(e){return ke="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ke(e)}function Ne(){Ne=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",i=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var o=t&&t.prototype instanceof d?t:d,a=Object.create(o.prototype),i=new S(r||[]);return a._invoke=function(e,t,n){var r="suspendedStart";return function(o,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw a;return{value:void 0,done:!0}}for(n.method=o,n.arg=a;;){var i=n.delegate;if(i){var c=b(i,n);if(c){if(c===u)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=l(e,t,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===u)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}(e,n,i),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var u={};function d(){}function f(){}function h(){}var m={};c(m,o,(function(){return this}));var g=Object.getPrototypeOf,p=g&&g(g(A([])));p&&p!==t&&n.call(p,o)&&(m=p);var w=h.prototype=d.prototype=Object.create(m);function y(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function v(e,t){function r(o,a,i,c){var s=l(e[o],e,a);if("throw"!==s.type){var u=s.arg,d=u.value;return d&&"object"==ke(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(d).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,c)}))}c(s.arg)}var o;this._invoke=function(e,n){function a(){return new t((function(t,o){r(e,n,t,o)}))}return o=o?o.then(a,a):a()}}function b(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,b(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var r=l(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function x(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(x,this),this.reset(!0)}function A(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,a=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:C}}function C(){return{value:void 0,done:!0}}return f.prototype=h,c(w,"constructor",h),c(h,"constructor",f),f.displayName=c(h,i,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,c(e,i,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},y(v.prototype),c(v.prototype,a,(function(){return this})),e.AsyncIterator=v,e.async=function(t,n,r,o,a){void 0===a&&(a=Promise);var i=new v(s(t,n,r,o),a);return e.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},y(w),c(w,i,"Generator"),c(w,o,(function(){return this})),c(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=A,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return i.type="throw",i.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,u):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:A(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function Te(e,t,n,r,o,a,i){try{var c=e[a](i),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,o)}function Le(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){Te(a,r,o,i,c,"next",e)}function c(e){Te(a,r,o,i,c,"throw",e)}i(void 0)}))}}document.addEventListener("DOMContentLoaded",(function(){var e=document.getElementById("shareBtn");if(e){var t=/iPhone|iPad|iPod|Android/i.test(navigator.userAgent),n=function(){var e=Le(Ne().mark((function e(t){var n,r,o,a=arguments;return Ne().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=a.length>1&&void 0!==a[1]?a[1]:3,r=0;case 2:if(!(r<n)){e.next=21;break}return e.prev=3,e.next=6,Ce(t,{type:"image/png",scale:2,quality:1,backgroundColor:"#ffffff",features:{copyScrollbar:!0,removeAbnormalAttributes:!0,removeControlCharacter:!0,fixSvgXmlDecode:!0,restoreScrollPosition:!0}});case 6:return o=e.sent,URL.revokeObjectURL(o),e.abrupt("return",o);case 11:if(e.prev=11,e.t0=e.catch(3),console.error("截图重试失败:",e.t0),r!==n-1){e.next=16;break}throw e.t0;case 16:return e.next=18,new Promise((function(e){return setTimeout(e,500)}));case 18:r++,e.next=2;break;case 21:case"end":return e.stop()}}),e,null,[[3,11]])})));return function(t){return e.apply(this,arguments)}}();e.addEventListener("click",Le(Ne().mark((function r(){var o,a,i,c,s,l,u,d,f,h,m,g,p,w,y,v,b;return Ne().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if("true"!==e.dataset.processing){r.next=2;break}return r.abrupt("return");case 2:if(o=document.querySelector(".mx-auto.xl\\:max-w-3xl")){r.next=10;break}return(a=document.createElement("div")).className="toast toast-top toast-center z-[9999]",a.innerHTML='\n                <div class="alert alert-warning">\n                    <span>页面内容未准备好，请刷新后重试</span>\n                </div>\n            ',document.body.appendChild(a),setTimeout((function(){return a.remove()}),3e3),r.abrupt("return");case 10:if(!performance||!performance.memory){r.next=19;break}if(!(performance.memory.usedJSHeapSize/performance.memory.jsHeapSizeLimit>.8)){r.next=19;break}return(i=document.createElement("div")).className="toast toast-top toast-center z-[9999]",i.innerHTML='\n                    <div class="alert alert-warning">\n                        <span>系统资源不足，请关闭其他标签页后重试</span>\n                    </div>\n                ',document.body.appendChild(i),setTimeout((function(){return i.remove()}),3e3),r.abrupt("return");case 19:if(e.dataset.processing="true",c=e.innerHTML,e.innerHTML='<span class="loading loading-spinner text-lg"></span><span class="hidden md:block">生成中</span>',s=document.title.includes("我喝了什么咖啡"),o){r.next=25;break}return r.abrupt("return");case 25:return l=[],u=document.documentElement.getAttribute("data-theme"),d=window.getComputedStyle(o),f={width:o.style.width||d.width,maxWidth:o.style.maxWidth||d.maxWidth,backgroundColor:o.style.backgroundColor||d.backgroundColor,padding:o.style.padding||d.padding,margin:o.style.margin||d.margin},r.prev=29,s?(m=null===(h=o.querySelector('[x-data="stepGuide"]'))||void 0===h?void 0:h.parentElement)&&(l.push({element:m,parent:m.parentElement,nextSibling:m.nextSibling}),m.parentElement.removeChild(m)):(o.querySelectorAll(".divider").forEach((function(e){(e.textContent.includes("附加信息")||e.textContent.includes("提要"))&&(l.push({element:e,parent:e.parentElement,nextSibling:e.nextSibling}),e.parentElement.removeChild(e))})),["dl","#repurchase-history",".alert",".stats"].forEach((function(e){o.querySelectorAll(e).forEach((function(e){l.push({element:e,parent:e.parentElement,nextSibling:e.nextSibling}),e.parentElement.removeChild(e)}))}))),document.documentElement.setAttribute("data-theme","cupcake"),Object.assign(o.style,{width:t?"100%":"430px",maxWidth:"430px",backgroundColor:"#ffffff",padding:t?"15px":"20px",margin:"0 auto"}),t&&Object.assign(o.style,{width:"100%",maxWidth:"100%",padding:"10px",backgroundColor:"#ffffff"}),r.next=36,new Promise((function(e){return setTimeout(e,200)}));case 36:return r.next=38,n(o);case 38:return g=r.sent,r.next=41,fetch(g);case 41:return p=r.sent,r.next=44,p.blob();case 44:w=r.sent,y=URL.createObjectURL(w),(v=document.createElement("a")).download="".concat(document.title.replace(" - 咖啡搭子","").replace(/[<>:"/\\|?*]/g,"_"),".png"),v.href=y,v.addEventListener("click",(function(){setTimeout((function(){URL.revokeObjectURL(y),v.remove()}),1e3)})),v.click(),e.innerHTML='<span class="icon-[material-symbols--check-circle-outline] text-lg"></span><span class="hidden md:block">已导出</span>',r.next=63;break;case 54:r.prev=54,r.t0=r.catch(29),console.error("截图失败:",r.t0),(b=document.createElement("div")).className="toast toast-top toast-center z-[9999]",b.innerHTML='\n                <div class="alert alert-warning">\n                    <span>'.concat(r.t0.message||"截图失败，请稍候重试","</span>\n                </div>\n            "),document.body.appendChild(b),setTimeout((function(){document.body.contains(b)&&b.remove()}),3e3),e.innerHTML='<span class="icon-[material-symbols--error-outline] text-lg"></span><span class="hidden md:block">失败</span>';case 63:return r.prev=63,setTimeout((function(){e.innerHTML=c}),2e3),document.documentElement.setAttribute("data-theme",u),Object.keys(f).forEach((function(e){"auto"!==f[e]&&f[e]?o.style[e]=f[e]:o.style[e]=""})),l.forEach((function(e){var t=e.element,n=e.parent,r=e.nextSibling;r?n.insertBefore(t,r):n.appendChild(t)})),window.gc&&window.gc(),e.dataset.processing="false",r.finish(63);case 71:case"end":return r.stop()}}),r,null,[[29,54,63,71]])}))))}}))})();