(()=>{"use strict";function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var t=function(){function t(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),this.carousel=document.getElementById("main-carousel"),this.prevBtn=document.getElementById("prev-btn"),this.nextBtn=document.getElementById("next-btn"),this.thumbnails=document.querySelectorAll(".thumbnail-btn"),this.colorButtons=document.querySelectorAll(".btn[data-image-url]"),this.currentSlide=1,this.imagesLoaded=!1,this.touchStartX=0,this.touchEndX=0,this.minSwipeDistance=50,this.totalSlides=this.carousel.querySelectorAll(".carousel-item").length,this.init()}var i,n;return i=t,(n=[{key:"init",value:function(){this.bindEvents(),this.preloadImages(),this.goToSlide(1)}},{key:"bindEvents",value:function(){var e=this;this.carousel.addEventListener("touchstart",(function(t){e.touchStartX=t.touches[0].clientX})),this.carousel.addEventListener("touchmove",(function(e){e.preventDefault()})),this.carousel.addEventListener("touchend",(function(t){e.touchEndX=t.changedTouches[0].clientX,e.handleSwipe()})),this.prevBtn.addEventListener("click",(function(){e.imagesLoaded&&(e.currentSlide=e.currentSlide>1?e.currentSlide-1:e.totalSlides,e.goToSlide(e.currentSlide))})),this.nextBtn.addEventListener("click",(function(){e.imagesLoaded&&(e.currentSlide=e.currentSlide<e.totalSlides?e.currentSlide+1:1,e.goToSlide(e.currentSlide))})),this.thumbnails.forEach((function(t){t.addEventListener("click",(function(){if(e.imagesLoaded){var i=parseInt(t.dataset.slide);e.goToSlide(i)}}))})),this.colorButtons.forEach((function(t){t.addEventListener("click",(function(){var i=t.dataset.imageUrl;i&&e.updateMainImage(i)}))}))}},{key:"handleSwipe",value:function(){if(this.imagesLoaded){var e=this.touchEndX-this.touchStartX;Math.abs(e)>this.minSwipeDistance&&(this.currentSlide=e>0?this.currentSlide>1?this.currentSlide-1:this.totalSlides:this.currentSlide<this.totalSlides?this.currentSlide+1:1,this.goToSlide(this.currentSlide))}}},{key:"preloadImages",value:function(){var e=this,t=Array.from(document.querySelectorAll(".carousel-item img")).map((function(e){return e.src}));this.colorButtons.forEach((function(e){var i=e.dataset.imageUrl;i&&!t.includes(i)&&t.push(i)}));var i=0;t.forEach((function(n){var a=new Image;a.onload=function(){++i===t.length&&(e.imagesLoaded=!0,document.querySelectorAll(".skeleton").forEach((function(e){e.style.display="none"})),document.querySelectorAll(".carousel-item img").forEach((function(e){e.style.opacity="1"})))},a.src=n}))}},{key:"updateMainImage",value:function(e){if(this.imagesLoaded){var t=this.carousel.querySelectorAll(".carousel-item")[0].querySelector("img");t&&(t.src=e);var i=document.querySelector('.thumbnail-btn[data-slide="1"] img');i&&(i.src=e),this.goToSlide(1)}}},{key:"goToSlide",value:function(e){if(this.imagesLoaded){var t=this.carousel.querySelectorAll(".carousel-item");t.forEach((function(e){e.style.display="none"})),t[e-1].style.display="block",this.currentSlide=e,this.thumbnails.forEach((function(t){parseInt(t.dataset.slide)===e?(t.classList.add("active"),t.classList.add("outline-base-content")):(t.classList.remove("active"),t.classList.remove("outline-base-content"))}))}}}])&&e(i.prototype,n),Object.defineProperty(i,"prototype",{writable:!1}),t}();document.addEventListener("DOMContentLoaded",(function(){new t}))})();