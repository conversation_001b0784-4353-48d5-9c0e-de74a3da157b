document.addEventListener("DOMContentLoaded",(function(){var t,e,n,a,o,s,d=document.getElementById("app-Latteculator-form"),l=document.getElementById("app-Cupsize-form"),u=document.getElementById("app-Sprosize-form");function r(){switch(document.querySelector(".tab-active").id){case"app-Latteculator-tab":var t=parseFloat(document.getElementById("condensation").value),e=parseFloat(document.getElementById("aeration").value),n=document.querySelector(".latteculator-results");t>e?n.innerHTML='<div role="alert" class="alert alert-warning"><span class="icon-[ph--warning-bold]"></span><span>错误：充气量不能低于冷凝量</span></div>':(document.querySelector(".latteculator-results table")||(n.innerHTML='\n                            <div class="overflow-x-auto">\n                                <table class="table w-full text-lg">\n                                <tbody>\n                                    <tr>\n                                    <td class="opacity-70">咖啡固形物</td>\n                                    <td class="font-medium"><span id="coffeeweightoutput">0.00</span>g (<span id="coffeestrengthoutput">0.00</span>%)</td>\n                                    </tr>\n                                    <tr>\n                                    <td class="opacity-70">脂肪含量</td>\n                                    <td class="font-medium"><span id="fatweightoutput">0.00</span>g (<span id="fatstrengthoutput">0.00</span>%)</td>\n                                    </tr>\n                                    <tr>\n                                    <td class="opacity-70">乳糖含量</td>\n                                    <td class="font-medium"><span id="lactoseweightoutput">0.00</span>g (<span id="lactosestrengthoutput">0.00</span>%)</td>\n                                    </tr>\n                                    <tr>\n                                    <td class="opacity-70">蛋白质含量</td>\n                                    <td class="font-medium"><span id="proteinweightoutput">0.00</span>g (<span id="proteinstrengthoutput">0.00</span>%)</td>\n                                    </tr>\n                                    <tr>\n                                    <td class="opacity-70">总重量</td>\n                                    <td class="font-medium"><span id="totalweightoutput">0.00</span>g</td>\n                                    </tr>\n                                    <tr>\n                                    <td class="opacity-70">\n                                        总体积\n                                        <label for="modal-totalvolume" class="cursor-pointer">\n                                        <span class="icon-[material-symbols--info-outline] text-info"></span>\n                                        </label>\n                                    </td>\n                                    <td class="font-medium"><span id="totalvolumeoutput">0.00</span>ml (<span id="totalvolumeoutput_oz">0.00</span>oz)</td>\n                                    </tr>\n                                </tbody>\n                                </table>\n                            </div>'),function(t){var e=parseFloat(document.getElementById("coffeeweight").value),n=parseFloat(document.getElementById("TDS").value)/100,a=parseFloat(document.getElementById("milkweight").value),o=parseFloat(document.getElementById("fat").value)/100,s=parseFloat(document.getElementById("lactose").value)/100,d=parseFloat(document.getElementById("protein").value)/100,l=parseFloat(document.getElementById("condensation").value),u=parseFloat(document.getElementById("aeration").value),r=document.querySelector(".latteculator-results");if(l>u)r.innerHTML='<div role="alert" class="alert alert-warning"><span class="icon-[ph--warning-bold]"></span><span>错误：充气量不能低于冷凝量</span></div>';else{r.classList.remove("opacity-50"),document.querySelector(".latteculator-results table")||(r.innerHTML='\n                <div class="overflow-x-auto rounded-lg border border-base-300">\n                    <table class="table table-zebra w-full text-lg">\n                        <tbody>\n                            <tr class="hover">\n                                <td class="opacity-70 border-b border-base-300">咖啡含量</td>\n                                <td class="font-medium border-b border-base-300"><span id="coffeeweightoutput">0.00</span>g (<span id="coffeestrengthoutput">0.00</span>%)</td>\n                            </tr>\n                            <tr>\n                                <td class="opacity-70">脂肪含量</td>\n                                <td class="font-medium"><span id="fatweightoutput">0.00</span>g (<span id="fatstrengthoutput">0.00</span>%)</td>\n                            </tr>\n                            <tr>\n                                <td class="opacity-70">乳糖含量</td>\n                                <td class="font-medium"><span id="lactoseweightoutput">0.00</span>g (<span id="lactosestrengthoutput">0.00</span>%)</td>\n                            </tr>\n                            <tr>\n                                <td class="opacity-70">蛋白质含量</td>\n                                <td class="font-medium"><span id="proteinweightoutput">0.00</span>g (<span id="proteinstrengthoutput">0.00</span>%)</td>\n                            </tr>\n                            <tr>\n                                <td class="opacity-70">总重量</td>\n                                <td class="font-medium"><span id="totalweightoutput">0.00</span>g</td>\n                            </tr>\n                            <tr>\n                                <td class="opacity-70">总体积</td>\n                                <td class="font-medium"><span id="totalvolumeoutput">0.00</span>ml (<span id="totalvolumeoutput_oz">0.00</span>oz)</td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </div>');var c=e*n,i=a*o+.0018*e,p=a*s,m=a*d+.0012*e,g=e+a+13.5,v=e+a*(1+u/100),y=v/28.41,f=c/g*100,b=i/g*100,h=p/g*100,E=m/g*100;document.getElementById("coffeeweightoutput").textContent=c.toFixed(2),document.getElementById("coffeestrengthoutput").textContent=f.toFixed(2),document.getElementById("fatweightoutput").textContent=i.toFixed(2),document.getElementById("fatstrengthoutput").textContent=b.toFixed(2),document.getElementById("lactoseweightoutput").textContent=p.toFixed(2),document.getElementById("lactosestrengthoutput").textContent=h.toFixed(2),document.getElementById("proteinweightoutput").textContent=m.toFixed(2),document.getElementById("proteinstrengthoutput").textContent=E.toFixed(2),document.getElementById("totalweightoutput").textContent=g.toFixed(2),document.getElementById("totalvolumeoutput").textContent=Math.round(100*v)/100,document.getElementById("totalvolumeoutput_oz").textContent=Math.round(100*y)/100}}());break;case"app-Cupsize-tab":var a=parseFloat(document.getElementById("cupsizeTDS").value),o=parseFloat(document.getElementById("desiredTDS").value),s=document.querySelector(".cupsize-results");a<o?s.innerHTML='<div role="alert" class="alert alert-warning"><span class="icon-[ph--warning-bold]"></span><span>错误：目标浓度必须小于等于TDS</span></div>':(document.querySelector(".cupsize-results table")||(s.innerHTML='\n                            <div class="overflow-x-auto rounded-lg border border-base-300">\n                                <table class="table table-zebra w-full text-lg">\n                                    <tbody>\n                                        <tr class="hover">\n                                            <td class="opacity-70 border-b border-base-300">所需牛奶</td>\n                                            <td class="font-medium border-b border-base-300"><span id="milkweightoutput">0.0</span>g</td>\n                                        </tr>\n                                        <tr class="hover">\n                                            <td class="opacity-70">最终体积</td>\n                                            <td class="font-medium"><span id="cupvolumeoutput">0.0</span>ml (<span id="cupvolumeoutput_oz">0.0</span>oz)</td>\n                                        </tr>\n                                    </tbody>\n                                </table>\n                            </div>'),function(t){var e=parseFloat(document.getElementById("cupsizecoffeeweight").value),n=parseFloat(document.getElementById("cupsizeTDS").value),a=parseFloat(document.getElementById("cupsizeaeration").value),o=(e*(n/parseFloat(document.getElementById("desiredTDS").value))-e)/(1+parseFloat(document.getElementById("cupsizecondensation").value)/100),s=e+o*(1+a/100),d=s/28.41;document.getElementById("milkweightoutput").textContent=Math.round(10*o)/10,document.getElementById("cupvolumeoutput").textContent=Math.round(10*s)/10,document.getElementById("cupvolumeoutput_oz").textContent=Math.round(10*d)/10}());break;case"app-Sprosize-tab":var d=parseFloat(document.getElementById("sproTDS").value),l=parseFloat(document.getElementById("sprodesiredTDS").value),u=document.querySelector(".sprosize-results");d<l?u.innerHTML='<div role="alert" class="alert alert-warning"><span class="icon-[ph--warning-bold]"></span><span>错误：目标浓度必须小于等于TDS</span></div>':(document.querySelector(".sprosize-results table")||(u.innerHTML='\n                            <div class="overflow-x-auto rounded-lg border border-base-300">\n                                <table class="table table-zebra w-full text-lg">\n                                    <tbody>\n                                        <tr class="hover">\n                                            <td class="opacity-70">所需咖啡量</td>\n                                            <td class="font-medium"><span id="sproweightoutput">0.00</span>g</td>\n                                        </tr>\n                                    </tbody>\n                                </table>\n                            </div>'),function(t){var e=parseFloat(document.getElementById("sproTDS").value),n=parseFloat(document.getElementById("sprodesiredTDS").value),a=.01*parseFloat(document.getElementById("sproaeration").value),o=.01*parseFloat(document.getElementById("sprocondensation").value),s=parseFloat(document.getElementById("sprocupvolume").value),d=(e-n)/e,l=(1-d)*(("oz"===document.getElementById("cupvolpicker").value?28.41*s:s)/(1+d*((a-o)/(1+o))));document.getElementById("sproweightoutput").textContent=Math.round(100*l)/100}())}}document.querySelectorAll('input[type="number"], select').forEach((function(t){t.addEventListener("input",r),t.addEventListener("change",r)})),[d,l,u].forEach((function(t){t&&(t.onsubmit=function(t){return t.preventDefault()})})),t=document.querySelectorAll(".app-tab"),e=[d,l,u],n=[document.querySelector(".latteculator-results"),document.querySelector(".cupsize-results"),document.querySelector(".sprosize-results")],a=document.querySelectorAll("#calculator-info [data-calculator]"),o={"app-Latteculator-tab":"latteculator","app-Cupsize-tab":"cupsize","app-Sprosize-tab":"sprosize"},s=document.querySelector(".tab-active"),a.forEach((function(t){t.dataset.calculator===o[s.id]?t.classList.remove("hidden"):t.classList.add("hidden")})),t.forEach((function(s,d){s.addEventListener("click",(function(){t.forEach((function(t){return t.classList.remove("tab-active")})),s.classList.add("tab-active"),e.forEach((function(t){return t.classList.add("hidden")})),n.forEach((function(t){return t.classList.add("hidden")})),e[d].classList.remove("hidden"),n[d].classList.remove("hidden"),a.forEach((function(t){t.dataset.calculator===o[s.id]?t.classList.remove("hidden"):t.classList.add("hidden")})),setTimeout(r,0)}))})),r()}));