/*! For license information please see blbnccrrnc.js.LICENSE.txt */
(()=>{function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function r(){"use strict";r=function(){return t};var t={},n=Object.prototype,o=n.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",i=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function l(e,t,r,n){var o=t&&t.prototype instanceof p?t:p,a=Object.create(o.prototype),c=new E(n||[]);return a._invoke=function(e,t,r){var n="suspendedStart";return function(o,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return{value:void 0,done:!0}}for(r.method=o,r.arg=a;;){var c=r.delegate;if(c){var i=_(c,r);if(i){if(i===f)continue;return i}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=d(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===f)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(e,r,c),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=l;var f={};function p(){}function h(){}function m(){}var v={};s(v,c,(function(){return this}));var y=Object.getPrototypeOf,g=y&&y(y(A([])));g&&g!==n&&o.call(g,c)&&(v=g);var b=m.prototype=p.prototype=Object.create(v);function x(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(t,r){function n(a,c,i,u){var s=d(t[a],t,c);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==e(f)&&o.call(f,"__await")?r.resolve(f.__await).then((function(e){n("next",e,i,u)}),(function(e){n("throw",e,i,u)})):r.resolve(f).then((function(e){l.value=e,i(l)}),(function(e){return n("throw",e,i,u)}))}u(s.arg)}var a;this._invoke=function(e,t){function o(){return new r((function(r,o){n(e,t,r,o)}))}return a=a?a.then(o,o):o()}}function _(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,_(e,t),"throw"===t.method))return f;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=d(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,f;var o=n.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function A(e){if(e){var t=e[c];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function t(){for(;++r<e.length;)if(o.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return n.next=n}}return{next:L}}function L(){return{value:void 0,done:!0}}return h.prototype=m,s(b,"constructor",m),s(m,"constructor",h),h.displayName=s(m,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,s(e,u,"GeneratorFunction")),e.prototype=Object.create(b),e},t.awrap=function(e){return{__await:e}},x(w.prototype),s(w.prototype,i,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var c=new w(l(e,r,n,o),a);return t.isGeneratorFunction(r)?c:c.next().then((function(e){return e.done?e.value:c.next()}))},x(b),s(b,u,"Generator"),s(b,c,(function(){return this})),s(b,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=A,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,n){return c.type="throw",c.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n],c=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var i=o.call(a,"catchLoc"),u=o.call(a,"finallyLoc");if(i&&u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(i){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var c=a?a.completion:{};return c.type=e,c.arg=t,a?(this.method="next",this.next=a.finallyLoc,f):this.complete(c)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:A(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function n(e,t,r,n,o,a,c){try{var i=e[a](c),u=i.value}catch(e){return void r(e)}i.done?t(u):Promise.resolve(u).then(n,o)}function o(e){return function(){var t=this,r=arguments;return new Promise((function(o,a){var c=e.apply(t,r);function i(e){n(c,o,a,i,u,"next",e)}function u(e){n(c,o,a,i,u,"throw",e)}i(void 0)}))}}function a(){var e=document.createElement("div");e.className="toast toast-bottom toast-center",e.innerHTML='\n        <div class="alert alert-success">\n            <span>回购信息保存成功。</span>\n        </div>\n    ',document.body.appendChild(e),setTimeout((function(){e.remove()}),3e3)}function c(e,t,r,n){n&&!window.Alpine&&(n.style.display=t?"flex":"none"),r&&!r.hasAttribute("x-text")&&(r.textContent=t?"最短养豆期(天)":"养豆天数");try{var o=e.querySelector(".modal-box");if(o&&"undefined"!=typeof Alpine&&o.hasAttribute("x-data"))if("function"==typeof Alpine.$data){var a=Alpine.$data(o);a&&(a.isRangePeriod=t)}else o.__x&&(o.__x.$data.isRangePeriod=t)}catch(e){console.warn("Alpine更新失败:",e),r&&(r.textContent=t?"最短养豆期(天)":"养豆天数")}}function i(e,t,r,n){if(e){var o=e,a=o.cloneNode(!0);o.parentNode.replaceChild(a,o),(e=a).addEventListener("change",(function(){var e=this.checked;if(n&&!window.Alpine&&(n.style.display=e?"flex":"none"),!e){var o=r.querySelector("form");if(o){var a=o.querySelector('input[name="rest_period_max"]');a&&(a.value="")}}try{var c=r.querySelector(".modal-box");if(c&&"undefined"!=typeof Alpine){if("function"==typeof Alpine.$data){var i=Alpine.$data(c);i&&(i.isRangePeriod=e)}else c.__x&&(c.__x.$data.isRangePeriod=e);t&&!t.hasAttribute("x-text")&&(t.textContent=e?"最短养豆期(天)":"养豆天数")}}catch(r){console.warn("Alpine更新失败:",r),t&&(t.textContent=e?"最短养豆期(天)":"养豆天数")}}))}}function u(e){return s.apply(this,arguments)}function s(){return s=o(r().mark((function e(t){var n,u,s,l,d,f,h,m,v,y,g,b,x,w;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/my/occurrence/".concat(t,"/edit/"));case 3:if(!(n=e.sent).ok){e.next=35;break}return e.next=7,n.json();case 7:if(u=e.sent,s=document.getElementById("occurrence-modal"),l=document.getElementById("occurrence-form")){e.next=13;break}return console.error("Form element not found"),e.abrupt("return");case 13:if(l.reset(),(d=Alpine.evaluate(l,"occurrenceId"))&&(d.occurrenceId=t),l.removeAttribute("data-bean-id"),l.setAttribute("data-occurrence-id",t),l.querySelector('input[name="bag_weight"]').value=u.bag_weight,l.querySelector('input[name="bag_remain"]').value=u.bag_remain,l.querySelector('input[name="purchase_price"]').value=u.purchase_price,l.querySelector('input[name="roast_date"]').value=u.roast_date,l.querySelector('input[name="created_at"]').value=u.created_at,f=l.querySelector('input[name="rest_period_min"]'),h=l.querySelector('input[name="rest_period_max"]'),m=l.querySelector("input[data-rest-period-type]"),v=l.querySelector(".rest-period-label"),y=l.querySelector(".max-period-container"),f&&h){f.value=u.rest_period_min||"",h.value=u.rest_period_max||"",g=null!==u.rest_period_max&&void 0!==u.rest_period_max&&u.rest_period_max>0&&u.rest_period_max!==u.rest_period_min,m&&(m.checked=g);try{(b=s.querySelector(".modal-box"))&&"undefined"!=typeof Alpine&&b.hasAttribute("x-data")&&("function"==typeof Alpine.$data?(x=Alpine.$data(b))&&(x.isRangePeriod=g):b.__x&&(b.__x.$data.isRangePeriod=g))}catch(e){console.warn("Alpine设置失败:",e)}v&&!v.hasAttribute("x-text")&&(v.textContent=g?"最短养豆期(天)":"养豆天数"),s.showModal(),c(s,g,v,y),i(m,v,s,y),setTimeout((function(){c(s,g,v,y)}),50)}else s.showModal();w=function(){var e=o(r().mark((function e(n){var o,c,i,u,s;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),o=new FormData(this),(c=this.querySelector("input[data-rest-period-type]"))&&(c.checked&&(i=o.get("rest_period_max"))&&"0"!==i||o.delete("rest_period_max")),e.prev=4,e.next=7,fetch("/my/occurrence/".concat(t,"/edit/"),{method:"POST",body:o,headers:{"X-CSRFToken":p("csrftoken")}});case 7:if(!(u=e.sent).ok){e.next=14;break}closeOccurrenceModal(),a(),window.location.reload(),e.next=18;break;case 14:return e.next=16,u.json();case 16:s=e.sent,alert(Object.values(s.errors).join("\n"));case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(4),console.error("Error:",e.t0),alert("发生错误，请重试");case 24:case"end":return e.stop()}}),e,this,[[4,20]])})));return function(t){return e.apply(this,arguments)}}(),l.replaceWith(l.cloneNode(!0)),document.getElementById("occurrence-form").addEventListener("submit",w),e.next=36;break;case 35:alert("获取数据失败，请重试");case 36:e.next=42;break;case 38:e.prev=38,e.t0=e.catch(0),console.error("Error:",e.t0),alert("发生错误，请重试");case 42:case"end":return e.stop()}}),e,null,[[0,38]])}))),s.apply(this,arguments)}function l(e){return d.apply(this,arguments)}function d(){return d=o(r().mark((function e(t){var n,a,c;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=document.getElementById("delete-occurrence-modal"),a=document.getElementById("confirm-delete-btn"),c=function(){var e=o(r().mark((function e(){var n,o;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/my/occurrence/".concat(t,"/delete/"),{method:"POST",headers:{"X-CSRFToken":p("csrftoken")}});case 3:if(!(n=e.sent).ok){e.next=9;break}f(),window.location.reload(),e.next=13;break;case 9:return e.next=11,n.json();case 11:o=e.sent,alert(o.error||"删除失败，请重试");case 13:e.next=19;break;case 15:e.prev=15,e.t0=e.catch(0),console.error("Error:",e.t0),alert("发生错误，请重试");case 19:case"end":return e.stop()}}),e,null,[[0,15]])})));return function(){return e.apply(this,arguments)}}(),n.showModal(),a.onclick=c;case 5:case"end":return e.stop()}}),e)}))),d.apply(this,arguments)}function f(){var e=document.getElementById("delete-occurrence-modal");e&&(e.close(),document.getElementById("confirm-delete-btn").onclick=null)}function p(e){var t=null;if(document.cookie&&""!==document.cookie)for(var r=document.cookie.split(";"),n=0;n<r.length;n++){var o=r[n].trim();if(o.substring(0,e.length+1)===e+"="){t=decodeURIComponent(o.substring(e.length+1));break}}return t}window.handleOccurrenceAction=function(e,t,r){e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),"edit"===t?u(r):"delete"===t&&l(r)},document.addEventListener("DOMContentLoaded",(function(){window.closeOccurrenceModal=function(){var e=document.getElementById("occurrence-modal");e&&e.close()},document.addEventListener("click",function(){var e=o(r().mark((function e(t){var n,o,a,s,d,f,h,m,v,y,g,b,x,w,_,k,S,E,A,L,q,O,P;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.target.matches('.occurrence-btn[data-action="repurchase"]')&&!t.target.closest('.occurrence-btn[data-action="repurchase"]')){e.next=64;break}if(t.preventDefault(),n=t.target.matches(".occurrence-btn")?t.target:t.target.closest(".occurrence-btn"),o=n.getAttribute("data-bean-id"),!(a=n.closest("[data-bean-id]"))||"true"!==a.getAttribute("data-archived")){e.next=20;break}return e.prev=7,e.next=10,fetch("/my/brewlog/bean/".concat(o,"/unarchive/"),{method:"POST",headers:{"X-CSRFToken":p("csrftoken")}});case 10:if(e.sent.ok){e.next=13;break}throw new Error("取消归档失败");case 13:e.next=20;break;case 15:return e.prev=15,e.t0=e.catch(7),console.error("取消归档失败:",e.t0),alert("无法取消归档，请重试"),e.abrupt("return");case 20:return s=document.getElementById("occurrence-modal"),(d=document.getElementById("occurrence-form")).removeAttribute("data-occurrence-id"),d.setAttribute("data-bean-id",o),e.prev=24,e.next=27,fetch("/my/bean/".concat(o,"/occurrence/add/"));case 27:if((f=e.sent).ok){e.next=33;break}return e.next=31,f.text();case 31:throw h=e.sent,new Error("HTTP ".concat(f.status,": ").concat(h));case 33:return e.next=35,f.json();case 35:if(!(m=e.sent).error){e.next=38;break}throw new Error(m.error);case 38:if(v=d.querySelector('input[name="bag_weight"]'),y=d.querySelector('input[name="bag_remain"]'),g=d.querySelector('input[name="purchase_price"]'),b=d.querySelector('input[name="roast_date"]'),x=d.querySelector('input[name="created_at"]'),v.value=m.bag_weight,y.value=m.bag_remain,g.value=m.purchase_price,w=new Date,_=w.toISOString().split("T")[0],b.value=_,x.value=m.created_at,k=d.querySelector('input[name="rest_period_min"]'),S=d.querySelector('input[name="rest_period_max"]'),E=d.querySelector("input[data-rest-period-type]"),A=d.querySelector(".rest-period-label"),L=d.querySelector(".max-period-container"),k&&S){k.value=m.rest_period_min||"",S.value=m.rest_period_max||"",q=null!==m.rest_period_max&&void 0!==m.rest_period_max&&m.rest_period_max>0&&m.rest_period_max!==m.rest_period_min,E&&(E.checked=q);try{(O=s.querySelector(".modal-box"))&&"undefined"!=typeof Alpine&&O.hasAttribute("x-data")&&("function"==typeof Alpine.$data?(P=Alpine.$data(O))&&(P.isRangePeriod=q):O.__x&&(O.__x.$data.isRangePeriod=q))}catch(e){console.warn("Alpine设置失败:",e)}A&&!A.hasAttribute("x-text")&&(A.textContent=q?"最短养豆期(天)":"养豆天数"),s.showModal(),c(s,q,A,L),i(E,A,s,L),setTimeout((function(){c(s,q,A,L)}),50)}else s.showModal();e.next=62;break;case 58:e.prev=58,e.t1=e.catch(24),console.error("Error:",e.t1),alert("获取数据失败，请重试");case 62:e.next=65;break;case 64:t.target.matches('.occurrence-btn[data-action="edit"]')?(t.preventDefault(),u(t.target.getAttribute("data-bean-id"))):t.target.matches('.occurrence-btn[data-action="delete"]')?(t.preventDefault(),l(t.target.getAttribute("data-bean-id"))):t.target.matches(".modal-close-btn")&&(t.preventDefault(),closeOccurrenceModal());case 65:case"end":return e.stop()}}),e,null,[[7,15],[24,58]])})));return function(t){return e.apply(this,arguments)}}());var e=document.getElementById("occurrence-form");if(e){var n=function(){var e=o(r().mark((function e(n){var o,c,i,u,s,l,d;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),o=this.getAttribute("data-bean-id"),c=new FormData(this),(i=this.querySelector("input[data-rest-period-type]"))&&(i.checked&&(u=c.get("rest_period_max"))&&"0"!==u||c.delete("rest_period_max")),e.prev=5,e.next=8,fetch("/my/bean/".concat(o,"/occurrence/add/"),{method:"POST",body:c,headers:{"X-CSRFToken":p("csrftoken")}});case 8:if(!(s=e.sent).ok){e.next=15;break}closeOccurrenceModal(),a(),window.location.reload(),e.next=21;break;case 15:return e.next=17,s.json();case 17:l=e.sent,"",d=l.errors?Object.entries(l.errors).map((function(e){var r,n,o=(n=2,function(e){if(Array.isArray(e))return e}(r=e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a=[],c=!0,i=!1;try{for(r=r.call(e);!(c=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);c=!0);}catch(e){i=!0,o=e}finally{try{c||null==r.return||r.return()}finally{if(i)throw o}}return a}}(r,n)||function(e,r){if(e){if("string"==typeof e)return t(e,r);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}(r,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),a=o[0],c=o[1];return"".concat(a,": ").concat(c.join(", "))})).join("\n"):"提交失败，请检查输入",alert(d);case 21:e.next=27;break;case 23:e.prev=23,e.t0=e.catch(5),console.error("Error:",e.t0),alert("发生错误，请重试");case 27:case"end":return e.stop()}}),e,this,[[5,23]])})));return function(t){return e.apply(this,arguments)}}();e.addEventListener("submit",n),e.defaultSubmitHandler=n}document.querySelectorAll("#delete-occurrence-modal .modal-close-btn").forEach((function(e){e.addEventListener("click",(function(e){e.preventDefault(),f()}))}))})),document.addEventListener("alpine:init",(function(){Alpine.data("repurchaseModal",(function(){return{isRangePeriod:!1,init:function(){var e=this;this.$el.addEventListener("show",(function(t){var r=t.detail;r&&(e.isRangePeriod=!!r.rest_period_max)}));var t=this.$el.querySelector("#occurrence-form");t&&t.addEventListener("submit",(function(r){r.preventDefault();var n=new FormData(t);e.isRangePeriod||n.delete("rest_period_max"),fetch(t.action,{method:"POST",body:n,headers:{"X-CSRFToken":p("csrftoken")}}).then((function(e){return e.json()})).then((function(e){if("success"===e.status)showSuccessMessage(),setTimeout((function(){window.location.reload()}),1e3);else{var t=document.createElement("div");t.className="alert alert-error",t.innerHTML="\n                                <span>".concat(e.message||"保存失败，请重试。","</span>\n                            "),document.body.appendChild(t),setTimeout((function(){t.remove()}),3e3)}})).catch((function(e){console.error("Error:",e)}))}))}}}))}))})();