(()=>{"use strict";document.addEventListener("alpine:init",(function(){Alpine.store("globalState",{hiddenPanels:{},checkHiddenState:function(e){var t="true"===localStorage.getItem("".concat(e,"-hidden"));this.hiddenPanels[e]=t},hasAnyHiddenPanels:function(){return Object.values(this.hiddenPanels).some((function(e){return e}))},showAllPanels:function(){var e=this;Object.keys(this.hiddenPanels).forEach((function(t){localStorage.removeItem("".concat(t,"-hidden")),e.hiddenPanels[t]=!1}));var t=new URL(window.location.href);t.searchParams.set("show","true"),window.location.href=t.toString()}}),Alpine.data("brewlogHindsight",(function(e){return{isHidden:!1,isCollapsed:!1,init:function(){if("true"===new URLSearchParams(window.location.search).get("show")){if(this.isHidden=!1,localStorage.removeItem("".concat(e,"-hidden")),Alpine.store("globalState").hiddenPanels[e]=!1,window.history.replaceState){var t=new URL(window.location.href);t.searchParams.delete("show"),window.history.replaceState({},document.title,t.toString())}}else this.isHidden="true"===localStorage.getItem("".concat(e,"-hidden")),this.isCollapsed="true"===localStorage.getItem("".concat(e,"-collapsed"));Alpine.store("globalState").checkHiddenState(e)},hidePanel:function(){this.isHidden=!0,localStorage.setItem("".concat(e,"-hidden"),"true"),Alpine.store("globalState").hiddenPanels[e]=!0},showPanel:function(){this.isHidden=!1,localStorage.removeItem("".concat(e,"-hidden")),Alpine.store("globalState").hiddenPanels[e]=!1},toggleCollapse:function(){this.isCollapsed=!this.isCollapsed,this.isCollapsed?localStorage.setItem("".concat(e,"-collapsed"),"true"):localStorage.removeItem("".concat(e,"-collapsed"))},expand:function(){this.isCollapsed=!1,localStorage.removeItem("".concat(e,"-collapsed")),this.showPanel()},collapse:function(){this.isCollapsed=!0,localStorage.setItem("".concat(e,"-collapsed"),"true")}}}))}))})();