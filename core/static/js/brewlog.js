/*! For license information please see brewlog.js.LICENSE.txt */
(()=>{"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){t=function(){return r};var r={},n=Object.prototype,o=n.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function l(e,t,r,n){var o=t&&t.prototype instanceof d?t:d,i=Object.create(o.prototype),c=new k(n||[]);return i._invoke=function(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var c=r.delegate;if(c){var a=E(c,r);if(a){if(a===h)continue;return a}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=f(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===h)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(e,r,c),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}r.wrap=l;var h={};function d(){}function p(){}function m(){}var v={};s(v,c,(function(){return this}));var y=Object.getPrototypeOf,g=y&&y(y(O([])));g&&g!==n&&o.call(g,c)&&(v=g);var w=m.prototype=d.prototype=Object.create(v);function b(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function x(t,r){function n(i,c,a,u){var s=f(t[i],t,c);if("throw"!==s.type){var l=s.arg,h=l.value;return h&&"object"==e(h)&&o.call(h,"__await")?r.resolve(h.__await).then((function(e){n("next",e,a,u)}),(function(e){n("throw",e,a,u)})):r.resolve(h).then((function(e){l.value=e,a(l)}),(function(e){return n("throw",e,a,u)}))}u(s.arg)}var i;this._invoke=function(e,t){function o(){return new r((function(r,o){n(e,t,r,o)}))}return i=i?i.then(o,o):o()}}function E(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method))return h;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var n=f(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,h;var o=n.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,h):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function O(e){if(e){var t=e[c];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function t(){for(;++r<e.length;)if(o.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return n.next=n}}return{next:R}}function R(){return{value:void 0,done:!0}}return p.prototype=m,s(w,"constructor",m),s(m,"constructor",p),p.displayName=s(m,u,"GeneratorFunction"),r.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,s(e,u,"GeneratorFunction")),e.prototype=Object.create(w),e},r.awrap=function(e){return{__await:e}},b(x.prototype),s(x.prototype,a,(function(){return this})),r.AsyncIterator=x,r.async=function(e,t,n,o,i){void 0===i&&(i=Promise);var c=new x(l(e,t,n,o),i);return r.isGeneratorFunction(t)?c:c.next().then((function(e){return e.done?e.value:c.next()}))},b(w),s(w,u,"Generator"),s(w,c,(function(){return this})),s(w,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},r.values=O,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,n){return c.type="throw",c.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],c=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var a=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(a&&u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var c=i?i.completion:{};return c.type=e,c.arg=t,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(c)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:O(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},r}function r(e,t,r,n,o,i,c){try{var a=e[i](c),u=a.value}catch(e){return void r(e)}a.done?t(u):Promise.resolve(u).then(n,o)}function n(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var c=e.apply(t,n);function a(e){r(c,o,i,a,u,"next",e)}function u(e){r(c,o,i,a,u,"throw",e)}a(void 0)}))}}function o(){var e=null;if(document.cookie&&""!==document.cookie)for(var t=document.cookie.split(";"),r=0;r<t.length;r++){var n=t[r].trim();if("csrftoken="===n.substring(0,10)){e=decodeURIComponent(n.substring(10));break}}return e}document.addEventListener("DOMContentLoaded",(function(){document.addEventListener("click",(function(e){document.querySelectorAll("[data-dropdown-toggle]").forEach((function(t){var r=t.previousElementSibling;t.contains(e.target)||r.contains(e.target)||t.parentElement.removeAttribute("open")}))}));var e=document.getElementById("exportButton");e&&e.addEventListener("click",(function(e){e.preventDefault();var t=document.getElementById("exportDialog");t&&t.showModal()}));var t=document.querySelector("#exportDialog a.btn-success");t&&t.addEventListener("click",(function(e){e.preventDefault();var t=document.createElement("form");t.method="GET",t.action=this.href,document.body.appendChild(t),t.submit(),document.body.removeChild(t),document.getElementById("exportDialog").close(),setTimeout((function(){window.location.reload()}),1e3)}));var r=document.getElementById("filterForm");if(r){var n=r.querySelectorAll('input[type="date"]'),o=r.querySelector('input[name="q"]');n.forEach((function(e){e.addEventListener("change",(function(e){e.preventDefault()}))})),o.addEventListener("keypress",(function(e){"Enter"===e.key&&(e.preventDefault(),r.submit())}))}document.querySelectorAll('input[type="date"]').forEach((function(e){e.addEventListener("change",(function(){setTimeout((function(){document.getElementById("filterForm").submit()}),1e3)}))}))})),document.addEventListener("alpine:init",(function(){Alpine.store("filterState",{isOpen:!1,init:function(){this.isOpen=!1},toggle:function(){this.isOpen=!this.isOpen}}),Alpine.store("brewlogList",{compareMode:!1,selectedRecords:[],maxCompareCount:2,init:function(){var e=localStorage.getItem("compareState");if(e){var t=JSON.parse(e);this.selectedRecords=t.selectedRecords,this.compareMode=t.compareMode}},toggleCompare:function(e){this.selectedRecords.includes(e)?this.selectedRecords=this.selectedRecords.filter((function(t){return t!==e})):this.selectedRecords.length<this.maxCompareCount&&this.selectedRecords.push(e),this.selectedRecords.length===this.maxCompareCount&&(window.location.href="/my/brewlog/compare/".concat(this.selectedRecords[0],"/").concat(this.selectedRecords[1],"/")),this.compareMode=this.selectedRecords.length>0,localStorage.setItem("compareState",JSON.stringify({selectedRecords:this.selectedRecords,compareMode:this.compareMode}))},clearCompare:function(){this.selectedRecords=[],this.compareMode=!1,localStorage.removeItem("compareState")},deleteEquipment:function(e){return n(t().mark((function r(){var n;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/my/brewlog/equipment/".concat(e,"/delete/"),{method:"POST",headers:{"X-CSRFToken":o()}});case 3:return n=t.sent,t.next=6,n.json();case 6:"success"===t.sent.status&&(window.location.href="/my/brewlog/equipment/"),t.next=14;break;case 10:t.prev=10,t.t0=t.catch(0),console.error("Error:",t.t0),alert("删除失败");case 14:case"end":return t.stop()}}),r,null,[[0,10]])})))()},deleteRecord:function(e){return n(t().mark((function r(){var n;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/my/brewlog/record/".concat(e,"/delete/"),{method:"POST",headers:{"X-CSRFToken":o()}});case 3:return n=t.sent,t.next=6,n.json();case 6:"success"===t.sent.status&&(window.location.href="/my/brewlog"),t.next=14;break;case 10:t.prev=10,t.t0=t.catch(0),console.error("Error:",t.t0),alert("删除失败");case 14:case"end":return t.stop()}}),r,null,[[0,10]])})))()},archiveEquipment:function(e){return n(t().mark((function r(){var n;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/my/brewlog/equipment/".concat(e,"/archive/"),{method:"POST",headers:{"X-CSRFToken":o()}});case 3:return n=t.sent,t.next=6,n.json();case 6:"success"===t.sent.status&&(window.location.href="/my/brewlog/equipment/"),t.next=14;break;case 10:t.prev=10,t.t0=t.catch(0),console.error("Error:",t.t0),alert("归档失败");case 14:case"end":return t.stop()}}),r,null,[[0,10]])})))()},unarchiveEquipment:function(e){return n(t().mark((function r(){var n;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/my/brewlog/equipment/".concat(e,"/unarchive/"),{method:"POST",headers:{"X-CSRFToken":o()}});case 3:return n=t.sent,t.next=6,n.json();case 6:"success"===t.sent.status&&(window.location.href="/my/brewlog/equipment/"),t.next=14;break;case 10:t.prev=10,t.t0=t.catch(0),console.error("Error:",t.t0),alert("取消归档失败");case 14:case"end":return t.stop()}}),r,null,[[0,10]])})))()},toggleFavoriteEquipment:function(e){var r=this;return n(t().mark((function n(){var i,c;return t().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,!(i=document.querySelector('[data-equipment-id="'.concat(e,'"]')))||null===i.closest(".collapse-content")){t.next=6;break}return t.next=6,r.unarchiveEquipment(e);case 6:return t.next=8,fetch("/my/brewlog/equipment/".concat(e,"/toggle-favorite/"),{method:"POST",headers:{"X-CSRFToken":o()}});case 8:return c=t.sent,t.next=11,c.json();case 11:"success"===t.sent.status&&(window.location.href="/my/brewlog/equipment/"),t.next=19;break;case 15:t.prev=15,t.t0=t.catch(0),console.error("Error:",t.t0),alert("操作失败，请稍后再试");case 19:case"end":return t.stop()}}),n,null,[[0,15]])})))()}})}))})();