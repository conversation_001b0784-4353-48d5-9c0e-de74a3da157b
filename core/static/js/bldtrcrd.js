/*! For license information please see bldtrcrd.js.LICENSE.txt */
(()=>{"use strict";function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function n(){n=function(){return e};var e={},r=Object.prototype,o=r.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof p?e:p,a=Object.create(o.prototype),i=new L(r||[]);return a._invoke=function(t,e,n){var r="suspendedStart";return function(o,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw a;return{value:void 0,done:!0}}for(n.method=o,n.arg=a;;){var i=n.delegate;if(i){var s=S(i,n);if(s){if(s===h)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=d(t,e,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}(t,n,i),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var h={};function p(){}function f(){}function v(){}var g={};u(g,i,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(T([])));y&&y!==r&&o.call(y,i)&&(g=y);var w=v.prototype=p.prototype=Object.create(g);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(e,n){function r(a,i,s,c){var u=d(e[a],e,i);if("throw"!==u.type){var l=u.arg,h=l.value;return h&&"object"==t(h)&&o.call(h,"__await")?n.resolve(h.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):n.resolve(h).then((function(t){l.value=t,s(l)}),(function(t){return r("throw",t,s,c)}))}c(u.arg)}var a;this._invoke=function(t,e){function o(){return new n((function(n,o){r(t,e,n,o)}))}return a=a?a.then(o,o):o()}}function S(t,e){var n=t.iterator[e.method];if(void 0===n){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,S(t,e),"throw"===e.method))return h;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var r=d(n,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,h;var o=r.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function T(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,r=function e(){for(;++n<t.length;)if(o.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:I}}function I(){return{value:void 0,done:!0}}return f.prototype=v,u(w,"constructor",v),u(v,"constructor",f),f.displayName=u(v,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,c,"GeneratorFunction")),t.prototype=Object.create(w),t},e.awrap=function(t){return{__await:t}},b(E.prototype),u(E.prototype,s,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,a){void 0===a&&(a=Promise);var i=new E(l(t,n,r,o),a);return e.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},b(w),u(w,c,"Generator"),u(w,i,(function(){return this})),u(w,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=[];for(var n in t)e.push(n);return e.reverse(),function n(){for(;e.length;){var r=e.pop();if(r in t)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=T,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!t)for(var e in this)"t"===e.charAt(0)&&o.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,r){return i.type="throw",i.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],i=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;k(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:T(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},e}function r(t,e,n,r,o,a,i){try{var s=t[a](i),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,o)}function o(t){return function(){var e=this,n=arguments;return new Promise((function(o,a){var i=t.apply(e,n);function s(t){r(i,o,a,s,c,"next",t)}function c(t){r(i,o,a,s,c,"throw",t)}s(void 0)}))}}document.addEventListener("DOMContentLoaded",(function(){function t(t,n){return e.apply(this,arguments)}function e(){return(e=o(n().mark((function t(e,r){var o,a;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/my/brewlog/equipment/".concat(e,"/info/"));case 3:if((o=t.sent).ok){t.next=6;break}throw new Error("Network response was not ok");case 6:return t.next=8,o.json();case 8:(a=t.sent).grind_size_preset?r.value=a.grind_size_preset:r.value="",t.next=16;break;case 12:t.prev=12,t.t0=t.catch(0),console.error("Error fetching grinder preset:",t.t0),r.value="";case 16:case"end":return t.stop()}}),t,null,[[0,12]])})))).apply(this,arguments)}var r,a;!function(){var t=document.getElementById("brewing_hours"),e=document.getElementById("brewing_minutes"),n=document.getElementById("brewing_seconds"),r=document.querySelector('input[name="brewing_time"]');if(t&&e&&n&&r){if(r.value){var o=r.value.split(":");3===o.length&&(t.value=parseInt(o[0],10).toString().padStart(2,"0"),e.value=parseInt(o[1],10).toString().padStart(2,"0"),n.value=parseInt(o[2],10).toString().padStart(2,"0"),i())}var a=r.closest("form");a&&a.addEventListener("submit",(function(t){i()})),[t,e,n].forEach((function(e){e.addEventListener("input",(function(){var n=parseInt(e.value)||0;n=e===t?Math.max(n,0):Math.min(Math.max(n,0),59),e.value=n,i()})),e.addEventListener("blur",(function(){e.value=(parseInt(e.value)||0).toString().padStart(2,"0"),i()}))}))}function i(){var o=parseInt(t.value||0),a=parseInt(e.value||0),i=parseInt(n.value||0),s="".concat(o.toString().padStart(2,"0"),":").concat(a.toString().padStart(2,"0"),":").concat(i.toString().padStart(2,"0"));r.value=s}}(),r=document.querySelector('input[type="range"]'),a=document.querySelector('input[name="rating_level"]'),r&&a&&(r.addEventListener("input",(function(){a.value=r.value})),a.value=r.value),function(){var e=document.querySelector('select[name="grinding_equipment"]'),r=document.querySelector('input[name="grind_size"]');if(e&&r){var a=document.querySelector("form"),i=(null==a?void 0:a.getAttribute("action"))||"";if((i.includes("/add/")||i.includes("add_record_page"))&&!i.includes("copy")&&!r.value){var s=e.value;setTimeout((function(){s&&t(s,r)}),0)}e.addEventListener("change",o(n().mark((function e(){var o;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(o=this.value)?t(o,r):r._preventEmptySet||(r.value="");case 2:case"end":return e.stop()}}),e,this)}))))}}()})),document.addEventListener("alpine:init",(function(){Alpine.data("brewingSteps",(function(){return{stepsEnabled:!1,steps:[],sections:{brewing:!0,equipment:!0,measurement:!0,steps:!0,environment:!1},init:function(){var t=this;this.stepsEnabled="true"===this.$el.dataset.stepsEnabled;try{var e=this.$el.dataset.steps;if(e&&"[]"!==e){var n=JSON.parse(e);Array.isArray(n)&&n.length>0&&(this.steps=n.map((function(t){return{text:t.text||"",order:t.order||1,hasTimer:t.hasTimer||!1,minutes:parseInt(t.minutes)||0,seconds:parseInt(t.seconds)||0}})),this.stepsEnabled=!0,this.sections.steps=!0)}}catch(t){console.error("Error parsing steps:",t,this.$el.dataset.steps),this.steps=[]}this.$watch("stepsEnabled",(function(e){e&&(0===t.steps.length&&t.addStep(),t.sections.steps=!0)}));var r=this.$el.querySelector("form");r&&r.addEventListener("submit",(function(e){e.preventDefault(),t.steps.forEach((function(t,e){var n=r.querySelector('input[name="steps['.concat(e,'][minutes]"]')),o=r.querySelector('input[name="steps['.concat(e,'][seconds]"]')),a=r.querySelector('input[name="steps['.concat(e,'][has_timer]"]'));if(a&&a.checked){var i=n?n.value.trim():"",s=o?o.value.trim():"";""===i&&""===s||!(i&&0!==parseInt(i)||s&&0!==parseInt(s))?(a.checked=!1,n&&(n.value="0"),o&&(o.value="0")):(""===i&&(n.value="0"),""===s&&(o.value="0")),t.hasTimer=a.checked,t.minutes=parseInt(n.value)||0,t.seconds=parseInt(o.value)||0}})),r.submit()}))},addStep:function(){this.steps.push({text:"",hasTimer:!1,minutes:0,seconds:0,order:this.steps.length+1}),this.sections.steps=!0},removeStep:function(t){this.steps.splice(t,1),this.steps.forEach((function(t,e){return t.order=e+1})),0===this.steps.length&&(this.stepsEnabled=!1)},moveStep:function(t,e){var n=t+e;if(n>=0&&n<this.steps.length){var r=this.steps[t];this.steps[t]=this.steps[n],this.steps[n]=r,this.steps.forEach((function(t,e){return t.order=e+1}))}},toggleSteps:function(){this.sections.steps=!this.sections.steps}}}))}));var a=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.input=document.querySelector("input[data-tags-url]"),this.hiddenInput=document.querySelector("#flavor-tags-data"),this.dropdown=document.querySelector("#tags-dropdown"),this.selectedTagsContainer=document.querySelector("#selected-tags"),this.selectedTags=new Map,this.allTags=[],this.input&&this.init()}var r,a,i,s,c;return r=t,a=[{key:"init",value:(c=o(n().mark((function t(){var e,r=this;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.loadExistingTags();case 3:this.selectedTagsContainer.querySelectorAll(".bg-primary\\/10").forEach((function(t){var e=t.querySelector("span").textContent.trim(),n=t.querySelector("button"),o=n.dataset.tagId;o&&e&&(r.selectedTags.set(o,e),n.addEventListener("click",(function(){return r.removeTag(o)})))})),(e=this.hiddenInput.value)&&e.split(",").forEach((function(t){var e=r.allTags.find((function(e){return e.id.toString()===t}));e&&!r.selectedTags.has(t)&&r.selectedTags.set(t,e.name)})),this.updateHiddenInput(),this.input.addEventListener("keydown",this.handleKeydown.bind(this)),this.input.addEventListener("input",this.handleInput.bind(this)),this.input.addEventListener("blur",(function(){setTimeout((function(){return r.hideDropdown()}),200)})),t.next=16;break;case 13:t.prev=13,t.t0=t.catch(0),console.error("Error in TagManager initialization:",t.t0);case 16:case"end":return t.stop()}}),t,this,[[0,13]])}))),function(){return c.apply(this,arguments)})},{key:"loadExistingTags",value:(s=o(n().mark((function t(){var e,r;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch(this.input.dataset.tagsUrl);case 3:if((e=t.sent).ok){t.next=6;break}throw new Error("Network response was not ok");case 6:return t.next=8,e.json();case 8:r=t.sent,this.allTags=r,t.next=16;break;case 12:t.prev=12,t.t0=t.catch(0),console.error("Error loading existing tags:",t.t0),this.allTags=[];case 16:case"end":return t.stop()}}),t,this,[[0,12]])}))),function(){return s.apply(this,arguments)})},{key:"handleKeydown",value:function(t){if("Enter"===t.key){t.preventDefault();var e=this.input.value.trim();if(e){var n=this.allTags.find((function(t){return t.name.toLowerCase()===e.toLowerCase()}));n?this.addTag(n.id,n.name):this.createNewTag(e),this.input.value="",this.hideDropdown()}}}},{key:"createNewTag",value:(i=o(n().mark((function t(e){var r,o;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("/my/brewlog/flavor-tags/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":document.querySelector("[name=csrfmiddlewaretoken]").value},body:JSON.stringify({name:e})});case 3:if((r=t.sent).ok){t.next=6;break}throw new Error("Failed to create tag");case 6:return t.next=8,r.json();case 8:o=t.sent,this.allTags.push(o),this.addTag(o.id,o.name),t.next=16;break;case 13:t.prev=13,t.t0=t.catch(0),console.error("Error creating new tag:",t.t0);case 16:case"end":return t.stop()}}),t,this,[[0,13]])}))),function(t){return i.apply(this,arguments)})},{key:"handleInput",value:function(){var t=this,e=this.input.value.trim().toLowerCase();if(e){var n=this.allTags.filter((function(n){return n.name.toLowerCase().includes(e)&&!t.selectedTags.has(n.id.toString())}));this.showDropdown(n)}else this.hideDropdown()}},{key:"addTag",value:function(t,e){var n=this;if(!this.selectedTags.has(t.toString())){this.selectedTags.set(t.toString(),e);var r=document.createElement("div");r.className="bg-primary/10 text-primary px-2 py-1 rounded-lg flex items-center gap-2",r.innerHTML="\n                <span>".concat(e,'</span>\n                <button type="button" class="hover:text-error" data-tag-id="').concat(t,'">×</button>\n            '),r.querySelector("button").addEventListener("click",(function(){return n.removeTag(t)})),this.selectedTagsContainer.appendChild(r),this.updateHiddenInput()}}},{key:"removeTag",value:function(t){this.selectedTags.delete(t.toString());var e=Array.from(this.selectedTagsContainer.children).find((function(e){return e.querySelector('button[data-tag-id="'.concat(t,'"]'))||e.querySelector("button").dataset.tagId===t.toString()}));e&&e.remove(),this.updateHiddenInput()}},{key:"showDropdown",value:function(t){var e=this;this.dropdown.innerHTML=t.map((function(t){return'\n            <li>\n                <button type="button" class="w-full text-left" data-tag-id="'.concat(t.id,'" data-tag-name="').concat(t.name,'">\n                    ').concat(t.name,"\n                </button>\n            </li>\n        ")})).join(""),this.dropdown.querySelectorAll("button").forEach((function(t){t.addEventListener("click",(function(){var n=t.dataset.tagId,r=t.dataset.tagName;e.addTag(n,r),e.input.value="",e.hideDropdown()}))})),this.dropdown.classList.remove("hidden")}},{key:"hideDropdown",value:function(){this.dropdown.classList.add("hidden")}},{key:"updateHiddenInput",value:function(){var t=Array.from(this.selectedTags.keys()).filter((function(t){return t&&t.toString().trim()})).map((function(t){return t.toString().trim()}));this.hiddenInput?this.hiddenInput.value=t.join(","):console.error("Hidden input element not found")}}],a&&e(r.prototype,a),Object.defineProperty(r,"prototype",{writable:!1}),t}();document.addEventListener("DOMContentLoaded",(function(){var t,e;t=document.querySelector('select[name="gadget_kit"]'),e=document.querySelectorAll('input[name="gadgets"]'),t&&e.length>0&&(t.addEventListener("change",o(n().mark((function t(){var r,o,a;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=this.value,e.forEach((function(t){t.checked=!1})),!r){t.next=18;break}return t.prev=3,t.next=6,fetch("/my/brewlog/equipment/".concat(r,"/gadgets/"));case 6:if((o=t.sent).ok){t.next=9;break}throw new Error("Network response was not ok");case 9:return t.next=11,o.json();case 11:a=t.sent,e.forEach((function(t){t.checked=a.includes(parseInt(t.value))})),t.next=18;break;case 15:t.prev=15,t.t0=t.catch(3),console.error("Error fetching gadget kit components:",t.t0);case 18:case"end":return t.stop()}}),t,this,[[3,15]])})))),e.forEach((function(e){e.addEventListener("change",(function(){t.value=""}))}))),new a}))})();