(()=>{"use strict";document.addEventListener("alpine:init",(function(){Alpine.data("yearSelector",(function(){return{selectedYear:null,availableYears:[],init:function(){var e=this,t=window.location.pathname.match(/\/brewlog\/heatmap\/(\d{4})\//);this.selectedYear=t?parseInt(t[1]):(new Date).getFullYear(),fetch("/my/brewlog/heatmap/years/").then((function(e){return e.json()})).then((function(t){t.years.includes(e.selectedYear)||t.years.push(e.selectedYear),e.availableYears=t.years.sort((function(e,t){return t-e})),e.$nextTick((function(){var t=e.$el.querySelector("select");t&&(t.value=e.selectedYear)}))})).catch((function(t){console.error("Error fetching available years:",t),e.availableYears=[e.selectedYear]}))},changeYear:function(){window.location.href="/my/brewlog/heatmap/".concat(this.selectedYear,"/")}}})),Alpine.data("heatmapData",(function(){return{init:function(){var e=this,t=this.$el;this.cells=JSON.parse(t.dataset.calendar),this.currentMonthIndex=parseInt(t.dataset.currentMonth),this.$watch("$store.theme.isDark",(function(){e.updateColorScale()})),window.addEventListener("update-heatmap",(function(t){e.cells=t.detail.data,e.$nextTick((function(){return e.scrollToCurrentMonth()}))})),this.updateColorScale()},cells:[],currentMonthIndex:0,colorScale:[],updateColorScale:function(){var e=this.$store.theme.isDark;this.colorScale=e?["rgb(100, 105, 100)","rgb(200, 170, 140)","rgb(220, 140, 100)","rgb(240, 110, 60)","rgb(250, 80, 30)"]:["rgb(240, 242, 240)","rgb(255, 243, 221)","rgb(254, 225, 177)","rgb(253, 189, 111)","rgb(252, 141, 49)"]},getCellStyle:function(e){var t=this.getColorIndex(e.count),n=new Date(e.date),r=e.week;return 11===n.getMonth()&&1===e.week&&(r=53),{"background-color":this.colorScale[t],"grid-row":e.weekday+1,"grid-column":r}},getColorIndex:function(e){return 0===e?0:1===e?1:2===e?2:3===e?3:4},showDetails:function(e){this.$dispatch("show-details",{date:e.date,count:e.count})},scrollToCurrentMonth:function(){if(window.innerWidth<1024){var e=this.$el,t=e.scrollWidth/12*this.currentMonthIndex;e.scrollLeft=t}}}}))}))})();