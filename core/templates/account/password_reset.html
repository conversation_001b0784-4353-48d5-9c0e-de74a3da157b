{% extends "account/base_entrance.html" %}
{% load i18n allauth account %}
{% block head_title %}
    {% trans "密码重置" %}
{% endblock head_title %}
{% block content %}
    {% element h1 %}
        {% trans "密码重置" %}
    {% endelement %}
<div class="container mx-auto flex justify-center text-base-content xl:max-w-7xl">
    <div class="px-4 py-4 lg:px-8 lg:py-8 flex flex-col items-center">
    {% if user.is_authenticated %}
        {% include "account/snippets/already_logged_in.html" %}
    {% endif %}
    <p>
        {% trans "忘记了您的密码？请输入您的电子邮箱地址，我们将给您发送一封允许您重置密码的电子邮件。" %}
    </p>
    {% url 'account_reset_password' as reset_url %}
    {% element form form=form method="post" action=reset_url %}
        {% slot body %}
            {% csrf_token %}
            {% element fields form=form %}
            {% endelement %}
        {% endslot %}
        {% slot actions %}
            {% element button type="submit" %}
                {% trans '重置我的密码' %}
            {% endelement %}
        {% endslot %}
    {% endelement %}
    </div>
</div>
{% endblock content %}