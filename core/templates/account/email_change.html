{% extends "account/base_manage_email.html" %}
{% load i18n %}
{% load allauth %}
{% block head_title %}
    {% trans "电子邮箱地址" %}
{% endblock head_title %}
{% block content %}
    {% element h1 %}
        {% trans "电子邮箱地址" %}
    {% endelement %}
    {% if not emailaddresses %}
        {% include "account/snippets/warn_no_email.html" %}
    {% endif %}
    {% url 'account_email' as action_url %}
    {% element form method="post" action=action_url %}
        {% slot body %}
            {% csrf_token %}
            {% if current_emailaddress %}
                {% element field disabled=True type="email" value=current_emailaddress.email %}
                    {% slot label %}
                        {% translate "当前电子邮件" %}:
                    {% endslot %}
                {% endelement %}
            {% endif %}
            {% if new_emailaddress %}
                {% element field name="email" value=new_emailaddress.email disabled=True type="email" %}
                    {% slot label %}
                        {% if not current_emailaddress %}
                            {% translate "当前电子邮件" %}:
                        {% else %}
                            {% translate "更改为" %}:
                        {% endif %}
                    {% endslot %}
                    {% slot help_text %}
                        {% blocktranslate %}您的电子邮箱地址仍在等待验证。{% endblocktranslate %}
                        {% element button form="pending-email" type="submit" name="action_send" tags="minor,secondary" %}
                            {% trans '重新发送验证' %}
                        {% endelement %}
                        {% if current_emailaddress %}
                            {% element button form="pending-email" type="submit" name="action_remove" tags="danger,minor" %}
                                {% trans '取消更改' %}
                            {% endelement %}
                        {% endif %}
                    {% endslot %}
                {% endelement %}
            {% endif %}
            {% element field name="email" value=form.email.value errors=form.email.errors type="email" %}
                {% slot label %}
                    {% translate "更改为" %}:
                {% endslot %}
            {% endelement %}
        {% endslot %}
        {% slot actions %}
            {% element button name="action_add" type="submit" %}
                {% trans "更改电子邮件" %}
            {% endelement %}
        {% endslot %}
    {% endelement %}
    {% if new_emailaddress %}
        <form style="display: none"
              id="pending-email"
              method="post"
              action="{% url 'account_email' %}">
            {% csrf_token %}
            <input type="hidden" name="email" value="{{ new_emailaddress.email }}">
        </form>
    {% endif %}
{% endblock content %}
