{% extends "account/base_entrance.html" %}
{% load allauth i18n %}
{% block head_title %}
    {% trans "注册" %}
{% endblock head_title %}
{% block content %}
    {% element h1 %}
        {% trans "注册" %}
    {% endelement %}
<div class="container mx-auto flex justify-center text-base-content xl:max-w-7xl">
    <div class="px-4 py-4 lg:px-8 lg:py-8 flex flex-col items-center">
    <div class="flex flex-col gap-2 text-sm opacity-70">
        <div class="flex items-center gap-2">
            <span class="icon-[ri--login-box-line] h-4 w-4"></span>
            <div>{% blocktrans %}已经注册过账号了? <a class="link" href="{{ login_url }}">点我登录</a>。{% endblocktrans %}</div>
        </div>
    </div>
    {% url 'account_signup' as action_url %}
    {% element form form=form method="post" action=action_url tags="entrance,signup" %}
        {% slot body %}
            {% csrf_token %}
            {% element fields form=form unlabeled=True %}
            {% endelement %}
            {% if redirect_field_value %}
                <input type="hidden"
                       name="{{ redirect_field_name }}"
                       value="{{ redirect_field_value }}" />
            {% endif %}
        {% endslot %}
        {% slot actions %}
            {% element button tags="prominent,signup" type="submit" %}
                {% trans "注册" %}
            {% endelement %}
            <div class="flex flex-col items-center">
                <span class="text-xs opacity-30">注册即代表您已阅读并同意《<a class="link" href="/blog/privacy">咖啡搭子隐私政策</a>》</span>
            </div>
        {% endslot %}
    {% endelement %}
    {% if SOCIALACCOUNT_ENABLED %}
        {% include "socialaccount/snippets/login.html" with page_layout="entrance" %}
    {% endif %}
    </div>
</div>
{% endblock content %}