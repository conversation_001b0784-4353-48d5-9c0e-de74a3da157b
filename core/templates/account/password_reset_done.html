{% extends "account/base_entrance.html" %}
{% load i18n %}
{% load allauth %}
{% load account %}
{% block head_title %}
    {% trans "密码重置" %}
{% endblock head_title %}
{% block content %}
    {% element h1 %}
        {% trans "密码重置" %}
    {% endelement %}
<div class="container mx-auto flex justify-center text-base-content xl:max-w-7xl">
    <div class="px-4 py-4 lg:px-8 lg:py-8 flex flex-col items-center">
    {% if user.is_authenticated %}
        {% include "account/snippets/already_logged_in.html" %}
    {% endif %}
    <p>
        {% blocktrans %}我们已经给您发送了一封电子邮件。如果您没有收到，请检查您的垃圾邮件文件夹。如果您10分钟内仍然没有收到，请联系我们。{% endblocktrans %}
    </p>
    </div>
</div>
{% endblock content %}