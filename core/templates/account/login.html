{% extends "account/base_entrance.html" %}
{% load i18n %}
{% load allauth account %}
{% block head_title %}
    {% trans "登录" %}
{% endblock head_title %}
{% block content %}
    {% element h1 %}
        {% trans "登录" %}
    {% endelement %}
<div class="container mx-auto flex justify-center text-base-content xl:max-w-7xl">
    <div class="px-4 py-4 lg:px-8 lg:py-8 flex flex-col items-center">
    <div class="flex flex-col gap-2 text-sm opacity-70">
        <div class="flex items-center gap-2">
            <span class="icon-[mdi--account-box-plus-outline] h-4 w-4"></span>
            <div>如果你还没有咖啡搭子账号，请先<a class="link" href="{{ signup_url }}">注册账号</a>。</div>
        </div>
        <div class="flex items-center gap-2">
            <span class="icon-[mdi--lock-question] h-4 w-4"></span>
            <div>忘记密码了？ <a class="link" href="{% url 'account_reset_password' %}">点我找回密码</a>。</div>
        </div>
    </div>
    {% url 'account_login' as login_url %}
    {% element form form=form method="post" action=login_url tags="entrance,login" %}
        {% slot body %}
            {% csrf_token %}
            {% element fields form=form unlabeled=True %}
            {% endelement %}
            {% if redirect_field_value %}
                <input type="hidden"
                       name="{{ redirect_field_name }}"
                       value="{{ redirect_field_value }}" />
            {% endif %}
        {% endslot %}
        {% slot actions %}
            {% element button type="submit" tags="prominent,login" %}
                {% trans "登录" %}
            {% endelement %}
        {% endslot %}
    {% endelement %}
    {% if SOCIALACCOUNT_ENABLED %}
        {% include "socialaccount/snippets/login.html" with page_layout="entrance" %}
    {% endif %}
    </div>
</div>
{% endblock content %}