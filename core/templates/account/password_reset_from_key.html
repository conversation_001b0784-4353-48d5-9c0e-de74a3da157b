{% extends "account/base_entrance.html" %}
{% load i18n %}
{% load allauth %}
{% block head_title %}
    {% trans "更改密码" %}
{% endblock head_title %}
{% block content %}
    {% element h1 %}
        {% if token_fail %}
            {% trans "无效的令牌" %}
        {% else %}
            {% trans "更改密码" %}
        {% endif %}
    {% endelement %}
<div class="container mx-auto flex justify-center text-base-content xl:max-w-7xl">
    <div class="px-4 py-4 lg:px-8 lg:py-8 flex flex-col items-center">
    {% if token_fail %}
        {% url 'account_reset_password' as passwd_reset_url %}
        <p>
            {% blocktrans %}密码重置链接无效，可能是因为它已经被使用过了。请<a href="{{ passwd_reset_url }}">重新请求一个新的密码重置链接</a>。{% endblocktrans %}
        </p>
    {% else %}
        {% element form method="post" action=action_url %}
            {% slot body %}
                {% csrf_token %}
                {% element fields form=form %}
                {% endelement %}
            {% endslot %}
            {% slot actions %}
                {% element button type="submit" name="action" %}
                    {% trans '更改密码' %}
                {% endelement %}
            {% endslot %}
        {% endelement %}
    {% endif %}
    </div>
</div>
{% endblock content %}