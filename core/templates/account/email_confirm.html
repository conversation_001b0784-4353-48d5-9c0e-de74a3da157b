{% extends "account/base_entrance.html" %}
{% load i18n %}
{% load account %}
{% load allauth %}
{% block head_title %}
    {% trans "确认电子邮箱地址" %}
{% endblock head_title %}
{% block content %}
    {% element h1 %}
        {% trans "确认电子邮箱地址" %}
    {% endelement %}
<div class="container mx-auto flex justify-center text-base-content xl:max-w-7xl">
    <div class="px-4 py-4 lg:px-8 lg:py-8 flex flex-col items-center">
    {% if confirmation %}
        {% user_display confirmation.email_address.user as user_display %}
        {% if can_confirm %}
            <p>
                {% blocktrans with confirmation.email_address.email as email %}请确认 <a href="mailto:{{ email }}">{{ email }}</a> 是用户 {{ user_display }} 的电子邮箱地址。{% endblocktrans %}
            </p>
            {% url 'account_confirm_email' confirmation.key as action_url %}
            {% element form method="post" action=action_url %}
                {% slot actions %}
                    {% csrf_token %}
                    {% element button type="submit" %}
                        {% trans '确认' %}
                    {% endelement %}
                {% endslot %}
            {% endelement %}
        {% else %}
            <p>
                {% blocktrans %}无法确认 {{ email }}，因为它已经被另一个账户确认了。{% endblocktrans %}
            </p>
        {% endif %}
    {% else %}
        {% url 'account_email' as email_url %}
        <p>
            {% blocktrans %}这个电子邮件确认链接已过期或无效。请<a class="link text-info" href="{{ email_url }}">重新发送</a>电子邮件确认请求。{% endblocktrans %}
        </p>
    {% endif %}
    </div>
</div>
{% endblock content %}