{% extends "account/base_manage_password.html" %}
{% load allauth i18n %}

{% block head_title %}
    {% trans "修改昵称" %}
{% endblock head_title %}

{% block content %}
    {% element h1 %}
        {% trans "修改昵称" %}
    {% endelement %}

<div class="container mx-auto flex justify-center text-base-content xl:max-w-7xl">
    <div class="px-4 py-4 lg:px-8 lg:py-8 flex flex-col items-center">
        <div class="flex flex-col gap-2 text-sm">
            {% url 'account_change_first_name' as action_url %}
            {% element form form=form method="post" action=action_url %}
                {% slot body %}
                    {% csrf_token %}
                    <fieldset class="fieldset max-w-xs">
                    <label class="label flex justify-between">
                        <span class="text-base-content text-sm">请输入昵称</span>
                        <span>(最多12个字)</span>
                    </label>
                    <input type="text" name="nickname" class="input validator" value="{{ user.first_name }}" pattern="[\p{L}\p{N}\p{P}\p{S}\s]{3,12}" minlength="3" maxlength="12" title="只允许英文、数字、汉字和常见符号" required>
                    <p class="validator-hint">请输入3~12个字符以内的昵称<br/>请勿使用稀有字符或不常见符号</p>
                    </fieldset>
                {% endslot %}
                {% slot actions %}
                    {% element button type="submit" %}
                        {% trans "保存修改" %}
                    {% endelement %}
                {% endslot %}
            {% endelement %}
        </div>
        
        {# 添加消息提示区域 #}
        {% if messages %}
            <div class="mt-4">
                {% for message in messages %}
                    {% if 'nickname_change' in message.extra_tags %}
                        <div class="alert {% if message.level == DEFAULT_MESSAGE_LEVELS.SUCCESS %}alert-success{% else %}alert-warning{% endif %}">{{ message }}</div>
                    {% endif %}
                {% endfor %}
            </div>
        {% endif %}
        
        <div role="alert" class="alert alert-info mt-8">
            <span class="text-4xl">👋</span>
            <div>
                <div class="font-bold">须知：昵称仅用于站内展示。</div>
                <div class="text-sm mt-2">您的注册用户名 <code class="badge">{{ user }}</code> 仍然用于登录网站，且无法更改。</div>
            </div>
        </div>
    </div>
</div>
{% endblock content %} 