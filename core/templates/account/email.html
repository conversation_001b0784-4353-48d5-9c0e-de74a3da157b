{% extends "account/base_manage_email.html" %}
{% load allauth i18n %}
{% block head_title %}
    {% trans "电子邮箱地址" %}
{% endblock head_title %}
{% block content %}
    {% element h1 %}
        {% trans "电子邮箱地址" %}
    {% endelement %}
<div class="container mx-auto flex justify-center text-base-content xl:max-w-7xl">
    <div class="px-4 py-4 lg:px-8 lg:py-8 flex flex-col items-center">
        <div class="flex flex-col gap-2 text-sm">
        {% if emailaddresses %}
            <p>{% trans '以下电子邮箱地址与您的账户关联：' %}</p>
            {% url 'account_email' as email_url %}
            {% element form form=form action=email_url method="post" %}
                {% slot body %}
                    {% csrf_token %}
                    {% for radio in emailaddress_radios %}
                        {% with emailaddress=radio.emailaddress %}
                            {% element field type="radio" checked=radio.checked name="email" value=emailaddress.email id=radio.id %}
                                {% slot label %}
                                    {{ emailaddress.email }}
                                    {% if emailaddress.verified %}
                                        {% element badge tags="success,email,verified" %}
                                            <span class="text-success">{% translate "已验证" %}</span>
                                        {% endelement %}
                                    {% else %}
                                        {% element badge tags="warning,email,unverified" %}
                                            <span class="text-error">{% translate "未验证" %}</span>
                                        {% endelement %}
                                    {% endif %}
                                    {% if emailaddress.primary %}
                                        {% element badge tags="email,primary" %}
                                            <span class="text-primary">{% translate "主要" %}</span>
                                        {% endelement %}
                                    {% endif %}
                                {% endslot %}
                            {% endelement %}
                        {% endwith %}
                    {% endfor %}
                {% endslot %}
                {% slot actions %}
                    {% element button type="submit" name="action_primary" %}
                        {% trans '设为主要' %}
                    {% endelement %}
                    {% element button tags="secondary" type="submit" name="action_send" %}
                        {% trans '重新发送验证' %}
                    {% endelement %}
                    {% element button tags="danger,delete" type="submit" name="action_remove" %}
                        {% trans '移除' %}
                    {% endelement %}
                {% endslot %}
            {% endelement %}
        {% else %}
            {% include "account/snippets/warn_no_email.html" %}
        {% endif %}
        {% if can_add_email %}
            <div class="mt-8">
            {% element h2 %}
                {% trans "添加电子邮箱地址：" %}
            {% endelement %}
            </div>
            {% url 'account_email' as action_url %}
            {% element form form=form method="post" action=action_url %}
                {% slot body %}
                    {% csrf_token %}
                    {% element fields form=form %}
                    {% endelement %}
                {% endslot %}
                befac
                {% slot actions %}
                    {% element button name="action_add" type="submit" %}
                        {% trans "添加" %}
                    {% endelement %}
                {% endslot %}
            {% endelement %}
        {% endif %}
        </div>
    </div>
</div>
{% endblock content %}
{% block extra_body %}
    <script type="text/javascript">
(function() {
  var message = "{% trans '您真的要移除选中的电子邮箱地址吗？' %}";
  var actions = document.getElementsByName('action_remove');
  if (actions.length) {
    actions[0].addEventListener("click", function(e) {
      if (! confirm(message)) {
        e.preventDefault();
      }
    });
  }
})();
    </script>
{% endblock extra_body %}
