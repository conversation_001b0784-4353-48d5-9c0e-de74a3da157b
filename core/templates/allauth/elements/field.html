{% load allauth %}
<p>
    <label for="{{ attrs.id }}">
        {% slot label %}
        {% endslot %}
    </label>
    {% if attrs.type == "textarea" %}
        <textarea {% if attrs.required %}required{% endif %}
                  {% if attrs.rows %}rows="{{ attrs.rows }}"{% endif %}
                  {% if attrs.disabled %}disabled{% endif %}
                  {% if attrs.readonly %}readonly{% endif %}
                  {% if attrs.checked %}checked{% endif %}
                  {% if attrs.name %}name="{{ attrs.name }}"{% endif %}
                  {% if attrs.id %}id="{{ attrs.id }}"{% endif %}
                  {% if attrs.placeholder %}placeholder="{{ attrs.placeholder }}"{% endif %}>{% slot value %}{% endslot %}</textarea>
    {% else %}
        <input {% if attrs.required %}required{% endif %}
               {% if attrs.disabled %}disabled{% endif %}
               {% if attrs.readonly %}readonly{% endif %}
               {% if attrs.checked %}checked{% endif %}
               {% if attrs.name %}name="{{ attrs.name }}"{% endif %}
               {% if attrs.id %}id="{{ attrs.id }}"{% endif %}
               {% if attrs.placeholder %}placeholder="{{ attrs.placeholder }}"{% endif %}
               {% if attrs.autocomplete %}autocomplete="{{ attrs.autocomplete }}"{% endif %}
               value="{{ attrs.value|default_if_none:"" }}"
               type="{{ attrs.type }}">
    {% endif %}
    {% if slots.help_text %}
        <span>{% slot help_text %}</span>
        {% endslot %}
    {% endif %}
</p>
