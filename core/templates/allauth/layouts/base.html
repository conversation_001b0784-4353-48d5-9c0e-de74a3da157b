{% extends "base.html" %}
{% load i18n %}
{% block title %}{% block head_title %}{% endblock head_title %}{% endblock %}
{% block body %}
    {% if messages %}
        <div>
            <strong>{% trans "消息:" %}</strong>
            <ul>
                {% for message in messages %}<li>{{ message }}</li>{% endfor %}
            </ul>
        </div>
    {% endif %}
    <div>
        <strong>{% trans "菜单:" %}</strong>
        <ul>
            {% if user.is_authenticated %}
                <li>
                    <a href="{% url 'account_email' %}">{% trans "更改邮箱" %}</a>
                </li>
                <li>
                    <a href="{% url 'account_logout' %}">{% trans "退出登录" %}</a>
                </li>
            {% else %}
                <li>
                    <a href="{% url 'account_login' %}">{% trans "登录" %}</a>
                </li>
                <li>
                    <a href="{% url 'account_signup' %}">{% trans "注册" %}</a>
                </li>
            {% endif %}
        </ul>
    </div>
    {% block content %}
    {% endblock content %}
{% endblock body %}
{% block extra_body %}
{% endblock extra_body %}
