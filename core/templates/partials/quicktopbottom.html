<div x-data="{isTop: true}" x-init="window.addEventListener('scroll', () => {isTop = window.scrollY < 100})" class="fixed bottom-4 right-4 flex flex-col lg:hidden z-50">
  <button x-show="!isTop" @click="window.scrollTo({top: 0, behavior: 'smooth'})" class="bg-base-300 text-2xl leading-none text-base-content p-2 rounded-full shadow-lg w-12 h-12" x-transition:enter="transition ease-out duration-300 transform" x-transition:enter-start="opacity-0 scale-75" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-300 transform" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-75" aria-label="Scroll to top">
    <span class="icon-[icon-park-outline--to-top-one] align-middle"></span>
  </button>
  <button x-show="isTop" @click="window.scrollTo({top: document.body.scrollHeight, behavior: 'smooth'})" class="bg-base-300 text-2xl leading-none text-base-content p-2 rounded-full shadow-lg w-12 h-12" x-transition:enter="transition ease-out duration-300 transform" x-transition:enter-start="opacity-0 scale-75" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-300 transform" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-75" aria-label="Scroll to bottom">
    <span class="icon-[icon-park-outline--to-bottom-one] align-middle"></span>
  </button>
</div>