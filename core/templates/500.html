{% extends "base.html" %}
{% block title %}500 - 内部服务器错误{% endblock %}
{% block body_class %}template-500{% endblock %}
{% block content %}
<div class="min-h-screen flex items-center justify-center text-base-content">
  <div class="text-center">
    <h1 class="block text-7xl font-bold text-gray-800 sm:text-9xl dark:text-white">500</h1>
    <h1 class="text-4xl font-bold mb-4">内部服务器错误</h1>
    <p class="text-lg mb-8">我们正在努力恢复服务，请稍后再试</p>
    
    <!-- 自动重试计时器 -->
    <div class="mb-8">
      <span>将在 <span id="countdown">10</span> 秒后自动重试</span>
    </div>
    
    <!-- 手动重试按钮 -->
    <button 
      class="btn btn-primary"
      onclick="window.location.reload()">
      立即重试
    </button>
    
    <!-- 返回首页 -->
    <a href="/" class="btn btn-ghost ml-4">
      返回首页
    </a>
  </div>
</div>

<script>
// 自动重试倒计时
let countdown = 10;
const countdownEl = document.getElementById('countdown');

const timer = setInterval(() => {
  countdown--;
  countdownEl.textContent = countdown;
  
  if (countdown <= 0) {
    clearInterval(timer);
    window.location.reload();
  }
}, 1000);
</script>
{% endblock %} 