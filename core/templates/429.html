{% extends "base.html" %}
{% block title %}429 - 请求过多{% endblock %}
{% block body_class %}template-429{% endblock %}
{% block content %}
<div class="min-h-[50vh] flex items-center justify-center py-10">
    <div class="text-center p-8 max-w-lg">
        <div class="mb-4">
            <i class="text-6xl text-warning icon-[ph--warning-circle-bold]"></i>
        </div>
        <h1 class="text-2xl font-bold mb-4">访问频率超限</h1>
        
        {% if request.path_info == '/u/signup/' %}
        <p class="mb-4">抱歉，当前注册人数较多，请稍后再试。为了保证服务质量，我们限制了每个IP每小时最多注册5次。</p>
        {% elif 'login' in request.path_info %}
        <p class="mb-4">登录尝试次数过多，请稍后再试。如果您忘记了密码，可以尝试重置密码。</p>
        {% elif 'password' in request.path_info %}
        <p class="mb-4">密码操作请求过于频繁，请稍后再试。</p>
        {% elif 'email' in request.path_info %}
        <p class="mb-4">邮件发送请求过于频繁，请稍后再试。每个邮箱每小时最多可以请求2次验证邮件。</p>
        {% else %}
        <p class="mb-4">您的操作过于频繁，请稍后再试。</p>
        {% endif %}
        
        <div class="mt-6">
            <a href="/" class="btn btn-primary">返回首页</a>
        </div>
    </div>
</div>
{% endblock %} 