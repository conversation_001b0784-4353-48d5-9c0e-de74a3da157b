from allauth.account.forms import <PERSON>ginForm, SignupForm, ResetPasswordForm, ResetPasswordKeyForm, AddEmailForm, ChangePasswordForm
from django import forms
from my.utils import DivErrorList

class CustomLoginForm(LoginForm):
    def __init__(self, *args, **kwargs):
        super(CustomLoginForm, self).__init__(*args, **kwargs)
        self.fields['login'].widget = forms.TextInput(attrs={'class': 'input w-full', 'placeholder': '用户名或电子邮箱地址'})
        self.fields['password'].widget = forms.PasswordInput(attrs={'class': 'input w-full', 'placeholder': '密码'})
        self.fields['login'].label = ''
        self.fields['password'].label = ''
        self.fields["password"].help_text = ''
        self.error_class = DivErrorList

class CustomSignupForm(SignupForm):
    def __init__(self, *args, **kwargs):
        super(CustomSignupForm, self).__init__(*args, **kwargs)
        self.fields['email'].widget = forms.TextInput(attrs={'class': 'input w-full', 'placeholder': '电子邮箱地址'})
        self.fields['username'].widget = forms.TextInput(attrs={'class': 'input w-full', 'placeholder': '用户名（至少5个字符）'})
        self.fields['password1'].widget = forms.PasswordInput(attrs={'class': 'input w-full', 'placeholder': '密码'})
        self.fields['email'].label = ''
        self.fields['username'].label = ''
        self.fields['password1'].label = ''
        self.fields["password1"].help_text = '<span class="text-sm opacity-50">密码不得少于8个字符，必须包含数字和英文。</span>'
        self.error_class = DivErrorList

class CustomResetPasswordForm(ResetPasswordForm):
    def __init__(self, *args, **kwargs):
        super(CustomResetPasswordForm, self).__init__(*args, **kwargs)
        self.fields['email'].widget = forms.TextInput(attrs={'class': 'input w-full', 'placeholder': '电子邮箱地址'})
        self.fields['email'].label = ''
        self.error_class = DivErrorList

class CustomResetPasswordKeyForm(ResetPasswordKeyForm):
    def __init__(self, *args, **kwargs):
        super(CustomResetPasswordKeyForm, self).__init__(*args, **kwargs)
        self.fields['password1'].widget = forms.PasswordInput(attrs={'class': 'input w-full', 'placeholder': '请输入新密码'})
        self.fields['password2'].widget = forms.PasswordInput(attrs={'class': 'input w-full', 'placeholder': '请再次输入新密码'})
        self.fields['password1'].label = ''
        self.fields['password2'].label = ''
        self.error_class = DivErrorList

class CustomAddEmailForm(AddEmailForm):
    def __init__(self, *args, **kwargs):
        super(CustomAddEmailForm, self).__init__(*args, **kwargs)
        self.fields['email'].widget = forms.TextInput(attrs={'class': 'input w-full', 'placeholder': '请输入新的E-mail地址'})
        self.fields['email'].label = ''
        self.error_class = DivErrorList

class CustomChangePasswordForm(ChangePasswordForm):
    def __init__(self, *args, **kwargs):
        super(CustomChangePasswordForm, self).__init__(*args, **kwargs)
        self.fields['oldpassword'].widget = forms.PasswordInput(attrs={'class': 'input w-full', 'placeholder': '请输入当前密码'})
        self.fields['password1'].widget = forms.PasswordInput(attrs={'class': 'input w-full', 'placeholder': '请输入新密码'})
        self.fields['password2'].widget = forms.PasswordInput(attrs={'class': 'input w-full', 'placeholder': '请再次输入新密码'})
        self.fields['oldpassword'].label = ''
        self.fields['password1'].label = ''
        self.fields['password2'].label = ''
        self.error_class = DivErrorList