"""
Django settings for core project.

Generated by 'django-admin startproject' using Django 4.0.5.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.0/ref/settings/
"""

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
import os
import environ
from datetime import timedelta

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
BASE_DIR = os.path.dirname(PROJECT_DIR)

# environ
ROOT_DIR = environ.Path(__file__)-3
env = environ.Env()
env.read_env(ROOT_DIR.path('.env').root)
SECRET_KEY = env('SECRET_KEY')
DEBUG = env.bool('DEBUG')

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/


# Application definition

INSTALLED_APPS = [
    "blog",
    "home",
    "search",
    "article",
    "bean",
    "tool",
    "my",
    "recipe",
    "equipment",
    "wechat",
    "iosapp",
    "corsheaders",
    "rest_framework",
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",  # JWT令牌黑名单
    "wagtail.contrib.search_promotions",
    "wagtail.contrib.forms",
    "wagtail.contrib.redirects",
    "wagtail.embeds",
    "wagtail.sites",
    "wagtail.users",
    "wagtail.snippets",
    "wagtail.documents",
    "wagtail.images",
    "wagtail.search",
    "wagtail.admin",
    "wagtail",
    "modelcluster",
    "taggit",
    "wagtailmarkdown",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "multiselectfield",
    "django.contrib.sitemaps",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.sites",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "wagtail.contrib.redirects.middleware.RedirectMiddleware",
    "allauth.account.middleware.AccountMiddleware",
]

ROOT_URLCONF = "core.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(PROJECT_DIR, "templates"),
        ],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                # home应用的SocialAcount snippets全局调用于footer：
                "core.context_processors.social_accounts",
                # my应用的CoffeeQuote snippets全局调用于brewlog项目：
                "core.context_processors.coffee_quote",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"

# allauth stuff
AUTHENTICATION_BACKENDS = (
    "django.contrib.auth.backends.ModelBackend",
    "allauth.account.auth_backends.AuthenticationBackend",
)

# 添加 allauth 频率限制配置
ACCOUNT_RATE_LIMITS = {
    # 注册限制：每IP每小时最多5次
    "signup": "5/h/ip",
    
    # 登录限制：每IP每分钟最多10次
    "login": "10/m/ip",
    
    # 登录失败限制：每IP每5分钟最多5次，每用户名每5分钟最多3次
    "login_failed": "5/5m/ip,3/5m/key",
    
    # 密码重置请求限制：每IP每小时最多8次，每邮箱每小时最多2次
    "reset_password": "8/h/ip,2/h/key",
    
    # 密码重置页面访问限制：每IP每小时最多10次
    "reset_password_from_key": "10/h/ip",
    
    # 修改密码限制：每用户每小时最多3次
    "change_password": "3/h/user",
    
    # 邮箱管理相关操作限制：每用户每小时最多5次
    "manage_email": "5/h/user",
    
    # 邮箱验证发送限制：每邮箱每小时最多2次
    "confirm_email": "2/h/key",
    
    # 重新认证限制：每用户每小时最多5次
    "reauthenticate": "5/h/user",
}

SITE_ID = 1
LOGIN_URL = '/u/signin/'
LOGIN_REDIRECT_URL = '/'
ACCOUNT_AUTHENTICATION_METHOD = "username_email"
ACCOUNT_CONFIRM_EMAIL_ON_GET = True
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_EMAIL_VERIFICATION = "mandatory"
ACCOUNT_LOGIN_ON_EMAIL_CONFIRMATION = True
ACCOUNT_LOGOUT_ON_GET = True
ACCOUNT_LOGIN_ON_PASSWORD_RESET = True
ACCOUNT_LOGOUT_REDIRECT_URL = '/'
ACCOUNT_PRESERVE_USERNAME_CASING = False
ACCOUNT_SESSION_REMEMBER = True
ACCOUNT_SIGNUP_PASSWORD_ENTER_TWICE = False
ACCOUNT_USERNAME_BLACKLIST = ["admin"]
ACCOUNT_USERNAME_MIN_LENGTH = 5
ACCOUNT_DEFAULT_HTTP_PROTOCOL = "https"
## custom forms.py
ACCOUNT_FORMS = {
    'login': 'core.forms.CustomLoginForm',
    'signup': 'core.forms.CustomSignupForm',
    'reset_password': 'core.forms.CustomResetPasswordForm',
    'reset_password_from_key': 'core.forms.CustomResetPasswordKeyForm',
    'add_email': 'core.forms.CustomAddEmailForm',
    'change_password': 'core.forms.CustomChangePasswordForm'
}
# Email settings
EMAIL_BACKEND = env("EMAIL_BACKEND")
EMAIL_HOST = env("EMAIL_HOST")
EMAIL_PORT = env("EMAIL_PORT")
EMAIL_HOST_USER = env("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = env("EMAIL_HOST_PASSWORD")
EMAIL_USE_TLS = env.bool("EMAIL_USE_TLS")
DEFAULT_FROM_EMAIL = env("DEFAULT_FROM_EMAIL")

# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': env("DB_NAME"),
        'USER': env("DB_USER"),
        'PASSWORD': env("DB_PASSWORD"),
        'HOST': env("DB_HOST"),
        'PORT': env("DB_PORT"),
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = "zh-hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_L10N = True

USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/

STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
]

STATICFILES_DIRS = [
    os.path.join(PROJECT_DIR, "static"),
]

# ManifestStaticFilesStorage is recommended in production, to prevent outdated
# JavaScript / CSS assets being served from cache (e.g. after a Wagtail upgrade).
# See https://docs.djangoproject.com/en/4.0/ref/contrib/staticfiles/#manifeststaticfilesstorage
STATICFILES_STORAGE = "django.contrib.staticfiles.storage.ManifestStaticFilesStorage"

STATIC_ROOT = os.path.join(BASE_DIR, "static")
STATIC_URL = "/static/"

MEDIA_ROOT = os.path.join(BASE_DIR, "media")
MEDIA_URL = "/media/"


# Wagtail settings

WAGTAIL_SITE_NAME = "core"

# WeChat Mini Program settings
WECHAT_MINI_PROGRAM = {
    'APP_ID': env('WX_MINIAPP_APPID'),
    'APP_SECRET': env('WX_MINIAPP_SECRET'),
}

# Search
# https://docs.wagtail.org/en/stable/topics/search/backends.html
WAGTAILSEARCH_BACKENDS = {
    "default": {
        "BACKEND": "wagtail.search.backends.database",
    }
}

# Base URL to use when referring to full URLs within the Wagtail admin backend -
# e.g. in notification emails. Don't include '/admin' or a trailing slash
WAGTAILADMIN_BASE_URL = "https://www.kafeidazi.com"

# page config
WAGTAIL_ALLOW_UNICODE_SLUGS = True
## Don't add a trailing slash to Wagtail-served URLs
WAGTAIL_APPEND_SLASH = False
APPEND_SLASH = False

# app config
WAGTAILMARKDOWN = {
    "autodownload_fontawesome": True,
    "allowed_tags": ['div', 'figcaption'],  # optional. a list of HTML tags. e.g. ['div', 'p', 'a']
    "allowed_styles": [],  # optional. a list of styles
    # optional. a dict with HTML tag as key and a list of attributes as value
    "allowed_attributes": {},
    # optional. Possible values: "extend" or "override". Defaults to "extend".
    "allowed_settings_mode": "extend",
    "extensions": ["sane_lists", "footnotes", "nl2br", "abbr", "fenced_code", "admonition"],  # optional. a list of python-markdown supported extensions, see https://python-markdown.github.io/sitemap.html
    # optional. a dictionary with the extension name as key, and its configuration as value
    "extension_configs": {},
    # optional. Possible values: "extend" or "override". Defaults to "extend".
    "extensions_settings_mode": "extend",
}
## hit_count
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
'''
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
    'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'logfile.log',  # 指定日志文件路径
    },
    },
    'loggers': {
    'django': {
            'handlers': ['file'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'django-allauth': {
            'handlers': ['file'],
            'level': 'DEBUG',
        'propagate': True,
        },
    },
}
'''
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_CLASS": "redis.BlockingConnectionPool",
            "CONNECTION_POOL_CLASS_KWARGS": {
                "max_connections": 15,
                "timeout": 5,
                "retry_on_timeout": True,
                "queue_timeout": 3,
            },
            "COMPRESSOR": "django_redis.compressors.zlib.ZlibCompressor",
            "COMPRESS_MIN_LEN": 64,
            "SOCKET_CONNECT_TIMEOUT": 2,
            "SOCKET_TIMEOUT": 2,
            "RETRY_ON_TIMEOUT": True,
            "HEALTH_CHECK_INTERVAL": 30,
            "IGNORE_EXCEPTIONS": True,
            "RETRY_ATTEMPTS": 3,
            "RETRY_DELAY": 0.1,
        },
        "KEY_PREFIX": "kfdz",
        "TIMEOUT": {
            "default": 3600,
            "session": 86400,
            "static": 604800,
        }
    }
}

# Redis 会话设置
SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"
SESSION_COOKIE_AGE = 31536000  # 设置为一年（以秒为单位）
SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # 关闭浏览器时不失效

# 缓存压缩设置
REDIS_COMPRESS_MIN_LENGTH = 64
REDIS_COMPRESS_LEVEL = 6

# 缓存键前缀
CACHE_KEY_PREFIX = "kfdz"

# 缓存超时时间配置
CACHE_TIMEOUT = {
    'DEFAULT': 3600,
    'SHORT': 300,
    'MEDIUM': 1800,
    'LONG': 86400,
    'NEVER': None,
}

# 4. 添加请求限流设置
RATELIMIT_ENABLE = True
RATELIMIT_USE_CACHE = "default"
RATELIMIT_FAIL_OPEN = True  # 限流失败时允许请求通过

# CORS settings
CORS_ALLOW_ALL_ORIGINS = DEBUG  # 开发环境允许所有域名访问
CORS_ALLOWED_ORIGINS = env.list('CORS_ALLOWED_ORIGINS', default=[
    "https://www.kafeidazi.com",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
])

# 允许所有子域名
CORS_ALLOWED_ORIGIN_REGEXES = env.list('CORS_ALLOWED_ORIGIN_REGEXES', default=[
    r"^https://.*\.ngrok-free\.app$",
])

CORS_ALLOW_CREDENTIALS = env.bool('CORS_ALLOW_CREDENTIALS', default=True)

CORS_ALLOW_METHODS = env.list('CORS_ALLOW_METHODS', default=[
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
])

CORS_ALLOW_HEADERS = env.list('CORS_ALLOW_HEADERS', default=[
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'x-token',
    'x-user-id',
    'ngrok-skip-browser-warning',
    'ngrok-auth-user',
    'ngrok-auth-pass',
])

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',  # 添加JWT认证
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
}

# SimpleJWT配置
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=24),  # 设置访问令牌有效期为24小时
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),  # 设置刷新令牌有效期为30天
    'ROTATE_REFRESH_TOKENS': True,  # 刷新令牌时自动生成新的刷新令牌
    'BLACKLIST_AFTER_ROTATION': True,  # 在令牌刷新后，旧的刷新令牌自动加入黑名单
    'UPDATE_LAST_LOGIN': True,  # 在用户登录时更新user.last_login字段
    
    'ALGORITHM': 'HS256',  # 使用HS256算法签名令牌
    'SIGNING_KEY': SECRET_KEY,  # 使用Django的密钥签名令牌
    'VERIFYING_KEY': None,  # 不使用公开密钥验证
    
    'AUTH_HEADER_TYPES': ('Bearer',),  # 设置认证头部类型
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',  # 认证头部名称
    'USER_ID_FIELD': 'id',  # User模型的ID字段名
    'USER_ID_CLAIM': 'user_id',  # 在令牌中表示用户ID的声明
    
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),  # 使用AccessToken类
    'TOKEN_TYPE_CLAIM': 'token_type',  # 令牌类型声明
    
    'JTI_CLAIM': 'jti',  # JWT ID声明
    
    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',  # 滑动令牌刷新过期声明
    'SLIDING_TOKEN_LIFETIME': timedelta(hours=24),  # 滑动令牌有效期
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=30),  # 滑动令牌刷新有效期
}

# 在开发环境中禁用CSRF
if DEBUG:
    CSRF_TRUSTED_ORIGINS = [
        'http://localhost:8000',
        'http://127.0.0.1:8000',
        'https://*.ngrok-free.app',
    ]
    
    # 为iOS API禁用CSRF
    CSRF_EXEMPT_PATHS = [
        r'^/ios/api/',
    ]
    
    def CSRF_MIDDLEWARE_EXEMPT(request):
        path = request.path_info.lstrip('/')
        if any(path.startswith(m.lstrip('^')) for m in CSRF_EXEMPT_PATHS):
            return True
        return False