from django.core.cache import cache
from functools import wraps
import hashlib

def make_cache_key(prefix, *args, **kwargs):
    """Generate a cache key from prefix and arguments"""
    key_parts = [prefix]
    key_parts.extend([str(arg) for arg in args])
    key_parts.extend([f"{k}:{v}" for k, v in sorted(kwargs.items())])
    key = ":".join(key_parts)
    return hashlib.md5(key.encode()).hexdigest()

def cache_article_data(timeout=1800):  # 30 minutes default
    """Cache decorator for article data"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = make_cache_key("article_data", *args, **kwargs)
            
            # Try to get cached data
            cached_data = cache.get(cache_key)
            if cached_data is not None:
                return cached_data
            
            # If no cached data, call the function
            result = func(*args, **kwargs)
            
            # Cache the result
            cache.set(cache_key, result, timeout)
            return result
        return wrapper
    return decorator

def clear_article_cache(article_id):
    """Clear all cache related to an article"""
    # Clear article data cache
    cache_key = make_cache_key("article_data", article_id)
    cache.delete(cache_key) 