from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from iosapp.cache_utils import invalidate_ios_cache
from django_redis import get_redis_connection
import logging
import time
from django.utils import timezone
from datetime import timedelta

User = get_user_model()
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '管理iOS应用的缓存'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear-all',
            action='store_true',
            help='清除所有iOS应用缓存',
        )
        
        parser.add_argument(
            '--clear-endpoint',
            dest='endpoint',
            type=str,
            help='清除指定API端点的缓存，例如：brewlog_list'
        )
        
        parser.add_argument(
            '--clear-user',
            dest='user_id',
            type=int,
            help='清除指定用户的缓存'
        )
        
        parser.add_argument(
            '--username',
            dest='username',
            type=str,
            help='通过用户名清除用户的缓存'
        )
        
        parser.add_argument(
            '--stats',
            action='store_true',
            help='显示缓存统计信息'
        )
        
        parser.add_argument(
            '--keys',
            action='store_true',
            help='显示所有iOS缓存键'
        )
        
        parser.add_argument(
            '--warmup',
            action='store_true',
            help='预热常用缓存'
        )

    def handle(self, *args, **options):
        try:
            # 获取Redis连接
            redis = get_redis_connection("default")
            
            # 显示缓存统计信息
            if options['stats']:
                self.show_cache_stats(redis)
                return
                
            # 显示缓存键
            if options['keys']:
                self.show_cache_keys(redis)
                return
                
            # 通过用户名查找用户ID
            if options['username']:
                try:
                    user = User.objects.get(username=options['username'])
                    options['user_id'] = user.id
                except User.DoesNotExist:
                    raise CommandError(f"找不到用户名为 {options['username']} 的用户")
            
            # 预热缓存
            if options['warmup']:
                self.warmup_cache(redis, options)
                return
                
            # 清除所有缓存
            if options['clear_all']:
                self.stdout.write("正在清除所有iOS缓存...")
                invalidate_ios_cache()
                self.stdout.write(self.style.SUCCESS("所有iOS缓存已清除"))
                return
                
            # 清除特定端点的缓存
            if options['endpoint']:
                endpoint = options['endpoint']
                user_id = options.get('user_id')
                
                if user_id:
                    try:
                        user = User.objects.get(id=user_id)
                        self.stdout.write(f"正在清除用户 {user.username} (ID: {user_id}) 的 {endpoint} 缓存...")
                        invalidate_ios_cache(endpoint, user_id)
                        self.stdout.write(self.style.SUCCESS(f"用户 {user.username} 的 {endpoint} 缓存已清除"))
                    except User.DoesNotExist:
                        raise CommandError(f"找不到ID为 {user_id} 的用户")
                else:
                    self.stdout.write(f"正在清除所有用户的 {endpoint} 缓存...")
                    invalidate_ios_cache(endpoint)
                    self.stdout.write(self.style.SUCCESS(f"所有用户的 {endpoint} 缓存已清除"))
                return
                
            # 清除特定用户的所有缓存
            if options['user_id']:
                user_id = options['user_id']
                try:
                    user = User.objects.get(id=user_id)
                    self.stdout.write(f"正在清除用户 {user.username} (ID: {user_id}) 的所有缓存...")
                    invalidate_ios_cache(user_id=user_id)
                    self.stdout.write(self.style.SUCCESS(f"用户 {user.username} 的所有缓存已清除"))
                except User.DoesNotExist:
                    raise CommandError(f"找不到ID为 {user_id} 的用户")
                return
                
            # 如果没有指定任何操作，显示帮助信息
            self.print_help('manage.py', 'ios_cache_manage')
            
        except Exception as e:
            logger.error(f"管理iOS缓存时出错: {str(e)}", exc_info=True)
            raise CommandError(f"管理iOS缓存时出错: {str(e)}")
    
    def show_cache_stats(self, redis):
        """显示缓存统计信息"""
        self.stdout.write("===== iOS缓存统计信息 =====")
        
        # 获取iOS缓存键数量
        keys = list(redis.scan_iter("ios:*"))
        total_keys = len(keys)
        
        # 获取内存使用
        try:
            memory_info = redis.info('memory')
            used_memory = memory_info.get('used_memory_human', 'N/A')
        except:
            used_memory = 'N/A'
            
        # 分析缓存键类型
        endpoint_stats = {}
        user_stats = {}
        
        for key in keys:
            key_str = key.decode('utf-8') if isinstance(key, bytes) else key
            parts = key_str.split(':')
            
            if len(parts) >= 3:
                # 获取端点统计
                endpoint = parts[1]
                if endpoint not in endpoint_stats:
                    endpoint_stats[endpoint] = 0
                endpoint_stats[endpoint] += 1
                
                # 获取用户统计
                if len(parts) >= 4 and parts[2].isdigit():
                    user_id = int(parts[2])
                    if user_id not in user_stats:
                        user_stats[user_id] = 0
                    user_stats[user_id] += 1
        
        # 输出统计结果
        self.stdout.write(f"总缓存键数量: {total_keys}")
        self.stdout.write(f"Redis内存使用: {used_memory}")
        
        # 输出端点统计
        self.stdout.write("\n----- 按端点统计 -----")
        for endpoint, count in sorted(endpoint_stats.items(), key=lambda x: x[1], reverse=True):
            self.stdout.write(f"{endpoint}: {count} 个缓存")
            
        # 输出用户统计
        if user_stats:
            self.stdout.write("\n----- 按用户统计 -----")
            for user_id, count in sorted(user_stats.items(), key=lambda x: x[1], reverse=True)[:10]:  # 只显示前10个
                try:
                    user = User.objects.get(id=user_id)
                    self.stdout.write(f"{user.username} (ID: {user_id}): {count} 个缓存")
                except User.DoesNotExist:
                    self.stdout.write(f"未知用户 (ID: {user_id}): {count} 个缓存")
                    
        # 如果用户统计超过10个，显示还有更多
        if len(user_stats) > 10:
            self.stdout.write(f"...还有 {len(user_stats) - 10} 个用户")
    
    def show_cache_keys(self, redis):
        """显示所有缓存键"""
        self.stdout.write("===== iOS缓存键列表 =====")
        
        keys = list(redis.scan_iter("ios:*"))
        keys.sort()
        
        for key in keys:
            key_str = key.decode('utf-8') if isinstance(key, bytes) else key
            
            # 尝试获取键的过期时间
            ttl = redis.ttl(key)
            expire_info = f"(TTL: {ttl}秒)" if ttl > 0 else "(永久)"
            
            self.stdout.write(f"{key_str} {expire_info}")
            
        self.stdout.write(f"\n共 {len(keys)} 个缓存键")
    
    def warmup_cache(self, redis, options):
        """预热常用缓存"""
        self.stdout.write("开始预热iOS应用缓存...")
        
        user_id = options.get('user_id')
        
        # 获取要预热的常用端点
        common_endpoints = [
            'equipment_list',
            'bean_list', 
            'brewlog_list',
            'brewlog_statistics',
            'brew_methods_list',
            'user_profile'
        ]
        
        # 选择要预热的用户
        if user_id:
            try:
                users = [User.objects.get(id=user_id)]
                self.stdout.write(f"将为用户 {users[0].username} 预热缓存")
            except User.DoesNotExist:
                raise CommandError(f"找不到ID为 {user_id} 的用户")
        else:
            # 默认为最活跃的10个用户预热
            try:
                from iosapp.models import IOSDevice
                # 获取最近活跃的10个用户
                active_devices = IOSDevice.objects.order_by('-last_active')[:10]
                users = []
                for device in active_devices:
                    if device.user not in users:
                        users.append(device.user)
                
                if not users:
                    # 如果没有找到活跃设备，则使用最近10个用户
                    users = User.objects.order_by('-last_login')[:10]
                    
                self.stdout.write(f"将为 {len(users)} 个活跃用户预热缓存")
            except Exception as e:
                self.stdout.write(self.style.WARNING(f"获取活跃用户时出错: {str(e)}"))
                users = User.objects.order_by('-date_joined')[:5]
                self.stdout.write(f"将为 {len(users)} 个最新用户预热缓存")
        
        # 开始预热
        start_time = time.time()
        total_keys = 0
        
        for user in users:
            self.stdout.write(f"为用户 {user.username} (ID: {user.id}) 预热缓存...")
            
            # 清除用户现有缓存，确保获取最新数据
            invalidate_ios_cache(user_id=user.id)
            
            for endpoint in common_endpoints:
                # 这里只是模拟预热，实际情况应该调用API端点
                # 由于缓存机制与视图函数绑定，这里我们只能进行模拟
                key = f"ios:{endpoint}:{user.id}:data"
                redis.setex(key, 3600, '{"prewarmed": true, "time": "' + str(timezone.now()) + '"}')
                total_keys += 1
                
        duration = time.time() - start_time
        self.stdout.write(self.style.SUCCESS(f"缓存预热完成，共预热 {total_keys} 个缓存键，耗时 {duration:.2f} 秒")) 