from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from iosapp.models import IOSDevice
from django.utils import timezone
from datetime import timedelta
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '检查和管理iOS设备数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--list', 
            action='store_true', 
            help='列出所有设备'
        )
        parser.add_argument(
            '--user', 
            type=str,
            help='指定用户名，查看该用户的设备'
        )
        parser.add_argument(
            '--clean-old', 
            type=int,
            help='清理指定天数未登录的设备'
        )
        parser.add_argument(
            '--device-id', 
            type=str,
            help='查询指定设备ID的信息'
        )

    def handle(self, *args, **options):
        if options['list']:
            self.list_devices()
        elif options['user']:
            self.list_user_devices(options['user'])
        elif options['clean_old']:
            self.clean_old_devices(options['clean_old'])
        elif options['device_id']:
            self.check_device(options['device_id'])
        else:
            self.stdout.write(self.style.WARNING('请提供有效的操作参数。使用 --help 查看帮助。'))

    def list_devices(self):
        """列出所有设备"""
        devices = IOSDevice.objects.all().order_by('-last_login')
        count = devices.count()
        self.stdout.write(self.style.SUCCESS(f'共找到 {count} 个iOS设备'))
        
        for device in devices:
            self.stdout.write(f'设备ID: {device.device_id}')
            self.stdout.write(f'  用户: {device.user.username}')
            self.stdout.write(f'  最后登录: {device.last_login}')
            self.stdout.write(f'  创建时间: {device.created_at}')
            self.stdout.write(f'  推送令牌: {"已设置" if device.push_token else "未设置"}')
            self.stdout.write('')

    def list_user_devices(self, username):
        """列出指定用户的设备"""
        try:
            user = User.objects.get(username=username)
            devices = IOSDevice.objects.filter(user=user).order_by('-last_login')
            count = devices.count()
            self.stdout.write(self.style.SUCCESS(f'用户 {username} 共有 {count} 个iOS设备'))
            
            for device in devices:
                self.stdout.write(f'设备ID: {device.device_id}')
                self.stdout.write(f'  最后登录: {device.last_login}')
                self.stdout.write(f'  创建时间: {device.created_at}')
                self.stdout.write(f'  推送令牌: {"已设置" if device.push_token else "未设置"}')
                self.stdout.write('')
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'用户 {username} 不存在'))

    def clean_old_devices(self, days):
        """清理指定天数未登录的设备"""
        cutoff_date = timezone.now() - timedelta(days=days)
        devices = IOSDevice.objects.filter(last_login__lt=cutoff_date)
        count = devices.count()
        
        if count > 0:
            self.stdout.write(self.style.WARNING(f'即将删除 {count} 个 {days} 天未登录的设备'))
            confirm = input("是否继续? (y/n): ")
            if confirm.lower() == 'y':
                devices.delete()
                self.stdout.write(self.style.SUCCESS(f'已删除 {count} 个设备'))
            else:
                self.stdout.write(self.style.WARNING('操作已取消'))
        else:
            self.stdout.write(self.style.SUCCESS(f'没有找到 {days} 天未登录的设备'))

    def check_device(self, device_id):
        """查询指定设备ID的信息"""
        try:
            device = IOSDevice.objects.get(device_id=device_id)
            self.stdout.write(self.style.SUCCESS(f'设备信息:'))
            self.stdout.write(f'设备ID: {device.device_id}')
            self.stdout.write(f'用户: {device.user.username} (ID: {device.user.id})')
            self.stdout.write(f'最后登录: {device.last_login}')
            self.stdout.write(f'创建时间: {device.created_at}')
            self.stdout.write(f'推送令牌: {"已设置" if device.push_token else "未设置"}')
        except IOSDevice.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'未找到设备ID: {device_id}'))
