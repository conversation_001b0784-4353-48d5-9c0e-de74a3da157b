from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.core.cache import cache
from my.models import Equipment, get_equipment_usage_cache_key, get_equipment_last_used_cache_key
from iosapp.cache_utils import invalidate_ios_cache
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '刷新所有设备的缓存，确保usage_count和last_used字段能正确返回'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user',
            type=int,
            help='指定用户ID进行刷新',
        )
        parser.add_argument(
            '--equipment',
            type=int,
            help='指定设备ID进行刷新',
        )

    def handle(self, *args, **options):
        user_id = options.get('user')
        equipment_id = options.get('equipment')
        
        equipment_query = Equipment.objects.all()
        
        # 如果指定了用户ID，则只刷新该用户的设备
        if user_id:
            try:
                user = User.objects.get(id=user_id)
                equipment_query = equipment_query.filter(user=user)
                self.stdout.write(f"将刷新用户 {user.username} (ID: {user_id}) 的设备缓存")
            except User.DoesNotExist:
                self.stderr.write(f"用户ID {user_id} 不存在")
                return
        
        # 如果指定了设备ID，则只刷新该设备
        if equipment_id:
            equipment_query = equipment_query.filter(id=equipment_id)
            if not equipment_query.exists():
                self.stderr.write(f"设备ID {equipment_id} 不存在")
                return
            else:
                self.stdout.write(f"将刷新设备ID {equipment_id} 的缓存")
        
        # 查询所有符合条件的设备
        equipment_count = equipment_query.count()
        self.stdout.write(f"找到 {equipment_count} 个设备需要刷新缓存")
        
        # 记录已处理的用户，避免重复刷新iOS缓存
        processed_users = set()
        
        # 遍历所有设备，刷新缓存
        for equipment in equipment_query:
            # 清除该设备的缓存
            cache.delete(get_equipment_usage_cache_key(equipment.id))
            cache.delete(get_equipment_last_used_cache_key(equipment.id))
            
            # 触发一次调用，使缓存重新生成
            try:
                usage_count = equipment.get_usage_count()
                last_used = equipment.get_last_used_datetime()
                self.stdout.write(f"设备 {equipment.name} (ID: {equipment.id}) 缓存刷新成功，使用次数: {usage_count}")
            except Exception as e:
                self.stderr.write(f"刷新设备 {equipment.name} (ID: {equipment.id}) 缓存时出错: {str(e)}")
            
            # 记录用户ID，后续刷新iOS缓存
            processed_users.add(equipment.user.id)
        
        # 刷新iOS端设备列表缓存
        for user_id in processed_users:
            try:
                invalidate_ios_cache('equipment_list', user_id)
                self.stdout.write(f"用户ID {user_id} 的iOS设备列表缓存已刷新")
            except Exception as e:
                self.stderr.write(f"刷新用户ID {user_id} 的iOS缓存时出错: {str(e)}")
        
        self.stdout.write(self.style.SUCCESS(f"成功处理 {len(processed_users)} 个用户的 {equipment_count} 个设备缓存"))