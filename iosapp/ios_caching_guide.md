# iOS客户端缓存使用与更新指南

本文档提供了关于如何在iOS客户端中正确处理缓存和数据更新的指导。

## 服务器端缓存机制

服务器使用多层缓存策略来提高性能：

1. **Redis缓存**：服务器端使用Redis缓存API响应，减轻数据库负担
2. **HTTP缓存控制**：通过HTTP头指导客户端缓存行为
3. **条件请求**：支持ETag和Last-Modified机制

## iOS客户端最佳实践

### 1. 数据版本检查

iOS客户端可以使用数据版本API检查是否需要刷新数据：

```swift
func checkForUpdates() async {
    // 获取本地存储的最后更新时间戳
    let lastUpdate = UserDefaults.standard.integer(forKey: "beans_last_update")
    
    // 构建请求URL
    let urlString = "\(apiBaseURL)/data/version/?data_types=beans"
    let url = URL(string: urlString)!
    var request = URLRequest(url: url)
    request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
    
    do {
        let (data, _) = try await URLSession.shared.data(for: request)
        let decoder = JSONDecoder()
        let versionInfo = try decoder.decode(DataVersionResponse.self, from: data)
        
        // 检查服务器数据是否比本地数据新
        if let beansVersion = versionInfo.versions["beans"], beansVersion > lastUpdate {
            // 需要刷新数据
            await fetchCoffeeBeans(forceRefresh: true)
            
            // 更新本地存储的时间戳
            UserDefaults.standard.set(beansVersion, forKey: "beans_last_update")
        }
    } catch {
        print("检查更新失败: \(error)")
    }
}
```

### 2. 强制刷新参数

当需要强制更新数据时，在请求中加入`force_refresh=true`参数：

```swift
func fetchCoffeeBeans(forceRefresh: Bool = false) async {
    var urlComponents = URLComponents(string: "\(apiBaseURL)/beans/")!
    
    // 添加分页参数
    var queryItems = [URLQueryItem(name: "page_size", value: "20")]
    
    // 如果需要强制刷新，添加强制刷新参数
    if forceRefresh {
        queryItems.append(URLQueryItem(name: "force_refresh", value: "true"))
    }
    
    urlComponents.queryItems = queryItems
    
    var request = URLRequest(url: urlComponents.url!)
    request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
    
    // 如果需要强制刷新，添加禁用缓存的HTTP头
    if forceRefresh {
        request.cachePolicy = .reloadIgnoringLocalCacheData
        request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
        request.addValue("no-cache", forHTTPHeaderField: "Pragma")
    }
    
    // 执行请求...
}
```

### 3. 自动刷新机制

在以下场景下自动刷新数据：

1. **视图出现时**：当列表视图出现或应用从后台恢复时

```swift
struct BeanListView: View {
    @StateObject private var viewModel = BeanListViewModel()
    
    var body: some View {
        List(viewModel.beans) { bean in
            // 列表内容
        }
        .onAppear {
            Task {
                await viewModel.checkForUpdates()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            Task {
                await viewModel.checkForUpdates()
            }
        }
    }
}
```

2. **数据操作后**：在对咖啡豆进行添加、修改、删除等操作后

```swift
func archiveCoffeeBean(bean: CoffeeBean) async {
    // 归档咖啡豆的API请求
    
    // 成功后刷新列表
    if response.success {
        // 更新本地时间戳
        if let timestamp = response.updated_at {
            UserDefaults.standard.set(timestamp, forKey: "beans_last_update")
        }
        
        // 刷新列表数据
        await fetchCoffeeBeans(forceRefresh: true)
    }
}
```

### 4. 实现刷新控制

添加用户刷新控制：

```swift
List {
    // 列表内容
}
.refreshable {
    // 下拉刷新时调用
    await viewModel.fetchCoffeeBeans(forceRefresh: true)
}
```

## 响应时间戳

所有修改数据的API响应都包含`updated_at`时间戳，应当保存这个时间戳用于后续比较：

```swift
struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let message: String?
    let data: T?
    let updated_at: Int?
}
```

## 性能与流量优化建议

1. **定向刷新**：仅刷新必要的数据，而不是所有数据
2. **增量更新**：考虑实现增量更新机制，只获取已更改的数据
3. **预缓存**：在用户不活跃时预缓存可能需要的数据
4. **批量请求**：合并多个小请求为一个大请求
5. **网络感知**：根据网络状况调整缓存策略

## 数据一致性

为了确保数据一致性，建议：

1. 在进行任何更改操作后刷新相关列表
2. 定期（如启动时和从后台恢复时）检查数据版本
3. 重要操作（如删除）后始终强制刷新
4. 提供明确的用户反馈，指示数据是否为最新 