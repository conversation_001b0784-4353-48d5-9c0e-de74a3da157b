from functools import wraps
import json
import hashlib
import time
from django.utils import timezone
from django.core.cache import cache
from django.http import HttpResponse
from rest_framework.response import Response
from django.conf import settings
from django_redis import get_redis_connection
import logging
from datetime import timedelta
from django.core.serializers.json import DjangoJ<PERSON>NEncoder
from typing import Any, Optional, Dict, List, Union
import functools

logger = logging.getLogger(__name__)

# 导入项目已有的缓存工具类
try:
    from my.cache_utils import cache_manager, get_cache_key
    from my.constants import CACHE_TIMEOUT
except ImportError:
    # 如果无法导入项目已有的缓存工具，则创建简化版本
    logger.warning("无法导入项目已有的缓存工具，使用简化版")

    # 缓存超时设置
    CACHE_TIMEOUT = {
        'DEFAULT': 3600,     # 默认1小时
        'SHORT': 300,        # 5分钟
        'MEDIUM': 1800,      # 30分钟
        'LONG': 3600,        # 1小时
        'DAILY': 86400,      # 24小时
        'WEEKLY': 604800,    # 1周
    }

    # 统一缓存前缀
    CACHE_PREFIX = 'iosapp'

    def get_cache_key(*args):
        """生成统一格式的缓存键"""
        key_parts = [str(arg) for arg in args]
        return f"{CACHE_PREFIX}:" + ":".join(key_parts)

    class SimpleCacheManager:
        """简化版缓存管理类"""

        def __init__(self):
            self.fallback_cache = {}  # 内存缓存作为备用
            try:
                self.redis = get_redis_connection("default")
            except:
                self.redis = None
                logger.warning("Redis连接失败，仅使用内存缓存")

        def get(self, *args) -> Any:
            """获取缓存"""
            key = get_cache_key(*args)
            try:
                if self.redis:
                    data = self.redis.get(key)
                    return json.loads(data) if data else None
                return self.fallback_cache.get(key)
            except Exception as e:
                logger.warning(f"获取缓存出错: {str(e)}")
                return self.fallback_cache.get(key)

        def set(self, *args, value: Any, timeout: int = None, **kwargs) -> bool:
            """设置缓存"""
            key = get_cache_key(*args)
            if timeout is None:
                timeout = CACHE_TIMEOUT['DEFAULT']

            try:
                serialized_value = json.dumps(value, cls=DjangoJSONEncoder)
                if self.redis:
                    self.redis.setex(key, timeout, serialized_value)
                self.fallback_cache[key] = value
                return True
            except Exception as e:
                logger.error(f"设置缓存出错: {str(e)}")
                self.fallback_cache[key] = value
                return False

        def delete_pattern(self, pattern: str) -> bool:
            """删除匹配模式的缓存"""
            try:
                if self.redis:
                    pattern = f"{CACHE_PREFIX}:{pattern}"
                    for key in self.redis.scan_iter(pattern):
                        self.redis.delete(key)
                # 清理内存缓存
                full_pattern = f"{CACHE_PREFIX}:{pattern}"
                for key in list(self.fallback_cache.keys()):
                    if key.startswith(full_pattern.replace('*', '')):
                        del self.fallback_cache[key]
                return True
            except Exception as e:
                logger.error(f"删除缓存模式出错: {str(e)}")
                return False

    # 创建简化版缓存管理器实例
    cache_manager = SimpleCacheManager()

# iOS客户端缓存配置
IOS_CACHE_CONFIG = {
    # API端点及其缓存时间(秒)
    'equipment_list': CACHE_TIMEOUT['MEDIUM'],  # 设备列表缓存30分钟
    'bean_list': CACHE_TIMEOUT['MEDIUM'],        # 咖啡豆列表缓存
    'brewlog_list': CACHE_TIMEOUT['MEDIUM'],     # 冲煮记录列表缓存
    'brewlog_statistics': CACHE_TIMEOUT['MEDIUM'], # 冲煮统计缓存
    'brew_methods_list': CACHE_TIMEOUT['DAILY'], # 冲煮方法列表缓存24小时
    'hindsight_data': CACHE_TIMEOUT['MEDIUM'],  # 后见之明数据缓存30分钟
    'heatmap_data': CACHE_TIMEOUT['MEDIUM'],    # 热力图数据缓存30分钟
    'bean_calendar_data': CACHE_TIMEOUT['MEDIUM'], # 咖啡豆日历数据缓存
    'recipe_list': CACHE_TIMEOUT['MEDIUM'],     # 配方列表缓存30分钟
    'user_profile': CACHE_TIMEOUT['DAILY'],     # 用户资料缓存24小时
    'user_devices': CACHE_TIMEOUT['MEDIUM'],    # 用户设备列表缓存30分钟

    # HTTP缓存控制
    'enable_etag': True,                       # 启用ETag
    'enable_last_modified': True,              # 启用Last-Modified
    'enable_client_caching': True,             # 启用客户端缓存控制
    'max_client_cache_age': 300,               # 客户端最大缓存时间(秒)
    'must_revalidate': True,                   # 是否必须重新验证缓存
    'no_store': False,                         # 是否禁止存储内容
}

def ios_cache_response(endpoint_name, timeout=None):
    """
    iOS API响应缓存装饰器

    参数:
        endpoint_name: API端点名称，对应IOS_CACHE_CONFIG中的配置
        timeout: 自定义缓存超时时间(秒)，默认使用IOS_CACHE_CONFIG中的配置
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查是否禁用缓存
            if request.GET.get('no_cache') == 'true' or request.GET.get('force_refresh') == 'true':
                # 如果请求要求不使用缓存，直接调用视图函数
                response = view_func(request, *args, **kwargs)

                # 在响应中添加禁止缓存的头
                response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                response['Pragma'] = 'no-cache'
                response['Expires'] = '0'

                return response

            # 确定缓存超时时间
            cache_timeout = timeout or IOS_CACHE_CONFIG.get(endpoint_name, CACHE_TIMEOUT['DEFAULT'])

            # 构建缓存键
            cache_parts = [
                'ios',
                endpoint_name,
                request.user.id if request.user.is_authenticated else 'anonymous'
            ]

            # 添加URL参数到缓存键
            if request.GET:
                # 排除no_cache参数
                params = {k: v for k, v in request.GET.items() if k != 'no_cache'}
                if params:
                    params_hash = hashlib.md5(json.dumps(params, sort_keys=True).encode()).hexdigest()
                    cache_parts.append(params_hash)

            # 添加路径参数到缓存键
            cache_parts.extend(str(v) for v in kwargs.values())

            # 检查HTTP缓存控制
            if IOS_CACHE_CONFIG['enable_client_caching']:
                etag_header = request.headers.get('If-None-Match')
                last_modified_header = request.headers.get('If-Modified-Since')

                # 获取缓存的ETag和Last-Modified
                cached_meta = cache_manager.get(*cache_parts, 'meta')

                if cached_meta:
                    if IOS_CACHE_CONFIG['enable_etag'] and etag_header:
                        if etag_header == cached_meta.get('etag'):
                            return HttpResponse(status=304)  # Not Modified

                    if IOS_CACHE_CONFIG['enable_last_modified'] and last_modified_header:
                        if last_modified_header == cached_meta.get('last_modified'):
                            return HttpResponse(status=304)  # Not Modified

            # 尝试获取缓存的响应数据
            cached_data = cache_manager.get(*cache_parts, 'data')

            if cached_data is not None:
                # 构造Response对象
                response = Response(cached_data)

                # 添加缓存控制头
                if IOS_CACHE_CONFIG['enable_client_caching'] and cached_meta:
                    if IOS_CACHE_CONFIG['enable_etag'] and 'etag' in cached_meta:
                        response['ETag'] = cached_meta['etag']

                    if IOS_CACHE_CONFIG['enable_last_modified'] and 'last_modified' in cached_meta:
                        response['Last-Modified'] = cached_meta['last_modified']

                    # 添加额外的缓存控制头
                    response['Cache-Control'] = f'max-age={cache_timeout}, private'

                logger.debug(f"返回缓存的响应 - 端点: {endpoint_name}, 用户: {request.user.id if request.user.is_authenticated else 'anonymous'}")
                return response

            # 执行视图函数获取响应
            response = view_func(request, *args, **kwargs)

            # 只缓存成功的响应
            if hasattr(response, 'status_code') and 200 <= response.status_code < 300:
                # 获取响应数据
                if hasattr(response, 'data'):
                    response_data = response.data
                elif isinstance(response, HttpResponse):
                    try:
                        response_data = json.loads(response.content)
                    except:
                        response_data = response.content.decode('utf-8')
                else:
                    response_data = response

                # 缓存响应数据
                cache_manager.set(*cache_parts, 'data', value=response_data, timeout=cache_timeout)

                # 准备并缓存HTTP缓存控制元数据
                if IOS_CACHE_CONFIG['enable_client_caching']:
                    meta = {}

                    if IOS_CACHE_CONFIG['enable_etag']:
                        # 生成ETag
                        def ensure_serializable(obj):
                            if isinstance(obj, dict):
                                return {k: ensure_serializable(v) for k, v in obj.items()}
                            elif isinstance(obj, (list, tuple)):
                                return [ensure_serializable(item) for item in obj]
                            elif isinstance(obj, (int, float, str, bool, type(None))):
                                return obj
                            else:
                                # 将其他类型转换为字符串
                                return str(obj)

                        # 应用序列化安全处理
                        serializable_data = ensure_serializable(response_data)
                        content_hash = hashlib.md5(json.dumps(serializable_data, sort_keys=True).encode()).hexdigest()
                        etag = f'W/"{content_hash}"'
                        meta['etag'] = etag
                        response['ETag'] = etag

                    if IOS_CACHE_CONFIG['enable_last_modified']:
                        # 设置Last-Modified
                        last_modified = timezone.now().strftime('%a, %d %b %Y %H:%M:%S GMT')
                        meta['last_modified'] = last_modified
                        response['Last-Modified'] = last_modified

                    # 缓存元数据
                    cache_manager.set(*cache_parts, 'meta', value=meta, timeout=cache_timeout)

                    # 添加缓存控制头
                    cache_control_parts = [f'max-age={cache_timeout}', 'private']

                    # 添加缓存验证和存储策略
                    if IOS_CACHE_CONFIG.get('must_revalidate', False):
                        cache_control_parts.append('must-revalidate')

                    if IOS_CACHE_CONFIG.get('no_store', False):
                        cache_control_parts.append('no-store')

                    response['Cache-Control'] = ', '.join(cache_control_parts)

                logger.debug(f"缓存API响应 - 端点: {endpoint_name}, 用户: {request.user.id if request.user.is_authenticated else 'anonymous'}")

            return response
        return wrapper
    return decorator

def invalidate_ios_cache(endpoint_name=None, user_id=None):
    """
    清除iOS客户端缓存

    参数:
        endpoint_name: 要清除的API端点名称，None表示清除所有端点的缓存
        user_id: 用户ID，None表示清除所有用户的缓存
    """
    patterns = []

    if endpoint_name and user_id:
        patterns.append(f"ios:{endpoint_name}:{user_id}:*")
    elif endpoint_name:
        patterns.append(f"ios:{endpoint_name}:*")
    elif user_id:
        patterns.append(f"ios:*:{user_id}:*")
    else:
        patterns.append("ios:*")

    for pattern in patterns:
        cache_manager.delete_pattern(pattern)
        logger.info(f"清除iOS缓存 - 模式: {pattern}")

    # 如果是设备相关的缓存，更新设备版本号
    if endpoint_name == 'equipment_list' and user_id:
        # 更新设备数据版本号
        from django.core.cache import cache
        import time

        cache_key = f'equipment_version:{user_id}'
        current_timestamp = int(time.time())

        # 更新版本号，确保比当前时间更新
        cache.set(cache_key, current_timestamp, timeout=None)  # 无超时时间，持久存储
        logger.info(f"更新设备数据版本号 - 用户: {user_id}, 新版本: {current_timestamp}")

def get_hash_for_model_collection(objects, fields=None):
    """
    为模型集合生成哈希值，用于检测集合是否发生变化

    参数:
        objects: 模型对象的查询集
        fields: 要包含在哈希计算中的字段列表，None表示包含所有字段
    """
    hash_data = []

    for obj in objects:
        if hasattr(obj, 'updated_at'):
            hash_data.append(str(obj.updated_at))
        elif hasattr(obj, 'created_at'):
            hash_data.append(str(obj.created_at))

        if fields:
            obj_data = {field: getattr(obj, field) for field in fields if hasattr(obj, field)}
        else:
            # 尝试获取所有非关系字段
            obj_data = {field.name: getattr(obj, field.name)
                       for field in obj._meta.fields
                       if not field.is_relation}

        hash_data.append(str(obj_data))

    # 生成哈希
    return hashlib.md5(''.join(hash_data).encode()).hexdigest()