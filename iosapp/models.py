from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.conf import settings
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from rest_framework_simplejwt.exceptions import TokenError
import logging

"""
iOS应用模型模块

该模块包含与iOS客户端应用相关的数据模型和业务逻辑。
主要包括：
- IOSDevice: iOS设备记录模型，用于管理设备注册、活动状态和黑名单功能
- IOSDeviceManager: 设备模型管理器，提供设备查询和创建的便捷方法
- JWT令牌处理相关方法: 处理令牌生成、验证和黑名单功能

将业务逻辑从视图层移至模型层，使代码更容易维护和扩展。
"""

User = get_user_model()
logger = logging.getLogger(__name__)

# 获取令牌有效期设置
TOKEN_LIFETIME = getattr(settings, 'SIMPLE_JWT', {}).get('ACCESS_TOKEN_LIFETIME')
REFRESH_TOKEN_LIFETIME = getattr(settings, 'SIMPLE_JWT', {}).get('REFRESH_TOKEN_LIFETIME')

class IOSDeviceManager(models.Manager):
    """iOS设备模型管理器"""
    
    def register_or_update_device(self, user, device_id, device_data=None):
        """
        注册或更新iOS设备信息
        
        Args:
            user: 用户对象
            device_id: 设备ID
            device_data: 包含设备信息的字典
            
        Returns:
            IOSDevice: 设备对象
            bool: 是否为新设备
        """
        device_data = device_data or {}
        try:
            device, created = self.update_or_create(
                device_id=device_id,
                defaults={
                    'user': user, 
                    'last_login': timezone.now(),
                    'last_active': timezone.now(),
                    'device_model': device_data.get('device_model'),
                    'device_os_version': device_data.get('device_os_version'),
                    'app_version': device_data.get('app_version')
                }
            )
            return device, created
        except Exception as e:
            logger.error(f"设备注册失败 - 错误: {str(e)}", exc_info=True)
            return None, False
    
    def is_blacklisted(self, device_id):
        """
        检查设备是否在黑名单中
        
        Args:
            device_id: 设备ID
            
        Returns:
            bool: 设备是否在黑名单中
        """
        return self.filter(device_id=device_id, is_blacklisted=True).exists()
    
    def get_user_devices(self, user):
        """
        获取用户的所有设备
        
        Args:
            user: 用户对象
            
        Returns:
            QuerySet: 设备查询集
        """
        return self.filter(user=user).order_by('-last_login')
    
    def refresh_token(self, refresh_token, device_id=None, device_data=None):
        """
        刷新用户的JWT令牌
        
        Args:
            refresh_token: 刷新令牌字符串
            device_id: 设备ID
            device_data: 设备信息
            
        Returns:
            dict: 包含新令牌的字典
            Exception: 如果刷新失败则返回异常
        """
        try:
            # 解析刷新令牌
            token = RefreshToken(refresh_token)
            
            # 验证设备ID是否匹配
            if not IOSDevice.verify_device_id_in_token(token, device_id):
                logger.warning(f"设备ID不匹配 - 令牌中: {token.get('device_id')}, 请求中: {device_id}")
                raise TokenError('设备ID不匹配，令牌刷新失败')
            
            # 生成新令牌
            new_access_token = str(token.access_token)
            
            # 更新设备活动记录
            if device_id:
                try:
                    device = self.filter(device_id=device_id).first()
                    if device:
                        device.update_refresh()
                        if device_data:
                            device.update_device_info(device_data)
                except Exception as e:
                    logger.error(f"更新设备记录失败: {str(e)}", exc_info=True)
            
            # 成功返回
            logger.info("令牌刷新成功")
            return {
                'access': new_access_token,
                'refresh': str(token),
                'expires_in': int(TOKEN_LIFETIME.total_seconds())
            }
            
        except TokenError as e:
            logger.warning(f"令牌刷新失败 - 无效的刷新令牌: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"令牌刷新时发生意外错误: {str(e)}", exc_info=True)
            raise

class IOSDevice(models.Model):
    """iOS设备模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    device_id = models.CharField(max_length=100, unique=True)
    push_token = models.CharField(max_length=100, null=True, blank=True)
    last_login = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_refresh = models.DateTimeField(null=True, blank=True)  # 最后一次刷新令牌时间
    last_active = models.DateTimeField(null=True, blank=True)   # 最后一次活动时间
    device_model = models.CharField(max_length=50, null=True, blank=True)  # 设备型号
    device_os_version = models.CharField(max_length=50, null=True, blank=True)  # 系统版本
    app_version = models.CharField(max_length=20, null=True, blank=True)  # 应用版本
    is_blacklisted = models.BooleanField(default=False)  # 设备是否被列入黑名单
    blacklisted_at = models.DateTimeField(null=True, blank=True)  # 加入黑名单时间

    # 使用自定义管理器
    objects = IOSDeviceManager()

    class Meta:
        db_table = 'iosapp_device'
        verbose_name = 'iOS设备'
        verbose_name_plural = 'iOS设备'
        
    def __str__(self):
        return f"{self.user.username} - {self.device_id[:8]}..."
        
    def update_active(self):
        """更新设备活动时间"""
        self.last_active = timezone.now()
        self.save(update_fields=['last_active'])
        
    def update_refresh(self):
        """更新设备刷新令牌时间"""
        self.last_refresh = timezone.now()
        self.last_active = timezone.now()
        self.save(update_fields=['last_refresh', 'last_active'])
        
    def update_device_info(self, device_data):
        """
        更新设备信息
        
        Args:
            device_data: 包含设备信息的字典
        """
        updated_fields = []
        
        if 'device_model' in device_data and device_data['device_model']:
            self.device_model = device_data['device_model']
            updated_fields.append('device_model')
            
        if 'device_os_version' in device_data and device_data['device_os_version']:
            self.device_os_version = device_data['device_os_version']
            updated_fields.append('device_os_version')
            
        if 'app_version' in device_data and device_data['app_version']:
            self.app_version = device_data['app_version']
            updated_fields.append('app_version')
            
        if updated_fields:
            self.save(update_fields=updated_fields)
        
    def blacklist(self):
        """将设备列入黑名单"""
        self.is_blacklisted = True
        self.blacklisted_at = timezone.now()
        self.save(update_fields=['is_blacklisted', 'blacklisted_at'])
        
    @classmethod
    def generate_tokens_for_user(cls, user, device_id=None):
        """
        为用户生成JWT令牌
        
        Args:
            user: 用户对象
            device_id: 设备ID
            
        Returns:
            dict: 包含令牌和过期时间的字典
        """
        refresh = RefreshToken.for_user(user)
        
        # 设置令牌自定义载荷
        if device_id:
            refresh['device_id'] = device_id
        refresh['user_id'] = user.id
        refresh['username'] = user.username
        
        access_token = refresh.access_token
        
        return {
            'access': str(access_token),
            'refresh': str(refresh),
            'expires_in': int(TOKEN_LIFETIME.total_seconds()),
            'refresh_expires_in': int(REFRESH_TOKEN_LIFETIME.total_seconds())
        }
    
    @classmethod
    def verify_device_id_in_token(cls, token, request_device_id):
        """
        验证令牌中的设备ID是否与请求中的设备ID匹配
        
        Args:
            token: RefreshToken对象
            request_device_id: 请求中的设备ID
            
        Returns:
            bool: 设备ID是否匹配
        """
        token_device_id = token.get('device_id')
        
        # 如果令牌或请求中没有设备ID，则认为匹配
        if not token_device_id or not request_device_id:
            return True
            
        return token_device_id == request_device_id
        
    @classmethod
    def verify_token(cls, user, device_id=None):
        """
        验证用户令牌的有效性并更新设备活动状态
        
        Args:
            user: 用户对象
            device_id: 设备ID
            
        Returns:
            dict: 包含验证结果的字典
        """
        try:
            last_verified = timezone.now()
            last_verified_str = last_verified.strftime('%Y-%m-%d %H:%M:%S')
            
            # 更新设备活动状态
            if device_id:
                try:
                    device = cls.objects.filter(
                        device_id=device_id,
                        user=user
                    ).first()
                    
                    if device:
                        device.update_active()
                except Exception as e:
                    logger.error(f"更新设备活动状态失败: {str(e)}", exc_info=True)
            
            # 返回验证成功响应
            return {
                'valid': True,
                'user_id': user.id,
                'username': user.username,
                'last_verified': last_verified_str
            }
        except Exception as e:
            logger.error(f"验证令牌时发生错误: {str(e)}", exc_info=True)
            raise
    
    @classmethod
    def logout_device(cls, user, refresh_token, device_id=None, blacklist_device=False):
        """
        登出设备并将令牌加入黑名单
        
        Args:
            user: 用户对象
            refresh_token: 刷新令牌
            device_id: 设备ID
            blacklist_device: 是否将设备加入黑名单
            
        Returns:
            bool: 是否成功
        """
        try:
            # 将刷新令牌加入黑名单
            token = RefreshToken(refresh_token)
            token.blacklist()
            
            # 处理设备记录
            if device_id:
                try:
                    device = cls.objects.filter(
                        device_id=device_id,
                        user=user
                    ).first()
                    
                    if device:
                        # 如果请求明确指定了要将设备加入黑名单
                        if blacklist_device:
                            device.blacklist()
                            logger.info(f"设备已被加入黑名单 - 设备ID: {device_id}")
                        else:
                            # 更新最后活动时间，记录正常登出
                            device.update_active()
                except Exception as device_error:
                    logger.error(f"处理设备记录失败: {str(device_error)}", exc_info=True)
            
            logger.info(f"用户 {user.username} 成功登出")
            return True
        except Exception as e:
            logger.error(f"登出处理错误: {str(e)}", exc_info=True)
            raise