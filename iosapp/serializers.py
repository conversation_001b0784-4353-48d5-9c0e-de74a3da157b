from rest_framework import serializers
from my.models import <PERSON>rewingRecord, Equipment, CoffeeBean, FlavorTag, BlendComponent, BeanOccurrence, RecipeThread, RecipeTag
from .models import IOSDevice
from django.db.models import Avg
import logging
from datetime import datetime
from decimal import Decimal
from django.utils.dateparse import parse_date

logger = logging.getLogger(__name__)

class IOSDeviceSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source='user.username', read_only=True)

    class Meta:
        model = IOSDevice
        fields = [
            'id', 'username', 'device_id', 'device_model', 'device_os_version',
            'app_version', 'last_login', 'last_refresh', 'last_active',
            'is_blacklisted', 'blacklisted_at', 'created_at'
        ]
        read_only_fields = ['id', 'username', 'created_at', 'last_login']

class IOSEquipmentSerializer(serializers.ModelSerializer):
    id = serializers.Integer<PERSON>ield()
    name = serializers.CharField()
    type = serializers.CharField()
    brand = serializers.CharField(allow_null=True)
    model = serializers.CharField(allow_null=True)
    description = serializers.CharField(allow_null=True)
    purchase_date = serializers.FloatField(source='purchase_date_timestamp', allow_null=True)
    purchase_price = serializers.FloatField(allow_null=True)
    notes = serializers.CharField(allow_null=True)
    created_at = serializers.FloatField(source='created_at_timestamp')
    updated_at = serializers.SerializerMethodField()
    is_deleted = serializers.BooleanField(default=False)
    is_archived = serializers.BooleanField(default=False)
    is_active = serializers.SerializerMethodField()
    is_favorite = serializers.BooleanField(default=False)
    brew_method = serializers.CharField(allow_null=True)
    brew_method_display = serializers.CharField(source='get_brew_method_display', allow_null=True)
    # 添加磨豆机用途字段
    grinder_purpose = serializers.CharField(allow_null=True)
    grinder_purpose_display = serializers.CharField(source='get_grinder_purpose_display', allow_null=True)
    # 添加磨豆机研磨度预设字段
    grind_size_preset = serializers.CharField(allow_null=True)
    # 添加折旧率和回本进度字段
    depreciation_rate = serializers.FloatField(source='_depreciation_rate', read_only=True, default=100.0)
    break_even_progress = serializers.FloatField(read_only=True)
    # 添加小工具组合组件字段
    gadget_components = serializers.SerializerMethodField()
    # 添加使用数据相关字段
    usage_count = serializers.IntegerField(source='get_usage_count', read_only=True)
    last_used = serializers.SerializerMethodField()

    class Meta:
        model = Equipment
        fields = [
            'id', 'name', 'type', 'brand', 'model',
            'description', 'purchase_date', 'purchase_price',
            'notes', 'created_at', 'updated_at', 'is_deleted',
            'is_archived', 'is_active', 'is_favorite', 'brew_method', 'brew_method_display',
            'grinder_purpose', 'grinder_purpose_display', 'grind_size_preset',
            'depreciation_rate', 'break_even_progress', 'gadget_components',
            'usage_count', 'last_used'
        ]

    def get_updated_at(self, obj):
        return obj.created_at.timestamp()

    def get_is_active(self, obj):
        return not (obj.is_deleted or obj.is_archived)

    def get_gadget_components(self, obj):
        """获取小工具组合中的组件信息列表"""
        # 只有当设备类型为小工具组合时才返回组件列表
        if obj.type == 'GADGET_KIT':
            components = []
            for gadget in obj.gadget_components.all():
                components.append({
                    'id': gadget.id,
                    'name': gadget.name,
                    'brand': gadget.brand
                })
            return components
        return None

    def get_last_used(self, obj):
        """获取设备最后使用时间"""
        try:
            last_used = obj.get_last_used_datetime()
            if last_used:
                return last_used.timestamp()
            return None
        except Exception as e:
            logger.error(f"获取设备最后使用时间时出错: {str(e)}")
            return None

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # 显式处理break_even_progress，确保是浮点数
        try:
            progress = instance.break_even_progress()
            if isinstance(progress, Decimal):
                data['break_even_progress'] = float(progress)
            else:
                data['break_even_progress'] = float(progress)
        except Exception:
            data['break_even_progress'] = 0.0

        # 确保所有字段都有默认值
        defaults = {
            'id': instance.id,
            'name': instance.name,
            'type': instance.type,
            'brand': None,
            'model': None,
            'description': None,
            'purchase_date': None,
            'purchase_price': None,
            'notes': None,
            'created_at': instance.created_at.timestamp(),
            'updated_at': instance.created_at.timestamp(),
            'is_deleted': False,
            'is_archived': False,
            'is_active': True,
            'is_favorite': False,
            'brew_method': None,
            'brew_method_display': None,
            'grinder_purpose': None,
            'grinder_purpose_display': None,
            'grind_size_preset': None,
            'depreciation_rate': 100.0,  # 默认100%表示全新
            'break_even_progress': 0.0,   # 默认0%表示未回本
            'gadget_components': None,    # 默认为空
            'usage_count': 0,             # 默认使用次数为0
            'last_used': None             # 默认最后使用时间为空
        }

        # 使用默认值填充缺失的字段
        for key, value in defaults.items():
            if key not in data or data[key] is None:
                data[key] = value

        return data

class IOSBlendComponentSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    roast_level_display = serializers.CharField(source='get_roast_level_display')
    blend_ratio = serializers.FloatField(allow_null=True)

    class Meta:
        model = BlendComponent
        fields = [
            'id', 'coffee_bean_id', 'origin', 'region', 'finca', 'variety',
            'process', 'roast_level', 'roast_level_display', 'blend_ratio',
            'order', 'altitude_type', 'altitude_single', 'altitude_min', 'altitude_max'
        ]

class IOSBeanOccurrenceSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    created_at = serializers.FloatField(source='created_at_timestamp')
    # 修改roast_date字段，使其支持更多格式
    roast_date = serializers.SerializerMethodField()

    class Meta:
        model = BeanOccurrence
        fields = [
            'id', 'coffee_bean_id', 'bag_weight', 'bag_remain', 'purchase_price',
            'roast_date', 'created_at', 'rest_period_min', 'rest_period_max'
        ]

    def get_roast_date(self, obj):
        """获取烘焙日期，返回时间戳格式或None"""
        if obj.roast_date:
            from datetime import datetime
            dt = datetime.combine(obj.roast_date, datetime.min.time())
            from django.utils import timezone
            dt = timezone.make_aware(dt)  # 确保是aware datetime
            return dt.timestamp()
        return None

class IOSCoffeeBeanSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    roaster = serializers.CharField()
    origin = serializers.CharField(allow_null=True, allow_blank=True, default='')
    process = serializers.CharField(allow_null=True, allow_blank=True, default='')
    type = serializers.CharField()
    type_display = serializers.CharField(source='get_type_display')
    roast_level = serializers.IntegerField()
    roast_level_display = serializers.SerializerMethodField()
    roast_date = serializers.FloatField(source='roast_date_timestamp', allow_null=True)
    purchase_date = serializers.FloatField(source='purchase_date_timestamp', allow_null=True)
    price = serializers.FloatField(source='purchase_price', allow_null=True, default=None)
    weight = serializers.FloatField(source='bag_weight', allow_null=True, default=None)
    description = serializers.CharField(allow_null=True, allow_blank=True, default='')
    notes = serializers.CharField(allow_null=True, allow_blank=True, default='')
    rating = serializers.IntegerField(allow_null=True, default=0)
    created_at = serializers.FloatField(source='created_at_timestamp')
    updated_at = serializers.SerializerMethodField()
    is_deleted = serializers.BooleanField(default=False)
    is_archived = serializers.BooleanField(default=False)
    is_favorite = serializers.BooleanField(default=False)
    is_finished = serializers.SerializerMethodField()
    is_decaf = serializers.BooleanField(default=False)
    taste_notes = serializers.SerializerMethodField()
    # 添加海拔相关字段
    altitude_type = serializers.CharField(default='SINGLE')
    altitude_single = serializers.IntegerField(allow_null=True, default=None)
    altitude_min = serializers.IntegerField(allow_null=True, default=None)
    altitude_max = serializers.IntegerField(allow_null=True, default=None)
    # 添加其他缺失字段
    region = serializers.CharField(allow_null=True, allow_blank=True, default='')
    finca = serializers.CharField(allow_null=True, allow_blank=True, default='')
    variety = serializers.CharField(allow_null=True, allow_blank=True, default='')
    barcode = serializers.CharField(allow_null=True, allow_blank=True, default='')
    rest_period_min = serializers.IntegerField(allow_null=True, default=None)
    rest_period_max = serializers.IntegerField(allow_null=True, default=None)
    rest_period_progress = serializers.IntegerField(allow_null=True, default=None)
    stock_status = serializers.CharField(default='充足')
    deleted_at = serializers.FloatField(allow_null=True, default=None)
    bag_remain = serializers.FloatField(allow_null=True, default=None)
    blend_components = serializers.SerializerMethodField()
    occurrences = serializers.SerializerMethodField()
    avg_rating = serializers.SerializerMethodField()

    # 添加初始包装信息字段
    initial_bag_weight = serializers.FloatField(allow_null=True, default=None)
    initial_bag_remain = serializers.FloatField(allow_null=True, default=None)
    initial_purchase_price = serializers.FloatField(allow_null=True, default=None)
    initial_roast_date = serializers.SerializerMethodField()
    initial_created_at = serializers.SerializerMethodField()
    initial_rest_period_min = serializers.IntegerField(allow_null=True, default=None)
    initial_rest_period_max = serializers.IntegerField(allow_null=True, default=None)

    # 添加使用数据相关字段
    usage_count = serializers.SerializerMethodField()
    last_used = serializers.SerializerMethodField()
    days_since_last_use = serializers.SerializerMethodField()
    most_used_equipment = serializers.SerializerMethodField()
    remaining_uses = serializers.SerializerMethodField()
    occurrences_count = serializers.SerializerMethodField()
    avg_repurchase_interval = serializers.SerializerMethodField()
    dimensions_avg = serializers.SerializerMethodField()
    tasting_count = serializers.SerializerMethodField()
    unique_flavor_tags = serializers.SerializerMethodField()
    flavor_accuracy = serializers.SerializerMethodField()

    class Meta:
        model = CoffeeBean
        fields = [
            'id', 'name', 'roaster', 'origin', 'process',
            'type', 'type_display',
            'roast_level', 'roast_level_display', 'roast_date',
            'purchase_date', 'price', 'weight', 'description',
            'notes', 'rating', 'created_at', 'updated_at',
            'is_deleted', 'is_archived', 'is_favorite', 'is_finished',
            'is_decaf', 'taste_notes',
            # 新增字段
            'altitude_type', 'altitude_single', 'altitude_min', 'altitude_max',
            'region', 'finca', 'variety', 'barcode',
            'rest_period_min', 'rest_period_max', 'rest_period_progress',
            'stock_status', 'deleted_at', 'bag_remain',
            'blend_components', 'occurrences', 'avg_rating',
            # 初始包装信息字段
            'initial_bag_weight', 'initial_bag_remain', 'initial_purchase_price',
            'initial_roast_date', 'initial_created_at',
            'initial_rest_period_min', 'initial_rest_period_max',
            # 使用数据相关字段
            'usage_count', 'last_used', 'days_since_last_use',
            'most_used_equipment', 'remaining_uses', 'occurrences_count',
            'avg_repurchase_interval', 'dimensions_avg', 'tasting_count',
            'unique_flavor_tags', 'flavor_accuracy'
        ]

    def get_roast_date(self, obj):
        """获取烘焙日期，返回时间戳格式或None"""
        if obj.roast_date:
            from datetime import datetime
            dt = datetime.combine(obj.roast_date, datetime.min.time())
            from django.utils import timezone
            dt = timezone.make_aware(dt)  # 确保是aware datetime
            return dt.timestamp()
        return None

    def update(self, instance, validated_data):
        """
        自定义更新方法处理时间戳字段转换为日期
        """
        # 由于现在roast_date是SerializerMethodField，不会直接出现在validated_data中
        # 而是由roast_date_timestamp处理，这段代码不需要修改

        # 从时间戳更新roast_date字段
        if 'roast_date_timestamp' in validated_data:
            timestamp = validated_data.pop('roast_date_timestamp')
            if timestamp is not None:
                try:
                    # 将时间戳转换为日期
                    instance.roast_date = datetime.fromtimestamp(float(timestamp)).date()
                except Exception as e:
                    logger.warning(f"时间戳转换失败: {timestamp}, 错误: {str(e)}")
                    # 失败时不修改字段
            else:
                instance.roast_date = None

        # 从时间戳更新created_at字段
        if 'created_at_timestamp' in validated_data:
            timestamp = validated_data.pop('created_at_timestamp')
            if timestamp is not None:
                try:
                    # 将时间戳转换为日期时间
                    instance.created_at = datetime.fromtimestamp(float(timestamp))
                except Exception as e:
                    logger.warning(f"时间戳转换失败: {timestamp}, 错误: {str(e)}")
                    # 失败时不修改字段

        # 处理其他字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance

    def get_updated_at(self, obj):
        return obj.created_at.timestamp()

    def get_taste_notes(self, obj):
        try:
            if hasattr(obj, 'flavor_tags'):
                return [tag.name for tag in obj.flavor_tags.all()]
            return []
        except Exception as e:
            logger.error(f"获取风味标签时出错: {str(e)}")
            return []

    def get_roast_level_display(self, obj):
        return obj.get_roast_level_display() or ''

    def get_is_finished(self, obj):
        return obj.bag_remain is None or obj.bag_remain <= 0

    def get_avg_rating(self, obj):
        """获取咖啡豆的平均评分，使用与view_bean函数完全相同的逻辑"""
        from django.db.models import Avg
        # 与view_bean函数完全相同的计算逻辑
        records = BrewingRecord.objects.filter(coffee_bean=obj)
        if records.exists():
            avg_rating = records.aggregate(Avg('rating_level'))['rating_level__avg']
            return round(avg_rating, 1) if avg_rating else None
        return None

    def get_blend_components(self, obj):
        """获取拼配咖啡的组成部分"""
        if obj.type != 'BLEND':
            return None
        components = obj.get_blend_components()
        if not components:
            return None
        return IOSBlendComponentSerializer(components, many=True).data

    def get_occurrences(self, obj):
        """获取咖啡豆的回购记录"""
        occurrences = BeanOccurrence.objects.filter(coffee_bean=obj)
        if not occurrences.exists():
            return None
        return IOSBeanOccurrenceSerializer(occurrences, many=True).data

    def get_usage_count(self, obj):
        """获取使用次数"""
        try:
            usage_data = obj.get_usage_data()
            return usage_data.get('usage_count', 0)
        except Exception as e:
            logger.error(f"获取使用次数时出错: {str(e)}")
            return 0

    def get_last_used(self, obj):
        """获取最后使用时间"""
        try:
            usage_data = obj.get_usage_data()
            last_used = usage_data.get('last_used')
            if last_used:
                return last_used.timestamp()
            return None
        except Exception as e:
            logger.error(f"获取最后使用时间时出错: {str(e)}")
            return None

    def get_days_since_last_use(self, obj):
        """获取距离上次使用的天数"""
        try:
            usage_data = obj.get_usage_data()
            return usage_data.get('days_since_last_use')
        except Exception as e:
            logger.error(f"获取距离上次使用天数时出错: {str(e)}")
            return None

    def get_most_used_equipment(self, obj):
        """获取最常用的冲煮器具"""
        try:
            equipment = obj.get_most_used_equipment()
            if equipment:
                # 检查equipment是字典还是模型实例
                if isinstance(equipment, dict):
                    # 从模型中直接返回的是只包含brew_method的字典
                    brew_method = equipment.get('brewing_equipment__brew_method')
                    if brew_method:
                        # 获取对应的显示值
                        brew_method_display = dict(Equipment.BREW_METHODS).get(brew_method)
                        return {
                            'id': None,
                            'name': None,
                            'type': None,
                            'brand': None,
                            'model': None,
                            'brew_method': brew_method,
                            'brew_method_display': brew_method_display,
                            'grinder_purpose': None,
                            'grinder_purpose_display': None,
                            'grind_size_preset': None,
                            'depreciation_rate': 100.0,  # 默认值
                            'break_even_progress': 0.0   # 默认值
                        }
                    return None
                else:
                    # 模型实例，使用原有逻辑
                    return {
                        'id': equipment.id,
                        'name': equipment.name,
                        'type': equipment.type,
                        'brand': equipment.brand,
                        'model': getattr(equipment, 'model', None),
                        'brew_method': getattr(equipment, 'brew_method', None),
                        'brew_method_display': equipment.get_brew_method_display() if hasattr(equipment, 'get_brew_method_display') and getattr(equipment, 'brew_method', None) else None,
                        'grinder_purpose': getattr(equipment, 'grinder_purpose', None),
                        'grinder_purpose_display': equipment.get_grinder_purpose_display() if hasattr(equipment, 'get_grinder_purpose_display') and getattr(equipment, 'grinder_purpose', None) else None,
                        'grind_size_preset': getattr(equipment, 'grind_size_preset', None),
                        'depreciation_rate': float(equipment._depreciation_rate),
                        'break_even_progress': float(equipment.break_even_progress())
                    }
            return None
        except Exception as e:
            logger.error(f"获取最常用冲煮器具时出错: {str(e)}")
            return None

    def get_remaining_uses(self, obj):
        """计算剩余可用次数"""
        try:
            return obj.calculate_remaining_uses()
        except Exception as e:
            logger.error(f"计算剩余可用次数时出错: {str(e)}")
            return None

    def get_occurrences_count(self, obj):
        """获取回购次数"""
        try:
            return obj.occurrences.count()
        except Exception as e:
            logger.error(f"获取回购次数时出错: {str(e)}")
            return 0

    def get_avg_repurchase_interval(self, obj):
        """计算平均回购周期"""
        try:
            avg_interval, _ = obj.calculate_repurchase_interval()
            return avg_interval
        except Exception as e:
            logger.error(f"计算平均回购周期时出错: {str(e)}")
            return None

    def get_dimensions_avg(self, obj):
        """获取品鉴维度平均值"""
        try:
            dimensions_data = obj.get_tasting_summary()
            # 确保dimensions_data是一个三元组且第一个元素不为None
            if dimensions_data and isinstance(dimensions_data, tuple) and len(dimensions_data) >= 3:
                dimensions_avg = dimensions_data[0]
                if dimensions_avg and isinstance(dimensions_avg, dict):
                    # 确保所有数值都是可序列化的
                    serialized_dimensions = {}
                    for key, value in dimensions_avg.items():
                        serialized_dimensions[key] = float(value) if value is not None else None
                    return serialized_dimensions
            return {}
        except Exception as e:
            logger.error(f"获取品鉴维度平均值时出错: {str(e)}")
            return {}

    def get_tasting_count(self, obj):
        """获取品鉴次数"""
        try:
            _, tasting_count, _ = obj.get_tasting_summary()
            return tasting_count
        except Exception as e:
            logger.error(f"获取品鉴次数时出错: {str(e)}")
            return 0

    def get_unique_flavor_tags(self, obj):
        """获取唯一风味标签"""
        try:
            _, _, unique_flavor_tags = obj.get_tasting_summary()
            if unique_flavor_tags:
                # 确保只返回简单的id和name字典
                serializable_tags = []
                for tag in unique_flavor_tags:
                    serializable_tags.append({
                        'id': tag.id,
                        'name': str(tag.name)  # 确保name是字符串
                    })
                return serializable_tags
            return []
        except Exception as e:
            logger.error(f"获取唯一风味标签时出错: {str(e)}")
            return []

    def get_flavor_accuracy(self, obj):
        """获取风味准确度百分比"""
        try:
            accuracy, _ = obj.calculate_flavor_accuracy()
            return accuracy  # 只返回准确度百分比，不返回实际品尝风味列表
        except Exception as e:
            logger.error(f"获取风味准确度时出错: {str(e)}")
            return None

    def get_coffee_bean(self, obj):
        try:
            if obj.coffee_bean:
                return {
                    'id': obj.coffee_bean.id,
                    'name': obj.coffee_bean.name,
                    'roaster': obj.coffee_bean.roaster,
                    'origin': obj.coffee_bean.origin or '',
                    'process': obj.coffee_bean.process or '',
                    'type': obj.coffee_bean.type,
                    'type_display': obj.coffee_bean.get_type_display(),
                    'roast_level': obj.coffee_bean.roast_level,
                    'roast_level_display': obj.coffee_bean.get_roast_level_display(),
                    'description': '',
                    'price': float(obj.coffee_bean.purchase_price) if obj.coffee_bean.purchase_price else None,
                    'weight': float(obj.coffee_bean.bag_weight) if obj.coffee_bean.bag_weight else None,
                    'purchase_date': obj.coffee_bean.purchase_date_timestamp,
                    'roast_date': obj.coffee_bean.roast_date_timestamp,
                    'is_finished': obj.coffee_bean.bag_remain is None or obj.coffee_bean.bag_remain <= 0,
                    'created_at': obj.coffee_bean.created_at.timestamp() if hasattr(obj.coffee_bean.created_at, 'timestamp') else None,
                    'is_favorite': obj.coffee_bean.is_favorite if hasattr(obj.coffee_bean, 'is_favorite') else False,
                    'is_decaf': obj.coffee_bean.is_decaf if hasattr(obj.coffee_bean, 'is_decaf') else False,
                    'is_archived': obj.coffee_bean.is_archived if hasattr(obj.coffee_bean, 'is_archived') else False,
                    'is_deleted': obj.coffee_bean.is_deleted if hasattr(obj.coffee_bean, 'is_deleted') else False,
                    # 添加海拔相关字段
                    'altitude_type': getattr(obj.coffee_bean, 'altitude_type', 'SINGLE'),
                    'altitude_single': getattr(obj.coffee_bean, 'altitude_single', None),
                    'altitude_min': getattr(obj.coffee_bean, 'altitude_min', None),
                    'altitude_max': getattr(obj.coffee_bean, 'altitude_max', None),
                    # 添加其他缺失字段
                    'region': getattr(obj.coffee_bean, 'region', '') or '',
                    'finca': getattr(obj.coffee_bean, 'finca', '') or '',
                    'variety': getattr(obj.coffee_bean, 'variety', '') or '',
                    'barcode': getattr(obj.coffee_bean, 'barcode', '') or '',
                    'rest_period_min': getattr(obj.coffee_bean, 'rest_period_min', None),
                    'rest_period_max': getattr(obj.coffee_bean, 'rest_period_max', None),
                    'rest_period_progress': getattr(obj.coffee_bean, 'rest_period_progress', None),
                    'stock_status': getattr(obj.coffee_bean, 'stock_status', '充足'),
                    'deleted_at': obj.coffee_bean.deleted_at.timestamp() if hasattr(obj.coffee_bean, 'deleted_at') and obj.coffee_bean.deleted_at else None,
                    'bag_remain': float(obj.coffee_bean.bag_remain) if hasattr(obj.coffee_bean, 'bag_remain') and obj.coffee_bean.bag_remain is not None else None,
                    'notes': obj.coffee_bean.notes or '',
                    'taste_notes': [],
                    # 添加初始包装信息字段
                    'initial_bag_weight': float(obj.coffee_bean.initial_bag_weight) if hasattr(obj.coffee_bean, 'initial_bag_weight') and obj.coffee_bean.initial_bag_weight is not None else None,
                    'initial_bag_remain': float(obj.coffee_bean.initial_bag_remain) if hasattr(obj.coffee_bean, 'initial_bag_remain') and obj.coffee_bean.initial_bag_remain is not None else None,
                    'initial_purchase_price': float(obj.coffee_bean.initial_purchase_price) if hasattr(obj.coffee_bean, 'initial_purchase_price') and obj.coffee_bean.initial_purchase_price is not None else None,
                    'initial_roast_date': self._convert_date_to_timestamp(obj.coffee_bean.initial_roast_date) if hasattr(obj.coffee_bean, 'initial_roast_date') and obj.coffee_bean.initial_roast_date else None,
                    'initial_created_at': obj.coffee_bean.initial_created_at.timestamp() if hasattr(obj.coffee_bean, 'initial_created_at') and obj.coffee_bean.initial_created_at else None,
                    'initial_rest_period_min': obj.coffee_bean.initial_rest_period_min if hasattr(obj.coffee_bean, 'initial_rest_period_min') else None,
                    'initial_rest_period_max': obj.coffee_bean.initial_rest_period_max if hasattr(obj.coffee_bean, 'initial_rest_period_max') else None
                }
            # 如果coffee_bean为空，返回一个包含必要id字段的空对象
            return {
                'id': 0,  # 提供一个默认ID
                'name': '',
                'roaster': '',
                'origin': '',
                'process': '',
                'type': 'SINGLE',
                'type_display': '单品',
                'roast_level': 3,
                'roast_level_display': '中浅烘',
                'description': '',
                'price': None,
                'weight': None,
                'purchase_date': None,
                'roast_date': None,
                'is_finished': False,
                'created_at': None,
                'is_favorite': False,
                'is_decaf': False,
                'is_archived': False,
                'is_deleted': False,
                'altitude_type': 'SINGLE',
                'altitude_single': None,
                'altitude_min': None,
                'altitude_max': None,
                'region': '',
                'finca': '',
                'variety': '',
                'barcode': '',
                'rest_period_min': None,
                'rest_period_max': None,
                'rest_period_progress': None,
                'stock_status': '充足',
                'deleted_at': None,
                'bag_remain': None,
                'notes': '',
                'taste_notes': [],
                'initial_bag_weight': None,
                'initial_bag_remain': None,
                'initial_purchase_price': None,
                'initial_roast_date': None,
                'initial_created_at': None,
                'initial_rest_period_min': None,
                'initial_rest_period_max': None
            }
        except Exception as e:
            logger.error(f"获取咖啡豆时出错: {str(e)}")
            # 出错时也返回包含id字段的空对象
            return {
                'id': 0,
                'name': '',
                'roaster': '',
                'origin': '',
                'process': '',
                'type': 'SINGLE',
                'type_display': '单品',
                'roast_level': 3,
                'roast_level_display': '中浅烘',
                'description': '',
                'price': None,
                'weight': None,
                'purchase_date': None,
                'roast_date': None,
                'is_finished': False,
                'created_at': None,
                'is_favorite': False,
                'is_decaf': False,
                'is_archived': False,
                'is_deleted': False,
                'altitude_type': 'SINGLE',
                'altitude_single': None,
                'altitude_min': None,
                'altitude_max': None,
                'region': '',
                'finca': '',
                'variety': '',
                'barcode': '',
                'rest_period_min': None,
                'rest_period_max': None,
                'rest_period_progress': None,
                'stock_status': '充足',
                'deleted_at': None,
                'bag_remain': None,
                'notes': '',
                'taste_notes': [],
                'initial_bag_weight': None,
                'initial_bag_remain': None,
                'initial_purchase_price': None,
                'initial_roast_date': None,
                'initial_created_at': None,
                'initial_rest_period_min': None,
                'initial_rest_period_max': None
            }

    def _convert_date_to_timestamp(self, date_obj):
        """将日期对象转换为时间戳"""
        if not date_obj:
            return None
        from datetime import datetime
        from django.utils import timezone
        dt = datetime.combine(date_obj, datetime.min.time())
        dt = timezone.make_aware(dt)  # 确保是aware datetime
        return dt.timestamp()

    def to_representation(self, instance):
        # 定义默认值，放在try外面确保始终可以访问
        defaults = {
            'id': instance.id,
            'name': instance.name,
            'roaster': instance.roaster,
            'origin': '',
            'process': '',
            'type': instance.type,
            'type_display': instance.get_type_display(),
            'roast_level': instance.roast_level if instance.roast_level else 4,  # 默认为中烘焙(4)
            'roast_date': None,
            'purchase_date': None,
            'price': None,
            'weight': None,
            'description': '',
            'notes': '',
            'rating': 0,
            'created_at': instance.created_at.strftime('%Y-%m-%d'),
            'updated_at': instance.created_at.strftime('%Y-%m-%d'),
            'is_deleted': False,
            'is_archived': False,
            'is_favorite': False,
            'is_finished': False,
            'is_decaf': instance.is_decaf if hasattr(instance, 'is_decaf') else False,
            'taste_notes': [],
            'altitude_type': 'SINGLE',
            'altitude_single': None,
            'altitude_min': None,
            'altitude_max': None,
            'region': '',
            'finca': '',
            'variety': '',
            'barcode': '',
            'rest_period_min': None,
            'rest_period_max': None,
            'rest_period_progress': None,
            'stock_status': '充足',
            'deleted_at': None,
            'bag_remain': None,
            'blend_components': None,
            'occurrences': None,
            'avg_rating': None,
            # 添加初始包装信息字段的默认值
            'initial_bag_weight': None,
            'initial_bag_remain': None,
            'initial_purchase_price': None,
            'initial_roast_date': None,
            'initial_created_at': None,
            'initial_rest_period_min': None,
            'initial_rest_period_max': None,
            # 添加使用数据相关字段的默认值
            'usage_count': 0,
            'last_used': None,
            'days_since_last_use': None,
            'most_used_equipment': None,
            'remaining_uses': None,
            'occurrences_count': 0,
            'avg_repurchase_interval': None,
            'dimensions_avg': {},
            'tasting_count': 0,
            'unique_flavor_tags': [],
            'flavor_accuracy': None,
            'gadget_components': None
        }

        try:
            data = super().to_representation(instance)
            if not data:
                data = {}

            # 使用默认值填充缺失的字段
            for key, value in defaults.items():
                if key not in data or data[key] is None:
                    data[key] = value

            # 确保日期格式统一
            if data.get('roast_date'):
                data['roast_date'] = data['roast_date'].strftime('%Y-%m-%d') if isinstance(data['roast_date'], datetime) else data['roast_date']
            if data.get('purchase_date'):
                data['purchase_date'] = data['purchase_date'].strftime('%Y-%m-%d') if isinstance(data['purchase_date'], datetime) else data['purchase_date']

            return data
        except Exception as e:
            logger.error(f"序列化咖啡豆时发生错误: {str(e)}", exc_info=True)
            return defaults

    def get_initial_roast_date(self, obj):
        """获取初始烘焙日期，返回时间戳格式或None"""
        if obj.initial_roast_date:
            return self._convert_date_to_timestamp(obj.initial_roast_date)
        return None

    def get_initial_created_at(self, obj):
        """获取初始创建日期，返回时间戳格式或None"""
        if obj.initial_created_at:
            return obj.initial_created_at.timestamp()
        return None

class IOSFlavorTagSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()

    class Meta:
        model = FlavorTag
        fields = ['id', 'name']

class IOSBrewingRecordSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    recipe_name = serializers.CharField(allow_null=True, allow_blank=True, default='')
    brewing_equipment = serializers.SerializerMethodField()
    grinding_equipment = serializers.SerializerMethodField()
    coffee_bean = serializers.SerializerMethodField()
    dose_weight = serializers.FloatField(source='dose_weight', allow_null=True, default=0)
    yield_weight = serializers.FloatField(source='yield_weight', allow_null=True, default=0)
    water_temperature = serializers.FloatField(allow_null=True, default=0)
    brewing_time = serializers.SerializerMethodField()
    grind_size = serializers.CharField(allow_null=True, allow_blank=True, default='')
    rating_level = serializers.IntegerField(source='rating_level', default=0)
    rating_display = serializers.CharField(source='get_rating_level_display', read_only=True)
    notes = serializers.CharField(allow_null=True, allow_blank=True, default='')
    created_at = serializers.FloatField(source='created_at_timestamp')
    is_decaf = serializers.SerializerMethodField()

    # 品鉴维度
    aroma = serializers.IntegerField(default=0)
    acidity = serializers.IntegerField(default=0)
    sweetness = serializers.IntegerField(default=0)
    body = serializers.IntegerField(default=0)
    aftertaste = serializers.IntegerField(default=0)

    # 环境信息
    water_quality = serializers.CharField(allow_null=True, allow_blank=True, default='')
    room_temperature = serializers.FloatField(allow_null=True)
    room_humidity = serializers.IntegerField(allow_null=True)

    # 步骤和风味标签
    steps = serializers.JSONField(default=list)
    flavor_tags = serializers.SerializerMethodField()
    gadgets = serializers.SerializerMethodField()
    gadget_kit = serializers.SerializerMethodField()
    tag_overlap = serializers.SerializerMethodField()

    def _convert_date_to_timestamp(self, date_obj):
        """将日期对象转换为时间戳"""
        if not date_obj:
            return None
        from datetime import datetime
        from django.utils import timezone
        dt = datetime.combine(date_obj, datetime.min.time())
        dt = timezone.make_aware(dt)  # 确保是aware datetime
        return dt.timestamp()

    class Meta:
        model = BrewingRecord
        fields = [
            'id', 'recipe_name', 'brewing_equipment', 'grinding_equipment', 'coffee_bean',
            'dose_weight', 'yield_weight', 'water_temperature', 'brewing_time',
            'grind_size', 'rating_level', 'rating_display', 'notes', 'created_at', 'is_decaf',
            'aroma', 'acidity', 'sweetness', 'body', 'aftertaste',
            'water_quality', 'room_temperature', 'room_humidity',
            'steps', 'flavor_tags', 'gadgets', 'gadget_kit', 'tag_overlap'
        ]

    def get_brewing_time(self, obj):
        try:
            if obj.brewing_time:
                return int(obj.brewing_time.total_seconds())
            return 0
        except Exception as e:
            logger.error(f"获取萃取时间时出错: {str(e)}")
            return 0

    def get_brewing_equipment(self, obj):
        try:
            if obj.brewing_equipment:
                return {
                    'id': obj.brewing_equipment.id,
                    'name': obj.brewing_equipment.name,
                    'type': obj.brewing_equipment.type,
                    'brand': obj.brewing_equipment.brand,
                    'model': None,
                    'description': None,
                    'purchase_date': obj.brewing_equipment.purchase_date_timestamp,
                    'purchase_price': float(obj.brewing_equipment.purchase_price) if obj.brewing_equipment.purchase_price else None,
                    'notes': obj.brewing_equipment.notes,
                    'created_at': obj.brewing_equipment.created_at.timestamp(),
                    'updated_at': obj.brewing_equipment.created_at.timestamp(),
                    'is_deleted': obj.brewing_equipment.is_deleted,
                    'is_archived': obj.brewing_equipment.is_archived,
                    'is_active': not (obj.brewing_equipment.is_deleted or obj.brewing_equipment.is_archived),
                    'brew_method': obj.brewing_equipment.brew_method,
                    'brew_method_display': obj.brewing_equipment.get_brew_method_display() if obj.brewing_equipment.brew_method else None,
                    'grinder_purpose': None,  # 冲煮设备不使用磨豆机字段
                    'grinder_purpose_display': None,  # 冲煮设备不使用磨豆机字段
                    'grind_size_preset': None,  # 冲煮设备不使用研磨度预设字段
                    'depreciation_rate': float(obj.brewing_equipment._depreciation_rate),
                    'break_even_progress': float(obj.brewing_equipment.break_even_progress())
                }
            return None
        except Exception as e:
            logger.error(f"获取冲煮设备时出错: {str(e)}")
            return None

    def get_grinding_equipment(self, obj):
        try:
            if obj.grinding_equipment:
                return {
                    'id': obj.grinding_equipment.id,
                    'name': obj.grinding_equipment.name,
                    'type': obj.grinding_equipment.type,
                    'brand': obj.grinding_equipment.brand,
                    'model': None,
                    'description': None,
                    'purchase_date': obj.grinding_equipment.purchase_date_timestamp,
                    'purchase_price': float(obj.grinding_equipment.purchase_price) if obj.grinding_equipment.purchase_price else None,
                    'notes': obj.grinding_equipment.notes,
                    'created_at': obj.grinding_equipment.created_at.timestamp(),
                    'updated_at': obj.grinding_equipment.created_at.timestamp(),
                    'is_deleted': obj.grinding_equipment.is_deleted,
                    'is_archived': obj.grinding_equipment.is_archived,
                    'is_active': not (obj.grinding_equipment.is_deleted or obj.grinding_equipment.is_archived),
                    'brew_method': None,
                    'brew_method_display': None,
                    'grinder_purpose': obj.grinding_equipment.grinder_purpose,
                    'grinder_purpose_display': obj.grinding_equipment.get_grinder_purpose_display() if obj.grinding_equipment.grinder_purpose else None,
                    'grind_size_preset': obj.grinding_equipment.grind_size_preset,
                    'depreciation_rate': float(obj.grinding_equipment._depreciation_rate),
                    'break_even_progress': float(obj.grinding_equipment.break_even_progress())
                }
            return None
        except Exception as e:
            logger.error(f"获取磨豆机时出错: {str(e)}")
            return None

    def get_coffee_bean(self, obj):
        try:
            if obj.coffee_bean:
                return {
                    'id': obj.coffee_bean.id,
                    'name': obj.coffee_bean.name,
                    'roaster': obj.coffee_bean.roaster,
                    'origin': obj.coffee_bean.origin or '',
                    'process': obj.coffee_bean.process or '',
                    'type': obj.coffee_bean.type,
                    'type_display': obj.coffee_bean.get_type_display(),
                    'roast_level': obj.coffee_bean.roast_level,
                    'roast_level_display': obj.coffee_bean.get_roast_level_display(),
                    'description': '',
                    'price': float(obj.coffee_bean.purchase_price) if obj.coffee_bean.purchase_price else None,
                    'weight': float(obj.coffee_bean.bag_weight) if obj.coffee_bean.bag_weight else None,
                    'purchase_date': obj.coffee_bean.purchase_date_timestamp,
                    'roast_date': obj.coffee_bean.roast_date_timestamp,
                    'is_finished': obj.coffee_bean.bag_remain is None or obj.coffee_bean.bag_remain <= 0,
                    'created_at': obj.coffee_bean.created_at.timestamp() if hasattr(obj.coffee_bean.created_at, 'timestamp') else None,
                    'is_favorite': obj.coffee_bean.is_favorite if hasattr(obj.coffee_bean, 'is_favorite') else False,
                    'is_decaf': obj.coffee_bean.is_decaf if hasattr(obj.coffee_bean, 'is_decaf') else False,
                    'is_archived': obj.coffee_bean.is_archived if hasattr(obj.coffee_bean, 'is_archived') else False,
                    'is_deleted': obj.coffee_bean.is_deleted if hasattr(obj.coffee_bean, 'is_deleted') else False,
                    # 添加海拔相关字段
                    'altitude_type': getattr(obj.coffee_bean, 'altitude_type', 'SINGLE'),
                    'altitude_single': getattr(obj.coffee_bean, 'altitude_single', None),
                    'altitude_min': getattr(obj.coffee_bean, 'altitude_min', None),
                    'altitude_max': getattr(obj.coffee_bean, 'altitude_max', None),
                    # 添加其他缺失字段
                    'region': getattr(obj.coffee_bean, 'region', '') or '',
                    'finca': getattr(obj.coffee_bean, 'finca', '') or '',
                    'variety': getattr(obj.coffee_bean, 'variety', '') or '',
                    'barcode': getattr(obj.coffee_bean, 'barcode', '') or '',
                    'rest_period_min': getattr(obj.coffee_bean, 'rest_period_min', None),
                    'rest_period_max': getattr(obj.coffee_bean, 'rest_period_max', None),
                    'rest_period_progress': getattr(obj.coffee_bean, 'rest_period_progress', None),
                    'stock_status': getattr(obj.coffee_bean, 'stock_status', '充足'),
                    'deleted_at': obj.coffee_bean.deleted_at.timestamp() if hasattr(obj.coffee_bean, 'deleted_at') and obj.coffee_bean.deleted_at else None,
                    'bag_remain': float(obj.coffee_bean.bag_remain) if hasattr(obj.coffee_bean, 'bag_remain') and obj.coffee_bean.bag_remain is not None else None,
                    'notes': obj.coffee_bean.notes or '',
                    'taste_notes': [],
                    # 添加初始包装信息字段
                    'initial_bag_weight': float(obj.coffee_bean.initial_bag_weight) if hasattr(obj.coffee_bean, 'initial_bag_weight') and obj.coffee_bean.initial_bag_weight is not None else None,
                    'initial_bag_remain': float(obj.coffee_bean.initial_bag_remain) if hasattr(obj.coffee_bean, 'initial_bag_remain') and obj.coffee_bean.initial_bag_remain is not None else None,
                    'initial_purchase_price': float(obj.coffee_bean.initial_purchase_price) if hasattr(obj.coffee_bean, 'initial_purchase_price') and obj.coffee_bean.initial_purchase_price is not None else None,
                    'initial_roast_date': self._convert_date_to_timestamp(obj.coffee_bean.initial_roast_date) if hasattr(obj.coffee_bean, 'initial_roast_date') and obj.coffee_bean.initial_roast_date else None,
                    'initial_created_at': obj.coffee_bean.initial_created_at.timestamp() if hasattr(obj.coffee_bean, 'initial_created_at') and obj.coffee_bean.initial_created_at else None,
                    'initial_rest_period_min': obj.coffee_bean.initial_rest_period_min if hasattr(obj.coffee_bean, 'initial_rest_period_min') else None,
                    'initial_rest_period_max': obj.coffee_bean.initial_rest_period_max if hasattr(obj.coffee_bean, 'initial_rest_period_max') else None
                }
            # 如果coffee_bean为空，返回一个包含必要id字段的空对象
            return {
                'id': 0,  # 提供一个默认ID
                'name': '',
                'roaster': '',
                'origin': '',
                'process': '',
                'type': 'SINGLE',
                'type_display': '单品',
                'roast_level': 3,
                'roast_level_display': '中浅烘',
                'description': '',
                'price': None,
                'weight': None,
                'purchase_date': None,
                'roast_date': None,
                'is_finished': False,
                'created_at': None,
                'is_favorite': False,
                'is_decaf': False,
                'is_archived': False,
                'is_deleted': False,
                'altitude_type': 'SINGLE',
                'altitude_single': None,
                'altitude_min': None,
                'altitude_max': None,
                'region': '',
                'finca': '',
                'variety': '',
                'barcode': '',
                'rest_period_min': None,
                'rest_period_max': None,
                'rest_period_progress': None,
                'stock_status': '充足',
                'deleted_at': None,
                'bag_remain': None,
                'notes': '',
                'taste_notes': [],
                'initial_bag_weight': None,
                'initial_bag_remain': None,
                'initial_purchase_price': None,
                'initial_roast_date': None,
                'initial_created_at': None,
                'initial_rest_period_min': None,
                'initial_rest_period_max': None
            }
        except Exception as e:
            logger.error(f"获取咖啡豆时出错: {str(e)}")
            # 出错时也返回包含id字段的空对象
            return {
                'id': 0,
                'name': '',
                'roaster': '',
                'origin': '',
                'process': '',
                'type': 'SINGLE',
                'type_display': '单品',
                'roast_level': 3,
                'roast_level_display': '中浅烘',
                'description': '',
                'price': None,
                'weight': None,
                'purchase_date': None,
                'roast_date': None,
                'is_finished': False,
                'created_at': None,
                'is_favorite': False,
                'is_decaf': False,
                'is_archived': False,
                'is_deleted': False,
                'altitude_type': 'SINGLE',
                'altitude_single': None,
                'altitude_min': None,
                'altitude_max': None,
                'region': '',
                'finca': '',
                'variety': '',
                'barcode': '',
                'rest_period_min': None,
                'rest_period_max': None,
                'rest_period_progress': None,
                'stock_status': '充足',
                'deleted_at': None,
                'bag_remain': None,
                'notes': '',
                'taste_notes': [],
                'initial_bag_weight': None,
                'initial_bag_remain': None,
                'initial_purchase_price': None,
                'initial_roast_date': None,
                'initial_created_at': None,
                'initial_rest_period_min': None,
                'initial_rest_period_max': None
            }

    def get_flavor_tags(self, obj):
        try:
            return [{'id': tag.id, 'name': tag.name} for tag in obj.flavor_tags.all()]
        except Exception as e:
            logger.error(f"获取风味标签时出错: {str(e)}")
            return []

    def get_gadgets(self, obj):
        try:
            gadgets_list = []

            # 优先使用 gadget_kit 中的小工具，其次使用直接关联的小工具
            if obj.gadget_kit and hasattr(obj.gadget_kit, 'gadget_components'):
                # 如果有小工具组合，返回组合中的小工具
                gadgets = obj.gadget_kit.gadget_components.all()
            else:
                # 如果没有小工具组合，返回直接关联的小工具
                gadgets = obj.gadgets.all()

            for gadget in gadgets:
                gadget_data = {
                    'id': gadget.id,
                    'name': gadget.name,
                    'type': gadget.type,
                    'brand': gadget.brand,
                    'model': None,
                    'description': None,
                    'purchase_date': gadget.purchase_date_timestamp if hasattr(gadget, 'purchase_date_timestamp') else None,
                    'purchase_price': float(gadget.purchase_price) if gadget.purchase_price else None,
                    'notes': gadget.notes,
                    'created_at': gadget.created_at.timestamp() if hasattr(gadget.created_at, 'timestamp') else None,
                    'updated_at': gadget.created_at.timestamp() if hasattr(gadget.created_at, 'timestamp') else None,
                    'is_deleted': gadget.is_deleted,
                    'is_archived': gadget.is_archived,
                    'is_active': not (gadget.is_deleted or gadget.is_archived),
                    'brew_method': getattr(gadget, 'brew_method', None),
                    'brew_method_display': gadget.get_brew_method_display() if hasattr(gadget, 'get_brew_method_display') and getattr(gadget, 'brew_method', None) else None,
                    'grinder_purpose': getattr(gadget, 'grinder_purpose', None),
                    'grinder_purpose_display': gadget.get_grinder_purpose_display() if hasattr(gadget, 'get_grinder_purpose_display') and getattr(gadget, 'grinder_purpose', None) else None,
                    'grind_size_preset': getattr(gadget, 'grind_size_preset', None),
                    'depreciation_rate': float(gadget._depreciation_rate),
                    'break_even_progress': float(gadget.break_even_progress())
                }
                gadgets_list.append(gadget_data)
            return gadgets_list
        except Exception as e:
            logger.error(f"获取小工具时出错: {str(e)}", exc_info=True)
            return []

    def get_gadget_kit(self, obj):
        try:
            if obj.gadget_kit:
                kit = obj.gadget_kit
                # 确保kit有id属性且不为空
                if getattr(kit, 'id', None):
                    return {
                        'id': kit.id,
                        'name': kit.name,
                        'type': kit.type,
                        'brand': kit.brand,
                        'model': None,
                        'description': None,
                        'purchase_date': kit.purchase_date_timestamp if hasattr(kit, 'purchase_date_timestamp') else None,
                        'purchase_price': float(kit.purchase_price) if kit.purchase_price else None,
                        'notes': kit.notes,
                        'created_at': kit.created_at.timestamp() if hasattr(kit.created_at, 'timestamp') else None,
                        'updated_at': kit.created_at.timestamp() if hasattr(kit.created_at, 'timestamp') else None,
                        'is_deleted': kit.is_deleted,
                        'is_archived': kit.is_archived,
                        'is_active': not (kit.is_deleted or kit.is_archived),
                        'brew_method': getattr(kit, 'brew_method', None),
                        'brew_method_display': kit.get_brew_method_display() if hasattr(kit, 'get_brew_method_display') and getattr(kit, 'brew_method', None) else None,
                        'grinder_purpose': getattr(kit, 'grinder_purpose', None),
                        'grinder_purpose_display': kit.get_grinder_purpose_display() if hasattr(kit, 'get_grinder_purpose_display') and getattr(kit, 'grinder_purpose', None) else None,
                        'grind_size_preset': getattr(kit, 'grind_size_preset', None),
                        'depreciation_rate': float(kit._depreciation_rate),
                        'break_even_progress': float(kit.break_even_progress())
                    }
            # 如果没有gadget_kit或kit没有id，明确返回None而非空对象
            return None
        except Exception as e:
            logger.error(f"获取小工具套件时出错: {str(e)}", exc_info=True)
            return None

    def get_tag_overlap(self, obj):
        return obj.calculate_tag_overlap()

    def get_is_decaf(self, obj):
        """获取是否为脱因咖啡"""
        try:
            if obj.coffee_bean and hasattr(obj.coffee_bean, 'is_decaf'):
                return obj.coffee_bean.is_decaf
            return False
        except Exception as e:
            logger.error(f"获取is_decaf时出错: {str(e)}")
            return False

    def to_representation(self, instance):
        try:
            data = {
                'id': instance.id,
                'recipe_name': instance.recipe_name or '',
                'brewing_equipment': self.get_brewing_equipment(instance) or {},
                'grinding_equipment': self.get_grinding_equipment(instance) or {},
                'coffee_bean': self.get_coffee_bean(instance),  # 移除or {}，确保总是返回带id的字典
                'dose_weight': float(instance.dose_weight or 0),
                'yield_weight': float(instance.yield_weight or 0),
                'water_temperature': float(instance.water_temperature or 0),
                'brewing_time': self.get_brewing_time(instance),
                'grind_size': instance.grind_size or '',
                'rating_level': int(instance.rating_level or 0),
                'rating_display': instance.get_rating_level_display() or '😐',
                'notes': instance.notes or '',
                'created_at': instance.created_at.timestamp(),

                # 品鉴维度
                'aroma': int(instance.aroma or 0),
                'acidity': int(instance.acidity or 0),
                'sweetness': int(instance.sweetness or 0),
                'body': int(instance.body or 0),
                'aftertaste': int(instance.aftertaste or 0),

                # 环境信息
                'water_quality': instance.water_quality or '',
                'room_temperature': float(instance.room_temperature or 0),
                'room_humidity': int(instance.room_humidity or 0),

                # 步骤和风味标签
                'steps': instance.steps or [],
                'flavor_tags': self.get_flavor_tags(instance),
                'gadgets': self.get_gadgets(instance),
                'gadget_kit': self.get_gadget_kit(instance),
                'tag_overlap': self.get_tag_overlap(instance),
                'is_decaf': self.get_is_decaf(instance)
            }

            # 确保所有数值类型字段都是正确的类型
            data['dose_weight'] = float(data['dose_weight'])
            data['yield_weight'] = float(data['yield_weight'])
            data['water_temperature'] = float(data['water_temperature'])
            data['brewing_time'] = int(data['brewing_time'])
            data['rating_level'] = int(data['rating_level'])
            data['room_temperature'] = float(data['room_temperature'])
            data['room_humidity'] = int(data['room_humidity'])
            # tag_overlap可能为None或整数，保持原样不做强制类型转换

            # 确保字符串类型字段不是None
            data['recipe_name'] = str(data['recipe_name'])
            data['grind_size'] = str(data['grind_size'])
            data['notes'] = str(data['notes'])
            data['water_quality'] = str(data['water_quality'])

            # 确保列表类型字段不是None
            data['steps'] = list(data['steps'])
            data['flavor_tags'] = list(data['flavor_tags'])
            if data['gadgets'] is None:
                data['gadgets'] = []

            # 特别注意：对于gadget_kit字段，当值为None时保持为null，不要转换为空对象
            if data['gadget_kit'] is None:
                data['gadget_kit'] = None  # 保持为null，不转为{}

            return data
        except Exception as e:
            logger.error(f"序列化冲煮记录时发生错误: {str(e)}", exc_info=True)
            # 返回一个有效的默认记录
            return {
                'id': instance.id,
                'recipe_name': '',
                'brewing_equipment': {},
                'grinding_equipment': {},
                'coffee_bean': {  # 确保返回包含id的字典
                    'id': 0,
                    'name': '',
                    'roaster': '',
                    'origin': '',
                    'process': '',
                    'type': 'SINGLE',
                    'type_display': '单品',
                    'roast_level': 3,
                    'roast_level_display': '中浅烘',
                    'description': '',
                    'price': None,
                    'weight': None,
                    'purchase_date': None,
                    'roast_date': None,
                    'is_finished': False,
                    'created_at': None,
                    'is_favorite': False,
                    'is_decaf': False,
                    'is_archived': False,
                    'is_deleted': False,
                    'altitude_type': 'SINGLE',
                    'altitude_single': None,
                    'altitude_min': None,
                    'altitude_max': None,
                    'region': '',
                    'finca': '',
                    'variety': '',
                    'barcode': '',
                    'rest_period_min': None,
                    'rest_period_max': None,
                    'rest_period_progress': None,
                    'stock_status': '充足',
                    'deleted_at': None,
                    'bag_remain': None,
                    'notes': '',
                    'taste_notes': []
                },
                'dose_weight': 0.0,
                'yield_weight': 0.0,
                'water_temperature': 0.0,
                'brewing_time': 0,
                'grind_size': '',
                'rating_level': 0,
                'rating_display': '😐',
                'notes': '',
                'created_at': instance.created_at.timestamp(),
                'aroma': 0,
                'acidity': 0,
                'sweetness': 0,
                'body': 0,
                'aftertaste': 0,
                'water_quality': '',
                'room_temperature': 0.0,
                'room_humidity': 0,
                'steps': [],
                'flavor_tags': [],
                'gadgets': [],
                'gadget_kit': None,
                'tag_overlap': None,
                'is_decaf': False
            }
        except Exception as e:
            logger.error(f"序列化冲煮记录时发生错误: {str(e)}", exc_info=True)
            # 返回一个有效的默认记录
            return {
                'id': instance.id,
                'recipe_name': '',
                'brewing_equipment': {},
                'grinding_equipment': {},
                'coffee_bean': {  # 确保返回包含id的字典
                    'id': 0,
                    'name': '',
                    'roaster': '',
                    'origin': '',
                    'process': '',
                    'type': 'SINGLE',
                    'type_display': '单品',
                    'roast_level': 3,
                    'roast_level_display': '中浅烘',
                    'description': '',
                    'price': None,
                    'weight': None,
                    'purchase_date': None,
                    'roast_date': None,
                    'is_finished': False,
                    'created_at': None,
                    'is_favorite': False,
                    'is_decaf': False,
                    'is_archived': False,
                    'is_deleted': False,
                    'altitude_type': 'SINGLE',
                    'altitude_single': None,
                    'altitude_min': None,
                    'altitude_max': None,
                    'region': '',
                    'finca': '',
                    'variety': '',
                    'barcode': '',
                    'rest_period_min': None,
                    'rest_period_max': None,
                    'rest_period_progress': None,
                    'stock_status': '充足',
                    'deleted_at': None,
                    'bag_remain': None,
                    'notes': '',
                    'taste_notes': []
                },
                'dose_weight': 0.0,
                'yield_weight': 0.0,
                'water_temperature': 0.0,
                'brewing_time': 0,
                'grind_size': '',
                'rating_level': 0,
                'rating_display': '😐',
                'notes': '',
                'created_at': instance.created_at.timestamp(),
                'aroma': 0,
                'acidity': 0,
                'sweetness': 0,
                'body': 0,
                'aftertaste': 0,
                'water_quality': '',
                'room_temperature': 0.0,
                'room_humidity': 0,
                'steps': [],
                'flavor_tags': [],
                'gadgets': [],
                'gadget_kit': None,
                'tag_overlap': None,
                'is_decaf': False
            }


class IOSRecipeTagSerializer(serializers.ModelSerializer):
    """配方标签序列化器"""
    created_at = serializers.SerializerMethodField()

    class Meta:
        model = RecipeTag
        fields = ['id', 'name', 'created_at']

    def get_created_at(self, obj):
        """获取创建时间的时间戳"""
        return obj.created_at.timestamp()


class IOSRecipeThreadSerializer(serializers.ModelSerializer):
    """配方主题序列化器"""
    tags = IOSRecipeTagSerializer(many=True, read_only=True)
    created_at = serializers.SerializerMethodField()
    updated_at = serializers.SerializerMethodField()
    last_used_at = serializers.SerializerMethodField()
    latest_record = serializers.SerializerMethodField()

    class Meta:
        model = RecipeThread
        fields = [
            'id', 'recipe_name', 'use_count', 'created_at',
            'updated_at', 'last_used_at', 'tags', 'latest_record'
        ]

    def get_created_at(self, obj):
        """获取创建时间的时间戳"""
        return obj.created_at.timestamp()

    def get_updated_at(self, obj):
        """获取更新时间的时间戳"""
        return obj.updated_at.timestamp()

    def get_last_used_at(self, obj):
        """获取最后使用时间的时间戳"""
        if obj.last_used_at:
            return obj.last_used_at.timestamp()
        return None

    def get_latest_record(self, obj):
        """获取最新的冲煮记录信息"""
        try:
            latest_record = obj.get_latest_record()
            if latest_record:
                return {
                    'id': latest_record.id,
                    'created_at': latest_record.created_at.timestamp()
                }
            return None
        except Exception as e:
            logger.error(f"获取最新记录时出错: {str(e)}")
            return None