# iOS应用缓存优化

本文档介绍了为iOS客户端应用API添加的缓存优化机制，以减少服务器负载并提高应用响应速度。

## 缓存机制概述

缓存系统包含两个主要部分：
1. **服务端缓存**：使用Redis存储API响应，避免重复计算
2. **客户端缓存**：通过HTTP缓存控制头(ETag, Last-Modified等)指导iOS客户端进行本地缓存

## 技术实现

### 服务端缓存

- 使用了自定义的`ios_cache_response`装饰器对高频访问的API接口进行缓存处理
- 缓存数据存储在Redis中，同时提供内存缓存作为降级方案
- 不同API端点设置了不同的缓存过期时间，基于数据更新频率：
  - 高频变更数据(如冲煮记录列表): 5分钟
  - 中频变更数据(如设备列表): 30分钟
  - 低频变更数据(如冲煮方法列表): 24小时

### HTTP缓存控制

- 为API响应添加了ETag和Last-Modified头
- 添加了Cache-Control头控制客户端缓存生命周期
- 支持条件请求(If-None-Match, If-Modified-Since)，未更改时返回304状态码

### 缓存失效

- 当相关数据变更时(如删除记录)，自动清除关联缓存
- 提供了`invalidate_ios_cache`函数用于手动清除缓存
- 支持通过URL参数(no_cache=true)临时绕过缓存

## 使用方法

### 添加缓存到API视图

```python
from iosapp.cache_utils import ios_cache_response

@api_view(['GET'])
@permission_classes([IsAuthenticated]) 
@ios_cache_response('endpoint_name')  # 端点名称应与IOS_CACHE_CONFIG中定义的匹配
def my_api_view(request):
    # 视图函数内容不变
    ...
```

### 手动清除缓存

```python
from iosapp.cache_utils import invalidate_ios_cache

# 清除特定用户的特定端点缓存
invalidate_ios_cache('endpoint_name', user_id)

# 清除所有用户的特定端点缓存
invalidate_ios_cache('endpoint_name')

# 清除特定用户的所有缓存
invalidate_ios_cache(user_id=user_id)

# 清除所有iOS缓存
invalidate_ios_cache()
```

### 客户端(iOS)注意事项

iOS应用应当实现以下功能以充分利用服务端缓存机制:

1. 根据Cache-Control头存储本地缓存
2. 发送条件请求时包含ETag和Last-Modified头
3. 正确处理304状态码响应，使用本地缓存数据
4. 提供强制刷新功能以便用户获取最新数据(发送no_cache=true参数)

## 缓存配置

所有缓存配置位于`iosapp/cache_utils.py`的`IOS_CACHE_CONFIG`字典中:

```python
IOS_CACHE_CONFIG = {
    # API端点及其缓存时间(秒)
    'equipment_list': 1800,      # 设备列表缓存30分钟
    'bean_list': 1800,           # 咖啡豆列表缓存30分钟
    'brewlog_list': 300,         # 冲煮记录列表缓存5分钟
    'brewlog_statistics': 300,   # 冲煮统计缓存5分钟
    'brew_methods_list': 86400,  # 冲煮方法列表缓存24小时
    ... 
}
```

## 性能影响

推荐对iOS客户端端实现缓存处理后，预期能够获得以下性能提升:

- 减少约80%的服务器数据库查询
- 降低服务器CPU使用率40-60%
- 减少网络带宽使用50-70%
- 提高客户端响应速度1-2秒
- 减少高峰期服务器负载并发问题

## 故障排除

如果遇到缓存问题:

1. 使用`no_cache=true`参数绕过缓存
2. 检查日志中是否有缓存相关错误
3. 手动清除特定端点缓存
4. 如果Redis服务出现问题，系统将自动降级到内存缓存 