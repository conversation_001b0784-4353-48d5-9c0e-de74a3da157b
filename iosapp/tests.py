from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from .models import IOSDevice
import json
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

class IOSDeviceModelTest(TestCase):
    def setUp(self):
        self.username = "testuser"
        self.password = "testpassword123"
        self.user = User.objects.create_user(
            username=self.username,
            password=self.password,
            email="<EMAIL>"
        )
        self.device_id = "test-device-id-12345"
        
    def test_device_creation(self):
        """测试创建设备模型"""
        device = IOSDevice.objects.create(
            user=self.user,
            device_id=self.device_id
        )
        self.assertEqual(device.user, self.user)
        self.assertEqual(device.device_id, self.device_id)
        self.assertIsNone(device.push_token)
        
    def test_device_uniqueness(self):
        """测试设备ID唯一性约束"""
        IOSDevice.objects.create(
            user=self.user,
            device_id=self.device_id
        )
        # 创建重复设备ID应该引发异常
        with self.assertRaises(Exception):
            IOSDevice.objects.create(
                user=self.user,
                device_id=self.device_id
            )

class IOSAPITest(TestCase):
    def setUp(self):
        self.username = "testuser"
        self.password = "testpassword123"
        self.user = User.objects.create_user(
            username=self.username,
            password=self.password,
            email="<EMAIL>"
        )
        self.device_id = "test-device-id-12345"
        self.client = APIClient()
        
    def test_login_with_device(self):
        """测试登录API是否正确关联设备"""
        # 确保开始时没有设备记录
        self.assertEqual(IOSDevice.objects.count(), 0)
        
        # 调用登录API
        url = reverse('ios_api:login')
        data = {
            'username': self.username,
            'password': self.password,
            'device_id': self.device_id
        }
        response = self.client.post(url, data, format='json')
        
        # 检查响应状态
        self.assertEqual(response.status_code, 200)
        
        # 验证设备是否已创建
        self.assertEqual(IOSDevice.objects.count(), 1)
        device = IOSDevice.objects.first()
        self.assertEqual(device.user, self.user)
        self.assertEqual(device.device_id, self.device_id)
        
    def test_login_without_device(self):
        """测试没有设备ID的登录请求"""
        # 确保开始时没有设备记录
        self.assertEqual(IOSDevice.objects.count(), 0)
        
        # 调用登录API，不提供设备ID
        url = reverse('ios_api:login')
        data = {
            'username': self.username,
            'password': self.password
        }
        response = self.client.post(url, data, format='json')
        
        # 检查响应状态
        self.assertEqual(response.status_code, 200)
        
        # 验证没有创建设备记录
        self.assertEqual(IOSDevice.objects.count(), 0)
        
    def test_get_devices(self):
        """测试设备列表API"""
        # 创建测试设备
        device = IOSDevice.objects.create(
            user=self.user,
            device_id=self.device_id
        )
        
        # 登录用户
        self.client.force_authenticate(user=self.user)
        
        # 获取设备列表
        url = reverse('ios_api:user_devices')
        response = self.client.get(url)
        
        # 检查响应
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['devices']), 1)
        self.assertEqual(data['devices'][0]['device_id'], self.device_id)
