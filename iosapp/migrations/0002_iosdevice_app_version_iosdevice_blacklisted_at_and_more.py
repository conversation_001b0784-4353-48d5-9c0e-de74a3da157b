# Generated by Django 4.2.20 on 2025-04-17 11:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('iosapp', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='iosdevice',
            name='app_version',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='iosdevice',
            name='blacklisted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='iosdevice',
            name='device_model',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='iosdevice',
            name='device_os_version',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='iosdevice',
            name='is_blacklisted',
            field=models.Bo<PERSON>an<PERSON>ield(default=False),
        ),
        migrations.AddField(
            model_name='iosdevice',
            name='last_active',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='iosdevice',
            name='last_refresh',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
