# iOS客户端缓存实现指南

本文档为iOS客户端开发者提供了实现缓存机制的详细指导，以配合服务端缓存优化，提高应用性能和用户体验。

## 概述

咖啡大杂项目的服务端API已添加了完整的缓存机制，包括Redis服务端缓存和HTTP缓存控制头。为了充分利用这些优化，iOS客户端需要实现对应的缓存处理逻辑。

## 技术要点

### 1. HTTP缓存控制头处理

服务端API响应包含以下缓存控制头：

- `ETag`：内容哈希值，用于判断资源是否变更
- `Last-Modified`：资源最后修改时间
- `Cache-Control`：缓存策略与过期时间指令

### 2. 实现条件请求

当再次请求相同资源时，客户端应发送：

- `If-None-Match`：包含之前获取到的ETag值
- `If-Modified-Since`：包含之前获取到的Last-Modified值

如果资源未变更，服务器将返回`304 Not Modified`状态码，无响应体，客户端应使用本地缓存的内容。

## 实现建议

### URLSession配置

```swift
// 配置URLSession使用磁盘缓存
let configuration = URLSessionConfiguration.default
configuration.requestCachePolicy = .useProtocolCachePolicy
configuration.urlCache = URLCache(memoryCapacity: 10 * 1024 * 1024,    // 10MB内存缓存
                                  diskCapacity: 50 * 1024 * 1024,      // 50MB磁盘缓存
                                  diskPath: "KafeiDaZiCache")

let session = URLSession(configuration: configuration)
```

### 处理API响应

```swift
func fetchData(endpoint: String, completion: @escaping (Result<Data, Error>) -> Void) {
    guard let url = URL(string: "https://kafeidazi.com/ios_api/\(endpoint)/") else {
        completion(.failure(NSError(domain: "InvalidURL", code: -1, userInfo: nil)))
        return
    }
    
    var request = URLRequest(url: url)
    request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
    
    let task = session.dataTask(with: request) { data, response, error in
        if let error = error {
            completion(.failure(error))
            return
        }
        
        guard let httpResponse = response as? HTTPURLResponse else {
            completion(.failure(NSError(domain: "InvalidResponse", code: -1, userInfo: nil)))
            return
        }
        
        // 处理304状态码 - 使用缓存数据
        if httpResponse.statusCode == 304 {
            // 从URLCache中获取缓存响应
            if let cachedResponse = session.configuration.urlCache?.cachedResponse(for: request),
               let cachedData = cachedResponse.data {
                completion(.success(cachedData))
                return
            } else {
                // 如果无法获取缓存，重新请求完整数据
                request.cachePolicy = .reloadIgnoringLocalCacheData
                self.fetchData(endpoint: endpoint, completion: completion)
                return
            }
        }
        
        // 处理普通成功响应
        if (200...299).contains(httpResponse.statusCode), let data = data {
            // 存储ETag和Last-Modified到本地存储（可选）
            if let etag = httpResponse.allHeaderFields["ETag"] as? String {
                UserDefaults.standard.set(etag, forKey: "ETag_\(endpoint)")
            }
            if let lastModified = httpResponse.allHeaderFields["Last-Modified"] as? String {
                UserDefaults.standard.set(lastModified, forKey: "LastModified_\(endpoint)")
            }
            
            completion(.success(data))
        } else {
            completion(.failure(NSError(domain: "HTTPError", code: httpResponse.statusCode, userInfo: nil)))
        }
    }
    
    task.resume()
}
```

### Alamofire集成（如果使用）

如果项目使用Alamofire，可以这样配置：

```swift
import Alamofire

let sessionManager: Session = {
    let configuration = URLSessionConfiguration.default
    configuration.requestCachePolicy = .useProtocolCachePolicy
    configuration.urlCache = URLCache(memoryCapacity: 10 * 1024 * 1024,
                                      diskCapacity: 50 * 1024 * 1024,
                                      diskPath: "KafeiDaZiCache")
    
    return Session(configuration: configuration)
}()

// 使用带缓存控制的请求
func fetchDataWithAlamofire(endpoint: String, completion: @escaping (Result<Data, Error>) -> Void) {
    // 从本地存储获取之前的ETag和Last-Modified
    let etag = UserDefaults.standard.string(forKey: "ETag_\(endpoint)")
    let lastModified = UserDefaults.standard.string(forKey: "LastModified_\(endpoint)")
    
    var headers: HTTPHeaders = ["Authorization": "Bearer \(accessToken)"]
    
    // 添加条件请求头
    if let etag = etag {
        headers["If-None-Match"] = etag
    }
    if let lastModified = lastModified {
        headers["If-Modified-Since"] = lastModified
    }
    
    sessionManager.request("https://kafeidazi.com/ios_api/\(endpoint)/", 
                           headers: headers)
        .validate()
        .responseData { response in
            // 处理304状态码 - 使用缓存数据
            if response.response?.statusCode == 304,
               let request = response.request,
               let urlCache = sessionManager.session.configuration.urlCache,
               let cachedResponse = urlCache.cachedResponse(for: request),
               let cachedData = cachedResponse.data {
                completion(.success(cachedData))
                return
            }
            
            // 处理正常响应
            switch response.result {
            case .success(let data):
                // 存储新的ETag和Last-Modified
                if let etag = response.response?.allHeaderFields["ETag"] as? String {
                    UserDefaults.standard.set(etag, forKey: "ETag_\(endpoint)")
                }
                if let lastModified = response.response?.allHeaderFields["Last-Modified"] as? String {
                    UserDefaults.standard.set(lastModified, forKey: "LastModified_\(endpoint)")
                }
                
                completion(.success(data))
            case .failure(let error):
                completion(.failure(error))
            }
        }
}
```

### 添加强制刷新功能

为了让用户能够手动刷新内容，可以添加强制刷新功能：

```swift
func forceFetchData(endpoint: String, completion: @escaping (Result<Data, Error>) -> Void) {
    guard let url = URL(string: "https://kafeidazi.com/ios_api/\(endpoint)/?no_cache=true") else {
        completion(.failure(NSError(domain: "InvalidURL", code: -1, userInfo: nil)))
        return
    }
    
    var request = URLRequest(url: url)
    request.cachePolicy = .reloadIgnoringLocalCacheData
    request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
    
    let task = session.dataTask(with: request) { data, response, error in
        // 处理响应...
    }
    
    task.resume()
}
```

### 实现缓存管理工具类

推荐创建一个专门的缓存管理工具类：

```swift
class KFDZCacheManager {
    static let shared = KFDZCacheManager()
    private let urlCache: URLCache
    
    private init() {
        urlCache = URLCache(memoryCapacity: 10 * 1024 * 1024,
                            diskCapacity: 50 * 1024 * 1024,
                            diskPath: "KafeiDaZiCache")
        URLCache.shared = urlCache
    }
    
    // 清除特定端点的缓存
    func clearCache(for endpoint: String) {
        UserDefaults.standard.removeObject(forKey: "ETag_\(endpoint)")
        UserDefaults.standard.removeObject(forKey: "LastModified_\(endpoint)")
        
        // 尝试清除URL缓存
        if let url = URL(string: "https://kafeidazi.com/ios_api/\(endpoint)/"),
           let request = try? URLRequest(url: url, method: .get) {
            urlCache.removeCachedResponse(for: request)
        }
    }
    
    // 清除所有缓存
    func clearAllCache() {
        urlCache.removeAllCachedResponses()
        
        // 清除所有ETag和Last-Modified记录
        let userDefaults = UserDefaults.standard
        for key in userDefaults.dictionaryRepresentation().keys {
            if key.hasPrefix("ETag_") || key.hasPrefix("LastModified_") {
                userDefaults.removeObject(forKey: key)
            }
        }
    }
    
    // 获取缓存大小信息
    func getCacheInfo() -> (memoryUsed: Int, diskUsed: Int) {
        return (urlCache.currentMemoryUsage, urlCache.currentDiskUsage)
    }
}
```

## 缓存策略建议

根据不同数据类型和刷新频率，建议采用以下缓存策略：

### 1. 高频变更数据

- 冲煮记录列表
- 冲煮统计数据

策略：
- 缓存时间较短（不超过5分钟）
- 列表页面每次进入自动刷新
- 提供下拉刷新功能

### 2. 中频变更数据

- 设备列表
- 咖啡豆列表 
- 热力图数据

策略：
- 缓存时间中等（约30分钟）
- 需要提供刷新按钮
- 有数据变更操作后自动刷新

### 3. 低频变更数据

- 冲煮方法列表
- 用户资料

策略：
- 长时间缓存（24小时以上）
- 应用启动时检查更新
- 仅在相关设置变更后刷新

## 调试建议

1. 在开发阶段，使用Charles或Proxyman等代理工具监控HTTP请求头和响应头
2. 添加缓存状态指示器，显示是使用的缓存数据还是新获取的数据
3. 实现缓存信息查看和清理功能，方便调试和解决潜在问题

## 注意事项

1. 确保处理好登录状态变化时的缓存清理
2. 不要缓存包含敏感信息的响应
3. 为用户提供明确的缓存清理选项
4. 处理网络异常时优先使用缓存数据
5. 关键数据（如交易相关）应适当降低缓存依赖

## 性能优化建议

1. 在适当的生命周期方法中预加载常用数据
2. 考虑实现后台预取机制，在应用空闲时更新缓存
3. 监控缓存命中率，根据实际使用情况调整缓存策略
4. 对大型响应考虑增加压缩处理 