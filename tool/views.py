from django.shortcuts import render, get_object_or_404
from .models import ToolPage

def get_tool_page_or_default(request, url_name):
    """
    尝试获取工具的 Wagtail 页面，如果不存在则使用默认模板
    """
    try:
        tool_page = ToolPage.objects.get(url_name=url_name)
        return tool_page.serve(request)
    except ToolPage.DoesNotExist:
        template_name = f'tool/{url_name.replace("-", "_")}.html'
        return render(request, template_name, {})

def caffeine_calculator(request):
    return get_tool_page_or_default(request, 'caffeine_calculator')

def espresso_calculator(request):
    return get_tool_page_or_default(request, 'espresso_calculator')

def brew_ratio_calculator(request):
    return get_tool_page_or_default(request, 'brew_ratio_calculator')

def iced_filter_calculator(request):
    return get_tool_page_or_default(request, 'iced_filter_calculator')

def coffee_extraction_calculator(request):
    return get_tool_page_or_default(request, 'coffee_extraction_calculator')

def brewlog_landing_page(request):
    return get_tool_page_or_default(request, 'brewlog_landing_page')

def bean_to_gram(request):
    return get_tool_page_or_default(request, 'bean_to_gram')

def latte_calculator(request):
    return get_tool_page_or_default(request, 'latte_calculator')