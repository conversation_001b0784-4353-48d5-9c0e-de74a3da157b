from django.urls import path
from . import views

urlpatterns = [
    path('caffeine-calculator', views.caffeine_calculator, name='caffeine_calculator'),
    path('caffeine-calculator/', views.caffeine_calculator, name='caffeine_calculator'),
    path('espresso-calculator', views.espresso_calculator, name='espresso_calculator'),
    path('espresso-calculator/', views.espresso_calculator, name='espresso_calculator'),
    path('brew-ratio-calculator', views.brew_ratio_calculator, name='brew_ratio_calculator'),
    path('brew-ratio-calculator/', views.brew_ratio_calculator, name='brew_ratio_calculator'),
    path('iced-filter-calculator', views.iced_filter_calculator, name='iced_filter_calculator'),
    path('iced-filter-calculator/', views.iced_filter_calculator, name='iced_filter_calculator'),
    path('coffee-extraction-calculator', views.coffee_extraction_calculator, name='coffee_extraction_calculator'),
    path('coffee-extraction-calculator/', views.coffee_extraction_calculator, name='coffee_extraction_calculator'),
    path('brewlog/', views.brewlog_landing_page, name='brewlog_landing_page'),
    path('brewlog', views.brewlog_landing_page, name='brewlog_landing_page'),
    path('bean-to-gram/', views.bean_to_gram, name='bean_to_gram'),
    path('bean-to-gram', views.bean_to_gram, name='bean_to_gram'),
    path('latte-calculator/', views.latte_calculator, name='latte_calculator'),
    path('latte-calculator', views.latte_calculator, name='latte_calculator'),
]