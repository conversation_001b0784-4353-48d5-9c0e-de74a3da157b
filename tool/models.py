from django.db import models
from wagtail.models import Page, Orderable
from wagtail.admin.panels import FieldPanel, MultiFieldPanel, InlinePanel
from wagtailmarkdown.fields import MarkdownField
from django.core.exceptions import ValidationError
from modelcluster.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>, ParentalManyToManyField
from modelcluster.models import ClusterableModel
from django import forms

class ToolCategory(Orderable):
    """工具分类"""
    page = ParentalKey(
        'ToolIndexPage',
        on_delete=models.CASCADE,
        related_name='categories'
    )
    name = models.CharField(
        verbose_name="分类名称",
        max_length=100
    )

    panels = [
        FieldPanel('name'),
    ]

    def __str__(self):
        return self.name

class ToolIndexPage(Page, ClusterableModel):
    """工具列表页面"""
    intro = MarkdownField(blank=True)

    content_panels = Page.content_panels + [
        FieldPanel('intro', classname="full"),
        InlinePanel('categories', label="分类管理"),
    ]

    # 限制子页面类型只能是 ToolPage
    subpage_types = ['tool.ToolPage']

    def get_uncategorized_tools(self):
        """获取未分类的工具"""
        return ToolPage.objects.live().child_of(self).filter(categories=None)

    def get_tool_categories(self):
        """获取所有分类"""
        return self.categories.all()

class ToolPage(Page):
    """工具详情页面"""
    description = MarkdownField(
        verbose_name="工具描述",
        help_text="使用Markdown格式编写工具的详细描述"
    )
    
    url_name = models.CharField(
        verbose_name="URL名称",
        max_length=100,
        help_text="工具的标识名称，将用于URL和模板路径（例如：caffeine_calculator）"
    )

    # 新增字段
    image_url = models.URLField(
        verbose_name="图片URL",
        max_length=255,
        blank=True,
        help_text="工具的图片URL，留空则显示图标"
    )
    
    icon_class = models.CharField(
        verbose_name="图标类名",
        max_length=100,
        blank=True,
        help_text="@iconify/tailwind格式的图标类名，例如：icon-[tabler--coffee]"
    )
    
    icon_color = models.CharField(
        verbose_name="图标颜色",
        max_length=50,
        blank=True,
        help_text="Tailwind颜色类名，例如：text-primary"
    )

    categories = ParentalManyToManyField(
        'ToolCategory',
        verbose_name="工具分类",
        blank=True,
        related_name='tools'
    )

    content_panels = Page.content_panels + [
        MultiFieldPanel([
            FieldPanel('description'),
            FieldPanel('url_name'),
        ], heading="基本信息"),
        MultiFieldPanel([
            FieldPanel('image_url'),
            FieldPanel('icon_class'),
            FieldPanel('icon_color'),
        ], heading="显示设置"),
        FieldPanel('categories', widget=forms.CheckboxSelectMultiple),
    ]

    # 设置父页面类型只能是 ToolIndexPage
    parent_page_types = ['tool.ToolIndexPage']

    def get_template(self, request, *args, **kwargs):
        """根据url_name获取对应的模板"""
        return f'tool/{self.url_name}.html'

    def clean(self):
        super().clean()
        # 验证模板文件是否存在
        from django.template.loader import get_template
        from django.template.exceptions import TemplateDoesNotExist
        try:
            get_template(f'tool/{self.url_name}.html')
        except TemplateDoesNotExist:
            raise ValidationError({
                'url_name': f"模板 'tool/{self.url_name}.html' 不存在"
            })

    def get_random_tools(self, exclude_self=True, limit=6):
        tools = type(self).objects.live()
        if exclude_self:
            tools = tools.exclude(id=self.id)
        return tools.order_by('?')[:limit]

    def get_context(self, request, *args, **kwargs):
        context = super().get_context(request, *args, **kwargs)
        context['random_tools'] = self.get_random_tools()
        return context