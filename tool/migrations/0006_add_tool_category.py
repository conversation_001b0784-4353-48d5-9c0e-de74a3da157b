from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields

class Migration(migrations.Migration):

    dependencies = [
        ('tool', '0005_remove_toolpage_template_alter_toolpage_url_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='ToolCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                ('name', models.CharField(max_length=100, verbose_name='分类名称')),
                ('page', modelcluster.fields.ParentalKey(on_delete=django.db.models.deletion.CASCADE, related_name='categories', to='tool.toolindexpage')),
            ],
            options={
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='toolpage',
            name='icon_class',
            field=models.CharField(blank=True, help_text='@iconify/tailwind格式的图标类名，例如：icon-[tabler--coffee]', max_length=100, verbose_name='图标类名'),
        ),
        migrations.AddField(
            model_name='toolpage',
            name='icon_color',
            field=models.CharField(blank=True, help_text='Tailwind颜色类名，例如：text-primary', max_length=50, verbose_name='图标颜色'),
        ),
        migrations.AddField(
            model_name='toolpage',
            name='image_url',
            field=models.URLField(blank=True, help_text='工具的图片URL，留空则显示图标', max_length=255, verbose_name='图片URL'),
        ),
        migrations.AddField(
            model_name='toolpage',
            name='categories',
            field=modelcluster.fields.ParentalManyToManyField(blank=True, related_name='tools', to='tool.toolcategory', verbose_name='工具分类'),
        ),
    ] 