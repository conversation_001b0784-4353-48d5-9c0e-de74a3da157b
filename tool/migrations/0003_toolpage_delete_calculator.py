# Generated by Django 4.2.7 on 2025-01-06 14:52

from django.db import migrations, models
import django.db.models.deletion
import wagtailmarkdown.fields


class Migration(migrations.Migration):

    dependencies = [
        ('tool', '0002_toolindexpage'),
    ]

    operations = [
        migrations.CreateModel(
            name='ToolPage',
            fields=[
                ('page_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='wagtailcore.page')),
                ('description', wagtailmarkdown.fields.MarkdownField(help_text='使用Markdown格式编写工具的详细描述', verbose_name='工具描述')),
                ('url_name', models.CharField(blank=True, help_text='对应 urls.py 中的 URL name，用于保持与旧路由的兼容', max_length=100, verbose_name='URL名称')),
                ('template', models.CharField(choices=[('tool/caffeine_calculator.html', '咖啡因计算器'), ('tool/brewlog_landing_page.html', '冲煮日志'), ('tool/espresso_calculator.html', '浓缩咖啡计算器'), ('tool/brew_ratio_calculator.html', '萃取比例计算器'), ('tool/iced_filter_calculator.html', '冰滴咖啡计算器'), ('tool/coffee_extraction_calculator.html', '咖啡萃取计算器'), ('tool/bean_to_gram.html', '咖啡豆克数换算'), ('tool/latte_calculator.html', '拿铁配比计算器')], help_text='选择此工具页面使用的模板', max_length=100, verbose_name='页面模板')),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
        migrations.DeleteModel(
            name='Calculator',
        ),
    ]
