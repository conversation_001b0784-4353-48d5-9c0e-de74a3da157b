from django.db import migrations
from django.db.models import Max, Q

def create_initial_tools(apps, schema_editor):
    ToolIndexPage = apps.get_model('tool', 'ToolIndexPage')
    ToolPage = apps.get_model('tool', 'ToolPage')
    Page = apps.get_model('wagtailcore', 'Page')
    ContentType = apps.get_model('contenttypes', 'ContentType')
    Locale = apps.get_model('wagtailcore', 'Locale')
    
    # 获取第一个可用的语言环境
    try:
        default_locale = Locale.objects.first()
        if not default_locale:
            default_locale = Locale.objects.create(language_code='zh')
    except Exception:
        default_locale = Locale.objects.create(language_code='zh')
    
    # 获取工具首页
    try:
        tool_index = ToolIndexPage.objects.get()
        tool_index_page = Page.objects.get(id=tool_index.page_ptr_id)
    except ToolIndexPage.DoesNotExist:
        return  # 如果工具首页不存在，跳过迁移

    # 获取 ToolPage 的 content type
    toolpage_content_type = ContentType.objects.get_for_model(ToolPage)
        
    # 定义初始工具列表
    tools = [
        {
            'title': '咖啡因计算器',
            'url_name': 'caffeine_calculator',
            'template': 'tool/caffeine_calculator.html',
            'description': '记下你一天所摄入的饮食，轻松计算你的咖啡因摄入量',
            'slug': 'caffeine-calculator',
        },
        {
            'title': '咖啡札记',
            'url_name': 'brewlog_landing_page',
            'template': 'tool/brewlog_landing_page.html',
            'description': '记录咖啡冲煮参数，管理咖啡器具和咖啡豆库存',
            'slug': 'brewlog',
        },
        {
            'title': '意式咖啡计算器',
            'url_name': 'espresso_calculator',
            'template': 'tool/espresso_calculator.html',
            'description': '计算以浓缩咖啡为基底的各种咖啡的调配比例',
            'slug': 'espresso-calculator',
        },
        {
            'title': '粉水比计算器',
            'url_name': 'brew_ratio_calculator',
            'template': 'tool/brew_ratio_calculator.html',
            'description': '计算手冲、冷萃、法压壶、摩卡壶等萃取方式的粉水比例',
            'slug': 'brew-ratio-calculator',
        },
        {
            'title': '冰手冲计算器',
            'url_name': 'iced_filter_calculator',
            'template': 'tool/iced_filter_calculator.html',
            'description': '计算手冲冰咖啡的冰块、热水、咖啡粉比例',
            'slug': 'iced-filter-calculator',
        },
        {
            'title': '萃取率计算器',
            'url_name': 'coffee_extraction_calculator',
            'template': 'tool/coffee_extraction_calculator.html',
            'description': '根据三个变量计算咖啡的萃取率',
            'slug': 'coffee-extraction-calculator',
        },
        {
            'title': '咖啡豆克数换算',
            'url_name': 'bean_to_gram',
            'template': 'tool/bean_to_gram.html',
            'description': '转换整颗咖啡全豆的个数与重量克数',
            'slug': 'bean-to-gram',
        },
        {
            'title': '拿铁配方计算器',
            'url_name': 'latte_calculator',
            'template': 'tool/latte_calculator.html',
            'description': '配方成分计算+杯型计算+反向配方计算',
            'slug': 'latte-calculator',
        },
    ]
    
    for tool in tools:
        # 检查工具是否已存在
        if not ToolPage.objects.filter(url_name=tool['url_name']).exists():
            # 获取最大的 path 值
            last_child = (Page.objects
                .filter(path__startswith=tool_index_page.path)
                .filter(~Q(path=tool_index_page.path))
                .order_by('path')
                .last())
            
            if last_child:
                last_path = last_child.path
                new_path = last_path[:-4] + str(int(last_path[-4:]) + 1).zfill(4)
            else:
                new_path = tool_index_page.path + '0001'

            # 创建工具页面
            tool_page = ToolPage(
                title=tool['title'],
                slug=tool['slug'],
                url_name=tool['url_name'],
                template=tool['template'],
                description=tool['description'],
                content_type=toolpage_content_type,
                path=new_path,
                depth=tool_index_page.depth + 1,
                numchild=0,
                url_path=tool_index_page.url_path + tool['slug'] + '/',
                locale=default_locale,
                live=True,
                has_unpublished_changes=False,
                first_published_at=None,
                last_published_at=None,
            )
            tool_page.save()

            # 更新父页面的子页面数量
            tool_index_page.numchild = tool_index_page.numchild + 1
            tool_index_page.save()

def remove_initial_tools(apps, schema_editor):
    """回滚函数"""
    ToolPage = apps.get_model('tool', 'ToolPage')
    Page = apps.get_model('wagtailcore', 'Page')
    
    # 获取所有要删除的工具页面的ID
    tool_ids = ToolPage.objects.filter(url_name__in=[
        'caffeine_calculator',
        'brewlog_landing_page',
        'espresso_calculator',
        'brew_ratio_calculator',
        'iced_filter_calculator',
        'coffee_extraction_calculator',
        'bean_to_gram',
        'latte_calculator',
    ]).values_list('page_ptr_id', flat=True)
    
    # 删除页面
    Page.objects.filter(id__in=tool_ids).delete()

class Migration(migrations.Migration):
    dependencies = [
        ('tool', '0003_toolpage_delete_calculator'),
    ]

    operations = [
        migrations.RunPython(create_initial_tools, remove_initial_tools),
    ] 