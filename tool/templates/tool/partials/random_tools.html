{% load wagtailcore_tags %}

<div class="container w-auto md:w-4/5 lg:w-4/5 mb-8 mx-4 md:mx-auto lg:mx-auto">
    <div class="divider pb-4 menu-title max-w-md mx-auto">更多实用工具</div>
    <div class="flex flex-wrap gap-3">
        {% for tool in random_tools %}
            <div class="contents text-base-content">
                <div class="relative border-2 border-base-content/5 rounded-lg basis-full xs:basis-[calc(50%-1.5rem)] mb-2 xs:mb-3 md:basis-[calc(50%-1.5rem)] lg:basis-[calc(33.33%-2rem)] transition-all duration-200 hover:shadow-sm active:shadow-sm hover:-translate-y-1 active:-translate-y-1">
                    <a href="{{ tool.url }}" target="_self" class="absolute inset-0"></a>
                    <div class="p-3">
                        <div class="flex items-center mb-2">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center overflow-hidden">
                                {% if tool.image_url %}
                                <img class="h-auto w-auto" src="{{ tool.image_url }}" alt="{{ tool.title }}">
                                {% elif tool.icon_class %}
                                <span class="{{ tool.icon_class }} text-2xl {% if tool.icon_color %}{{ tool.icon_color }}{% endif %}"></span>
                                {% endif %}
                            </div>
                            <div class="flex flex-col flex-1 px-2">
                                <h4 class="font-semibold text-lg truncate">{{ tool.title }}</h4>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-primary px-2 py-1 font-bold">查看</button>
                            </div>
                        </div>
                        <p class="text-sm text-base-content/50 line-clamp-2">
                            {{ tool.description|richtext }}
                        </p>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>