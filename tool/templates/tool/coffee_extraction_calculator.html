{% extends "base.html" %}
{% load static wagtailcore_tags wagtailuserbar cache %}

{% block content %}
<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
      <h1 class="text-xl text-center text-base-content">
        咖啡萃取率计算器
      </h1>
      <div class="text-sm text-center text-base-content opacity-60">
        <p>一个简单的咖啡萃取率计算工具，帮助你根据三个变量计算咖啡的萃取率：咖啡粉重量、TDS（总溶解固体）和咖啡液重量。落在金杯标准的计算结果将呈现绿色背景。</p>
      </div>
    </div>
</div>

<main class="max-w-2xl mx-auto p-6 space-y-6 text-base-content">
  <ol class="overflow-hidden space-y-8">
    <li class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">1</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">输入粉量</h4>
          <label class="input flex items-center gap-2 mt-2">
            <input id="coffee-grams" class="grow bg-transparent" placeholder="例：12.00" />
            <span>g</span>
          </label>
        </div>
      </div>
    </li>
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">2</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">输入TDS值</h4>
          <label class="input flex items-center gap-2 mt-2">
            <input id="tds" class="grow bg-transparent" placeholder="例：1.40" />
            <span>&nbsp;&nbsp;</span>
          </label>
        </div>
      </div>
    </li>
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">3</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">输入液重</h4>
          <label class="input flex items-center gap-2 mt-2">
            <input id="beverage-weight" class="grow bg-transparent" placeholder="例：165" />
            <span>g</span>
          </label>
        </div>
      </div>
    </li>
    <li class="relative flex-1">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10"><span
            class="icon-[ph--scroll-duotone] text-lg"></span></span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">萃取率</h4>
          <div id="result-container" class="flex items-center gap-4 my-6 text-2xl rounded-full p-4">
            <div id="emoji-header">☕️</div> <div id="extraction-result">0%</div>
          </div>
        </div>
      </div>
    </li>
  </ol>
</main>

{% cache 86400 'extraction_calc_faq' %}
  {% include "tool/partials/ext_calc_faq.html" %}
{% endcache %}

{% cache 86400 'random_tools' page.id %}
  {% include "tool/partials/random_tools.html" %}
{% endcache %}

<script>
const coffeeGramsInput=document['querySelector']('#coffee-grams'),tdsInput=document['querySelector']('#tds'),weightInput=document['querySelector']('#beverage-weight'),resultBox=document['querySelector']('#extraction-result'),emojiHeader=document['querySelector']('#emoji-header'),resultContainer=document['querySelector']('#result-container'),originalContainerBg=getComputedStyle(resultContainer)['backgroundColor'],originalEmoji=emojiHeader['textContent'];function getBackgroundColor(_0x5e51ff){if(!_0x5e51ff)return originalContainerBg;if(_0x5e51ff<0x12)return'#9b45b2';if(_0x5e51ff<0x14)return'#397754';if(_0x5e51ff<0x16)return'#70be51';if(_0x5e51ff<0x18)return'#f0a3bc';return'#eb6b40';}function getEmoji(_0x152087){if(!_0x152087)return originalEmoji;if(_0x152087<0x12)return'👀';if(_0x152087<0x14)return'👍';if(_0x152087<0x16)return'👌';if(_0x152087<0x18)return'🙌';return'🖖';}function getInputValues(){const _0x49fb07=Number(coffeeGramsInput['value']),_0xd5187=Number(tdsInput['value']),_0x307bce=Number(weightInput['value']);if(isNaN(_0x49fb07)&&isNaN(_0xd5187)&&isNaN(_0x307bce))return;return{'coffeeGrams':_0x49fb07,'tds':_0xd5187,'weight':_0x307bce};}function calculateExtraction(){const _0xa4a917=getInputValues();if(!_0xa4a917)return;const {coffeeGrams:_0x32bd81,tds:_0x475f8b,weight:_0x2bfc35}=_0xa4a917;return _0x2bfc35*_0x475f8b/_0x32bd81;}function updateResultIfReady(){const _0x10430a=calculateExtraction();resultBox&&(resultBox['innerText']=_0x10430a?_0x10430a['toFixed'](0x2)+'%':'0%'),resultContainer&&(resultContainer['style']['backgroundColor']=getBackgroundColor(_0x10430a)),emojiHeader&&(emojiHeader['textContent']=getEmoji(_0x10430a));}Boolean(coffeeGramsInput&&tdsInput&&weightInput)&&(coffeeGramsInput['addEventListener']('keyup',updateResultIfReady),tdsInput['addEventListener']('keyup',updateResultIfReady),weightInput['addEventListener']('keyup',updateResultIfReady));
</script>
{% endblock %}
