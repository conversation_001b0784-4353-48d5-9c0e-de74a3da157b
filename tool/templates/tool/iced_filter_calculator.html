{% extends "base.html" %}
{% load static wagtailcore_tags wagtailuserbar cache %}

{% block content %}
<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
      <h1 class="text-xl text-center text-base-content">
        冰手冲咖啡水粉比例计算器
      </h1>
      <div class="text-sm text-center text-base-content opacity-60">
        基于<a class="link link-hover" href="https://www.youtube.com/watch?v=PApBycDrPo0" target="_blank">詹姆斯·霍夫曼的<span class="underline">冰滤咖啡冲煮方案</span></a>计算
      </div>
    </div>
</div>

<main class="max-w-2xl mx-auto p-6 space-y-6 text-base-content">
  <ol class="overflow-hidden space-y-8">
    <li class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">1</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">选择计算方式</h4>
          <div class="flex flex-col items-center gap-4 my-6 md:grid md:grid-cols-2 lg:grid lg:grid-cols-2">
            <label class="flex cursor-pointer gap-2">
              <span class="label-text">按粉重</span>
              <input type="checkbox" id="toggle" class="toggle theme-controller" data-js-checkbox="data-js-checkbox" />
              <span class="label-text">按水重</span>
            </label>
          </div>
        </div>
      </div>
    </li>
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">2</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">输入重量</h4>
          <label class="input flex items-center gap-2 mt-4">
            <input type="number" id="weight" class="grow input input-ghost focus:outline-hidden" data-js-input="data-js-input" placeholder="0.00" />
            <span data-js-metric="data-js-metric">g</span>
          </label>
        </div>
      </div>
    </li>
    <li class="relative flex-1">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10"><span
            class="icon-[ph--scroll-duotone] text-lg"></span></span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">最终配方</h4>
          <div class="flex items-center gap-4 my-6">
            <div class="overflow-x-auto">
              <table class="table w-full text-lg">
                <tbody>
                  <tr>
                    <td class="opacity-70">水温</td>
                    <td class="font-medium">90-95 °C</td>
                  </tr>
                  <tr>
                    <td class="opacity-70">闷蒸</td>
                    <td class="font-medium">45 秒</td>
                  </tr>
                  <tr>
                    <td class="opacity-70">粉水比</td>
                    <td class="font-medium">1:15.3</td>
                  </tr>
                  <tr>
                    <td class="opacity-70">热水<span class="text-sm opacity-40">(60%)</span></td>
                    <td class="font-medium text-accent" data-js-hot-water="data-js-hot-water">0 ml</td>
                  </tr>
                  <tr>
                    <td class="opacity-70">冰块<span class="text-sm opacity-40">(40%)</span></td>
                    <td class="font-medium text-accent" data-js-ice="data-js-ice">0 ml</td>
                  </tr>
                  <tr class="hidden" data-js-hidden-row="data-js-hidden-row">
                    <td class="opacity-70">粉重</td>
                    <td class="font-medium text-accent" data-js-beans="data-js-beans">0 g</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </li>
  </ol>
</main>

{% cache 86400 'icedfilter_calc_faq' %}
  {% include "tool/partials/icedfilter_calc_faq.html" %}
{% endcache %}

{% cache 86400 'random_tools' page.id %}
  {% include "tool/partials/random_tools.html" %}
{% endcache %}

<script>
  const $input = document['querySelector']('[data-js-input]'), $checkbox = document['querySelector']('[data-js-checkbox]'), $ice = document['querySelector']('[data-js-ice]'), $hotWater = document['querySelector']('[data-js-hot-water]'), $mlGratio = 0x3e8 / 0x41, $gMlratio = 0x41 / 0x3e8, $metric = document['querySelector']('[data-js-metric]'), $hiddenRow = document['querySelector']('[data-js-hidden-row]'), $beans = document['querySelector']('[data-js-beans]'); $input['oninput'] = handleInput; function numberWithCommas(_0x4c22ab) { return _0x4c22ab['toString']()['replace'](/\B(?=(\d{3})+(?!\d))/g, ','); } function handleInput(_0x14ae0c) { const _0x597a34 = _0x14ae0c['target']['value']; compute(_0x597a34); } function handleOnChange(_0x3bc25f) { const _0x1752e7 = _0x3bc25f['value']; compute(_0x1752e7); } function compute(_0x460271) { const _0x577c5b = _0x460271; if ($checkbox['checked']) { var _0x9dab11 = _0x577c5b * $gMlratio; $beans['innerHTML'] = numberWithCommas(Math['round']((_0x9dab11 + Number['EPSILON']) * 0x64) / 0x64) + 'g', $ice['innerHTML'] = numberWithCommas(Math['round']((_0x577c5b * 0.4 + Number['EPSILON']) * 0x64) / 0x64) + '\x20ml', $hotWater['innerHTML'] = numberWithCommas(Math['round']((_0x577c5b * 0.6 + Number['EPSILON']) * 0x64) / 0x64) + '\x20ml'; } else { var _0x9dab11 = _0x577c5b * $mlGratio; $ice['innerHTML'] = numberWithCommas(Math['round']((_0x9dab11 * 0.4 + Number['EPSILON']) * 0x64) / 0x64) + '\x20ml', $hotWater['innerHTML'] = numberWithCommas(Math['round']((_0x9dab11 * 0.6 + Number['EPSILON']) * 0x64) / 0x64) + '\x20ml'; } } $checkbox['addEventListener']('change', _0x40b2e6 => { _0x40b2e6['target']['checked'] ? ($metric['innerHTML'] = 'ml', $input['placeholder'] = '00', $hiddenRow['classList']['toggle']('hidden'), handleOnChange($input)) : ($metric['innerHTML'] = 'g', $input['placeholder'] = '0.00', $hiddenRow['classList']['toggle']('hidden'), handleOnChange($input)); });
</script>
{% endblock %}
