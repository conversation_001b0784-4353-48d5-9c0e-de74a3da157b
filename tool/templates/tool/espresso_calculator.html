{% extends "base.html" %}
{% load static wagtailcore_tags wagtailuserbar cache %}

{% block content %}
<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
      <h1 class="text-xl text-center text-base-content">
        意式咖啡萃取比例计算器
      </h1>
      <div class="text-sm text-center text-base-content opacity-60">
        计算以浓缩咖啡为基底的各种咖啡的调配比例
      </div>
      <div class="text-xs text-center text-base-content opacity-30 mt-4">
        您还可以使用该工具计算不含牛奶的意式浓缩咖啡萃取比例
      </div>
    </div>
</div>

<div x-data="coffeeCalculator()" x-init="updateAll()" class="max-w-2xl mx-auto p-6 space-y-6 text-base-content">
  <ol class="overflow-hidden space-y-4">
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">1</span>
        <div class="block w-xl">
          <h4 class="text-base text-primary mt-1 lg:mt-2">选择饮品类型</h4>
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 w-full mt-4">
            <template x-for="(drink, index) in drinks" :key="index">
              <label class="px-2 py-1 flex items-center cursor-pointer">
                <input type="radio" x-model="typeOfBrew" :value="drink.value" name="type-of-brew" class="hidden"
                  @change="updateAll()">
                <span class="btn btn-wide"
                  :class="{'btn-neutral': typeOfBrew === drink.value, 'btn-outline': typeOfBrew !== drink.value}"
                  x-text="drink.label"></span>
              </label>
            </template>
          </div>
        </div>
      </div>
    </li>
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">2</span>
        <div class="block w-xl">
          <h4 class="text-base text-primary mt-1 lg:mt-2">选择浓缩份数和咖啡粉量</h4>
          <div class="flex flex-col items-start gap-4 my-6">
            <div class="tabs tabs-box">
              <template x-for="(shot, index) in shots" :key="index">
                <button class="tab" :class="{'tab-active': coffeeType === shot.value}"
                  @click="coffeeType = shot.value; updateGramsOptions()" x-text="shot.label">
                </button>
              </template>
            </div>
            <div class="mt-2 w-full">
              <template x-for="gram in gramsOptions" :key="gram">
                <label class="px-2 py-1 flex items-center">
                  <input type="radio" x-model="coffeGrams" :value="gram" name="grams-of-coffee" class="hidden"
                    @change="updateAll()">
                  <span class="btn btn-wide" :class="coffeGrams == gram ? 'btn-neutral' : 'btn-outline'"
                    x-text="`${gram} 克`"></span>
                </label>
              </template>
            </div>
          </div>
        </div>
      </div>
    </li>
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">3</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">记下所需研磨咖啡粉量</h4>
          <div class="flex items-center gap-4 my-6">
            <div x-text="`${coffeGrams} 克`"></div>
          </div>
        </div>
      </div>
    </li>
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">4</span>
        <div class="block w-xl">
          <h4 class="text-base text-primary mt-1 lg:mt-2">选择萃取浓度</h4>
          <div class="mt-4 w-full">
            <template x-for="(strengthOption, index) in strengthOptions" :key="index">
              <label class="px-2 py-1 flex items-center cursor-pointer">
                <input type="radio" x-model="strength" :value="strengthOption.value" name="strength" class="hidden"
                  @change="updateAll()">
                <span class="btn btn-wide"
                  :class="{'btn-neutral': Number(strength) === strengthOption.value, 'btn-outline': Number(strength) !== strengthOption.value}"
                  x-text="strengthOption.label"></span>
              </label>
            </template>
          </div>
        </div>
      </div>
    </li>
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">5</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">记下所需咖啡液萃取量</h4>
          <div class="flex items-center gap-4 my-6">
            <div x-text="`${extractionYield} 克`"></div>
          </div>
        </div>
      </div>
    </li>
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">6</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">记下所需牛奶蒸煮量</h4>
          <div class="flex items-center gap-4 my-6">
            <div x-text="`${steamedMilk} 克`"></div>
          </div>
        </div>
      </div>
    </li>
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">7</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">饮品总量</h4>
          <div class="flex items-center gap-4 my-6">
            <div x-text="`${totalSize} 克`"></div>
          </div>
        </div>
      </div>
    </li>
    <li class="relative flex-1">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10"><span class="icon-[ph--scroll-duotone] text-lg"></span></span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">最终配方</h4>
          <div class="flex items-center gap-4 my-6">
            <div class="overflow-x-auto">
              <table class="table w-full text-lg">
                <tbody>
                  <tr>
                    <td class="opacity-70">饮品类型</td>
                    <td class="font-medium"><span x-text="typeOfBrew"></span></td>
                  </tr>
                  <tr>
                    <td class="opacity-70">浓缩份数</td>
                    <td class="font-medium"><span x-text="coffeeType"></span></td>
                  </tr>
                  <tr>
                    <td class="opacity-70">咖啡粉量</td>
                    <td class="font-medium"><span x-text="coffeGrams + ' 克'"></span></td>
                  </tr>
                  <tr>
                    <td class="opacity-70">萃取浓度</td>
                    <td class="font-medium"><span x-text="strengthLabel"></span></td>
                  </tr>
                  <tr>
                    <td class="opacity-70">浓缩液量</td>
                    <td class="font-medium"><span x-text="extractionYield + ' 克'"></span></td>
                  </tr>
                  <tr>
                    <td class="opacity-70">蒸煮奶量</td>
                    <td class="font-medium"><span x-text="steamedMilk + ' 克'"></span></td>
                  </tr>
                  <tr>
                    <td class="opacity-70">饮品总量</td>
                    <td class="font-medium"><span x-text="totalSize + ' 克'"></span></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </li>
  </ol>
</div>

{% cache 86400 'random_tools' page.id %}
  {% include "tool/partials/random_tools.html" %}
{% endcache %}

<script>
function coffeeCalculator() { return { 'drinks': [{ 'label': '拿铁', 'value': '拿铁' }, { 'label': '卡布奇诺', 'value': '卡布奇诺' }, { 'label': '澳白', 'value': '澳白' }, { 'label': '玛奇朵', 'value': '玛奇朵' }, { 'label': '可塔朵', 'value': '可塔朵' }, { 'label': '意式浓缩', 'value': '意式浓缩' }], 'shots': [{ 'label': '单份', 'value': '单份' }, { 'label': '双份', 'value': '双份' }], 'gramsOptions': [0x12, 0x13, 0x14], 'strengthOptions': [{ 'label': '更浓郁\x20(1:1.5)', 'value': 1.5 }, { 'label': '常规\x20(1:2)', 'value': 0x2 }, { 'label': '更轻盈\x20(1:2.5)', 'value': 2.5 }], 'typeOfBrew': '拿铁', 'coffeeType': '双份', 'coffeGrams': 0x12, 'strength': 0x2, 'extractionYield': 0x0, 'steamedMilk': 0x0, 'totalSize': 0x0, 'updateAll'() { this['extractionYield'] = this['coffeGrams'] * this['strength']; if (this['typeOfBrew'] === '意式浓缩') this['totalSize'] = this['extractionYield']; else { if (this['typeOfBrew'] === '玛奇朵') this['totalSize'] = 0x41; else { if (this['typeOfBrew'] === '可塔朵') this['totalSize'] = 0x5f; else { if (this['typeOfBrew'] === '澳白') this['totalSize'] = 0x8d; else { if (this['typeOfBrew'] === '拿铁') this['totalSize'] = 0x10a; else this['typeOfBrew'] === '卡布奇诺' && (this['totalSize'] = 0x8d); } } } } this['steamedMilk'] = this['totalSize'] - this['extractionYield']; const _0x34a3fe = this['strengthOptions']['find'](_0x555fc6 => _0x555fc6['value'] === Number(this['strength'])); _0x34a3fe ? this['strengthLabel'] = _0x34a3fe['label'] : this['strengthLabel'] = ''; }, 'updateGramsOptions'() { if (this['coffeeType'] === '单份') this['gramsOptions'] = [0x7, 0x8, 0x9]; else this['coffeeType'] === '双份' && (this['gramsOptions'] = [0x12, 0x13, 0x14]); this['coffeGrams'] = this['gramsOptions'][0x0], this['updateAll'](); } }; }
</script>
{% endblock %}