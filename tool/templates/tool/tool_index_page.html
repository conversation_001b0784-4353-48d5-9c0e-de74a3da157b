{% extends "base.html" %}
{% load static %}
{% load wagtailcore_tags wagtailimages_tags %}
{% block body_class %}template-toolindexpage{% endblock %}
{% block content %}

<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4 text-base-content">
    <div class="w-full lg:w-6/12 mx-auto">
        <h1 class="text-xl text-center">
            {{ page.title }}
        </h1>
        {% if page.intro %}
        <div class="text-sm text-center opacity-60">
            {{ page.intro|richtext }}
        </div>
        {% endif %}
    </div>
</div>

<div class="container mx-auto px-4 py-4 lg:px-8 lg:py-8 xl:max-w-7xl text-base-content">
    {% for category in page.categories.all %}
    {% if not forloop.first %}
        <div class="divider my-10">{{ category.name }}</div>
    {% endif %}
    <div class="flex flex-wrap gap-3">
    {% for tool in category.tools.all %}
        <div class="contents">
            <div class="relative border-2 border-base-content/5 rounded-lg basis-full xs:basis-[calc(50%-1.5rem)] mb-2 xs:mb-3 md:basis-[calc(50%-1.5rem)] lg:basis-[calc(33.33%-2rem)] transition-all duration-200 hover:shadow-sm active:shadow-sm hover:-translate-y-1 active:-translate-y-1">
            <a href="{{ tool.url }}" target="_self" class="absolute inset-0"></a>
            <div class="p-3">
                <div class="flex items-center mb-2">
                <div class="w-10 h-10 rounded-full flex items-center justify-center overflow-hidden">
                    {% if tool.image_url %}
                    <img class="h-auto w-auto" src="{{ tool.image_url }}" alt="{{ tool.title }}">
                    {% elif tool.icon_class %}
                    <span class="{{ tool.icon_class }} text-2xl {% if tool.icon_color %}{{ tool.icon_color }}{% endif %}"></span>
                    {% endif %}
                </div>
                <div class="flex flex-col flex-1 px-2">
                    <h4 class="font-semibold text-lg truncate">{{ tool.title }}</h4>
                </div>
                <div>
                    <button class="btn btn-sm btn-primary px-2 py-1 font-bold">查看</button>
                </div>
                </div>
                <p class="text-sm text-base-content/50 line-clamp-2">
                {{ tool.description|richtext }}
                </p>
            </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endfor %}
</div>
<div class="hidden">
    <span class="icon-[iconoir--percentage-circle]"></span>
    <span class="icon-[mdi--kettle-pour-over] text-blue-400"></span>
    <span class="icon-[tabler--coffee]"></span>
    <span class="icon-[openmoji--latte-macchiato]"></span>
    <span class="icon-[circum--coffee-cup]"></span>
    <span class="icon-[bi--droplet-half]"></span>
    <span class="icon-[material-symbols--conversion-path]"></span>
</div>
{% endblock %}