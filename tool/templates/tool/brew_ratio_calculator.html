{% extends "base.html" %}
{% load static wagtailcore_tags wagtailuserbar cache %}

{% block content %}
<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
      <h1 class="text-xl text-center text-base-content">
        粉水比计算器
      </h1>
      <div class="text-sm text-center text-base-content opacity-60">
        计算手冲咖啡、冷萃咖啡、法压壶、摩卡壶、美式滴滤咖啡等萃取方式的粉水比例
      </div>
    </div>
</div>

<div class="calc max-w-lg mx-auto p-6 space-y-6 text-base-content">
  <ol class="overflow-hidden space-y-4">
    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">1</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">选择萃取方式</h4>
          <div class="type-of-coffee flex flex-col gap-4 my-6">
            <div class="type-of-coffee-block"><input type="radio" name="type-of-coffee" value="1" id="type-of-coffee-1"
                class="radio" checked><label for="type-of-coffee-1" class="ml-2 cursor-pointer">自动滴滤机</label></div>
            <div class="type-of-coffee-block"><input type="radio" name="type-of-coffee" value="2" id="type-of-coffee-2"
                class="radio"><label for="type-of-coffee-2" class="ml-2 cursor-pointer">手冲滤杯(V60)</label></div>
            <div class="type-of-coffee-block"><input type="radio" name="type-of-coffee" value="3" id="type-of-coffee-3"
                class="radio"><label for="type-of-coffee-3" class="ml-2 cursor-pointer">Chemex滴滤壶</label></div>
            <div class="type-of-coffee-block"><input type="radio" name="type-of-coffee" value="4" id="type-of-coffee-4"
                class="radio"><label for="type-of-coffee-4" class="ml-2 cursor-pointer">法压壶</label></div>
            <div class="type-of-coffee-block"><input type="radio" name="type-of-coffee" value="5" id="type-of-coffee-5"
                class="radio"><label for="type-of-coffee-5" class="ml-2 cursor-pointer">爱乐压</label></div>
            <div class="type-of-coffee-block"><input type="radio" name="type-of-coffee" value="6" id="type-of-coffee-6"
                class="radio"><label for="type-of-coffee-6" class="ml-2 cursor-pointer">摩卡壶</label></div>
            <div class="type-of-coffee-block"><input type="radio" name="type-of-coffee" value="7" id="type-of-coffee-7"
                class="radio"><label for="type-of-coffee-7" class="ml-2 cursor-pointer">虹吸壶</label></div>
            <div class="type-of-coffee-block"><input type="radio" name="type-of-coffee" value="8" id="type-of-coffee-8"
                class="radio"><label for="type-of-coffee-8" class="ml-2 cursor-pointer">冷萃</label></div>
          </div>
        </div>
      </div>
    </li>

    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">2</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">选择制作量</h4>
          <div class="flex items-center gap-4 my-6">
            <select name="brewSize" class="select">
              <option value="1">1 杯</option>
              <option value="2">2 杯</option>
              <option value="3">3 杯</option>
              <option value="4">4 杯</option>
              <option value="5">5 杯</option>
              <option value="6">6 杯</option>
              <option value="7">7 杯</option>
              <option value="8">8 杯</option>
            </select>
          </div>
          <div class="stats stats-vertical lg:stats-horizontal shadow-sm">
            <div class="stat bg-base-100">
              <div class="stat-title">注水量</div>
              <div class="stat-value"><span id="waterOz" class="water-size">8</span></div>
              <div class="stat-desc">盎司(oz)</div>
            </div>
            <div class="stat bg-base-100">
              <div class="stat-title">注水量</div>
              <div class="stat-value"><span id="waterG" class="water-size">226</span></div>
              <div class="stat-desc">克(g)</div>
            </div>
            <div class="stat bg-base-100">
              <div class="stat-title">注水量</div>
              <div class="stat-value"><span id="waterMl" class="water-size">236</span></div>
              <div class="stat-desc">毫升(ml)</div>
            </div>
          </div>
        </div>
      </div>
    </li>

    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">3</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">选择萃取浓度</h4>
          <div class="strength flex flex-col gap-4 my-6">
            <div><input type="radio" name="strength" value="18.18" id="mild" class="radio"><label for="mild" class="ml-2 cursor-pointer">温和 1:<span
                  class="mild-label">18</span></label></div>
            <div><input type="radio" name="strength" value="16" id="average" class="radio" checked><label
                for="average" class="ml-2 cursor-pointer">常规
                1:<span class="average-label">16</span></label></div>
            <div><input type="radio" name="strength" value="15" id="strong" class="radio"><label for="strong" class="ml-2 cursor-pointer">强烈 1:<span
                  class="strong-label">15</span></label></div>
            <div><input type="radio" name="strength" value="13.33" id="robust" class="radio"><label for="robust"><span
                  class="con-rob ml-2 cursor-pointer">浓郁</span> 1:<span class="robust-label">13</span></label></div>
          </div>
          <div class="stats stats-vertical lg:stats-horizontal shadow-sm">
            <div class="stat bg-base-100">
              <div class="stat-title">咖啡粉量</div>
              <div class="stat-value total-ground-coffee">13.6</div>
              <div class="stat-desc">克(g)</div>
            </div>
          </div>
        </div>
      </div>
    </li>

    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">4</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">选择烘焙度</h4>
          <div class="strength flex flex-col gap-4 my-6">
            <div><input type="radio" name="roast-level" value="1" id="light-roast" class="radio"><label
                for="light-roast" class="ml-2 cursor-pointer">浅烘</label></div>
            <div><input type="radio" name="roast-level" value="2" id="medium-roast" class="radio" checked><label
                for="medium-roast" class="ml-2 cursor-pointer">中烘</label></div>
            <div><input type="radio" name="roast-level" value="3" id="dark-roast" class="radio"><label
                for="dark-roast" class="ml-2 cursor-pointer">深烘</label></div>
          </div>
        </div>
      </div>
    </li>

    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">5</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">记下所需水温</h4>
          <div class="grind-size overflow-x-auto my-6">
            <table id="wt-table" class="table">
              <tr class="wt">
                <td>浅烘</td>
                <td>92 - 96 度</td>
              </tr>
              <tr class="wt bg-base-200 dark:bg-neutral-600">
                <td>中烘</td>
                <td>90 - 93 度</td>
              </tr>
              <tr class="wt">
                <td>深烘</td>
                <td>86 - 90 度 <i>(再稍微低一些也行)</i></td>
              </tr>
            </table>

            <table id="wt-div" style="display: none">
              <tr class="bg-base-200 dark:bg-neutral-600">
                <td>冷萃: 用冷水或冰水泡，常温放置或冰箱冷藏都可以</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </li>

    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">6</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">记下所需研磨度</h4>
          <div class="grind-size overflow-x-auto my-6">
            <table class="table">
              <tr class="gs bg-base-200 dark:bg-neutral-600">
                <td>自动滴滤机</td>
                <td>中等研磨</td>
              </tr>
              <tr class="gs">
                <td>手冲滤杯</td>
                <td>中等研磨</td>
              </tr>
              <tr class="gs">
                <td>Chemex滴滤壶</td>
                <td>中等研磨</td>
              </tr>
              <tr class="gs">
                <td>法压壶</td>
                <td>粗研磨</td>
              </tr>
              <tr class="gs">
                <td>爱乐压</td>
                <td>中等研磨</td>
              </tr>
              <tr class="gs">
                <td>摩卡壶</td>
                <td>细研磨</td>
              </tr>
              <tr class="gs">
                <td>虹吸壶</td>
                <td>中等研磨</td>
              </tr>
              <tr class="gs">
                <td>冷萃</td>
                <td>粗研磨</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </li>

    <li
      class="relative flex-1 after:content-['']  after:w-0.5 after:h-full  after:bg-primary after:inline-block after:absolute after:-bottom-8 after:left-4 lg:after:left-5">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">7</span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">选择萃取时间</h4>
          <div class="brew-length flex flex-col gap-4 my-6">
            <div><input type="radio" name="brew-length" value="1" id="fast" class="radio"><label for="fast" class="ml-2 cursor-pointer">快
              (轻盈)</label></div>
            <div><input type="radio" name="brew-length" value="2" id="balanced" class="radio" checked><label for="balanced" class="ml-2 cursor-pointer">常规 (平衡)</label></div>
            <div><input type="radio" name="brew-length" value="3" id="slow" class="radio"><label for="slow" class="ml-2 cursor-pointer">慢
              (浓郁)</label></div>
          </div>
          <div class="stats stats-vertical lg:stats-horizontal shadow-sm">
            <div class="stat bg-base-100">
              <div class="stat-title">萃取时长</div>
              <div class="stat-value brew-length-coffee">3 分钟</div>
            </div>
          </div>
        </div>
      </div>
    </li>

    <li class="relative flex-1">
      <div class="flex items-start font-medium w-full">
        <span
          class="w-8 h-8 aspect-square bg-primary border-2 border-transparent rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10"><span
            class="icon-[ph--scroll-duotone] text-lg"></span></span>
        <div class="block">
          <h4 class="text-base text-primary mt-1 lg:mt-2">最终配方</h4>
          <div class="overflow-x-auto max-w-xs my-6">
            <table class="table text-lg">
              <tbody>
                <tr>
                  <td class="opacity-70 min-w-[8rem]">设备</td>
                  <td class="font-medium"><span class="final-brew-device">自动滴滤机</span></td>
                </tr>
                <tr>
                  <td class="opacity-70">水量</td>
                  <td class="font-medium"><span class="final-total-water-cup">1
                      杯</span>, <span class="final-total-water-oz">8
                      盎司</span>, <span class="final-total-water-g">227 克</span>, <span class="final-total-water-ml">237 毫升</span>
                  </td>
                </tr>
                <tr>
                  <td class="opacity-70">水温</td>
                  <td class="font-medium"><span class="final-water-temperature">90 -
                      93 度</span></td>
                </tr>
                <tr>
                  <td class="opacity-70">粉水比</td>
                  <td class="font-medium"><span class="final-brew-ratio">1:17</span></td>
                </tr>
                <tr>
                  <td class="opacity-70">咖啡粉</td>
                  <td class="font-medium"><span class="final-ground-coffee">13.61 克</span></td>
                </tr>
                <tr>
                  <td class="opacity-70">萃取时长</td>
                  <td class="font-medium"><span class="final-brew-length">5 分钟</span></td>
                </tr>
                <tr id="cold-brew-final" style="display: none">
                  <td class="opacity-70">冷萃浓缩液</td>
                  <td class="font-medium"><span id="cbf-text"></span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </li>
</ol>
</div>
<script src="https://lib.baomitu.com/jquery/2.0.0/jquery.min.js"></script>
<script>
  var coffeeType = 0x1, brewSize = 0x1, waterOz = 0x8, waterG = 226.72, waterMl = 236.584, strength = 16.66, groundCoffee = 27.2, brewLength = 0x2; $(document)['on']('change', 'input[name=type-of-coffee]', function () { ChangeType(), ChangeSize(), ChangeStrength(), ChangeBrewLength(), RoastLevel(); }), $(document)['on']('change', '[name=brewSize]', function () { ChangeSize(), ChangeStrength(), RoastLevel(); }), $(document)['on']('change', 'input[name=strength]', function () { ChangeStrength(); }), $(document)['on']('change', 'input[name=brew-length]', function () { ChangeBrewLength(); }); function ChangeType() { coffeeType = $('input[name=type-of-coffee]:checked')['val'](), $('.gs')['removeClass']('bg-base-200\x20dark:bg-neutral-600'), $('[name=brewSize]')['empty'](); if (coffeeType == 0x1) $('[name=brewSize]')['append']('<option\x20value=\x221\x22>1\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x222\x22>2\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x223\x22>3\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x224\x22>4\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x225\x22>5\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x226\x22>6\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x227\x22>7\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x228\x22>8\x20杯</option>'), $('.average-label')['text'](0x10), $('#average')['val'](0x10), $('.mild-label')['text'](0x12), $('#mild')['val'](0x12), $('.robust-label')['text'](0xd), $('#robust')['val'](0xd), $('.strong-label')['text'](0xf), $('#strong')['val'](0xf), $('.con-rob')['text']('浓郁'), $('.final-brew-device')['text']('自动滴滤机'); else { if (coffeeType == 0x2) $('[name=brewSize]')['append']('<option\x20value=\x221\x22>1\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x221.5\x22>1.5\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x222\x22>2\x20杯</option>'), $('.average-label')['text'](0x10), $('#average')['val'](0x10), $('.mild-label')['text'](0x12), $('#mild')['val'](0x12), $('.robust-label')['text'](0xd), $('#robust')['val'](0xd), $('.strong-label')['text'](0xf), $('#strong')['val'](0xf), $('.con-rob')['text']('浓郁'), $('.final-brew-device')['text']('手冲滤杯(V60)'); else { if (coffeeType == 0x3) $('[name=brewSize]')['append']('<option\x20value=\x222\x22>3\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x223.125\x22>5\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x223.75\x22>6\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x225\x22>8\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x226.25\x22>10\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x228.125\x22>13\x20杯</option>'), $('.average-label')['text'](0x10), $('#average')['val'](0x10), $('.mild-label')['text'](0x12), $('#mild')['val'](0x12), $('.robust-label')['text'](0xd), $('#robust')['val'](0xd), $('.strong-label')['text'](0xf), $('#strong')['val'](0xf), $('.con-rob')['text']('浓郁'), $('.final-brew-device')['text']('Chemex滴滤壶'); else { if (coffeeType == 0x4) $('[name=brewSize]')['append']('<option\x20value=\x221\x22>1\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x221.5\x22>1.5\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x222\x22>2\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x223\x22>3\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x224.5\x22>4.5\x20杯</option>'), $('.average-label')['text'](0x10), $('#average')['val'](0x10), $('.mild-label')['text'](0x12), $('#mild')['val'](0x12), $('.robust-label')['text'](0xc), $('#robust')['val'](0xc), $('.strong-label')['text'](0xe), $('#strong')['val'](0xe), $('.con-rob')['text']('浓郁'), $('.final-brew-device')['text']('法压壶'); else { if (coffeeType == 0x5) $('[name=brewSize]')['append']('<option\x20value=\x220.5\x22>0.5\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x220.75\x22>0.75\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x221\x22>1\x20杯</option>'), $('.average-label')['text'](0xe), $('#average')['val'](0xe), $('.mild-label')['text'](0x11), $('#mild')['val'](0x11), $('.robust-label')['text'](0xc), $('#robust')['val'](0xc), $('.strong-label')['text'](0xd), $('#strong')['val'](0xd), $('.con-rob')['text']('浓郁'), $('.final-brew-device')['text']('爱乐压'); else { if (coffeeType == 0x6) $('[name=brewSize]')['append']('<option\x20value=\x220.25\x22>1\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x220.81\x22>3\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x221.25\x22>6\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x222.31\x22>9\x20杯</option>'), $('.average-label')['text'](0x8), $('#average')['val'](0x8), $('.mild-label')['text'](0x9), $('#mild')['val'](0x9), $('.robust-label')['text'](0x6), $('#robust')['val'](0x6), $('.strong-label')['text'](0x7), $('#strong')['val'](0x7), $('.con-rob')['text']('浓郁'), $('.final-brew-device')['text']('摩卡壶'); else { if (coffeeType == 0x7) $('[name=brewSize]')['append']('<option\x20value=\x221\x22>1\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x221.5\x22>1.5\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x222.5\x22>2.5\x20杯</option>'), $('.average-label')['text'](0xe), $('#average')['val'](0xe), $('.mild-label')['text'](0x10), $('#mild')['val'](0x10), $('.robust-label')['text'](0xb), $('#robust')['val'](0xb), $('.strong-label')['text'](0xc), $('#strong')['val'](0xc), $('.con-rob')['text']('浓郁'), $('.final-brew-device')['text']('虹吸壶'); else coffeeType == 0x8 && ($('[name=brewSize]')['append']('<option\x20value=\x222\x22>2\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x224\x22>4\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x228\x22>8\x20杯</option>'), $('[name=brewSize]')['append']('<option\x20value=\x2220\x22>2.5\x20加仑</option>'), $('[name=brewSize]')['append']('<option\x20value=\x2240\x22>5.0\x20加仑</option>'), $('.average-label')['text'](0xe), $('#average')['val'](0xe), $('.mild-label')['text'](0x10), $('#mild')['val'](0x10), $('.robust-label')['text'](0x4), $('#robust')['val'](0x4), $('.strong-label')['text'](0xb), $('#strong')['val'](0xb), $('.con-rob')['text']('浓缩液'), $('.final-brew-device')['text']('冷萃')); } } } } } } var _0x1b112a = coffeeType - 0x1; $('.gs:eq(' + _0x1b112a + ')')['addClass']('bg-base-200\x20dark:bg-neutral-600'); } function ChangeSize() { brewSize = $('[name=brewSize]')['val']() * 0x1, waterOz = 0x8 * brewSize, waterG = waterOz * 28.34, waterMl = waterOz * 29.573, $('#waterOz')['text'](waterOz['toFixed'](0x0)), $('#waterMl')['text'](waterMl['toFixed'](0x0)), $('#waterG')['text'](waterG['toFixed'](0x0)), $('.final-total-water-cup')['text'](brewSize + '\x20杯'), $('.final-total-water-oz')['text'](waterOz['toFixed'](0x0) + '\x20盎司'), $('.final-total-water-g')['text'](waterG['toFixed'](0x0) + '\x20克'), $('.final-total-water-ml')['text'](waterMl['toFixed'](0x0) + '\x20毫升'); coffeeType == 0x8 ? ($('#wt-table')['hide'](), $('#wt-div')['show']()) : ($('#wt-table')['show'](), $('#wt-div')['hide']()); coffeeType == 0x8 && brewSize > 0xa ? ($('input[name=\x22strength\x22][value=\x224\x22]')['trigger']('click'), $('#mild')['prop']('disabled', !![]), $('#average')['prop']('disabled', !![]), $('#strong')['prop']('disabled', !![])) : ($('#mild')['prop']('disabled', ![]), $('#average')['prop']('disabled', ![]), $('#strong')['prop']('disabled', ![])); if (coffeeType == 0x8 && brewSize > 0xa) { if (brewSize == 0x14) $('#cbf-text')['text']('最后加入\x20160\x20盎司的水，制成\x202.5\x20加仑的饮品。'); else brewSize == 0x28 ? $('#cbf-text')['text']('最后加入\x20320\x20盎司的水，制成\x205\x20加仑的饮品。') : $('#cold-brew-final')['hide'](); $('#cold-brew-final')['show'](); } else $('#cold-brew-final')['hide'](); } function ChangeStrength() { strength = $('input[name=strength]:checked')['val'](), groundCoffee = waterG['toFixed'](0x0) / strength, $('.total-ground-coffee')['html'](groundCoffee['toFixed'](0x2)), $('.final-brew-ratio')['text']('1:' + Math['round'](strength)), $('.final-ground-coffee')['text'](groundCoffee['toFixed'](0x2) + '\x20克'); } function ChangeBrewLength() { brewLength = $('input[name=brew-length]:checked')['val'](); var _0x27f558 = ''; brewLength == 0x1 && ((coffeeType == 0x1 || coffeeType == 0x3 || coffeeType == 0x4) && (_0x27f558 = '4\x20分钟'), coffeeType == 0x2 && (_0x27f558 = '2\x20分\x2030\x20秒'), coffeeType == 0x5 && (_0x27f558 = '1\x20分钟'), coffeeType == 0x6 && (_0x27f558 = '3\x20分钟'), coffeeType == 0x7 && (_0x27f558 = '1\x20分\x2030\x20秒'), coffeeType == 0x8 && (_0x27f558 = '16\x20小时')), brewLength == 0x2 && ((coffeeType == 0x1 || coffeeType == 0x3 || coffeeType == 0x4) && (_0x27f558 = '5\x20分钟'), coffeeType == 0x2 && (_0x27f558 = '3\x20分钟'), coffeeType == 0x5 && (_0x27f558 = '1\x20分\x2030\x20秒'), coffeeType == 0x6 && (_0x27f558 = '6\x20分钟'), coffeeType == 0x7 && (_0x27f558 = '2\x20分钟'), coffeeType == 0x8 && (_0x27f558 = '20\x20小时')), brewLength == 0x3 && ((coffeeType == 0x1 || coffeeType == 0x3 || coffeeType == 0x4) && (_0x27f558 = '6\x20分钟'), coffeeType == 0x2 && (_0x27f558 = '4\x20分钟'), coffeeType == 0x5 && (_0x27f558 = '2\x20分钟'), coffeeType == 0x6 && (_0x27f558 = '8\x20分钟'), coffeeType == 0x7 && (_0x27f558 = '2\x20分\x2015\x20秒'), coffeeType == 0x8 && (_0x27f558 = '24\x20小时')), $('.brew-length-coffee')['text'](_0x27f558), $('.final-brew-length')['text'](_0x27f558); } function RoastLevel() { $('.wt')['removeClass']('bg-base-200\x20dark:bg-neutral-600'); var _0x26d312 = $('input[name=roast-level]:checked')['val'](), _0x519f3b = _0x26d312 - 0x1; $('.wt:eq(' + _0x519f3b + ')')['addClass']('bg-base-200\x20dark:bg-neutral-600'); if (coffeeType == 0x8) $('.final-water-temperature')['text']('用冷水或冰水泡，常温放置或冰箱冷藏都可以'); else { if (_0x26d312 == 0x1) $('.final-water-temperature')['text']('92\x20-\x2096\x20度'); else _0x26d312 == 0x2 ? $('.final-water-temperature')['text']('90\x20-\x2093\x20度') : $('.final-water-temperature')['text']('86\x20-\x2090\x20度\x20(再稍微低一些也行)'); } } $(document)['on']('change', 'input[name=roast-level]:checked', function () { RoastLevel(); });
</script>
  {% cache 86400 'random_tools' page.id %}
    {% include "tool/partials/random_tools.html" %}
  {% endcache %}
{% endblock %}