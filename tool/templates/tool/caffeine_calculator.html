{% extends "base.html" %}
{% load static wagtailcore_tags wagtailuserbar cache %}

{% block content %}
<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
      <h1 class="text-xl text-center text-base-content">
        咖啡因计算器
      </h1>
      <div class="text-sm text-center text-base-content opacity-60">
        记下你一天所摄入的饮食，轻松计算你的咖啡因摄入量。
      </div>
      <div class="text-xs text-center text-base-content opacity-30 mt-4">
        <em>所有基础数值源自美国农业部的食品数据中心(USDA Food Data Central Database)。</em>
      </div>
    </div>
</div>

<div class="flex flex-col md:flex-row lg:flex-row text-base-content" x-data="caffeineCalculator()">
  <div class="container mx-auto w-auto md:w-1/2 lg:w-1/2">
    <div class="grid justify-center mb-2">
      <h2 class="text-2xl font-bold text-primary my-4">你今天摄入了多少咖啡因？</h2>
      <fieldset class="fieldset max-w-xs">
        <label class="label flex justify-between" for="weight">
          <span class="text-sm text-base-content font-medium">请输入你的体重</span>
          <span>（单位：kg）</span>
        </label>
        <input type="number" id="weight" name="weight" x-model="weight" class="input w-full max-w-xs" placeholder="例如：60" min="0" step="0.01" required>
      </fieldset>
      <p class="text-sm font-medium my-4">请选择你今天摄入的饮食和数量：</p>
      <div class="overflow-x-auto">
        <ul class="list bg-base-100">
          <!-- 滴滤咖啡 -->
          <li class="list-row">
            <div>☕ 滴滤咖啡</div>
            <div class=" opacity-60">240g/杯</div>
            <div class="flex items-center gap-2">
              <button type="button" id="decrement-button" data-input-counter-decrement="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('滴滤咖啡', 96, 240, -1)" 
                x-bind:disabled="getServings('滴滤咖啡') === 0 ? true : null">
                <span class="icon-[ph--minus]"></span>
              </button>
              <span x-text="getServings('滴滤咖啡') + ' 杯'"></span>
              <button type="button" id="increment-button" data-input-counter-increment="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('滴滤咖啡', 96, 240, 1)">
                <span class="icon-[ph--plus]"></span>
              </button>
            </div>
          </li>

          <!-- 意式浓缩 -->
          <li class="list-row">
            <div>☕ 意式浓缩</div>
            <div class=" opacity-60">单份</div>
            <div class="flex items-center gap-2">
              <button type="button" id="decrement-button" data-input-counter-decrement="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('意式浓缩', 63.6, 30, -1)" 
                x-bind:disabled="getServings('意式浓缩') === 0 ? true : null">
                <span class="icon-[ph--minus]"></span>
              </button>
              <span x-text="getServings('意式浓缩') + ' 份'"></span>
              <button type="button" id="increment-button" data-input-counter-increment="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('意式浓缩', 63.6, 30, 1)">
                <span class="icon-[ph--plus]"></span>
              </button>
            </div>
          </li>

          <!-- 低因咖啡 -->
          <li class="list-row">
            <div>☕ 低因咖啡</div>
            <div class=" opacity-60">240g/杯</div>
            <div class="flex items-center gap-2">
              <button type="button" id="decrement-button" data-input-counter-decrement="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('低因咖啡', 2.4, 240, -1)" 
                x-bind:disabled="getServings('低因咖啡') === 0 ? true : null">
                <span class="icon-[ph--minus]"></span>
              </button>
              <span x-text="getServings('低因咖啡') + ' 杯'"></span>
              <button type="button" id="increment-button" data-input-counter-increment="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('低因咖啡', 2.4, 240, 1)">
                <span class="icon-[ph--plus]"></span>
              </button>
            </div>
          </li>

          <!-- 速溶咖啡 -->
          <li class="list-row">
            <div>☕ 速溶咖啡粉</div>
            <div class=" opacity-60">2g/份</div>
            <div class="flex items-center gap-2">
              <button type="button" id="decrement-button" data-input-counter-decrement="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('速溶咖啡', 62.8, 240, -1)" 
                x-bind:disabled="getServings('速溶咖啡') === 0 ? true : null">
                <span class="icon-[ph--minus]"></span>
              </button>
              <span x-text="getServings('速溶咖啡') + ' 份'"></span>
              <button type="button" id="increment-button" data-input-counter-increment="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('速溶咖啡', 62.8, 240, 1)">
                <span class="icon-[ph--plus]"></span>
              </button>
            </div>
          </li>

          <!-- 红茶 -->
          <li class="list-row">
            <div>🫖 红茶</div>
            <div class=" opacity-60">240g/杯</div>
            <div class="flex items-center gap-2">
              <button type="button" id="decrement-button" data-input-counter-decrement="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('红茶', 48, 240, -1)" 
                x-bind:disabled="getServings('红茶') === 0 ? true : null">
                <span class="icon-[ph--minus]"></span>
              </button>
              <span x-text="getServings('红茶') + ' 杯'"></span>
              <button type="button" id="increment-button" data-input-counter-increment="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('红茶', 48, 240, 1)">
                <span class="icon-[ph--plus]"></span>
              </button>
            </div>
          </li>

          <!-- 绿茶 -->
          <li class="list-row">
            <div>🍵 绿茶</div>
            <div class=" opacity-60">240g/杯</div>
            <div class="flex items-center gap-2">
              <button type="button" id="decrement-button" data-input-counter-decrement="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('绿茶', 28.8, 240, -1)" 
                x-bind:disabled="getServings('绿茶') === 0 ? true : null">
                <span class="icon-[ph--minus]"></span>
              </button>
              <span x-text="getServings('绿茶') + ' 杯'"></span>
              <button type="button" id="increment-button" data-input-counter-increment="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('绿茶', 28.8, 240, 1)">
                <span class="icon-[ph--plus]"></span>
              </button>
            </div>
          </li>

          <!-- 汽水 -->
          <li class="list-row">
            <div>🥤 汽水</div>
            <div class=" opacity-60">370g/瓶</div>
            <div class="flex items-center gap-2">
              <button type="button" id="decrement-button" data-input-counter-decrement="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('汽水', 33.5, 370, -1)" 
                x-bind:disabled="getServings('汽水') === 0 ? true : null">
                <span class="icon-[ph--minus]"></span>
              </button>
              <span x-text="getServings('汽水') + ' 瓶'"></span>
              <button type="button" id="increment-button" data-input-counter-increment="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('汽水', 33.5, 370, 1)">
                <span class="icon-[ph--plus]"></span>
              </button>
            </div>
          </li>

          <!-- 能量饮料 -->
          <li class="list-row">
            <div>⚡️ 能量饮料</div>
            <div class=" opacity-60">240g/瓶</div>
            <div class="flex items-center gap-2">
              <button type="button" id="decrement-button" data-input-counter-decrement="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('能量饮料', 73.5, 240, -1)" 
                x-bind:disabled="getServings('能量饮料') === 0 ? true : null">
                <span class="icon-[ph--minus]"></span>
              </button>
              <span x-text="getServings('能量饮料') + ' 瓶'"></span>
              <button type="button" id="increment-button" data-input-counter-increment="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('能量饮料', 73.5, 240, 1)">
                <span class="icon-[ph--plus]"></span>
              </button>
            </div>
          </li>

          <!-- 能量弹 -->
          <li class="list-row">
            <div>⚡️ 能量弹</div>
            <div class=" opacity-60">60g/瓶</div>
            <div class="flex items-center gap-2">
              <button type="button" id="decrement-button" data-input-counter-decrement="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('能量弹', 200, 60, -1)" 
                x-bind:disabled="getServings('能量弹') === 0 ? true : null">
                <span class="icon-[ph--minus]"></span>
              </button>
              <span x-text="getServings('能量弹') + ' 瓶'"></span>
              <button type="button" id="increment-button" data-input-counter-increment="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('能量弹', 200, 60, 1)">
                <span class="icon-[ph--plus]"></span>
              </button>
            </div>
          </li>

          <!-- 黑巧克力 -->
          <li class="list-row">
            <div>🍫 黑巧克力</div>
            <div class=" opacity-60">28g/份</div>
            <div class="flex items-center gap-2">
              <button type="button" id="decrement-button" data-input-counter-decrement="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('黑巧克力', 15.9, 28, -1)" 
                x-bind:disabled="getServings('黑巧克力') === 0 ? true : null">
                <span class="icon-[ph--minus]"></span>
              </button>
              <span x-text="getServings('黑巧克力') + ' 份'"></span>
              <button type="button" id="increment-button" data-input-counter-increment="counter-input"
                class="btn btn-circle btn-ghost btn-xs"
                x-on:click="addOrModifyDrink('黑巧克力', 15.9, 28, 1)">
                <span class="icon-[ph--plus]"></span>
              </button>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="container mx-auto w-auto md:w-1/2 lg:w-1/2" x-bind:class="{ 'hidden': !result }">
    <div class="mx-10" x-show="result">
      <h2 class="text-2xl font-bold text-primary my-4">你的咖啡因摄入量</h2>
      <!-- 总摄入量 -->
      <div class="mb-4">
        <p class="opacity-60 text-sm">总摄入量:</p>
        <p class="text-lg font-medium" x-text="getTotalCaffeine() + ' 毫克(mg)'"></p>
      </div>
      <!-- 咖啡因等级 -->
      <div class="mb-4">
        <p class="opacity-60 text-sm">你的咖啡因等级是:</p>
        <p class="text-lg font-medium" x-ref="level"></p>
      </div>
      <!-- 咖啡因影响 -->
      <div class="mb-4">
        <p class="opacity-60 text-sm">咖啡因对你的健康可能有以下影响:</p>
        <p class="text-lg" x-ref="effect"></p>
      </div>
      <!-- 咖啡因建议 -->
      <div class="mb-4">
        <p class="opacity-60 text-sm">根据你的咖啡因摄入量，我们给你以下建议:</p>
        <p class="text-lg" x-ref="advice"></p>
      </div>
      <!-- 重置按钮 -->
      <div class="mb-4">
        <button type="button" class="btn btn-wide btn-primary" x-on:click="reset()">重新计算</button>
      </div>
    </div>
  </div>
</div>

{% cache 86400 'caffeine_calc_faq' %}
  {% include "tool/partials/caffeine_calc_faq.html" %}
{% endcache %}

{% cache 86400 'random_tools' page.id %}
  {% include "tool/partials/random_tools.html" %}
{% endcache %}

<script>
  function caffeineCalculator(){let _0x2d8eb9=localStorage['getItem']('weight'),_0x342093=_0x2d8eb9?parseFloat(_0x2d8eb9):0x3c;return{'weight':_0x342093,'drinks':[],'result':![],'level':'','effect':'','advice':'','addOrModifyDrink'(_0x311a53,_0x22b585,_0x3111a6,_0x52cbae){localStorage['setItem']('weight',this['weight']['toString']());const _0x427cfd=this['findDrinkIndex'](_0x311a53);if(_0x427cfd!==-0x1){const _0x2621fd=this['drinks'][_0x427cfd]['servings'],_0x4c0a13=Math['max'](0x0,_0x2621fd+_0x52cbae);this['drinks'][_0x427cfd]={...this['drinks'][_0x427cfd],'servings':_0x4c0a13,'totalCaffeine':_0x4c0a13*_0x22b585};}else this['drinks']['push']({'name':_0x311a53,'servings':0x1,'caffeinePerServing':_0x22b585,'totalCaffeine':_0x22b585});this['result']=!![],this['updateResult']();},'findDrinkIndex'(_0x135207){return this['drinks']['findIndex'](_0x25647b=>_0x25647b['name']===_0x135207);},'getServings'(_0x1064f5){const _0x31cd9f=this['findDrinkIndex'](_0x1064f5);return _0x31cd9f!==-0x1?parseInt(this['drinks'][_0x31cd9f]['servings']):0x0;},'reset'(){this['drinks']=[],this['result']=![],window['scrollTo'](0x0,0x0);},'getTotalCaffeine'(){const _0x4002f8=this['drinks']['reduce']((_0x1f84bd,_0x59bdf4)=>_0x1f84bd+_0x59bdf4['totalCaffeine'],0x0);return Math['round'](_0x4002f8*0x64)/0x64;},'calculateLevel'(){const _0x1076bf=Math['floor'](this['weight'])*5.7;if(this['getTotalCaffeine']()<_0x1076bf/0x3)return'低';else{if(this['getTotalCaffeine']()<_0x1076bf/0x3*0x2)return'中';else return this['getTotalCaffeine']()<=_0x1076bf?'高':'过高';}},'getEffect'(_0x22ae3a){switch(_0x22ae3a){case'低':return'提高警觉度和注意力，增强记忆和学习能力，改善心情和减轻疲劳。';case'中':return'加速新陈代谢和燃烧脂肪，增强运动表现和耐力，降低患糖尿病和帕金森病的风险。';case'高':return'引起焦虑和紧张，导致失眠和头痛，增加心率和血压。';case'过高':return'引发恶心和呕吐，导致心律不齐和胸痛，增加患骨质疏松症和胃溃疡的风险。';default:return'';}},'getAdvice'(_0x5ed7f5){switch(_0x5ed7f5){case'低':return'你的咖啡因摄入量是安全的，你可以继续享受你喜欢的饮品。你可以适当增加一些咖啡因，以获得更多的健康益处。你应该注意你的咖啡因来源，选择更天然和健康的选项，如茶和巧克力。';case'中':return'你的咖啡因摄入量是适度的，你可以保持你的饮食习惯。你可以根据你的身体状况和反应，适当调整你的咖啡因摄入量。你应该避免在晚上摄入过多的咖啡因，以免影响你的睡眠质量。';case'高':return'你的咖啡因摄入量是较高的，你应该减少你的咖啡因摄入量。你可以尝试用一些不含咖啡因或含咖啡因较少的饮品替代你的咖啡因来源，如水、果汁或脱咖啡因的咖啡。你应该注意你的咖啡因摄入量对你的身心健康的影响，如果出现任何不适，你应该及时就医。';case'过高':return'你的咖啡因摄入量是危险的，你必须立即停止你的咖啡因摄入。你可以寻求专业的医疗帮助，以治疗你的咖啡因中毒症状。你应该改变你的饮食习惯，避免任何含有咖啡因的食物和饮料，直到你的身体恢复正常。';default:return'';}},'updateResult'(){const _0x510d06=this['calculateLevel']();this['result']=!![];const _0x26671b=document['querySelector']('[x-ref=\x22level\x22]'),_0x791890=document['querySelector']('[x-ref=\x22effect\x22]'),_0x41c58e=document['querySelector']('[x-ref=\x22advice\x22]');_0x26671b&&(_0x26671b['innerText']=_0x510d06),_0x791890&&(_0x791890['innerText']=this['getEffect'](_0x510d06)),_0x41c58e&&(_0x41c58e['innerText']=this['getAdvice'](_0x510d06));},'nextTick'(_0x1c19a8){setTimeout(_0x1c19a8,0x0);}};}const calculator=caffeineCalculator();calculator['updateResult']();
</script>
{% endblock %}