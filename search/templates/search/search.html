{% extends "base.html" %}
{% load static wagtailcore_tags %}

{% block body_class %}template-searchresults{% endblock %}

{% block title %}站内搜索{% endblock %}

{% block content %}
<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
        <h1 class="text-xl text-center text-base-content">
            搜索咖啡搭子
        </h1>
    </div>
</div>

<div class="container mx-auto xl:max-w-7xl text-base-content">
<!-- SearchBox -->
    <div class="flex justify-center items-center px-4 py-4 lg:px-8 lg:py-8">
        <form action="{% url 'search' %}" method="get" class="max-w-[480px] w-full px-4">
            <div class="relative">
                <input type="text" name="query" {% if search_query %} value="{{ search_query }}" {% endif %}
                    class="w-full border border-accent h-12 shadow-sm p-4 rounded-full bg-base-100" placeholder="搜索咖啡搭子...">
                <button type="submit">
                    <span class="icon-[bi--search] text-accent-content h-5 w-5 absolute top-3.5 right-3 fill-current dark:text-base-content dark:fill-current"></span>
                </button>
            </div>
        </form>
    </div>
<!-- End SearchBox -->

<!-- SearchResult -->
{% if search_results %}
{% if search_results.paginator.num_pages > 1 %}
<span class="text-sm opacity-70 mx-8">第{{ search_results.number }}页搜索结果:</span>
{% endif %}
    <ul class="flex flex-col gap-8 py-8">
        {% for result in search_results %}
        <li class="mx-8">
            <a class="group relative flex items-center justify-between overflow-hidden rounded-box p-4 duration-300 hover:bg-base-200"
                href="{% pageurl result %}" target="_blank" rel="noopener noreferrer">
                <div>
                    <h2 class="text-xl font-bold group-hover:underline">{{ result }}</h2>
                    {% if result.search_description %}
                    <p class="text-sm font-bold text-base-content/60">{{ result.search_description }}</p>
                    {% endif %}
                </div>
            </a>
        </li>
        {% endfor %}
    </ul>
<!-- EndSearchResult -->

<!-- Pagination -->
    <div class="flex justify-center gap-8 mx-8 mb-8">
        {% if search_results.has_previous %}
        <a href="{% url 'search' %}?query={{ search_query|urlencode }}&amp;page={{ search_results.previous_page_number }}"
            class="btn">
            上一页
        </a>
        {% endif %}
        {% if search_results.has_next %}
        <a href="{% url 'search' %}?query={{ search_query|urlencode }}&amp;page={{ search_results.next_page_number }}"
            class="btn">
            下一页
        </a>
        {% endif %}
    </div>
<!-- End Pagination -->

<!-- NoResult -->
{% elif search_query %}
{% if search_query|length > 9 %}
没有搜索结果，请尝试输入更短的关键字以扩大搜索范围。
{% else %}
没有搜索结果，请尝试输入相似的关键字以扩大搜索范围。
{% endif %}
{% endif %}
<!-- End NoResult -->
</div>
{% endblock %}