{% load static wagtailcore_tags %}

<div class="container mx-auto p-4 bg-base-100 shadow-inner rounded-b-lg text-base-content">
    {% if search_results %}
    <p class="text-sm opacity-60">相关搜索结果：</p>
    {% for result in search_results %}
    <a href="{% pageurl result %}" class="p-3 rounded-lg hover:bg-base-300 block">
        <p class="flex items-center font-medium">▶︎ {{ result }}</p>
    </a>
    {% endfor %}
    <div class="flex mt-4 justify-center">
        {% if search_results.has_next %}
        <a href="{% url 'search' %}?query={{ search_query|urlencode }}&amp;page={{ search_results.next_page_number }}"
            class="btn btn-wide">
            查看更多结果
        </a>
        {% endif %}
    </div>
    {% elif search_query %}
    {% if search_query|length > 9 %}
    没有搜索结果，请尝试输入更短的关键字以扩大搜索范围。
    {% else %}
    没有搜索结果，请尝试输入相似的关键字以扩大搜索范围。
    {% endif %}
    {% endif %}
</div>