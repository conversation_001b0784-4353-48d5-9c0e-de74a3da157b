from django.core.paginator import EmptyPage, PageNotAnI<PERSON>ger, Paginator
from django.template.response import TemplateResponse
from urllib.parse import unquote

from wagtail.models import Page
from wagtail.contrib.search_promotions.models import Query

def search(request):
    search_query_encoded = request.GET.get("query", None)
    page = request.GET.get("page", 1)

    search_query = unquote(search_query_encoded) if search_query_encoded else None

    # Search
    if search_query:
        search_results = Page.objects.live().search(search_query)
        query = Query.get(search_query)

        # Record hit
        query.add_hit()
    else:
        search_results = Page.objects.none()

    # Pagination
    paginator = Paginator(search_results, 10)
    try:
        search_results = paginator.page(page)
    except PageNotAnInteger:
        search_results = paginator.page(1)
    except EmptyPage:
        search_results = paginator.page(paginator.num_pages)

    if "HX-Request" in request.headers:
        template = "search/live_search.html"
    else:
        template = "search/search.html"

    return TemplateResponse(
        request,
        template,
        {
            "search_query": search_query,
            "search_results": search_results,
        },
    )