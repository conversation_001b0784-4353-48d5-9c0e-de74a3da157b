# Generated by Django 4.2.20 on 2025-03-30 20:04

import datetime
from django.db import migrations, models
import django.db.models.deletion
import modelcluster.contrib.taggit
import modelcluster.fields
import wagtail.fields
import wagtailmarkdown.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('taggit', '0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx'),
        ('wagtailcore', '0094_alter_page_locale'),
    ]

    operations = [
        migrations.CreateModel(
            name='ArticleIndexPage',
            fields=[
                ('page_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='wagtailcore.page')),
                ('intro', wagtailmarkdown.fields.MarkdownField(blank=True)),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
        migrations.CreateModel(
            name='ArticlePage',
            fields=[
                ('page_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='wagtailcore.page')),
                ('date', models.DateTimeField(blank=True, default=datetime.datetime.today, null=True, verbose_name='发布日期')),
                ('intro', models.CharField(blank=True, max_length=250, null=True, verbose_name='导读（目前无用）')),
                ('body', wagtailmarkdown.fields.MarkdownField(blank=True, null=True, verbose_name='正文内容')),
                ('external_image', models.URLField(blank=True, null=True, verbose_name='外链图标')),
                ('author', models.CharField(blank=True, default='Anon', max_length=50, null=True, verbose_name='作者')),
                ('from_media', models.CharField(blank=True, max_length=50, null=True, verbose_name='来自媒体')),
                ('original_link', models.URLField(blank=True, null=True, verbose_name='原文链接')),
                ('video_embed_code', models.TextField(blank=True, null=True, verbose_name='视频嵌入代码')),
                ('media_links', wagtail.fields.StreamField([('link', 3)], blank=True, block_lookup={0: ('wagtail.blocks.CharBlock', (), {'help_text': '链接标题', 'max_length': 255, 'required': True}), 1: ('wagtail.blocks.URLBlock', (), {'help_text': '链接地址', 'required': True}), 2: ('wagtail.blocks.CharBlock', (), {'help_text': '媒体名称', 'max_length': 50, 'required': True}), 3: ('wagtail.blocks.StructBlock', [[('title', 0), ('url', 1), ('media_name', 2)]], {'icon': 'link'})}, null=True)),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
        migrations.CreateModel(
            name='ArticleTagIndexPage',
            fields=[
                ('page_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='wagtailcore.page')),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
        migrations.CreateModel(
            name='ArticlePageTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_object', modelcluster.fields.ParentalKey(on_delete=django.db.models.deletion.CASCADE, related_name='tagged_items', to='article.articlepage')),
                ('tag', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_items', to='taggit.tag')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='articlepage',
            name='tags',
            field=modelcluster.contrib.taggit.ClusterTaggableManager(blank=True, help_text='A comma-separated list of tags.', through='article.ArticlePageTag', to='taggit.Tag', verbose_name='文章标签'),
        ),
    ]
