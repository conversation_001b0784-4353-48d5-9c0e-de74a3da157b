from django.db import models
from django.db.models import Count
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from modelcluster.fields import Parental<PERSON>ey
from modelcluster.contrib.taggit import ClusterTaggableManager
from taggit.models import Tag, TaggedItemBase
from wagtail.models import Page
from wagtail.admin.panels import FieldPanel, MultiFieldPanel
from wagtail.search import index
from wagtailmarkdown.fields import MarkdownField
from datetime import datetime
import re
from wagtail.fields import StreamField
from wagtail import blocks
from core.cache import clear_article_cache
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

class ArticleIndexPage(Page):
    intro = MarkdownField(blank=True)

    content_panels = Page.content_panels + [
        FieldPanel('intro', classname="full")
    ]

    def get_context(self, request):
        context = super().get_context(request)
        articlepages = ArticlePage.objects.child_of(self).live().order_by('-date')

        paginator = Paginator(articlepages, 12)

        page = request.GET.get('page')
        try:
            articlepages = paginator.page(page)
        except PageNotAnInteger:
            articlepages = paginator.page(1)
        except EmptyPage:
            articlepages = paginator.page(paginator.num_pages)

        context['articlepages'] = articlepages
        return context

class ArticlePageTag(TaggedItemBase):
    content_object = ParentalKey(
        'ArticlePage',
        related_name='tagged_items',
        on_delete=models.CASCADE
    )

class ArticlePage(Page):
    date = models.DateTimeField("发布日期", blank=True, null=True, default=datetime.today)
    intro = models.CharField("导读（目前无用）", blank=True, null=True, max_length=250)
    body = MarkdownField("正文内容", blank=True, null=True)
    tags = ClusterTaggableManager("文章标签", blank=True, through=ArticlePageTag)
    external_image = models.URLField("外链图标", blank=True, null=True)
    author = models.CharField("作者", blank=True, null=True, max_length=50, default="Anon")
    from_media = models.CharField("来自媒体", blank=True, null=True, max_length=50)
    original_link = models.URLField("原文链接", blank=True, null=True)
    video_embed_code = models.TextField("视频嵌入代码", blank=True, null=True)

    media_link_block = blocks.StructBlock([
        ('title', blocks.CharBlock(required=True, max_length=255, help_text='链接标题')),
        ('url', blocks.URLBlock(required=True, help_text='链接地址')),
        ('media_name', blocks.CharBlock(required=True, max_length=50, help_text='媒体名称')),
    ], icon='link')
    media_links = StreamField([
        ('link', media_link_block),
    ], null=True, blank=True, use_json_field=True)

    search_fields = Page.search_fields + [
        index.SearchField('title'),
        index.SearchField('body'),
    ]

    content_panels = Page.content_panels + [
        MultiFieldPanel([
            FieldPanel('date'),
            FieldPanel('external_image'),
            FieldPanel('tags'),
        ], heading="基本信息"),
        FieldPanel('body'),
        FieldPanel('media_links', heading="媒体报道"),
        MultiFieldPanel([
            FieldPanel('author'),
            FieldPanel('from_media'),
            FieldPanel('original_link'),
        ], heading="版权声明"),
        MultiFieldPanel([
            FieldPanel('intro'),
            FieldPanel('video_embed_code'),
        ], heading="附加信息"),
    ]

    def body_main_image(self):
        md_content = self.body
        img_urls = re.findall(r'!\[.*?\]\((.*?)\)', md_content)
        if img_urls:
            return img_urls[0]
        else:
            return None

    def save(self, *args, **kwargs):
        """Override save to clear cache when article is saved"""
        super().save(*args, **kwargs)
        if self.id:
            clear_article_cache(self.id)

@receiver([post_save, post_delete], sender=ArticlePage)
def clear_cache_on_article_change(sender, instance, **kwargs):
    """Clear cache when article is saved or deleted"""
    if instance.id:
        clear_article_cache(instance.id)

class ArticleTagIndexPage(Page):

    def get_context(self, request):
        tag = request.GET.get('tag')
        context = super().get_context(request)

        if not tag:
            context['tags'] = (
                Tag.objects.filter(
                    article_articlepagetag_items__isnull=False,
                    article_articlepagetag_items__content_object__live=True,
                )
                .annotate(count=Count("article_articlepagetag_items"))
                .distinct()
                .order_by("-count", "name")
            )
        else:
            articlepages = ArticlePage.objects.live().filter(tags__name=tag).order_by('-date')
            paginator = Paginator(articlepages, 12)
            page = request.GET.get('page')
            try:
                articlepages = paginator.page(page)
            except PageNotAnInteger:
                articlepages = paginator.page(1)
            except EmptyPage:
                articlepages = paginator.page(paginator.num_pages)
            context['articlepages'] = articlepages

        return context