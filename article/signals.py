import datetime
import secrets
import hashlib
from django.db.models.signals import pre_save
from django.dispatch import receiver
from .models import ArticlePage
from django.core.exceptions import PermissionDenied

def get_url_suffix(date, index):
    data = f"{date}-{index}-{secrets.token_hex(16)}"
    hashed = hashlib.sha256(data.encode()).hexdigest()
    return hashed[:16]

@receiver(pre_save, sender=ArticlePage)
def set_article_slug(sender, instance, **kwargs):
    if not instance.pk:
        date = datetime.date.today()
        index = secrets.randbelow(65535)
        instance.slug = get_url_suffix(date, index)
    elif instance.slug != ArticlePage.objects.get(pk=instance.pk).slug:
        raise PermissionDenied("You can't change the slug after the initial save")

@receiver(pre_save, sender=ArticlePage)
def check_slug_change(sender, instance, **kwargs):
    if instance.pk is not None and instance._state.adding is False:
        old_instance = ArticlePage.objects.get(pk=instance.pk)
        if old_instance.slug != instance.slug:
            raise PermissionDenied("You can't change the slug after the initial save")