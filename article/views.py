# article/views.py
from django.core.cache import cache
from core.cache import cache_article_data, make_cache_key
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from .models import ArticlePage
from my.models import Favorite
from django.contrib.contenttypes.models import ContentType
import random

@cache_article_data(timeout=1800)  # 30 minutes
def get_article_data(article_id):
    """Get cached article data"""
    page = get_object_or_404(ArticlePage, id=article_id)
    current_tags = page.tags.all()
    
    # 获取所有文章（排除当前文章）
    all_articles = ArticlePage.objects.live().exclude(id=article_id)
    
    # 随机选择5篇文章
    random_articles = random.sample(list(all_articles), 5)
    
    # 获取相关文章
    related_articles = (
        ArticlePage.objects.live()
        .exclude(id=article_id)
        .filter(tags__in=current_tags)
        .exclude(pk__in=[article.pk for article in random_articles])
        .distinct()
        .order_by('-first_published_at')
    )[:5]
    
    return {
        'page': page,
        'related_articles': related_articles,
        'random_articles': random_articles,
    }

def article_page(request, slug):
    # 获取当前文章
    page = get_object_or_404(ArticlePage, slug=slug)
    
    # 获取缓存的文章数据
    cached_data = get_article_data(page.id)
    
    # 获取用户相关的动态内容（不缓存）
    is_favorited = False
    if request.user.is_authenticated:
        article_content_type = ContentType.objects.get_for_model(ArticlePage)
        is_favorited = Favorite.objects.filter(
            user=request.user,
            content_type=article_content_type,
            object_id=page.id
        ).exists()
    
    # 合并上下文
    context = {
        **cached_data,
        'is_favorited': is_favorited,
    }
    
    return render(request, 'article/article_page.html', context)