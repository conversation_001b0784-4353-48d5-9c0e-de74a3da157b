from django import template
from django.utils.timesince import timesince
from datetime import datetime

register = template.Library()

@register.filter
def custom_timesince(value):
    if not value:
        return ''

    now = datetime.now()
    delta = now - value

    if delta.days > 30:
        formatted_date = value.strftime("%Y年%m月%d日")
        return formatted_date

    if delta.days > 0:
        return f'{delta.days} 天前'

    hours = int(delta.seconds / 3600)
    if hours > 0:
        return f'{hours} 小时前'

    return '刚刚'