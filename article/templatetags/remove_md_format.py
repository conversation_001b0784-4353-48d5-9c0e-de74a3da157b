from django import template
import re

register = template.Library()

@register.filter
def remove_markdown(text):
    text = re.sub(r'!\[.*?\]\(.*?\)', '', text)  # 清除链接 ![...](...)
    text = re.sub(r'\[.*?\]\(.*?\)', '', text)  # 清除链接 [...](...)
    text = re.sub(r'!\[.*?\]', '', text)  # 清除图片 ![...]
    text = re.sub(r'\*\*.*?\*\*', '', text)  # 清除加粗 **...**
    text = re.sub(r'<figcaption>.*?<\/figcaption>', '', text)  # 清除图片注释标签

    return text