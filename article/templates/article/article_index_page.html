{% extends "base.html" %}
{% load static %}
{% load wagtailcore_tags wagtailimages_tags %}
{% load custom_timesince remove_md_format %}

{% block body_class %}template-articleindexpage{% endblock %}

{% block content %}

<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
        <h1 class="text-xl text-center text-base-content">
            {{ page.title }}
        </h1>
    </div>
</div>

<div class="container mx-auto px-4 py-4 lg:px-8 xl:max-w-7xl">
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-4">
        {% for post in articlepages %}
        {% with post=post.specific %}
        <a href="{% pageurl post %}" target="_blank" class="card rounded-md shadow-md bg-base-100 hover:bg-base-200 transition-colors text-base-content">
            {% with post.body_main_image as main_image %}
            <figure class="max-h-64">
                {% if main_image %}
                <img class="w-full h-auto object-fill object-center" src="{{ main_image }}" alt="{{ post.title }}" decoding="async" loading="lazy">
                {% elif post.external_image %}
                <img class="w-full h-auto object-fill object-center" src="{{ post.external_image }}" alt="{{ post.title }}" decoding="async" loading="lazy">
                {% endif %}
            </figure>
            <div class="card-body">
                <h3 class="card-title">{{ post.title }}</h3>
                {% if not main_image and not post.external_image %}
                <p class="opacity-80">{{ post.body|remove_markdown|truncatechars:120 }}</p>
                {% endif %}
                {% endwith %}
                <div class="card-actions justify-between">
                    <div class="opacity-60">{{ post.author }}</div>
                    <div class="opacity-60">{{ post.date|custom_timesince }}</div>
                </div>
            </div>
        </a>
        {% endwith %}
        {% endfor %}
    </div>
</div>

<div class="py-4 px-1 mb-2 lg:py-8 lg:px-4">
    <div class="flex items-center justify-center">
        <ul class="list-none mb-6 flex">
            {% if articlepages.has_previous %}
            <li>
            <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?page={{ articlepages.previous_page_number }}">上一页</a>
            </li>
            {% else %}
            <li>
            <a class="pointer-events-none relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-300 transition-all duration-300">上一页</a>
            </li>
            {% endif %}
            {% for page_num in articlepages.paginator.page_range %}
            {% if articlepages.number == page_num %}
            <li aria-current="page">
            <a class="relative block rounded-sm bg-primary-100 px-3 py-1.5 text-lg font-medium text-primary transition-all duration-300">{{ page_num }}</a>
            </li>
            {% elif page_num > articlepages.number|add:'-3' and page_num < articlepages.number|add:'3' %}
            <li>
            <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?page={{ page_num }}">{{ page_num }}</a>
            </li>
            {% endif %}
            {% endfor %}
            {% if articlepages.has_next %}
            <li>
            <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?page={{ articlepages.next_page_number }}">下一页</a>
            </li>
            {% else %}
            <li>
            <a class="pointer-events-none relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-300 transition-all duration-300">下一页</a>
            </li>
            {% endif %}
        </ul>
    </div>
</div>

{% endblock %}