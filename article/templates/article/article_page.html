{% extends "base.html" %}

{% load static wagtailcore_tags wagtailimages_tags wagtailmarkdown custom_timesince wpm %}

{% block body_class %}template-articlepage{% endblock %}

{% block content %}
<!-- Article -->
<div x-data="{
        scrollToTop() { 
            window.scrollTo({ top: 0, behavior: 'smooth' }); 
        },
        isShowing: false
    }" @scroll.window="isShowing = (window.pageYOffset > 150) ? true : false"
    class="max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto">
    <div class="grid lg:grid-cols-6 gap-y-8 lg:gap-y-0 md:gap-x-6 lg:gap-x-12 lg:mb-5">
        <!-- Left Sidebar -->
        <div class="lg:col-span-1 lg:w-full lg:h-full hidden lg:block">
            <div class="sticky top-0 start-0 py-8 md:ps-4 lg:ps-8 float-right">
                <ul class="menu rounded-box items-center gap-4 text-2xl leading-none">
                    <li>
                        <a x-data="{ tooltip: false }"
                            @click="navigator.clipboard.writeText(window.location.href); tooltip = true; setTimeout(() => { tooltip = false; }, 3000)"
                            @mouseleave="tooltip = false" class="tooltip tooltip-left" data-tip="复制链接">
                            <span class="icon-[mdi--link-variant] text-base-content opacity-80"></span>
                            <span x-show.transition.duration.300="tooltip"
                                class="absolute bg-gray-800 text-white text-sm py-1 px-2 rounded-md left-full mr-4 flex items-center whitespace-nowrap"
                                style="display: none;">链接复制成功，快去分享吧~</span>
                        </a>
                    </li>
                    <li>
                        {% if user.is_authenticated %}
                        <a x-data="{ 
                                favorited: {{ is_favorited|yesno:'true,false' }}, 
                                csrfToken: '',
                                tooltipText: '收藏本文'
                            }"
                        x-init="tooltipText = favorited ? '取消收藏' : '收藏本文'"
                        @click="fetch(favorited ? '{% url "remove_favorite_by_type" 'article' 'articlepage' page.id %}' : '{% url "add_favorite" 'article' 'articlepage' page.id %}', { 
                            method: 'POST', 
                            headers: { 'X-CSRFToken': csrfToken, 'Content-Type': 'application/json' } 
                        }).then(response => response.json()).then(data => { 
                            favorited = data.favorited;
                            tooltipText = favorited ? '取消收藏' : '收藏本文';
                            $dispatch('favorite-updated', { favorited: favorited });
                        })"
                        x-bind:class="favorited ? 'btn-ghost' : 'btn-outline'"
                        x-bind:aria-pressed="favorited"
                        class="tooltip tooltip-left" x-bind:data-tip="tooltipText"
                        @mouseover="csrfToken = document.getElementById('csrf-token').value"
                        @favorite-updated.window="favorited = $event.detail.favorited; tooltipText = favorited ? '取消收藏' : '收藏本文'">
                            <span x-show="favorited" class="icon-[bi--bookmark-star-fill] text-base-content opacity-80"></span>
                            <span x-show="!favorited" class="icon-[bi--bookmark-star] text-base-content opacity-80"></span>
                        </a>
                        {% else %}
                        <a class="tooltip tooltip-left" data-tip="收藏本文" href="{% url 'account_login' %}">
                            <span class="icon-[bi--bookmark-star] text-base-content opacity-80"></span>
                        </a>
                        {% endif %}
                    </li>
                    <li>
                        <a class="tooltip tooltip-left" data-tip="意见反馈" href="https://www.wjx.cn/vm/PZah0CI.aspx" target="_blank"
                            rel="noopener noreferrer">
                            <span class="icon-[material-symbols--feedback-outline-rounded] text-base-content opacity-80"></span>
                        </a>
                    </li>
                    <li>
                        <a @click="scrollToTop" x-show="isShowing" class="tooltip tooltip-left" data-tip="回到顶部">
                            <span class="icon-[icon-park-outline--to-top-one] text-base-content opacity-80"></span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <!-- End Left Sidebar -->
        <!-- Content -->
        <div class="prose prose-stone lg:col-span-3 col-span-2">
            <div class="space-y-5 lg:space-y-8">
                <h1 class="mt-8 font-normal text-2xl md:text-3xl lg:text-4xl">{{ page.title }}</h1>
                <div class="not-prose flex flex-row flex-wrap gap-4 text-sm opacity-70">
                    <div class="flex items-center gap-1">
                        {% if page.specific.get_parent %}
                        <span class="icon-[material-symbols--folder-open-outline-rounded]"></span><a href="{% pageurl page.specific.get_parent %}">{{ page.specific.get_parent.title }}</a></span>
                        {% endif %}
                    </div>
                    <div class="flex items-center gap-1">
                        <span class="icon-[ri--quill-pen-line]"></span><span>{{ page.author }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                        <span class="icon-[material-symbols--date-range-outline-rounded]"></span><span>{{ page.date|custom_timesince }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                        <span class="icon-[fluent--hourglass-half-20-regular]"></span><span>{{ page.body|estimate_reading_time }}读完</span>
                    </div>
                </div>
                {% if page.video_embed_code %}
                <div class="skeleton video-container aspect-w-16 aspect-h-9">
                    {{ page.video_embed_code|safe }}
                </div>
                {% endif %}
                <article class="break-all" id="articleContent">{{ page.body|markdown }}</article>
                <!-- Disclaimer -->
                <div class="text-xs opacity-60">
                    {% if page.original_link %}
                    <p>© 本文来自：<a rel="nofollow noreferrer" target="_blank" href="{{ page.original_link }}">{{ page.from_media }}</a>，作者：{{ page.author }}，本文著作权归原作者所有。</p>
                    {% endif %}
                </div>
                <!-- End Disclaimer -->
                <!-- Tags -->
                {% if page.tags.all.count %}
                <div class="not-prose mb-2 flex flex-wrap gap-2 text-xs opacity-60">
                    <span>标签:</span>
                    {% for tag in page.tags.all %}
                    <a class="link" href="{% slugurl 'tags' %}?tag={{ tag }}">{{ tag }}</a>
                    {% endfor %}
                </div>
                {% endif %}
                <!-- End Tags -->
                <!-- Add Fav -->
                {% if user.is_authenticated %}
                <div class="flex justify-center lg:hidden mt-8">
                    <input type="hidden" id="csrf-token" value="{{ csrf_token }}" />
                    <div x-data="{ favorited: {{ is_favorited|yesno:'true,false' }}, csrfToken: '' }">
                        <button 
                            x-on:click="fetch(favorited ? '{% url "remove_favorite_by_type" 'article' 'articlepage' page.id %}' : '{% url "add_favorite" 'article' 'articlepage' page.id %}', {
                                method: 'POST',
                                headers: {
                                    'X-CSRFToken': csrfToken,
                                    'Content-Type': 'application/json'
                                }
                            }).then(response => response.json()).then(data => {
                                favorited = data.favorited;
                            })"
                            x-bind:class="favorited ? 'btn-ghost' : 'btn-outline'"
                            x-bind:aria-pressed="favorited"
                            class="btn btn-ghost"
                            hx-ext="ClickToDisable"
                            hx-target="this"
                            hx-swap="outerHTML"
                            hx-indicator="#favorite-btn-indicator"
                            hx-disable="this"
                            @mouseover="csrfToken = document.getElementById('csrf-token').value"
                        >
                            <span x-show="favorited" class="icon-[bi--bookmark-star-fill]"></span>
                            <span x-show="!favorited" class="icon-[bi--bookmark-star]"></span>
                            <span x-text="favorited ? '已收藏' : '收藏本文'"></span>
                        </button>
                        <div id="favorite-btn-indicator" style="display: none;"><span class="loading loading-spinner text-primary"></span></div>
                    </div>
                </div>
                {% else %}
                <div class="not-prose flex justify-center mt-4">
                    <a href="{% url 'account_login' %}" class="btn btn-outline">
                        <span class="icon-[bi--bookmark-star]"></span>
                        收藏本文
                    </a>
                </div>
                {% endif %}
                <!-- End Add Fav -->
                <!-- MediaLinks -->
                {% if page.specific.media_links %}
                <div class="divider"></div>
                <div>
                    <div class="flex items-center">
                        <span class="icon-[ph--pulse-bold] text-base-content"></span>
                        <span class="menu-title">媒体报道</span>
                    </div>
                    <ul>
                        {% for media_link in page.specific.media_links %}
                        <li>
                            <div class="not-prose flex">
                                <a target="_blank" rel="noopener, noreferrer" href="{{ media_link.value.url }}"
                                    class="link link-hover hover:text-info">
                                    <h4 class="inline">{{ media_link.value.title }}</h4>
                                    <span class="text-sm opacity-40 ml-2 inline-block">{{ media_link.value.media_name }}</span>
                                </a>
                            </div>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
                <!-- End MediaLinks -->
                {% if not user.is_authenticated %}
                <!-- ggad_wz_sqr -->
                <div class="max-w-sm md:max-w-md lg:w-full">
                    <ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-2270384440206851" data-ad-slot="3667955023"
                        data-ad-format="auto" data-full-width-responsive="false"></ins>
                </div>
                <!-- End ggad_wz_sqr -->
                {% endif %}
                <!-- Login Banner -->
                {% if not user.is_authenticated %}
                <div class="not-prose card border border-primary my-10 max-w-lg">
                    <div class="card-body flex flex-col gap-4">
                        <p class="text-lg font-black lg:text-xl">加入<span class="text-primary">咖啡搭子</span>，开启您的专属体验</p>
                        <ul class="list-none p-0">
                            <li>🚀 免广告：全站浏览<span class="underline decoration-wavy underline-offset-2 tooltip" data-tip="特指谷歌广告联盟">无广告</span>干扰</li>
                            <li>📎 收藏夹：轻松管理您的喜好</li>
                            <li>📝 咖啡札记：记录您的冲煮参数</li>
                        </ul>
                        <div class="card-actions">
                            <a class="btn btn-primary" href="{% url 'account_signup' %}">免费注册</a>
                            <a class="btn btn-primary btn-outline" href="{% url 'account_login' %}">立即登录</a>
                        </div>
                    </div>
                </div>
                {% endif %}
                <!-- End Login Banner -->
            </div>
        </div>
        <!-- End Content -->

        <!-- Right Sidebar -->
        <div class="lg:col-span-2 lg:w-full lg:h-full col-span-2">
            <div class="sticky top-0 start-0 py-8 md:ps-2 lg:ps-4">
                {% if related_articles %}
                <div class="space-y-6">
                    <div class="flex items-center">
                        <span class="icon-[ri--article-line] text-base-content"></span>
                        <span class="menu-title">相关阅读</span>
                    </div>
                    {% for article in related_articles %}
                    {% with article.body_main_image as main_image %}
                    {% with article.external_image as external_image %}
                    {% if main_image %}
                    <a class="group flex items-center gap-x-6 text-base-content" href="{% pageurl article %}">
                        <div class="grow">
                            <span class="link link-hover hover:text-info">
                                {{ article.title }}
                            </span>
                        </div>
                        <div class="shrink-0 relative rounded-lg overflow-hidden w-20 h-11">
                            <img class="w-full h-full absolute top-0 start-0 object-cover rounded-lg" src="{{ main_image }}"
                                alt="{{ article.title }}" decoding="async" loading="lazy">
                        </div>
                    </a>
                    {% else %}
                    {% if external_image %}
                    <a class="group flex items-center gap-x-6 text-base-content" href="{% pageurl article %}">
                        <div class="grow">
                            <span class="link link-hover hover:text-info">
                                {{ article.title }}
                            </span>
                        </div>
                        <div class="shrink-0 relative rounded-lg overflow-hidden w-20 h-11">
                            <img class="w-full h-full absolute top-0 start-0 object-cover rounded-lg" src="{{ external_image }}"
                                alt="{{ article.title }}" decoding="async" loading="lazy">
                        </div>
                    </a>
                    {% else %}
                    <a class="group flex items-center gap-x-6 text-base-content" href="{% pageurl article %}">
                        <div class="grow">
                            <span class="link link-hover hover:text-info">
                                {{ article.title }}
                            </span>
                        </div>
                    </a>
                    {% endif %}
                    {% endif %}
                    {% endwith %}
                    {% endwith %}
                    {% endfor %}
                </div>
                {% endif %}
                {% if random_articles %}
                <div class="space-y-6 mt-16">
                    <div class="flex items-center">
                        <span class="icon-[ri--bubble-chart-line] text-base-content"></span>
                        <span class="menu-title">探索发现</span>
                    </div>
                    {% for article in random_articles %}
                    {% with article.body_main_image as main_image %}
                    {% with article.external_image as external_image %}
                    {% if main_image %}
                    <a class="group flex items-center gap-x-6 text-base-content" href="{% pageurl article %}">
                        <div class="grow">
                            <span class="link link-hover hover:text-info">
                                {{ article.title }}
                            </span>
                        </div>
                        <div class="shrink-0 relative rounded-lg overflow-hidden w-20 h-11">
                            <img class="w-full h-full absolute top-0 start-0 object-cover rounded-lg" src="{{ main_image }}"
                                alt="{{ article.title }}" decoding="async" loading="lazy">
                        </div>
                    </a>
                    {% else %}
                    {% if external_image %}
                    <a class="group flex items-center gap-x-6 text-base-content" href="{% pageurl article %}">
                        <div class="grow">
                            <span class="link link-hover hover:text-info">
                                {{ article.title }}
                            </span>
                        </div>
                        <div class="shrink-0 relative rounded-lg overflow-hidden w-20 h-11">
                            <img class="w-full h-full absolute top-0 start-0 object-cover rounded-lg" src="{{ external_image }}"
                                alt="{{ article.title }}" decoding="async" loading="lazy">
                        </div>
                    </a>
                    {% else %}
                    <a class="group flex items-center gap-x-6 text-base-content" href="{% pageurl article %}">
                        <div class="grow">
                            <span class="link link-hover hover:text-info">
                                {{ article.title }}
                            </span>
                        </div>
                    </a>
                    {% endif %}
                    {% endif %}
                    {% endwith %}
                    {% endwith %}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
        <!-- End Right Sidebar -->

    </div>
</div>
<!-- End Article -->
{% endblock %}

{% block extra_js %}
<script defer type="text/javascript" src="{% static 'js/article.js' %}"></script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        (adsbygoogle = window.adsbygoogle || []).push({});
    });
</script>
{% endblock %}