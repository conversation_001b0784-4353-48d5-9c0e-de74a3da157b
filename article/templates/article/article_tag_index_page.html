{% extends "base.html" %}
{% load static %}
{% load wagtailcore_tags wagtailimages_tags %}
{% load custom_timesince remove_md_format %}

{% block body_class %}template-articletagindexpage{% endblock %}

{% block content %}

{% if request.GET.tag %}
<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
        <h1 class="text-xl text-center text-base-content">
            #{{ request.GET.tag }}
        </h1>
    </div>
</div>
<div class="container mx-auto px-4 py-4 lg:px-8 lg:py-8 xl:max-w-7xl text-base-content">
    {% if articlepages %}
    <p class="text-sm opacity-60">相关文章 {{ articlepages.paginator.count }} 篇：</p>
    <div class="grid grid-cols-1 gap-4 pt-5 md:grid-cols-2 lg:pt-10 xl:grid-cols-4">
        {% for post in articlepages %}
        {% with post=post.specific %}
        <a href="{% pageurl post %}" target="_blank" class="card rounded-md shadow-md bg-base-100 hover:bg-base-200 transition-colors text-base-content">
            {% with post.body_main_image as main_image %}
            <figure class="max-h-64">
                {% if main_image %}
                <img class="w-full h-auto object-fill object-center" src="{{ main_image }}" alt="{{ post.title }}" decoding="async" loading="lazy">
                {% elif post.external_image %}
                <img class="w-full h-auto object-fill object-center" src="{{ post.external_image }}" alt="{{ post.title }}" decoding="async" loading="lazy">
                {% endif %}
            </figure>
            <div class="card-body">
                <h3 class="card-title">{{ post.title }}</h3>
                {% if not main_image and not post.external_image %}
                <p class="opacity-80">{{ post.body|remove_markdown|truncatechars:120 }}</p>
                {% endif %}
                {% endwith %}
                <div class="card-actions justify-between">
                    <div class="opacity-60">{{ post.author }}</div>
                    <div class="opacity-60">{{ post.date|custom_timesince }}</div>
                </div>
            </div>
        </a>
        {% endwith %}
        {% empty %}
        没有找到与该标签相关的文章。
        {% endfor %}
    </div>
    {% else %}
    <p>没有找到与该标签相关的文章。</p>
    {% endif %}
</div>
<div class="py-2 px-1 lg:p-4 mb-2 lg:py-8 lg:px-4">
    <div class="flex items-center justify-center">
        <ul class="list-none mb-6 flex">
            {% if articlepages.has_previous %}
            <li>
                <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100"
                    href="?tag={{ request.GET.tag }}&page={{ articlepages.previous_page_number }}">上一页</a>
            </li>
            {% else %}
            <li>
                <a
                    class="pointer-events-none relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-300 transition-all duration-300">上一页</a>
            </li>
            {% endif %}
            {% for page_num in articlepages.paginator.page_range %}
            {% if page_num == articlepages.number %}
            <li aria-current="page">
                <a
                    class="relative block rounded-sm bg-primary-100 px-3 py-1.5 text-lg font-medium text-primary transition-all duration-300">{{ page_num }}</a>
            </li>
            {% else %}
            <li>
                <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100"
                    href="?tag={{ request.GET.tag }}&page={{ page_num }}">{{ page_num }}</a>
            </li>
            {% endif %}
            {% endfor %}
            {% if articlepages.has_next %}
            <li>
                <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100"
                    href="?tag={{ request.GET.tag }}&page={{ articlepages.next_page_number }}">下一页</a>
            </li>
            {% else %}
            <li>
                <a
                    class="pointer-events-none relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-300 transition-all duration-300">下一页</a>
            </li>
            {% endif %}
        </ul>
    </div>
</div>
{% else %}
<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
        <h1 class="text-xl text-center text-base-content">
            热门标签
        </h1>
    </div>
</div>
<div class="max-w-screen-xl mx-auto p-5 sm:p-10 md:p-16">
    <div class="flex flex-wrap gap-4 list-none">
        {% for tag in tags|slice:":50" %}
        <li><a href="{% pageurl self %}?tag={{ tag }}" class="btn btn-outline btn-sm">#{{ tag }}</a></li>
        {% endfor %}
    </div>
</div>
{% endif %}
{% endblock %}