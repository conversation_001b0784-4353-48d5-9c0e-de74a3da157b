# Generated by Django 4.2.7 on 2024-11-08 16:14

from django.db import migrations
import wagtail.blocks
import wagtail.fields


class Migration(migrations.Migration):

    dependencies = [
        ('equipment', '0013_alter_espressopage_product_parts_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='espressopage',
            name='product_property',
            field=wagtail.fields.StreamField([('machine_type', wagtail.blocks.StructBlock([('tags', wagtail.blocks.MultipleChoiceBlock(choices=[('家用', '家用'), ('商用', '商用'), ('小型商用', '小型商用'), ('全自动', '全自动'), ('半自动', '半自动'), ('手动', '手动'), ('便携式', '便携式'), ('拉杆式', '拉杆式'), ('研磨分体', '研磨分体'), ('研磨一体', '研磨一体')], required=False))])), ('color', wagtail.blocks.StructBlock([('color_name', wagtail.blocks.CharBlock(help_text='请输入颜色名称', required=False)), ('image_url', wagtail.blocks.URLBlock(help_text='请输入图片URL', required=False)), ('color_tax', wagtail.blocks.DecimalBlock(help_text='请输入颜色差价', required=False))])), ('dimension', wagtail.blocks.StructBlock([('height', wagtail.blocks.IntegerBlock(help_text='请输入高度（mm）', required=False)), ('width', wagtail.blocks.IntegerBlock(help_text='请输入宽度（mm）', required=False)), ('depth', wagtail.blocks.IntegerBlock(help_text='请输入深度（mm）', required=False)), ('weight', wagtail.blocks.DecimalBlock(help_text='请输入重量（kg）', required=False)), ('cup_height', wagtail.blocks.IntegerBlock(help_text='请输入最大杯子高度（mm）', required=False))])), ('housing', wagtail.blocks.StructBlock([('voltage', wagtail.blocks.CharBlock(default='220V 50Hz', help_text='请输入电压', required=False)), ('plug', wagtail.blocks.ChoiceBlock(choices=[('10A', '10A'), ('16A', '16A')], required=False)), ('input_power', wagtail.blocks.CharBlock(help_text='请输入额定功率（W）', required=False)), ('max_power', wagtail.blocks.CharBlock(help_text='请输入最大功率（W）', required=False)), ('min_power', wagtail.blocks.CharBlock(help_text='请输入最小功率（W）', required=False)), ('drip_tray_capacity', wagtail.blocks.DecimalBlock(help_text='请输入接水盘容量（L）', required=False)), ('specs', wagtail.blocks.ListBlock(wagtail.blocks.StructBlock([('description', wagtail.blocks.TextBlock(required=False)), ('maintenance', wagtail.blocks.TextBlock(required=False))])))])), ('features', wagtail.blocks.StructBlock([('specs', wagtail.blocks.ListBlock(wagtail.blocks.StructBlock([('description', wagtail.blocks.TextBlock(required=False)), ('maintenance', wagtail.blocks.TextBlock(required=False))]))), ('tags', wagtail.blocks.MultipleChoiceBlock(choices=[('预浸泡', '预浸泡'), ('PID/NTC温控', 'PID/NTC温控'), ('物联网', '物联网'), ('自动开关机', '自动开关机'), ('萃取计时器', '萃取计时器'), ('预设萃取曲线', '预设萃取曲线'), ('OPV稳压', 'OPV稳压'), ('适配胶囊咖啡', '适配胶囊咖啡'), ('定时研磨', '定时研磨'), ('定量研磨', '定量研磨'), ('手冲模式', '手冲模式'), ('冷萃模式', '冷萃模式')], required=False))]))], blank=True),
        ),
    ]
