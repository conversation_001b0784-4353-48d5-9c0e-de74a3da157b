# Generated by Django 4.2.7 on 2024-11-11 11:42

from django.db import migrations
from wagtail.blocks import StreamValue

def update_interface_tags(apps, schema_editor):
    EspressoPage = apps.get_model('equipment', 'EspressoPage')
    
    for page in EspressoPage.objects.all():
        if page.product_parts:
            modified = False
            
            for block in page.product_parts:
                if block.block_type == 'user_panel' and block.value:
                    tags = block.value.get('tags', [])
                    if '可调低压浸泡时长' in tags:
                        tags.remove('可调低压浸泡时长')
                        tags.append('可调预浸泡时长')
                        modified = True
                    if '可调预注水时长' in tags:
                        tags.remove('可调预注水时长')
                        tags.append('可调预浸泡水量')
                        modified = True
                    
                    # 将修改后的 value 重新赋值给 block
                    block.value['tags'] = tags
            
            if modified:
                page.save()

class Migration(migrations.Migration):

    dependencies = [
        ('equipment', '0020_alter_espressopage_product_parts'),
    ]

    operations = [
        migrations.RunPython(update_interface_tags, migrations.RunPython.noop),
    ]