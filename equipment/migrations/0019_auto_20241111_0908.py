# Generated by Django 4.2.7 on 2024-11-11 09:08

from django.db import migrations
from wagtail.blocks import StreamValue

def update_interface_tags(apps, schema_editor):
    EspressoPage = apps.get_model('equipment', 'EspressoPage')  # 替换为实际的页面模型名
    
    for page in EspressoPage.objects.all():
        if page.product_parts:  # 假设你的 StreamField 名称是 product_parts
            modified = False
            
            for block in page.product_parts:  # StreamField 可以直接迭代
                if block.block_type == 'user_panel':
                    tags = block.value.get('tags', [])
                    if '可调预浸泡' in tags:
                        tags.remove('可调预浸泡')
                        tags.append('可调低压浸泡时长')
                        modified = True
            
            if modified:
                page.save()

class Migration(migrations.Migration):

    dependencies = [
        ('equipment', '0018_alter_espressopage_product_property'),  # 替换为前一个迁移文件名
    ]

    operations = [
        migrations.RunPython(update_interface_tags, migrations.RunPython.noop),
    ]