# Generated by Django 4.2.7 on 2024-11-08 20:13

from django.db import migrations
import wagtail.blocks
import wagtail.fields


class Migration(migrations.Migration):

    dependencies = [
        ('equipment', '0014_alter_espressopage_product_property'),
    ]

    operations = [
        migrations.AlterField(
            model_name='espressopage',
            name='product_parts',
            field=wagtail.fields.StreamField([('group_head', wagtail.blocks.StructBlock([('group_head_type', wagtail.blocks.ChoiceBlock(choices=[('E61', 'E61'), ('电加热', '电加热'), ('拉杆式', '拉杆式'), ('饱和式', '饱和式'), ('半饱和式', '半饱和式'), ('传统式', '传统式')], required=False)), ('numbers', wagtail.blocks.IntegerBlock(default=1, help_text='请输入冲煮头个数', required=False)), ('specs', wagtail.blocks.ListBlock(wagtail.blocks.StructBlock([('description', wagtail.blocks.TextBlock(required=False)), ('maintenance', wagtail.blocks.TextBlock(required=False))])))])), ('portafilter', wagtail.blocks.StructBlock([('portafilter_inc', wagtail.blocks.CharBlock(help_text='请输入原装手柄规格', required=False)), ('basket_inc', wagtail.blocks.CharBlock(help_text='请输入原装粉碗规格', required=False)), ('basket_size', wagtail.blocks.ChoiceBlock(choices=[('51mm', '51mm'), ('52mm', '52mm'), ('53mm', '53mm'), ('54mm', '54mm'), ('58mm', '58mm')], required=False)), ('specs', wagtail.blocks.ListBlock(wagtail.blocks.StructBlock([('description', wagtail.blocks.TextBlock(required=False)), ('maintenance', wagtail.blocks.TextBlock(required=False))])))])), ('pump', wagtail.blocks.StructBlock([('pump_type', wagtail.blocks.ChoiceBlock(choices=[('旋转泵', '旋转泵'), ('震动泵', '震动泵'), ('齿轮泵', '齿轮泵'), ('叶轮泵', '叶轮泵')], required=False)), ('pump_number', wagtail.blocks.IntegerBlock(default=1, help_text='请输入水泵个数', required=False)), ('brew_pressure', wagtail.blocks.IntegerBlock(help_text='请输入宣传泵压（bar）', required=False)), ('specs', wagtail.blocks.ListBlock(wagtail.blocks.StructBlock([('description', wagtail.blocks.TextBlock(required=False)), ('maintenance', wagtail.blocks.TextBlock(required=False))])))])), ('water_supply', wagtail.blocks.StructBlock([('reservoir', wagtail.blocks.DecimalBlock(help_text='请输入水箱容量（L）', required=False)), ('plumbed', wagtail.blocks.BooleanBlock(help_text='是否可接进水管', required=False)), ('specs', wagtail.blocks.ListBlock(wagtail.blocks.StructBlock([('description', wagtail.blocks.TextBlock(required=False)), ('maintenance', wagtail.blocks.TextBlock(required=False))]))), ('tags', wagtail.blocks.MultipleChoiceBlock(choices=[('自动上水', '自动上水'), ('水箱上水', '水箱上水'), ('双进水模式', '双进水模式'), ('选配自动上水', '选配自动上水')], required=False))])), ('boiler', wagtail.blocks.StructBlock([('boiler_type', wagtail.blocks.ChoiceBlock(choices=[('单锅炉', '单锅炉'), ('双锅炉', '双锅炉'), ('多锅炉', '多锅炉'), ('热交换锅炉', '热交换锅炉'), ('单加热块', '单加热块'), ('双加热块', '双加热块'), ('多加热块', '多加热块'), ('混合锅炉', '混合锅炉')], required=False)), ('brew_size', wagtail.blocks.DecimalBlock(help_text='请输入冲煮锅炉容量（L）', required=False)), ('steam_size', wagtail.blocks.DecimalBlock(help_text='请输入蒸气锅炉容量（L）', required=False)), ('brew_power', wagtail.blocks.IntegerBlock(help_text='请输入冲煮锅炉功率（W）', required=False)), ('steam_power', wagtail.blocks.IntegerBlock(help_text='请输入蒸气锅炉功率（W）', required=False)), ('warmup_time', wagtail.blocks.CharBlock(help_text='请输入带单位的预热时长', required=False)), ('specs', wagtail.blocks.ListBlock(wagtail.blocks.StructBlock([('description', wagtail.blocks.TextBlock(required=False)), ('maintenance', wagtail.blocks.TextBlock(required=False))])))])), ('steam_wand', wagtail.blocks.StructBlock([('holes', wagtail.blocks.IntegerBlock(help_text='请输入孔数', required=False)), ('hole_size', wagtail.blocks.DecimalBlock(help_text='请输入孔径（mm）', required=False)), ('wand_length', wagtail.blocks.DecimalBlock(help_text='请输入蒸气棒长度（mm）', required=False)), ('hot_water', wagtail.blocks.BooleanBlock(help_text='是否有热水口', required=False)), ('specs', wagtail.blocks.ListBlock(wagtail.blocks.StructBlock([('description', wagtail.blocks.TextBlock(required=False)), ('maintenance', wagtail.blocks.TextBlock(required=False))]))), ('tags', wagtail.blocks.MultipleChoiceBlock(choices=[('自动打奶', '自动打奶'), ('感温即停', '感温即停'), ('万向旋转', '万向旋转'), ('起泡外管', '起泡外管'), ('防烫管', '防烫管')], required=False))])), ('user_panel', wagtail.blocks.StructBlock([('switch', wagtail.blocks.TextBlock(help_text='有哪些开关', required=False)), ('button', wagtail.blocks.TextBlock(help_text='有哪些按钮', required=False)), ('indicator', wagtail.blocks.TextBlock(help_text='有哪些指示灯', required=False)), ('menu', wagtail.blocks.TextBlock(help_text='有哪些菜单', required=False)), ('gauge', wagtail.blocks.TextBlock(help_text='有哪些仪表', required=False)), ('stick', wagtail.blocks.TextBlock(help_text='有哪些拨杆/旋钮', required=False)), ('specs', wagtail.blocks.ListBlock(wagtail.blocks.StructBlock([('description', wagtail.blocks.TextBlock(required=False)), ('maintenance', wagtail.blocks.TextBlock(required=False))]))), ('tags', wagtail.blocks.MultipleChoiceBlock(choices=[('实体按钮', '实体按钮'), ('触控屏', '触控屏'), ('电子显示屏', '电子显示屏'), ('温度仪', '温度仪'), ('压力表', '压力表'), ('杯量控制', '杯量控制'), ('可调锅炉温度', '可调锅炉温度'), ('可调萃取温度', '可调萃取温度'), ('可调萃取压力', '可调萃取压力'), ('可调萃取流量', '可调萃取流量'), ('可调预浸泡', '可调预浸泡'), ('内置电子秤', '内置电子秤')], required=False))])), ('grinder', wagtail.blocks.StructBlock([('burr_type', wagtail.blocks.ChoiceBlock(choices=[('平刀', '平刀'), ('锥刀', '锥刀'), ('鬼齿', '鬼齿'), ('桨叶式', '桨叶式')], help_text='请输入刀盘类型', required=False)), ('hopper_number', wagtail.blocks.IntegerBlock(help_text='请输入豆仓个数', required=False)), ('hopper_capacity', wagtail.blocks.IntegerBlock(help_text='请输入豆仓容量（g）', required=False)), ('burr_number', wagtail.blocks.IntegerBlock(default=1, help_text='请输入研磨器个数', required=False)), ('clicks', wagtail.blocks.IntegerBlock(help_text='请输入研磨挡数', required=False)), ('burr_size', wagtail.blocks.IntegerBlock(help_text='请输入磨芯尺寸（mm）', required=False)), ('area', wagtail.blocks.IntegerBlock(help_text='请输入研磨面积（mm²）', required=False)), ('speed', wagtail.blocks.DecimalBlock(help_text='请输入研磨速度（g/s）', required=False)), ('removable_burr', wagtail.blocks.BooleanBlock(help_text='是否可拆卸磨盘', required=False)), ('specs', wagtail.blocks.ListBlock(wagtail.blocks.StructBlock([('description', wagtail.blocks.TextBlock(required=False)), ('maintenance', wagtail.blocks.TextBlock(required=False))]))), ('tags', wagtail.blocks.MultipleChoiceBlock(choices=[('自动弹粉', '自动弹粉'), ('防静电飞粉', '防静电飞粉'), ('几乎零残粉', '几乎零残粉')], required=False))]))], blank=True),
        ),
    ]
