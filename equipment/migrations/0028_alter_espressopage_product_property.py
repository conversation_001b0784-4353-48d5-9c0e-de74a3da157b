# Generated by Django 4.2.7 on 2024-11-14 14:24

from django.db import migrations
import wagtail.fields


class Migration(migrations.Migration):

    dependencies = [
        ('equipment', '0027_alter_espressopage_product_parts_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='espressopage',
            name='product_property',
            field=wagtail.fields.StreamField([('machine_type', 1), ('color', 5), ('dimension', 11), ('housing', 17), ('features', 19)], blank=True, block_lookup={0: ('wagtail.blocks.MultipleChoiceBlock', [], {'choices': [('家用', '家用'), ('商用', '商用'), ('小型商用', '小型商用'), ('研磨分体', '研磨分体'), ('研磨一体', '研磨一体'), ('全自动', '全自动'), ('半自动', '半自动'), ('手动', '手动'), ('便携式', '便携式'), ('拉杆式', '拉杆式')], 'required': False}), 1: ('wagtail.blocks.StructBlock', [[('tags', 0)]], {}), 2: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入颜色名称', 'required': False}), 3: ('wagtail.blocks.URLBlock', (), {'help_text': '请输入图片URL', 'required': False}), 4: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入颜色差价', 'required': False}), 5: ('wagtail.blocks.StructBlock', [[('color_name', 2), ('image_url', 3), ('color_tax', 4)]], {}), 6: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入高度（mm）', 'required': False}), 7: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入宽度（mm）', 'required': False}), 8: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入深度（mm）', 'required': False}), 9: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入重量（kg）', 'required': False}), 10: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入最大杯子高度（mm）', 'required': False}), 11: ('wagtail.blocks.StructBlock', [[('height', 6), ('width', 7), ('depth', 8), ('weight', 9), ('cup_height', 10)]], {}), 12: ('wagtail.blocks.CharBlock', (), {'default': '220V 50Hz', 'help_text': '请输入电压', 'required': False}), 13: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入额定功率（W）', 'required': False}), 14: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入最大功率（W）', 'required': False}), 15: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入最小功率（W）', 'required': False}), 16: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入接水盘容量（L）', 'required': False}), 17: ('wagtail.blocks.StructBlock', [[('voltage', 12), ('input_power', 13), ('max_power', 14), ('min_power', 15), ('drip_tray_capacity', 16)]], {}), 18: ('wagtail.blocks.MultipleChoiceBlock', [], {'choices': [('预浸泡', '预浸泡'), ('PID/NTC温控', 'PID/NTC温控'), ('OPV稳压', 'OPV稳压'), ('萃取计时器', '萃取计时器'), ('物联网', '物联网'), ('预设萃取曲线', '预设萃取曲线'), ('适配胶囊咖啡', '适配胶囊咖啡'), ('手冲模式', '手冲模式'), ('冷萃模式', '冷萃模式'), ('定时研磨', '定时研磨'), ('定量研磨', '定量研磨')], 'required': False}), 19: ('wagtail.blocks.StructBlock', [[('tags', 18)]], {})}),
        ),
    ]
