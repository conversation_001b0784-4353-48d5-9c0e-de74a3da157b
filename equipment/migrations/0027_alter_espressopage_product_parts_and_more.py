# Generated by Django 4.2.7 on 2024-11-14 10:18

from django.db import migrations
import wagtail.fields


class Migration(migrations.Migration):

    dependencies = [
        ('equipment', '0026_alter_espressopage_product_parts'),
    ]

    operations = [
        migrations.AlterField(
            model_name='espressopage',
            name='product_parts',
            field=wagtail.fields.StreamField([('group_head', 2), ('portafilter', 6), ('pump', 10), ('water_supply', 15), ('boiler', 22), ('steam_wand', 28), ('user_panel', 36), ('grinder', 47)], blank=True, block_lookup={0: ('wagtail.blocks.ChoiceBlock', [], {'choices': [('E61', 'E61'), ('电加热', '电加热'), ('拉杆式', '拉杆式'), ('饱和式', '饱和式'), ('半饱和式', '半饱和式'), ('传统式', '传统式')], 'required': False}), 1: ('wagtail.blocks.IntegerBlock', (), {'default': 1, 'help_text': '请输入冲煮头个数', 'required': False}), 2: ('wagtail.blocks.StructBlock', [[('group_head_type', 0), ('numbers', 1)]], {}), 3: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入原装手柄规格', 'required': False}), 4: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入原装粉碗规格', 'required': False}), 5: ('wagtail.blocks.ChoiceBlock', [], {'choices': [('51mm', '51mm'), ('52mm', '52mm'), ('53mm', '53mm'), ('54mm', '54mm'), ('58mm', '58mm')], 'required': False}), 6: ('wagtail.blocks.StructBlock', [[('portafilter_inc', 3), ('basket_inc', 4), ('basket_size', 5)]], {}), 7: ('wagtail.blocks.ChoiceBlock', [], {'choices': [('旋转泵', '旋转泵'), ('震动泵', '震动泵'), ('齿轮泵', '齿轮泵'), ('叶轮泵', '叶轮泵'), ('叶片泵', '叶片泵')], 'required': False}), 8: ('wagtail.blocks.IntegerBlock', (), {'default': 1, 'help_text': '请输入水泵个数', 'required': False}), 9: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入宣传泵压（bar）', 'required': False}), 10: ('wagtail.blocks.StructBlock', [[('pump_type', 7), ('pump_number', 8), ('brew_pressure', 9)]], {}), 11: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入水箱容量（L）', 'required': False}), 12: ('wagtail.blocks.BooleanBlock', (), {'help_text': '是否可接进水管', 'required': False}), 13: ('wagtail.blocks.BooleanBlock', (), {'help_text': '是否可接下水管', 'required': False}), 14: ('wagtail.blocks.MultipleChoiceBlock', [], {'choices': [('自动上水', '自动上水'), ('水箱上水', '水箱上水'), ('双进水模式', '双进水模式'), ('选配自动上水', '选配自动上水')], 'required': False}), 15: ('wagtail.blocks.StructBlock', [[('reservoir', 11), ('plumbed', 12), ('drainable', 13), ('tags', 14)]], {}), 16: ('wagtail.blocks.ChoiceBlock', [], {'choices': [('单锅炉', '单锅炉'), ('双锅炉', '双锅炉'), ('多锅炉', '多锅炉'), ('子母锅炉', '子母锅炉'), ('单加热块', '单加热块'), ('双加热块', '双加热块'), ('多加热块', '多加热块'), ('混合锅炉', '混合锅炉')], 'required': False}), 17: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入冲煮锅炉容量（L）', 'required': False}), 18: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入蒸气锅炉容量（L）', 'required': False}), 19: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入冲煮锅炉功率（W）', 'required': False}), 20: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入蒸气锅炉功率（W）', 'required': False}), 21: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入带单位的预热时长', 'required': False}), 22: ('wagtail.blocks.StructBlock', [[('boiler_type', 16), ('brew_size', 17), ('steam_size', 18), ('brew_power', 19), ('steam_power', 20), ('warmup_time', 21)]], {}), 23: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入孔数', 'required': False}), 24: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入孔径（mm）', 'required': False}), 25: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入蒸气棒长度（mm）', 'required': False}), 26: ('wagtail.blocks.BooleanBlock', (), {'help_text': '是否有热水口', 'required': False}), 27: ('wagtail.blocks.MultipleChoiceBlock', [], {'choices': [('自动打奶', '自动打奶'), ('感温即停', '感温即停'), ('万向旋转', '万向旋转'), ('起泡外管', '起泡外管'), ('防烫管', '防烫管')], 'required': False}), 28: ('wagtail.blocks.StructBlock', [[('holes', 23), ('hole_size', 24), ('wand_length', 25), ('hot_water', 26), ('tags', 27)]], {}), 29: ('wagtail.blocks.TextBlock', (), {'help_text': '有哪些开关', 'required': False}), 30: ('wagtail.blocks.TextBlock', (), {'help_text': '有哪些按钮', 'required': False}), 31: ('wagtail.blocks.TextBlock', (), {'help_text': '有哪些指示灯', 'required': False}), 32: ('wagtail.blocks.TextBlock', (), {'help_text': '有哪些菜单', 'required': False}), 33: ('wagtail.blocks.TextBlock', (), {'help_text': '有哪些仪表', 'required': False}), 34: ('wagtail.blocks.TextBlock', (), {'help_text': '有哪些拨杆/旋钮', 'required': False}), 35: ('wagtail.blocks.MultipleChoiceBlock', [], {'choices': [('实体按钮', '实体按钮'), ('触控屏', '触控屏'), ('电子显示屏', '电子显示屏'), ('温度仪', '温度仪'), ('压力表', '压力表'), ('杯量控制', '杯量控制'), ('可调蒸汽温度', '可调蒸汽温度'), ('可调萃取温度', '可调萃取温度'), ('可调萃取压力', '可调萃取压力'), ('可调萃取流量', '可调萃取流量'), ('可调预浸泡时长', '可调预浸泡时长'), ('可调预浸泡水量', '可调预浸泡水量'), ('可调OPV峰值', '可调OPV峰值'), ('内置电子秤', '内置电子秤')], 'required': False}), 36: ('wagtail.blocks.StructBlock', [[('switch', 29), ('button', 30), ('indicator', 31), ('menu', 32), ('gauge', 33), ('stick', 34), ('tags', 35)]], {}), 37: ('wagtail.blocks.ChoiceBlock', [], {'choices': [('平刀', '平刀'), ('锥刀', '锥刀'), ('鬼齿', '鬼齿'), ('桨叶式', '桨叶式')], 'help_text': '请输入刀盘类型', 'required': False}), 38: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入豆仓个数', 'required': False}), 39: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入豆仓容量（g）', 'required': False}), 40: ('wagtail.blocks.IntegerBlock', (), {'default': 1, 'help_text': '请输入研磨器个数', 'required': False}), 41: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入研磨挡数', 'required': False}), 42: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入磨芯尺寸（mm）', 'required': False}), 43: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入研磨面积（mm²）', 'required': False}), 44: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入研磨速度（g/s）', 'required': False}), 45: ('wagtail.blocks.BooleanBlock', (), {'help_text': '是否可拆卸磨盘', 'required': False}), 46: ('wagtail.blocks.MultipleChoiceBlock', [], {'choices': [('自动弹粉', '自动弹粉'), ('防静电飞粉', '防静电飞粉'), ('几乎零残粉', '几乎零残粉')], 'required': False}), 47: ('wagtail.blocks.StructBlock', [[('burr_type', 37), ('hopper_number', 38), ('hopper_capacity', 39), ('burr_number', 40), ('clicks', 41), ('burr_size', 42), ('area', 43), ('speed', 44), ('removable_burr', 45), ('tags', 46)]], {})}),
        ),
        migrations.AlterField(
            model_name='espressopage',
            name='product_property',
            field=wagtail.fields.StreamField([('machine_type', 1), ('color', 5), ('dimension', 11), ('housing', 17), ('features', 19)], blank=True, block_lookup={0: ('wagtail.blocks.MultipleChoiceBlock', [], {'choices': [('家用', '家用'), ('商用', '商用'), ('小型商用', '小型商用'), ('全自动', '全自动'), ('半自动', '半自动'), ('手动', '手动'), ('便携式', '便携式'), ('拉杆式', '拉杆式'), ('研磨分体', '研磨分体'), ('研磨一体', '研磨一体')], 'required': False}), 1: ('wagtail.blocks.StructBlock', [[('tags', 0)]], {}), 2: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入颜色名称', 'required': False}), 3: ('wagtail.blocks.URLBlock', (), {'help_text': '请输入图片URL', 'required': False}), 4: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入颜色差价', 'required': False}), 5: ('wagtail.blocks.StructBlock', [[('color_name', 2), ('image_url', 3), ('color_tax', 4)]], {}), 6: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入高度（mm）', 'required': False}), 7: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入宽度（mm）', 'required': False}), 8: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入深度（mm）', 'required': False}), 9: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入重量（kg）', 'required': False}), 10: ('wagtail.blocks.IntegerBlock', (), {'help_text': '请输入最大杯子高度（mm）', 'required': False}), 11: ('wagtail.blocks.StructBlock', [[('height', 6), ('width', 7), ('depth', 8), ('weight', 9), ('cup_height', 10)]], {}), 12: ('wagtail.blocks.CharBlock', (), {'default': '220V 50Hz', 'help_text': '请输入电压', 'required': False}), 13: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入额定功率（W）', 'required': False}), 14: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入最大功率（W）', 'required': False}), 15: ('wagtail.blocks.CharBlock', (), {'help_text': '请输入最小功率（W）', 'required': False}), 16: ('wagtail.blocks.DecimalBlock', (), {'help_text': '请输入接水盘容量（L）', 'required': False}), 17: ('wagtail.blocks.StructBlock', [[('voltage', 12), ('input_power', 13), ('max_power', 14), ('min_power', 15), ('drip_tray_capacity', 16)]], {}), 18: ('wagtail.blocks.MultipleChoiceBlock', [], {'choices': [('预浸泡', '预浸泡'), ('PID/NTC温控', 'PID/NTC温控'), ('OPV稳压', 'OPV稳压'), ('萃取计时器', '萃取计时器'), ('物联网', '物联网'), ('预设萃取曲线', '预设萃取曲线'), ('适配胶囊咖啡', '适配胶囊咖啡'), ('手冲模式', '手冲模式'), ('冷萃模式', '冷萃模式'), ('定时研磨', '定时研磨'), ('定量研磨', '定量研磨')], 'required': False}), 19: ('wagtail.blocks.StructBlock', [[('tags', 18)]], {})}),
        ),
    ]
