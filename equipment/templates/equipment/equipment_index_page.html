{% extends "base.html" %}
{% load static %}
{% load wagtailcore_tags wagtailimages_tags %}
{% block body_class %}template-equipmentindexpage{% endblock %}
{% block content %}

<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
        <h1 class="text-xl text-center text-base-content">
            {{ page.title }}
        </h1>
        {% if page.intro %}
        <div class="text-sm text-center text-base-content opacity-60">
            {{ page.intro|richtext }}
        </div>
        {% endif %}
    </div>
</div>

<div class="container mx-auto px-4 py-4 lg:px-8 lg:py-8 xl:max-w-7xl">
    <div class="grid gap-12 lg:grid-cols-3">
        <div class="flex h-72 flex-col items-center justify-center gap-6 rounded-box border-2 border-base-300 p-10 text-center text-base-content [text-wrap:balance]">
            <span class="icon-[openmoji--espresso-machine] text-8xl"></span>
            <a class="btn btn-ghost group text-lg" href="/equipment/espresso/">意式咖啡机 <span class="icon-[material-symbols--arrow-forward] h-6 w-6 transition-transform duration-300 group-hover:translate-x-1 rtl:rotate-180 rtl:group-hover:-translate-x-1 md:inline-block"></span></a>
        </div>
    </div>
    <div class="divider text-base-content/30 my-20">开发中</div>
        <div class="grid gap-12 lg:grid-cols-3">
            <div class="flex h-72 flex-col items-center justify-center gap-6 rounded-box border-2 border-dashed border-base-300 p-10 text-center text-base-content/30 [text-wrap:balance]">
                <span class="icon-[carbon--construction] text-xl"></span>
                <div>磨豆机</div>
            </div>
            <div class="flex h-72 flex-col items-center justify-center gap-6 rounded-box border-2 border-dashed border-base-300 p-10 text-center text-base-content/30 [text-wrap:balance]">
                <span class="icon-[carbon--construction] text-xl"></span>
                <div>配件</div>
            </div>
            <div class="flex h-72 flex-col items-center justify-center gap-6 rounded-box border-2 border-dashed border-base-300 p-10 text-center text-base-content/30 [text-wrap:balance]">
                <span class="icon-[carbon--construction] text-xl"></span>
                <div>手冲设备</div>
            </div>
            <div class="flex h-72 flex-col items-center justify-center gap-6 rounded-box border-2 border-dashed border-base-300 p-10 text-center text-base-content/30 [text-wrap:balance]">
                <span class="icon-[carbon--construction] text-xl"></span>
                <div>更多...</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}