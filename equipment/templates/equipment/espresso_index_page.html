{% extends "base.html" %}
{% load static %}
{% load wagtailcore_tags wagtailimages_tags custom_floatformat %}
{% block body_class %}template-espressoindexpage{% endblock %}
{% block content %}
<!-- heading -->
<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
        <h1 class="text-xl text-center text-base-content">
            {{ page.title }}
        </h1>
        {% if page.intro %}
        <div class="text-sm text-center text-base-content opacity-60">
            {{ page.intro|richtext }}
        </div>
        {% endif %}
    </div>
</div>

<!-- filter + list container -->
<div class="container mx-auto flex flex-col lg:flex-row gap-4 p-4">
    <!-- filter -->
    <div x-data="{ isOpen: false }" class="lg:w-[200px] shrink-0">
        <div class="card text-base-content bg-base-100 lg:block">
            <div class="card-body p-4">
                <!-- 移动端展开按钮 (只在 lg 以下显示) -->
                <div class="lg:hidden flex justify-center">
                    <button @click="isOpen = !isOpen"
                        class="flex w-full justify-center items-center px-4 py-2">
                        <span class="icon-[oui--filter]"></span>
                        <span x-text="isOpen ? '收起筛选功能' : '展开筛选功能'" class="ml-1 font-medium"></span>
                    </button>
                </div>

                <!-- 筛选表单 -->
                <form id="filter-form" x-show="isOpen || window.innerWidth >= 1024" class="flex flex-col" method="get" action=".">
                    <div class="flex flex-col md:grid md:grid-cols-2 lg:flex lg:flex-col gap-4 mb-6">
                        <!-- 品牌选择 -->
                        <div class="form-group">
                            <label class="form-control w-full">
                                <div class="label">
                                    <span class="label-text">品牌</span>
                                </div>
                                <select name="zh_brand" id="zh_brand" class="select w-full">
                                    <option value="">全部</option>
                                    <option value="百胜图" {% if request.GET.zh_brand == '百胜图' %}selected{% endif %}>百胜图</option>
                                    <option value="柏翠" {% if request.GET.zh_brand == '柏翠' %}selected{% endif %}>柏翠</option>
                                    <option value="格米莱" {% if request.GET.zh_brand == '格米莱' %}selected{% endif %}>格米莱</option>
                                    <option value="海氏" {% if request.GET.zh_brand == '海氏' %}selected{% endif %}>海氏</option>
                                    <option value="辣妈" {% if request.GET.zh_brand == '辣妈' %}selected{% endif %}>辣妈</option>
                                    <option value="莱利特" {% if request.GET.zh_brand == '莱利特' %}selected{% endif %}>莱利特</option>
                                </select>
                            </label>
                        </div>

                        <!-- 类别选择 -->
                        <div class="form-group" x-data="{ 
                            isOpen: false,
                            selectedCount: {{ selected_machine_types|length }},
                            updateCount() {
                                this.selectedCount = document.querySelectorAll('input[name=machine_type]:checked').length;
                            }
                        }">
                            <label class="form-control w-full">
                                <div class="label">
                                    <span class="label-text">类别</span>
                                </div>
                                <!-- 触发按钮 -->
                                <button type="button" 
                                        @click="isOpen = true"
                                        class="select w-full text-left items-center">
                                    已选 <span x-text="selectedCount"></span> 项
                                </button>
                            </label>

                            <!-- 模态框 -->
                            <div x-show="isOpen" 
                                class="fixed inset-0 z-50 overflow-y-auto"
                                style="display: none;">
                                <!-- 背景遮罩 -->
                                <div class="fixed inset-0 bg-black/50" @click="isOpen = false"></div>
                                
                                <!-- 模态框内容 -->
                                <div class="relative min-h-screen flex items-center justify-center p-4">
                                    <div class="relative bg-base-100 rounded-lg w-full max-w-md p-4 max-h-[80vh] overflow-y-auto">
                                        <!-- 标题和关闭按钮 -->
                                        <div class="flex justify-between items-center mb-4">
                                            <h3 class="text-lg font-medium">选择类别</h3>
                                            <button type="button" @click="isOpen = false" class="text-base-content/60 cursor-pointer">
                                                <span class="icon-[heroicons--x-mark-20-solid] w-5 h-5"></span>
                                            </button>
                                        </div>

                                        <!-- 选项列表 -->
                                        <div class="space-y-2 grid grid-cols-2">
                                            {% for choice in machine_type_choices %}
                                            <label class="flex items-center p-2 hover:bg-base-200 rounded-lg cursor-pointer">
                                                <input type="checkbox" 
                                                    class="checkbox" 
                                                    name="machine_type" 
                                                    value="{{ choice.0 }}"
                                                    @change="updateCount()"
                                                    {% if choice.0 in selected_machine_types %}checked{% endif %}>
                                                <span class="ml-2">{{ choice.1 }}</span>
                                            </label>
                                            {% endfor %}
                                        </div>

                                        <!-- 确认按钮 -->
                                        <div class="mt-4 flex justify-end gap-2">
                                            <button type="button" 
                                                    @click="
                                                        document.querySelectorAll('input[name=machine_type]').forEach(checkbox => checkbox.checked = false);
                                                        updateCount();
                                                    "
                                                    class="btn btn-error btn-outline">
                                                清除
                                            </button>
                                            <button type="button" 
                                                    @click="isOpen = false"
                                                    class="btn btn-primary">
                                                确定
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 价格区间 -->
                        <div class="form-group col-span-1 md:col-span-2">
                            <label class="form-control w-full">
                                <div class="label">
                                    <span class="label-text">预算</span>
                                </div>
                                <div class="flex flex-col md:flex-row lg:flex lg:flex-col gap-1">
                                    <div class="flex-1">
                                        <input type="number" 
                                            id="price_min" 
                                            name="price_min" 
                                            value="{{ request.GET.price_min }}" 
                                            class="input w-full" 
                                            placeholder="最低价格" 
                                            min="0">
                                    </div>
                                    <span class="hidden lg:hidden md:block opacity-60 self-center">→</span>
                                    <span class="md:hidden lg:block text-center opacity-60">↓</span>
                                    <div class="flex-1">
                                        <input type="number" 
                                            id="price_max" 
                                            name="price_max" 
                                            value="{{ request.GET.price_max }}" 
                                            class="input w-full" 
                                            placeholder="最高价格" 
                                            min="0">
                                    </div>
                                </div>
                            </label>
                        </div>

                        <!-- 锅炉选择 -->
                        <div class="form-group">
                            <label class="form-control w-full">
                                <div class="label">
                                    <span class="label-text">锅炉</span>
                                </div>
                                <select name="boiler_type" id="boiler_type" class="select w-full">
                                    <option value="">全部</option>
                                    <option value="单锅炉" {% if request.GET.boiler_type == '单锅炉' %}selected{% endif %}>单锅炉</option>
                                    <option value="双锅炉" {% if request.GET.boiler_type == '双锅炉' %}selected{% endif %}>双锅炉</option>
                                    <option value="多锅炉" {% if request.GET.boiler_type == '多锅炉' %}selected{% endif %}>多锅炉</option>
                                    <option value="子母锅炉" {% if request.GET.boiler_type == '子母锅炉' %}selected{% endif %}>子母锅炉</option>
                                    <option value="单加热块" {% if request.GET.boiler_type == '单加热块' %}selected{% endif %}>单加热块</option>
                                    <option value="双加热块" {% if request.GET.boiler_type == '双加热块' %}selected{% endif %}>双加热块</option>
                                    <option value="混合锅炉" {% if request.GET.boiler_type == '混合锅炉' %}selected{% endif %}>混合锅炉</option>
                                </select>
                            </label>
                        </div>

                        <!-- 泵选择 -->
                        <div class="form-group">
                            <label class="form-control w-full">
                                <div class="label">
                                    <span class="label-text">水泵</span>
                                </div>
                                <select name="pump_type" id="pump_type" class="select w-full">
                                    <option value="">全部</option>
                                    <option value="旋转泵" {% if request.GET.pump_type == '旋转泵' %}selected{% endif %}>旋转泵</option>
                                    <option value="震动泵" {% if request.GET.pump_type == '震动泵' %}selected{% endif %}>震动泵</option>
                                    <option value="齿轮泵" {% if request.GET.pump_type == '齿轮泵' %}selected{% endif %}>齿轮泵</option>
                                </select>
                            </label>
                        </div>

                        <!-- 冲煮头选择 -->
                        <div class="form-group">
                            <label class="form-control w-full">
                                <div class="label">
                                    <span class="label-text">冲煮头</span>
                                </div>
                                <select name="group_head_type" id="group_head_type" class="select w-full">
                                    <option value="">全部</option>
                                    <option value="饱和式" {% if request.GET.group_head_type == '饱和式' %}selected{% endif %}>饱和式</option>
                                    <option value="半饱和式" {% if request.GET.group_head_type == '半饱和式' %}selected{% endif %}>半饱和式</option>
                                    <option value="E61" {% if request.GET.group_head_type == 'E61' %}selected{% endif %}>E61</option>
                                    <option value="电加热" {% if request.GET.group_head_type == '电加热' %}selected{% endif %}>电加热</option>
                                    <option value="传统式" {% if request.GET.group_head_type == '传统式' %}selected{% endif %}>传统式</option>
                                    <option value="拉杆式" {% if request.GET.group_head_type == '拉杆式' %}selected{% endif %}>拉杆式</option>
                                </select>
                            </label>
                        </div>

                        <!-- 功能多选下拉框 -->
                        <div class="form-group" x-data="{ 
                            isOpen: false,
                            selectedCount: {{ selected_features|length }},
                            updateCount() {
                                this.selectedCount = document.querySelectorAll('input[name=features]:checked').length;
                            }
                        }">
                            <label class="form-control w-full">
                                <div class="label">
                                    <span class="label-text">功能</span>
                                </div>
                                <!-- 触发按钮 -->
                                <button type="button" 
                                        @click="isOpen = true"
                                        class="select w-full text-left items-center">
                                    已选 <span x-text="selectedCount"></span> 项
                                </button>
                            </label>

                            <!-- 模态框 -->
                            <div x-show="isOpen" 
                                class="fixed inset-0 z-50 overflow-y-auto"
                                style="display: none;">
                                <!-- 背景遮罩 -->
                                <div class="fixed inset-0 bg-black/50" @click="isOpen = false"></div>
                                
                                <!-- 模态框内容 -->
                                <div class="relative min-h-screen flex items-center justify-center p-4">
                                    <div class="relative bg-base-100 rounded-lg w-full max-w-md p-4 max-h-[80vh] overflow-y-auto">
                                        <!-- 标题和关闭按钮 -->
                                        <div class="flex justify-between items-center mb-4">
                                            <h3 class="text-lg font-medium">选择功能</h3>
                                            <button type="button" @click="isOpen = false" class="text-base-content/60 cursor-pointer">
                                                <span class="icon-[heroicons--x-mark-20-solid] w-5 h-5"></span>
                                            </button>
                                        </div>

                                        <!-- 选项列表 -->
                                        <div class="space-y-2 grid grid-cols-2">
                                            {% for choice in features_choices %}
                                            <label class="flex items-center p-2 hover:bg-base-200 rounded-lg cursor-pointer">
                                                <input type="checkbox" 
                                                    class="checkbox" 
                                                    name="features" 
                                                    value="{{ choice.0 }}"
                                                    @change="updateCount()"
                                                    {% if choice.0 in selected_features %}checked{% endif %}>
                                                <span class="ml-2">{{ choice.1 }}</span>
                                            </label>
                                            {% endfor %}
                                        </div>

                                        <!-- 确认按钮 -->
                                        <div class="mt-4 flex justify-end gap-2">
                                            <button type="button" 
                                                    @click="
                                                        document.querySelectorAll('input[name=features]').forEach(checkbox => checkbox.checked = false);
                                                        updateCount();
                                                    "
                                                    class="btn btn-error btn-outline">
                                                清除
                                            </button>
                                            <button type="button" 
                                                    @click="isOpen = false"
                                                    class="btn btn-primary">
                                                确定
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 手控多选下拉框 -->
                        <div class="form-group" x-data="{ 
                            isOpen: false,
                            selectedCount: {{ selected_configs|length }},
                            updateCount() {
                                this.selectedCount = document.querySelectorAll('input[name=user_panel]:checked').length;
                            }
                        }">
                            <label class="form-control w-full">
                                <div class="label">
                                    <span class="label-text">手控</span>
                                </div>
                                <!-- 触发按钮 -->
                                <button type="button" 
                                        @click="isOpen = true"
                                        class="select w-full text-left items-center">
                                    已选 <span x-text="selectedCount"></span> 项
                                </button>
                            </label>

                            <!-- 模态框 -->
                            <div x-show="isOpen" 
                                class="fixed inset-0 z-50 overflow-y-auto"
                                style="display: none;">
                                <!-- 背景遮罩 -->
                                <div class="fixed inset-0 bg-black/50" @click="isOpen = false"></div>
                                
                                <!-- 模态框内容 -->
                                <div class="relative min-h-screen flex items-center justify-center p-4">
                                    <div class="relative bg-base-100 rounded-lg w-full max-w-md p-4 max-h-[80vh] overflow-y-auto">
                                        <!-- 标题和关闭按钮 -->
                                        <div class="flex justify-between items-center mb-4">
                                            <h3 class="text-lg font-medium">选择手控操作</h3>
                                            <button type="button" @click="isOpen = false" class="text-base-content/60 cursor-pointer">
                                                <span class="icon-[heroicons--x-mark-20-solid] w-5 h-5"></span>
                                            </button>
                                        </div>

                                        <!-- 选项列表 -->
                                        <div class="space-y-2">
                                            {% for choice in configs_choices %}
                                            <label class="flex items-center p-2 hover:bg-base-200 rounded-lg cursor-pointer">
                                                <input type="checkbox" 
                                                    class="checkbox" 
                                                    name="user_panel" 
                                                    value="{{ choice.0 }}"
                                                    @change="updateCount()"
                                                    {% if choice.0 in selected_configs %}checked{% endif %}>
                                                <span class="ml-2">{{ choice.1 }}</span>
                                            </label>
                                            {% endfor %}
                                        </div>

                                        <!-- 确认按钮 -->
                                        <div class="mt-4 flex justify-end gap-2">
                                            <button type="button" 
                                                    @click="
                                                        document.querySelectorAll('input[name=user_panel]').forEach(checkbox => checkbox.checked = false);
                                                        updateCount();
                                                    "
                                                    class="btn btn-error btn-outline">
                                                清除
                                            </button>
                                            <button type="button" 
                                                    @click="isOpen = false"
                                                    class="btn btn-primary">
                                                确定
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- 按钮组 -->
                    <div class="flex justify-center gap-4 pt-4">
                        <a href="https://www.kafeidazi.com/equipment/espresso/" class="btn btn-sm">重置</a>
                        <button type="submit" class="btn btn-primary btn-sm">筛选</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- list -->
    <div class="flex grow justify-center">
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-y-10 lg:gap-y-20 gap-x-14">
            {% for espresso in espressopages %}
            {% with page=espresso.specific %}
            <div class="w-72 bg-white dark:bg-base-100 shadow-md rounded-xl duration-500 hover:scale-105 hover:shadow-xl text-base-content">
                <a href="{% pageurl page %}" target="_blank">
                    {% with first_image_url=page.images.splitlines|first %}
                        <img src="{{ first_image_url }}" alt="{{ page.title }}" class="h-80 w-72 object-cover rounded-t-xl" />
                    {% endwith %}
                    <div class="px-4 py-3 w-72">
                        <span class="mr-3 text-xs opacity-60">{{ page.country }} {{ page.en_brand }} · {{ page.zh_brand }}</span>
                        <p class="text-lg font-bold truncate block capitalize">{{ page.product_name }}</p>
                        <div class="flex items-center">
                            <p class="text-lg font-semibold cursor-auto my-3">￥{{ page.price|custom_floatformat }}</p>
                            <div class="ml-auto"><span class="badge badge-accent badge-outline">{{ page.model_name }}</span></div>
                        </div>
                    </div>
                </a>
            </div>
            {% endwith %}
            {% empty %}
            <p class="text-base-content">暂无符合条件的内容。</p>
            {% endfor %}
        </div>
    </div>
</div>

<!-- pagination -->
<div class="py-2 px-1 lg:p-4 mb-2 lg:py-8 lg:px-4">
    <div class="flex items-center justify-center">
        <ul class="list-none mb-6 flex">
            {% if espressopages.has_previous %}
            <li>
                <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}&page={{ espressopages.previous_page_number }}">上一页</a>
            </li>
            {% else %}
            <li>
                <a class="pointer-events-none relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-300 transition-all duration-300">上一页</a>
            </li>
            {% endif %}
            {% for page_num in espressopages.paginator.page_range %}
            {% if espressopages.number == page_num %}
            <li aria-current="page">
                <a class="relative block rounded-sm bg-primary-100 px-3 py-1.5 text-lg font-medium text-primary transition-all duration-300">{{ page_num }}</a>
            </li>
            {% elif page_num > espressopages.number|add:'-3' and page_num < espressopages.number|add:'3' %}
            <li>
                <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}&page={{ page_num }}">{{ page_num }}</a>
            </li>
            {% endif %}
            {% endfor %}
            {% if espressopages.has_next %}
            <li>
                <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}&page={{ espressopages.next_page_number }}">下一页</a>
            </li>
            {% else %}
            <li>
                <a class="pointer-events-none relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-300 transition-all duration-300">下一页</a>
            </li>
            {% endif %}
        </ul>
    </div>
</div>

{% endblock %}