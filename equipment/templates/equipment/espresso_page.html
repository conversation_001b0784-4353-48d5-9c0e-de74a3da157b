{% extends "base.html" %}
{% load wagtailcore_tags wagtailimages_tags wagtailmarkdown custom_floatformat check_block static cache %}
{% block seo_desc %}
<meta name="description" content="咖啡搭子为您提供{{ page.en_brand }}{{ page.zh_brand }}的{{ page.product_name }}咖啡机价格、图片、产品规格、参数配置等详细参数信息。" />
{% endblock %}
{% block body_class %}template-espressopage{% endblock %}
{% block content %}
<main class="flex flex-col items-center text-base-content">

  <section class="grid grid-cols-1 lg:grid-cols-2 m-4 gap-12">
    <!-- 左侧图片展示区 -->
    <div class="flex flex-col gap-4">
      <!-- 主图片轮播 -->
      <div class="relative">
        <div class="group rounded-box border-base-300 relative overflow-hidden border h-[24rem] md:h-[28rem] lg:h-[26rem]">
          <div class="carousel flex w-full" id="main-carousel">
            <!-- 第一张图片 -->
            {% with first_image_url=page.images.splitlines|first %}
            <div id="slide1" class="carousel-item relative w-full shrink-0">
              <div class="skeleton w-full h-full absolute inset-0 bg-base-200"></div>
              <img src="{{ first_image_url }}" alt="{{ page.product_name }}" 
                   class="w-full h-full object-contain p-2 md:p-4 lg:p-4" />
            </div>
            {% endwith %}
            
            <!-- 后续图片 -->
            {% with image_list=page.images.splitlines|slice:"1:" %}
              {% for image_url in image_list %}
                <div id="slide{{ forloop.counter|add:1 }}" class="carousel-item relative w-full shrink-0">
                  <div class="skeleton w-full h-full absolute inset-0 bg-base-200"></div>
                  <img src="{{ image_url }}" alt="{{ page.product_name }}" 
                       class="w-full h-full object-contain p-2 md:p-4 lg:p-4" />
                </div>
              {% endfor %}
            {% endwith %}
          </div>

          <!-- 左右切换按钮 -->
          <div class="absolute flex justify-between transform -translate-y-1/2 left-5 right-5 top-1/2 z-10">
            <button id="prev-btn" class="btn btn-circle opacity-0 group-hover:opacity-70">❮</button> 
            <button id="next-btn" class="btn btn-circle opacity-0 group-hover:opacity-70">❯</button>
          </div>
        </div>
      </div>

      <!-- 缩略图导航 -->
      <div class="-mx-4 overflow-x-auto">
        <div class="flex flex-wrap gap-2 p-4" id="thumbnail-container">
          {% with first_image_url=page.images.splitlines|first %}
          <button class="thumbnail-btn border-base-content/15 h-16 w-16 shrink-0 cursor-pointer overflow-hidden rounded-sm border-2 active" data-slide="1">
            <img src="{{ first_image_url }}" alt="thumbnail 1" class="h-full w-full object-cover brightness-90"/>
          </button>
          {% endwith %}
          
          {% with image_list=page.images.splitlines|slice:"1:" %}
            {% for image_url in image_list %}
              <button class="thumbnail-btn border-base-content/15 h-16 w-16 shrink-0 cursor-pointer overflow-hidden rounded-sm border-2" data-slide="{{ forloop.counter|add:1 }}">
                <img src="{{ image_url }}" alt="thumbnail {{ forloop.counter|add:1 }}" class="h-full w-full object-cover brightness-90"/>
              </button>
            {% endfor %}
          {% endwith %}
        </div>
      </div>
    </div>

    <div class="flex flex-col items-center gap-6">
      <h1 class="text-3xl font-bold">{{ page.product_name }}</h1>
      <div class="max-w-lg leading-normal opacity-70">{{ page.intro|linebreaks }}</div>
      <div class="grid gap-2 px-4 md:px-0">
        <div class="grid">
          <div class="flow-root">
            <dl class="-my-3 divide-y divide-base-200 text-sm">

              <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                <dt class="font-medium">品牌</dt>
                <dd class="opacity-70 sm:col-span-2">
                  {{ page.country }} {{ page.en_brand }} · {{ page.zh_brand }}
                </dd>
              </div>

              <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                <dt class="font-medium">型号</dt>
                <dd class="opacity-70 sm:col-span-2">
                {{ page.model_name }}{% for block in page.variation_list %}, <a href="{{ block.value.landing_page }}" class="text-info" target="_blank">{{ block.value.variation }}</a>{% endfor %}
                </dd>
              </div>

              {% with color_blocks=page.product_property|filter_by_block_type:"color" %}
                {% if color_blocks %}
                  <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                    <dt class="font-medium">颜色</dt>
                    <dd class="opacity-70 sm:col-span-2">
                      {% for block in color_blocks %}
                        <button class="btn mb-2" href="#item1" data-image-url="{{ block.value.image_url }}" data-color="{{ block.value.color_name }}">
                          {{ block.value.color_name }}
                          {% if block.value.color_tax %}
                            <div class="badge badge-secondary">{% if block.value.color_tax > 0 %}+{% endif %}{{ block.value.color_tax }}￥</div>
                          {% endif %}
                        </button>
                      {% endfor %}
                    </dd>
                  </div>
                {% endif %}
              <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
              <script>
                $(document).ready(function() {
                  $('.btn').click(function() {
                    var imageUrl = $(this).data('image-url');
                    var color = $(this).data('color');
                    $('#main-image').attr('src', imageUrl);
                    // 如果需要，可以在这里添加更多的逻辑，例如更新其他元素的颜色
                  });
                });
              </script>
              {% endwith %}

              <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                <dt class="font-medium">类别</dt>
                <dd class="opacity-70 sm:col-span-2">
                  {% for block in page.product_property %}
                    {% if block.block_type == 'machine_type' %}
                      {{ block.value.tags|join:", " }}
                    {% endif %}
                  {% endfor %}
                </dd>
              </div>

              <!-- 性价比 -->
              {% with value_meter=page.value_meter %}
              {% cache 3600 value_meter page.id %}
              <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                  <dt class="font-medium">
                      <div class="flex justify-items-center gap-1">
                      <span>搭子指数</span>
                      <button class="text-info text-base" onclick="my_modal_3.showModal()"><span class="icon-[material-symbols--info-outline] cursor-pointer"></span></button>
                      </div>
                      <dialog id="my_modal_3" class="modal">
                      <div class="modal-box">
                          <form method="dialog">
                          <button class="absolute right-2 top-2"><span class="icon-[heroicons--x-mark-20-solid] w-5 h-5 cursor-pointer"></span></button>
                          </form>
                          <h3 class="text-lg font-bold">搭子指数：您的购买指南针</h3>
                          <p class="py-4">搭子指数是基于意式咖啡机的技术参数，由我们的智能系统自动计算得出的综合评分。它旨在为您提供一个性价比的量化视角，帮助您在众多产品中做出明智的选择。请注意，这个评分并不直接反映实际使用体验，而是一个辅助您决策的工具。</p>
                      </div>
                      </dialog>
                  </dt>
                  <dd class="sm:col-span-2 flex items-center gap-4">
                      <div class="gap-1 text-warning">
                          {% for i in '12345'|make_list %}
                              {% if forloop.counter <= value_meter.0 %}
                                  <span class="icon-[carbon--star-filled]"></span>
                              {% else %}
                                  <span class="icon-[carbon--star]"></span>
                              {% endif %}
                          {% endfor %}
                      </div>
                  </dd>
              </div>
              {% endcache %}
              {% endwith %}

              <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                <dt class="font-medium tooltip tooltip-info cursor-help text-left decoration-info underline decoration-dashed underline-offset-4" data-tip="指该商品历史常卖价，最新价格以商品链接为准">参考价格</dt>
                <dd class="opacity-70 sm:col-span-2">{{ page.price|custom_floatformat }} 元</dd>
              </div>

              <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                <dt class="font-medium">商品链接</dt>
                <dd class="sm:col-span-2 flex flex-col gap-4">
                  <a target="_blank" rel="noopener noreferrer" href="{{ page.campaign_link|default:page.product_link }}"
                    class="text-info link link-hover group break-all">{{ page.product_link }}
                    <span class="icon-[charm--link-external] text-sm inline-block fill-current opacity-0 group-hover:opacity-50"></span>
                  </a>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

    </div>
  </section>

  <section class="m-4">
    <div class="divider menu-title max-w-md mx-auto">详细规格</div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
      <div class="join join-vertical w-full">
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            尺寸
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              <table class="table table-pin-rows">
              {% for block in page.product_property %}
              {% if block.block_type == 'dimension' %}
                <thead><tr><th>高度（mm）</th></tr></thead>
                <tbody><tr><td>{{ block.value.height }}</td></tr></tbody>
                <thead><tr><th>宽度（mm）</th></tr></thead>
                <tbody><tr><td>{{ block.value.width }}</td></tr></tbody>
                <thead><tr><th>深度（mm）</th></tr></thead>
                <tbody><tr><td>{{ block.value.depth }}</td></tr></tbody>
                <thead><tr><th>重量（kg）</th></tr></thead>
                <tbody><tr><td>{{ block.value.weight|default_if_none:'不详' }}</td></tr></tbody>
                {% if block.value.cup_height %}
                <thead><tr><th>最大杯高（mm）</th></tr></thead>
                <tbody><tr><td>{{ block.value.cup_height }}</td></tr></tbody>
                {% endif %}
              {% endif %}
              {% endfor %}
              </table>
            </div>
          </div>
        </div>
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            外壳
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              {% for block in page.product_property %}
              {% if block.block_type == 'housing' %}
              <table class="table table-pin-rows">
                <thead><tr><th>电压</th></tr></thead>
                <tbody><tr><td>{{ block.value.voltage|default_if_none:'不详' }}</td></tr></tbody>
                {% if block.value.input_power %}
                <thead><tr><th>输入功率（W）</th></tr></thead>
                <tbody><tr><td>{{ block.value.input_power }}</td></tr></tbody>
                {% endif %}
                {% if block.value.max_power %}
                <thead><tr><th>最大功率（W）</th></tr></thead>
                <tbody><tr><td>{{ block.value.max_power }}</td></tr></tbody>
                {% endif %}
                {% if block.value.min_power %}
                <thead><tr><th>最小功率（W）</th></tr></thead>
                <tbody><tr><td>{{ block.value.min_power }}</td></tr></tbody>
                {% endif %}
                {% if block.value.drip_tray_capacity %}
                <thead><tr><th>接水盘容量（L）</th></tr></thead>
                <tbody><tr><td>{{ block.value.drip_tray_capacity }}</td></tr></tbody>
                {% endif %}
              </table>
              {% endif %}
              {% endfor %}
            </div>
          </div>
        </div>
        {% if page.material %}
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            材质
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              <div>{{ page.material|linebreaks }}</div>
            </div>
          </div>
        </div>
        {% endif %}
      </div>
      <div class="join join-vertical w-full">
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            锅炉
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              <table class="table table-pin-rows">
              {% for block in page.product_parts %}
              {% if block.block_type == 'boiler' %}
                <thead><tr><th>类型</th></tr></thead>
                <tbody><tr><td>{{ block.value.boiler_type }}</td></tr></tbody>
                {% if block.value.brew_size %}
                <thead><tr><th>冲煮锅炉容量（L）</th></tr></thead>
                <tbody><tr><td>{{ block.value.brew_size }}</td></tr></tbody>
                {% endif %}
                {% if block.value.steam_size %}
                <thead><tr><th>蒸汽锅炉容量（L）</th></tr></thead>
                <tbody><tr><td>{{ block.value.steam_size }}</td></tr></tbody>
                {% endif %}
                {% if block.value.brew_power %}
                <thead><tr><th>冲煮功率（W）</th></tr></thead>
                <tbody><tr><td>{{ block.value.brew_power }}</td></tr></tbody>
                {% endif %}
                {% if block.value.steam_power %}
                <thead><tr><th>蒸汽功率（W）</th></tr></thead>
                <tbody><tr><td>{{ block.value.steam_power }}</td></tr></tbody>
                {% endif %}
                {% if block.value.warmup_time %}
                <thead><tr><th>预热时长</th></tr></thead>
                <tbody><tr><td>{{ block.value.warmup_time }}</td></tr></tbody>
                {% endif %}
              {% endif %}
              {% endfor %}
              </table>
            </div>
          </div>
        </div>
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            水泵
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              <table class="table table-pin-rows">
                {% for block in page.product_parts %}
                {% if block.block_type == 'pump' %}
                <thead><tr><th>水泵类型</th></tr></thead>
                <tbody><tr><td>{{ block.value.pump_type }}</td></tr></tbody>
                <thead><tr><th>水泵个数</th></tr></thead>
                <tbody><tr><td>{{ block.value.pump_number }}</td></tr></tbody>
                <thead><tr><th>水泵压力（bar）</th></tr></thead>
                <tbody><tr><td>{{ block.value.brew_pressure|default_if_none:'不详' }}</td></tr></tbody>
              {% endif %}
              {% endfor %}
              </table>
            </div>
          </div>
        </div>
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            供水
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              <table class="table table-pin-rows">
              {% for block in page.product_parts %}
              {% if block.block_type == 'water_supply' %}
                <thead><tr><th>进水方式</th></tr></thead>
                <tbody><tr><td>{% for tag in block.value.tags %}{{ tag|linebreaks }}{% endfor %}</td></tr></tbody>
                {% if block.value.reservoir %}
                <thead><tr><th>水箱容量（L）</th></tr></thead>
                <tbody><tr><td>{{ block.value.reservoir }}</td></tr></tbody>
                {% endif %}
                <thead><tr><th>是否可接进水管</th></tr></thead>
                <tbody><tr><td>{{ block.value.plumbed|yesno:"✅,❌" }}</td></tr></tbody>
                <thead><tr><th>是否可接下水管</th></tr></thead>
                <tbody><tr><td>{{ block.value.drainable|yesno:"✅,❌" }}</td></tr></tbody>
              {% endif %}
              {% endfor %}
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="join join-vertical w-full">
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            冲煮头
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              <table class="table table-pin-rows">
              {% for block in page.product_parts %}
              {% if block.block_type == 'group_head' %}
                <thead><tr><th>类型</th></tr></thead>
                <tbody><tr><td>{{ block.value.group_head_type }}</td></tr></tbody>
                <thead><tr><th>个数</th></tr></thead>
                <tbody><tr><td>{{ block.value.numbers }}</td></tr></tbody>
              {% endif %}
              {% endfor %}
              </table>
            </div>
          </div>
        </div>
        {% with is_auto=page.product_property|filter_by_block_type:"machine_type"|first %}
        {% if "全自动" not in is_auto.value.tags %}
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            手柄
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              <table class="table table-pin-rows">
                {% for block in page.product_parts %}
                {% if block.block_type == 'portafilter' %}
                <thead><tr><th>原装手柄规格</th></tr></thead>
                <tbody><tr><td>{{ block.value.portafilter_inc|default_if_none:'不详' }}</td></tr></tbody>
                {% if block.value.basket_inc %}
                <thead><tr><th>原装粉碗规格</th></tr></thead>
                <tbody><tr><td>{{ block.value.basket_inc }}</td></tr></tbody>
                {% endif %}
                <thead><tr><th>粉碗尺寸</th></tr></thead>
                <tbody><tr><td>{{ block.value.basket_size }}</td></tr></tbody>
              {% endif %}
              {% endfor %}
              </table>
            </div>
          </div>
        </div>
        {% endif %}
        {% endwith %}
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            蒸汽棒
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              <table class="table table-pin-rows">
              {% for block in page.product_parts %}
              {% if block.block_type == 'steam_wand' %}
                {% for tag in block.value.tags %}
                <div class="badge badge-outline badge-base-content mb-4 mr-2">{{ tag }}</div>
                {% endfor %}
                <thead><tr><th>孔数</th></tr></thead>
                <tbody><tr><td>{{ block.value.holes|default_if_none:'不详' }}</td></tr></tbody>
                {% if block.value.hole_size %}
                <thead><tr><th>孔径（mm）</th></tr></thead>
                <tbody><tr><td>{{ block.value.hole_size }}</td></tr></tbody>
                {% endif %}
                {% if block.value.wand_length %}
                <thead><tr><th>蒸汽棒长度（mm）</th></tr></thead>
                <tbody><tr><td>{{ block.value.wand_length }}</td></tr></tbody>
                {% endif %}
                <thead><tr><th>是否有热水口</th></tr></thead>
                <tbody><tr><td>{{ block.value.hot_water|yesno:"✅,❌" }}</td></tr></tbody>
              {% endif %}
              {% endfor %}
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="join join-vertical w-full">
        {% with grinder_blocks=page.product_parts|filter_by_block_type:"grinder" %}
        {% if grinder_blocks %}
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            研磨器
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              <table class="table table-pin-rows">
              {% for block in page.product_parts %}
              {% if block.block_type == 'grinder' %}
                {% for tag in block.value.tags %}
                <div class="badge badge-outline badge-base-content mb-4 mr-2">{{ tag }}</div>
                {% endfor %}
                <thead><tr><th>磨盘类型</th></tr></thead>
                <tbody><tr><td>{{ block.value.burr_type }}</td></tr></tbody>
                <thead><tr><th>豆仓个数</th></tr></thead>
                <tbody><tr><td>{{ block.value.hopper_number }}</td></tr></tbody>
                <thead><tr><th>豆仓容量（g）</th></tr></thead>
                <tbody><tr><td>{{ block.value.hopper_capacity|default_if_none:'不详' }}</td></tr></tbody>
                <thead><tr><th>磨盘个数</th></tr></thead>
                <tbody><tr><td>{{ block.value.burr_number|default_if_none:'不详' }}</td></tr></tbody>
                <thead><tr><th>研磨挡位</th></tr></thead>
                <tbody><tr><td>{{ block.value.clicks|default_if_none:'不详' }}</td></tr></tbody>
                <thead><tr><th>磨芯尺寸（mm）</th></tr></thead>
                <tbody><tr><td>{{ block.value.burr_size|default_if_none:'不详' }}</td></tr></tbody>
                {% if block.value.area %}
                <thead><tr><th>研磨面积（mm²）</th></tr></thead>
                <tbody><tr><td>{{ block.value.area }}</td></tr></tbody>
                {% endif %}
                {% if block.value.speed %}
                <thead><tr><th>研磨速度（g/s）</th></tr></thead>
                <tbody><tr><td>{{ block.value.speed }}</td></tr></tbody>
                {% endif %}
                <thead><tr><th>是否可拆卸磨盘</th></tr></thead>
                <tbody><tr><td>{{ block.value.removable_burr|yesno:"✅,❌" }}</td></tr></tbody>
              {% endif %}
              {% endfor %}
              </table>
            </div>
          </div>
        </div>
        {% endif %}
        {% endwith %}
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            更多功能
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              <table class="table table-pin-rows">
              {% for block in page.product_property %}
              {% if block.block_type == 'features' %}
              {% for tag in block.value.tags %}
              <div class="badge badge-outline badge-base-content mb-4 mr-2">{{ tag }}</div>
              {% endfor %}
              {% endif %}
              {% endfor %}
              </table>
            </div>
          </div>
        </div>
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            界面和操作
          </div>
          <div class="collapse-content">
            <div class="overflow-x-auto">
              {% for block in page.product_parts %}
              {% if block.block_type == 'user_panel' %}
              {% for tag in block.value.tags %}
              <div class="badge badge-outline badge-base-content mb-4 mr-2">{{ tag }}</div>
              {% endfor %}
              <table class="table table-pin-rows">
                {% if block.value.switch %}
                <thead><tr><th>开关</th></tr></thead>
                <tbody><tr><td>{{ block.value.switch|linebreaks }}</td></tr></tbody>
                {% endif %}
                {% if block.value.button %}
                <thead><tr><th>按钮</th></tr></thead>
                <tbody><tr><td>{{ block.value.button|linebreaks }}</td></tr></tbody>
                {% endif %}
                {% if block.value.indicator %}
                <thead><tr><th>指示灯</th></tr></thead>
                <tbody><tr><td>{{ block.value.indicator|linebreaks }}</td></tr></tbody>
                {% endif %}
                {% if block.value.menu %}
                <thead><tr><th>菜单</th></tr></thead>
                <tbody><tr><td>{{ block.value.menu|linebreaks }}</td></tr></tbody>
                {% endif %}
                {% if block.value.gauge %}
                <thead><tr><th>仪表盘</th></tr></thead>
                <tbody><tr><td>{{ block.value.gauge|linebreaks }}</td></tr></tbody>
                {% endif %}
                {% if block.value.stick %}
                <thead><tr><th>拨杆/旋钮</th></tr></thead>
                <tbody><tr><td>{{ block.value.stick|linebreaks }}</td></tr></tbody>
                {% endif %}
              </table>
              {% endif %}
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  {% if page.package_list %}
  <section class="m-4">
    <div class="divider menu-title max-w-md mx-auto">装箱清单</div>
    <div class="break-all prose prose-stone">{{ page.package_list|markdown }}</div>
  </section>
  {% endif %}

  {% if page.resource_list %}
  <section class="m-4">
    <div class="divider menu-title max-w-md mx-auto">资源链接</div>
      <ul class="flex flex-col gap-8 py-8">
          {% for res in page.resource_list %}
          <li class="mx-8">
              <a class="group relative flex items-center justify-between overflow-hidden rounded-box p-4 duration-300 hover:bg-base-200" href="{{ res.value.res_link }}" target="_blank" rel="noopener noreferrer">
                <div>
                  <h3 class="text-xl font-bold group-hover:underline">{{ res.value.res_name }}</h3>
                  {% if res.value.note %}
                  <p class="text-sm font-bold text-base-content/60">{{ res.value.note }}</p>
                  {% endif %}
                </div>
              </a>
          </li>
          {% endfor %}
      </ul>
    </div>
  </section>
  {% endif %}

  {% if page.faq_list %}
  <section class="m-4">
    <div class="divider menu-title max-w-md mx-auto">常见问题</div>
      <div class="{% if page.faq_list|length > 1 %}join join-vertical w-full{% endif %}">
        {% for faqs in page.faq_list %}
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            {{ faqs.value.question }}
          </div>
          <div class="collapse-content prose prose-stone">
            {{ faqs.value.answer|richtext }}
          </div>
        </div>
        {% endfor %}
    </div>
  </section>
  {% endif %}

</main>
{% endblock %}
{% block extra_js %}
<script type="text/javascript" src="{% static 'js/shblbt.js' %}"></script>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/shblbt.css' %}">
{% endblock %}