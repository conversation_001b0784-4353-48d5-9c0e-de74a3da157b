from django.db import models
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from wagtail.models import Page
from wagtail.admin.panels import FieldPanel, MultiFieldPanel
from wagtail.search import index
from wagtailmarkdown.fields import MarkdownField
from django.utils.translation import gettext_lazy as _
from datetime import datetime
from wagtail.fields import StreamField
from django.db.models import Q
from .blocks import ProductPropertyBlock, ProductPartsBlock, ResourceListBlock, FaqListBlock, VariationListBlock
from django.core.validators import MinValueValidator

# 咖啡设备列表
class EquipmentIndexPage(Page):
    intro = MarkdownField(blank=True)

    def get_context(self, request):
        context = super().get_context(request)
        espresso = EspressoPage.objects.live().order_by('-date')[:30]
        context['espresso'] = espresso
        return context

    content_panels = Page.content_panels + [
        FieldPanel('intro', classname="full")
    ]

# 意式咖啡机页面
class EspressoPage(Page):
    # 发布信息
    date = models.DateTimeField("发布日期", blank=True, null=True, default=datetime.today)

    # 产品描述
    product_name = models.CharField(_('产品名称'), blank=True, null=True, max_length=255)
    model_name = models.CharField(_('型号'), blank=True, null=True, max_length=255)
    intro = models.TextField(_('简介'), blank=True, null=True)
    images = models.TextField(_('图片集'), blank=True, null=True)

    # 品牌方
    zh_brand = models.CharField(_('中文品牌名'), blank=True, null=True, max_length=255)
    en_brand = models.CharField(_('英文品牌名'), blank=True, null=True, max_length=255)
    country = models.CharField(_('品牌国别（非产地），请输入emoji旗帜符号'), blank=True, null=True, max_length=255)

    # 商品信息
    price = models.DecimalField(_('参考价格'), blank=True, null=True, default=0, max_digits=10, decimal_places=2, help_text=_('请输入参考价格，单位为元'), validators=[MinValueValidator(0)])
    product_link = models.CharField(_('商品链接'), blank=True, null=True, help_text=_('请输入商品链接的URL链接'))
    campaign_link = models.TextField(_('返利链接'), blank=True, null=True, help_text=_('请输入商品链接相关电商联盟的返利URL'))

    # 产品属性
    product_property = StreamField(ProductPropertyBlock(), blank=True)

    # 部件属性
    product_parts = StreamField(ProductPartsBlock(), blank=True)
    material = models.TextField(_('各部位材质'), blank=True, null=True)

    # 装箱清单，资源/FAQ/版本列表
    package_list = MarkdownField(blank=True)
    resource_list = StreamField(ResourceListBlock(), blank=True)
    faq_list = StreamField(FaqListBlock(), blank=True)
    variation_list = StreamField(VariationListBlock(), blank=True)

    # 可搜索字段
    search_fields = Page.search_fields + [
        index.SearchField('title'),
        index.SearchField('intro'),
    ]

    # 后台面板组织字段
    content_panels = Page.content_panels + [
        FieldPanel('date'),
        MultiFieldPanel([
            FieldPanel('zh_brand'),
            FieldPanel('en_brand'),
            FieldPanel('country'),
        ], heading="品牌方"),
        # 商品信息
        MultiFieldPanel([
            FieldPanel('price'),
            FieldPanel('product_link'),
            FieldPanel('campaign_link'),
        ], heading=_('商品信息')),
        MultiFieldPanel([
            FieldPanel('product_name'),
            FieldPanel('model_name'),
            FieldPanel('intro'),
            FieldPanel('images'),
        ], heading="产品描述"),
        FieldPanel('package_list', heading="装箱清单"),
        FieldPanel('material', heading="材质清单"),
        FieldPanel('product_property', heading="产品属性"),
        FieldPanel('product_parts', heading="部件属性"),
        FieldPanel('resource_list', heading="资源列表"),
        FieldPanel('faq_list', heading="FAQs列表"),
        FieldPanel('variation_list', heading="同系列版本列表"),
    ]
    
    def value_meter(self):
        score = 0
        max_score = 100
        
        # 提高基础分至15分
        base_score = 15
        score += base_score

        # 锅炉分数调整 (最高12分)
        if self.product_parts:
            for block in self.product_parts:
                if block.block_type == 'boiler':
                    boiler_type = block.value.get('boiler_type')
                    if boiler_type == '多锅炉':
                        score += 12
                    elif boiler_type == '双锅炉':
                        score += 10
                    elif boiler_type == '混合锅炉':
                        score += 9
                    elif boiler_type == '子母锅炉':
                        score += 8
                    elif boiler_type == '单锅炉':
                        score += 6
                    elif boiler_type == '多加热块':
                        score += 5
                    elif boiler_type == '双加热块':
                        score += 4
                    break

        # 水泵分数调整 (最高10分)
        if self.product_parts:
            for block in self.product_parts:
                if block.block_type == 'pump':
                    pump_type = block.value.get('pump_type')
                    if pump_type != '震动泵':
                        score += 8
                    pump_number = block.value.get('pump_number')
                    if pump_number != '1':
                        score += 2

        # 附加功能评分调整 (最高15分)
        if self.product_property:
            for block in self.product_property:
                if block.block_type == 'features':
                    tags = block.value.get('tags', [])
                    if 'OPV稳压' in tags:
                        score += 3
                    if '萃取计时器' in tags:
                        score += 2
                    if '物联网' in tags:
                        score += 2
                    if '预设萃取曲线' in tags:
                        score += 2
                    if '适配胶囊咖啡' in tags:
                        score += 2
                    if '定量研磨' in tags:
                        score += 2
                    if '预浸泡' in tags:
                        score += 2
                    break

        # 控制界面功能评分调整 (最高18分)
        if self.product_parts:
            for block in self.product_parts:
                if block.block_type == 'user_panel':
                    tags = block.value.get('tags', [])
                    if '压力表' in tags:
                        score += 3
                    if '杯量控制' in tags:
                        score += 2
                    if '可调萃取温度' in tags:
                        score += 3
                    if '可调萃取压力' in tags:
                        score += 2
                    if '可调萃取流量' in tags:
                        score += 2
                    if '可调预浸泡时长' in tags:
                        score += 2
                    if '可调预浸泡水量' in tags:
                        score += 2
                    if '可调OPV峰值' in tags:
                        score += 2
                    break

        # 价格系数调整 - 更温和的价格惩罚
        if self.price:
            price = float(self.price)
            price_multiplier = 1.0
            
            # 获取机器类型
            machine_types = []
            if self.product_property:
                for block in self.product_property:
                    if block.block_type == 'machine_type':
                        machine_types = block.value.get('tags', [])
                        break
            
            # 进口产品价格系数调整
            is_imported = self.country != '🇨🇳'
            
            # 商用机器价格系数
            if '商用' in machine_types:
                if is_imported:
                    if price <= 9999:
                        price_multiplier = 1.3
                    elif price <= 14999:
                        price_multiplier = 1.2
                    elif price <= 19999:
                        price_multiplier = 1.1
                    elif price <= 29999:
                        price_multiplier = 1.0
                    else:
                        price_multiplier = 0.95
                else:
                    if price <= 5999:
                        price_multiplier = 1.3
                    elif price <= 9999:
                        price_multiplier = 1.2
                    elif price <= 14999:
                        price_multiplier = 1.1
                    elif price <= 19999:
                        price_multiplier = 1.0
                    elif price <= 29999:
                        price_multiplier = 0.95
                    else:
                        price_multiplier = 0.9
            
            # 家用+小型商用价格系数
            elif '小型商用' in machine_types and '家用' in machine_types:
                if is_imported:
                    if price <= 4999:
                        price_multiplier = 1.5
                    elif price <= 5999:
                        price_multiplier = 1.4
                    elif price <= 6999:
                        price_multiplier = 1.3
                    elif price <= 7999:
                        price_multiplier = 1.2
                    elif price <= 8999:
                        price_multiplier = 1.1
                    else:
                        price_multiplier = 1.0
                else:
                    if price <= 2999:
                        price_multiplier = 1.5
                    elif price <= 3999:
                        price_multiplier = 1.4
                    elif price <= 4999:
                        price_multiplier = 1.3
                    elif price <= 5999:
                        price_multiplier = 1.2
                    elif price <= 6999:
                        price_multiplier = 1.1
                    else:
                        price_multiplier = 1.0
            
            # 纯家用机器价格系数
            else:
                if is_imported:
                    if price <= 999:
                        price_multiplier = 1.7
                    elif price <= 1499:
                        price_multiplier = 1.6
                    elif price <= 1999:
                        price_multiplier = 1.5
                    elif price <= 2499:
                        price_multiplier = 1.4
                    elif price <= 2999:
                        price_multiplier = 1.3
                    elif price <= 3499:
                        price_multiplier = 1.2
                    elif price <= 3999:
                        price_multiplier = 1.1
                    else:
                        price_multiplier = 1.0
                else:
                    if price <= 599:
                        price_multiplier = 1.7
                    elif price <= 999:
                        price_multiplier = 1.6
                    elif price <= 1499:
                        price_multiplier = 1.5
                    elif price <= 1999:
                        price_multiplier = 1.4
                    elif price <= 2499:
                        price_multiplier = 1.3
                    elif price <= 2999:
                        price_multiplier = 1.2
                    elif price <= 3499:
                        price_multiplier = 1.1
                    else:
                        price_multiplier = 1.0

            # 特殊类型调整 - 更温和的调整系数
            if '全自动' in machine_types:
                if price <= 3499:
                    price_multiplier *= 1.7
                else:
                    price_multiplier *= 1.6
            if '便携式' in machine_types:
                if price <= 999:
                    price_multiplier *= 1.2
                elif price <= 1499:
                    price_multiplier *= 1.1
                else:
                    price_multiplier *= 1.0
            if '研磨一体' in machine_types:
                if price <= 2999:
                    price_multiplier *= 1.1
                else:
                    price_multiplier *= 0.95

        score *= price_multiplier

        # 确保最终分数不超过100
        final_score = min(round(score, 1), 100)
        
        # 返回数字等级(1-5)、文字描述和具体分数
        if final_score >= 70:
            return 5, "很高", final_score
        elif final_score >= 55:
            return 4, "较高", final_score
        elif final_score >= 45:
            return 3, "一般", final_score
        elif final_score >= 35:
            return 2, "较低", final_score
        else:
            return 1, "很低", final_score

# 意式咖啡机列表页面
class EspressoIndexPage(Page):
    intro = MarkdownField(blank=True)

    content_panels = Page.content_panels + [
        FieldPanel('intro', classname="full")
    ]

    def get_context(self, request):
        context = super().get_context(request)
        queryset = EspressoPage.objects.child_of(self).live().order_by('-date')

        # 获取筛选参数
        boiler_type = request.GET.get('boiler_type')
        group_head_type = request.GET.get('group_head_type')
        pump_type = request.GET.get('pump_type')
        machine_type = request.GET.get('machine_type')
        water_supply_type = request.GET.get('water_supply_type')
        zh_brand = request.GET.get('zh_brand')
        price_min = request.GET.get('price_min')
        price_max = request.GET.get('price_max')
        features = request.GET.getlist('features')
        configs = request.GET.getlist('user_panel')

        # 应用筛选条件
        if boiler_type:
            # 使用 contains 来搜索 StreamField 中的数据
            queryset = queryset.filter(product_parts__contains=[{
                'type': 'boiler',
                'value': {'boiler_type': boiler_type}
            }])
        
        if group_head_type:
            queryset = queryset.filter(product_parts__contains=[{
                'type': 'group_head',
                'value': {'group_head_type': group_head_type}
            }])
        
        if pump_type:
            queryset = queryset.filter(product_parts__contains=[{
                'type': 'pump',
                'value': {'pump_type': pump_type}
            }])

        # 获取已选择的机器类型
        machine_types = request.GET.getlist('machine_type')
        if machine_types:
            for machine_type in machine_types:
                queryset = queryset.filter(product_property__contains=[{
                    'type': 'machine_type',
                    'value': {'tags': [machine_type]}
                }])

        # 添加已选择的机器类型数量到上下文
        context['selected_machine_types'] = machine_types

        if water_supply_type:
            queryset = queryset.filter(product_parts__contains=[{
                'type': 'water_supply',
                'value': {'tags': [water_supply_type]}
            }])

        if zh_brand:
            queryset = queryset.filter(zh_brand=zh_brand)
        
        # 处理价格筛选
        if price_min == '':
            price_min = 0
        if price_min is not None:
            queryset = queryset.filter(price__gte=price_min)
        
        if price_max:  # 如果有最高价格，则筛选
            queryset = queryset.filter(price__lte=price_max)

        # 处理功能筛选
        if features:
            for feature in features:
                queryset = queryset.filter(product_property__contains=[{
                    'type': 'features',
                    'value': {'tags': [feature]}
                }])

        # 将选中的功能传递到模板
        context['selected_features'] = features

        # 处理手控筛选
        if configs:
            for config in configs:
                queryset = queryset.filter(product_parts__contains=[{
                    'type': 'user_panel',
                    'value': {'tags': [config]}
                }])

        # 将选中的功能传递到模板
        context['selected_configs'] = configs
        
        # 将处理后的价格值传回模板
        context['price_min'] = price_min if price_min != 0 else ''
        context['price_max'] = price_max
        
        # 分页处理
        paginator = Paginator(queryset, 12)
        page = request.GET.get('page')
        try:
            queryset = paginator.page(page)
        except PageNotAnInteger:
            queryset = paginator.page(1)
        except EmptyPage:
            queryset = paginator.page(paginator.num_pages)

        context['espressopages'] = queryset
        context['features_choices'] = [
            ('预浸泡', '预浸泡'),
            ('PID/NTC温控', 'PID/NTC温控'),
            ('OPV稳压', 'OPV稳压'),
            ('萃取计时器', '萃取计时器'),
            ('物联网', '物联网'),
            ('预设萃取曲线', '预设萃取曲线'),
            ('适配胶囊咖啡', '适配胶囊咖啡'),
            ('手冲模式', '手冲模式'),
            ('冷萃模式', '冷萃模式'),
        ]
        context['machine_type_choices'] = [
            ('家用', '家用'),
            ('商用', '商用'),
            ('小型商用', '小型商用'),
            ('全自动', '全自动'),
            ('半自动', '半自动'),
            ('手动', '手动'),
            ('便携式', '便携式'),
            ('拉杆式', '拉杆式'),
            ('研磨分体', '研磨分体'),
            ('研磨一体', '研磨一体'),
        ]
        context['configs_choices'] = [
            ('杯量控制', '杯量控制'),
            ('可调萃取温度', '可调萃取温度'),
            ('可调萃取压力', '可调萃取压力'),
            ('可调萃取流量', '可调萃取流量'),
            ('可调OPV峰值', '可调OPV峰值'),
            ('可调预浸泡时长', '可调预浸泡时长'),
            ('可调预浸泡水量', '可调预浸泡水量'),
            ('可调蒸汽温度', '可调蒸汽温度'),
        ]
        return context