from django.shortcuts import render
from .models import EspressoPage, EspressoIndexPage
from wagtail.models import Page

def espresso_page(request, pk):
    page = Page.objects.live().get(pk=pk)
    if isinstance(page, EspressoPage):
        context = {
            'page': page,
        }
        return render(request, 'espresso_page.html', context)
    else:
        return render(request, 'error.html', {}, status=404)
