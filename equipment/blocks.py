from wagtail.blocks import Struct<PERSON>lock, TextBlock, ChoiceBlock, MultipleChoiceBlock, ListBlock, CharBlock, URLBlock, DecimalBlock, RichTextBlock, StreamBlock, BooleanBlock, IntegerBlock

# 意式咖啡机

## 产品属性

### 机子类型
class MachineTypeBlock(StructBlock):
    tags = MultipleChoiceBlock(choices=[
        ('家用', '家用'),
        ('商用', '商用'),
        ('小型商用', '小型商用'),
        ('研磨分体', '研磨分体'),
        ('研磨一体', '研磨一体'),
        ('半自动', '半自动'),
        ('全自动', '全自动'),
        ('手动', '手动'),
        ('便携式', '便携式'),
        ('拉杆式', '拉杆式'),
    ], required=False)

### 颜色
class ColorItemBlock(StructBlock):
    color_name = CharBlock(required=False, help_text="请输入颜色名称")
    image_url = URLBlock(required=False, help_text="请输入图片URL")
    color_tax = DecimalBlock(required=False, help_text="请输入颜色差价")

### 尺寸
class DimensionBlock(StructBlock):
    height = IntegerBlock(required=False, help_text="请输入高度（mm）")
    width = IntegerBlock(required=False, help_text="请输入宽度（mm）")
    depth = IntegerBlock(required=False, help_text="请输入深度（mm）")
    weight = DecimalBlock(required=False, help_text="请输入重量（kg）")
    cup_height = IntegerBlock(required=False, help_text="请输入最大杯子高度（mm）")

### 外壳
class HousingBlock(StructBlock):
    voltage = CharBlock(required=False, default='220V 50Hz', help_text="请输入电压")
    input_power = CharBlock(required=False, help_text="请输入额定功率（W）")
    max_power = CharBlock(required=False, help_text="请输入最大功率（W）")
    min_power = CharBlock(required=False, help_text="请输入最小功率（W）")
    drip_tray_capacity = DecimalBlock(required=False, help_text="请输入接水盘容量（L）")

### 其他功能
class FeatureCateBlock(StructBlock):
    tags = MultipleChoiceBlock(choices=[
        ('预浸泡', '预浸泡'),
        ('PID/NTC温控', 'PID/NTC温控'),
        ('OPV稳压', 'OPV稳压'),
        ('萃取计时器', '萃取计时器'),
        ('物联网', '物联网'),
        ('预设萃取曲线', '预设萃取曲线'),
        ('适配胶囊咖啡', '适配胶囊咖啡'),
        ('手冲模式', '手冲模式'),
        ('冷萃模式', '冷萃模式'),
        ('定时研磨', '定时研磨'),
        ('定量研磨', '定量研磨'),
    ], required=False)

### 汇总
class ProductPropertyBlock(StreamBlock):
    machine_type = MachineTypeBlock()
    color = ColorItemBlock()
    dimension = DimensionBlock()
    housing = HousingBlock()
    features = FeatureCateBlock()

## 部件属性

### 冲煮头
class GroupHeadBlock(StructBlock):
    group_head_type = ChoiceBlock(choices=[
        ('E61', 'E61'),
        ('电加热', '电加热'),
        ('拉杆式', '拉杆式'),
        ('饱和式', '饱和式'),
        ('半饱和式', '半饱和式'),
        ('传统式', '传统式'),
    ], required=False)
    numbers = IntegerBlock(default=1, required=False, help_text="请输入冲煮头个数")

### 手柄
class PortafilterBlock(StructBlock):
    portafilter_inc = CharBlock(required=False, help_text="请输入原装手柄规格")
    basket_inc = CharBlock(required=False, help_text="请输入原装粉碗规格")
    basket_size = ChoiceBlock(choices=[
        ('51mm', '51mm'),
        ('52mm', '52mm'),
        ('53mm', '53mm'),
        ('54mm', '54mm'),
        ('58mm', '58mm'),
    ], required=False)

### 水泵
class PumpBlock(StructBlock):
    pump_type = ChoiceBlock(choices=[
        ('旋转泵', '旋转泵'),
        ('震动泵', '震动泵'),
        ('齿轮泵', '齿轮泵'),
    ], required=False)
    pump_number = IntegerBlock(default=1, required=False, help_text="请输入水泵个数")
    brew_pressure = IntegerBlock(required=False, help_text="请输入宣传泵压（bar）")

### 给水系统
class WaterSupplyBlock(StructBlock):
    reservoir = DecimalBlock(required=False, help_text="请输入水箱容量（L）")
    plumbed = BooleanBlock(required=False, help_text="是否可接进水管")
    drainable = BooleanBlock(required=False, help_text="是否可接下水管")
    tags = MultipleChoiceBlock(choices=[
        ('自动上水', '自动上水'),
        ('水箱上水', '水箱上水'),
        ('双进水模式', '双进水模式'),
        ('选配自动上水', '选配自动上水'),
    ], required=False)

### 锅炉/加热块
class BoilerBlock(StructBlock):
    boiler_type = ChoiceBlock(choices=[
        ('单锅炉', '单锅炉'),
        ('双锅炉', '双锅炉'),
        ('多锅炉', '多锅炉'),
        ('子母锅炉', '子母锅炉'),
        ('单加热块', '单加热块'),
        ('双加热块', '双加热块'),
        ('多加热块', '多加热块'),
        ('混合锅炉', '混合锅炉'),
    ], required=False)
    brew_size = DecimalBlock(required=False, help_text="请输入冲煮锅炉容量（L）")
    steam_size = DecimalBlock(required=False, help_text="请输入蒸气锅炉容量（L）")
    brew_power = IntegerBlock(required=False, help_text="请输入冲煮锅炉功率（W）")
    steam_power = IntegerBlock(required=False, help_text="请输入蒸气锅炉功率（W）")
    warmup_time = CharBlock(required=False, help_text="请输入带单位的预热时长")

### 蒸气棒
class PitcherBlock(StructBlock):
    holes = IntegerBlock(required=False, help_text="请输入孔数")
    hole_size = DecimalBlock(required=False, help_text="请输入孔径（mm）")
    wand_length = DecimalBlock(required=False, help_text="请输入蒸气棒长度（mm）")
    hot_water = BooleanBlock(required=False, help_text="是否有热水口")
    tags = MultipleChoiceBlock(choices=[
        ('自动打奶', '自动打奶'),
        ('感温即停', '感温即停'),
        ('万向旋转', '万向旋转'),
        ('起泡外管', '起泡外管'),
        ('防烫管', '防烫管'),
    ], required=False)

### 控制板
class InterfaceBlock(StructBlock):
    switch = TextBlock(required=False, help_text="有哪些开关")
    button = TextBlock(required=False, help_text="有哪些按钮")
    indicator = TextBlock(required=False, help_text="有哪些指示灯")
    menu = TextBlock(required=False, help_text="有哪些菜单")
    gauge = TextBlock(required=False, help_text="有哪些仪表")
    stick = TextBlock(required=False, help_text="有哪些拨杆/旋钮")
    tags = MultipleChoiceBlock(choices=[
        ('实体按钮', '实体按钮'),
        ('触控屏', '触控屏'),
        ('电子显示屏', '电子显示屏'),
        ('温度仪', '温度仪'),
        ('压力表', '压力表'),
        ('杯量控制', '杯量控制'),
        ('可调蒸汽温度', '可调蒸汽温度'),
        ('可调萃取温度', '可调萃取温度'),
        ('可调萃取压力', '可调萃取压力'),
        ('可调萃取流量', '可调萃取流量'),
        ('可调预浸泡时长', '可调预浸泡时长'),
        ('可调预浸泡水量', '可调预浸泡水量'),
        ('可调OPV峰值', '可调OPV峰值'),
        ('内置电子秤', '内置电子秤'),
    ], required=False)

### 研磨器
class GrinderBlock(StructBlock):
    burr_type = ChoiceBlock(choices=[
        ('平刀', '平刀'),
        ('锥刀', '锥刀'),
        ('鬼齿', '鬼齿'),
        ('桨叶式', '桨叶式'),
    ], required=False, help_text="请输入刀盘类型")
    hopper_number = IntegerBlock(required=False, help_text="请输入豆仓个数")
    hopper_capacity = IntegerBlock(required=False, help_text="请输入豆仓容量（g）")
    burr_number = IntegerBlock(default=1, required=False, help_text="请输入研磨器个数")
    clicks = IntegerBlock(required=False, help_text="请输入研磨挡数")
    burr_size = IntegerBlock(required=False, help_text="请输入磨芯尺寸（mm）")
    area = IntegerBlock(required=False, help_text="请输入研磨面积（mm²）")
    speed = DecimalBlock(required=False, help_text="请输入研磨速度（g/s）")
    removable_burr = BooleanBlock(required=False, help_text="是否可拆卸磨盘")
    tags = MultipleChoiceBlock(choices=[
        ('自动弹粉', '自动弹粉'),
        ('防静电飞粉', '防静电飞粉'),
        ('几乎零残粉', '几乎零残粉'),
    ], required=False)

## 汇总
class ProductPartsBlock(StreamBlock):
    group_head = GroupHeadBlock()
    portafilter = PortafilterBlock()
    pump = PumpBlock()
    water_supply = WaterSupplyBlock()
    boiler = BoilerBlock()
    steam_wand = PitcherBlock()
    user_panel = InterfaceBlock()
    grinder = GrinderBlock()

## 资源和FAQ列表

### 资源列表
class resource_block(StructBlock):
    res_name = CharBlock(required=False, max_length=250, help_text="请输入资源标题")
    res_link = CharBlock(required=False, help_text="请输入资源链接")
    note = CharBlock(required=False, help_text="请输入对应备注")

class ResourceListBlock(StreamBlock):
    res_list = resource_block()

### FAQ列表
class faq_block(StructBlock):
    question = CharBlock(required=False, max_length=250, help_text="请输入问题")
    answer = RichTextBlock(required=False, help_text="请输入回答")

class FaqListBlock(StreamBlock):
    faq_list = faq_block()

### 系列版本列表
class variation_block(StructBlock):
    variation = CharBlock(required=False, max_length=250, help_text="请输入同系列版本")
    landing_page = URLBlock(required=False, help_text="请输入对应详情页URL")

class VariationListBlock(StreamBlock):
    variation_list = variation_block()