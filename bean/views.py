from django.shortcuts import render, get_object_or_404
from my.models import Favorite
from django.contrib.contenttypes.models import ContentType
from .models import RoastedBeanPage

def roasted_bean_page(request, slug):
    page = get_object_or_404(RoastedBeanPage, slug=slug)
    bean_content_type = ContentType.objects.get_for_model(RoastedBeanPage)

    is_favorited = False
    if request.user.is_authenticated:
        is_favorited = Favorite.objects.filter(user=request.user, content_type=bean_content_type, object_id=page.id).exists()

    context = {
        'page': page,
        'is_favorited': is_favorited,
    }

    return render(request, 'bean/roasted_bean_page.html', context)