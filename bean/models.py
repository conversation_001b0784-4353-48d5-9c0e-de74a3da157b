from wagtail.models import Page
from wagtail.fields import RichTextField
from wagtail.admin.panels import FieldPanel, MultiFieldPanel
from wagtail.search import index
from wagtailmarkdown.fields import MarkdownField
from django.db import models
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.utils.translation import gettext_lazy as _
from datetime import datetime
from wagtail.search.backends import get_search_backend

# 定义烘焙程度的选项
ROAST_LEVEL_CHOICES = [
    ('极浅烘焙', _('极浅烘焙')),
    ('浅烘焙', _('浅烘焙')),
    ('中浅烘焙', _('中浅烘焙')),
    ('中烘焙', _('中烘焙')),
    ('中深烘焙', _('中深烘焙')),
    ('深烘焙', _('深烘焙')),
    ('极深烘焙', _('极深烘焙')),
]
# 定义产品分类的选项
PRODUCT_CAT_CHOICES = [
    ('意式', _('意式')),
    ('手冲', _('手冲')),
    ('冷萃', _('冷萃')),
    ('挂耳', _('挂耳')),
    ('胶囊', _('胶囊')),
    ('研磨咖啡粉', _('研磨咖啡粉')),
    ('袋泡咖啡粉', _('袋泡咖啡粉')),
]
# 在RoastedBeanPage模型中添加所有字段信息
class RoastedBeanPage(Page):
    # 产品描述
    date = models.DateTimeField('发布日期', blank=True, null=True, default=datetime.today)
    roaster = models.CharField(_('烘焙商'), blank=True, null=True, max_length=255)
    product_name = models.CharField(_('产品名称'), blank=True, null=True, max_length=255)
    barcode = models.CharField(_('条形码'), blank=True, null=True, max_length=50, unique=True, help_text=_('请输入产品条形码，用于与用户库存匹配，每个商品的条形码必须唯一'))
    package_image = models.URLField(_('包装图片'), blank=True, null=True, help_text=_('请输入包装图片的URL链接'))
    flavor_description = models.TextField(_('风味描述'), blank=True, null=True, help_text=_('请输入官方的风味词组'))
    product_description = MarkdownField(_('产品描述'), blank=True, null=True, help_text=_('请输入官方的产品文案'))

    # 商品信息
    price = models.DecimalField(_('参考价格'), blank=True, null=True, default=0, max_digits=10, decimal_places=2, help_text=_('请输入参考价格，单位为元'))
    product_link = models.CharField(_('商品链接'), blank=True, null=True, help_text=_('请输入商品链接的URL链接'))
    campaign_link = models.TextField(_('返利链接'), blank=True, null=True, help_text=_('请输入商品链接相关电商联盟的返利URL'))
    net_weight = models.PositiveIntegerField(_('最小贩卖单位'), blank=True, null=True, default=227, help_text=_('请选择总净含量，单位为克'))
    drip_bag = models.PositiveIntegerField(_('挂耳/胶囊/袋泡个数'), blank=True, null=True, help_text=_('最小贩卖单位的挂耳/胶囊/袋泡所含分装包数量，花式DG胶囊按半数计算'))
    capsule_type = models.CharField(_('胶囊型号'), blank=True, null=True, max_length=50, choices=[('Nespresso', _('Nespresso')),('多趣酷思', _('多趣酷思')),])

    # 咖啡豆种
    product_cat = models.CharField(_('产品分类'), blank=True, null=True, max_length=255, choices=PRODUCT_CAT_CHOICES, default='意式')
    bean_type = models.CharField(_('类型'), blank=True, null=True, max_length=255, choices=[('单品', _('单品')), ('拼配', _('拼配'))], default='单品')
    variety = models.TextField(_('豆种'), blank=True, null=True, max_length=255, default="阿拉比卡", help_text=_('请输入详细豆种，例如阿拉比卡→埃塞原生种'))

    # 处理方式
    roast_level = models.CharField(_('烘焙程度'), blank=True, null=True, max_length=255, choices=ROAST_LEVEL_CHOICES, default='中烘焙')
    processing = models.TextField(_('工艺'), blank=True, null=True, max_length=255, help_text=_('请输入详细工艺，混合工艺注意换行写'))

    # 产地信息
    origin = models.TextField(_('产地'), blank=True, null=True, max_length=255, help_text=_('请输入生豆产地'))
    region = models.TextField(_('产区'), blank=True, null=True, max_length=255, help_text=_('请输入生豆产区'))
    estate = models.TextField(_('庄园/处理站'), blank=True, null=True, max_length=255)
    altitude_range = models.TextField(_('种植海拔'), blank=True, null=True, max_length=255, help_text=_('请附带单位m'))

    # 定义content_panels
    content_panels = Page.content_panels + [
        FieldPanel('date'),
        # 产品描述
        MultiFieldPanel([
            FieldPanel('roaster'),
            FieldPanel('product_name'),
            FieldPanel('barcode'),
            FieldPanel('package_image'),
            FieldPanel('flavor_description'),
            FieldPanel('product_description'),
        ], heading=_('产品描述')),
        # 商品信息
        MultiFieldPanel([
            FieldPanel('price'),
            FieldPanel('product_link'),
            FieldPanel('campaign_link'),
            FieldPanel('net_weight'),
            FieldPanel('drip_bag'),
            FieldPanel('capsule_type'),
        ], heading=_('商品信息')),
        # 咖啡豆种
        MultiFieldPanel([
            FieldPanel('product_cat'),
            FieldPanel('bean_type'),
            FieldPanel('variety'),
        ], heading=_('咖啡豆种')),
        # 处理方式
        MultiFieldPanel([
            FieldPanel('roast_level'),
            FieldPanel('processing'),
        ], heading=_('处理方式')),
        # 产地信息
        MultiFieldPanel([
            FieldPanel('origin'),
            FieldPanel('region'),
            FieldPanel('estate'),
            FieldPanel('altitude_range'),
        ], heading=_('产地信息')),
    ]

    search_fields = Page.search_fields + [
        index.SearchField('roaster'),
        index.SearchField('product_name'),
        index.SearchField('flavor_description'),
        index.SearchField('bean_type'),
        index.SearchField('capsule_type'),
        index.SearchField('origin'),
        index.SearchField('region'),
    ]
    
    # 计算性价比
    def price_per_cup(self):
        if self.net_weight and self.price:
            return round((self.price / self.net_weight) * 18, 2)
        return None
    def price_per_pour(self):
        if self.net_weight and self.price:
            return round((self.price / self.net_weight) * 15, 2)
        return None
    def price_per_gram(self):
        if self.net_weight and self.price:
            return round((self.price / self.net_weight) * 1, 2)
        return None
    def price_per_bag(self):
        if self.net_weight and self.price:
            return round((self.price / self.drip_bag) * 1, 2)
        return None

    # 获取与当前页面相同 roaster 和 product_cat 的相关豆子，排除当前页面和非发布页，随机选择 6 个数据
    def get_related_beans(self):
        related_beans = RoastedBeanPage.objects.live().filter(
            roaster=self.roaster,
            product_cat=self.product_cat
        ).exclude(id=self.id)
        related_beans = related_beans.order_by('?')[:6]
        return related_beans

class RoastedBeanIndexPage(Page):
    intro = RichTextField(blank=True)

    content_panels = Page.content_panels + [
        FieldPanel('intro', classname="full")
    ]

    def get_context(self, request):
        context = super().get_context(request)
        roastedbeanpages = RoastedBeanPage.objects.child_of(self).live().order_by('-date')

        search_query = request.GET.get('q')
        roast_level_filter = request.GET.get('roast_level_filter')
        sort_by_price = request.GET.get('sort_by_price')

        if search_query:
            search_backend = get_search_backend('default')
            search_results = search_backend.search(search_query, roastedbeanpages)
            roastedbeanpages = [page.specific for page in search_results]

        if roast_level_filter:
            roastedbeanpages = [page for page in roastedbeanpages if page.roast_level == roast_level_filter]

        if sort_by_price:
            if sort_by_price == 'asc':
                roastedbeanpages = sorted(roastedbeanpages, key=lambda x: x.price or 0)
            elif sort_by_price == 'desc':
                roastedbeanpages = sorted(roastedbeanpages, key=lambda x: x.price or 0, reverse=True)

        paginator = Paginator(roastedbeanpages, 12)

        page = request.GET.get('page')
        try:
            roastedbeanpages = paginator.page(page)
        except PageNotAnInteger:
            roastedbeanpages = paginator.page(1)
        except EmptyPage:
            roastedbeanpages = paginator.page(paginator.num_pages)
        
        context['roastedbeanpages'] = roastedbeanpages
        return context