{% extends "base.html" %}

{% load wagtailcore_tags wagtailimages_tags wagtailmarkdown custom_floatformat %}

{% block seo_desc %}
<meta name="description" content="{{ page.roaster }} {{ page.product_name }}咖啡豆产品规格及风味介绍，这款咖啡是{{ page.product_cat }}{% if page.bean_type %}{{ page.bean_type }}{% endif %}{% if page.roast_level %}{{ page.roast_level }}{% endif %}，{{ page.net_weight }}克到手价格{{ page.price }}元。" />
{% endblock %}

{% block body_class %}template-roastedbeanpage{% endblock %}

{% block content %}

<main class="flex flex-col grow text-base-content">
  <!-- Main Info -->
  <div class="w-full lg:max-w-5xl mx-auto flex flex-col md:flex-row gap-6 py-8 justify-center">
    <!-- Image -->
    <div class="relative grow w-full md:w-1/2">
      <div class="w-full h-full flex flex-col justify-center items-center relative">
        <div class="flex justify-center">
          <div class="relative w-[80%] lg:max-w-[90%] avatar indicator">
            {% if page.roast_level %}<span class="indicator-item indicator-center badge badge-accent">{{ page.roast_level }}</span>{% endif %}
            <img alt="{{ page.title }}" src="{{ page.package_image }}" class="rounded-sm w-full aspect-w-698 aspect-h-938">
          </div>
        </div>
        <!-- Add Fav -->
        {% if user.is_authenticated %}
        <div class="flex justify-center mt-4">
            <input type="hidden" id="csrf-token" value="{{ csrf_token }}" />
            <div x-data="{ favorited: {{ is_favorited|yesno:'true,false' }}, csrfToken: '{{ csrf_token }}' }">
                <button 
                    x-on:click="fetch(favorited ? '{% url "remove_favorite_by_type" "bean" "roastedbeanpage" page.id %}' : '{% url "add_favorite" "bean" "roastedbeanpage" page.id %}', {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': csrfToken,
                            'Content-Type': 'application/json'
                        }
                    }).then(response => response.json()).then(data => {
                        favorited = data.favorited;
                    })"
                    x-bind:class="favorited ? 'btn-ghost' : 'btn-outline'"
                    x-bind:aria-pressed="favorited"
                    class="btn btn-ghost"
                >
                    <span x-show="favorited" class="icon-[bi--bookmark-star-fill]"></span>
                    <span x-show="!favorited" class="icon-[bi--bookmark-star]"></span>
                    <span x-text="favorited ? '已收藏' : '收藏咖啡'"></span>
                </button>
                <div id="favorite-btn-indicator" style="display: none;"><span class="loading loading-spinner text-primary"></span></div>
            </div>
        </div>
        {% else %}
        <a href="{% url 'account_login' %}" class="btn btn-outline mt-4">
          <span class="icon-[bi--bookmark-star]"></span>
          收藏咖啡
        </a>
        {% endif %}
        <!-- End Add Fav -->
      </div>
    </div>
    <!-- End Image -->
    <!-- Product Info -->
    <div class="flex flex-col items-center lg:items-start justify-center w-full md:w-1/2">
      <section class="flex flex-col w-full md:mx-auto min-w-[20rem] max-w-[23rem]">
        <div class="flex items-center justify-center bg-amber-900/10 dark:bg-indigo-900/20 rounded-lg text-[1.1em] md:text-[1.35vw] lg:text-[1.25vw] xl:text-[1.1em] mask mask-squircle">
          <div class="flex flex-col items-center justify-center relative">
            <dl class="h-[20em] w-[17.25em] flex flex-col items-center justify-center text-center relative">
              <dd class="tracking-wider text-[0.9em] absolute top-[11%] h-[10%] flex flex-col items-center justify-center">
                <span class="block leading-none">{{ page.roaster }}</span>
              </dd>
              <dt class="flex flex-col items-center justify-center min-h-[6em] px-[1em] absolute top-[26%]">
                <h2 class="text-[2em] leading-none"><span>{{ page.product_name }}</span></h2>
              </dt>
              <dd class="text-[0.78em] tracking-wider absolute top-[61%] leading-none">风味描述
                <ul class="text-[1.3em] mt-[0.5em] tracking-normal p-0 normal-case leading-[1.2]">
                  {{ page.flavor_description|linebreaks }}
                </ul>
              </dd>
            </dl>
          </div>
        </div>
        <div class="grid gap-2 mt-6 px-4 md:px-0">
          <div class="grid">
            <div class="divider pb-4 menu-title">商品信息</div>
            <div class="flow-root">
              <dl class="-my-3 divide-y divide-base-200 text-sm">
                <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                  <dt class="font-medium">贩卖规格</dt>
                  <dd class="opacity-70 sm:col-span-2">{{ page.net_weight }} 克</dd>
                </div>

                <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                  <dt class="font-medium tooltip tooltip-info cursor-help text-left decoration-info underline decoration-dashed underline-offset-4" data-tip="指该商品历史常卖价，最新价格以商品链接为准">参考价格</dt>
                  <dd class="opacity-70 sm:col-span-2">{{ page.price|custom_floatformat }} 元</dd>
                </div>

                <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                  {% if page.product_cat == '意式' %}
                  <dt class="font-medium">每杯参考价格</dt>
                  <dd class="opacity-70 sm:col-span-2">每 18 克 {{ page.price_per_cup }} 元</dd>
                  {% endif %}
                  {% if page.product_cat == '手冲' or page.product_cat == '研磨咖啡粉' %}
                  <dt class="font-medium">每杯参考价格</dt>
                  <dd class="opacity-70 sm:col-span-2">每 15 克 {{ page.price_per_pour }} 元</dd>
                  {% endif %}
                  {% if page.product_cat == '冷萃' %}
                  <dt class="font-medium">每克参考价格</dt>
                  <dd class="opacity-70 sm:col-span-2">{{ page.price_per_gram }} 元</dd>
                  {% endif %}
                  {% if page.product_cat == '挂耳' %}
                  <dt class="font-medium">每片参考价格</dt>
                  <dd class="opacity-70 sm:col-span-2">{{ page.price_per_bag }} 元</dd>
                  {% endif %}
                  {% if page.product_cat == '胶囊' %}
                  <dt class="font-medium">每杯参考价格</dt>
                  <dd class="opacity-70 sm:col-span-2">{{ page.price_per_bag }} 元</dd>
                  {% endif %}
                  {% if page.product_cat == '速溶咖啡粉' and page.drip_bag %}
                  <dt class="font-medium">每包参考价格</dt>
                  <dd class="opacity-70 sm:col-span-2">{{ page.price_per_bag }} 元</dd>
                  {% endif %}
                  {% if page.product_cat == '速溶咖啡粉' and not page.drip_bag %}
                  <dt class="font-medium tooltip tooltip-info cursor-help text-left decoration-info underline decoration-dashed underline-offset-4" data-tip="速溶咖啡粉通常取一茶勺（2克左右）咖啡粉冲泡150毫升热水。">每克参考价格</dt>
                  <dd class="opacity-70 sm:col-span-2">{{ page.price_per_gram }} 元</dd>
                  {% endif %}
                </div>

              <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                <dt class="font-medium">商品链接</dt>
                <dd class="sm:col-span-2 flex flex-col gap-4">
                  <a target="_blank" rel="noopener noreferrer" href="{{ page.campaign_link|default:page.product_link }}"
                    class="text-info link link-hover group break-all">{{ page.product_link }}
                    <span class="icon-[charm--link-external] text-sm inline-block fill-current opacity-0 group-hover:opacity-50"></span>
                  </a>
                </dd>
              </div>
              </dl>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- End Product Info -->
  </div>
  <div class="mb-10">
    <div class="inline-block w-full">
      {% if page.product_description %}
      <div x-data="{ expanded: false, maxLength: 200, description: '{{ page.product_description|markdown|escapejs }}' }" class="pt-10 pb-5 px-6 md:px-0 max-w-prose m-auto relative">
        <span class="icon-[fa-solid--quote-left] text-3xl text-gray-400 mb-4"></span>
        <div x-html="expanded ? description : (description.slice(0, maxLength) + (description.length > maxLength ? '...' : ''))" class="prose text-xl inline text-ellipsis overflow-hidden">
        </div>
        <a href="#" x-show="description.length > maxLength" x-on:click.prevent="expanded = !expanded" class="text-info inline">
          <span x-text="expanded ? '收起 ▲' : '展开 ▼'"></span>
        </a>
      </div>
      {% endif %}
      <!-- End Main Info -->
      <!-- Bean Info -->
      <div class="pt-10 pb-5 10 px-6 md:px-0 max-w-prose m-auto relative">
        <div class="join join-vertical w-full">
          <div class="collapse collapse-plus join-item border border-base-300">
            <input type="checkbox" class="w-auto h-auto" checked="checked" />
            <div class="collapse-title text-xl font-medium">
              咖啡类别
            </div>
            <div class="collapse-content">
              <div class="p-5 pt-0 opacity-70">
                <div class="flex flex-col md:grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <dl class="relative">
                    <dt class="font-serif text-xs opacity-75">系列</dt>
                    <dd>{{ page.product_cat }}</dd>
                  </dl>
                  {% if page.product_cat == '胶囊' and page.capsule_type %}
                  <dl class="relative">
                    <dt class="font-serif text-xs opacity-75">胶囊型号</dt>
                    <dd>{{ page.capsule_type }}</dd>
                  </dl>
                  {% endif %}
                  {% if page.bean_type %}
                  <dl class="relative">
                    <dt class="font-serif text-xs opacity-75">类型</dt>
                    <dd>{{ page.bean_type }}</dd>
                  </dl>
                  {% endif %}
                  {% if page.variety %}
                  <dl class="relative">
                    <dt class="font-serif text-xs opacity-75">品种</dt>
                    <dd>{{ page.variety|linebreaks }}</dd>
                  </dl>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
          {% if page.roast_level or page.processing %}
          <div class="collapse collapse-plus join-item border border-base-300">
            <input type="checkbox" class="w-auto h-auto" />
            <div class="collapse-title text-xl font-medium">
              处理方式
            </div>
            <div class="collapse-content">
              <div class="p-5 pt-0 opacity-70">
                <div class="flex flex-col md:grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {% if page.roast_level %}
                  <dl class="relative">
                    <dt class="font-serif text-xs opacity-75">烘焙度</dt>
                    <dd>{{ page.roast_level }}</dd>
                  </dl>
                  {% endif %}
                  {% if page.processing %}
                  <dl class="relative">
                    <dt class="font-serif text-xs opacity-75">工艺</dt>
                    <dd>{{ page.processing|linebreaks }}</dd>
                  </dl>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
          {% endif %}
          {% if page.origin or page.region or page.estate or page.altitude_range %}
          <div class="collapse collapse-plus join-item border border-base-300">
            <input type="checkbox" class="w-auto h-auto" />
            <div class="collapse-title text-xl font-medium">
              生豆信息
            </div>
            <div class="collapse-content">
              <div class="p-5 pt-0 opacity-70">
                <div class="flex flex-col md:grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {% if page.origin %}
                  <dl class="relative">
                    <dt class="font-serif text-xs opacity-75">产地</dt>
                    <dd class="break-words whitespace-pre-wrap">{{ page.origin|linebreaks }}</dd>
                  </dl>
                  {% endif %}
                  {% if page.region %}
                  <dl class="relative">
                    <dt class="font-serif text-xs opacity-75">产区</dt>
                    <dd class="break-words whitespace-pre-wrap">{{ page.region|linebreaks }}</dd>
                  </dl>
                  {% endif %}
                  {% if page.estate %}
                  <dl class="relative">
                    <dt class="font-serif text-xs opacity-75">庄园/处理站</dt>
                    <dd class="break-words whitespace-pre-wrap">{{ page.estate|linebreaks }}</dd>
                  </dl>
                  {% endif %}
                  {% if page.altitude_range %}
                  <dl class="relative">
                    <dt class="font-serif text-xs opacity-75">种植海拔</dt>
                    <dd class="break-words whitespace-pre-wrap">{{ page.altitude_range|linebreaks }}</dd>
                  </dl>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
      <!-- End Bean Info -->
      <!-- Related Beans -->
      {% with related_beans=page.get_related_beans %}
      {% if related_beans %}
      <div class="space-y-6 grid justify-center">
      <div class="divider pb-4 menu-title">相关产品</div>
      <div class="w-fit mx-auto grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 justify-items-center justify-center gap-y-8 lg:gap-y-14 gap-x-14 mt-1 mb-5">
        {% for bean in related_beans %}
        <div class="w-72 bg-white dark:bg-base-100 shadow-md rounded-xl duration-500 hover:scale-105 hover:shadow-xl text-base-content">
            <a href="{% pageurl bean %}" target="_blank">
                <img src="{{ bean.package_image }}" alt="{{ bean.title }}" class="h-80 w-72 object-cover rounded-t-xl" />
                <div class="px-4 py-3 w-72">
                    <span class="mr-3 text-xs opacity-60">{{ bean.roaster }}</span>
                    <p class="text-lg font-bold truncate block capitalize">{{ bean.product_name }}</p>
                    <div class="flex items-center">
                        <p class="text-lg font-semibold cursor-auto my-3">￥{{ bean.price|custom_floatformat }}</p>
                        <div class="ml-auto"><span class="badge badge-accent badge-outline">{{ bean.product_cat }}</span></div>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
      </div>
      </div>
      {% endif %}
      {% endwith %}
      <!-- End Related Beans -->
    </div>
  </div>
  <!-- End Bean Info -->
</main>
{% endblock %}