{% extends "base.html" %}
{% load static %}
{% load wagtailcore_tags wagtailimages_tags %}
{% load custom_floatformat %}
{% load cache %}
{% block body_class %}template-roastedbeanindexpage{% endblock %}

{% block content %}

<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
        <h1 class="text-xl text-center text-base-content">
            {{ page.title }}
        </h1>
        {% if page.intro %}
        <div class="text-sm text-center text-base-content opacity-60">
            {{ page.intro|richtext }}
        </div>
        {% endif %}
    </div>
</div>

<!-- List Filter -->
<div x-data="{ isOpen: false }" class="card text-base-content">
    <div class="card-body w-fit mx-auto">
        <div class="lg:hidden flex justify-center">
            <button @click="isOpen = !isOpen"
                class="mt-2 flex w-full justify-center items-center px-4 py-2 md:mt-0 md:w-auto md:p-0">
                <span class="icon-[oui--filter]"></span>
                <span x-text="isOpen ? '收起筛选功能' : '展开筛选功能'" class="ml-1 font-medium"></span>
            </button>
        </div>
        <form id="filter-form" class="flex flex-col items-start lg:gap-x-12 lg:flex-row lg:justify-between" x-show="isOpen || window.innerWidth >= 1024">
            <div class="form-group lg:flex-1 mb-4 lg:mb-0">
                <label class="form-control w-full max-w-xs">
                    <div class="label">
                        <span class="label-text">搜索词</span>
                    </div>
                    <input type="text" name="q" placeholder="请输入关键词" value="{{ request.GET.q }}"
                        class="input" />
                    <div class="label">
                        <span class="label-text-alt text-xs">多个关键词请用空格隔开，例如：巧克力 意式 中烘</span>
                    </div>
                </label>
            </div>
            <div class="form-group lg:flex-1 mb-4 lg:mb-0">
                <label class="form-control w-full max-w-xs">
                    <div class="label">
                        <span class="label-text">烘焙度</span>
                    </div>
                    <select name="roast_level_filter" id="roast_level_filter" class="select">
                        <option value="">全部</option>
                        <option value="极浅烘焙" {% if request.GET.roast_level_filter == '极浅烘焙' %} selected {% endif %}>极浅烘焙</option>
                        <option value="浅烘焙" {% if request.GET.roast_level_filter == '浅烘焙' %} selected {% endif %}>浅烘焙</option>
                        <option value="中浅烘焙" {% if request.GET.roast_level_filter == '中浅烘焙' %} selected {% endif %}>中浅烘焙</option>
                        <option value="中烘焙" {% if request.GET.roast_level_filter == '中烘焙' %} selected {% endif %}>中烘焙</option>
                        <option value="中深烘焙" {% if request.GET.roast_level_filter == '中深烘焙' %} selected {% endif %}>中深烘焙</option>
                        <option value="深烘焙" {% if request.GET.roast_level_filter == '深烘焙' %} selected {% endif %}>深烘焙</option>
                    </select>
                </label>
            </div>
            <div class="form-group lg:flex-1 mb-4 lg:mb-0">
                <label class="form-control w-full max-w-xs">
                    <div class="label">
                        <span class="label-text">排序方式</span>
                    </div>
                    <select name="sort_by_price" id="sort_by_price" class="select">
                        <option value="">按上架时间</option>
                        <option value="asc" {% if request.GET.sort_by_price == 'asc' %} selected {% endif %}>价格从低到高
                        </option>
                        <option value="desc" {% if request.GET.sort_by_price == 'desc' %} selected {% endif %}>价格从高到低
                        </option>
                    </select>
                </label>
            </div>
            <div class="form-group lg:flex-1 mb-4 lg:mb-0">
                <div class="label">
                    <span class="label-text"></span>
                </div>
                <button type="submit" class="btn btn-outline-primary align-baseline mt-4">
                    开始筛选
                </button>
            </div>
        </form>
    </div>
</div>
<!-- End List Filter -->

<!-- Grid Section -->
<section class="w-fit mx-auto grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 justify-items-center justify-center gap-y-20 gap-x-14 mt-1 mb-5">

    <!-- Product Card-->
    {% cache 3600 bean_list request.GET.urlencode %}
    {% for roastedbeanpage in roastedbeanpages %}
    {% with page=roastedbeanpage.specific %}
    <div class="w-72 bg-white dark:bg-base-100 shadow-md rounded-xl duration-500 hover:scale-105 hover:shadow-xl text-base-content">
        <a href="{% pageurl page %}" target="_blank">
            <img src="{{ page.package_image }}" alt="{{ page.title }}" class="h-80 w-72 object-cover rounded-t-xl" />
            <div class="px-4 py-3 w-72">
                <span class="mr-3 text-xs opacity-60">{{ page.roaster }}</span>
                <p class="text-lg font-bold truncate block capitalize">{{ page.product_name }}</p>
                <div class="flex items-center">
                    <p class="text-lg font-semibold cursor-auto my-3">￥{{ page.price|custom_floatformat }}</p>
                    <div class="ml-auto"><span class="badge badge-accent badge-outline">{{ page.product_cat }}</span></div>
                </div>
            </div>
        </a>
    </div>
    {% endwith %}
    {% empty %}
    <p>暂无符合条件的内容。</p>
    {% endfor %}
    {% endcache %}
    <!-- End Product Card -->
</section>
<!-- End Grid Section -->

<!-- Pagination -->
<div class="py-2 px-1 lg:p-4 mb-2 lg:py-8 lg:px-4">
    <div class="flex items-center justify-center">
        <ul class="list-none mb-6 flex">
            {% if roastedbeanpages.has_previous %}
            <li>
                <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}&page={{ roastedbeanpages.previous_page_number }}">上一页</a>
            </li>
            {% else %}
            <li>
                <a class="pointer-events-none relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-300 transition-all duration-300">上一页</a>
            </li>
            {% endif %}
            {% for page_num in roastedbeanpages.paginator.page_range %}
            {% if roastedbeanpages.number == page_num %}
            <li aria-current="page">
                <a class="relative block rounded-sm bg-primary-100 px-3 py-1.5 text-lg font-medium text-primary transition-all duration-300">{{ page_num }}</a>
            </li>
            {% elif page_num > roastedbeanpages.number|add:'-3' and page_num < roastedbeanpages.number|add:'3' %}
            <li>
                <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}&page={{ page_num }}">{{ page_num }}</a>
            </li>
            {% endif %}
            {% endfor %}
            {% if roastedbeanpages.has_next %}
            <li>
                <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}&page={{ roastedbeanpages.next_page_number }}">下一页</a>
            </li>
            {% else %}
            <li>
                <a class="pointer-events-none relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-300 transition-all duration-300">下一页</a>
            </li>
            {% endif %}
        </ul>
    </div>
</div>
<!-- End Pagination -->
{% endblock %}