# Generated by Django 5.0 on 2024-06-05 07:34

import datetime
import django.db.models.deletion
import wagtail.fields
import wagtailmarkdown.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('wagtailcore', '0091_remove_revision_submitted_for_moderation'),
    ]

    operations = [
        migrations.CreateModel(
            name='RoastedBeanIndexPage',
            fields=[
                ('page_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='wagtailcore.page')),
                ('intro', wagtail.fields.RichTextField(blank=True)),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
        migrations.CreateModel(
            name='RoastedBeanPage',
            fields=[
                ('page_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='wagtailcore.page')),
                ('date', models.DateTimeField(blank=True, default=datetime.datetime.today, null=True, verbose_name='发布日期')),
                ('roaster', models.CharField(blank=True, max_length=255, null=True, verbose_name='烘焙商')),
                ('product_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='产品名称')),
                ('package_image', models.URLField(blank=True, help_text='请输入包装图片的URL链接', null=True, verbose_name='包装图片')),
                ('flavor_description', models.TextField(blank=True, help_text='请输入官方的风味词组', null=True, verbose_name='风味描述')),
                ('product_description', wagtailmarkdown.fields.MarkdownField(blank=True, help_text='请输入官方的产品文案', null=True, verbose_name='产品描述')),
                ('price', models.DecimalField(blank=True, decimal_places=2, default=0, help_text='请输入参考价格，单位为元', max_digits=10, null=True, verbose_name='参考价格')),
                ('product_link', models.CharField(blank=True, help_text='请输入商品链接的URL链接', null=True, verbose_name='商品链接')),
                ('campaign_link', models.CharField(blank=True, help_text='请输入各电商联盟的返利链接', null=True, verbose_name='返利链接')),
                ('net_weight', models.PositiveIntegerField(blank=True, default=227, help_text='请选择总净含量，单位为克', null=True, verbose_name='最小贩卖单位')),
                ('drip_bag', models.PositiveIntegerField(blank=True, help_text='最小贩卖单位的挂耳/胶囊/袋泡所含分装包数量，花式DG胶囊按半数计算', null=True, verbose_name='挂耳/胶囊/袋泡个数')),
                ('capsule_type', models.CharField(blank=True, choices=[('Nespresso', 'Nespresso'), ('多趣酷思', '多趣酷思')], max_length=50, null=True, verbose_name='胶囊型号')),
                ('product_cat', models.CharField(blank=True, choices=[('意式', '意式'), ('手冲', '手冲'), ('冷萃', '冷萃'), ('挂耳', '挂耳'), ('胶囊', '胶囊'), ('研磨咖啡粉', '研磨咖啡粉'), ('袋泡咖啡粉', '袋泡咖啡粉')], default='意式', max_length=255, null=True, verbose_name='产品分类')),
                ('bean_type', models.CharField(blank=True, choices=[('单品', '单品'), ('拼配', '拼配')], default='单品', max_length=255, null=True, verbose_name='类型')),
                ('variety', models.TextField(blank=True, default='阿拉比卡', help_text='请输入详细豆种，例如阿拉比卡→埃塞原生种', max_length=255, null=True, verbose_name='豆种')),
                ('roast_level', models.CharField(blank=True, choices=[('极浅烘焙', '极浅烘焙'), ('浅烘焙', '浅烘焙'), ('中浅烘焙', '中浅烘焙'), ('中烘焙', '中烘焙'), ('中深烘焙', '中深烘焙'), ('深烘焙', '深烘焙'), ('极深烘焙', '极深烘焙')], default='中烘焙', max_length=255, null=True, verbose_name='烘焙程度')),
                ('processing', models.TextField(blank=True, help_text='请输入详细工艺，混合工艺注意换行写', max_length=255, null=True, verbose_name='工艺')),
                ('origin', models.TextField(blank=True, help_text='请输入生豆产地', max_length=255, null=True, verbose_name='产地')),
                ('region', models.TextField(blank=True, help_text='请输入生豆产区', max_length=255, null=True, verbose_name='产区')),
                ('estate', models.TextField(blank=True, max_length=255, null=True, verbose_name='庄园/处理站')),
                ('altitude_range', models.TextField(blank=True, help_text='请附带单位m', max_length=255, null=True, verbose_name='种植海拔')),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
    ]
