from rest_framework import serializers
from decimal import Decimal
from my.models import BrewingRecord, Equipment, CoffeeBean, FlavorTag, HindsightStats
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class FlavorTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = FlavorTag
        fields = ['id', 'name']

class EquipmentSerializer(serializers.ModelSerializer):
    type_display = serializers.SerializerMethodField()
    brew_method_display = serializers.SerializerMethodField()

    class Meta:
        model = Equipment
        fields = [
            'id', 'name', 'brand', 'type', 'type_display', 
            'brew_method', 'brew_method_display', 'notes', 
            'created_at', 'is_favorite', 'is_archived'
        ]

    def get_type_display(self, obj):
        return dict(Equipment.EQUIPMENT_TYPES).get(obj.type, '')
        
    def get_brew_method_display(self, obj):
        return dict(Equipment.BREW_METHODS).get(obj.brew_method, '') if obj.brew_method else ''

class BeanSerializer(serializers.ModelSerializer):
    roast_level_display = serializers.SerializerMethodField()

    class Meta:
        model = CoffeeBean
        fields = [
            'id', 'name', 'roaster', 'origin', 'process', 
            'roast_level', 'roast_level_display', 'notes', 
            'created_at', 'is_favorite', 'is_archived',
            'bag_weight', 'bag_remain', 'purchase_price'
        ]
        
    def get_roast_level_display(self, obj):
        return dict(CoffeeBean.ROAST_LEVEL_CHOICES).get(obj.roast_level, '')

class BrewLogSerializer(serializers.ModelSerializer):
    equipment = EquipmentSerializer(read_only=True, source='brewing_equipment')
    bean = BeanSerializer(read_only=True, source='coffee_bean')
    rating = serializers.IntegerField(source='rating_level', min_value=0, max_value=10)
    temperature = serializers.DecimalField(source='water_temperature', max_digits=4, decimal_places=1, min_value=Decimal('0'), max_value=Decimal('100'))
    dose = serializers.DecimalField(source='dose_weight', max_digits=5, decimal_places=1, min_value=Decimal('0'), max_value=Decimal('1000'))
    total_time = serializers.DurationField(source='brewing_time')
    yield_weight = serializers.DecimalField(max_digits=5, decimal_places=1, min_value=Decimal('0'), max_value=Decimal('1000'))
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    notes = serializers.CharField(allow_blank=True)
    steps = serializers.JSONField(required=False)
    steps_enabled = serializers.BooleanField(default=False)
    recipe_name = serializers.CharField(allow_blank=True)
    
    class Meta:
        model = BrewingRecord
        fields = [
            'id', 'equipment', 'bean', 'grind_size', 'dose', 'yield_weight',
            'temperature', 'total_time', 'rating', 'notes', 'created_at',
            'steps', 'steps_enabled', 'recipe_name'
        ]

class BrewLogHindsightSerializer(serializers.ModelSerializer):
    brewlog = BrewLogSerializer(read_only=True)
    hindsight_text = serializers.CharField(read_only=True)
    
    class Meta:
        model = BrewingRecord
        fields = ['id', 'brewlog', 'hindsight_text', 'created_at']

class BrewLogHeatmapSerializer(serializers.ModelSerializer):
    brewlog = BrewLogSerializer(read_only=True)
    heatmap_data = serializers.JSONField(read_only=True)
    
    class Meta:
        model = BrewingRecord
        fields = ['id', 'brewlog', 'heatmap_data', 'created_at'] 