from django.shortcuts import render
import json
from django.http import JsonResponse
from django.contrib.auth.models import User
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.db import transaction
from django.conf import settings
from .models import WechatUser
import requests
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from django.core.cache import cache
from django.utils import timezone
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from .utils import get_wechat_session_key, create_token
from my.models import BrewingRecord, Equipment, CoffeeBean, FlavorTag, HindsightStats
from django.shortcuts import get_object_or_404, redirect
from .serializers import BrewLogSerializer, EquipmentSerializer, BeanSerializer, FlavorTagSerializer, BrewLogHindsightSerializer, BrewLogHeatmapSerializer
from rest_framework.pagination import PageNumberPagination
from django.utils.decorators import method_decorator
from rest_framework import viewsets

User = get_user_model()

def get_tokens_for_user(user):
    """获取用户的JWT token"""
    refresh = RefreshToken.for_user(user)
    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token),
    }

# Create your views here.

@csrf_exempt
@require_http_methods(["POST", "GET"])
def wechat_login(request):
    """
    微信小程序登录
    """
    if request.method == "GET":
        return JsonResponse({
            'message': '微信登录接口正常',
            'method': 'GET',
            'test': True
        })

    try:
        data = json.loads(request.body)
        code = data.get('code')
        
        if not code:
            return JsonResponse({'error': '缺少code参数'}, status=400)
        
        # 获取微信小程序配置
        app_id = settings.WECHAT_MINI_PROGRAM['APP_ID']
        app_secret = settings.WECHAT_MINI_PROGRAM['APP_SECRET']
        
        if not app_id or not app_secret:
            return JsonResponse({'error': '微信小程序配置错误'}, status=500)
        
        # 请求微信接口获取openid和session_key
        url = f'https://api.weixin.qq.com/sns/jscode2session'
        params = {
            'appid': app_id,
            'secret': app_secret,
            'js_code': code,
            'grant_type': 'authorization_code'
        }
        
        response = requests.get(url, params=params)
        wechat_data = response.json()
        
        if 'errcode' in wechat_data:
            return JsonResponse({
                'error': '微信登录失败',
                'detail': wechat_data.get('errmsg', '未知错误')
            }, status=400)
        
        openid = wechat_data.get('openid')
        session_key = wechat_data.get('session_key')
        unionid = wechat_data.get('unionid')
        
        # 查找或创建WechatUser
        wechat_user = WechatUser.objects.filter(openid=openid).first()
        
        if wechat_user and wechat_user.user:
            # 已存在的用户，返回用户信息和token
            tokens = get_tokens_for_user(wechat_user.user)
            return JsonResponse({
                'token': tokens['access'],
                'refresh_token': tokens['refresh'],
                'user': {
                    'id': wechat_user.user.id,
                    'username': wechat_user.user.username,
                    'nickname': wechat_user.nickname,
                    'avatar_url': wechat_user.avatar_url,
                    'is_bound': bool(wechat_user.bound_time)
                }
            })
        else:
            # 新用户，创建WechatUser记录
            with transaction.atomic():
                if not wechat_user:
                    # 创建基础用户
                    username = f'wx_{openid[:8]}'  # 使用openid前8位作为用户名
                    user = User.objects.create_user(username=username)
                    
                    # 创建微信用户
                    wechat_user = WechatUser.objects.create(
                        user=user,
                        openid=openid,
                        unionid=unionid
                    )
                
                tokens = get_tokens_for_user(wechat_user.user)
                return JsonResponse({
                    'token': tokens['access'],
                    'refresh_token': tokens['refresh'],
                    'user': {
                        'id': wechat_user.user.id,
                        'username': wechat_user.user.username,
                        'nickname': wechat_user.nickname,
                        'avatar_url': wechat_user.avatar_url,
                        'is_bound': bool(wechat_user.bound_time)
                    }
                })
            
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def update_user_info(request):
    """
    更新用户信息
    """
    try:
        # 从 token 中获取用户信息
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JsonResponse({
                'error': '未提供有效的认证信息'
            }, status=401)

        token = auth_header.split(' ')[1]
        from rest_framework_simplejwt.tokens import AccessToken
        token_obj = AccessToken(token)
        current_user_id = token_obj['user_id']
        
        # 获取当前微信用户
        wechat_user = WechatUser.objects.get(user_id=current_user_id)
        
        data = json.loads(request.body)
        
        # 更新用户信息
        if 'nickname' in data:
            wechat_user.nickname = data['nickname']
        if 'avatar_url' in data:
            wechat_user.avatar_url = data['avatar_url']
            
        wechat_user.save()
        
        return JsonResponse({
            'message': '更新成功',
            'user': {
                'id': wechat_user.user.id,
                'username': wechat_user.user.username,
                'nickname': wechat_user.nickname,
                'avatar_url': wechat_user.avatar_url,
                'is_bound': bool(wechat_user.bound_time)
            }
        })
        
    except WechatUser.DoesNotExist:
        return JsonResponse({
            'error': '用户不存在'
        }, status=404)
    except Exception as e:
        # 记录错误日志
        import logging
        logger = logging.getLogger('django')
        logger.error(f'Update user info failed: {str(e)}', exc_info=True)
        
        return JsonResponse({
            'error': '更新失败，请稍后重试'
        }, status=500)

@api_view(['POST'])
@permission_classes([AllowAny])
def bind_account(request):
    """绑定已有网站账号到微信小程序账号，或使用已绑定账号登录"""
    username = request.data.get('username')
    password = request.data.get('password')

    if not all([username, password]):
        return Response({
            'error': '请提供完整的账号信息'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # 验证网站账号
        target_user = User.objects.get(username=username)
        if not target_user.check_password(password):
            return Response({
                'error': '用户名或密码错误'
            }, status=status.HTTP_401_UNAUTHORIZED)

        # 检查网站账号是否已绑定微信
        try:
            wechat_user = WechatUser.objects.get(user=target_user)
            
            # 如果已经绑定了微信，检查是否是当前微信号
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                token_obj = AccessToken(token)
                current_user_id = token_obj['user_id']
                current_wechat_user = WechatUser.objects.get(user_id=current_user_id)
                
                if wechat_user.openid == current_wechat_user.openid:
                    # 是当前微信号，生成新的 token 并登录
                    tokens = get_tokens_for_user(target_user)
                    return Response({
                        'token': tokens['access'],
                        'refresh_token': tokens['refresh'],
                        'user': {
                            'id': target_user.id,
                            'username': target_user.username,
                            'nickname': wechat_user.nickname,
                            'avatar_url': wechat_user.avatar_url,
                            'is_bound': True
                        }
                    })
            
            # 如果是其他微信号，返回错误
            return Response({
                'error': '该账号已绑定其他微信号',
                'code': 'ALREADY_BOUND'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        except WechatUser.DoesNotExist:
            # 账号未绑定微信，继续绑定流程
            pass

        # 获取当前微信用户
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return Response({
                'error': '未提供有效的认证信息'
            }, status=status.HTTP_401_UNAUTHORIZED)

        token = auth_header.split(' ')[1]
        token_obj = AccessToken(token)
        current_user_id = token_obj['user_id']
        current_wechat_user = WechatUser.objects.get(user_id=current_user_id)
        
        # 检查请求频率
        cache_key = f'bind_attempt_{current_wechat_user.openid}'
        attempt_count = cache.get(cache_key, 0)
        if attempt_count >= 5:  # 5分钟内最多5次尝试
            return Response({
                'error': '操作过于频繁，请稍后再试'
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)

        # 合并数据
        if not current_wechat_user.merge_user_data(target_user):
            return Response({
                'error': '账号关联失败，请稍后重试'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # 更新绑定时间
        current_wechat_user.user = target_user
        current_wechat_user.bound_time = timezone.now()
        current_wechat_user.save()

        # 清除频率限制缓存
        cache.delete(cache_key)

        # 生成新的 token
        tokens = get_tokens_for_user(target_user)
        
        return Response({
            'token': tokens['access'],
            'refresh_token': tokens['refresh'],
            'user': {
                'id': target_user.id,
                'username': target_user.username,
                'nickname': current_wechat_user.nickname,
                'avatar_url': current_wechat_user.avatar_url,
                'is_bound': True
            }
        })

    except User.DoesNotExist:
        return Response({
            'error': '用户名或密码错误'
        }, status=status.HTTP_401_UNAUTHORIZED)
    except WechatUser.DoesNotExist:
        return Response({
            'error': '微信账号状态异常'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        # 记录错误日志
        import logging
        logger = logging.getLogger('django')
        logger.error(f'Account binding failed: {str(e)}', exc_info=True)
        
        return Response({
            'error': '账号关联失败，请稍后重试'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@csrf_exempt
@authentication_classes([])
@permission_classes([AllowAny])
def api_get_available_years(request):
    """获取有冲煮记录的年份列表 API"""
    try:
        user_id = request.headers.get('X-User-ID')
        if not user_id:
            return JsonResponse({'error': '缺少用户ID'}, status=400)
            
        years = BrewingRecord.objects.filter(
            user_id=int(user_id)  # 转换为整数
        ).dates('created_at', 'year').values_list('created_at__year', flat=True)
        
        return JsonResponse({'years': list(years)})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@api_view(['GET'])
@csrf_exempt
@authentication_classes([])
@permission_classes([AllowAny])
def api_brewing_hindsight(request):
    """冲煮记录回顾统计 API"""
    try:
        user_id = request.headers.get('X-User-ID')
        if not user_id:
            return JsonResponse({'error': '缺少用户ID'}, status=400)
            
        time_range = request.GET.get('time_range', 'week')
        
        # 从数据库获取用户对象
        from django.contrib.auth import get_user_model
        User = get_user_model()
        user = User.objects.get(id=int(user_id))
        
        # 传递用户对象而不是 ID
        from my.models import HindsightStats
        stats_data = HindsightStats.get_stats(user, time_range)
        
        return JsonResponse({
            'stats': stats_data['stats'],
            'total_records': stats_data['total_records']
        })
    except User.DoesNotExist:
        return JsonResponse({'error': '用户不存在'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@api_view(['GET'])
@csrf_exempt
@authentication_classes([])
@permission_classes([AllowAny])
def api_brewlog_heatmap(request):
    """显示冲煮记录热力图 API"""
    try:
        user_id = request.headers.get('X-User-ID')
        if not user_id:
            return JsonResponse({'error': '缺少用户ID'}, status=400)
            
        # 从查询参数获取年份
        year = request.GET.get('year')
        if year is None:
            year = timezone.now().year
        else:
            try:
                year = int(year)  # 确保年份是整数
            except ValueError:
                return JsonResponse({'error': '年份格式无效'}, status=400)
            
        from my.models import BrewingRecord
        calendar_result = BrewingRecord.objects.generate_calendar_data(int(user_id), year)  # 转换为整数
        
        return JsonResponse({
            'calendar_data': calendar_result,
            'current_month_index': timezone.now().month - 1  # 转换为0基索引
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@api_view(['GET'])
@csrf_exempt
@authentication_classes([])
@permission_classes([AllowAny])
def api_brewing_stats(request):
    """获取冲煮记录统计数据 API"""
    try:
        user_id = request.headers.get('X-User-ID')
        if not user_id:
            return JsonResponse({'error': '缺少用户ID'}, status=400)
            
        time_range = request.GET.get('time_range', 'week')
        
        # 从数据库获取用户对象
        from django.contrib.auth import get_user_model
        User = get_user_model()
        user = User.objects.get(id=int(user_id))
        
        # 获取统计数据
        from my.models import HindsightStats
        stats_data = HindsightStats.get_stats(user, time_range)
        
        return JsonResponse({
            'stats': stats_data['stats'],
            'total_records': stats_data['total_records']
        })
    except User.DoesNotExist:
        return JsonResponse({'error': '用户不存在'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

# ViewSets for API
class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

@method_decorator(csrf_exempt, name='dispatch')
@authentication_classes([])
@permission_classes([AllowAny])
class BrewLogViewSet(viewsets.ModelViewSet):
    serializer_class = BrewLogSerializer
    pagination_class = StandardResultsSetPagination
    
    def get_queryset(self):
        user_id = self.request.headers.get('X-User-ID')
        if not user_id:
            return BrewingRecord.objects.none()
            
        return BrewingRecord.objects.filter(
            user_id=user_id
        ).select_related(
            'brewing_equipment',
            'grinding_equipment',
            'coffee_bean',
            'gadget_kit'
        ).prefetch_related(
            'gadgets',
            'coffee_bean__flavor_tags',
            'coffee_bean__blend_components'
        ).order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        # 如果没有指定页码，默认获取第一页
        if 'page' not in request.query_params:
            request.query_params._mutable = True
            request.query_params['page'] = '1'
            request.query_params._mutable = False
            
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return Response({
                'results': serializer.data,
                'count': self.paginator.page.paginator.count,
                'next': self.paginator.get_next_link(),
                'previous': self.paginator.get_previous_link()
            })
        serializer = self.get_serializer(queryset, many=True)
        return Response({'results': serializer.data})

@method_decorator(csrf_exempt, name='dispatch')
@authentication_classes([])
@permission_classes([AllowAny])
class EquipmentViewSet(viewsets.ModelViewSet):
    serializer_class = EquipmentSerializer
    pagination_class = StandardResultsSetPagination
    
    def get_queryset(self):
        user_id = self.request.headers.get('X-User-ID')
        if not user_id:
            return Equipment.objects.none()
            
        return Equipment.objects.filter(
            user_id=user_id,
            is_deleted=False
        ).select_related(
            'user'
        ).prefetch_related(
            'gadget_components'
        ).order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return Response({
                'results': serializer.data,
                'count': self.paginator.page.paginator.count,
                'next': self.paginator.get_next_link(),
                'previous': self.paginator.get_previous_link()
            })
        serializer = self.get_serializer(queryset, many=True)
        return Response({'results': serializer.data})

@method_decorator(csrf_exempt, name='dispatch')
@authentication_classes([])
@permission_classes([AllowAny])
class BeanViewSet(viewsets.ModelViewSet):
    serializer_class = BeanSerializer
    pagination_class = StandardResultsSetPagination
    
    def get_queryset(self):
        user_id = self.request.headers.get('X-User-ID')
        if not user_id:
            return CoffeeBean.objects.none()
            
        return CoffeeBean.objects.filter(
            user_id=user_id,
            is_deleted=False
        ).select_related(
            'user'
        ).prefetch_related(
            'blend_components',
            'flavor_tags',
            'occurrences'
        ).order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return Response({
                'results': serializer.data,
                'count': self.paginator.page.paginator.count,
                'next': self.paginator.get_next_link(),
                'previous': self.paginator.get_previous_link()
            })
        serializer = self.get_serializer(queryset, many=True)
        return Response({'results': serializer.data})

@method_decorator(csrf_exempt, name='dispatch')
@authentication_classes([])
@permission_classes([AllowAny])
class BrewLogHindsightViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = BrewLogHindsightSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        user_id = self.request.headers.get('X-User-ID')
        if not user_id:
            return BrewingRecord.objects.none()
            
        return BrewingRecord.objects.select_related(
            'brewing_equipment',
            'coffee_bean'
        ).filter(
            user_id=user_id,
            is_deleted=False
        ).order_by('-created_at')

@method_decorator(csrf_exempt, name='dispatch')
@authentication_classes([])
@permission_classes([AllowAny])
class BrewLogHeatmapViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = BrewLogHeatmapSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        user_id = self.request.headers.get('X-User-ID')
        if not user_id:
            return BrewingRecord.objects.none()
            
        return BrewingRecord.objects.select_related(
            'brewing_equipment',
            'coffee_bean'
        ).filter(
            user_id=user_id,
            is_deleted=False
        ).order_by('-created_at')
