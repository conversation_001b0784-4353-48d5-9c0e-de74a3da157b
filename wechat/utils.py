import requests
from django.conf import settings
from rest_framework_simplejwt.tokens import RefreshToken

def get_wechat_session_key(code):
    """
    获取微信小程序 session_key
    
    Args:
        code: 小程序登录时获取的 code
        
    Returns:
        tuple: (session_key, openid, unionid)
            - session_key: 会话密钥
            - openid: 用户唯一标识
            - unionid: 用户在开放平台的唯一标识（如果有）
    """
    app_id = settings.WECHAT_MINI_PROGRAM['APP_ID']
    app_secret = settings.WECHAT_MINI_PROGRAM['APP_SECRET']
    
    url = 'https://api.weixin.qq.com/sns/jscode2session'
    params = {
        'appid': app_id,
        'secret': app_secret,
        'js_code': code,
        'grant_type': 'authorization_code'
    }
    
    try:
        response = requests.get(url, params=params)
        data = response.json()
        
        if 'errcode' in data:
            raise ValueError(f"获取session_key失败: {data.get('errmsg', '未知错误')}")
            
        return (
            data.get('session_key'),
            data.get('openid'),
            data.get('unionid')
        )
        
    except Exception as e:
        raise ValueError(f"请求微信接口失败: {str(e)}")

def create_token(user):
    """
    为用户创建JWT令牌
    
    Args:
        user: 用户对象
        
    Returns:
        str: JWT令牌
    """
    refresh = RefreshToken.for_user(user)
    return str(refresh.access_token) 