# Generated by Django 4.2.7 on 2025-01-21 16:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wechat', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='wechatuser',
            options={},
        ),
        migrations.RemoveField(
            model_name='wechatuser',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='wechatuser',
            name='updated_at',
        ),
        migrations.AddField(
            model_name='wechatuser',
            name='bound_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='wechatuser',
            name='avatar_url',
            field=models.URLField(blank=True, max_length=500, null=True),
        ),
        migrations.AlterField(
            model_name='wechatuser',
            name='nickname',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='wechatuser',
            name='openid',
            field=models.CharField(max_length=50, unique=True),
        ),
        migrations.AlterField(
            model_name='wechatuser',
            name='unionid',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='wechatuser',
            name='user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
