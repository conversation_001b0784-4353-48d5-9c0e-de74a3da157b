# Generated by Django 4.2.7 on 2025-01-20 13:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WechatUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('openid', models.CharField(max_length=100, unique=True, verbose_name='微信OpenID')),
                ('unionid', models.CharField(blank=True, max_length=100, null=True, verbose_name='微信UnionID')),
                ('nickname', models.CharField(blank=True, max_length=100, null=True, verbose_name='微信昵称')),
                ('avatar_url', models.URLField(blank=True, null=True, verbose_name='头像URL')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='wechat_user', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '微信用户',
                'verbose_name_plural': '微信用户',
            },
        ),
    ]
