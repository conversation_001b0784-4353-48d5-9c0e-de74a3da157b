from django.db import models, transaction
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

# Create your models here.

class WechatUser(models.Model):
    """微信用户模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    openid = models.CharField(max_length=50, unique=True)
    unionid = models.CharField(max_length=50, null=True, blank=True)
    nickname = models.CharField(max_length=50, null=True, blank=True)
    avatar_url = models.URLField(max_length=500, null=True, blank=True)
    bound_time = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.nickname or self.openid}"

    def save(self, *args, sync_to_user=True, **kwargs):
        # 如果有关联用户且昵称不为空，且需要同步，则同步到用户的 first_name
        if sync_to_user and self.user and self.nickname:
            self.user.first_name = self.nickname
            self.user.save()
        super().save(*args, **kwargs)

    def merge_user_data(self, target_user):
        """
        将临时用户的数据合并到目标用户
        
        Args:
            target_user: 目标用户（网站账号）
            
        Returns:
            bool: 合并是否成功
        """
        from my.models import (
            Favorite, Equipment, CoffeeBean, BrewingRecord,
            FlavorTag, BeanOccurrence
        )
        
        temp_user = self.user
        if not temp_user:
            return False
            
        try:
            with transaction.atomic():
                # 1. 合并收藏数据
                Favorite.objects.filter(user=temp_user).update(user=target_user)
                
                # 2. 合并设备数据
                Equipment.objects.filter(user=temp_user).update(user=target_user)
                
                # 3. 合并咖啡豆数据
                coffee_beans = CoffeeBean.objects.filter(user=temp_user)
                for bean in coffee_beans:
                    # 先更新咖啡豆的所有者
                    bean.user = target_user
                    bean.save()
                    
                    # 然后更新相关的回购记录
                    BeanOccurrence.objects.filter(coffee_bean=bean).update(coffee_bean=bean)
                
                # 4. 合并冲煮记录
                BrewingRecord.objects.filter(user=temp_user).update(user=target_user)
                
                # 5. 合并风味标签
                FlavorTag.objects.filter(user=temp_user).update(user=target_user)
                
                # 6. 更新微信用户关联和同步昵称
                self.user = target_user
                self.bound_time = timezone.now()
                if self.nickname:
                    target_user.first_name = self.nickname
                    target_user.save()
                self.save(sync_to_user=False)  # 避免循环更新
                
                # 7. 删除临时用户
                temp_user.delete()
                
                return True
                
        except Exception as e:
            # 记录错误日志
            import logging
            logger = logging.getLogger('django')
            logger.error(f'Data merge failed: {str(e)}', exc_info=True)
            return False
