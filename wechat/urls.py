from django.urls import path, include
from . import views
from rest_framework.routers import DefaultRouter

app_name = 'wechat'

# ViewSet路由
router = DefaultRouter()
router.register(r'brewlogs', views.BrewLogViewSet, basename='api-brewlog')
router.register(r'equipment', views.EquipmentViewSet, basename='api-equipment')
router.register(r'beans', views.BeanViewSet, basename='api-bean')
router.register(r'hindsight', views.BrewLogHindsightViewSet, basename='api-hindsight')
router.register(r'heatmap', views.BrewLogHeatmapViewSet, basename='api-heatmap')

urlpatterns = [
    path('login/', views.wechat_login, name='login'),
    path('update-user-info/', views.update_user_info, name='update_user_info'),
    path('bind-account/', views.bind_account, name='bind_account'),
    
    # 小程序API
    path('api/brewlogs/available-years/', views.api_get_available_years, name='api-available-years'),
    path('api/brewlogs/stats/', views.api_brewing_stats, name='api-stats'),
    path('api/brewlogs/hindsight/', views.api_brewing_hindsight, name='api-hindsight'),
    path('api/brewlogs/heatmap/', views.api_brewlog_heatmap, name='api-heatmap'),
    
    # ViewSet API路由
    path('api/', include(router.urls)),
] 