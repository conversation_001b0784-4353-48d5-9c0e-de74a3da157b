from django.db import models
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from wagtail.models import Page
from wagtail.admin.panels import FieldPanel, MultiFieldPanel
from wagtail.search import index
from wagtailmarkdown.fields import MarkdownField
from django.utils.translation import gettext_lazy as _
from datetime import datetime
from wagtail.fields import StreamField
from wagtail import blocks
from multiselectfield import MultiSelectField
from django.db.models import Q

class RecipePage(Page):
    # 发布信息
    date = models.DateTimeField("发布日期", blank=True, null=True, default=datetime.today)
    author = models.CharField("作者", blank=True, null=True, max_length=50, default="Anon")

    # 食谱描述
    zh_name = models.CharField("中文品名", blank=True, null=True, max_length=100)
    en_name = models.CharField("英文品名", blank=True, null=True, max_length=100)
    intro = models.TextField(_('简介'), blank=True, null=True)
    cover_image = models.URLField("封面主图", blank=True, null=True)
    body = MarkdownField("详细做法", blank=True, null=True)

    # 食谱属性
    # 定义食谱类别的选项
    TYPE_CHOICES = (
        ('cold', '冷饮'),
        ('hot', '热饮'),
        ('special', '特调'),
        ('latte', '奶咖'),
        ('tea', '茶咖'),
        ('liquor', '酒咖'),
        ('fruit', '果咖'),
        ('black', '黑咖'),
    )
    recipe_type = MultiSelectField(choices=TYPE_CHOICES, max_length=255)
    def get_recipe_type_list(self):
        return self.recipe_type.split(',')
    difficulty = models.CharField(_('难度'), blank=True, null=True, max_length=50, choices=[('easy', _('简单')), ('medium', _('普通')), ('hard', _('困难'))], default='easy')
    prep_time = models.PositiveIntegerField(_('总时间'), blank=True, null=True, default=3, help_text=_('预计总制作时长，单位为分钟'))

    # 成分列表
    ingredient_block = blocks.StructBlock([
        ('name', blocks.CharBlock(required=False, max_length=250, help_text='成分名称')),
        ('unit', blocks.CharBlock(required=False, max_length=50, help_text='成分单位')),
        ('note', blocks.CharBlock(required=False, max_length=250, help_text='备注')),
    ], icon='table')
    ingredient_list = StreamField([
        ('table', ingredient_block),
    ], null=True, blank=True, use_json_field=True)

    # 器具列表
    tool_block = blocks.StructBlock([
        ('name', blocks.CharBlock(required=False, max_length=250, help_text='器具名称')),
        ('unit', blocks.CharBlock(required=False, max_length=50, help_text='器具单位')),
        ('note', blocks.CharBlock(required=False, max_length=250, help_text='备注')),
    ], icon='cogs')
    tool_list = StreamField([
        ('cogs', tool_block),
    ], null=True, blank=True, use_json_field=True)

    # 问题列表
    faq_block = blocks.StructBlock([
        ('question', blocks.CharBlock(required=True, max_length=50, help_text='问题')),
        ('answer', blocks.RichTextBlock(required=True, help_text='回答')),
    ], icon='help')
    faq_list = StreamField([
        ('help', faq_block),
    ], null=True, blank=True, use_json_field=True)

    search_fields = Page.search_fields + [
        index.SearchField('title'),
        index.SearchField('body'),
    ]

    content_panels = Page.content_panels + [
        MultiFieldPanel([
            FieldPanel('date'),
            FieldPanel('author'),
        ], heading="基本信息"),
        MultiFieldPanel([
            FieldPanel('zh_name'),
            FieldPanel('en_name'),
            FieldPanel('intro'),
            FieldPanel('cover_image'),
        ], heading="食谱描述"),
        FieldPanel('body', heading="详细做法"),
        MultiFieldPanel([
            FieldPanel('recipe_type'),
            FieldPanel('difficulty'),
            FieldPanel('prep_time'),
        ], heading="食谱属性"),
        FieldPanel('ingredient_list', heading="成分列表"),
        FieldPanel('tool_list', heading="器具列表"),
        FieldPanel('faq_list', heading="FAQs列表"),
    ]

class RecipeIndexPage(Page):
    intro = MarkdownField(blank=True)

    content_panels = Page.content_panels + [
        FieldPanel('intro', classname="full")
    ]

    def get_context(self, request):
        context = super().get_context(request)
        recipepages = RecipePage.objects.child_of(self).live().order_by('-date')

        # 获取筛选条件
        search_query = request.GET.get('q', '')
        recipe_type = request.GET.getlist('recipe_type', [])
        difficulty = request.GET.get('difficulty', '')

        # 应用筛选条件
        # 如果存在搜索词，分割搜索词并构建查询
        if search_query:
            # 分割搜索词，假设搜索词由空格分隔
            search_terms = search_query.split()
            # 构建查询，确保每个搜索词都在 zh_name 或 body 中部分匹配
            query = Q()
            for term in search_terms:
                query |= Q(zh_name__icontains=term) | Q(body__icontains=term)
            recipepages = recipepages.filter(query)

        if difficulty:
            recipepages = recipepages.filter(difficulty=difficulty)

        if recipe_type and recipe_type != ['']:
            filtered_recipes = []
            for recipe in recipepages:
                if any(rt in recipe.recipe_type for rt in recipe_type):
                    filtered_recipes.append(recipe)
            recipepages = filtered_recipes

        # 分页处理
        paginator = Paginator(recipepages, 12)
        page = request.GET.get('page')
        try:
            recipepages = paginator.page(page)
        except PageNotAnInteger:
            recipepages = paginator.page(1)
        except EmptyPage:
            recipepages = paginator.page(paginator.num_pages)

        context['recipepages'] = recipepages
        context['search_query'] = search_query
        context['recipe_type'] = recipe_type
        context['difficulty'] = difficulty
        return context