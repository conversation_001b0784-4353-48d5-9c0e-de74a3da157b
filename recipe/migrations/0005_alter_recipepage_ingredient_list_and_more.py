# Generated by Django 4.2.7 on 2024-07-20 22:49

from django.db import migrations
import wagtail.blocks
import wagtail.fields


class Migration(migrations.Migration):

    dependencies = [
        ('recipe', '0004_alter_recipepage_faq_list_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='recipepage',
            name='ingredient_list',
            field=wagtail.fields.StreamField([('table', wagtail.blocks.StructBlock([('name', wagtail.blocks.CharBlock(help_text='成分名称', max_length=250, required=False)), ('unit', wagtail.blocks.CharBlock(help_text='成分单位', max_length=50, required=False)), ('note', wagtail.blocks.CharBlock(help_text='备注', max_length=250, required=False))], icon='table'))], blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='recipepage',
            name='tool_list',
            field=wagtail.fields.StreamField([('cogs', wagtail.blocks.StructBlock([('name', wagtail.blocks.CharBlock(help_text='器具名称', max_length=250, required=False)), ('unit', wagtail.blocks.CharBlock(help_text='器具单位', max_length=50, required=False)), ('note', wagtail.blocks.CharBlock(help_text='备注', max_length=250, required=False))], icon='cogs'))], blank=True, null=True),
        ),
    ]
