# Generated by Django 4.2.7 on 2024-07-20 20:54

from django.db import migrations
import wagtail.blocks
import wagtail.fields


class Migration(migrations.Migration):

    dependencies = [
        ('recipe', '0002_alter_recipepage_intro'),
    ]

    operations = [
        migrations.AlterField(
            model_name='recipepage',
            name='faq_list',
            field=wagtail.fields.StreamField([('help', wagtail.blocks.StructBlock([('name', wagtail.blocks.CharBlock(help_text='器具名称', max_length=250, required=True)), ('unit', wagtail.blocks.CharBlock(help_text='器具单位', max_length=50, required=True)), ('note', wagtail.blocks.CharBlock(help_text='备注', max_length=250))], icon='cogs'))], blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='recipepage',
            name='ingredient_list',
            field=wagtail.fields.StreamField([('table', wagtail.blocks.StructBlock([('name', wagtail.blocks.CharBlock(help_text='成分名称', max_length=250, required=True)), ('unit', wagtail.blocks.CharBlock(help_text='成分单位', max_length=50, required=True)), ('note', wagtail.blocks.CharBlock(help_text='备注', max_length=250))], icon='table'))], blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='recipepage',
            name='tool_list',
            field=wagtail.fields.StreamField([('cogs', wagtail.blocks.StructBlock([('name', wagtail.blocks.CharBlock(help_text='器具名称', max_length=250, required=True)), ('unit', wagtail.blocks.CharBlock(help_text='器具单位', max_length=50, required=True)), ('note', wagtail.blocks.CharBlock(help_text='备注', max_length=250))], icon='cogs'))], blank=True, null=True),
        ),
    ]
