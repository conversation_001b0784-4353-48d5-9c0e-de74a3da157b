# Generated by Django 4.2.7 on 2024-07-20 21:10

from django.db import migrations
import wagtail.blocks
import wagtail.fields


class Migration(migrations.Migration):

    dependencies = [
        ('recipe', '0003_alter_recipepage_faq_list_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='recipepage',
            name='faq_list',
            field=wagtail.fields.StreamField([('help', wagtail.blocks.StructBlock([('question', wagtail.blocks.CharBlock(help_text='问题', max_length=50, required=True)), ('answer', wagtail.blocks.RichTextBlock(help_text='回答', required=True))], icon='help'))], blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='recipepage',
            name='ingredient_list',
            field=wagtail.fields.StreamField([('table', wagtail.blocks.StructBlock([('name', wagtail.blocks.CharBlock(help_text='成分名称', max_length=250)), ('unit', wagtail.blocks.CharBlock(help_text='成分单位', max_length=50)), ('note', wagtail.blocks.CharBlock(help_text='备注', max_length=250))], icon='table'))], blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='recipepage',
            name='tool_list',
            field=wagtail.fields.StreamField([('cogs', wagtail.blocks.StructBlock([('name', wagtail.blocks.CharBlock(help_text='器具名称', max_length=250)), ('unit', wagtail.blocks.CharBlock(help_text='器具单位', max_length=50)), ('note', wagtail.blocks.CharBlock(help_text='备注', max_length=250))], icon='cogs'))], blank=True, null=True),
        ),
    ]
