# Generated by Django 4.2.7 on 2024-07-20 20:29

import datetime
from django.db import migrations, models
import django.db.models.deletion
import multiselectfield.db.fields
import wagtail.blocks
import wagtail.fields
import wagtailmarkdown.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('wagtailcore', '0091_remove_revision_submitted_for_moderation'),
    ]

    operations = [
        migrations.CreateModel(
            name='RecipeIndexPage',
            fields=[
                ('page_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='wagtailcore.page')),
                ('intro', wagtailmarkdown.fields.MarkdownField(blank=True)),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
        migrations.CreateModel(
            name='RecipePage',
            fields=[
                ('page_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='wagtailcore.page')),
                ('date', models.DateTimeField(blank=True, default=datetime.datetime.today, null=True, verbose_name='发布日期')),
                ('author', models.CharField(blank=True, default='Anon', max_length=50, null=True, verbose_name='作者')),
                ('zh_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='中文品名')),
                ('en_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='英文品名')),
                ('intro', models.CharField(blank=True, max_length=250, null=True, verbose_name='简介')),
                ('cover_image', models.URLField(blank=True, null=True, verbose_name='封面主图')),
                ('body', wagtailmarkdown.fields.MarkdownField(blank=True, null=True, verbose_name='详细做法')),
                ('recipe_type', multiselectfield.db.fields.MultiSelectField(choices=[('cold', '冷饮'), ('hot', '热饮'), ('special', '特调')], max_length=255)),
                ('difficulty', models.CharField(blank=True, choices=[('easy', '简单'), ('medium', '普通'), ('hard', '困难')], default='easy', max_length=50, null=True, verbose_name='难度')),
                ('prep_time', models.PositiveIntegerField(blank=True, default=3, help_text='预计总制作时长，单位为分钟', null=True, verbose_name='总时间')),
                ('ingredient_list', wagtail.fields.StreamField([('table', wagtail.blocks.StructBlock([('name', wagtail.blocks.CharBlock(help_text='成分名称', max_length=250, required=True)), ('unit', wagtail.blocks.CharBlock(help_text='成分单位', max_length=50, required=True)), ('note', wagtail.blocks.CharBlock(help_text='备注', max_length=250, required=True))], icon='table'))], blank=True, null=True)),
                ('tool_list', wagtail.fields.StreamField([('cogs', wagtail.blocks.StructBlock([('name', wagtail.blocks.CharBlock(help_text='器具名称', max_length=250, required=True)), ('unit', wagtail.blocks.CharBlock(help_text='器具单位', max_length=50, required=True)), ('note', wagtail.blocks.CharBlock(help_text='备注', max_length=250, required=True))], icon='cogs'))], blank=True, null=True)),
                ('faq_list', wagtail.fields.StreamField([('help', wagtail.blocks.StructBlock([('name', wagtail.blocks.CharBlock(help_text='器具名称', max_length=250, required=True)), ('unit', wagtail.blocks.CharBlock(help_text='器具单位', max_length=50, required=True)), ('note', wagtail.blocks.CharBlock(help_text='备注', max_length=250, required=True))], icon='cogs'))], blank=True, null=True)),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
    ]
