from django.shortcuts import render, get_object_or_404
from django.db.models import Count
from django.contrib.contenttypes.models import ContentType
from .models import RecipePage
from my.models import Favorite

def recipe_page(request, slug):
    page = get_object_or_404(RecipePage, slug=slug)
    recipe_content_type = ContentType.objects.get_for_model(RecipePage)

    is_favorited = False
    if request.user.is_authenticated:
        is_favorited = Favorite.objects.filter(user=request.user, content_type=recipe_content_type, object_id=page.id).exists()

    # 获取当前页面的 recipe_type
    current_recipe_types = set(page.recipe_type)

    # 查找所有其他发布的食谱
    all_recipes = RecipePage.objects.live().exclude(id=page.id)

    # 筛选出与当前页面的 recipe_type 存在重叠选项的食谱
    related_recipes = []
    for recipe in all_recipes:
        recipe_types_set = set(recipe.recipe_type)
        overlap_count = len(current_recipe_types & recipe_types_set)
        if overlap_count > 0:
            related_recipes.append((recipe, overlap_count))

    # 按重叠数从多到少、然后从新到旧排序
    related_recipes.sort(key=lambda x: (-x[1], -x[0].date.timestamp()))
    related_recipes = [recipe[0] for recipe in related_recipes[:5]]

    context = {
        'page': page,
        'is_favorited': is_favorited,
        'faq_list': page.specific.faq_list,
        'faq_list_length': len(page.specific.faq_list),
        'related_recipes': related_recipes,  # 添加相关食谱到上下文
    }

    return render(request, 'recipe/recipe_page.html', context)