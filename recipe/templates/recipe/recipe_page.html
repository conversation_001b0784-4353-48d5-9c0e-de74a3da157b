{% extends "base.html" %}
{% load wagtailcore_tags wagtailimages_tags wagtailmarkdown static %}
{% block body_class %}template-recipepage{% endblock %}

{% block title %}
  {% if page.seo_title %}
  {{ page.seo_title }} | 咖啡食谱
  {% else %}
  {{ page.zh_name }}的制作过程和方法 | 咖啡食谱
  {% endif %}
{% endblock %}

{% block content %}
<main class="flex flex-col items-center text-base-content">
  <!-- basic info -->
  <section class="container m-0 p-0">
    <div class="flex flex-col lg:flex-row justify-center items-center py-8">
      <div class="flex flex-col lg:flex-row items-center lg:items-start lg:max-w-6xl w-full px-4">
        <img src="{{ page.cover_image }}" class="rounded-sm w-full lg:w-1/2 max-h-96 object-cover mb-8 lg:mb-0 lg:mr-8" alt="{{ page.title }}" />
        <div class="w-full lg:w-1/2 flex flex-col items-center">
          <h1 class="text-3xl font-bold">{{ page.zh_name }}</h1>
          <p class="text-xl py-6 opacity-50">{{ page.en_name }}</p>
          <div class="flex flex-row flex-wrap gap-4 text-lg opacity-70">
            <div class="flex items-center gap-1">
              <span class="icon-[pepicons-pop--countdown]"></span>
              <span>{{ page.prep_time }} 分钟</span>
            </div>
            <div class="flex items-center gap-1">
              {% if page.difficulty == "easy" %}
              <span class="icon-[game-icons--rank-1]"></span>
              {% elif page.difficulty == "medium" %}
              <span class="icon-[game-icons--rank-2]"></span>
              {% elif page.difficulty == "hard" %}
              <span class="icon-[game-icons--rank-3]"></span>
              {% endif %}
              <span>{{ page.get_difficulty_display }}</span>
            </div>
          </div>
          <div class="divider"></div>
          <!-- Actions -->
          <div class="flex flex-row flex-wrap gap-4">
            <!-- Add Fav -->
            {% if user.is_authenticated %}
            <div class="flex justify-center lg:justify-start">
              <input type="hidden" id="csrf-token" value="{{ csrf_token }}" />
              <div x-data="{ favorited: {{ is_favorited|yesno:'true,false' }}, csrfToken: '{{ csrf_token }}' }">
                <button x-on:click="fetch(favorited ? '{% url "remove_favorite_by_type" "recipe" "recipepage" page.id %}' : '{% url "add_favorite" "recipe" "recipepage" page.id %}', {
                                  method: 'POST',
                                  headers: {
                                      'X-CSRFToken': csrfToken,
                                      'Content-Type': 'application/json'
                                  }
                              }).then(response => response.json()).then(data => {
                                  favorited = data.favorited;
                              })" x-bind:class="favorited ? 'btn-ghost' : 'btn-outline'" x-bind:aria-pressed="favorited"
                  class="btn btn-ghost">
                  <span x-show="favorited" class="icon-[bi--bookmark-star-fill]"></span>
                  <span x-show="!favorited" class="icon-[bi--bookmark-star]"></span>
                  <span x-text="favorited ? '已收藏' : '收藏'"></span>
                </button>
                <div id="favorite-btn-indicator" style="display: none;"><span
                    class="loading loading-spinner text-primary"></span></div>
              </div>
            </div>
            {% else %}
            <a href="{% url 'account_login' %}" class="btn btn-outline">
              <span class="icon-[bi--bookmark-star]"></span>
              收藏
            </a>
            {% endif %}
            <!-- End Add Fav -->
            <button class="btn btn-outline">
              <span class="icon-[ps--printer]"></span>打印
            </button>
          </div>
          <!--End Actions-->
          <div x-data="{ expanded: false, maxLength: 200, description: '{{ page.intro|markdown|escapejs }}' }" class="max-w-prose m-auto relative pt-4">
            <div x-html="expanded ? description : (description.slice(0, maxLength) + (description.length > maxLength ? '...' : ''))" class="prose text-xl inline text-ellipsis overflow-hidden">
            </div>
            <a href="#" x-show="description.length > maxLength" x-on:click.prevent="expanded = !expanded" class="text-info inline">
              <span x-text="expanded ? '收起 ▲' : '展开 ▼'"></span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- content -->
  <section class="container mx-auto px-4">
    <div class="flex flex-col lg:flex-row justify-center items-center">
      <div class="flex flex-col-reverse lg:flex-row lg:max-w-6xl w-full">
        <!-- instruction -->
        <div class="lg:w-2/3 w-full p-4 lg:pr-8">
          <h2 class="text-2xl font-bold text-primary mb-4">{{ page.zh_name }}的做法</h2>
          <article class="break-all prose prose-stone">{{ page.body|markdown }}</article>
        </div>
        <!-- list -->
        <div class="lg:w-1/3 w-full flex flex-col items-center lg:items-start px-4 lg:px-0 mb-8 lg:mb-0">
          <div class="md:ps-4 lg:ps-8 w-full">
            <div class="grid gap-2 mt-6 px-4 md:px-0">
              <div class="grid">
                <div class="divider pb-4 menu-title">所需材料</div>
                <div class="flow-root">
                  <dl class="-my-3 divide-y divide-base-200 text-sm">
                    {% for ingredients in page.specific.ingredient_list %}
                    <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-2 sm:gap-4">
                      <dt class="font-medium text-left">{{ ingredients.value.name }}</dt>
                      <dd class="opacity-70">{{ ingredients.value.unit }}</dd>
                      {% if ingredients.value.note %}
                      <dd class="opacity-70 sm:col-span-3 flex items-center">
                        <span class="icon-[icons8--idea] mr-1 flex-shrink-0"></span>
                        {{ ingredients.value.note }}
                      </dd>
                      {% endif %}
                    </div>
                    {% endfor %}
                  </dl>
                </div>
              </div>
              {% if page.specific.tool_list %}
              <div class="grid">
                <div class="divider pb-4 menu-title">所需器具</div>
                <div class="flow-root">
                  <dl class="-my-3 divide-y divide-base-200 text-sm">
                    {% for tools in page.specific.tool_list %}
                    <div class="grid grid-cols-1 gap-1 py-3 sm:grid-cols-3 sm:gap-4">
                      <dt class="font-medium text-left">{{ tools.value.name }}</dt>
                      <dd class="opacity-70">{{ tools.value.unit }}</dd>
                      {% if tools.value.note %}
                      <dd class="opacity-70 sm:col-span-3 flex items-center"><span class="icon-[icons8--idea] mr-1"></span>{{ tools.value.note }}</dd>
                      {% endif %}
                    </div>
                    {% endfor %}
                  </dl>
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- FAQs -->
  {% if faq_list %}
  <section class="container mx-auto px-4">
    <div class="pt-10 pb-5 10 px-6 md:px-0 max-w-prose m-auto relative">
      <h2 class="text-2xl font-bold text-primary mb-4">常见问题</h2>
      <div class="{% if faq_list_length > 1 %}join join-vertical w-full{% endif %}">
        {% for faqs in faq_list %}
        <div class="collapse collapse-plus join-item border border-base-300">
          <input type="checkbox" class="w-auto h-auto" />
          <div class="collapse-title text-xl font-medium">
            {{ faqs.value.question }}
          </div>
          <div class="collapse-content prose prose-stone">
            {{ faqs.value.answer|richtext }}
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
  </section>
  {% endif %}
  <!-- Related -->
  {% if related_recipes %}
  <section class="container mx-auto px-4 pb-8">
    <div class="space-y-6 justify-center">
      <div class="divider pb-4 menu-title max-w-md mx-auto">相关配方</div>
      <div class="grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-y-4 lg:gap-y-14 gap-x-14 mt-1 mb-5 mx-auto">
        {% for recipe in related_recipes %}
        <a href="{% pageurl recipe %}" class="group relative flex h-48 items-end justify-end overflow-hidden rounded-lg bg-gray-100 shadow-lg md:h-96">
          <img src="{{ recipe.cover_image }}" loading="lazy" alt="{{ recipe.title }}的做法" class="absolute inset-0 h-full w-full object-cover object-center transition duration-200 group-hover:scale-110" />
          <div class="pointer-events-none absolute inset-0 bg-gradient-to-t from-gray-800 via-transparent to-transparent opacity-50"></div>
          <span class="relative mr-3 mb-3 inline-block rounded-lg border border-gray-500 px-2 py-1 text-xs text-gray-200 backdrop-blur-sm md:px-3 md:text-sm">{{ recipe.title }}</span>
        </a>
        {% endfor %}
      </div>
    </div>
  </section>
  {% endif %}
</main>
{% endblock %}

{% block extra_js %}
<script defer type="text/javascript" src="{% static 'js/rcpsht.js' %}"></script>
{% endblock %}