{% extends "base.html" %}
{% load static %}
{% load wagtailcore_tags wagtailimages_tags %}
{% block body_class %}template-recipeindexpage{% endblock %}

{% block content %}
<!-- heading -->
<div class="py-2 px-1 lg:p-12 bg-base-200 mb-2 lg:py-8 lg:px-4">
    <div class="w-full lg:w-6/12 mx-auto">
        <h1 class="text-xl text-center text-base-content">
            {{ page.title }}
        </h1>
        {% if page.intro %}
        <div class="text-sm text-center text-base-content opacity-60">
            {{ page.intro|richtext }}
        </div>
        {% endif %}
    </div>
</div>
<!-- filter -->
<div x-data="{ isOpen: false }" class="card text-base-content">
    <div class="card-body w-fit mx-auto">
        <div class="lg:hidden flex justify-center">
            <button @click="isOpen = !isOpen"
                class="mt-2 flex w-full justify-center items-center px-4 py-2 md:mt-0 md:w-auto md:p-0">
                <span class="icon-[oui--filter]"></span>
                <span x-text="isOpen ? '收起筛选功能' : '展开筛选功能'" class="ml-1 font-medium"></span>
            </button>
        </div>
        <form id="filter-form" class="flex flex-col items-start lg:gap-x-12 lg:flex-row lg:justify-between" x-show="isOpen || window.innerWidth >= 1024" method="get">
            <div class="form-group lg:flex-1 mb-4 lg:mb-0">
                <label class="form-control w-full max-w-xs">
                    <div class="label">
                        <span class="label-text">搜索词</span>
                    </div>
                    <input type="text" name="q" placeholder="请输入关键词" value="{{ search_query }}"
                        class="input max-w-xs" />
                </label>
            </div>
            <div class="form-group lg:flex-1 mb-4 lg:mb-0">
                <label class="form-control w-full max-w-xs">
                    <div class="label">
                        <span class="label-text">类别</span>
                    </div>
                    <select name="recipe_type" id="recipe_type" class="select">
                        <option value="">全部</option>
                        <option value="black" {% if 'black' in recipe_type %}selected{% endif %}>☕ 黑咖</option>
                        <option value="latte" {% if 'latte' in recipe_type %}selected{% endif %}>🥛 奶咖</option>
                        <option value="tea" {% if 'tea' in recipe_type %}selected{% endif %}>🍵 茶咖</option>
                        <option value="fruit" {% if 'fruit' in recipe_type %}selected{% endif %}>🍊 果咖</option>
                        <option value="liquor" {% if 'liquor' in recipe_type %}selected{% endif %}>🍸 酒咖</option>
                    </select>
                </label>
            </div>
            <div class="form-group lg:flex-1 mb-4 lg:mb-0">
                <label class="form-control w-full max-w-xs">
                    <div class="label">
                        <span class="label-text">难度</span>
                    </div>
                    <select name="difficulty" id="difficulty" class="select">
                        <option value="">全部</option>
                        <option value="easy" {% if difficulty == 'easy' %}selected{% endif %}>简单</option>
                        <option value="medium" {% if difficulty == 'medium' %}selected{% endif %}>普通</option>
                        <option value="hard" {% if difficulty == 'hard' %}selected{% endif %}>困难</option>
                    </select>
                </label>
            </div>
            <div class="form-group lg:flex-1 mb-4 lg:mb-0">
                <div class="label">
                    <span class="label-text"></span>
                </div>
                <button type="submit" class="btn btn-outline-primary align-baseline mt-4">
                    筛选
                </button>
            </div>
        </form>
    </div>
</div>
<!-- grid -->
<div class="container mx-auto px-4 lg:px-8 xl:max-w-7xl">
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-4">
        {% for post in recipepages %}
        {% with post=post.specific %}
        <a href="{% pageurl post %}" target="_blank" class="card rounded-md shadow-md bg-base-100 hover:bg-base-200 transition-colors text-base-content">
            <figure class="max-h-64">
                <img class="w-full h-auto object-fill object-center" src="{{ post.cover_image }}" alt="{{ post.title }}" decoding="async" loading="lazy">
            </figure>
            <div class="card-body">
                <h3 class="card-title">{{ post.title }}</h3>
                <p class="opacity-80">{{ post.intro|truncatechars:120 }}</p>
                <div class="card-actions justify-between">
                    <div class="opacity-60 flex items-center gap-1">
                        <span class="icon-[pepicons-pop--countdown]"></span>
                        <span>{{ post.prep_time }} 分钟</span>
                    </div>
                    <div class="opacity-60 flex items-center gap-1">
                        {% if post.difficulty == "easy" %}
                        <span class="icon-[game-icons--rank-1]"></span>
                        {% elif post.difficulty == "medium" %}
                        <span class="icon-[game-icons--rank-2]"></span>
                        {% elif post.difficulty == "hard" %}
                        <span class="icon-[game-icons--rank-3]"></span>
                        {% endif %}
                        <span>{{ post.get_difficulty_display }}</span>
                    </div>
                </div>
            </div>
        </a>
        {% endwith %}
        {% endfor %}
    </div>
</div>
<!-- pagination -->
<div class="py-4 px-1 mb-2 lg:py-8 lg:px-4">
    <div class="flex items-center justify-center">
        <ul class="list-none mb-6 flex">
            {% if recipepages.has_previous %}
            <li>
            <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?page={{ recipepages.previous_page_number }}">上一页</a>
            </li>
            {% else %}
            <li>
            <a class="pointer-events-none relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-300 transition-all duration-300">上一页</a>
            </li>
            {% endif %}
            {% for page_num in recipepages.paginator.page_range %}
            {% if recipepages.number == page_num %}
            <li aria-current="page">
            <a class="relative block rounded-sm bg-primary-100 px-3 py-1.5 text-lg font-medium text-primary transition-all duration-300">{{ page_num }}</a>
            </li>
            {% elif page_num > recipepages.number|add:'-3' and page_num < recipepages.number|add:'3' %}
            <li>
            <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?page={{ page_num }}">{{ page_num }}</a>
            </li>
            {% endif %}
            {% endfor %}
            {% if recipepages.has_next %}
            <li>
            <a class="relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-content/70 transition-all duration-300 hover:bg-base-100" href="?page={{ recipepages.next_page_number }}">下一页</a>
            </li>
            {% else %}
            <li>
            <a class="pointer-events-none relative block rounded-sm bg-transparent px-3 py-1.5 text-lg text-base-300 transition-all duration-300">下一页</a>
            </li>
            {% endif %}
        </ul>
    </div>
</div>

{% endblock %}