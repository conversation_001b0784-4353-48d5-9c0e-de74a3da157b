# Web 框架和核心组件
Django>=4.0.5,<5.0.0
wagtail>=6.4.1,<6.5.0
djangorestframework>=3.13.1,<4.0.0
djangorestframework-simplejwt==5.5.0

# Wagtail 相关扩展
wagtail-markdown>=0.11.1,<0.12.0

# 认证与权限
django-allauth>=0.61.1,<0.62.0
django-permissionedforms>=0.1,<0.2
django-ratelimit>=4.1.0,<4.2.0

# 数据库和缓存
django-redis>=5.4.0,<5.5.0
## PostgreSQL 数据库适配器
psycopg2>=2.9.9,<3.0.0

# 功能扩展
django-filter>=23.3,<24.0
django-multiselectfield>=0.1.13,<0.2.0
django-taggit>=5.0,<7.0
django-treebeard>=4.5.1,<5.0.0
django-environ>=0.11.2,<0.12.0
django-cors-headers>=4.6.0,<5.0.0
django-modelcluster>=6.1,<7.0
## 疑似无用
django-etc>=1.4.0,<2.0.0

# 图像处理
Pillow==11.1.0
pillow-heif>=0.13.1,<1.0.0
Willow>=1.6.2,<2.0.0

# 数据处理，疑似只用了pandas未用numpy
numpy>=2.1.3,<3.0.0
pandas>=2.2.3,<3.0.0

# 文件处理
openpyxl>=3.0.10,<4.0.0
XlsxWriter>=3.0.3,<4.0.0
defusedxml>=0.7.1,<1.0.0
filetype>=1.2.0,<2.0.0

# 服务器
gevent>=24.11.1,<25.0.0

# 前端集成
django-webpack-loader>=3.1.1,<4.0.0

# 工具库
anyascii>=0.3.1,<0.4.0
beautifulsoup4>=4.9.3,<5.0.0
requests>=2.28.0,<3.0.0
pytz>=2023.3.post1

# 原始脚手架所含依赖
asgiref>=3.5.2,<4.0.0
certifi>=2022.5.18.1,<2023.0.0
charset-normalizer>=2.0.12,<3.0.0
draftjs-exporter>=2.1.7,<3.0.0
et-xmlfile>=1.1.0,<2.0.0
html5lib>=1.1,<2.0
idna>=3.3,<4.0
l18n>=2021.3,<2022.0
six>=1.16.0,<2.0.0
soupsieve>=2.3.2.post1,<3.0.0
sqlparse>=0.4.2,<0.5.0
tablib>=3.2.1,<4.0.0
telepath>=0.2,<1.0
urllib3>=1.26.9,<2.0
webencodings>=0.5.1,<1.0
xlrd>=2.0.1,<3.0
xlwt>=1.3.0,<2.0