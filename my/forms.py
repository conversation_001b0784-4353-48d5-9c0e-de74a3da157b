from django import forms
from django.forms.widgets import DateInput
from .models import Equipment, CoffeeBean, BrewingRecord, FlavorTag, BlendComponent, BeanOccurrence
from django.utils import timezone
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal

class GadgetMultipleChoiceField(forms.ModelMultipleChoiceField):
    def label_from_instance(self, obj):
        return f"{obj.brand} {obj.name}" if obj.brand else obj.name

class EquipmentForm(forms.ModelForm):
    grinder_purpose = forms.ChoiceField(
        choices=Equipment.GRINDER_PURPOSE_CHOICES,
        required=False,
    )
    
    type = forms.ChoiceField(
        choices=Equipment.EQUIPMENT_TYPES,
        label='设备类型',
        required=True,
        error_messages={
            'required': '请选择设备类型',
            'invalid_choice': '请选择有效的设备类型'
        }
    )
    
    brew_method = forms.ChoiceField(
        choices=Equipment.BREW_METHODS,
        required=False,
        label='冲煮方式',
    )

    notes = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'flex w-full h-auto min-h-[80px] px-3 py-2 text-sm bg-transparent border rounded-md border-neutral-300 ring-offset-background placeholder:text-neutral-400 focus:border-neutral-300 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-neutral-400 disabled:cursor-not-allowed disabled:opacity-50',
            'placeholder': '添加备注（选填）',
            'x-data': '{ resize() { $el.style.height = "0px"; $el.style.height = $el.scrollHeight + "px" } }',
            'x-init': 'resize()',
            '@input': 'resize(); $dispatch("input-change", $el.value.length)',
            'rows': '1',
            'maxlength': '500'
        }),
        required=False,
        label='备注'
    )

    created_at = forms.DateTimeField(
        label='购买时间',
        required=False,
        widget=forms.DateTimeInput(
            attrs={
                'type': 'datetime-local',
                'class': 'grow input input-ghost focus:outline-hidden'
            },
            format='%Y-%m-%dT%H:%M'
        )
    )

    purchase_price = forms.DecimalField(
        label='购买价格',
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'grow input input-ghost focus:outline-hidden',
            'step': '0.01',
            'min': '0'
        })
    )

    # 新增小工具组合字段
    gadget_components = forms.ModelMultipleChoiceField(
        queryset=None,  # 将在 __init__ 中设置
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'checkbox'}),
        label='组合内容'
    )

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        if not user and 'initial' in kwargs:
            user = kwargs['initial'].get('user')
        
        # 如果是编辑现有的小工具组合，确保组件数据正确传递
        instance = kwargs.get('instance')
        if instance and instance.type == 'GADGET_KIT' and not kwargs.get('data'):
            # 创建一个包含当前选中组件的初始数据
            initial_data = kwargs.get('initial', {}).copy()
            initial_data['gadget_components'] = [str(g.id) for g in instance.gadget_components.all()]
            kwargs['initial'] = initial_data
        
        super().__init__(*args, **kwargs)
        
        # 设置 created_at 的初始值
        if self.instance and self.instance.created_at:
            if timezone.is_naive(self.instance.created_at):
                self.instance.created_at = timezone.make_aware(
                    self.instance.created_at,
                    timezone=timezone.get_current_timezone()
                )
            local_time = timezone.localtime(self.instance.created_at)
            self.initial['created_at'] = local_time.strftime('%Y-%m-%dT%H:%M')
        else:
            # 如果是新建设备，默认设置为当前时间
            self.initial['created_at'] = timezone.localtime(timezone.now()).strftime('%Y-%m-%dT%H:%M')

        # 设置小工具组件的查询集
        if user:
            # 获取所有活跃的小工具
            active_gadgets = Equipment.objects.filter(
                user=user,
                type='GADGET',
                is_deleted=False
            )
            
            # 如果是编辑现有组合，添加已选择但可能已归档的小工具
            if self.instance and self.instance.pk and self.instance.type == 'GADGET_KIT':
                selected_gadgets = self.instance.gadget_components.all()
                active_gadgets = (active_gadgets | selected_gadgets).distinct()
            
            self.fields['gadget_components'].queryset = active_gadgets

    def clean(self):
        cleaned_data = super().clean()
        user = self.initial.get('user')
        equipment_type = cleaned_data.get('type')
        name = cleaned_data.get('name')

        if user and equipment_type and name:
            # 构建查询条件
            exists_query = Equipment.objects.filter(
                user=user,
                type=equipment_type,
                name=name,
                is_deleted=False
            )
            
            # 如果是编辑现有设备，排除当前实例
            if self.instance and self.instance.pk:
                exists_query = exists_query.exclude(pk=self.instance.pk)
            
            if exists_query.exists():
                raise ValidationError({
                    'name': _('已存在相同名称的%(type)s，请使用其他名称。') % {
                        'type': dict(Equipment.EQUIPMENT_TYPES).get(equipment_type)
                    }
                })

        # 验证设备类型对应的字段
        if equipment_type == 'BREWER':
            if not cleaned_data.get('brew_method'):
                self.add_error('brew_method', '请选择赛道')
            # 清除磨豆机用途的值
            cleaned_data['grinder_purpose'] = ''
        elif equipment_type == 'GRINDER':
            if not cleaned_data.get('grinder_purpose'):
                self.add_error('grinder_purpose', '请选择用途')
            # 清除冲煮设备赛道的值
            cleaned_data['brew_method'] = ''
        elif equipment_type == 'GADGET_KIT':
            # 验证小工具组合必须选择至少一个小工具
            gadget_components = cleaned_data.get('gadget_components')
            if not gadget_components or len(gadget_components) == 0:
                raise ValidationError({
                    'gadget_components': _('小工具组合必须至少包含一个小工具')
                })
            # 清除其他类型特有的字段
            cleaned_data['brew_method'] = ''
            cleaned_data['grinder_purpose'] = ''
        else:
            # 其他类型设备，清除这两个字段的值
            cleaned_data['brew_method'] = ''
            cleaned_data['grinder_purpose'] = ''
        
        # 如果选择了小工具组合类型，验证是否有活跃的小工具
        if equipment_type == 'GADGET_KIT':
            active_gadgets = Equipment.objects.filter(
                user=user,
                type='GADGET',
                is_archived=False,
                is_deleted=False
            ).exists()
            
            if not active_gadgets:
                raise ValidationError({
                    'type': _('没有可用的小工具，无法创建小工具组合')
                })
        
        return cleaned_data

    def clean_created_at(self):
        created_at = self.cleaned_data.get('created_at')
        if created_at:
            # 确保时区感知
            if timezone.is_naive(created_at):
                created_at = timezone.make_aware(
                    created_at,
                    timezone=timezone.get_current_timezone()
                )
            
            # 标准化datetime格式：移除微秒
            created_at = created_at.replace(microsecond=0)
        
        return created_at

    class Meta:
        model = Equipment
        fields = [
            'type', 'brew_method', 'grinder_purpose', 'name', 'brand', 
            'created_at', 'purchase_price', 'notes', 'gadget_components',
            'grind_size_preset'
        ]
        error_messages = {
            'name': {
                'required': '请输入设备名称',
                'max_length': '设备名称不能超过50个字符'
            }
        }

class BlendComponentForm(forms.ModelForm):
    class Meta:
        model = BlendComponent
        fields = [
            'blend_ratio',
            'roast_level',
            'origin',
            'region',
            'finca',
            'variety',
            'process',
            'altitude_type',
            'altitude_single',
            'altitude_min',
            'altitude_max',
            'order'
        ]
        widgets = {
            'blend_ratio': forms.NumberInput(attrs={
                'class': 'input w-full',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'roast_level': forms.NumberInput(attrs={
                'class': 'range range-primary w-full',
                'type': 'range',
                'min': '1',
                'max': '7',
                'step': '1'
            }),
            'origin': forms.TextInput(attrs={'class': 'input w-full'}),
            'region': forms.TextInput(attrs={'class': 'input w-full'}),
            'finca': forms.TextInput(attrs={'class': 'input w-full'}),
            'variety': forms.TextInput(attrs={'class': 'input w-full'}),
            'process': forms.TextInput(attrs={'class': 'input w-full'}),
            'altitude_type': forms.RadioSelect(attrs={'class': 'radio radio-primary'}),
            'altitude_single': forms.NumberInput(attrs={
                'class': 'input w-full',
                'min': '0',
                'max': '3000'
            }),
            'altitude_min': forms.NumberInput(attrs={
                'class': 'input w-full',
                'min': '0',
                'max': '3000'
            }),
            'altitude_max': forms.NumberInput(attrs={
                'class': 'input w-full',
                'min': '0',
                'max': '3000'
            })
        }

class CoffeeBeanForm(forms.ModelForm):
    type = forms.ChoiceField(
        choices=CoffeeBean.BEAN_TYPES,
        widget=forms.RadioSelect(attrs={'class': 'radio'}),
        label='咖啡豆类型',
        required=True,
        initial='SKIP'
    )
    
    flavor_tags = forms.CharField(
        label='风味标签',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'input w-full',
            'placeholder': '输入标签，按回车添加'
        })
    )
    roast_date_enabled = forms.BooleanField(
        label='添加烘焙日期',
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'checkbox'})
    )

    roast_date = forms.DateField(
        label='烘焙日期',
        required=False,
        widget=DateInput(
            attrs={
                'type': 'date',
                'class': 'input w-full mt-2',
                'placeholder': '选择日期'
            },
            format='%Y-%m-%d'
        ),
        input_formats=['%Y-%m-%d']
    )

    is_decaf = forms.BooleanField(
        label='低因咖啡',
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'checkbox'
        })
    )

    created_at = forms.DateTimeField(
        label='购买时间',
        required=False,
        widget=forms.DateTimeInput(
            attrs={
                'type': 'datetime-local',
                'class': 'input w-full'
            },
            format='%Y-%m-%dT%H:%M'
        )
    )

    notes = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'flex w-full h-auto min-h-[80px] px-3 py-2 text-sm bg-transparent border rounded-md border-neutral-300 ring-offset-background placeholder:text-neutral-400 focus:border-neutral-300 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-neutral-400 disabled:cursor-not-allowed disabled:opacity-50',
            'x-data': '{ resize() { $el.style.height = "0px"; $el.style.height = $el.scrollHeight + "px" } }',
            'x-init': 'resize()',
            '@input': 'resize(); $dispatch("input-change", $el.value.length)',
            'rows': '1',
            'maxlength': '500'
        }),
        required=False,
        label='备注'
    )

    purchase_price = forms.DecimalField(
        label='购买价格',
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'input w-full',
            'step': '0.01',
            'min': '0'
        })
    )

    altitude_type = forms.ChoiceField(
        choices=[
            ('SINGLE', '单一海拔'),
            ('RANGE', '海拔范围')
        ],
        initial='SINGLE',
        widget=forms.RadioSelect(attrs={'class': 'radio radio-primary'}),
        required=False
    )

    bag_weight = forms.DecimalField(
        max_digits=7,  # 增加到7位
        decimal_places=2,
        required=False,
        label='包装规格',
        widget=forms.NumberInput(attrs={
            'class': 'input w-full',
            'placeholder': '请输入包装规格',
            'step': '0.01'
        })
    )

    bag_remain = forms.DecimalField(
        max_digits=7,  # 增加到7位
        decimal_places=2,
        required=False,
        label='库存余量',
        widget=forms.NumberInput(attrs={
            'class': 'input w-full',
            'step': '0.01'
        })
    )

    class Meta:
        model = CoffeeBean
        fields = [
            'type', 'is_decaf', 'roaster', 'name', 'roast_date', 'roast_level',
            'origin', 'region', 'finca', 'variety', 'altitude_type', 
            'altitude_single', 'altitude_min', 'altitude_max', 'process',
            'bag_weight', 'bag_remain', 'created_at', 'notes', 'purchase_price',
            'rest_period_min', 'rest_period_max'
        ]
        labels = {
            'roaster': '豆商',
            'name': '咖啡豆名称',
            'roast_date': '',
            'origin': '产地',
            'region': '产区',
            'finca': '庄园',
            'variety': '品种',
            'altitude_type': '海拔类型',
            'altitude_single': '种植海拔',
            'altitude_min': '最低海拔',
            'altitude_max': '最高海拔',
            'process': '处理法',
            'roast_level': '烘焙程度',
            'bag_weight': '包装规格',
            'bag_remain': '库存余量',
            'rest_period_min': '最短养豆期',
            'rest_period_max': '最长养豆期'
        }
        widgets = {
            'roaster': forms.TextInput(attrs={'class': 'input w-full'}),
            'name': forms.TextInput(attrs={'class': 'input w-full'}),
            'roast_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'input w-full mt-2',
                'style': 'display: none;',
                'placeholder': '选择日期'
            }),
            'origin': forms.TextInput(attrs={'class': 'input w-full'}),
            'region': forms.TextInput(attrs={'class': 'input w-full'}),
            'finca': forms.TextInput(attrs={'class': 'input w-full'}),
            'variety': forms.TextInput(attrs={'class': 'input w-full'}),
            'altitude_type': forms.Select(attrs={'class': 'select w-full', 'x-model': 'altitudeType'}),
            'altitude_single': forms.NumberInput(attrs={'class': 'input w-full', 'min': '0', 'max': '3000'}),
            'altitude_min': forms.NumberInput(attrs={'class': 'input w-full', 'min': '0', 'max': '3000'}),
            'altitude_max': forms.NumberInput(attrs={'class': 'input w-full', 'min': '0', 'max': '3000'}),
            'process': forms.TextInput(attrs={'class': 'input w-full'}),
            'roast_level': forms.HiddenInput(),
            'bag_weight': forms.NumberInput(attrs={
                'class': 'input w-full',
                'step': '0.1',
                'min': '0'
            }),
            'bag_remain': forms.NumberInput(attrs={
                'class': 'input w-full',
                'step': '0.1',
                'min': '0'
            }),
            'created_at': forms.DateTimeInput(
                attrs={
                    'type': 'datetime-local',
                    'class': 'input w-full'
                },
                format='%Y-%m-%dT%H:%M'
            )
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 设置 created_at 的初始值
        if self.instance and self.instance.created_at:
            if timezone.is_naive(self.instance.created_at):
                self.instance.created_at = timezone.make_aware(
                    self.instance.created_at,
                    timezone=timezone.get_current_timezone()
                )
            local_time = timezone.localtime(self.instance.created_at)
            self.initial['created_at'] = local_time.strftime('%Y-%m-%dT%H:%M')
        else:
            # 如果是新建咖啡豆，默认设置为当前时间
            self.initial['created_at'] = timezone.localtime(timezone.now()).strftime('%Y-%m-%dT%H:%M')

        # 为 roast_date 字段设置 HTML5 日期输入类型
        self.fields['roast_date'].widget = DateInput(
            attrs={
                'type': 'date',
                'class': 'input w-full mt-2',
                'placeholder': '选择日期'
            },
            format='%Y-%m-%d'  # 指定日期格式为 yyyy-MM-dd
        )

        # 如果已有日期值，确保它被正确格式化
        if self.instance.roast_date:
            self.initial['roast_date'] = self.instance.roast_date.strftime('%Y-%m-%d')

    def clean_created_at(self):
        created_at = self.cleaned_data.get('created_at')
        if created_at:
            # 确保时区感知
            if timezone.is_naive(created_at):
                created_at = timezone.make_aware(
                    created_at,
                    timezone=timezone.get_current_timezone()
                )
            
            # 标准化datetime格式：移除微秒
            created_at = created_at.replace(microsecond=0)
        
        return created_at

    def clean(self):
        cleaned_data = super().clean()
        bean_type = cleaned_data.get('type')
        
        # 如果是拼配豆，跳过 altitude_type 验证
        if bean_type == 'BLEND':
            if 'altitude_type' in self._errors:
                del self._errors['altitude_type']
            cleaned_data['altitude_type'] = 'SINGLE'  # 设置一个默认值
            
            blend_components = self.data.getlist('blend_components-blend_ratio')
            if blend_components:
                total_ratio = sum(float(ratio) for ratio in blend_components if ratio)
                if not (99.99 <= total_ratio <= 100.01):  # 允许0.01%的误差
                    raise ValidationError('拼配比例总和必须等于100%')
            if 'roast_level' in self._errors:
                del self._errors['roast_level']
            cleaned_data['roast_level'] = 4  # 设置一个默认值
        else:
            # 非拼配豆时才验证 altitude_type
            if not cleaned_data.get('altitude_type'):
                cleaned_data['altitude_type'] = 'SINGLE'  # 设置默认值
        
        # 验证库存
        bag_weight = cleaned_data.get('bag_weight')
        bag_remain = cleaned_data.get('bag_remain')
        if bag_weight is not None and bag_remain is not None:
            if bag_remain > bag_weight:
                raise ValidationError({
                    'bag_remain': _('库存余量不能大于包装规格')
                })
        
        # 验证海拔范围
        altitude_type = cleaned_data.get('altitude_type')
        altitude_min = cleaned_data.get('altitude_min')
        altitude_max = cleaned_data.get('altitude_max')
        if altitude_type == 'RANGE' and altitude_min and altitude_max:
            if altitude_min > altitude_max:
                raise ValidationError({
                    'altitude_min': _('最低海拔不能大于最高海拔')
                })
        
        return cleaned_data

    def save(self, commit=True):
        bean = super().save(commit=False)
        
        # 如果没有设置库存余量但设置了包装规格，则自动设置库存余量
        if bean.bag_remain is None and bean.bag_weight is not None:
            bean.bag_remain = bean.bag_weight
        
        if commit:
            bean.save()
        return bean

class BrewingRecordForm(forms.ModelForm):
    steps_enabled = forms.BooleanField(
        label='添加详细步骤',
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'checkbox'})
    )

    gadgets = GadgetMultipleChoiceField(
        queryset=None,  # 将在 __init__ 中设置
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'checkbox'}),
        label='小工具'
    )

    gadget_kit = forms.ModelChoiceField(
        queryset=None,  # 将在 __init__ 中设置
        required=False,
        empty_label="选择小工具组合（选填）",
        widget=forms.Select(attrs={'class': 'select w-full'}),
        label='小工具组合'
    )

    # 添加环境数据字段
    water_quality = forms.CharField(
        max_length=50,
        required=False,
        label='水质'
    )
    
    room_temperature = forms.DecimalField(
        max_digits=4,
        decimal_places=1,
        required=False,
        label='室温',
        validators=[
            MinValueValidator(Decimal('-50.0')),
            MaxValueValidator(Decimal('50.0'))
        ]
    )
    
    room_humidity = forms.IntegerField(
        required=False,
        label='环境湿度',
        validators=[
            MinValueValidator(0),
            MaxValueValidator(100)
        ]
    )

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            # 获取所有未归档的设备，以及当前记录使用的已删除设备
            active_brewers = Equipment.objects.filter(
                user=user, 
                type='BREWER',
                is_archived=False,
                is_deleted=False
            )
            if self.instance and self.instance.brewing_equipment and self.instance.brewing_equipment.is_deleted:
                active_brewers |= Equipment.objects.filter(id=self.instance.brewing_equipment.id)
            self.fields['brewing_equipment'].queryset = active_brewers

            # 同样处理磨豆机
            active_grinders = Equipment.objects.filter(
                user=user, 
                type='GRINDER',
                is_archived=False,
                is_deleted=False
            )
            if self.instance and self.instance.grinding_equipment and self.instance.grinding_equipment.is_deleted:
                active_grinders |= Equipment.objects.filter(id=self.instance.grinding_equipment.id)
            self.fields['grinding_equipment'].queryset = active_grinders

            # 处理咖啡豆
            active_beans = CoffeeBean.objects.filter(
                user=user,
                is_deleted=False
            )
            if self.instance and self.instance.coffee_bean and (self.instance.coffee_bean.is_archived or self.instance.coffee_bean.is_deleted):
                active_beans |= CoffeeBean.objects.filter(id=self.instance.coffee_bean.id)
            self.fields['coffee_bean'].queryset = active_beans

            # 添加小工具查询
            active_gadgets = Equipment.objects.filter(
                user=user,
                type='GADGET',
                is_archived=False,
                is_deleted=False
            )
            self.fields['gadgets'].queryset = active_gadgets

            # 如果是编辑现有记录，设置已选择的小工具
            if self.instance and self.instance.pk:
                self.initial['gadgets'] = self.instance.gadgets.all()

            # 设置小工具组合的查询集
            self.fields['gadget_kit'].queryset = Equipment.objects.filter(
                user=user,
                type='GADGET_KIT',
                is_deleted=False
            )
            
            # 如果选择了小工具组合，自动设置小工具
            if self.instance and self.instance.gadget_kit:
                self.initial['gadgets'] = self.instance.gadget_kit.gadget_components.all()

        # 格式化创建时间的初始值
        if self.instance and self.instance.created_at:
            if timezone.is_naive(self.instance.created_at):
                self.instance.created_at = timezone.make_aware(
                    self.instance.created_at,
                    timezone=timezone.get_current_timezone()
                )
            local_time = timezone.localtime(self.instance.created_at)
            self.initial['created_at'] = local_time.strftime('%Y-%m-%dT%H:%M')

        # 只在没有初始值时设置默认的萃取时间
        if not self.initial.get('brewing_time'):
            self.initial['brewing_time'] = '00:00:00'

    def clean(self):
        cleaned_data = super().clean()
        if not cleaned_data.get('steps_enabled'):
            cleaned_data['steps'] = []
        return cleaned_data

    class Meta:
        model = BrewingRecord
        fields = [
            'recipe_name',  # 配方名称字段
            'brewing_equipment', 'coffee_bean', 'grind_size',
            'grinding_equipment', 'dose_weight', 'yield_weight',
            'water_temperature', 'brewing_time', 'rating_level', 
            'notes', 'created_at', 'steps', 'steps_enabled',
            'gadgets',  # 小工具字段
            'gadget_kit',  # 小工具组合字段
            'aroma', 'acidity', 'body', 'sweetness', 'aftertaste',
            'water_quality',
            'room_temperature',
            'room_humidity',
        ]
        
        # 添加必填字段错误信息
        error_messages = {
            'grind_size': {'required': '请输入研磨设置'},
            'dose_weight': {'required': '请输入粉重'},
            'yield_weight': {'required': '请输入液重'},
            'brewing_time': {'required': '请输入萃取时间'},
        }
        widgets = {
            'recipe_name': forms.TextInput(attrs={
                'class': 'input w-full',
                'placeholder': '为这个配方起个名字'
            }),
            'brewing_equipment': forms.Select(attrs={'class': 'select w-full'}),
            'coffee_bean': forms.Select(attrs={'class': 'select w-full'}),
            'grinding_equipment': forms.Select(attrs={'class': 'select w-full'}),
            'grind_size': forms.TextInput(
                attrs={
                    'class': 'input w-full'
                }
            ),
            'dose_weight': forms.NumberInput(attrs={'class': 'input w-full', 'step': '0.1'}),
            'yield_weight': forms.NumberInput(attrs={'class': 'input w-full', 'step': '0.1'}),
            'water_temperature': forms.NumberInput(attrs={'class': 'input w-full', 'step': '0.1'}),
            'brewing_time': forms.HiddenInput(),
            'rating_level': forms.HiddenInput(),
            'notes': forms.Textarea(attrs={
                'class': 'flex w-full h-auto min-h-[80px] px-3 py-2 text-sm bg-transparent border rounded-md border-neutral-300 ring-offset-background placeholder:text-neutral-400 focus:border-neutral-300 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-neutral-400 disabled:cursor-not-allowed disabled:opacity-50',
                'x-data': '{ resize() { $el.style.height = "0px"; $el.style.height = $el.scrollHeight + "px" } }',
                'x-init': 'resize()',
                '@input': 'resize(); $dispatch("input-change", $el.value.length)',
                'rows': '1',
                'maxlength': '500'
            }),
            'created_at': forms.DateTimeInput(
                attrs={
                    'type': 'datetime-local',
                    'class': 'input w-full'
                },
                format='%Y-%m-%dT%H:%M'
            ),
            'aroma': forms.NumberInput(attrs={'type': 'range', 'min': '0', 'max': '5', 'step': '1', 'class': 'range'}),
            'acidity': forms.NumberInput(attrs={'type': 'range', 'min': '0', 'max': '5', 'step': '1', 'class': 'range'}),
            'body': forms.NumberInput(attrs={'type': 'range', 'min': '0', 'max': '5', 'step': '1', 'class': 'range'}),
            'sweetness': forms.NumberInput(attrs={'type': 'range', 'min': '0', 'max': '5', 'step': '1', 'class': 'range'}),
            'aftertaste': forms.NumberInput(attrs={'type': 'range', 'min': '0', 'max': '5', 'step': '1', 'class': 'range'}),
            'water_quality': forms.TextInput(attrs={'class': 'input w-full'}),
            'room_temperature': forms.NumberInput(attrs={'class': 'input w-full', 'step': '0.1'}),
            'room_humidity': forms.NumberInput(attrs={'class': 'input w-full', 'step': '1'}),
        }

    def clean_created_at(self):
        created_at = self.cleaned_data.get('created_at')
        if created_at and timezone.is_naive(created_at):
            created_at = timezone.make_aware(
                created_at,
                timezone=timezone.get_current_timezone()
            )
        return created_at

    def save(self, commit=True):
        record = super().save(commit=False)
        
        # 如果是编辑现有记录
        if record.pk:
            # 获取数据库中的原始记录
            original_record = BrewingRecord.objects.get(pk=record.pk)
            
            # 如果咖啡豆发生变化
            if original_record.coffee_bean != record.coffee_bean:
                # 如果原始记录有咖啡豆，需要退还原来的用量
                if original_record.coffee_bean and original_record.dose_weight:
                    original_bean = original_record.coffee_bean
                    if original_bean.bag_remain is not None:
                        original_bean.bag_remain = original_bean.bag_remain + original_record.dose_weight
                        original_bean.save()
                
                # 如果新记录有咖啡豆，需要扣除新的用量
                if record.coffee_bean and record.dose_weight:
                    if record.coffee_bean.bag_remain is not None:
                        new_remain = record.coffee_bean.bag_remain - record.dose_weight
                        record.coffee_bean.bag_remain = max(0, new_remain)
                        record.coffee_bean.save()
            
            # 如果咖啡豆没变但剂量发生变化
            elif record.coffee_bean and original_record.dose_weight != record.dose_weight:
                # 计算剂量差值
                dose_diff = original_record.dose_weight - record.dose_weight
                # 正差值表示新剂量更小，需要补偿给库存
                # 负差值表示新剂量更大，需要从库存扣除
                if record.coffee_bean.bag_remain is not None:
                    new_remain = record.coffee_bean.bag_remain + dose_diff
                    record.coffee_bean.bag_remain = max(0, new_remain)
                    record.coffee_bean.save()
        
        # 如果是新记录
        else:
            # 如果有选择咖啡豆且有设置剂量
            if record.coffee_bean and record.dose_weight:
                # 只在咖啡豆有余量时扣减
                if record.coffee_bean.bag_remain is not None:
                    new_remain = record.coffee_bean.bag_remain - record.dose_weight
                    record.coffee_bean.bag_remain = max(0, new_remain)
                    record.coffee_bean.save()
        
        if commit:
            record.save()
            
            # 保存 ManyToManyField 关系
            gadgets = self.cleaned_data.get('gadgets')
            if gadgets is not None:  # 如果有选择小工具
                record.gadgets.clear()  # 清除现有关系
                record.gadgets.add(*gadgets)  # 添加新的关系
        
        return record

class BeanOccurrenceForm(forms.ModelForm):
    purchase_price = forms.DecimalField(
        label='购买价格',
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'input w-full',
            'step': '0.01',
            'min': '0'
        })
    )
    
    class Meta:
        model = BeanOccurrence
        fields = ['bag_weight', 'bag_remain', 'purchase_price', 'roast_date', 'created_at', 'rest_period_min', 'rest_period_max']
        widgets = {
            'roast_date': forms.DateInput(attrs={'type': 'date'}),
            'created_at': forms.DateTimeInput(
                attrs={
                    'type': 'datetime-local',
                    'class': 'input'
                },
                format='%Y-%m-%dT%H:%M'
            ),
        }

    def clean(self):
        cleaned_data = super().clean()
        bag_weight = cleaned_data.get('bag_weight')
        bag_remain = cleaned_data.get('bag_remain')
        
        if bag_weight and not bag_remain:
            cleaned_data['bag_remain'] = bag_weight
        
        if bag_weight and cleaned_data.get('bag_remain') and cleaned_data['bag_remain'] > bag_weight:
            raise forms.ValidationError('剩余重量不能大于包装重量')
        
        # 处理时区
        created_at = cleaned_data.get('created_at')
        if created_at and timezone.is_naive(created_at):
            cleaned_data['created_at'] = timezone.make_aware(
                created_at,
                timezone=timezone.get_current_timezone()
            )
        
        # 验证养豆期范围
        rest_period_min = cleaned_data.get('rest_period_min')
        rest_period_max = cleaned_data.get('rest_period_max')
        
        if rest_period_min and rest_period_max and rest_period_min > rest_period_max:
            raise forms.ValidationError('最短养豆期不能大于最长养豆期')
        
        return cleaned_data