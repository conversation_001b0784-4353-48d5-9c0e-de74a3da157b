from article.models import ArticlePage
from bean.models import RoastedBeanPage
from datetime import timedelta, date, datetime
from decimal import Decimal
from django.contrib.auth.models import User
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db import models
from django.db.models import Avg, Case, When, BooleanField, ExpressionWrapper, DateTimeField
from django.db.models import Count, F, Q, Sum, Max
from django.db.models.functions import TruncDate, ExtractWeekDay, ExtractHour, ExtractDay, Now
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from wagtail.models import Page
from zoneinfo import ZoneInfo
import random, logging
from django.contrib.auth import get_user_model
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.core.cache import cache
import decimal
from django.conf import settings
import calendar
from .constants import CACHE_TIMEOUT
from collections import defaultdict

logger = logging.getLogger(__name__)

User = get_user_model()

# 设备使用统计缓存键
def get_equipment_usage_cache_key(equipment_id):
    return f'equipment_usage:{equipment_id}'

def get_equipment_last_used_cache_key(equipment_id):
    return f'equipment_last_used:{equipment_id}'

def get_default_content_type():
    return ContentType.objects.get_for_model(ArticlePage).id

def get_default_object_id():
    default_article = ArticlePage.objects.first()
    if default_article:
        return default_article.id
    else:
        raise ValueError("No ArticlePage instances exist to use as a default object_id")

class Favorite(models.Model):
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='favorites'
    )
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        unique_together = ('user', 'content_type', 'object_id')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.content_object}"

class Equipment(models.Model):
    EQUIPMENT_TYPES = [
        ('GRINDER', '磨豆机'),
        ('BREWER', '冲煮器具'),
        ('GADGET', '小工具'),
        ('GADGET_KIT', '小工具组合'),
    ]

    TYPE_CHOICES = EQUIPMENT_TYPES

    BREW_METHODS = [
        ('ESPRESSO', '意式'),
        ('POUR_OVER', '手冲'),
        ('AEROPRESS', '爱乐压'),
        ('COLD_BREW', '冷萃'),
        ('MOKA_POT', '摩卡壶'),
        ('FRENCH_PRESS', '法压壶'),
        ('AUTO_DRIP', '自动滴滤')
    ]

    GRINDER_PURPOSE_CHOICES = [
        ('ESPRESSO', '意式'),
        ('POUR_OVER', '手冲'),
        ('ALL_PURPOSE', '通用'),
    ]

    grinder_purpose = models.CharField(
        max_length=20,
        choices=GRINDER_PURPOSE_CHOICES,
        null=True,
        blank=True,
        verbose_name='用途'
    )

    grind_size_preset = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name='预设研磨度',
        help_text='磨豆机的当前研磨度设置'
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    type = models.CharField(
        max_length=10,
        choices=EQUIPMENT_TYPES,
        blank=False,
        null=False,
    )
    name = models.CharField(max_length=50)
    brand = models.CharField(max_length=50, blank=True, verbose_name='品牌')
    brew_method = models.CharField(
        max_length=20,
        choices=BREW_METHODS,
        null=True,
        blank=True,
        verbose_name='冲煮方式'
    )
    is_archived = models.BooleanField(default=False, verbose_name='已归档')
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    is_favorite = models.BooleanField(default=False, verbose_name='首选')
    notes = models.TextField(max_length=500, blank=True, verbose_name='备注')
    created_at = models.DateTimeField(
        null=False,
        blank=False,
        default=timezone.now,
        verbose_name='购买时间'
    )
    purchase_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='购买价格',
        validators=[MinValueValidator(0)]
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['user', 'type', 'name'],
                condition=models.Q(is_deleted=False),
                name='unique_active_equipment'
            )
        ]

    def __str__(self):
        return f"{self.name}"

    def delete(self, *args, **kwargs):
        # 如果是小工具，从所有组合中移除
        if self.type == 'GADGET':
            # 从所有小工具组合中移除该小工具
            self.kit_components.clear()

        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def clean(self):
        cleaned_data = super().clean()

        # 检查设备类型是否被修改
        if self.pk:  # 如果是现有记录
            original = Equipment.objects.get(pk=self.pk)
            if original.type != self.type:
                raise ValidationError({
                    'type': '设备类型创建后不可修改。如需更改，请创建新的设备。'
                })

        # 如果设备类型不是磨豆机，清空研磨度设置
        if self.type != 'GRINDER':
            self.grind_size_preset = None
            self.grinder_purpose = None

        # 如果设备类型是小工具组合，验证其组件
        if self.type == 'GADGET_KIT' and self.pk:
            # 获取所有组件
            components = self.gadget_components.all()

            # 移除任何非小工具类型的组件
            invalid_components = components.exclude(type='GADGET')
            if invalid_components.exists():
                self.gadget_components.remove(*invalid_components)

            # 如果自身在组件中，移除
            if self in components:
                self.gadget_components.remove(self)

        return cleaned_data

    # 不同类型设备的预计使用寿命（年）
    USEFUL_LIFE_MAP = {
        'GRINDER': 8,
        'BREWER': 10,
        'GADGET': 6,
    }

    # 不同类型设备的预计净残值率
    SALVAGE_RATE_MAP = {
        'GRINDER': 0.15,  # 磨豆机残值率15%
        'BREWER': 0.10,   # 冲煮器具残值率10%
        'GADGET': 0.05,   # 小工具残值率5%
    }

    def depreciation_rate(self):
        """使用直线法计算设备的折旧率"""
        useful_life = self.USEFUL_LIFE_MAP.get(self.type, 5)
        salvage_rate = self.SALVAGE_RATE_MAP.get(self.type, 0.10)

        annual_depreciation_rate = (1 - salvage_rate) / useful_life
        age = (timezone.now() - self.created_at).days / 365
        current_value_rate = 1 - (annual_depreciation_rate * min(age, useful_life))

        return round(max(current_value_rate, salvage_rate) * 100, 2)

    def break_even_progress(self):
        """计算设备回本进度"""
        if self.purchase_price is None:
            return 0

        # 不同类型设备的单次使用价值（元）
        USAGE_VALUE_MAP = {
            'GRINDER': 3.0,    # 磨豆机每次使用价值3元
            'BREWER': 5.0,     # 冲煮设备每次使用价值5元
            'GADGET': 1.0,     # 小工具每次使用价值1元
        }

        # 获取设备类型对应的单次使用价值
        usage_value = Decimal(str(USAGE_VALUE_MAP.get(self.type, 3.0)))

        # 根据购买价格调整单次使用价值
        # 价格越高，单次使用价值越高，但增长是非线性的
        price_factor = (self.purchase_price / Decimal('1000')).sqrt()  # 使用平方根使增长更平缓
        adjusted_usage_value = usage_value * price_factor

        # 使用聚合查询计算设备的使用次数和总节省成本
        # 修改：添加 distinct() 确保每条记录只被计数一次
        usage_data = BrewingRecord.objects.filter(
            Q(brewing_equipment=self) |
            Q(grinding_equipment=self) |
            Q(gadgets=self)
        ).distinct().aggregate(
            usage_count=Count('id')
        )

        total_usage = usage_data['usage_count'] or 0
        total_savings = Decimal(str(total_usage)) * adjusted_usage_value

        # 计算回本百分比，最大为100%
        break_even_percentage = min(total_savings / self.purchase_price, Decimal('1'))
        return round(break_even_percentage * 100, 2)

    def save(self, *args, **kwargs):
        self.clean()

        # 检查设备类型是否变更为小工具组合
        if self.type == 'GADGET_KIT':
            # 清除小工具组合不相关的字段
            self.brand = ''
            self.purchase_price = None
            self.grinder_purpose = None
            self.grind_size_preset = None
            self.brew_method = None

            # 如果这个设备本身是小工具，从所有组合中移除它
            if self.pk:  # 如果不是新创建的设备
                # 获取原始设备
                try:
                    original = Equipment.objects.get(pk=self.pk)

                    # 只有在设备类型变更时才清空组件关系
                    if original.type != 'GADGET_KIT':
                        # 从其他组合中移除自己
                        self.kit_components.clear()
                        # 清空自己的组件
                        self.gadget_components.clear()
                        # 清除所有引用这个设备作为组合的记录
                        BrewingRecord.objects.filter(gadget_kit=self).update(gadget_kit=None)
                except Equipment.DoesNotExist:
                    pass  # 新设备，不需要处理

        if not self.created_at:
            self.created_at = timezone.now()

        # 确保创建时间格式统一(去除微秒)
        if self.created_at:
            self.created_at = self.created_at.replace(microsecond=0)

        super().save(*args, **kwargs)

    gadget_components = models.ManyToManyField(
        'self',
        blank=True,
        limit_choices_to={'type': 'GADGET'},
        related_name='kit_components',
        symmetrical=False,
        verbose_name='组合内容'
    )

    @classmethod
    def has_active_equipment(cls, user, equipment_type):
        """
        检查用户是否有指定类型的活跃设备

        Args:
            user: 用户对象
            equipment_type: 设备类型，如'BREWER'、'GRINDER'等

        Returns:
            bool: 是否有活跃设备
        """
        return cls.objects.filter(
            user=user,
            type=equipment_type,
            is_archived=False,
            is_deleted=False
        ).exists()

    @classmethod
    def get_favorite_equipment(cls, user, equipment_type):
        """获取用户指定类型的首选设备"""
        return cls.objects.filter(
            user=user,
            type=equipment_type,
            is_favorite=True,
            is_archived=False,
            is_deleted=False
        ).first()

    @classmethod
    def get_favorite_gadget_kit(cls, user):
        """获取用户的首选小工具组合"""
        favorite_kit = cls.objects.filter(
            user=user,
            type='GADGET_KIT',
            is_favorite=True,
            is_archived=False,
            is_deleted=False
        ).first()

        if favorite_kit:
            # 获取组合中的小工具ID列表
            gadget_ids = [g.id for g in favorite_kit.gadget_components.all()]
            return {
                'kit': favorite_kit,
                'gadget_ids': gadget_ids
            }

        # 如果没有首选小工具组合，获取首选小工具列表
        favorite_gadgets = cls.objects.filter(
            user=user,
            type='GADGET',
            is_favorite=True,
            is_archived=False,
            is_deleted=False
        )
        if favorite_gadgets.exists():
            return {
                'kit': None,
                'gadget_ids': [g.id for g in favorite_gadgets]
            }

        return {
            'kit': None,
            'gadget_ids': []
        }

    @classmethod
    def get_available_gadget_kits(cls, user, exclude_id=None):
        """
        获取可用的小工具组合列表，排除指定ID的设备

        Args:
            user: 用户对象
            exclude_id: 要排除的设备ID

        Returns:
            QuerySet: 过滤后的小工具组合查询集
        """
        kits = cls.objects.filter(
            user=user,
            type='GADGET_KIT',
            is_archived=False,
            is_deleted=False
        )

        if exclude_id:
            kits = kits.exclude(id=exclude_id)

        return kits

    @classmethod
    def has_any_equipment(cls, user):
        """检查用户是否有任何设备记录（包括已删除的）"""
        return cls.objects.filter(user=user).exists()

    @classmethod
    def needs_onboarding(cls, user):
        """检查用户是否需要进入新手引导"""
        has_beans = CoffeeBean.has_any_beans(user)
        has_equipment = cls.has_any_equipment(user)
        return not (has_beans and has_equipment)

    @property
    def created_at_timestamp(self):
        return self.created_at.timestamp() if self.created_at else None

    @property
    def purchase_date_timestamp(self):
        return self.created_at.timestamp() if self.created_at else None

    def get_usage_count(self):
        """获取设备使用次数，使用缓存机制"""
        cache_key = get_equipment_usage_cache_key(self.id)
        cached_count = cache.get(cache_key)

        if cached_count is not None:
            return cached_count

        # 如果缓存不存在，从数据库查询
        if self.type == 'GADGET_KIT':
            # 如果是小工具组合，统计使用了该组合的记录数
            usage_count = BrewingRecord.objects.filter(
                gadget_kit=self
            ).count()
        else:
            # 使用 distinct() 确保每条记录只被计数一次
            usage_count = BrewingRecord.objects.filter(
                Q(brewing_equipment=self) |
                Q(grinding_equipment=self) |
                Q(gadgets=self)
            ).distinct().count()

        # 存入缓存，设置30分钟过期
        cache.set(cache_key, usage_count, 60 * 30)
        return usage_count

    def get_last_used_datetime(self):
        """获取设备最后使用时间，使用缓存机制"""
        cache_key = get_equipment_last_used_cache_key(self.id)
        cached_datetime = cache.get(cache_key)

        if cached_datetime is not None:
            return cached_datetime

        # 如果缓存不存在，从数据库查询
        if self.type == 'GADGET_KIT':
            # 获取最后使用时间
            last_record = BrewingRecord.objects.filter(
                gadget_kit=self
            ).order_by('-created_at').first()
        else:
            # 获取最后使用时间
            last_record = BrewingRecord.objects.filter(
                Q(brewing_equipment=self) |
                Q(grinding_equipment=self) |
                Q(gadgets=self)
            ).order_by('-created_at').first()

        # 确保时间是 aware datetime
        if last_record:
            last_used = last_record.created_at
            if timezone.is_naive(last_used):
                last_used = timezone.make_aware(last_used)
            last_used = last_used.astimezone(ZoneInfo('Asia/Shanghai'))
        else:
            created_at = self.created_at
            if timezone.is_naive(created_at):
                created_at = timezone.make_aware(created_at)
            last_used = created_at.astimezone(ZoneInfo('Asia/Shanghai'))

        # 存入缓存，设置30分钟过期
        cache.set(cache_key, last_used, 60 * 30)
        return last_used

    @property
    def usage_count(self):
        """设备使用次数属性"""
        return self.get_usage_count()

    @property
    def last_used(self):
        """设备最后使用时间属性"""
        return self.get_last_used_datetime()

    @property
    def _depreciation_rate(self):
        """为序列化器提供的浮点数类型折旧率"""
        try:
            rate = self.depreciation_rate()
            # 确保转换为浮点数
            return float(rate)
        except Exception:
            return 100.0

    @property
    def _break_even_progress(self):
        """为序列化器提供的浮点数类型回本进度"""
        try:
            progress = self.break_even_progress()
            # Decimal转换为float (不是str)
            if isinstance(progress, Decimal):
                return float(progress)
            # 确保是float类型
            return float(progress)
        except Exception:
            return 0.0

class CoffeeBean(models.Model):
    BEAN_TYPES = [
        ('SINGLE', '单品'),
        ('BLEND', '拼配'),
        ('SKIP', '跳过'),
    ]
    ROAST_LEVEL_CHOICES = [
        (1, "极浅烘"),
        (2, "浅烘"),
        (3, "浅中烘"),
        (4, "中烘"),
        (5, "中深烘"),
        (6, "深烘"),
        (7, "极深烘"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    type = models.CharField(max_length=10, choices=BEAN_TYPES)
    roaster = models.CharField(max_length=50)
    name = models.CharField(max_length=50)
    barcode = models.CharField(max_length=50, blank=True, null=True, verbose_name='条形码', help_text='产品条形码，用于与商品库匹配')
    roast_date = models.DateField(null=True, blank=True)
    roast_level = models.IntegerField(
        choices=ROAST_LEVEL_CHOICES,
        validators=[MinValueValidator(1), MaxValueValidator(7)],
        default=4,
        verbose_name='烘焙程度'
    )
    origin = models.CharField(max_length=50, blank=True)
    region = models.CharField(max_length=50, blank=True)
    finca = models.CharField(max_length=50, blank=True)
    variety = models.CharField(max_length=50, blank=True)
    altitude = models.CharField(max_length=50, blank=True)
    process = models.CharField(max_length=50, blank=True)
    is_archived = models.BooleanField(default=False, verbose_name='已归档')
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    is_favorite = models.BooleanField(default=False, verbose_name='首选')
    bag_weight = models.DecimalField(
        max_digits=7,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='包装规格',
        validators=[MinValueValidator(0)]
    )
    bag_remain = models.DecimalField(
        max_digits=7,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='库存余量',
        validators=[MinValueValidator(0)]
    )
    is_decaf = models.BooleanField(
        default=False,
        verbose_name='低因咖啡',
        help_text='是否为低因咖啡'
    )
    created_at = models.DateTimeField(
        null=False,
        blank=False,
        default=timezone.now,
        verbose_name='购买时间'
    )
    notes = models.TextField(
        max_length=500,
        blank=True,
        verbose_name='备注'
    )
    purchase_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='购买价格',
        validators=[MinValueValidator(0)]
    )
    ALTITUDE_TYPE_CHOICES = [
        ('SINGLE', '单一海拔'),
        ('RANGE', '海拔区间'),
    ]

    altitude_type = models.CharField(
        max_length=10,
        choices=ALTITUDE_TYPE_CHOICES,
        default='SINGLE',
        verbose_name='海拔类型'
    )
    altitude_single = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='种植海拔',
        help_text='单位:米',
        validators=[MinValueValidator(0), MaxValueValidator(9999)]  # 修改为9999
    )
    altitude_min = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='最低海拔',
        help_text='单位:米',
        validators=[MinValueValidator(0), MaxValueValidator(9999)]  # 修改为9999
    )
    altitude_max = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='最高海拔',
        help_text='单位:米',
        validators=[MinValueValidator(0), MaxValueValidator(9999)]  # 修改为9999
    )

    # 初始包装信息（0号记录）
    initial_bag_weight = models.DecimalField(
        max_digits=6,
        decimal_places=1,
        null=True,
        blank=True,
        verbose_name='初始包装重量(g)'
    )
    initial_bag_remain = models.DecimalField(
        max_digits=6,
        decimal_places=1,
        null=True,
        blank=True,
        verbose_name='初始剩余重量(g)'
    )
    initial_purchase_price = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='初始购买价格',
        validators=[MinValueValidator(0)]
    )
    initial_roast_date = models.DateField(
        null=True,
        blank=True,
        verbose_name='初始烘焙日期'
    )
    initial_created_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='初始购买时间'
    )
    initial_rest_period_min = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='初始最短养豆期',
        help_text='单位:天',
        validators=[MinValueValidator(1), MaxValueValidator(60)]
    )
    initial_rest_period_max = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='初始最长养豆期',
        help_text='单位:天',
        validators=[MinValueValidator(1), MaxValueValidator(60)]
    )

    rest_period_min = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='最短养豆期',
        help_text='单位:天',
        validators=[MinValueValidator(1), MaxValueValidator(60)]
    )
    rest_period_max = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='最长养豆期',
        help_text='单位:天',
        validators=[MinValueValidator(1), MaxValueValidator(60)]
    )

    @property
    def price_per_gram(self):
        """计算每克价格"""
        if self.purchase_price and self.bag_weight and self.bag_weight > 0:
            return self.purchase_price / self.bag_weight
        return None

    def calculate_rest_period_progress(self):
        """
        计算养豆进度
        返回元组：(进度百分比, 是否在最佳赏味期)
        进度百分比：基于60天计算，超过60天返回100%
        是否在最佳赏味期：根据设定的养豆期区间判断
        """
        if not self.roast_date:
            return (0, False)

        days_since_roast = (timezone.now().date() - self.roast_date).days

        # 计算进度（基于60天，反向计算）
        if days_since_roast >= 60:
            progress = 0  # 超过60天进度为0
        else:
            progress = (60 - days_since_roast) / 60 * 100  # 剩余天数的百分比

        # 判断是否在最佳赏味期
        in_best_period = False
        if self.rest_period_min:
            if self.rest_period_max:
                # 范围模式：必须严格在范围内
                in_best_period = self.rest_period_min <= days_since_roast <= self.rest_period_max
            else:
                # 单一值模式：必须等于最小值
                in_best_period = days_since_roast == self.rest_period_min

        return (progress, in_best_period)

    def __str__(self):
        return f"{self.name}"

    def delete(self, *args, **kwargs):
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def save(self, *args, **kwargs):
        # 处理养豆期字段
        if self.rest_period_min and self.rest_period_max:
            if self.rest_period_min == self.rest_period_max:
                # 如果最小值等于最大值，清除最大值，转换为单一值模式
                self.rest_period_max = None
        elif self.rest_period_max and not self.rest_period_min:
            # 如果只设置了最大值，将最小值设为相同值
            self.rest_period_min = self.rest_period_max

        # 如果没有设置创建时间，使用当前时间
        if not self.created_at:
            self.created_at = timezone.now()

        # 确保创建时间格式统一(去除微秒)
        if self.created_at:
            self.created_at = self.created_at.replace(microsecond=0)

        # 如果是新创建的记录，保存初始值
        if not self.pk:  # 如果是新记录
            self.initial_bag_weight = self.bag_weight
            self.initial_bag_remain = self.bag_remain
            self.initial_purchase_price = self.purchase_price
            self.initial_roast_date = self.roast_date
            self.initial_created_at = self.created_at
            self.initial_rest_period_min = self.rest_period_min
            self.initial_rest_period_max = self.rest_period_max

        super().save(*args, **kwargs)

    def get_blend_components(self):
        """获取拼配组件"""
        if self.type == 'BLEND':
            return self.blend_components.all().order_by('order')
        return []

    @property
    def has_blend_components(self):
        """检查是否有拼配组件"""
        return self.type == 'BLEND' and self.blend_components.exists()

    @classmethod
    def has_active_beans(cls, user):
        """
        检查用户是否有活跃的咖啡豆

        Args:
            user: 用户对象

        Returns:
            bool: 是否有活跃的咖啡豆
        """
        return cls.objects.filter(
            user=user,
            is_archived=False,
            is_deleted=False
        ).exists()

    @classmethod
    def get_active_beans(cls, user):
        """
        获取用户的活跃咖啡豆列表

        Args:
            user: 用户对象

        Returns:
            QuerySet: 活跃咖啡豆查询集
        """
        return cls.objects.filter(
            user=user,
            is_archived=False,
            is_deleted=False
        )

    def get_stock_status(self):
        """
        获取咖啡豆的库存状态信息

        Returns:
            str: 库存状态描述，例如：'(已用完)'、'(剩余 100/200g)'、'(剩余 100g)'
        """
        if self.bag_remain is None:
            return ''

        if self.bag_remain <= 0:
            return '(已用完)'

        if self.bag_weight:
            return f'(剩余 {self.bag_remain}/{self.bag_weight}g)'

        return f'(剩余 {self.bag_remain}g)'

    @classmethod
    def get_favorite_bean(cls, user):
        """获取用户的首选咖啡豆"""
        return cls.objects.filter(
            user=user,
            is_favorite=True,
            is_archived=False,
            is_deleted=False
        ).first()

    def calculate_flavor_accuracy(self):
        """
        计算咖啡豆风味特征准确度

        计算方法：
        1. 获取所有使用该咖啡豆的冲煮记录中的风味标签（去重）
        2. 计算这些标签与咖啡豆本身风味标签的重叠度

        Returns:
            tuple: (准确度百分比, 实际品尝标签列表)
                - 如果没有足够数据计算，返回 (None, [])
        """
        # 如果咖啡豆没有风味标签，返回空结果
        if not self.flavor_tags.exists():
            return None, []

        # 获取所有使用该咖啡豆且有风味标签的冲煮记录
        records_with_tags = BrewingRecord.objects.filter(
            coffee_bean=self,
            flavor_tags__isnull=False
        ).distinct()

        if not records_with_tags.exists():
            return None, []

        # 获取所有冲煮记录的风味标签（去重）
        tasted_tags = set()
        for record in records_with_tags:
            tasted_tags.update(record.flavor_tags.values_list('id', flat=True))

        if not tasted_tags:
            return None, []

        # 获取咖啡豆的风味标签
        bean_tags = set(self.flavor_tags.values_list('id', flat=True))

        # 计算重叠度
        overlap = len(tasted_tags.intersection(bean_tags))
        total_unique = len(tasted_tags.union(bean_tags))

        if total_unique == 0:
            return None, []

        # 获取实际品尝标签的名称列表
        tasted_tag_names = FlavorTag.objects.filter(
            id__in=tasted_tags
        ).values_list('name', flat=True)

        accuracy = round((overlap / total_unique) * 100)
        return accuracy, list(tasted_tag_names)

    @classmethod
    def has_any_beans(cls, user):
        """检查用户是否有任何咖啡豆记录（包括已删除的）"""
        return cls.objects.filter(user=user).exists()

    def get_stats(user):
        """获取用户的咖啡豆统计数据"""
        coffee_beans = CoffeeBean.objects.filter(user=user)
        active_beans = coffee_beans.filter(is_archived=False, is_deleted=False)

        # 先计算养豆中的数量
        resting_beans = active_beans.filter(
            roast_date__isnull=False,
            rest_period_min__isnull=False
        ).annotate(
            days_since_roast=ExtractDay(Now() - F('roast_date'))
        ).filter(
            days_since_roast__lt=F('rest_period_min')
        )

        resting_count = resting_beans.count()

        # 计算使用中的数量时排除养豆中的豆子
        in_use_beans = active_beans.filter(
            Q(bag_remain__isnull=True) |
            Q(bag_remain__gt=0)
        ).exclude(
            id__in=resting_beans.values_list('id', flat=True)
        )

        stats = {
            'in_use': in_use_beans.count(),
            'out_of_stock': active_beans.filter(
                bag_remain__isnull=False,
                bag_remain__lte=0
            ).count(),
            'resting': resting_count,
            'archived': coffee_beans.filter(
                is_archived=True,
                is_deleted=False
            ).count()
        }

        return stats

    def get_rest_period_dates(self):
        """
        获取咖啡豆的最佳赏味期起止日期

        Returns:
            tuple: (rest_start, rest_end) 或 (None, None)
        """
        if not self.roast_date or not self.rest_period_min:
            return None, None

        rest_start = self.roast_date + timedelta(days=self.rest_period_min)
        if self.rest_period_max and self.rest_period_max != self.rest_period_min:
            rest_end = self.roast_date + timedelta(days=self.rest_period_max)
        else:
            rest_end = rest_start

        return rest_start, rest_end

    @classmethod
    def get_calendar_beans(cls, user, calendar_start, calendar_end):
        """
        获取日历范围内的活跃咖啡豆

        Args:
            user: 用户对象
            calendar_start: 日历开始日期
            calendar_end: 日历结束日期

        Returns:
            QuerySet: 活跃的咖啡豆查询集
        """
        rest_start_date = ExpressionWrapper(
            F('roast_date') + F('rest_period_min') * timedelta(days=1),
            output_field=DateTimeField()
        )

        rest_end_date = ExpressionWrapper(
            F('roast_date') + F('rest_period_max') * timedelta(days=1),
            output_field=DateTimeField()
        )

        return cls.objects.filter(
            user=user,
            is_archived=False,
            is_deleted=False
        ).filter(
            # 购买日期在范围内
            Q(created_at__date__range=(calendar_start, calendar_end)) |
            # 烘焙日期在范围内
            Q(roast_date__range=(calendar_start, calendar_end)) |
            # 最佳赏味期范围与日历范围有重叠
            Q(
                roast_date__isnull=False,
                rest_period_min__isnull=False,
                roast_date__lt=calendar_end
            )
        ).select_related('user').distinct()

    def get_calendar_display_info(self, color=None, progress=None):
        """
        获取用于日历显示的咖啡豆信息

        Args:
            color: 可选的颜色值
            progress: 可选的进度值

        Returns:
            dict: 包含显示信息的字典
        """
        if progress is None and self.bag_weight and self.bag_remain:
            progress = float((self.bag_remain / self.bag_weight) * 100)

        return {
            'id': self.id,
            'name': self.name,
            'roaster': self.roaster,
            'color': color,
            'progress': float(progress) if progress is not None else None,
            'bag_remain': float(self.bag_remain) if self.bag_remain is not None else None,
            'bag_weight': float(self.bag_weight) if self.bag_weight is not None else None,
        }

    def get_tasting_summary(self):
        """
        获取使用该咖啡豆的所有冲煮记录的品鉴数据汇总

        Returns:
            tuple: (
                dimensions_avg: dict - 五个维度的平均值,
                tasting_count: int - 有效品鉴记录数量,
                unique_flavor_tags: QuerySet - 去重后的风味标签
            )
        """
        # 获取有品鉴笔记的记录
        records_with_notes = BrewingRecord.objects.filter(
            coffee_bean=self
        ).filter(
            Q(flavor_tags__isnull=False) |
            Q(aroma__gt=0) |
            Q(acidity__gt=0) |
            Q(sweetness__gt=0) |
            Q(aftertaste__gt=0) |
            Q(body__gt=0)
        ).distinct()

        tasting_count = records_with_notes.count()

        if tasting_count == 0:
            return None, 0, None

        # 计算五个维度的平均值
        dimensions_avg = records_with_notes.aggregate(
            avg_aroma=Avg('aroma'),
            avg_acidity=Avg('acidity'),
            avg_sweetness=Avg('sweetness'),
            avg_aftertaste=Avg('aftertaste'),
            avg_body=Avg('body')
        )

        # 获取所有风味标签（去重）
        unique_flavor_tags = FlavorTag.objects.filter(
            brewing_records__in=records_with_notes  # 修改这里，使用正确的反向关系名
        ).distinct()

        return dimensions_avg, tasting_count, unique_flavor_tags

    @property
    def created_at_timestamp(self):
        return self.created_at.timestamp() if self.created_at else None

    @property
    def purchase_date_timestamp(self):
        return self.created_at.timestamp() if self.created_at else None

    @property
    def roast_date_timestamp(self):
        if not self.roast_date:
            return None
        # 将date转换为datetime，设置时间为当天的开始
        dt = datetime.combine(self.roast_date, datetime.min.time())
        dt = timezone.make_aware(dt)  # 确保是aware datetime
        return dt.timestamp()

    def check_blend_components_attributes(self):
        """
        检查拼配咖啡豆组件是否有各个属性的有效值

        Returns:
            dict: 包含各属性检查结果的字典
        """
        if self.type != 'BLEND':
            return {}

        blend_components = self.blend_components.all().order_by('order')

        # 检查拼配组件是否有各个属性的有效值
        has_component_origin = blend_components.exclude(origin='').exists()
        has_component_region = blend_components.exclude(region='').exists()
        has_component_finca = blend_components.exclude(finca='').exists()
        has_component_variety = blend_components.exclude(variety='').exists()
        has_component_process = blend_components.exclude(process='').exists()

        # 检查海拔
        has_component_altitude = False
        for component in blend_components:
            if (component.altitude_type == 'SINGLE' and component.altitude_single) or \
               (component.altitude_type == 'RANGE' and component.altitude_min and component.altitude_max):
                has_component_altitude = True
                break

        return {
            'has_component_origin': has_component_origin,
            'has_component_region': has_component_region,
            'has_component_finca': has_component_finca,
            'has_component_variety': has_component_variety,
            'has_component_process': has_component_process,
            'has_component_altitude': has_component_altitude,
        }

    def get_usage_data(self):
        """
        获取咖啡豆的使用数据，包括使用次数、最后使用时间、平均评分等

        Returns:
            dict: 包含使用数据的字典
        """
        # 获取所有使用该咖啡豆的冲煮记录
        records = BrewingRecord.objects.filter(coffee_bean=self)
        usage_count = records.count()

        # 最后使用时间
        last_record = records.order_by('-created_at').first()
        last_used = None
        days_since_last_use = None

        if last_record:
            last_used = last_record.created_at
            # 修改为纯日期比较
            last_use_date = last_record.created_at.astimezone(ZoneInfo('Asia/Shanghai')).date()
            today = timezone.now().astimezone(ZoneInfo('Asia/Shanghai')).date()

            # 计算使用间隔天数
            if last_use_date == today:
                days_since_last_use = 0
            elif last_use_date == today - timedelta(days=1):
                days_since_last_use = 1
            else:
                days_since_last_use = (today - last_use_date).days

        # 计算平均评分
        avg_rating = None
        if records.exists():
            avg_rating_val = records.aggregate(Avg('rating_level'))['rating_level__avg']
            avg_rating = round(avg_rating_val, 1) if avg_rating_val else None

        return {
            'usage_count': usage_count,
            'last_used': last_used,
            'days_since_last_use': days_since_last_use,
            'avg_rating': avg_rating
        }

    def get_most_used_equipment(self):
        """
        获取使用该咖啡豆最常用的冲煮器具

        Returns:
            dict or None: 包含最常用冲煮器具信息的字典，无数据时返回None
        """
        usage_data = self.get_usage_data()

        if usage_data['usage_count'] > 0:
            return BrewingRecord.objects.filter(coffee_bean=self)\
                .values('brewing_equipment__brew_method')\
                .annotate(count=Count('brewing_equipment'))\
                .order_by('-count')\
                .first()
        return None

    def calculate_remaining_uses(self):
        """
        计算咖啡豆剩余可用次数

        Returns:
            int or None: 剩余可用次数，无法计算时返回None
        """
        if not self.bag_remain:
            return None

        # 计算最近30次使用的平均剂量
        avg_dose = BrewingRecord.objects.filter(
            coffee_bean=self
        ).order_by('-created_at')[:30].aggregate(
            Avg('dose_weight')
        )['dose_weight__avg']

        if not avg_dose:
            return None

        return int(self.bag_remain / avg_dose)

    def calculate_repurchase_interval(self):
        """
        计算咖啡豆平均回购周期

        Returns:
            tuple: (
                avg_repurchase_interval: timedelta或None - 平均回购周期,
                occurrences: list - 附加了time_gap的回购记录列表
            )
        """
        # 获取回购记录
        occurrences = list(self.occurrences.all().order_by('-created_at'))

        # 计算平均回购周期
        avg_repurchase_interval = None
        if len(occurrences) > 1:
            total_interval = timedelta(0)
            intervals_count = 0

            for i in range(len(occurrences)-1):
                current = occurrences[i]
                next_occurrence = occurrences[i+1]

                # 确保时间有时区信息
                current_time = current.created_at
                next_time = next_occurrence.created_at

                if timezone.is_naive(current_time):
                    current_time = timezone.make_aware(current_time)
                if timezone.is_naive(next_time):
                    next_time = timezone.make_aware(next_time)

                interval = current_time - next_time
                total_interval += interval
                intervals_count += 1

            if intervals_count > 0:
                avg_repurchase_interval = total_interval / intervals_count

        # 计算相邻记录之间的时间差
        for i in range(len(occurrences)-1):
            current = occurrences[i]
            next_occurrence = occurrences[i+1]

            # 确保时间有时区信息
            current_time = current.created_at
            next_time = next_occurrence.created_at

            if timezone.is_naive(current_time):
                current_time = timezone.make_aware(current_time)
            if timezone.is_naive(next_time):
                next_time = timezone.make_aware(next_time)

            # 计算时间差
            current.time_gap = current_time - next_time

        return avg_repurchase_interval, occurrences

class FlavorTag(models.Model):
    name = models.CharField(max_length=50)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    coffee_beans = models.ManyToManyField(CoffeeBean, related_name='flavor_tags')

    class Meta:
        unique_together = ('name', 'user')

    def __str__(self):
        return self.name

class BrewingRecordManager(models.Manager):
    def get_filtered_records(self, user, filters=None, page=1, per_page=20):
        """
        获取经过筛选的冲煮记录

        Args:
            user: 用户对象
            filters: 包含筛选条件的字典，可能包含以下键：
                - brew_method: 冲煮方法
                - coffee_bean: 咖啡豆ID
                - rating_range: 评分范围 (格式: "min-max")
                - search_query: 搜索关键词
                - date_from: 开始日期 (格式: "YYYY-MM-DD")
                - date_to: 结束日期 (格式: "YYYY-MM-DD")
            page: 当前页码（从1开始）
            per_page: 每页记录数
        """
        filters = filters or {}

        # 基础查询集
        queryset = self.filter(user=user)

        # 处理日期范围筛选
        date_from = filters.get('date_from')
        date_to = filters.get('date_to')

        # 如果没有指定日期范围
        if not date_from and not date_to:
            today = timezone.now()

            # 获取本月的记录数
            current_month_start = today.replace(day=1)
            current_month_records = queryset.filter(
                created_at__gte=current_month_start,
                created_at__lte=today
            ).exists()

            # 如果本月没有记录，获取最后一条记录的月份
            if not current_month_records:
                last_record = queryset.order_by('-created_at').first()
                if last_record:
                    last_record_date = last_record.created_at
                    date_from = last_record_date.replace(day=1).strftime('%Y-%m-%d')
                    date_to = (last_record_date.replace(day=1) + timedelta(days=32)).replace(day=1) - timedelta(days=1)
                    date_to = date_to.strftime('%Y-%m-%d')
                else:
                    # 如果没有任何记录，使用当前月份
                    date_from = current_month_start.strftime('%Y-%m-%d')
                    date_to = today.strftime('%Y-%m-%d')
            else:
                # 有本月记录，使用当前月份
                date_from = current_month_start.strftime('%Y-%m-%d')
                date_to = today.strftime('%Y-%m-%d')

            # 更新filters以保持显示值的一致性
            filters['date_from'] = date_from
            filters['date_to'] = date_to

        # 应用日期范围筛选
        if date_from:
            try:
                date_from_obj = timezone.make_aware(
                    datetime.strptime(date_from, '%Y-%m-%d'),
                    timezone=ZoneInfo('Asia/Shanghai')
                )
                queryset = queryset.filter(created_at__gte=date_from_obj)
            except ValueError:
                pass

        if date_to:
            try:
                date_to_obj = timezone.make_aware(
                    datetime.strptime(date_to, '%Y-%m-%d'),
                    timezone=ZoneInfo('Asia/Shanghai')
                ).replace(hour=23, minute=59, second=59)
                queryset = queryset.filter(created_at__lte=date_to_obj)
            except ValueError:
                pass

        # 应用其他筛选条件
        brew_method = filters.get('brew_method')
        if brew_method:
            queryset = queryset.filter(brewing_equipment__brew_method=brew_method)

        coffee_bean = filters.get('coffee_bean')
        if coffee_bean:
            queryset = queryset.filter(coffee_bean_id=coffee_bean)

        rating_range = filters.get('rating_range')
        if rating_range:
            try:
                min_rating, max_rating = map(float, rating_range.split('-'))
                # 将评分区间转换为对应的 rating_level 范围
                # 8.1-10 分对应 rating_level 9-10
                # 6.1-8 分对应 rating_level 7-8
                # 4.1-6 分对应 rating_level 5-6
                # 2.1-4 分对应 rating_level 3-4
                # 0-2 分对应 rating_level 1-2
                if min_rating == 8.1:  # 8.1-10
                    queryset = queryset.filter(rating_level__gte=9)
                elif min_rating == 6.1:  # 6.1-8
                    queryset = queryset.filter(rating_level__gte=7, rating_level__lte=8)
                elif min_rating == 4.1:  # 4.1-6
                    queryset = queryset.filter(rating_level__gte=5, rating_level__lte=6)
                elif min_rating == 2.1:  # 2.1-4
                    queryset = queryset.filter(rating_level__gte=3, rating_level__lte=4)
                else:  # 0-2
                    queryset = queryset.filter(rating_level__gte=1, rating_level__lte=2)
            except ValueError:
                pass

        # 全文搜索
        search_query = filters.get('search_query')
        if search_query:
            queryset = queryset.filter(
                Q(recipe_name__icontains=search_query) |
                Q(coffee_bean__name__icontains=search_query) |
                Q(coffee_bean__roaster__icontains=search_query) |  # 添加对烘焙商的搜索
                Q(brewing_equipment__name__icontains=search_query) |
                Q(grinding_equipment__name__icontains=search_query) |
                Q(notes__icontains=search_query) |
                Q(steps__icontains=search_query) |
                Q(flavor_tags__name__icontains=search_query) |
                Q(water_quality__icontains=search_query)
            ).distinct()  # 添加distinct()避免因flavor_tags关联导致的重复结果

        # 添加排序和关联查询优化
        queryset = queryset.select_related(
            'coffee_bean',
            'brewing_equipment',
            'grinding_equipment',
            'gadget_kit'
        ).prefetch_related(
            'flavor_tags',
            'gadgets'
        ).order_by('-created_at')

        # 计算总记录数
        total_count = queryset.count()

        # 计算总页数
        total_pages = (total_count + per_page - 1) // per_page

        # 确保页码在有效范围内
        page = max(1, min(page, total_pages)) if total_pages > 0 else 1

        # 计算分页的起始和结束索引
        start = (page - 1) * per_page
        end = start + per_page

        # 返回分页数据和分页信息
        return {
            'records': queryset[start:end],
            'total_count': total_count,
            'total_pages': total_pages,
            'current_page': page,
            'per_page': per_page,
            'has_previous': page > 1,
            'has_next': page < total_pages,
            'previous_page': page - 1 if page > 1 else None,
            'next_page': page + 1 if page < total_pages else None,
        }

    def get_available_years(self, user):
        """
        获取用户有冲煮记录的年份列表

        Args:
            user: 用户对象

        Returns:
            dict: 包含以下键：
                - years: 有记录的年份列表
                - current_year: 当前年份
        """
        years_range = self.filter(user=user).aggregate(
            min_year=models.Min('created_at__year'),
            max_year=models.Max('created_at__year')
        )

        if years_range['min_year'] and years_range['max_year']:
            years = list(range(years_range['min_year'], years_range['max_year'] + 1))
        else:
            years = [timezone.now().year]

        return {
            'years': years,
            'current_year': timezone.now().year
        }

    def generate_calendar_data(self, user, year):
        """
        生成指定年份的热力图数据

        Args:
            user: 用户对象
            year: 年份

        Returns:
            dict: 包含以下键：
                - calendar_data: 日历数据列表
                - current_month_index: 当前月份索引（0-11）
                - months: 月份名称列表
                - weekdays: 星期名称列表
        """
        try:
            # 设置年份的起始和结束日期（使用用户时区）
            tz = ZoneInfo('Asia/Shanghai')
            start_date = timezone.datetime(year, 1, 1, tzinfo=tz)
            end_date = timezone.datetime(year, 12, 31, 23, 59, 59, 999999, tzinfo=tz)

            # 获取该年份的冲煮记录，使用用户时区进行日期转换
            brewing_records = self.filter(
                user=user,
                created_at__gte=start_date,
                created_at__lte=end_date
            ).annotate(
                date=TruncDate('created_at', tzinfo=tz)
            ).values('date').annotate(
                count=Count('id')
            ).order_by('date')

            # 转换为日期字典格式
            records_dict = {}
            for record in brewing_records:
                date_str = record['date'].strftime('%Y-%m-%d')
                records_dict[date_str] = {
                    'count': record['count'],
                    'date': date_str
                }

            # 生成完整的年度数据
            calendar_data = []
            current_date = start_date

            # 计算1月1日的周数偏移量
            jan1_weekday = start_date.weekday()

            # 使用日期循环而不是时间循环，避免时区问题
            while current_date.date() <= end_date.date():
                date_str = current_date.strftime('%Y-%m-%d')
                record = records_dict.get(date_str, {'count': 0, 'date': date_str})

                # 计算周数，考虑1月1日的偏移
                day_of_year = (current_date - start_date).days
                week = (day_of_year + jan1_weekday) // 7 + 1

                calendar_data.append({
                    'date': date_str,
                    'count': record['count'],
                    'weekday': current_date.weekday(),
                    'week': week,
                    'month': current_date.month - 1  # 0-based month index
                })

                # 增加一天，保持在同一时区
                current_date = (current_date + timedelta(days=1)).replace(
                    hour=0, minute=0, second=0, microsecond=0
                )

            return {
                'calendar_data': calendar_data,
                'current_month_index': timezone.now().month - 1 if year == timezone.now().year else None,
                'months': ['一月', '二月', '三月', '四月', '五月', '六月',
                          '七月', '八月', '九月', '十月', '十一月', '十二月'],
                'weekdays': ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            }

        except Exception as e:
            logger.error(f"Error in generate_calendar_data: {str(e)}")
            logger.error("Traceback:", exc_info=True)
            return {
                'calendar_data': [],
                'current_month_index': None,
                'months': ['一月', '二月', '三月', '四月', '五月', '六月',
                          '七月', '八月', '九月', '十月', '十一月', '十二月'],
                'weekdays': ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            }

class BrewingRecord(models.Model):
    # 添加自定义管理器
    objects = BrewingRecordManager()

    RATING_LEVEL_CHOICES = [
        (1, "😫"),
        (2, "☹️"),
        (3, "🙁"),
        (4, "😕"),
        (5, "😐"),
        (6, "🙂"),
        (7, "😌"),
        (8, "😃"),
        (9, "😄"),
        (10, "😆"),
    ]

    # 品鉴维度评分范围
    FLAVOR_PROFILE_CHOICES = [
        (0, "未评分"),
        (1, "1分"),
        (2, "2分"),
        (3, "3分"),
        (4, "4分"),
        (5, "5分"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    brewing_equipment = models.ForeignKey(
        Equipment,
        on_delete=models.SET_NULL,
        null=True,
        related_name='brewing_records',
        limit_choices_to={'type': 'BREWER'}
    )
    coffee_bean = models.ForeignKey(CoffeeBean, on_delete=models.SET_NULL, null=True)
    grind_size = models.CharField(max_length=50)
    grinding_equipment = models.ForeignKey(
        Equipment,
        on_delete=models.SET_NULL,
        null=True,
        related_name='grinding_records',
        limit_choices_to={'type': 'GRINDER'}
    )
    dose_weight = models.DecimalField(max_digits=5, decimal_places=2)
    yield_weight = models.DecimalField(max_digits=6, decimal_places=2)
    water_temperature = models.DecimalField(max_digits=4, decimal_places=1, null=True, blank=True)
    rating_level = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(10)],
        choices=RATING_LEVEL_CHOICES,
        default=6,
        verbose_name='评分'
    )
    brewing_time = models.DurationField()
    notes = models.TextField(max_length=500, blank=True)
    created_at = models.DateTimeField(
        auto_now_add=False,
        default=timezone.now,
        verbose_name='记录时间'
    )
    steps = models.JSONField(
        default=list,
        blank=True,
        verbose_name='详细步骤',
        help_text='存储格式：[{"text": "步骤描述", "timer": "MM:SS", "order": 1}, ...]'
    )
    gadgets = models.ManyToManyField(
        Equipment,
        related_name='brewing_records_as_gadget',
        limit_choices_to={'type': 'GADGET'},
        blank=True,
        verbose_name='小工具'
    )
    recipe_name = models.CharField(max_length=50, blank=True, null=True, verbose_name="配方名称")
    gadget_kit = models.ForeignKey(
        Equipment,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='brewing_records_as_kit',
        limit_choices_to=models.Q(
            type='GADGET_KIT',
            is_archived=False,
            is_deleted=False,
            gadget_components__isnull=False  # 确保组合中有组件
        ) & ~models.Q(
            brewing_records_as_gadget__isnull=False  # 排除被用作小工具的设备
        ) & ~models.Q(
            type='GADGET'  # 明确排除小工具类型
        ),
        verbose_name='小工具组合'
    )

    # 品鉴笔记相关字段
    flavor_tags = models.ManyToManyField(
        FlavorTag,
        related_name='brewing_records',
        blank=True,
        verbose_name='实际品尝风味'
    )
    aroma = models.IntegerField(
        choices=FLAVOR_PROFILE_CHOICES,
        default=0,
        verbose_name='香气'
    )
    acidity = models.IntegerField(
        choices=FLAVOR_PROFILE_CHOICES,
        default=0,
        verbose_name='酸质'
    )
    body = models.IntegerField(
        choices=FLAVOR_PROFILE_CHOICES,
        default=0,
        verbose_name='醇厚'
    )
    sweetness = models.IntegerField(
        choices=FLAVOR_PROFILE_CHOICES,
        default=0,
        verbose_name='甜度'
    )
    aftertaste = models.IntegerField(
        choices=FLAVOR_PROFILE_CHOICES,
        default=0,
        verbose_name='余韵'
    )

    # 在 BrewingRecord 类中添加新字段
    water_quality = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='水质',
        help_text='水质或水源信息'
    )

    room_temperature = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        null=True,
        blank=True,
        verbose_name='室温',
        help_text='单位：℃',
        validators=[
            MinValueValidator(Decimal('-50.0')),
            MaxValueValidator(Decimal('50.0'))
        ]
    )

    room_humidity = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='环境湿度',
        help_text='单位：%',
        validators=[
            MinValueValidator(0),
            MaxValueValidator(100)
        ]
    )

    class Meta:
        unique_together = ('user', 'brewing_equipment', 'coffee_bean', 'created_at')

    def delete(self, *args, **kwargs):
        """
        重写delete方法，在删除冲煮记录前回退咖啡豆库存，同时处理缓存更新逻辑
        """
        # 导入日志模块
        import logging
        logger = logging.getLogger(__name__)

        # 保存咖啡豆ID以便清除缓存
        bean_id = self.coffee_bean.id if self.coffee_bean else None
        user_id = self.user.id

        # 如果有关联咖啡豆且不是删除或归档状态，回退库存
        if self.coffee_bean and not self.coffee_bean.is_deleted and not self.coffee_bean.is_archived:
            # 获取最新状态的咖啡豆
            coffee_bean = CoffeeBean.objects.get(id=self.coffee_bean.id)

            # 计算新的库存值
            new_bag_remain = coffee_bean.bag_remain + self.dose_weight

            # 确保不超过总重量
            if coffee_bean.bag_weight and new_bag_remain > coffee_bean.bag_weight:
                new_bag_remain = coffee_bean.bag_weight

            # 更新库存
            coffee_bean.bag_remain = new_bag_remain
            coffee_bean.save()

            # 记录日志
            logger.info(f"删除冲煮记录 ID={self.id}：已更新咖啡豆 {coffee_bean.id} 的库存，增加 {self.dose_weight}g，新库存为 {new_bag_remain}g")

        # 调用原始的delete方法
        result = super().delete(*args, **kwargs)

        # 更新相关缓存
        try:
            # 导入缓存工具
            from my.cache_utils import invalidate_bean_cache, invalidate_hindsight_cache, invalidate_record_cache

            # 清除相关咖啡豆的缓存
            if bean_id:
                invalidate_bean_cache(user_id, bean_id)

            # 清除用户统计数据缓存
            invalidate_hindsight_cache(user_id)

            # 清除趋势缓存
            from django.core.cache import cache

            # 使用本模块中的get_trend_cache_key函数
            trend_cache_key = get_trend_cache_key(user_id, self.id)
            cache.delete(trend_cache_key)
            # 清除冲煮记录相关缓存
            invalidate_record_cache(user_id, self.id)

            logger.info(f"已清除用户 {user_id} 相关缓存")
        except Exception as e:
            # 缓存清除失败不应影响记录删除操作
            logger.error(f"清除缓存时出错: {str(e)}")

        return result

    def save(self, *args, **kwargs):
        if self.created_at and timezone.is_naive(self.created_at):
            self.created_at = timezone.make_aware(
                self.created_at,
                timezone=ZoneInfo('Asia/Shanghai')
            )

        # 确保创建时间格式统一(去除微秒)
        if self.created_at:
            self.created_at = self.created_at.replace(microsecond=0)

        # 检查是否存在重复记录
        while BrewingRecord.objects.filter(
            user=self.user,
            brewing_equipment=self.brewing_equipment,
            coffee_bean=self.coffee_bean,
            created_at=self.created_at
        ).exclude(id=self.id).exists():  # 排除自身
            # 如果存在重复记录，将时间戳加1秒
            self.created_at += timedelta(seconds=1)

        super().save(*args, **kwargs)

    @classmethod
    def get_streak_info(cls, user):
        """
        获取用户的打卡信息，包括当前连续打卡天数和上次打卡距今天数

        Args:
            user: 用户对象

        Returns:
            dict: 包含以下键：
                - current_streak: 当前连续打卡天数
                - last_streak_days: 上次打卡距今天数（仅当current_streak为0时有值）
        """
        # 获取当前连续打卡天数
        current_streak = cls.get_streak_days(user)

        # 如果当前没有连续打卡，计算上次打卡距今天数
        last_streak_days = None
        if current_streak == 0:
            try:
                # 获取最新的一条记录
                latest_record = cls.objects.filter(user=user).order_by('-created_at').first()

                if latest_record:
                    # 使用Asia/Shanghai时区
                    tz = ZoneInfo('Asia/Shanghai')

                    # 获取当前时间并确保使用正确的时区
                    now = timezone.now().astimezone(tz)

                    # 确保记录时间使用相同的时区
                    last_record_time = latest_record.created_at
                    if timezone.is_naive(last_record_time):
                        last_record_time = timezone.make_aware(last_record_time, tz)
                    else:
                        last_record_time = last_record_time.astimezone(tz)

                    # 计算天数差异（转换为日期后计算）
                    last_streak_days = (now.date() - last_record_time.date()).days
            except Exception as e:
                logger.error(f"Error calculating last_streak_days: {e}")
                last_streak_days = None

        return {
            'current_streak': current_streak,
            'last_streak_days': last_streak_days
        }

    @classmethod
    def get_streak_days(cls, user):
        """计算用户的连续打卡天数"""
        records = cls.objects.filter(
            user=user
        ).order_by('-created_at').values(
            date=TruncDate('created_at')
        ).distinct()

        if not records:
            return 0

        # 获取所有打卡日期
        dates = [record['date'] for record in records]

        # 获取今天和昨天的日期
        today = date.today()
        yesterday = today - timedelta(days=1)

        # 获取最后一次打卡日期
        last_date = dates[0]

        # 如果最后一次打卡不是今天也不是昨天，说明连续打卡中断
        if last_date < yesterday:
            return 0

        # 从最后一次打卡日期开始计算
        current_date = last_date
        streak = 1

        # 遍历剩余的日期
        for next_date in dates[1:]:
            # 如果日期连续，增加计数
            if (current_date - next_date).days == 1:
                streak += 1
                current_date = next_date
            # 如果是同一天的记录，跳过
            elif (current_date - next_date).days == 0:
                continue
            # 如果发现间隔，结束计算
            else:
                break

        return streak

    @classmethod
    def get_brewing_stats(cls, user):
        """
        获取用户的冲煮统计数据

        Args:
            user: 用户对象

        Returns:
            dict: 包含以下键：
                - month_count: 本月冲煮次数
                - year_count: 本年冲煮次数
                - streak_days: 连续冲煮天数
        """
        try:
            # 使用Asia/Shanghai时区
            tz = ZoneInfo('Asia/Shanghai')
            now = timezone.now().astimezone(tz)

            # 获取本月第一天
            month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # 获取本年第一天
            year_start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

            # 获取本月和本年的记录数
            month_count = cls.objects.filter(
                user=user,
                created_at__gte=month_start,
                created_at__lte=now
            ).count()

            year_count = cls.objects.filter(
                user=user,
                created_at__gte=year_start,
                created_at__lte=now
            ).count()

            # 使用现有的get_streak_days方法获取连续冲煮天数
            streak_days = cls.get_streak_days(user)

            return {
                'month_count': month_count,
                'year_count': year_count,
                'streak_days': streak_days
            }

        except Exception as e:
            logger.error(f"Error in get_brewing_stats: {str(e)}")
            return {
                'month_count': 0,
                'year_count': 0,
                'streak_days': 0
            }

    @classmethod
    def parse_steps_from_post(cls, post_data):
        """
        从POST数据中解析步骤数据

        Args:
            post_data: POST请求数据

        Returns:
            tuple: (steps_enabled, steps_list)
                - steps_enabled: 是否启用步骤
                - steps_list: 步骤列表
        """
        # 检查是否有步骤数据
        steps_enabled = any(key.startswith('steps[') for key in post_data.keys())
        steps = []

        if steps_enabled:
            step_index = 0
            while f'steps[{step_index}][text]' in post_data:
                step_text = post_data.get(f'steps[{step_index}][text]')
                if step_text.strip():
                    step = {
                        'text': step_text,
                        'order': step_index + 1
                    }

                    # 处理定时器数据
                    has_timer = post_data.get(f'steps[{step_index}][has_timer]') == 'on'
                    if has_timer:
                        minutes = int(post_data.get(f'steps[{step_index}][minutes]', 0))
                        seconds = int(post_data.get(f'steps[{step_index}][seconds]', 0))
                        step['timer'] = f"{minutes:02d}:{seconds:02d}"

                    steps.append(step)
                step_index += 1

        return steps_enabled, steps

    @classmethod
    def format_steps_for_json(cls, post_data):
        """
        将POST数据中的步骤格式化为JSON格式（用于表单验证失败时保持数据）

        Args:
            post_data: POST请求数据

        Returns:
            list: 格式化后的步骤列表
        """
        steps = []
        step_index = 0

        while f'steps[{step_index}][text]' in post_data:
            step_text = post_data.get(f'steps[{step_index}][text]')
            if step_text.strip():
                step = {
                    'text': step_text,
                    'hasTimer': post_data.get(f'steps[{step_index}][has_timer]') == 'on',
                    'minutes': post_data.get(f'steps[{step_index}][minutes]', 0),
                    'seconds': post_data.get(f'steps[{step_index}][seconds]', 0)
                }
                steps.append(step)
            step_index += 1

        return steps

    @classmethod
    def format_steps_from_record(cls, record_steps):
        """
        将记录中的步骤数据格式化（用于复制记录时）

        Args:
            record_steps: 记录中的步骤数据

        Returns:
            list: 格式化后的步骤列表
        """
        steps = []
        for step in record_steps:
            timer = step.get('timer')
            minutes = seconds = 0
            if timer and ':' in timer:
                minutes, seconds = map(int, timer.split(':'))

            step_data = {
                'text': step.get('text', ''),
                'order': step.get('order', 1),
                'hasTimer': bool(timer),
                'minutes': minutes,
                'seconds': seconds
            }
            steps.append(step_data)
        return steps

    def get_history_data(self, limit=9):
        """获取相同配方的历史记录数据"""
        if not self.recipe_name:
            return []

        # 获取除当前记录外的历史记录
        history_records = BrewingRecord.objects.filter(
            user=self.user,
            recipe_name=self.recipe_name,
        ).exclude(
            id=self.id
        ).order_by('-created_at')[:limit]

        # 如果历史记录数量为0（即只有当前记录），返回空列表
        if history_records.count() == 0:
            return []

        # 准备数据
        history_data = [self._format_record_data(self)]  # 添加当前记录
        history_data.extend([
            self._format_record_data(record)
            for record in history_records
        ])

        # 按日期排序
        history_data.sort(key=lambda x: x['date'])
        return history_data

    @staticmethod
    def _format_record_data(record):
        """格式化记录数据"""
        return {
            'id': record.id,
            'date': record.created_at.strftime('%Y-%m-%d'),
            'rating': float(record.rating_level) if record.rating_level else 0,
            'grind_size': record.grind_size,
            'water_temp': float(record.water_temperature) if record.water_temperature else None,
            'dose': float(record.dose_weight),
            'yield': float(record.yield_weight),
            'time': str(record.brewing_time) if record.brewing_time else None,
            'notes': record.notes
        }

    @property
    def has_tasting_notes(self):
        return (
            self.flavor_tags.exists() or
            any([
                self.aroma > 0,
                self.acidity > 0,
                self.sweetness > 0,
                self.aftertaste > 0,
                self.body > 0
            ])
        )

    def calculate_tag_overlap(self):
        """计算当前冲煮记录的风味标签与咖啡豆风味标签的重叠度"""
        if not self.coffee_bean or not self.coffee_bean.flavor_tags.exists():
            return None

        record_tags = set(self.flavor_tags.values_list('id', flat=True))
        bean_tags = set(self.coffee_bean.flavor_tags.values_list('id', flat=True))

        if not record_tags or not bean_tags:
            return None

        # 计算重叠度
        overlap = len(record_tags.intersection(bean_tags))
        total_unique = len(record_tags.union(bean_tags))

        if total_unique == 0:
            return None

        return round((overlap / total_unique) * 100)

    @property
    def created_at_timestamp(self):
        return self.created_at.timestamp() if self.created_at else None

class CoffeeQuote(models.Model):
    content = models.TextField('内容')
    content_original = models.TextField('原文', blank=True, help_text='可选，如果是翻译的话可以填写原文')
    author = models.CharField('作者', max_length=100)
    source = models.CharField('出处', max_length=200, blank=True)

    class Meta:
        verbose_name = '咖啡箴言'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.content[:20]}... - {self.author}"

    @classmethod
    def get_random_quote(cls):
        """
        从缓存的箴言列表中随机获取一条箴言

        Returns:
            CoffeeQuote: 随机箴言对象，如果没有箴言则返回None
        """
        cache_key = 'all_coffee_quotes'
        quotes = cache.get(cache_key)

        if quotes is None:
            quotes = list(cls.objects.all())
            if not quotes:
                return None

            # 缓存所有箴言一周时间，因为箴言内容很少更新
            cache.set(cache_key, quotes, timeout=7 * 24 * 3600)  # 缓存1周

        return random.choice(quotes) if quotes else None

    @classmethod
    def clear_cache(cls):
        """
        清除箴言缓存
        在添加、修改或删除箴言时调用
        """
        cache.delete('all_coffee_quotes')

class BlendComponent(models.Model):
    coffee_bean = models.ForeignKey(
        CoffeeBean,
        on_delete=models.CASCADE,
        related_name='blend_components'
    )
    blend_ratio = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='拼配比例'
    )
    roast_level = models.IntegerField(
        default=4,
        validators=[MinValueValidator(1), MaxValueValidator(7)],
        verbose_name='烘焙程度'
    )
    origin = models.CharField(max_length=100, blank=True, verbose_name='产地')
    region = models.CharField(max_length=100, blank=True, verbose_name='产区')
    finca = models.CharField(max_length=100, blank=True, verbose_name='庄园')
    variety = models.CharField(max_length=100, blank=True, verbose_name='品种')
    process = models.CharField(max_length=100, blank=True, verbose_name='处理法')
    order = models.IntegerField(default=0, verbose_name='排序')

    # 添加海拔相关字段
    altitude_type = models.CharField(
        max_length=10,
        choices=[('SINGLE', '单一海拔'), ('RANGE', '海拔范围')],
        default='SINGLE',
        verbose_name='海拔类型'
    )
    altitude_single = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='种植海拔',
        help_text='单位:米',
        validators=[MinValueValidator(0), MaxValueValidator(9999)]  # 修改为9999
    )
    altitude_min = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='最低海拔',
        help_text='单位:米',
        validators=[MinValueValidator(0), MaxValueValidator(9999)]  # 修改为9999
    )
    altitude_max = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='最高海拔',
        help_text='单位:米',
        validators=[MinValueValidator(0), MaxValueValidator(9999)]  # 修改为9999
    )

    class Meta:
        verbose_name = '拼配组件'
        verbose_name_plural = '拼配组件'

    def __str__(self):
        return f"{self.coffee_bean.name} - 组件 {self.order + 1}"

    def get_roast_level_display(self):
        """获取烘焙度的显示名称"""
        roast_levels = {
            1: '极浅烘',
            2: '浅烘',
            3: '中浅烘',
            4: '中烘',
            5: '中深烘',
            6: '深烘',
            7: '极深烘'
        }
        return roast_levels.get(self.roast_level, '未知')

class BeanOccurrence(models.Model):
    coffee_bean = models.ForeignKey(
        CoffeeBean,
        on_delete=models.CASCADE,
        related_name='occurrences'
    )
    bag_weight = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='包装重量(g)'
    )
    bag_remain = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='剩余重量(g)'
    )
    purchase_price = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='购买价格',
        validators=[MinValueValidator(0)]
    )
    roast_date = models.DateField(
        null=True,
        blank=True,
        verbose_name='烘焙日期'
    )
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='购买时间'
    )
    rest_period_min = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='最短养豆期',
        help_text='单位:天',
        validators=[MinValueValidator(1), MaxValueValidator(60)]
    )
    rest_period_max = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='最长养豆期',
        help_text='单位:天',
        validators=[MinValueValidator(1), MaxValueValidator(60)]
    )

    class Meta:
        ordering = ['-created_at']
        verbose_name = '咖啡豆回购记录'
        verbose_name_plural = '咖啡豆回购记录'

    def __str__(self):
        return f"{self.coffee_bean.name} - {self.created_at.strftime('%Y-%m-%d')}"

    def clean(self):
        if self.bag_remain and self.bag_weight:
            if self.bag_remain > self.bag_weight:
                raise ValidationError('剩余重量不能大于包装重量')

    def save(self, *args, **kwargs):
        # 处理养豆期字段
        if self.rest_period_min and self.rest_period_max:
            if self.rest_period_min == self.rest_period_max:
                # 如果最小值等于最大值，清除最大值，转换为单一值模式
                self.rest_period_max = None
        elif self.rest_period_max and not self.rest_period_min:
            # 如果只设置了最大值，将最小值设为相同值
            self.rest_period_min = self.rest_period_max

        # 确保created_at没有微秒部分
        if self.created_at:
            self.created_at = self.created_at.replace(microsecond=0)

        super().save(*args, **kwargs)

    @property
    def created_at_timestamp(self):
        """获取创建时间的时间戳表示"""
        return self.created_at.timestamp() if self.created_at else None

    @property
    def roast_date_timestamp(self):
        """获取烘焙日期的时间戳表示"""
        if not self.roast_date:
            return None
        # 将date转换为datetime，设置时间为当天的开始
        dt = datetime.combine(self.roast_date, datetime.min.time())
        dt = timezone.make_aware(dt)  # 确保是aware datetime
        return dt.timestamp()

class HindsightStats:
    """处理冲煮记录统计的类"""

    # 定义时间范围选项
    TIME_RANGES = {
        'week': {'days': 7, 'label': '近一周'},
        'month': {'days': 30, 'label': '近一月'},
        'half_year': {'days': 180, 'label': '近半年'},
        'year': {'days': 365, 'label': '近一年'},
    }

    def __init__(self, user, time_range='week', year=None):
        self.user = user
        self.year = year

        if self.year is not None:
            # 如果指定了年份，使用该年份的起止时间
            tz = ZoneInfo('Asia/Shanghai')
            year = int(year)
            self.start_date = timezone.datetime(year, 1, 1, tzinfo=tz)
            self.end_date = timezone.datetime(year, 12, 31, 23, 59, 59, 999999, tzinfo=tz)
            # 计算年份的总天数（考虑闰年）
            self.total_days = (date(year, 12, 31) - date(year, 1, 1)).days + 1
            # 年鉴视图不使用 time_range
            self.time_range = None
            self.time_ranges = None
        else:
            # 否则使用相对时间范围
            self.time_range = time_range
            self.time_ranges = self.TIME_RANGES
            now = timezone.now()
            days = self.time_ranges[time_range]['days']
            self.total_days = days  # 相对时间范围使用预设的天数

            # 设置结束时间为今天的23:59:59.999999
            self.end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)

            # 设置开始时间为N天前的00:00:00
            self.start_date = (
                self.end_date.replace(hour=0, minute=0, second=0, microsecond=0) -
                timedelta(days=days - 1)
            )

        self.stats = {}
        self.total_records = 0

    @classmethod
    def get_stats(cls, user, time_range='week', year=None):
        """获取统计数据（带缓存）"""
        from .cache_utils import cache_manager, CACHE_TIMEOUT

        # 构建缓存键，加入年份参数
        cache_key = f'hindsight_{year}' if year else 'hindsight'

        # 如果是年鉴视图，不使用 time_range
        if year is not None:
            time_range = None

        # 尝试从缓存获取数据
        cached_data = cache_manager.get(cache_key, user.id, time_range)
        if cached_data:
            return cached_data

        # 计算新的统计数据
        stats_calculator = cls(user, time_range, year)
        stats = stats_calculator.calculate_all_stats()

        # 缓存数据
        cache_manager.set(
            cache_key, user.id, time_range,
            value={'stats': stats, 'total_records': stats_calculator.total_records},
            timeout=CACHE_TIMEOUT['MEDIUM']
        )

        return {'stats': stats, 'total_records': stats_calculator.total_records}

    def get_records_queryset(self):
        """获取基础的记录查询集"""
        return BrewingRecord.objects.filter(
            user=self.user,
            created_at__gte=self.start_date,
            created_at__lte=self.end_date
        ).select_related(
            'coffee_bean',
            'brewing_equipment'
        )

    def calculate_basic_stats(self):
        """计算基础统计数据"""
        records = self.get_records_queryset()
        self.total_records = records.count()

        if self.total_records == 0:
            self.stats.update(self.get_empty_stats())
            return

        # 计算总用豆量和平均每次用豆
        total_dose = records.aggregate(
            total_dose=Sum('dose_weight')
        )['total_dose'] or 0
        avg_dose = total_dose / self.total_records if self.total_records > 0 else 0

        # 计算活跃天数（有记录的天数）
        active_days = records.annotate(
            date=TruncDate('created_at')
        ).values('date').distinct().count()

        # 计算总天数
        if self.time_ranges is None:  # 年鉴视图
            # 使用该年的实际天数
            if calendar.isleap(int(self.year)):
                total_days = 366
            else:
                total_days = 365
        else:
            # 使用预定义的天数
            total_days = self.time_ranges[self.time_range]['days']

        # 计算平均值 - 使用总天数作为分母
        brews_per_day = self.total_records / total_days if total_days > 0 else 0

        # 计算半斤豆子可用天数
        daily_consumption = total_dose / total_days if total_days > 0 else 0
        days_250g_lasts = 250 / daily_consumption if daily_consumption > 0 else 0

        # 更新统计数据
        self.stats.update({
            'total_records': self.total_records,
            'total_dose': round(total_dose, 1),
            'avg_dose': round(avg_dose, 1),
            'days_250g_lasts': round(days_250g_lasts, 1),
            'brews_per_day': round(brews_per_day, 2),
            'active_days': active_days,
            'total_days': total_days,
            'active_rate': round(active_days / total_days * 100, 1) if total_days > 0 else 0,
        })

    def calculate_equipment_stats(self):
        """计算设备使用统计"""
        records = self.get_records_queryset()

        most_used_method = records.values(
            'brewing_equipment__brew_method'
        ).annotate(
            count=Count('id')
        ).order_by('-count').first()

        most_used_brewer = records.values(
            'brewing_equipment__name',
            'brewing_equipment__brand'
        ).annotate(
            count=Count('id')
        ).order_by('-count').first()

        most_used_grinder = records.values(
            'grinding_equipment__name',
            'grinding_equipment__brand'
        ).annotate(
            count=Count('id')
        ).order_by('-count').first()

        self.stats.update({
            'most_used_method': dict(Equipment.BREW_METHODS).get(
                most_used_method['brewing_equipment__brew_method']
            ) if most_used_method else None,
            'most_used_brewer': most_used_brewer['brewing_equipment__name'] if most_used_brewer else None,
            'most_used_brewer_brand': most_used_brewer['brewing_equipment__brand'] if most_used_brewer else None,
            'most_used_grinder': most_used_grinder['grinding_equipment__name'] if most_used_grinder else None,
            'most_used_grinder_brand': most_used_grinder['grinding_equipment__brand'] if most_used_grinder else None,
        })

    def calculate_time_patterns(self):
        """计算时间模式统计"""
        records = self.get_records_queryset()

        # 计算实际有记录的天数
        active_days = records.annotate(
            date=TruncDate('created_at')
        ).values('date').distinct().count()

        # 工作日vs周末统计
        weekday_stats = records.annotate(
            weekday=ExtractWeekDay('created_at')
        ).values('weekday').annotate(count=Count('id'))

        weekday_counts = {stat['weekday']: stat['count'] for stat in weekday_stats}

        # 计算工作日和周末的平均值
        if self.time_ranges is None:  # 年鉴视图
            # 计算这一年中工作日和周末的实际天数
            year = int(self.year)
            start = date(year, 1, 1)
            end = date(year, 12, 31)
            days = (end - start).days + 1

            weekend_days = sum(1 for i in range(days) if (start + timedelta(i)).weekday() >= 5)
            weekday_days = days - weekend_days
        else:
            # 使用预定义时间范围的天数
            days = self.time_ranges[self.time_range]['days']
            weekend_days = len([d for d in range(days) if (self.start_date.date() + timedelta(d)).weekday() >= 5])
            weekday_days = days - weekend_days

        # 计算工作日和周末的记录总数
        weekend_records = sum(weekday_counts.get(d, 0) for d in [1, 7])  # 周六(7)和周日(1)
        weekday_records = sum(weekday_counts.get(d, 0) for d in range(2, 7))  # 周一(2)到周五(6)

        # 计算平均值
        weekend_avg = weekend_records / weekend_days if weekend_days > 0 else 0
        weekday_avg = weekday_records / weekday_days if weekday_days > 0 else 0

        # 确定高峰时段
        peak_period = '周末' if weekend_avg > weekday_avg else '工作日'

        # 计算每天的时段分布
        hour_stats = records.annotate(
            hour=ExtractHour('created_at')
        ).values('hour').annotate(count=Count('id'))

        # 找出最常冲煮的时间
        if hour_stats:
            peak_hour = max(hour_stats, key=lambda x: x['count'])['hour']
            # 将小时数格式化为时间字符串
            peak_time = f"{peak_hour:02d}:00"
        else:
            peak_time = None

        # 找出最常冲煮的星期几
        if weekday_stats:
            peak_weekday = max(weekday_stats, key=lambda x: x['count'])['weekday']
            weekday_map = {
                1: '周日', 2: '周一', 3: '周二', 4: '周三',
                5: '周四', 6: '周五', 7: '周六'
            }
            peak_weekday_name = weekday_map.get(peak_weekday)
        else:
            peak_weekday_name = None

        self.stats.update({
            'active_days': active_days,
            'total_days': self.total_days,
            'brews_per_active_day': round(self.total_records / active_days, 1) if active_days > 0 else 0,
            'peak_period': peak_period,
            'peak_weekday': peak_weekday_name,
            'peak_time': peak_time,  # 现在是格式化的时间字符串
        })

    def calculate_bean_stats(self):
        """计算咖啡豆相关统计"""
        records = self.get_records_queryset()

        # 品尝过的豆子数量
        tasted_beans_count = records.values('coffee_bean').distinct().count()

        # 最常回购的豆子
        most_repurchased_bean = None
        most_repurchased_bean_roaster = None
        beans_with_occurrences = CoffeeBean.objects.filter(
            user=self.user,
            occurrences__isnull=False
        ).annotate(
            occurrences_count=Count('occurrences')
        ).order_by('-occurrences_count').first()

        if beans_with_occurrences and beans_with_occurrences.occurrences_count > 0:
            most_repurchased_bean = beans_with_occurrences.name
            most_repurchased_bean_roaster = beans_with_occurrences.roaster

        # 咖啡豆总支出
        bean_costs = CoffeeBean.objects.filter(
            user=self.user,
            created_at__gte=self.start_date,
            created_at__lte=self.end_date,
            purchase_price__isnull=False
        ).aggregate(
            total=Sum('purchase_price')
        )['total'] or Decimal('0')

        occurrence_costs = BeanOccurrence.objects.filter(
            coffee_bean__user=self.user,
            created_at__gte=self.start_date,
            created_at__lte=self.end_date,
            purchase_price__isnull=False
        ).aggregate(
            total=Sum('purchase_price')
        )['total'] or Decimal('0')

        total_bean_costs = bean_costs + occurrence_costs

        # 设备总支出
        equipment_costs = Equipment.objects.filter(
            user=self.user,
            created_at__gte=self.start_date,
            created_at__lte=self.end_date,
            purchase_price__isnull=False
        ).aggregate(
            total=Sum('purchase_price')
        )['total'] or Decimal('0')

        # 计算最常光顾的豆商
        # 1. 从主表获取豆商购买记录
        roaster_purchases = CoffeeBean.objects.filter(
            user=self.user,
            created_at__gte=self.start_date,
            created_at__lte=self.end_date,
            roaster__isnull=False
        ).exclude(roaster='').values('roaster').annotate(
            purchase_count=Count('id'),
            total_amount=Sum('purchase_price', filter=Q(purchase_price__isnull=False))
        )

        # 2. 从回购记录获取豆商购买记录
        occurrence_purchases = BeanOccurrence.objects.filter(
            coffee_bean__user=self.user,
            created_at__gte=self.start_date,
            created_at__lte=self.end_date,
            coffee_bean__roaster__isnull=False
        ).exclude(
            coffee_bean__roaster=''
        ).values('coffee_bean__roaster').annotate(
            purchase_count=Count('id'),
            total_amount=Sum('purchase_price', filter=Q(purchase_price__isnull=False))
        )

        # 3. 合并两个查询结果
        roaster_stats = {}

        # 处理主表数据
        for purchase in roaster_purchases:
            roaster = purchase['roaster']
            roaster_stats[roaster] = {
                'count': purchase['purchase_count'],
                'amount': purchase['total_amount'] or Decimal('0')
            }

        # 处理回购记录数据
        for purchase in occurrence_purchases:
            roaster = purchase['coffee_bean__roaster']
            if roaster in roaster_stats:
                roaster_stats[roaster]['count'] += purchase['purchase_count']
                roaster_stats[roaster]['amount'] += (purchase['total_amount'] or Decimal('0'))
            else:
                roaster_stats[roaster] = {
                    'count': purchase['purchase_count'],
                    'amount': purchase['total_amount'] or Decimal('0')
                }

        # 4. 找出购买次数最多的豆商
        most_visited_roaster = None
        most_visited_count = 0
        most_visited_amount = Decimal('0')

        for roaster, stats in roaster_stats.items():
            if stats['count'] > most_visited_count:
                most_visited_roaster = roaster
                most_visited_count = stats['count']
                most_visited_amount = stats['amount']

        # 计算冲煮用豆成本
        total_brewing_cost = Decimal('0')
        total_valid_records = 0

        # 对每次冲煮记录单独计算成本
        for record in records.select_related('coffee_bean'):
            if not record.dose_weight:
                continue

            try:
                bean = record.coffee_bean
                brew_time = record.created_at

                # 获取所有这个豆子的购买记录（包括首次和回购），按时间排序
                bean_purchases = []

                # 添加初始购买记录 - 使用初始购买价格和重量
                if bean.initial_purchase_price is not None and bean.initial_bag_weight is not None and bean.initial_bag_weight > 0:
                    initial_time = bean.initial_created_at if bean.initial_created_at else bean.created_at
                    bean_purchases.append((
                        initial_time,
                        bean.initial_purchase_price,  # 使用初始购买价格
                        bean.initial_bag_weight       # 使用初始包装重量
                    ))
                elif bean.purchase_price is not None and bean.bag_weight is not None and bean.bag_weight > 0:
                    # 如果没有初始记录，则使用当前记录
                    bean_purchases.append((bean.created_at, bean.purchase_price, bean.bag_weight))

                # 获取所有回购记录
                occurrences = BeanOccurrence.objects.filter(
                    coffee_bean_id=bean.id,
                    purchase_price__isnull=False,
                    bag_weight__isnull=False,
                    bag_weight__gt=0
                ).order_by('created_at').values('created_at', 'purchase_price', 'bag_weight')

                for occ in occurrences:
                    bean_purchases.append((
                        occ['created_at'],
                        occ['purchase_price'],
                        occ['bag_weight']
                    ))

                # 按时间排序所有购买记录
                bean_purchases.sort(key=lambda x: x[0])

                # 找到冲煮时使用的那批豆子的价格
                price = None
                weight = None
                purchase_time = None

                # 遍历所有购买记录，找到萃取时间之前的最后一次购买
                for curr_time, curr_price, curr_weight in bean_purchases:
                    if curr_time > brew_time:  # 如果购买时间晚于萃取时间，停止查找
                        break
                    # 更新价格（这样会一直更新到萃取时间之前的最后一次购买）
                    purchase_time = curr_time
                    price = Decimal(str(curr_price))
                    weight = Decimal(str(curr_weight))

                if not (price and weight and weight > 0):
                    continue

                # 计算单价和成本
                unit_price = (price / weight).quantize(Decimal('.0001'), rounding=decimal.ROUND_HALF_UP)
                dose = Decimal(str(record.dose_weight))
                brewing_cost = (unit_price * dose).quantize(Decimal('.01'), rounding=decimal.ROUND_HALF_UP)

                total_brewing_cost += brewing_cost
                total_valid_records += 1

            except (CoffeeBean.DoesNotExist, decimal.InvalidOperation) as e:
                continue

        # 计算平均每杯成本
        avg_cost_per_cup = Decimal('0')
        if total_valid_records > 0:
            avg_cost_per_cup = (total_brewing_cost / total_valid_records).quantize(Decimal('.01'), rounding=decimal.ROUND_HALF_UP)

        logger.debug(
            f"Final brewing cost stats:\n"
            f"- Total cost: {total_brewing_cost}\n"
            f"- Valid records: {total_valid_records}\n"
            f"- Average cost per cup: {avg_cost_per_cup}"
        )

        self.stats.update({
            'tasted_beans_count': tasted_beans_count,
            'most_repurchased_bean': most_repurchased_bean,
            'most_repurchased_bean_roaster': most_repurchased_bean_roaster,
            'total_bean_costs': total_bean_costs,  # 已经是两位小数了，不需要再次round
            'equipment_costs': equipment_costs,
            'most_visited_roaster': most_visited_roaster,
            'most_visited_roaster_amount': round(most_visited_amount, 2) if most_visited_amount else 0,
            'total_brewing_cost': total_brewing_cost,  # 已经是两位小数了，不需要再次round
            'avg_cost_per_cup': avg_cost_per_cup,     # 已经是两位小数了，不需要再次round
            'brewing_cost_records_count': total_valid_records,
        })

    def calculate_streak_stats(self):
        """计算连续打卡相关统计"""
        # 计算最长连续打卡天数
        all_records = BrewingRecord.objects.filter(
            user=self.user
        ).order_by('created_at').values(
            date=TruncDate('created_at')
        ).distinct()

        max_streak = 0
        current_streak = 1

        if all_records:
            dates = [record['date'] for record in all_records]
            for i in range(1, len(dates)):
                # 如果是同一天的记录，跳过
                if (dates[i] - dates[i-1]).days == 0:
                    continue
                # 如果是连续的天数
                elif (dates[i] - dates[i-1]).days == 1:
                    current_streak += 1
                # 如果不连续，重置计数器
                else:
                    max_streak = max(max_streak, current_streak)
                    current_streak = 1
            max_streak = max(max_streak, current_streak)

        days_since_registration = (timezone.now().date() - self.user.date_joined.date()).days

        self.stats.update({
            'max_streak': max_streak,
            'days_since_registration': days_since_registration,
        })

    def calculate_flavor_stats(self):
        """计算口味偏好统计"""
        records = self.get_records_queryset()

        # 最爱的豆子（评分最高）
        favorite_bean = records.values(
            'coffee_bean__name',
            'coffee_bean__roaster',  # 添加 roaster
            'coffee_bean__id'
        ).annotate(
            count=Count('id'),
            avg_rating=Avg('rating_level')
        ).filter(
            avg_rating__isnull=False
        ).order_by('-avg_rating', '-count').first()

        # 最常用的豆子（使用次数最多）
        most_used_bean = records.values(
            'coffee_bean__name',
            'coffee_bean__roaster',  # 添加 roaster
            'coffee_bean__id'
        ).annotate(
            count=Count('id'),
            avg_rating=Avg('rating_level')
        ).order_by('-count', '-avg_rating').first()

        favorite_bean_rating = None
        if favorite_bean:
            favorite_bean_rating = round(favorite_bean['avg_rating'], 1) if favorite_bean['avg_rating'] else None

        most_used_bean_rating = None
        if most_used_bean:
            most_used_bean_rating = round(most_used_bean['avg_rating'], 1) if most_used_bean['avg_rating'] else None

        # 最常品的产地
        # 创建一个子查询来获取拼配组件的产地
        blend_origins = records.filter(
            coffee_bean__type='BLEND'
        ).values(
            'coffee_bean__blend_components__origin'
        ).exclude(
            coffee_bean__blend_components__origin=''
        ).exclude(
            coffee_bean__blend_components__origin__isnull=True
        ).annotate(
            count=Count('id')
        )

        # 获取单品咖啡的产地
        single_origins = records.filter(
            coffee_bean__type='SINGLE'
        ).values(
            'coffee_bean__origin'
        ).exclude(
            coffee_bean__origin=''
        ).exclude(
            coffee_bean__origin__isnull=True
        ).annotate(
            count=Count('id')
        )

        # 合并两个查询结果
        all_origins = {}
        for origin in single_origins:
            origin_name = origin['coffee_bean__origin']
            if origin_name:
                all_origins[origin_name] = all_origins.get(origin_name, 0) + origin['count']

        for origin in blend_origins:
            origin_name = origin['coffee_bean__blend_components__origin']
            if origin_name:
                all_origins[origin_name] = all_origins.get(origin_name, 0) + origin['count']

        # 找出最常品的产地
        most_common_origin = None
        max_count = 0
        for origin, count in all_origins.items():
            if count > max_count:
                most_common_origin = origin
                max_count = count

        # 最常品的风味标签
        most_common_flavors = records.values(
            'coffee_bean__flavor_tags__name'
        ).exclude(
            coffee_bean__flavor_tags__name__isnull=True
        ).annotate(
            count=Count('coffee_bean__flavor_tags__name')
        ).order_by('-count')[:4]

        # 最爱的配方（评分最高）
        favorite_recipe = records.exclude(
            recipe_name__isnull=True
        ).exclude(
            recipe_name=''
        ).values(
            'recipe_name'
        ).annotate(
            count=Count('id'),
            avg_rating=Avg('rating_level')
        ).filter(
            avg_rating__isnull=False
        ).order_by('-avg_rating', '-count').first()

        # 最常用的配方（使用次数最多）
        most_used_recipe = records.exclude(
            recipe_name__isnull=True
        ).exclude(
            recipe_name=''
        ).values(
            'recipe_name'
        ).annotate(
            count=Count('id'),
            avg_rating=Avg('rating_level')
        ).order_by('-count', '-avg_rating').first()

        # 最常品的品种
        # 创建一个子查询来获取拼配组件的品种
        blend_varieties = records.filter(
            coffee_bean__type='BLEND'
        ).values(
            'coffee_bean__blend_components__variety'
        ).exclude(
            coffee_bean__blend_components__variety=''
        ).exclude(
            coffee_bean__blend_components__variety__isnull=True
        ).annotate(
            count=Count('id')
        )

        # 获取单品咖啡的品种
        single_varieties = records.filter(
            coffee_bean__type='SINGLE'
        ).values(
            'coffee_bean__variety'
        ).exclude(
            coffee_bean__variety=''
        ).exclude(
            coffee_bean__variety__isnull=True
        ).annotate(
            count=Count('id')
        )

        # 合并两个查询结果
        all_varieties = {}
        for variety in single_varieties:
            variety_name = variety['coffee_bean__variety']
            if variety_name:
                all_varieties[variety_name] = all_varieties.get(variety_name, 0) + variety['count']

        for variety in blend_varieties:
            variety_name = variety['coffee_bean__blend_components__variety']
            if variety_name:
                all_varieties[variety_name] = all_varieties.get(variety_name, 0) + variety['count']

        # 找出最常品的品种
        most_common_variety = None
        max_count = 0
        for variety, count in all_varieties.items():
            if count > max_count:
                most_common_variety = variety
                max_count = count

        self.stats.update({
            'favorite_bean_name': favorite_bean['coffee_bean__name'] if favorite_bean else None,
            'favorite_bean_roaster': favorite_bean['coffee_bean__roaster'] if favorite_bean else None,  # 添加 roaster
            'favorite_bean_rating': favorite_bean_rating,
            'favorite_bean_count': favorite_bean['count'] if favorite_bean else None,
            'most_used_bean_name': most_used_bean['coffee_bean__name'] if most_used_bean else None,
            'most_used_bean_roaster': most_used_bean['coffee_bean__roaster'] if most_used_bean else None,  # 添加 roaster
            'most_used_bean_rating': most_used_bean_rating,
            'most_used_bean_count': most_used_bean['count'] if most_used_bean else None,
            'most_common_origin': most_common_origin,
            'most_common_flavors': [item['coffee_bean__flavor_tags__name'] for item in most_common_flavors if item['coffee_bean__flavor_tags__name']],
            'favorite_recipe_name': favorite_recipe['recipe_name'] if favorite_recipe else None,
            'favorite_recipe_rating': round(favorite_recipe['avg_rating'], 1) if favorite_recipe and favorite_recipe['avg_rating'] else None,
            'favorite_recipe_count': favorite_recipe['count'] if favorite_recipe else None,
            'most_used_recipe_name': most_used_recipe['recipe_name'] if most_used_recipe else None,
            'most_used_recipe_count': most_used_recipe['count'] if most_used_recipe else None,
            'most_used_recipe_rating': round(most_used_recipe['avg_rating'], 1) if most_used_recipe and most_used_recipe['avg_rating'] else None,
            'most_common_variety': most_common_variety,
        })

    @staticmethod
    def get_period(hour):
        """获取时段描述"""
        if 5 <= hour <= 11:
            return '早上'
        elif 12 <= hour <= 17:
            return '下午'
        else:
            return '晚上'

    def get_empty_stats(self):
        """返回空统计数据"""
        return {
            'brews_per_day': 0,
            'avg_dose': 0,
            'days_250g_lasts': 0,
            'total_records': 0,
            'total_dose': 0,
            'most_used_method': None,
            'most_used_brewer': None,
            'most_used_brewer_brand': None,
            'most_used_grinder': None,
            'most_used_grinder_brand': None,
            'peak_period': None,
            'peak_weekday': None,
            'peak_time': None,
            'active_days': 0,
            'brews_per_active_day': 0,
            'tasted_beans_count': 0,
            'most_repurchased_bean': None,
            'most_repurchased_bean_roaster': None,
            'total_bean_costs': 0,
            'equipment_costs': 0,
            'max_streak': 0,
            'days_since_registration': (timezone.now().date() - self.user.date_joined.date()).days,
            'favorite_bean_name': None,
            'favorite_bean_rating': None,
            'favorite_bean_count': None,
            'most_used_bean_name': None,
            'most_used_bean_rating': None,
            'most_used_bean_count': None,
            'most_common_origin': None,
            'most_common_flavors': [],
            'favorite_recipe_name': None,
            'favorite_recipe_rating': None,
            'favorite_recipe_count': None,
            'most_used_recipe_name': None,
            'most_used_recipe_count': None,
            'most_used_recipe_rating': None,
            'most_common_variety': None,
            'most_visited_roaster': None,
            'most_visited_roaster_amount': 0,
            'total_brewing_cost': 0,
            'avg_cost_per_cup': 0,
            'brewing_cost_records_count': 0,
            'total_all_time_records': 0,
        }

    def calculate_all_stats(self):
        """计算所有统计数据"""
        self.calculate_basic_stats()

        # 计算用户所有时间的冲煮记录总数（无论当前选择的时间范围）
        total_all_time_records = BrewingRecord.objects.filter(
            user=self.user
        ).count()
        self.stats['total_all_time_records'] = total_all_time_records

        if self.total_records > 0:
            self.calculate_equipment_stats()
            self.calculate_time_patterns()
            self.calculate_bean_stats()
            self.calculate_streak_stats()
            self.calculate_flavor_stats()

        # 添加iOS客户端所需的字段映射
        ios_response = self.stats.copy()

        # 基础字段映射
        ios_response['total_brews'] = ios_response.get('total_records', 0)
        ios_response['active_days'] = ios_response.get('active_days', 0)
        ios_response['average_brews_per_day'] = ios_response.get('brews_per_day', 0)
        ios_response['average_rating'] = ios_response.get('avg_rating', 0)

        # 设备统计
        equipment_stats = []
        if 'equipment_usage' in self.stats and self.stats['equipment_usage']:
            for equip in self.stats['equipment_usage']:
                if equip['count'] > 0:
                    equipment_stats.append({
                        'name': equip['name'],
                        'count': equip['count'],
                        'percentage': equip['percentage']
                    })
        ios_response['equipment_stats'] = equipment_stats

        # 咖啡豆统计
        bean_stats = []
        if 'bean_usage' in self.stats and self.stats['bean_usage']:
            for bean in self.stats['bean_usage']:
                if bean['count'] > 0:
                    bean_stats.append({
                        'name': bean['name'],
                        'count': bean['count'],
                        'percentage': bean['percentage']
                    })
        ios_response['bean_stats'] = bean_stats

        # 月度冲煮统计
        monthly_brews = []
        if 'monthly_data' in self.stats and self.stats['monthly_data']:
            for month_data in self.stats['monthly_data']:
                monthly_brews.append({
                    'month': month_data['month'],
                    'count': month_data['count']
                })
        ios_response['monthly_brews'] = monthly_brews

        # 评分分布
        rating_distribution = []
        if 'rating_distribution' in self.stats and self.stats['rating_distribution']:
            for rating, count in self.stats['rating_distribution'].items():
                try:
                    rating_value = int(rating)
                    rating_distribution.append({
                        'rating': rating_value,
                        'count': count
                    })
                except (ValueError, TypeError):
                    continue
        ios_response['rating_distribution'] = rating_distribution

        return ios_response

def get_trend_cache_key(user_id, record_id):
    return f'trend_data_user_{user_id}_record_{record_id}'

@receiver([post_save, post_delete], sender=BrewingRecord)
def clear_trend_cache(sender, instance, **kwargs):
    """当记录被保存或删除时清除相关缓存"""
    user_id = instance.user.id
    record_id = instance.id

    # 清除当前记录的趋势数据缓存
    cache_key = get_trend_cache_key(user_id, record_id)
    cache.delete(cache_key)

    # 清除同一用户其他记录的趋势数据缓存
    # 因为一条记录的变化会影响其他记录的趋势图
    user_records = BrewingRecord.objects.filter(user_id=user_id)
    for record in user_records:
        cache_key = get_trend_cache_key(user_id, record.id)
        cache.delete(cache_key)

@receiver([post_save, post_delete], sender='my.CoffeeQuote')
def clear_quotes_cache(sender, instance, **kwargs):
    """当箴言被保存或删除时清除缓存"""
    CoffeeQuote.clear_cache()

class RecipeTag(models.Model):
    name = models.CharField(max_length=50, verbose_name='标签名')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('name', 'user')
        verbose_name = '配方标签'
        verbose_name_plural = '配方标签'

    def __str__(self):
        return self.name

class RecipeThread(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    recipe_name = models.CharField(max_length=50, verbose_name='配方名称')
    tags = models.ManyToManyField(RecipeTag, related_name='recipe_threads', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    use_count = models.IntegerField(default=0, verbose_name='使用次数')
    last_used_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ('user', 'recipe_name')
        verbose_name = '配方主题'
        verbose_name_plural = '配方主题'
        ordering = ['-last_used_at']

    def __str__(self):
        return self.recipe_name

    def get_latest_record(self):
        """获取最近一次使用该配方的记录"""
        return BrewingRecord.objects.filter(
            user=self.user,
            recipe_name=self.recipe_name
        ).order_by('-created_at').first()

    def get_latest_parameters(self):
        """获取最近10条记录中使用频率最高的参数，但咖啡豆使用最近一次使用的"""
        # 获取该配方的最近10条记录
        records = BrewingRecord.objects.filter(
            user=self.user,
            recipe_name=self.recipe_name
        ).order_by('-created_at')[:10]

        if not records:
            return None

        # 获取用户最近一次使用的咖啡豆（不限配方）
        latest_bean = BrewingRecord.objects.filter(
            user=self.user,
            coffee_bean__isnull=False
        ).order_by('-created_at').values_list('coffee_bean_id', flat=True).first()

        # 统计各参数的使用频率
        params = {
            'brewing_equipment': [],
            'grinding_equipment': [],
            'coffee_bean': [],  # 虽然不再使用频率统计，但保留该字段以保持代码结构
            'grind_size': [],
            'dose_weight': [],
            'yield_weight': [],
            'water_temperature': [],
            'brewing_time': [],
            'gadgets': [],
            'gadget_kit': [], # 添加小工具组合统计
            'water_quality': [],
            'rating_level': [],  # 添加评分统计
            'steps': []  # 添加步骤统计
            # 移除 room_temperature 和 room_humidity
        }

        for record in records:
            params['brewing_equipment'].append(record.brewing_equipment_id)
            params['grinding_equipment'].append(record.grinding_equipment_id)
            params['grind_size'].append(record.grind_size)
            params['dose_weight'].append(record.dose_weight)
            params['yield_weight'].append(record.yield_weight)
            params['water_temperature'].append(record.water_temperature)
            params['brewing_time'].append(record.brewing_time)
            params['water_quality'].append(record.water_quality)
            params['rating_level'].append(record.rating_level)  # 添加评分
            params['steps'].append(record.steps)  # 添加步骤

            # 添加小工具组合id
            params['gadget_kit'].append(record.gadget_kit_id)

            # 添加单个小工具id
            for gadget in record.gadgets.all():
                params['gadgets'].append(gadget.id)

        # 获取每个参数的最常用值
        most_common = {}
        for key, values in params.items():
            if key == 'coffee_bean':
                most_common[key] = latest_bean  # 直接使用最近使用的咖啡豆
                continue

            if not values:
                most_common[key] = None if key != 'rating_level' else 6  # 评分默认值为6
                continue

            # 移除None值
            values = [v for v in values if v is not None]
            if not values:
                most_common[key] = None if key != 'rating_level' else 6  # 评分默认值为6
                continue

            # 对于gadgets，需要特殊处理
            if key == 'gadgets':
                from collections import Counter
                counter = Counter(values)
                most_common[key] = [item[0] for item in counter.most_common()]
            # 对于gadget_kit，取最常用的一个
            elif key == 'gadget_kit':
                from collections import Counter
                counter = Counter(values)
                most_common_kits = counter.most_common()
                most_common[key] = most_common_kits[0][0] if most_common_kits else None
            # 对于steps，取最后一次使用的步骤
            elif key == 'steps':
                most_common[key] = values[0] if values else None
            else:
                from collections import Counter
                counter = Counter(values)
                most_common[key] = counter.most_common(1)[0][0]

        return most_common

    def quick_brew(self):
        """快速创建一条冲煮记录"""
        params = self.get_latest_parameters()
        if not params:
            return None

        # 获取最近一条有步骤的记录
        latest_record_with_steps = BrewingRecord.objects.filter(
            user=self.user,
            recipe_name=self.recipe_name,
            steps__isnull=False  # 确保steps不为null
        ).exclude(
            steps=[]  # 排除空列表
        ).order_by('-created_at').first()

        # 复制最近一条有步骤的记录的步骤，如果没有则使用空列表
        steps = latest_record_with_steps.steps if latest_record_with_steps else []

        record = BrewingRecord(
            user=self.user,
            recipe_name=self.recipe_name,
            brewing_equipment_id=params['brewing_equipment'],
            grinding_equipment_id=params['grinding_equipment'],
            coffee_bean_id=params['coffee_bean'],
            grind_size=params['grind_size'],
            dose_weight=params['dose_weight'],
            yield_weight=params['yield_weight'],
            water_temperature=params['water_temperature'],
            brewing_time=params['brewing_time'],
            water_quality=params['water_quality'],
            rating_level=params.get('rating_level', 6),  # 使用默认值6
            steps=steps,  # 使用有效的步骤数据
            # 不设置 room_temperature 和 room_humidity，让它们保持默认值
            gadget_kit_id=params['gadget_kit'], # 设置小工具组合
        )
        record.save()

        # 按优先级设置小工具：优先使用小工具组合，其次使用单个小工具
        if params['gadget_kit']:
            # 如果有小工具组合，则使用组合（组合会自动关联其包含的小工具）
            pass  # gadget_kit_id 已在创建记录时设置
        elif params['gadgets']:
            # 如果没有小工具组合但有单个小工具，则设置单个小工具
            record.gadgets.set(params['gadgets'])

        # 更新咖啡豆库存
        if record.coffee_bean and not record.coffee_bean.is_deleted and not record.coffee_bean.is_archived:
            try:
                # 获取最新状态的咖啡豆
                coffee_bean = CoffeeBean.objects.get(id=record.coffee_bean.id)

                # 如果有库存记录，则减少库存
                if coffee_bean.bag_remain is not None:
                    # 计算新的库存值
                    new_bag_remain = coffee_bean.bag_remain - record.dose_weight

                    # 确保不小于0
                    if new_bag_remain < 0:
                        new_bag_remain = 0

                    # 更新库存
                    coffee_bean.bag_remain = new_bag_remain
                    coffee_bean.save()

                    # 清除咖啡豆相关缓存
                    try:
                        from my.cache_utils import invalidate_bean_cache, invalidate_hindsight_cache, invalidate_record_cache

                        # 清除咖啡豆缓存
                        invalidate_bean_cache(self.user.id, coffee_bean.id)

                        # 清除统计数据缓存
                        invalidate_hindsight_cache(self.user.id)

                        # 清除记录缓存
                        invalidate_record_cache(self.user.id, record.id)

                        # 尝试清除iOS端相关缓存
                        try:
                            from iosapp.cache_utils import invalidate_ios_cache

                            # 清除冲煮记录列表缓存
                            invalidate_ios_cache('brewlog_list', self.user.id)
                            invalidate_ios_cache('brewlog_statistics', self.user.id)
                            invalidate_ios_cache('filtered_brewlog_list', self.user.id)

                            # 清除咖啡豆相关缓存
                            invalidate_ios_cache('bean_list', self.user.id)
                            invalidate_ios_cache('bean_calendar_data', self.user.id)

                            # 清除统计数据缓存
                            invalidate_ios_cache('hindsight_data', self.user.id)
                            invalidate_ios_cache('heatmap_data', self.user.id)
                        except ImportError:
                            # 如果无法导入iOS缓存模块，记录但不中断操作
                            import logging
                            logger = logging.getLogger(__name__)
                            logger.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {self.user.id}")
                    except ImportError:
                        # 如果无法导入缓存模块，记录但不中断操作
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.warning(f"无法清除缓存：cache_utils模块不可用 - 用户ID: {self.user.id}")

                    # 记录日志
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info(f"快速冲煮记录 ID={record.id}：已更新咖啡豆 {coffee_bean.id} 的库存，减少 {record.dose_weight}g，新库存为 {new_bag_remain}g")
            except Exception as e:
                # 库存更新失败不应影响记录创建
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"更新咖啡豆库存时出错: {str(e)}")

        # 更新使用统计
        self.use_count += 1
        self.last_used_at = timezone.now()
        self.save()

        return record

    @classmethod
    def update_recipe_threads(cls, user):
        """更新用户的配方主题数据"""
        # 获取所有有配方名称的记录
        records = BrewingRecord.objects.filter(
            user=user,
            recipe_name__isnull=False
        ).exclude(recipe_name='').values('recipe_name').annotate(
            use_count=Count('id'),
            last_used=Max('created_at')
        )

        # 更新或创建配方主题
        for record in records:
            thread, created = cls.objects.update_or_create(
                user=user,
                recipe_name=record['recipe_name'],
                defaults={
                    'use_count': record['use_count'],
                    'last_used_at': record['last_used']
                }
            )

        # 删除不再使用的配方主题
        cls.objects.filter(user=user).exclude(
            recipe_name__in=[r['recipe_name'] for r in records]
        ).delete()

    @classmethod
    def get_user_recipes(cls, user):
        """获取用户的所有配方主题（带缓存）"""
        cache_key = f'user_recipes_{user.id}'
        recipes = cache.get(cache_key)

        if recipes is None:
            recipes = list(cls.objects.filter(user=user).prefetch_related('tags'))
            cache.set(cache_key, recipes, CACHE_TIMEOUT['MEDIUM'])

        return recipes

def clear_recipe_cache(sender, instance, **kwargs):
    """当记录被保存或删除时清除配方缓存"""
    if instance.recipe_name:
        # 更新配方主题
        RecipeThread.update_recipe_threads(instance.user)
        # 清除缓存
        cache_key = f'user_recipes_{instance.user.id}'
        cache.delete(cache_key)

# 注册信号
post_save.connect(clear_recipe_cache, sender=BrewingRecord)
post_delete.connect(clear_recipe_cache, sender=BrewingRecord)

# 监听冲煮记录的保存和删除事件，清除相关设备的缓存
@receiver([post_save, post_delete], sender='my.BrewingRecord')
def clear_equipment_cache(sender, instance, **kwargs):
    """当冲煮记录发生变化时，清除相关设备的缓存"""
    # 获取所有相关设备ID
    equipment_ids = []
    user_id = instance.user.id

    # 冲煮设备
    if instance.brewing_equipment:
        equipment_ids.append(instance.brewing_equipment.id)

    # 研磨设备
    if instance.grinding_equipment:
        equipment_ids.append(instance.grinding_equipment.id)

    # 小工具组合
    if instance.gadget_kit:
        equipment_ids.append(instance.gadget_kit.id)

    # 小工具（多对多关系）
    if hasattr(instance, 'gadgets'):
        for gadget in instance.gadgets.all():
            equipment_ids.append(gadget.id)

    # 清除每个设备的缓存
    for equipment_id in equipment_ids:
        cache.delete(get_equipment_usage_cache_key(equipment_id))
        cache.delete(get_equipment_last_used_cache_key(equipment_id))

    # 使用全局缓存清除函数
    try:
        from my.cache_utils import invalidate_equipment_cache, invalidate_record_cache

        # 清除设备缓存
        invalidate_equipment_cache(user_id)

        # 清除冲煮记录缓存
        invalidate_record_cache(user_id, instance.id)

    except (ImportError, AttributeError) as e:
        # 缓存清除失败不应影响正常操作
        logger.error(f"清除设备缓存时出错: {str(e)}")

# 监听设备的保存和删除事件，清除相关缓存
@receiver([post_save, post_delete], sender='my.Equipment')
def clear_equipment_model_cache(sender, instance, **kwargs):
    """当设备模型发生变化时，清除相关缓存"""
    user_id = instance.user.id
    equipment_id = instance.id

    # 清除设备缓存键
    cache.delete(get_equipment_usage_cache_key(equipment_id))
    cache.delete(get_equipment_last_used_cache_key(equipment_id))

    # 使用全局缓存清除函数
    try:
        from my.cache_utils import invalidate_equipment_cache

        # 清除设备相关缓存
        invalidate_equipment_cache(user_id, equipment_id)

        # 尝试清除iOS端相关缓存
        try:
            from iosapp.cache_utils import invalidate_ios_cache

            # 清除设备列表缓存
            invalidate_ios_cache('equipment_list', user_id)

            # 清除可能受影响的统计数据缓存
            invalidate_ios_cache('brewlog_statistics', user_id)
            invalidate_ios_cache('hindsight_data', user_id)
            invalidate_ios_cache('heatmap_data', user_id)
            invalidate_ios_cache('brewlog_list', user_id)
            invalidate_ios_cache('filtered_brewlog_list', user_id)
        except ImportError:
            # 如果无法导入iOS缓存模块，记录但不中断操作
            logger.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {user_id}, 设备ID: {equipment_id}")

    except (ImportError, AttributeError) as e:
        # 缓存清除失败不应影响正常操作
        logger.error(f"清除设备模型缓存时出错: {str(e)}")