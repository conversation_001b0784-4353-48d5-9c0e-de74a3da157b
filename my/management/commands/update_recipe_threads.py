from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from my.models import RecipeThread

User = get_user_model()

class Command(BaseCommand):
    help = '更新所有用户的配方主题数据'

    def handle(self, *args, **options):
        users = User.objects.all()
        total_users = users.count()
        
        self.stdout.write(self.style.SUCCESS(f'开始更新 {total_users} 个用户的配方主题...'))
        
        for i, user in enumerate(users, 1):
            try:
                # 更新该用户的配方主题
                RecipeThread.update_recipe_threads(user)
                self.stdout.write(
                    self.style.SUCCESS(f'[{i}/{total_users}] 成功更新用户 {user} 的配方主题')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'[{i}/{total_users}] 更新用户 {user} 的配方主题时出错: {str(e)}')
                )
        
        self.stdout.write(self.style.SUCCESS('所有用户的配方主题更新完成！')) 