from wagtail.snippets.views.snippets import SnippetViewSet
from wagtail.snippets.models import register_snippet
from .models import CoffeeQuote

class CoffeeQuoteViewSet(SnippetViewSet):
    model = CoffeeQuote
    icon = "quote"  # 图标
    menu_label = '咖啡箴言'  # 显示在菜单中的名称
    menu_order = 200  # 菜单排序
    list_display = ('content', 'content_original', 'author', 'source')
    search_fields = ('content', 'content_original', 'author', 'source')

register_snippet(CoffeeQuoteViewSet) 