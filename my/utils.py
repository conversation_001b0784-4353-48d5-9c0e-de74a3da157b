from django.forms.utils import ErrorList
from django.utils.safestring import mark_safe

class DivErrorList(ErrorList):
    def __str__(self):
        return self.as_divs()

    def as_divs(self):
        if not self:
            return ""
        return mark_safe("""
            <div role="alert" class="alert alert-error">
                <span class="icon-[ph--warning-bold] text-lg"></span>
                <span>%s</span>
            </div>
         """ % ''.join(['<span>%s</span>' % e for e in self]))

def warm_hindsight_cache(user_id):
    """预热用户的统计数据缓存"""
    from django.test.client import RequestFactory
    from django.contrib.auth.models import User
    from .views import brewing_hindsight
    
    factory = RequestFactory()
    user = User.objects.get(id=user_id)
    
    # 为每个时间范围预热缓存
    for time_range in ['week', 'month', 'half_year', 'year']:
        request = factory.get(f'/my/brewlog/hindsight/?time_range={time_range}')
        request.user = user
        brewing_hindsight(request)