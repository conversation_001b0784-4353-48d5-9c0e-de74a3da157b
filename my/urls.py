# my/urls.py
from django.urls import path, re_path, include
from rest_framework.routers import DefaultRouter
from . import views

# ViewSet路由 - 已转移到wechat/urls.py
# router = DefaultRouter()
# router.register(r'brewlogs', views.BrewLogViewSet, basename='api-brewlog')
# router.register(r'equipment', views.EquipmentViewSet, basename='api-equipment')
# router.register(r'beans', views.BeanViewSet, basename='api-bean')

# API 路由 - 注意顺序很重要，更具体的路由要放在前面
api_urlpatterns = [
    # 小程序专用API路由 - 已转移到wechat/urls.py
    # ViewSet路由放在最后
    # path('', include(router.urls)),
]

urlpatterns = [
    # API 路由
    # path('api/', include(api_urlpatterns)),  # 已转移到wechat应用
    
    # 收藏相关
    path('favorite/toggle/<str:app_label>/<str:model>/<int:object_id>/', views.toggle_favorite, name='toggle_favorite'),
    path('favorite/add/<str:app_label>/<str:model>/<int:object_id>/', views.add_favorite, name='add_favorite'),
    path('favorite/remove/<str:app_label>/<str:model>/<int:object_id>/', views.remove_favorite, name='remove_favorite_by_type'),
    path('favorite/<int:favorite_id>/remove/', views.remove_favorite_by_id, name='remove_favorite'),
    path('fav/', views.my_favorites, name='my_favorites'),
    
    # 咖啡札记主页（使用正则表达式匹配带斜线和不带斜线的情况）
    re_path(r'^brewlog/?$', views.brewlog, name='brewlog'),
    # 咖啡札记主页（同时支持带斜线和不带斜线的访问）
    path('brewlog', views.brewlog, name='brewlog_no_slash'),  # 不带斜线
    path('brewlog/', views.brewlog, name='brewlog'),          # 带斜线
    ### 新手引导
    path('brewlog/onboarding/', views.onboarding, name='onboarding'),
    path('brewlog/onboarding/submit/', views.onboarding_submit, name='onboarding_submit'),
    ### 开发日志
    path('brewlog/whatsnew/', views.whats_new, name='whats_new'),
    
    ## 设备相关
    path('brewlog/equipment/', views.equipment_list, name='equipment_list'),
    path('brewlog/equipment/add/', views.add_equipment_page, name='add_equipment_page'),
    path('brewlog/equipment/<int:equipment_id>/edit/page/', views.edit_equipment_page, name='edit_equipment_page'),
    path('brewlog/equipment/<int:equipment_id>/delete/', views.delete_equipment, name='delete_equipment'),
    path('brewlog/equipment/<int:equipment_id>/archive/', views.archive_equipment, name='archive_equipment'),
    path('brewlog/equipment/<int:equipment_id>/unarchive/', views.unarchive_equipment, name='unarchive_equipment'),
    path('brewlog/equipment/<int:equipment_id>/toggle-favorite/', views.toggle_favorite_equipment, name='toggle_favorite_equipment'),
    path('brewlog/equipment/<int:equipment_id>/info/', views.get_equipment_info, name='get_equipment_info'),
    path('brewlog/equipment/<int:equipment_id>/', views.view_equipment, name='view_equipment'),
    path('brewlog/equipment/<int:equipment_id>/gadgets/', views.get_gadget_kit_components, name='get_gadget_kit_components'),
    
    ## 咖啡豆相关
    path('brewlog/bean/', views.bean_list, name='bean_list'),
    path('brewlog/bean/add/', views.add_bean_page, name='add_bean_page'),
    path('brewlog/bean/<int:bean_id>/edit/page/', views.edit_bean_page, name='edit_bean_page'),
    path('brewlog/bean/<int:bean_id>/delete/', views.delete_coffee_bean, name='delete_coffee_bean'),
    path('brewlog/bean/<int:bean_id>/archive/', views.archive_coffee_bean, name='archive_bean'),
    path('brewlog/bean/<int:bean_id>/unarchive/', views.unarchive_coffee_bean, name='unarchive_bean'),
    path('brewlog/bean/<int:bean_id>/toggle-favorite/', views.toggle_favorite_bean, name='toggle_favorite_bean'),
    ### 回购相关
    path('brewlog/bean/<int:bean_id>/', views.view_bean, name='view_bean'),
    path('bean/<int:bean_id>/occurrence/add/', views.add_bean_occurrence, name='add_bean_occurrence'),
    path('occurrence/<int:occurrence_id>/edit/', views.edit_bean_occurrence, name='edit_bean_occurrence'),
    path('occurrence/<int:occurrence_id>/delete/', views.delete_bean_occurrence, name='delete_bean_occurrence'),
    ### 风味标签
    path('brewlog/flavor-tags/', views.get_flavor_tags, name='get_flavor_tags'),
    path('brewlog/flavor-tags/<int:tag_id>/', views.delete_flavor_tag, name='delete_flavor_tag'),
    path('brewlog/bean/calendar/', views.bean_calendar, name='bean_calendar'),
    
    ## 冲煮记录相关
    path('brewlog/record/add/', views.add_record_page, name='add_record_page'),
    path('brewlog/record/<int:record_id>/edit/page/', views.edit_record_page, name='edit_record_page'),
    path('brewlog/record/<int:record_id>/view/page/', views.view_record_page, name='view_record_page'),
    path('brewlog/record/<int:record_id>/delete/', views.delete_brewing_record, name='delete_brewing_record'),
    ### 后见之明相关
    path('brewlog/hindsight/', views.brewing_hindsight, name='brewing_hindsight'),
    ### 冲煮记录热力图
    path('brewlog/heatmap/', views.brewlog_heatmap, name='brewlog_heatmap'),
    path('brewlog/heatmap/<int:year>/', views.brewlog_heatmap, name='brewlog_heatmap_year'),
    path('brewlog/heatmap/years/', views.get_available_years, name='get_available_years'),
    ### 导出功能
    path('brewlog/export/', views.export_brewing_records, name='export_brewing_records'),
    ### 对比功能
    path('brewlog/compare/<int:record1_id>/<int:record2_id>/', views.compare_records, name='compare_records'),
    
    ### 配方管理
    path('brewlog/recipes/', views.recipe_list, name='recipe_list'),
    path('brewlog/recipes/tag/manage/', views.manage_recipe_tag, name='manage_recipe_tag'),
    path('brewlog/recipes/<int:recipe_id>/tag/<int:tag_id>/toggle/', views.toggle_recipe_tag, name='toggle_recipe_tag'),
    path('brewlog/recipes/<int:recipe_id>/quick-brew/', views.quick_brew, name='quick_brew'),
    path('brewlog/recipes/<int:recipe_id>/preview/', views.preview_quick_brew, name='preview_quick_brew'),
    path('brewlog/recipes/<int:recipe_id>/rename/', views.rename_recipe, name='rename_recipe'),
]