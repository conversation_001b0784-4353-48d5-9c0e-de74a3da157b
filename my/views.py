from .forms import EquipmentForm, CoffeeBeanForm, BrewingRecordForm, BeanOccurrenceForm
from .models import (
    Favorite, Equipment, CoffeeBean, BrewingRecord, FlavorTag,
    CoffeeQuote, BlendComponent, BeanOccurrence, HindsightStats,
    get_trend_cache_key, RecipeThread, RecipeTag
)
from .cache_utils import (
    invalidate_hindsight_cache,
    cache_response,
    invalidate_bean_cache,
    cache_manager,
    CACHE_TIMEOUT,
    invalidate_equipment_cache,
    invalidate_record_cache,
    invalidate_recipe_cache
)
from datetime import datetime, timedelta, date
from decimal import Decimal
from django_ratelimit.decorators import ratelimit
from django_redis import get_redis_connection
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.core.cache.backends.base import CacheKeyWarning
from django.core.exceptions import PermissionDenied
from django.core.paginator import Paginator
from django.core.serializers.json import DjangoJ<PERSON><PERSON><PERSON><PERSON>
from django.db.models import Q, Avg, Count, Min, <PERSON>, <PERSON><PERSON>, Case, When, BooleanField, F, Date<PERSON>ield, ExpressionWrapper, DurationField, DateTimeField
from django.db.models.functions import TruncDate, TruncMonth, ExtractWeekDay, ExtractHour, ExtractDay, Now
from django.http import HttpResponseForbidden, JsonResponse, HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils import timezone
from django.utils.cache import get_cache_key
from django.utils.safestring import mark_safe
from django.utils.timezone import make_aware
from django.views.decorators.cache import cache_page
from functools import wraps
from itertools import groupby
from zoneinfo import ZoneInfo
import csv
import json
import logging
import pandas as pd
import random
import time
import warnings
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import authentication_classes, permission_classes
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from rest_framework.decorators import api_view
from django.contrib import messages
import re
from rest_framework.authentication import TokenAuthentication

logger = logging.getLogger(__name__)

def toggle_favorite(request, app_label, model, object_id):
    content_type = get_object_or_404(ContentType, app_label=app_label, model=model)
    obj = get_object_or_404(content_type.model_class(), id=object_id)
    if Favorite.objects.filter(user=request.user, content_type=content_type, object_id=object_id).exists():
        Favorite.objects.get(user=request.user, content_type=content_type, object_id=object_id).delete()
        favorited = False
    else:
        Favorite.objects.create(user=request.user, content_type=content_type, object_id=object_id)
        favorited = True
    return JsonResponse({'favorited': favorited})

@login_required
def add_favorite(request, app_label, model, object_id):
    content_type = get_object_or_404(ContentType, app_label=app_label, model=model)
    obj = get_object_or_404(content_type.model_class(), id=object_id)
    Favorite.objects.get_or_create(user=request.user, content_type=content_type, object_id=object_id)
    return JsonResponse({'favorited': True})

@login_required
def remove_favorite(request, app_label, model, object_id):
    content_type = get_object_or_404(ContentType, app_label=app_label, model=model)
    obj = get_object_or_404(content_type.model_class(), id=object_id)
    favorite = get_object_or_404(Favorite, user=request.user, content_type=content_type, object_id=object_id)
    favorite.delete()
    return JsonResponse({'favorited': False})

@login_required
def my_favorites(request):
    user = request.user
    type_filter = request.GET.get('type', 'articles')

    # 获取不同类型收藏的数量
    favorites_count = user.favorites.values('content_type__model')\
        .annotate(count=Count('id'))

    counts = {item['content_type__model']: item['count'] for item in favorites_count}

    # 根据类型筛选收藏
    favorites = user.favorites.select_related('content_type')\
        .order_by('-created_at')

    if type_filter == 'articles':
        favorites = favorites.filter(content_type__model='articlepage')
    elif type_filter == 'beans':
        favorites = favorites.filter(content_type__model='roastedbeanpage')
    elif type_filter == 'recipes':
        favorites = favorites.filter(content_type__model='recipepage')

    # 分页
    paginator = Paginator(favorites, 10)
    page = request.GET.get('page')
    favorites = paginator.get_page(page)

    context = {
        'favorites': favorites,
        'active_tab': type_filter,
        'article_count': counts.get('articlepage', 0),
        'bean_count': counts.get('roastedbeanpage', 0),
        'recipe_count': counts.get('recipepage', 0),
    }

    # 检查是否是 HTMX 请求
    if 'HX-Request' in request.headers:
        return render(request, 'my/partials/favorites_list.html', context)
    return render(request, 'my/my_favorites.html', context)

@login_required
def brewlog(request):
    # 检查是否需要进入新手引导
    if check_onboarding_status(request.user):
        return redirect('onboarding')

    # 获取筛选参数
    filters = {
        'brew_method': request.GET.get('brew_method'),
        'coffee_bean': request.GET.get('coffee_bean'),
        'rating_range': request.GET.get('rating_range'),
        'search_query': request.GET.get('q'),
        'date_from': request.GET.get('date_from'),
        'date_to': request.GET.get('date_to')
    }

    # 获取用户的打卡信息
    streak_info = BrewingRecord.get_streak_info(request.user)

    # 获取当前页码
    try:
        page = int(request.GET.get('page', 1))
    except ValueError:
        page = 1

    # 使用模型管理器获取筛选后的记录
    pagination = BrewingRecord.objects.get_filtered_records(
        request.user,
        filters=filters,
        page=page,
        per_page=20
    )

    # 获取用户的冲煮统计数据
    stats = BrewingRecord.get_brewing_stats(request.user)

    # 检查是否有活跃的设备和咖啡豆
    has_active_brewer = Equipment.has_active_equipment(request.user, 'BREWER')
    has_active_grinder = Equipment.has_active_equipment(request.user, 'GRINDER')
    has_active_beans = CoffeeBean.has_active_beans(request.user)

    # 获取当前时区的时间
    tz = ZoneInfo('Asia/Shanghai')
    now = timezone.now().astimezone(tz)

    # 计算本月的起止时间
    month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)

    # 计算本年的起止时间
    year_start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
    year_end = now.replace(month=12, day=31, hour=23, minute=59, second=59)

    context = {
        'brewing_records': pagination['records'],
        'pagination': {
            'total_count': pagination['total_count'],
            'total_pages': pagination['total_pages'],
            'current_page': pagination['current_page'],
            'has_previous': pagination['has_previous'],
            'has_next': pagination['has_next'],
            'previous_page': pagination['previous_page'],
            'next_page': pagination['next_page'],
        },
        'streak_days': streak_info['current_streak'],
        'last_streak_days': streak_info['last_streak_days'],
        'month_count': stats['month_count'],
        'year_count': stats['year_count'],
        'selected_date': filters['date_from'],
        'has_active_brewer': has_active_brewer,
        'has_active_grinder': has_active_grinder,
        'has_active_beans': has_active_beans,
        # 筛选相关
        'brew_methods': Equipment.BREW_METHODS,
        'coffee_beans': CoffeeBean.get_active_beans(request.user),
        'current_brew_method': filters['brew_method'],
        'current_coffee_bean': filters['coffee_bean'],
        'current_rating_range': filters['rating_range'],
        'search_query': filters['search_query'],
        # 日期范围格式化为 YYYY-MM-DD
        'date_from': filters['date_from'],
        'date_to': filters['date_to'],
        'month_start': month_start,
        'month_end': month_end,
        'year_start': year_start,
        'year_end': year_end,
    }

    return render(request, 'my/brewlog/brewlog.html', context)

@login_required
def add_equipment_page(request):
    # 获取活跃小工具数量
    active_gadgets_count = Equipment.objects.filter(
        user=request.user,
        type='GADGET',
        is_archived=False,
        is_deleted=False
    ).count()

    if request.method == 'POST':
        form = EquipmentForm(request.POST, initial={'user': request.user})
        if form.is_valid():
            equipment = form.save(commit=False)
            equipment.user = request.user
            equipment.save()
            # 如果是小工具组合，保存组件关系
            if equipment.type == 'GADGET_KIT':
                form.save_m2m()  # 保存多对多关系

            # 清除设备缓存
            from my.cache_utils import invalidate_equipment_cache
            invalidate_equipment_cache(request.user.id, equipment.id)

            # 清除iOS端缓存
            try:
                from iosapp.cache_utils import invalidate_ios_cache
                invalidate_ios_cache('equipment_list', request.user.id)
            except ImportError:
                # 如果无法导入iOS缓存模块，记录日志但不中断操作
                logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {request.user.id}, 设备ID: {equipment.id}")

            return redirect('equipment_list')
    else:
        form = EquipmentForm(initial={'user': request.user})

    return render(request, 'my/brewlog/add_equipment.html', {
        'form': form,
        'active_gadgets_count': active_gadgets_count
    })

@login_required
def edit_equipment_page(request, equipment_id):
    equipment = get_object_or_404(Equipment, id=equipment_id, user=request.user)

    # 获取活跃小工具数量
    active_gadgets_count = Equipment.objects.filter(
        user=request.user,
        type='GADGET',
        is_archived=False,
        is_deleted=False
    ).count()

    # 检查设备是否已被软删除
    if equipment.is_deleted:
        form = EquipmentForm(instance=equipment, initial={'user': request.user})
        return render(request, 'my/brewlog/edit_equipment.html', {
            'form': form,
            'equipment': equipment,
            'active_gadgets_count': active_gadgets_count
        })

    if request.method == 'POST':
        form = EquipmentForm(
            request.POST,
            instance=equipment,
            initial={'user': request.user}
        )
        if form.is_valid():
            # 使用 commit=False 保存主表数据
            equipment = form.save(commit=False)
            equipment.save()

            # 如果是小工具组合，保存组件关系
            if equipment.type == 'GADGET_KIT':
                # 清除现有关系
                equipment.gadget_components.clear()
                # 保存新的关系
                if form.cleaned_data.get('gadget_components'):
                    equipment.gadget_components.set(form.cleaned_data['gadget_components'])

            # 清除设备缓存
            from my.cache_utils import invalidate_equipment_cache
            invalidate_equipment_cache(request.user.id, equipment.id)

            # 清除iOS端缓存
            try:
                from iosapp.cache_utils import invalidate_ios_cache
                invalidate_ios_cache('equipment_list', request.user.id)
            except ImportError:
                # 如果无法导入iOS缓存模块，记录日志但不中断操作
                logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {request.user.id}, 设备ID: {equipment.id}")

            return redirect('view_equipment', equipment_id=equipment.id)
    else:
        form = EquipmentForm(
            instance=equipment,
            initial={'user': request.user}
        )

    return render(request, 'my/brewlog/edit_equipment.html', {
        'form': form,
        'equipment': equipment,
        'active_gadgets_count': active_gadgets_count
    })

@login_required
def add_bean_page(request):
    if request.method == 'POST':
        form = CoffeeBeanForm(request.POST)
        if form.is_valid():
            bean = form.save(commit=False)
            bean.user = request.user
            bean.save()

            # 处理拼配组件
            if bean.type == 'BLEND':
                # 从前端获取组件数量
                components_count = len([k for k in request.POST.keys() if k.startswith('blend_components-') and k.endswith('-origin')])

                # 创建拼配组件
                for i in range(components_count):
                    prefix = f'blend_components-{i}-'
                    try:
                        component = BlendComponent.objects.create(
                            coffee_bean=bean,
                            origin=request.POST.get(f'{prefix}origin', ''),
                            region=request.POST.get(f'{prefix}region', ''),
                            finca=request.POST.get(f'{prefix}finca', ''),
                            variety=request.POST.get(f'{prefix}variety', ''),
                            process=request.POST.get(f'{prefix}process', ''),
                            roast_level=int(request.POST.get(f'{prefix}roast_level', 4)),
                            blend_ratio=float(request.POST.get(f'{prefix}blend_ratio', 100 if components_count == 1 else 0)),
                            altitude_type=request.POST.get(f'{prefix}altitude_type', 'SINGLE'),
                            altitude_single=request.POST.get(f'{prefix}altitude_single', None) or None,
                            altitude_min=request.POST.get(f'{prefix}altitude_min', None) or None,
                            altitude_max=request.POST.get(f'{prefix}altitude_max', None) or None,
                            order=i
                        )
                        # print(f"Created blend component {i}:", component.__dict__)
                    except Exception as e:
                        # print(f"Error creating blend component {i}: {e}")
                        # 如果创建失败，删除已创建的咖啡豆和组件
                        bean.delete()
                        return render(request, 'my/brewlog/add_bean.html', {
                            'form': form,
                            'error': f'拼配组件数据无效: {str(e)}'
                        })

            # 处理风味标签
            tags_data = request.POST.get('flavor_tags_data', '')
            if tags_data:
                tags = [tag.strip() for tag in tags_data.split(',') if tag.strip()]
                for tag_name in tags:
                    tag, _ = FlavorTag.objects.get_or_create(
                        name=tag_name,
                        user=request.user
                    )
                    bean.flavor_tags.add(tag)

            # 清除缓存
            invalidate_bean_cache(request.user.id)
            return redirect('bean_list')
        else:
            #print("Form errors:", form.errors)
            # 如果表单验证失败，获取拼配组件数据
            blend_components = []
            i = 0
            while True:
                prefix = f'blend_components-{i}-'
                if not any(k.startswith(prefix) for k in request.POST):
                    break
                component = {
                    'blend_ratio': request.POST.get(f'{prefix}blend_ratio', ''),
                    'roast_level': request.POST.get(f'{prefix}roast_level', '4'),
                    'origin': request.POST.get(f'{prefix}origin', ''),
                    'region': request.POST.get(f'{prefix}region', ''),
                    'finca': request.POST.get(f'{prefix}finca', ''),
                    'variety': request.POST.get(f'{prefix}variety', ''),
                    'altitude_type': request.POST.get(f'{prefix}altitude_type', 'SINGLE'),
                    'altitude_single': request.POST.get(f'{prefix}altitude_single', ''),
                    'altitude_min': request.POST.get(f'{prefix}altitude_min', ''),
                    'altitude_max': request.POST.get(f'{prefix}altitude_max', ''),
                    'process': request.POST.get(f'blend_components-{i}-process', '')
                }
                blend_components.append(component)
                i += 1

            return render(request, 'my/brewlog/add_bean.html', {
                'form': form,
                'blend_components': json.dumps(blend_components),
                'form_data': request.POST
            })

    return render(request, 'my/brewlog/add_bean.html', {'form': CoffeeBeanForm()})

@login_required
def edit_bean_page(request, bean_id):
    bean = get_object_or_404(CoffeeBean, id=bean_id, user=request.user)

    if bean.is_deleted:
        form = CoffeeBeanForm(instance=bean)
        return render(request, 'my/brewlog/edit_bean.html', {
            'form': form,
            'bean': bean,
        })

    if request.method == 'POST':
        form = CoffeeBeanForm(request.POST, instance=bean)
        if form.is_valid():
            try:
                # 先保存基本信息
                bean = form.save(commit=False)

                # 根据 altitude_type 设置海拔信息
                altitude_type = request.POST.get('altitude_type', 'SINGLE')
                if altitude_type == 'SINGLE':
                    bean.altitude_min = None
                    bean.altitude_max = None
                else:
                    bean.altitude_single = None

                # 如果有回购记录,同步更新最新一条回购记录的包装信息
                latest_occurrence = bean.occurrences.order_by('-created_at').first()
                if latest_occurrence:
                    latest_occurrence.bag_weight = bean.bag_weight
                    latest_occurrence.bag_remain = bean.bag_remain
                    latest_occurrence.purchase_price = bean.purchase_price
                    latest_occurrence.roast_date = bean.roast_date
                    latest_occurrence.rest_period_min = bean.rest_period_min
                    latest_occurrence.rest_period_max = bean.rest_period_max
                    latest_occurrence.save()

                bean.save()

                # 处理拼配组件
                if bean.type == 'BLEND':
                    # 先删除现有的拼配组件
                    bean.blend_components.all().delete()

                    # 从前端获取组件数量
                    components_count = len([k for k in request.POST.keys() if k.startswith('blend_components-') and k.endswith('-origin')])

                    # 创建新的组件
                    for i in range(components_count):
                        prefix = f'blend_components-{i}-'
                        try:
                            # 获取海拔类型和相应的值
                            altitude_type = request.POST.get(f'{prefix}altitude_type', 'SINGLE')
                            if altitude_type == 'SINGLE':
                                altitude_value = request.POST.get(f'{prefix}altitude_single', '')
                            else:
                                altitude_min = request.POST.get(f'{prefix}altitude_min', '')
                                altitude_max = request.POST.get(f'{prefix}altitude_max', '')
                                altitude_value = f"{altitude_min}-{altitude_max}" if altitude_min and altitude_max else ''

                            component = BlendComponent.objects.create(
                                coffee_bean=bean,
                                origin=request.POST.get(f'{prefix}origin', ''),
                                region=request.POST.get(f'{prefix}region', ''),
                                finca=request.POST.get(f'{prefix}finca', ''),
                                variety=request.POST.get(f'{prefix}variety', ''),
                                process=request.POST.get(f'{prefix}process', ''),
                                roast_level=int(request.POST.get(f'{prefix}roast_level', 4)),
                                blend_ratio=float(request.POST.get(f'{prefix}blend_ratio', 100 if components_count == 1 else 0)),
                                altitude_type=request.POST.get(f'{prefix}altitude_type', 'SINGLE'),
                                altitude_single=request.POST.get(f'{prefix}altitude_single', None) or None,
                                altitude_min=request.POST.get(f'{prefix}altitude_min', None) or None,
                                altitude_max=request.POST.get(f'{prefix}altitude_max', None) or None,
                                order=i
                            )
                        except Exception as e:
                            raise ValueError(f'拼配组件 #{i+1} 数据无效: {str(e)}')

                # 处理风味标签
                tags_data = request.POST.get('flavor_tags_data', '')
                bean.flavor_tags.clear()
                if tags_data:
                    tags = [tag.strip() for tag in tags_data.split(',') if tag.strip()]
                    for tag_name in tags:
                        tag, _ = FlavorTag.objects.get_or_create(
                            name=tag_name,
                            user=request.user
                        )
                        bean.flavor_tags.add(tag)

                # 成功保存后重定向到查看页面
                bean.save()

                # 清除缓存
                invalidate_bean_cache(request.user.id, bean.id)
                return redirect('view_bean', bean_id=bean.id)

            except Exception as e:
                # 如果出错，返回到编辑页面并显示错误信息
                return render(request, 'my/brewlog/edit_bean.html', {
                    'form': form,
                    'bean': bean,
                    'error': str(e),
                    'blend_components': json.dumps([{
                        'blend_ratio': request.POST.get(f'blend_components-{i}-blend_ratio', ''),
                        'roast_level': request.POST.get(f'blend_components-{i}-roast_level', '4'),
                        'origin': request.POST.get(f'blend_components-{i}-origin', ''),
                        'region': request.POST.get(f'blend_components-{i}-region', ''),
                        'finca': request.POST.get(f'blend_components-{i}-finca', ''),
                        'variety': request.POST.get(f'blend_components-{i}-variety', ''),
                        'altitude_type': request.POST.get(f'{prefix}altitude_type', 'SINGLE'),
                        'altitude_single': request.POST.get(f'{prefix}altitude_single', ''),
                        'altitude_min': request.POST.get(f'{prefix}altitude_min', ''),
                        'altitude_max': request.POST.get(f'{prefix}altitude_max', ''),
                        'process': request.POST.get(f'blend_components-{i}-process', '')
                    } for i in range(components_count)])
                })
        else:
            # 如果表单验证失败，返回到编辑页面并显示错误信息
            return render(request, 'my/brewlog/edit_bean.html', {
                'form': form,
                'bean': bean,
                'error': '表单数据无效，请检查输入。',
                'blend_components': request.POST.get('blend_components', '[]')
            })

    # GET 请求处理
    blend_components = []
    if bean.type == 'BLEND':
        for component in bean.blend_components.all().order_by('order'):
            blend_components.append({
                'blend_ratio': str(component.blend_ratio),
                'roast_level': component.roast_level,
                'origin': component.origin,
                'region': component.region,
                'finca': component.finca,
                'variety': component.variety,
                'altitude_type': component.altitude_type,
                'altitude_single': component.altitude_single,
                'altitude_min': component.altitude_min,
                'altitude_max': component.altitude_max,
                'process': component.process
            })

    form = CoffeeBeanForm(instance=bean)
    # 获取已有标签并格式化为字符串
    existing_tags = ','.join([tag.name for tag in bean.flavor_tags.all()])
    return render(request, 'my/brewlog/edit_bean.html', {
        'form': form,
        'bean': bean,
        'blend_components': json.dumps(blend_components),
        'existing_tags': existing_tags
    })

@login_required
def add_record_page(request):
    initial_data = {}
    steps_enabled = False
    steps_json = '[]'

    if request.GET.get('copy_from'):
        try:
            original_record = get_object_or_404(BrewingRecord, id=request.GET.get('copy_from'), user=request.user)
            # 设置初始数据
            initial_data = {
                'created_at': timezone.now().strftime('%Y-%m-%dT%H:%M'),
                'recipe_name': original_record.recipe_name,
                'brewing_equipment': original_record.brewing_equipment.id if original_record.brewing_equipment else None,
                'grinding_equipment': original_record.grinding_equipment.id if original_record.grinding_equipment else None,
                'coffee_bean': original_record.coffee_bean.id if original_record.coffee_bean else None,
                'grind_size': original_record.grind_size,
                'dose_weight': original_record.dose_weight,
                'yield_weight': original_record.yield_weight,
                'water_temperature': original_record.water_temperature,
                'notes': original_record.notes,
                'rating_level': original_record.rating_level,
                'gadget_kit': original_record.gadget_kit.id if original_record.gadget_kit else None,
                # 添加品鉴笔记相关字段
                'aroma': original_record.aroma,
                'acidity': original_record.acidity,
                'body': original_record.body,
                'sweetness': original_record.sweetness,
                'aftertaste': original_record.aftertaste,
            }
            # 获取原记录的风味标签
            initial_flavor_tags = [{'id': tag.id, 'name': tag.name} for tag in original_record.flavor_tags.all().order_by('name')]
            initial_flavor_tags_json = json.dumps(initial_flavor_tags, ensure_ascii=False)
            initial_flavor_tags_data = ','.join(str(tag['id']) for tag in initial_flavor_tags)

            # 正确处理 brewing_time
            if original_record.brewing_time:
                total_seconds = int(original_record.brewing_time.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60
                # 格式化为字符串
                initial_data['brewing_time'] = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

            # 如果原记录有小工具组合，优先使用组合中的小工具
            if original_record.gadget_kit:
                initial_data['gadgets'] = [g.id for g in original_record.gadget_kit.gadget_components.all()]
            # 否则使用单独选择的小工具
            elif original_record.gadgets.exists():
                initial_data['gadgets'] = [g.id for g in original_record.gadgets.all()]

            # 复制步骤数据
            if original_record.steps:
                steps = []
                for step in original_record.steps:
                    timer = step.get('timer')
                    minutes = seconds = 0
                    if timer and ':' in timer:
                        minutes, seconds = map(int, timer.split(':'))

                    step_data = {
                        'text': step.get('text', ''),
                        'order': step.get('order', 1),
                        'hasTimer': bool(timer),
                        'minutes': minutes,
                        'seconds': seconds
                    }
                    steps.append(step_data)
                steps_json = mark_safe(json.dumps(steps, ensure_ascii=False))
                steps_enabled = True
        except BrewingRecord.DoesNotExist:
            form = BrewingRecordForm(user=request.user)
    else:
        # 新增记录时设置首选项
        initial_data = {
            'created_at': timezone.now().strftime('%Y-%m-%dT%H:%M'),
        }

        # 设置首选冲煮器具
        favorite_brewer = Equipment.get_favorite_equipment(request.user, 'BREWER')
        if favorite_brewer:
            initial_data['brewing_equipment'] = favorite_brewer.id

        # 设置首选磨豆机
        favorite_grinder = Equipment.get_favorite_equipment(request.user, 'GRINDER')
        if favorite_grinder:
            initial_data['grinding_equipment'] = favorite_grinder.id

        # 设置首选咖啡豆
        favorite_bean = CoffeeBean.get_favorite_bean(request.user)
        if favorite_bean:
            initial_data['coffee_bean'] = favorite_bean.id

        # 设置首选小工具组合和小工具
        favorite_gadget_data = Equipment.get_favorite_gadget_kit(request.user)
        if favorite_gadget_data['kit']:
            initial_data['gadget_kit'] = favorite_gadget_data['kit'].id
        if favorite_gadget_data['gadget_ids']:
            initial_data['gadgets'] = favorite_gadget_data['gadget_ids']

    form = BrewingRecordForm(initial=initial_data, user=request.user)

    if request.method == 'POST':
        form = BrewingRecordForm(request.POST, user=request.user)
        # 使用模型方法处理步骤数据
        steps_enabled, steps = BrewingRecord.parse_steps_from_post(request.POST)

        if form.is_valid():
            record = form.save(commit=False)
            record.user = request.user

            # 设置步骤数据
            record.steps = steps
            record.steps_enabled = steps_enabled

            # 处理环境数据
            record.water_quality = form.cleaned_data.get('water_quality')
            record.room_temperature = form.cleaned_data.get('room_temperature')
            record.room_humidity = form.cleaned_data.get('room_humidity')

            record.save()  # 保存记录

            # 保存 gadgets 多对多关系
            gadgets = form.cleaned_data.get('gadgets')
            if gadgets is not None:
                record.gadgets.set(gadgets)  # 使用 set() 方法设置多对多关系

            # 保存品鉴标签
            flavor_tags_data = request.POST.get('flavor-tags-data', '')
            tag_ids = []
            if flavor_tags_data:
                tag_ids = [int(tag_id) for tag_id in flavor_tags_data.split(',') if tag_id.strip()]

            # 设置风味标签
            if tag_ids:
                record.flavor_tags.add(*tag_ids)

            # 清除相关缓存
            if record.coffee_bean:
                invalidate_bean_cache(request.user.id, record.coffee_bean.id)

            invalidate_hindsight_cache(request.user.id)

            return redirect('brewlog')
        else:
            # 如果表单验证失败，使用模型方法格式化步骤数据
            steps_json = mark_safe(json.dumps(
                BrewingRecord.format_steps_for_json(request.POST),
                ensure_ascii=False
            ))

    # 获取活跃的设备和咖啡豆
    active_equipment = Equipment.objects.filter(
        user=request.user,
        is_archived=False,
        is_deleted=False
    ).order_by('-is_favorite', '-id')  # 首选排在前面

    # 分别获取首选的冲煮设备和磨豆机
    favorite_brewing_equipment = active_equipment.filter(
        type='BREWER',
        is_favorite=True
    ).first()

    favorite_grinding_equipment = active_equipment.filter(
        type='GRINDER',
        is_favorite=True
    ).first()

    # 获取活跃的咖啡豆
    active_beans = CoffeeBean.objects.filter(
        user=request.user,
        is_archived=False,
        is_deleted=False
    ).order_by('-is_favorite', '-id')  # 首选排在前面

    # 为每个咖啡豆添加库存状态信息
    beans_with_stock = []
    for bean in active_beans:
        beans_with_stock.append({
            'bean': bean,
            'stock_status': bean.get_stock_status()
        })

    favorite_coffee_bean = active_beans.filter(is_favorite=True).first()

    # 获取用户的风味标签
    flavor_tags = list(FlavorTag.objects.filter(user=request.user).values('id', 'name'))
    flavor_tags_json = mark_safe(json.dumps(flavor_tags, ensure_ascii=False))
    flavor_profile_choices = BrewingRecord.FLAVOR_PROFILE_CHOICES

    # 如果是复制记录，获取原记录的标签
    initial_flavor_tags_data = ''
    initial_flavor_tags_json = '[]'
    if copy_from_id := request.GET.get('copy_from'):
        try:
            original_record = BrewingRecord.objects.get(
                id=copy_from_id,
                user=request.user
            )
            # 获取原记录的标签
            original_tags = list(original_record.flavor_tags.all().values('id', 'name').order_by('name'))
            initial_flavor_tags_json = json.dumps(original_tags, ensure_ascii=False)
            initial_flavor_tags_data = ','.join(str(tag['id']) for tag in original_tags)
        except BrewingRecord.DoesNotExist:
            pass

    return render(request, 'my/brewlog/add_record.html', {
        'form': form,
        'active_equipment': active_equipment,  # 这里包含了小工具组合
        'active_beans': beans_with_stock,
        'favorite_brewing_equipment': favorite_brewing_equipment,
        'favorite_grinding_equipment': favorite_grinding_equipment,
        'favorite_coffee_bean': favorite_coffee_bean,
        'steps_json': steps_json,
        'steps_enabled': steps_enabled,
        'selected_tab': request.POST.get('selected_tab', '1'),
        'flavor_tags_json': flavor_tags_json,
        'flavor_profile_choices': flavor_profile_choices,
        'initial_flavor_tags_json': mark_safe(initial_flavor_tags_json),
        'initial_flavor_tags_data': initial_flavor_tags_data,  # 添加这个字段
    })

@login_required
def edit_record_page(request, record_id):
    record = get_object_or_404(BrewingRecord, id=record_id, user=request.user)
    beans_with_stock = CoffeeBean.objects.filter(
        Q(user=request.user, is_archived=False) |
        Q(id=record.coffee_bean.id) if record.coffee_bean else Q()
    ).order_by('-created_at')

    # 获取初始风味标签数据
    initial_flavor_tags = [{'id': tag.id, 'name': tag.name} for tag in record.flavor_tags.all().order_by('name')]
    initial_flavor_tags_json = json.dumps(initial_flavor_tags, ensure_ascii=False)

    if request.method == 'POST':
        form = BrewingRecordForm(request.POST, instance=record, user=request.user)
        if form.is_valid():
            # 保存修改前的咖啡豆ID
            old_bean_id = record.coffee_bean.id if record.coffee_bean else None

            # 先保存基本数据，但不提交到数据库
            record = form.save(commit=False)

            # 保存品鉴维度数据
            record.aroma = int(request.POST.get('aroma', 0))
            record.acidity = int(request.POST.get('acidity', 0))
            record.body = int(request.POST.get('body', 0))
            record.sweetness = int(request.POST.get('sweetness', 0))
            record.aftertaste = int(request.POST.get('aftertaste', 0))

            # 确保brewing_time保持原始格式
            if 'brewing_time' in request.POST:
                time_parts = request.POST['brewing_time'].split(':')
                if len(time_parts) == 3:
                    hours = int(time_parts[0])
                    minutes = int(time_parts[1])
                    seconds = int(time_parts[2])
                    record.brewing_time = timedelta(
                        hours=hours,
                        minutes=minutes,
                        seconds=seconds
                    )

            # 处理步骤数据
            steps_enabled = any(key.startswith('steps[') for key in request.POST.keys())
            record.steps_enabled = steps_enabled

            if steps_enabled:
                steps = []
                step_index = 0

                while f'steps[{step_index}][text]' in request.POST:
                    step_text = request.POST.get(f'steps[{step_index}][text]')
                    if step_text.strip():
                        step = {
                            'text': step_text,
                            'order': step_index + 1
                        }

                        has_timer = request.POST.get(f'steps[{step_index}][has_timer]') == 'on'
                        if has_timer:
                            minutes = int(request.POST.get(f'steps[{step_index}][minutes]', 0))
                            seconds = int(request.POST.get(f'steps[{step_index}][seconds]', 0))
                            step['timer'] = f"{minutes:02d}:{seconds:02d}"

                        steps.append(step)
                    step_index += 1

                record.steps = steps
            else:
                record.steps = []

            # 处理环境数据
            record.water_quality = form.cleaned_data.get('water_quality')
            record.room_temperature = form.cleaned_data.get('room_temperature')
            record.room_humidity = form.cleaned_data.get('room_humidity')

            # 保存记录到数据库
            record.save()

            # 保存多对多关系
            form.save_m2m()

            # 处理风味标签
            flavor_tags_data = request.POST.get('flavor-tags-data', '')
            tag_ids = []
            if flavor_tags_data:
                tag_ids = [int(tag_id) for tag_id in flavor_tags_data.split(',') if tag_id.strip()]

            # 设置风味标签（使用clear而不是set，以避免可能的缓存问题）
            record.flavor_tags.clear()
            if tag_ids:
                record.flavor_tags.add(*tag_ids)

            # 更新咖啡豆库存
            if old_bean_id != record.coffee_bean.id:
                if old_bean_id:
                    old_bean = CoffeeBean.objects.get(id=old_bean_id)
                    old_bean.bag_remain = F('bag_remain') + record.dose_weight
                    old_bean.save()

                if record.coffee_bean:
                    record.coffee_bean.bag_remain = F('bag_remain') - record.dose_weight
                    record.coffee_bean.save()

            # 清除记录相关缓存
            invalidate_record_cache(request.user.id, record.id)

            # 清除相关咖啡豆缓存
            if record.coffee_bean:
                invalidate_bean_cache(request.user.id, record.coffee_bean.id)
            if old_bean_id and old_bean_id != record.coffee_bean.id:
                invalidate_bean_cache(request.user.id, old_bean_id)

            # 清除相关设备缓存
            equipment_ids = []
            if record.brewing_equipment:
                equipment_ids.append(record.brewing_equipment.id)
            if record.grinding_equipment:
                equipment_ids.append(record.grinding_equipment.id)
            if record.gadget_kit:
                equipment_ids.append(record.gadget_kit.id)
            for gadget in record.gadgets.all():
                equipment_ids.append(gadget.id)

            for equipment_id in equipment_ids:
                invalidate_equipment_cache(request.user.id, equipment_id)

            # 清除用户统计数据缓存
            invalidate_hindsight_cache(request.user.id)

            # 清除iOS端相关缓存
            try:
                from iosapp.cache_utils import invalidate_ios_cache
                # 清除冲煮记录列表缓存
                invalidate_ios_cache('brewlog_list', request.user.id)
                invalidate_ios_cache('brewlog_statistics', request.user.id)
                invalidate_ios_cache('filtered_brewlog_list', request.user.id)

                # 清除咖啡豆相关缓存
                invalidate_ios_cache('bean_list', request.user.id)
                invalidate_ios_cache('bean_calendar_data', request.user.id)

                # 清除统计数据缓存
                invalidate_ios_cache('hindsight_data', request.user.id)
                invalidate_ios_cache('heatmap_data', request.user.id)
            except ImportError:
                # 如果无法导入iOS缓存模块，记录但不中断操作
                logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {request.user.id}")

            messages.success(request, '冲煮记录已更新')
            return redirect('view_record_page', record_id=record.id)
    else:
        form = BrewingRecordForm(instance=record, user=request.user)
        # 设置已选择的小工具
        form.initial['gadgets'] = [gadget.id for gadget in record.gadgets.all()]

    # 获取活跃的咖啡豆并添加库存信息
    active_beans = CoffeeBean.objects.filter(
        user=request.user,
        is_archived=False,
        is_deleted=False
    ).order_by('-is_favorite', '-id')

    # 如果当前记录的咖啡豆已归档，也要包含它
    if record.coffee_bean and record.coffee_bean.is_archived:
        active_beans = list(active_beans)
        if record.coffee_bean not in active_beans:
            active_beans.append(record.coffee_bean)

    # 为每个咖啡豆添加库存状态信息
    beans_with_stock = []
    for bean in active_beans:
        beans_with_stock.append({
            'bean': bean,
            'stock_status': bean.get_stock_status()
        })

    if request.method == 'POST':
        form = BrewingRecordForm(request.POST, instance=record, user=request.user)
        if form.is_valid():
            record = form.save(commit=False)

            # 处理步骤数据
            steps_enabled = any(key.startswith('steps[') for key in request.POST.keys())
            record.steps_enabled = steps_enabled

            if steps_enabled:
                steps = []
                step_index = 0

                while f'steps[{step_index}][text]' in request.POST:
                    step_text = request.POST.get(f'steps[{step_index}][text]')
                    if step_text.strip():
                        step = {
                            'text': step_text,
                            'order': step_index + 1
                        }

                        has_timer = request.POST.get(f'steps[{step_index}][has_timer]') == 'on'
                        if has_timer:
                            minutes = int(request.POST.get(f'steps[{step_index}][minutes]', 0))
                            seconds = int(request.POST.get(f'steps[{step_index}][seconds]', 0))
                            step['timer'] = f"{minutes:02d}:{seconds:02d}"

                        steps.append(step)
                    step_index += 1

                record.steps = steps
            else:
                record.steps = []

            record.save()
            return redirect('view_record_page', record_id=record.id)
    else:
        form = BrewingRecordForm(instance=record, user=request.user)

        # 处理步骤数据
        steps = []
        if record.steps:
            for step in record.steps:
                timer = step.get('timer')
                minutes = seconds = 0
                if timer and ':' in timer:
                    minutes, seconds = map(int, timer.split(':'))

                step_data = {
                    'text': step.get('text', ''),
                    'order': step.get('order', 1),
                    'hasTimer': bool(timer),
                    'minutes': minutes,
                    'seconds': seconds
                }
                steps.append(step_data)

        steps_json = mark_safe(json.dumps(steps, ensure_ascii=False))

    # 处理brewing_time的显示格式
    if record.brewing_time:
        total_seconds = int(record.brewing_time.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        form.initial['brewing_time'] = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    # 获取活跃的设备（包括所有类型）
    active_equipment = Equipment.objects.filter(
        user=request.user,
        is_archived=False,
        is_deleted=False
    ).order_by('-is_favorite', '-id')

    # 如果记录中使用了已归档或删除的设备，也要包含它们
    if record.brewing_equipment and record.brewing_equipment.is_deleted:
        active_equipment |= Equipment.objects.filter(id=record.brewing_equipment.id)
    if record.grinding_equipment and record.grinding_equipment.is_deleted:
        active_equipment |= Equipment.objects.filter(id=record.grinding_equipment.id)
    if record.gadget_kit and record.gadget_kit.is_deleted:
        active_equipment |= Equipment.objects.filter(id=record.gadget_kit.id)

    # 获取用户的风味标签
    flavor_tags = list(FlavorTag.objects.filter(user=request.user).values('id', 'name'))
    flavor_tags_json = mark_safe(json.dumps(flavor_tags, ensure_ascii=False))

    # 获取记录的初始标签
    initial_flavor_tags = list(record.flavor_tags.values('id', 'name'))
    initial_flavor_tags_json = mark_safe(json.dumps(initial_flavor_tags, ensure_ascii=False))
    flavor_profile_choices = BrewingRecord.FLAVOR_PROFILE_CHOICES

    return render(request, 'my/brewlog/edit_record.html', {
        'form': form,
        'record': record,
        'active_equipment': active_equipment,  # 添加这行以支持小工具组合选择
        'active_beans': beans_with_stock,
        'steps_json': steps_json if 'steps_json' in locals() else '[]',
        'steps_enabled': bool(record.steps),
        'selected_tab': request.POST.get('selected_tab', '1'),
        'flavor_tags': flavor_tags_json,
        'initial_flavor_tags_json': initial_flavor_tags_json,
        'flavor_profile_choices': BrewingRecord.FLAVOR_PROFILE_CHOICES,
    })

@login_required
def view_record_page(request, record_id):
    record = get_object_or_404(BrewingRecord, id=record_id, user=request.user)

    # 获取缓存的趋势数据
    cache_key = get_trend_cache_key(request.user.id, record_id)
    history_data = cache.get(cache_key)
    cache_status = 'hit'

    if history_data is None:
        cache_status = 'miss'
        # 如果缓存不存在，重新获取历史数据
        history_data = record.get_history_data()

        # 只有当历史数据长度大于1时才缓存
        if len(history_data) > 1:
            # 缓存趋势数据，不设置过期时间
            cache.set(cache_key, history_data)
        else:
            # 如果记录数不足，设置为None以避免显示图表
            history_data = None

    context = {
        'record': record,
        'user': request.user,
        'history_data': json.dumps(history_data) if history_data and len(history_data) > 1 else None,
        'cache_status': cache_status,  # 用于调试
        'debug': settings.DEBUG  # 只在开发环境显示缓存状态
    }

    return render(request, 'my/brewlog/view_record.html', context)

@login_required
def delete_equipment(request, equipment_id):
    equipment = get_object_or_404(Equipment, id=equipment_id, user=request.user)
    equipment.delete()

    # 清除缓存
    invalidate_equipment_cache(request.user.id, equipment_id)

    # 清除iOS端缓存
    try:
        from iosapp.cache_utils import invalidate_ios_cache
        invalidate_ios_cache('equipment_list', request.user.id)
    except ImportError:
        # 如果无法导入iOS缓存模块，记录日志但不中断操作
        logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {request.user.id}, 设备ID: {equipment_id}")

    return JsonResponse({'status': 'success'})

@login_required
def delete_coffee_bean(request, bean_id):
    bean = get_object_or_404(CoffeeBean, id=bean_id, user=request.user)
    bean.delete()

    # 清除缓存
    invalidate_bean_cache(request.user.id, bean.id)

    # 清除iOS端缓存
    try:
        from iosapp.cache_utils import invalidate_ios_cache
        invalidate_ios_cache('bean_list', request.user.id)
        invalidate_ios_cache('bean_calendar_data', request.user.id)
        invalidate_ios_cache('hindsight_data', request.user.id)
    except ImportError:
        # 如果无法导入iOS缓存模块，记录日志但不中断操作
        logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {request.user.id}, Bean ID: {bean_id}")

    return JsonResponse({'status': 'success'})

@login_required
def delete_brewing_record(request, record_id):
    record = get_object_or_404(BrewingRecord, id=record_id, user=request.user)

    # 获取涉及的设备ID，用于清除设备缓存
    equipment_ids = []
    if record.brewing_equipment:
        equipment_ids.append(record.brewing_equipment.id)
    if record.grinding_equipment:
        equipment_ids.append(record.grinding_equipment.id)
    if record.gadget_kit:
        equipment_ids.append(record.gadget_kit.id)
    if hasattr(record, 'gadgets'):
        for gadget in record.gadgets.all():
            equipment_ids.append(gadget.id)

    user_id = record.user.id

    # 删除记录 (库存回退和缓存更新逻辑已移至模型的delete方法)
    record.delete()

    # 清除记录缓存
    invalidate_record_cache(user_id, record_id)

    # 清除相关设备缓存
    from my.cache_utils import invalidate_equipment_cache
    for equipment_id in equipment_ids:
        invalidate_equipment_cache(user_id, equipment_id)

    # 清除iOS端缓存
    try:
        from iosapp.cache_utils import invalidate_ios_cache
        invalidate_ios_cache('brewlog_list', user_id)
        invalidate_ios_cache('brewlog_statistics', user_id)
        invalidate_ios_cache('filtered_brewlog_list', user_id)
        invalidate_ios_cache('equipment_list', user_id)
        invalidate_ios_cache('hindsight_data', user_id)
        invalidate_ios_cache('heatmap_data', user_id)
    except ImportError:
        # 如果无法导入iOS缓存模块，记录日志但不中断操作
        logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {user_id}")

    return JsonResponse({'status': 'success'})

@login_required
def get_flavor_tags(request):
    if request.method == 'GET':
        # 只获取当前用户的标签
        tags = FlavorTag.objects.filter(user=request.user).order_by('name')

        # 计算每个标签的使用次数
        result = []
        for tag in tags:
            # 统计风味标签在咖啡豆中的使用次数
            beans_count = tag.coffee_beans.count()

            # 统计风味标签在冲煮记录中的使用次数
            records_count = BrewingRecord.objects.filter(
                user=request.user,
                flavor_tags=tag
            ).count()

            # 总使用次数
            usage_count = beans_count + records_count

            result.append({
                'id': tag.id,
                'name': tag.name,
                'usage_count': usage_count
            })

        return JsonResponse(result, safe=False)
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            tag_name = data.get('name', '').strip()

            if not tag_name:
                return JsonResponse({'error': '标签名称不能为空'}, status=400)

            # 检查当前用户是否已有相同标签（不区分大小写）
            existing_tag = FlavorTag.objects.filter(
                user=request.user,
                name__iexact=tag_name
            ).first()

            if existing_tag:
                return JsonResponse({'id': existing_tag.id, 'name': existing_tag.name, 'usage_count': 0})

            # 创建新标签，关联到当前用户
            new_tag = FlavorTag.objects.create(
                name=tag_name,
                user=request.user
            )
            return JsonResponse({'id': new_tag.id, 'name': new_tag.name, 'usage_count': 0})

        except json.JSONDecodeError:
            return JsonResponse({'error': '无效的 JSON 数据'}, status=400)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': '不支持的请求方法'}, status=405)

@login_required
def delete_flavor_tag(request, tag_id):
    """
    删除用户的风味标签
    仅当标签未被使用时可以删除
    """
    if request.method != 'DELETE':
        return JsonResponse({'error': '仅支持DELETE请求'}, status=405)

    try:
        # 获取标签并确保属于当前用户
        tag = get_object_or_404(FlavorTag, id=tag_id, user=request.user)

        # 检查标签是否已被使用
        beans_count = tag.coffee_beans.count()
        records_count = BrewingRecord.objects.filter(user=request.user, flavor_tags=tag).count()

        if beans_count > 0 or records_count > 0:
            return JsonResponse({
                'error': '无法删除已使用的标签',
                'message': f'该标签已被{beans_count}个咖啡豆和{records_count}条冲煮记录使用'
            }, status=400)

        # 删除标签
        tag.delete()
        return JsonResponse({'success': True}, status=200)

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def archive_coffee_bean(request, bean_id):
    if request.method == 'POST':
        try:
            bean = CoffeeBean.objects.get(id=bean_id, user=request.user)
            bean.is_archived = True
            # 如果是首选咖啡豆，取消首选状态
            if bean.is_favorite:
                bean.is_favorite = False
            bean.save()

            # 清除缓存
            invalidate_bean_cache(request.user.id, bean.id)

            return JsonResponse({'status': 'success'})
        except CoffeeBean.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '咖啡豆不存在'}, status=404)
    return JsonResponse({'status': 'error', 'message': '方法不允许'}, status=405)

@login_required
def unarchive_coffee_bean(request, bean_id):
    bean = get_object_or_404(CoffeeBean, id=bean_id, user=request.user)
    bean.is_archived = False
    bean.save()

    # 清除缓存
    invalidate_bean_cache(request.user.id, bean.id)

    return JsonResponse({'status': 'success'})

@login_required
def archive_equipment(request, equipment_id):
    if request.method == 'POST':
        try:
            equipment = Equipment.objects.get(id=equipment_id, user=request.user)
            equipment.is_archived = True
            # 如果是首选设备，取消首选状态
            if equipment.is_favorite:
                equipment.is_favorite = False
            equipment.save()

            # 清除设备缓存
            from my.cache_utils import invalidate_equipment_cache
            invalidate_equipment_cache(request.user.id, equipment_id)

            # 清除iOS端缓存
            try:
                from iosapp.cache_utils import invalidate_ios_cache
                invalidate_ios_cache('equipment_list', request.user.id)
            except ImportError:
                # 如果无法导入iOS缓存模块，记录日志但不中断操作
                logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {request.user.id}, 设备ID: {equipment_id}")

            return JsonResponse({'status': 'success'})
        except Equipment.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '设备不存在'}, status=404)
    return JsonResponse({'status': 'error', 'message': '方法不允许'}, status=405)

@login_required
def unarchive_equipment(request, equipment_id):
    if request.method == 'POST':
        try:
            equipment = Equipment.objects.get(id=equipment_id, user=request.user)
            equipment.is_archived = False
            equipment.save()

            # 清除设备缓存
            from my.cache_utils import invalidate_equipment_cache
            invalidate_equipment_cache(request.user.id, equipment_id)

            # 清除iOS端缓存
            try:
                from iosapp.cache_utils import invalidate_ios_cache
                invalidate_ios_cache('equipment_list', request.user.id)
            except ImportError:
                # 如果无法导入iOS缓存模块，记录日志但不中断操作
                logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {request.user.id}, 设备ID: {equipment_id}")

            return JsonResponse({'status': 'success'})
        except Equipment.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '设备不存在'}, status=404)
    return JsonResponse({'status': 'error', 'message': '方法不允许'}, status=405)

@login_required
@ratelimit(key='user', rate='1/d')
def export_brewing_records(request):
    was_limited = getattr(request, 'limited', False)
    if was_limited:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return HttpResponse(status=403)
        else:
            # 如果是直接访问，重定向回主页
            return redirect('brewlog')

    # 设置导出时间缓存，使用当天23:59:59作为过期时间
    cache_key = f'export_limit_{request.user.id}'
    tz = ZoneInfo('Asia/Shanghai')
    now = timezone.now().astimezone(tz)
    # 计算当天23:59:59的时间
    end_of_day = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    # 计算从现在到当天结束的秒数
    seconds_until_end_of_day = int((end_of_day - now).total_seconds())
    # 设置缓存，过期时间为当天结束
    cache.set(cache_key, now, timeout=seconds_until_end_of_day)

    # 生成文件名（包含时间戳）
    filename = f"咖啡札记_{now.strftime('%Y%m%d_%H%M%S')}.csv"

    # 对中文文件名进行 URL 编码
    from urllib.parse import quote
    encoded_filename = quote(filename)

    # 生成CSV响应
    response = HttpResponse(
        content_type='text/csv',
        headers={
            # 同时提供 UTF-8 编码的文件名和 ASCII 文件名
            'Content-Disposition': f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}',
            'Access-Control-Expose-Headers': 'Content-Disposition',
            # 添加缓存控制头，防止浏览器缓存
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        },
    )
    response.write('\ufeff'.encode('utf-8'))

    writer = csv.writer(response)
    writer.writerow(['ID', '冲煮器具', '磨豆机', '咖啡豆', '研磨设置',
                    '粉重', '液重', '水温', '评分', '萃取时间',
                    '记录时间', '备注', '详细步骤', '用水',
                    '室温', '环境湿度',
                    '风味标签', '香气', '酸质', '甜度', '醇厚', '余韵'])

    brewing_records = BrewingRecord.objects.filter(user=request.user).values(
        'id', 'brewing_equipment__name', 'grinding_equipment__name',
        'coffee_bean__name', 'grind_size', 'dose_weight', 'yield_weight',
        'water_temperature', 'rating_level', 'brewing_time', 'created_at',
        'notes', 'steps', 'water_quality', 'room_temperature', 'room_humidity'
    ).prefetch_related('flavor_tags')

    for record in brewing_records:
        # 获取风味标签
        flavor_tags = BrewingRecord.objects.get(id=record['id']).flavor_tags.all()
        flavor_tags_str = '、'.join([tag.name for tag in flavor_tags]) if flavor_tags else ''

        # 获取品鉴维度
        record_obj = BrewingRecord.objects.get(id=record['id'])

        writer.writerow([
            record['id'],
            record['brewing_equipment__name'],
            record['grinding_equipment__name'],
            record['coffee_bean__name'],
            record['grind_size'],
            record['dose_weight'],
            record['yield_weight'],
            record['water_temperature'],
            record['rating_level'],
            record['brewing_time'],
            record['created_at'],
            record['notes'],
            record['steps'],
            record['water_quality'],
            record['room_temperature'],
            record['room_humidity'],
            flavor_tags_str,
            record_obj.get_aroma_display(),
            record_obj.get_acidity_display(),
            record_obj.get_sweetness_display(),
            record_obj.get_body_display(),
            record_obj.get_aftertaste_display()
        ])

    return response

@login_required
def toggle_favorite_equipment(request, equipment_id):
    equipment = get_object_or_404(Equipment, id=equipment_id, user=request.user)

    # 如果当前设备已经是首选，则取消首选
    if equipment.is_favorite:
        equipment.is_favorite = False
    else:
        # 如果当前设备已归档，先取消归档
        if equipment.is_archived:
            equipment.is_archived = False
            # 记录取消归档操作
            logger.info(f"设置首选时自动取消归档：用户 {request.user.id}, 设备 {equipment.id} ({equipment.name})")

        # 取消同类型其他设备的首选状态
        Equipment.objects.filter(
            user=request.user,
            type=equipment.type,
            is_favorite=True
        ).update(is_favorite=False)

        # 设置当前设备为首选
        equipment.is_favorite = True

    equipment.save()

    # 清除设备缓存
    invalidate_equipment_cache(request.user.id, equipment_id)

    return JsonResponse({
        'status': 'success',
        'is_favorite': equipment.is_favorite
    })

@login_required
def toggle_favorite_bean(request, bean_id):
    bean = get_object_or_404(CoffeeBean, id=bean_id, user=request.user)

    # 如果当前咖啡豆已经是首选，则取消首选
    if bean.is_favorite:
        bean.is_favorite = False
    else:
        # 如果当前咖啡豆已归档，先取消归档
        if bean.is_archived:
            bean.is_archived = False

        # 取消其他咖啡豆的首选状态
        CoffeeBean.objects.filter(
            user=request.user,
            is_favorite=True
        ).update(is_favorite=False)

        # 设置当前咖啡豆为首选
        bean.is_favorite = True

    bean.save()
    invalidate_bean_cache(request.user.id, bean.id)
    return JsonResponse({
        'status': 'success',
        'is_favorite': bean.is_favorite
    })

@login_required
def equipment_list(request):
    # 获取筛选参数
    brand = request.GET.get('brand')
    equip_type = request.GET.get('type')
    sort_by = request.GET.get('sort_by', 'type')  # 默认按类型排序

    # 基础查询集
    equipment_list = Equipment.objects.filter(
        user=request.user,
        is_deleted=False
    )

    # 应用品牌筛选
    if brand:
        equipment_list = equipment_list.filter(brand=brand)

    # 应用类型筛选
    if equip_type:
        equipment_list = equipment_list.filter(type=equip_type)

    # 分离活跃和已归档的设备
    active_equipment = equipment_list.filter(is_archived=False)
    archived_equipment = equipment_list.filter(is_archived=True)

    # 初始化分组变量
    active_groups = {}
    archived_groups = {}
    group_type = 'time'  # 默认按时间分组

    # 根据排序方式处理数据
    if sort_by and sort_by.startswith('brand'):
        order_field = 'brand' if sort_by == 'brand' else '-brand'
        active_equipment = active_equipment.order_by(order_field)
        archived_equipment = archived_equipment.order_by(order_field)

        # 按品牌分组
        def group_by_brand(equipment):
            groups = {}
            for equip in equipment:
                brand_key = equip.brand or '未知品牌'
                if brand_key not in groups:
                    groups[brand_key] = []
                groups[brand_key].append(equip)
            return dict(sorted(groups.items(), reverse=(sort_by == 'brand_desc')))

        active_groups = group_by_brand(active_equipment)
        archived_groups = group_by_brand(archived_equipment)
        group_type = 'brand'

    elif sort_by and sort_by.startswith('type'):
        order_field = 'type' if sort_by == 'type' else '-type'
        active_equipment = active_equipment.order_by(order_field)
        archived_equipment = archived_equipment.order_by(order_field)

        # 按类型分组
        def group_by_type(equipment):
            groups = {}
            for equip in equipment:
                type_key = equip.get_type_display()
                if type_key not in groups:
                    groups[type_key] = []
                groups[type_key].append(equip)
            return dict(sorted(groups.items(), reverse=(sort_by == 'type_desc')))

        active_groups = group_by_type(active_equipment)
        archived_groups = group_by_type(archived_equipment)
        group_type = 'type'

    else:  # 默认按时间排序
        order_field = '-created_at' if sort_by in [None, 'time'] else 'created_at'
        active_equipment = active_equipment.order_by(order_field)
        archived_equipment = archived_equipment.order_by(order_field)

        # 按月份分组
        def group_by_month(equipment):
            groups = {}
            for equip in equipment:
                month_key = equip.created_at.strftime('%Y年%m月')
                if month_key not in groups:
                    groups[month_key] = []
                groups[month_key].append(equip)
            return dict(sorted(groups.items(), reverse=(sort_by in [None, 'time'])))

        active_groups = group_by_month(active_equipment)
        archived_groups = group_by_month(archived_equipment)
        group_type = 'month'

    # 获取所有品牌和类型用于筛选
    all_brands = equipment_list.exclude(brand__isnull=True).exclude(
        brand__exact='').values_list('brand', flat=True).distinct()

    context = {
        'active_groups': active_groups,
        'archived_groups': archived_groups,
        'active_equipment': active_equipment,
        'archived_equipment': archived_equipment,
        'all_brands': all_brands,
        'equipment_types': Equipment.EQUIPMENT_TYPES,
        'current_brand': brand,
        'current_type': equip_type,
        'current_sort': sort_by,
    }

    return render(request, 'my/brewlog/equipment_list.html', context)

@login_required
@cache_response('bean_list', timeout=CACHE_TIMEOUT['MEDIUM'])
def bean_list(request):
    coffee_beans = CoffeeBean.objects.filter(user=request.user)

    # 获取筛选参数
    roast_level = request.GET.get('roast_level')
    process = request.GET.get('process')
    rating_range = request.GET.get('rating_range')
    sort_by = request.GET.get('sort_by', 'time')  # 默认按时间倒序
    roaster = request.GET.get('roaster')  # 新增：烘焙商筛选
    variety = request.GET.get('variety')  # 新增：豆种筛选

    # 构建基础查询集
    active_beans = coffee_beans.filter(
        is_archived=False,
        is_deleted=False
    ).annotate(
        avg_rating=Avg('brewingrecord__rating_level'),
        brew_count=Count('brewingrecord')
    )

    archived_beans = coffee_beans.filter(
        is_archived=True,
        is_deleted=False
    ).annotate(
        avg_rating=Avg('brewingrecord__rating_level'),
        brew_count=Count('brewingrecord')
    )

    # 获取所有处理法，包括拼配组件中的处理法
    processes = set()
    # 从咖啡豆主表获取处理法
    processes.update(
        coffee_beans.exclude(process__isnull=True)
        .exclude(process__exact='')
        .values_list('process', flat=True)
        .distinct()
    )
    # 从拼配组件获取处理法
    processes.update(
        BlendComponent.objects.filter(coffee_bean__user=request.user)
        .exclude(process__isnull=True)
        .exclude(process__exact='')
        .values_list('process', flat=True)
        .distinct()
    )

    # 新增：获取所有烘焙商
    roasters = coffee_beans.exclude(roaster__isnull=True) \
                          .exclude(roaster__exact='') \
                          .values_list('roaster', flat=True) \
                          .distinct() \
                          .order_by('roaster')

    # 新增：获取所有豆种，包括拼配组件中的豆种
    varieties = set()
    # 从咖啡豆主表获取豆种
    varieties.update(
        coffee_beans.exclude(variety__isnull=True)
        .exclude(variety__exact='')
        .values_list('variety', flat=True)
        .distinct()
    )
    # 从拼配组件获取豆种
    varieties.update(
        BlendComponent.objects.filter(coffee_bean__user=request.user)
        .exclude(variety__isnull=True)
        .exclude(variety__exact='')
        .values_list('variety', flat=True)
        .distinct()
    )
    # 转为有序列表
    varieties = sorted(varieties)

    def normalize_process(p):
        """标准化处理法字符串，处理特殊字符"""
        if not p:
            return ''
        p = p.strip()
        # 将特殊字符替换为URL友好的格式
        replacements = {
            '+': '_plus_',
            '&': '_and_',
            '/': '_slash_',
            '\\': '_backslash_',
            ' ': '_space_',
            '，': '_comma_',
            ',': '_comma_',
            '、': '_pause_',
            '|': '_or_',
        }
        result = p
        for old, new in replacements.items():
            result = result.replace(old, new)
        return result

    def denormalize_process(p):
        """还原处理法字符串中的特殊字符"""
        if not p:
            return ''
        replacements = {
            '_plus_': '+',
            '_and_': '&',
            '_slash_': '/',
            '_backslash_': '\\',
            '_space_': ' ',
            '_comma_': '，',
            '_pause_': '、',
            '_or_': '|',
        }
        result = p
        for old, new in replacements.items():
            result = result.replace(old, new)
        return result

    # 处理特殊字符，将处理法标准化
    processes = {normalize_process(p) for p in processes if p and p.strip()}

    # 应用筛选条件
    if roast_level:
        # 对于拼配豆，检查其组件的烘焙度
        # 1. 找出所有拼配豆ID
        blend_ids = coffee_beans.filter(
            type='BLEND',
            blend_components__isnull=False  # 确保有组件
        ).distinct().values_list('id', flat=True)

        # 2. 对每个拼配豆，检查其组件的烘焙度
        if blend_ids:
            # 找出所有组件都符合烘焙度条件的拼配豆
            valid_blend_beans = coffee_beans.filter(
                id__in=blend_ids
            ).annotate(
                total_components=Count('blend_components'),
                matching_components=Count(
                    'blend_components',
                    filter=Q(blend_components__roast_level=roast_level)
                )
            ).filter(
                total_components=F('matching_components'),  # 所有组件都必须匹配
                total_components__gt=0  # 确保至少有一个组件
            )
        else:
            valid_blend_beans = coffee_beans.none()

        # 合并单品和符合条件的拼配豆的查询结果
        active_beans = (active_beans.filter(
            Q(type='SINGLE', roast_level=roast_level) |  # 单品豆直接匹配烘焙度
            Q(id__in=valid_blend_beans)  # 拼配豆必须所有组件都匹配
        )).distinct()
        archived_beans = (archived_beans.filter(
            Q(type='SINGLE', roast_level=roast_level) |  # 单品豆直接匹配烘焙度
            Q(id__in=valid_blend_beans)  # 拼配豆必须所有组件都匹配
        )).distinct()

    if process:
        # 标准化处理法参数
        normalized_process = normalize_process(process)
        # 对于拼配豆，检查其组件的处理法
        blend_beans = coffee_beans.filter(
            type='BLEND',
            blend_components__process__iexact=denormalize_process(normalized_process)
        )
        # 合并单品和拼配豆的查询结果
        active_beans = (active_beans.filter(process__iexact=denormalize_process(normalized_process)) |
                       active_beans.filter(id__in=blend_beans)).distinct()
        archived_beans = (archived_beans.filter(process__iexact=denormalize_process(normalized_process)) |
                         archived_beans.filter(id__in=blend_beans)).distinct()

    # 新增：按烘焙商筛选
    if roaster:
        active_beans = active_beans.filter(roaster=roaster).distinct()
        archived_beans = archived_beans.filter(roaster=roaster).distinct()

    # 新增：按豆种筛选
    if variety:
        # 对于拼配豆，检查其组件的豆种
        blend_beans = coffee_beans.filter(
            type='BLEND',
            blend_components__variety__iexact=variety
        )
        # 合并单品和拼配豆的查询结果
        active_beans = (active_beans.filter(variety__iexact=variety) |
                       active_beans.filter(id__in=blend_beans)).distinct()
        archived_beans = (archived_beans.filter(variety__iexact=variety) |
                         archived_beans.filter(id__in=blend_beans)).distinct()

    if rating_range:
        try:
            min_rating, max_rating = map(int, rating_range.split('-'))
            active_beans = active_beans.filter(avg_rating__gte=min_rating, avg_rating__lt=max_rating)
            archived_beans = archived_beans.filter(avg_rating__gte=min_rating, avg_rating__lt=max_rating)
        except ValueError:
            pass

    # 应用排序并分组
    def get_rating_group(rating):
        if rating is None:
            return 0
        elif rating > 8:
            return 5
        elif rating > 6:
            return 4
        elif rating > 4:
            return 3
        elif rating > 2:
            return 2
        else:
            return 1

    def get_rating_hearts(rating_group):
        if rating_group == 0:
            return '未评分'
        return '♥' * rating_group

    # 默认按时间排序和分组
    if not sort_by or sort_by.startswith('time'):
        order_field = '-created_at' if sort_by in [None, 'time'] else 'created_at'
        active_beans = active_beans.order_by(order_field)
        archived_beans = archived_beans.order_by(order_field)

        # 按月份分组
        def group_by_month(beans):
            groups = {}
            for bean in beans:
                month_key = bean.created_at.strftime('%Y年%m月')
                if month_key not in groups:
                    groups[month_key] = []
                groups[month_key].append(bean)
            return dict(sorted(groups.items(), reverse=(sort_by in [None, 'time'])))

        active_groups = group_by_month(active_beans)
        archived_groups = group_by_month(archived_beans)
        group_type = 'month'

    elif sort_by.startswith('rating'):
        order_field = '-avg_rating' if sort_by == 'rating' else 'avg_rating'
        active_beans = active_beans.order_by(order_field)
        archived_beans = archived_beans.order_by(order_field)

        # 按评分区间分组
        def group_by_rating(beans):
            groups = {
                5: {'name': '♥♥♥♥♥ (8.1-10分)', 'beans': []},
                4: {'name': '♥♥♥♥ (6.1-8分)', 'beans': []},
                3: {'name': '♥♥♥ (4.1-6分)', 'beans': []},
                2: {'name': '♥♥ (2.1-4分)', 'beans': []},
                1: {'name': '♥ (0-2分)', 'beans': []},
                0: {'name': '未评分', 'beans': []}
            }
            for bean in beans:
                rating_group = get_rating_group(bean.avg_rating)
                groups[rating_group]['beans'].append(bean)
            return {k: v for k, v in groups.items() if v['beans']}  # 只返回有数据的组

        active_groups = group_by_rating(active_beans)
        archived_groups = group_by_rating(archived_beans)
        group_type = 'rating'

    elif sort_by.startswith('roaster'):
        order_field = 'roaster' if sort_by == 'roaster' else '-roaster'
        active_beans = active_beans.order_by(order_field)
        archived_beans = archived_beans.order_by(order_field)

        # 按豆商分组
        def group_by_roaster(beans):
            groups = {}
            for bean in beans:
                roaster = bean.roaster or '未知豆商'
                if roaster not in groups:
                    groups[roaster] = {'name': roaster, 'beans': []}
                groups[roaster]['beans'].append(bean)
            return dict(sorted(groups.items(), reverse=(sort_by == 'roaster_desc')))

        active_groups = group_by_roaster(active_beans)
        archived_groups = group_by_roaster(archived_beans)
        group_type = 'roaster'

    # 获取统计数据，将其传递到上下文中
    active_stats = CoffeeBean.get_stats(request.user)

    # 为每个咖啡豆添加标记
    # 定义一个函数来处理单个组
    def process_bean_group(group_data):
        beans = group_data.get('beans') if isinstance(group_data, dict) and 'beans' in group_data else group_data
        if not isinstance(beans, list):
            return  # 如果不是列表，跳过处理

        for bean in beans:
            if not isinstance(bean, CoffeeBean):
                continue  # 跳过非CoffeeBean对象

            bean.is_resting = False
            bean.is_in_use = False
            bean.is_out_of_stock = False

            if bean.roast_date and bean.rest_period_min:
                days_since = (timezone.now().date() - bean.roast_date).days
                if days_since < bean.rest_period_min:
                    bean.is_resting = True

            if bean.bag_remain is not None:
                if bean.bag_remain <= 0:
                    bean.is_out_of_stock = True
                elif bean.bag_remain > 0 and not bean.is_resting:
                    bean.is_in_use = True

    # 处理活跃和归档的咖啡豆组
    for group_key, group_data in active_groups.items():
        process_bean_group(group_data)

    for group_key, group_data in archived_groups.items():
        process_bean_group(group_data)

    context = {
        'active_groups': active_groups,
        'archived_groups': archived_groups,
        'group_type': group_type,
        'roast_levels': CoffeeBean.ROAST_LEVEL_CHOICES,
        'processes': [(p, denormalize_process(p)) for p in sorted(processes)],  # 传递标准化和原始处理法
        'current_roast_level': roast_level,
        'current_process': normalized_process if process else None,
        'current_rating_range': rating_range,
        'current_sort': sort_by,
        'active_beans': active_beans,
        'archived_beans': archived_beans,
        # 新增：传递烘焙商和豆种数据
        'roasters': roasters,
        'varieties': varieties,
        'current_roaster': roaster,
        'current_variety': variety,
        'active_stats': active_stats,
    }

    # 计算统计数据
    active_stats = CoffeeBean.get_stats(request.user)

    context.update({
        'active_stats': active_stats,
    })

    return render(request, 'my/brewlog/bean_list.html', context)

@login_required
def view_equipment(request, equipment_id):
    equipment = get_object_or_404(Equipment, id=equipment_id)
    if equipment.user != request.user:
        raise PermissionDenied

    # 使用模型层的方法获取使用次数和最后使用时间
    # 这些值通过模型的属性自动获取，无需显式赋值

    context = {
        'equipment': equipment,
        'user': request.user,
    }
    return render(request, 'my/brewlog/view_equipment.html', context)

@login_required
@cache_response('bean_details', timeout=CACHE_TIMEOUT['SHORT'])
def view_bean(request, bean_id):
    # 使用 select_related 和 prefetch_related 优化查询
    bean = get_object_or_404(
        CoffeeBean.objects.prefetch_related(
            'blend_components',
            'flavor_tags'
        ).select_related(
            'user'
        ),
        id=bean_id
    )

    if bean.user != request.user:
        raise PermissionDenied

    # 计算风味特征准确度
    flavor_accuracy, tasted_flavors = bean.calculate_flavor_accuracy()

    # 获取拼配咖啡组件属性信息
    blend_components_attrs = bean.check_blend_components_attributes()

    # 获取使用数据（使用次数、最后使用时间、平均评分等）
    usage_data = bean.get_usage_data()
    bean.usage_count = usage_data['usage_count']
    bean.last_used = usage_data['last_used']
    bean.days_since_last_use = usage_data['days_since_last_use']
    bean.avg_rating = usage_data['avg_rating']

    # 获取最常用的冲煮器具
    most_used_equipment = bean.get_most_used_equipment()

    # 计算剩余可用次数
    remaining_uses = bean.calculate_remaining_uses()

    # 获取回购次数
    bean.occurrences_count = bean.occurrences.count()

    # 计算平均回购周期和回购记录
    avg_repurchase_interval, occurrences = bean.calculate_repurchase_interval()

    # 获取品鉴数据汇总
    dimensions_avg, tasting_count, unique_flavor_tags = bean.get_tasting_summary()

    context = {
        'bean': bean,
        'user': request.user,
        'most_used_equipment': most_used_equipment,
        'remaining_uses': remaining_uses,
        'occurrences_count': bean.occurrences_count,
        'occurrences': occurrences,
        'avg_repurchase_interval': avg_repurchase_interval,
        'flavor_accuracy': flavor_accuracy,
        'tasted_flavors': tasted_flavors,
        'dimensions_avg': dimensions_avg,
        'tasting_count': tasting_count,
        'unique_flavor_tags': unique_flavor_tags,
    }

    # 如果是拼配咖啡，添加组件属性检查结果到上下文
    if bean.type == 'BLEND':
        context.update(blend_components_attrs)

    return render(request, 'my/brewlog/view_bean.html', context)

@login_required
def add_bean_occurrence(request, bean_id):
    bean = get_object_or_404(CoffeeBean, id=bean_id, user=request.user)

    if request.method == 'POST':
        # 如果咖啡豆已归档，先取消归档
        if bean.is_archived:
            bean.is_archived = False
            bean.save()
            # 记录取消归档操作
            logger.info(f"回购时自动取消归档：用户 {request.user.id}, 咖啡豆 {bean.id} ({bean.name})")
            # 清除缓存
            invalidate_bean_cache(request.user.id, bean.id)

        form = BeanOccurrenceForm(request.POST)
        if form.is_valid():
            occurrence = form.save(commit=False)
            occurrence.coffee_bean = bean

            # 检查是否为第一次回购
            is_first_repurchase = not bean.occurrences.exists()

            # 如果是第一次回购，保存当前值为初始值
            if is_first_repurchase:
                # 记录保存前的值
                logger.info(f"回购前bean.created_at: {bean.created_at}")

                # 保存当前值为初始值 - 注意：模型的save方法会保存初始值，
                # 但只在第一次创建时，所以我们需要手动设置
                bean.initial_bag_weight = bean.bag_weight
                bean.initial_bag_remain = bean.bag_remain
                bean.initial_purchase_price = bean.purchase_price
                bean.initial_roast_date = bean.roast_date
                # 显式保存当前的created_at到initial_created_at
                bean.initial_created_at = bean.created_at
                bean.initial_rest_period_min = bean.rest_period_min
                bean.initial_rest_period_max = bean.rest_period_max

                # 保存bean的变更
                bean.save()

                # 重新获取bean对象以验证保存结果
                refreshed_bean = CoffeeBean.objects.get(id=bean.id)
                logger.info(f"保存初始值后: initial_created_at={refreshed_bean.initial_created_at}, created_at={refreshed_bean.created_at}")

            # 保存回购记录
            occurrence.save()

            # 更新咖啡豆的包装信息
            bean.bag_weight = occurrence.bag_weight
            bean.bag_remain = occurrence.bag_remain
            bean.purchase_price = occurrence.purchase_price
            bean.roast_date = occurrence.roast_date
            # 更新咖啡豆的创建时间为最新回购时间，并移除微秒
            bean.created_at = occurrence.created_at.replace(microsecond=0)
            bean.rest_period_min = occurrence.rest_period_min  # 添加养豆期
            bean.rest_period_max = occurrence.rest_period_max  # 添加养豆期
            bean.save()

            # 添加：清除统计数据缓存
            invalidate_hindsight_cache(request.user.id)

            # 再次记录保存后的状态
            final_bean = CoffeeBean.objects.get(id=bean.id)
            logger.info(f"最终状态: initial_created_at={final_bean.initial_created_at}, created_at={final_bean.created_at}")

            return JsonResponse({'status': 'success'})
        else:
            return JsonResponse({'status': 'error', 'errors': form.errors}, status=400)
    else:
        # 返回默认值的JSON响应
        now = timezone.now()
        if timezone.is_naive(now):
            now = timezone.make_aware(now)
        current_time = timezone.localtime(now).strftime('%Y-%m-%dT%H:%M')
        try:
            response_data = {
                'bag_weight': str(bean.bag_weight) if bean.bag_weight else '',
                'bag_remain': str(bean.bag_weight) if bean.bag_weight else '',  # 新包装默认是满的
                'purchase_price': str(bean.purchase_price) if bean.purchase_price else '',
                'roast_date': '',  # 烘焙日期默认为空
                'created_at': current_time,  # 购买日期默认为当前时间
                'rest_period_min': str(bean.rest_period_min) if bean.rest_period_min else '',  # 添加养豆期
                'rest_period_max': str(bean.rest_period_max) if bean.rest_period_max else '',  # 添加养豆期
            }

            return JsonResponse(response_data)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

@login_required
def edit_bean_occurrence(request, occurrence_id):
    try:
        occurrence = get_object_or_404(BeanOccurrence, id=occurrence_id)

        if occurrence.coffee_bean.user != request.user:
            raise PermissionDenied

        if request.method == 'POST':
            form = BeanOccurrenceForm(request.POST, instance=occurrence)
            if form.is_valid():
                form.save()

                try:
                    latest_occurrence = occurrence.coffee_bean.occurrences.order_by('-created_at').first()
                    if occurrence == latest_occurrence:
                        bean = occurrence.coffee_bean
                        bean.bag_weight = occurrence.bag_weight
                        bean.bag_remain = occurrence.bag_remain
                        bean.purchase_price = occurrence.purchase_price
                        bean.roast_date = occurrence.roast_date
                        bean.created_at = occurrence.created_at
                        bean.rest_period_min = occurrence.rest_period_min  # 添加养豆期
                        bean.rest_period_max = occurrence.rest_period_max  # 添加养豆期
                        bean.save()
                except Exception as e:
                    bean = occurrence.coffee_bean
                    bean.bag_weight = occurrence.bag_weight
                    bean.bag_remain = occurrence.bag_remain
                    bean.purchase_price = occurrence.purchase_price
                    bean.roast_date = occurrence.roast_date
                    bean.created_at = occurrence.created_at
                    bean.rest_period_min = occurrence.rest_period_min  # 添加养豆期
                    bean.rest_period_max = occurrence.rest_period_max  # 添加养豆期
                    bean.save()

                # 清除相关缓存
                invalidate_bean_cache(
                    user_id=request.user.id,
                    bean_id=occurrence.coffee_bean.id
                )
                # 添加：清除统计数据缓存
                invalidate_hindsight_cache(request.user.id)

                return JsonResponse({'status': 'success'})
            else:
                return JsonResponse({'status': 'error', 'errors': form.errors}, status=400)
        else:
            # 返回当前记录的数据
            try:
                # 确保 created_at 有时区信息
                created_at = occurrence.created_at
                if timezone.is_naive(created_at):
                    created_at = timezone.make_aware(created_at)

                data = {
                    'bag_weight': str(occurrence.bag_weight) if occurrence.bag_weight else '',
                    'bag_remain': str(occurrence.bag_remain) if occurrence.bag_remain else '',
                    'purchase_price': str(occurrence.purchase_price) if occurrence.purchase_price else '',
                    'roast_date': occurrence.roast_date.strftime('%Y-%m-%d') if occurrence.roast_date else '',
                    'created_at': timezone.localtime(created_at).strftime('%Y-%m-%dT%H:%M') if created_at else '',
                    'rest_period_min': str(occurrence.rest_period_min) if occurrence.rest_period_min else '',  # 添加养豆期
                    'rest_period_max': str(occurrence.rest_period_max) if occurrence.rest_period_max else '',  # 添加养豆期
                }
                return JsonResponse(data)
            except Exception as e:
                return JsonResponse({'error': str(e)}, status=500)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def delete_bean_occurrence(request, occurrence_id):
    occurrence = get_object_or_404(BeanOccurrence, id=occurrence_id)
    if occurrence.coffee_bean.user != request.user:
        raise PermissionDenied

    if request.method == 'POST':
        bean = occurrence.coffee_bean
        occurrence.delete()

        # 如果还有其他回购记录，更新咖啡豆的包装信息为最新的记录
        latest_occurrence = bean.occurrences.first()
        if latest_occurrence:
            bean.bag_weight = latest_occurrence.bag_weight
            bean.bag_remain = latest_occurrence.bag_remain
            bean.purchase_price = latest_occurrence.purchase_price
            bean.roast_date = latest_occurrence.roast_date
            bean.created_at = latest_occurrence.created_at
            bean.rest_period_min = latest_occurrence.rest_period_min
            bean.rest_period_max = latest_occurrence.rest_period_max
            bean.save()
        else:
            # 如果没有回购记录了，恢复到初始值
            bean.bag_weight = bean.initial_bag_weight
            bean.bag_remain = bean.initial_bag_remain
            bean.purchase_price = bean.initial_purchase_price
            bean.roast_date = bean.initial_roast_date
            # initial_created_at可能为空，如果为空则保持当前值不变
            if bean.initial_created_at:
                bean.created_at = bean.initial_created_at
            bean.rest_period_min = bean.initial_rest_period_min
            bean.rest_period_max = bean.initial_rest_period_max
            bean.save()

        # 添加：清除统计数据缓存
        invalidate_hindsight_cache(request.user.id)

        return JsonResponse({'status': 'success'})

    return JsonResponse({'status': 'error', 'message': '方法不允许'}, status=405)

@login_required
def remove_favorite_by_id(request, favorite_id):
    if request.method not in ['DELETE', 'POST']:
        return JsonResponse({'status': 'error', 'message': '方法不允许'}, status=405)

    favorite = get_object_or_404(Favorite, id=favorite_id, user=request.user)
    content_type = favorite.content_type
    favorite.delete()

    if request.headers.get('HX-Request'):
        # 获取最新的计数
        counts = request.user.favorites.values('content_type__model')\
            .annotate(count=Count('id'))
        counts_dict = {item['content_type__model']: item['count'] for item in counts}

        # 返回包含计数的 HTML 片段
        return HttpResponse(f"""
            <div id="favorite-{favorite_id}"></div>
            <div id="count-{content_type.model}" hx-swap-oob="true">
                {counts_dict.get(content_type.model, 0)}
            </div>
        """)
    return JsonResponse({'status': 'success'})

@login_required
def get_available_years(request):
    """获取有冲煮记录的年份列表"""
    years_data = BrewingRecord.objects.get_available_years(request.user)
    return JsonResponse(years_data)

@login_required
def get_gadget_kit_components(request, equipment_id):
    """获取小工具组合的组件列表"""
    try:
        equipment = get_object_or_404(Equipment, id=equipment_id, user=request.user)
        gadgets = [g.id for g in equipment.gadget_components.all()]
        return JsonResponse(gadgets, safe=False)
    except Equipment.DoesNotExist:
        return JsonResponse({'error': 'Equipment not found'}, status=404)

@login_required
def get_equipment_info(request, equipment_id):
    """获取设备信息的 API 端点"""
    try:
        equipment = get_object_or_404(Equipment, id=equipment_id, user=request.user)
        data = {
            'id': equipment.id,
            'name': equipment.name,
            'type': equipment.type,
            'grind_size_preset': equipment.grind_size_preset
        }
        return JsonResponse(data)
    except Equipment.DoesNotExist:
        return JsonResponse({'error': 'Equipment not found'}, status=404)

@login_required
def brewlog_heatmap(request, year=None):
    """显示冲煮记录热力图"""
    if year is None:
        year = timezone.now().year

    try:
        calendar_result = BrewingRecord.objects.generate_calendar_data(request.user, year)
        return render(request, 'my/brewlog/brewlog_heatmap.html', {
            'calendar_data': json.dumps(calendar_result['calendar_data']),
            'current_month_index': calendar_result['current_month_index'],
            'months': calendar_result['months'],
            'weekdays': calendar_result['weekdays']
        })
    except Exception as e:
        return render(request, 'my/brewlog/brewlog_heatmap.html', {'error': True})

def handle_redis_connection(view_func):
    """处理 Redis 连接问题的装饰器"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            return view_func(request, *args, **kwargs)
        except Exception as e:  # 捕获所有Redis相关异常
            # 1. 记录详细的错误日志
            logger.error(f"View: {view_func.__name__}")
            logger.error(f"Request path: {request.path}")
            logger.error(f"Error type: {type(e).__name__}")
            logger.error(f"Error message: {str(e)}")
            logger.error("Full traceback:", exc_info=True)

            # 2. 尝试不使用缓存直接执行视图
            try:
                # 临时禁用缓存警告
                warnings.filterwarnings("ignore", category=CacheKeyWarning)

                # 直接执行视图函数
                return view_func(request, *args, **kwargs)
            except Exception as e:
                logger.error(f"Fallback execution failed: {str(e)}", exc_info=True)

                # 3. 返回友好的错误信息
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        """
                        <div class="alert alert-error">
                            <span>服务暂时繁忙，请稍后再试</span>
                            <button class="btn btn-sm" hx-get="${request.path}" hx-target="closest div">
                                重试
                            </button>
                        </div>
                        """,
                        status=503
                    )
                return render(request, '503.html', status=503)
    return wrapper

@handle_redis_connection
@login_required
@cache_response('hindsight', daily_refresh=True)
def brewing_hindsight(request):
    """冲煮记录回顾统计"""
    # 获取时间范围参数
    time_range = request.GET.get('time_range', 'week')  # 默认查看近一周
    year = request.GET.get('year')  # 新增年份参数

    # 获取有记录的年份列表
    years = BrewingRecord.objects.filter(
        user=request.user
    ).dates('created_at', 'year', order='DESC')

    years_list = [year.year for year in years]

    # 获取统计数据
    if year:
        # 如果选择了年份，使用年份的起止时间
        tz = ZoneInfo('Asia/Shanghai')
        start_date = timezone.datetime(int(year), 1, 1, tzinfo=tz)
        end_date = timezone.datetime(int(year), 12, 31, 23, 59, 59, tzinfo=tz)

        # 获取该年份的记录
        records = BrewingRecord.objects.filter(
            user=request.user,
            created_at__range=(start_date, end_date)
        )
        total_records = records.count()

        # 计算统计数据 - 修改这里
        stats = HindsightStats(request.user, year=year)  # 只传入 year 参数,不传 time_range
        stats_data = {
            'stats': stats.calculate_all_stats(),
            'total_records': total_records
        }
    else:
        # 使用常规时间范围
        stats_data = HindsightStats.get_stats(request.user, time_range)

    context = {
        'stats': stats_data['stats'],
        'total_records': stats_data['total_records'],
        'time_ranges': HindsightStats.TIME_RANGES,
        'current_range': time_range,
        'available_years': years_list,  # 添加可用年份列表
        'selected_year': year,  # 添加选中的年份
    }

    return render(request, 'my/brewlog/brewlog_hindsight.html', context)

@login_required
def compare_records(request, record1_id, record2_id):
    # 获取两条记录并确保属于当前用户
    record1 = get_object_or_404(BrewingRecord, id=record1_id, user=request.user)
    record2 = get_object_or_404(BrewingRecord, id=record2_id, user=request.user)

    # 确定 pro 和 con 记录
    if record1.rating_level > record2.rating_level:
        pro_record, con_record = record1, record2
    elif record1.rating_level < record2.rating_level:
        pro_record, con_record = record2, record1
    else:
        # 评分相同时，比较创建时间
        if record1.created_at > record2.created_at:
            pro_record, con_record = record1, record2
        else:
            pro_record, con_record = record2, record1

    return render(request, 'my/brewlog/compare_records.html', {
        'pro_record': pro_record,
        'con_record': con_record,
        'other_record': con_record,  # 添加这一行到 pro_record 的上下文
    })

def check_onboarding_status(user):
    return Equipment.needs_onboarding(user)

@login_required
def onboarding(request):
    """新手引导页面"""
    if not check_onboarding_status(request.user):
        return redirect('brewlog')

    return render(request, 'my/brewlog/brewlog_onboarding.html')

@login_required
def onboarding_submit(request):
    """处理新手引导表单提交"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '无效的请求方法'})

    try:
        data = json.loads(request.body)

        # 检查是否已经存在相关记录
        if CoffeeBean.objects.filter(user=request.user).exists():
            return JsonResponse({
                'success': False,
                'message': '您已经创建过咖啡豆记录，请直接使用现有记录或创建新记录'
            })

        if Equipment.objects.filter(
            user=request.user,
            type__in=['BREWER', 'GRINDER']
        ).exists():
            return JsonResponse({
                'success': False,
                'message': '您已经创建过设备记录，请直接使用现有记录或创建新记录'
            })

        # 创建咖啡豆记录
        bean = CoffeeBean.objects.create(
            user=request.user,
            type='SKIP',
            roaster='自家烘焙' if data['roasterType'] == 'home' else '初始豆商',
            name=data['beanName'],
            roast_level=4,
            is_favorite=True,
            created_at=timezone.now().replace(microsecond=0)
        )

        # 创建冲煮器具记录
        brewer = Equipment.objects.create(
            user=request.user,
            type='BREWER',
            name='我的冲煮器具',
            brew_method=data['brewMethod'],
            is_favorite=True,
            created_at=timezone.now().replace(microsecond=0)
        )

        # 创建磨豆机记录
        grinder_name = {
            'dedicated': '我的磨豆机',
            'built_in': '研磨一体',
            'store': '商家代磨'
        }[data['grinderType']]

        grinder = Equipment.objects.create(
            user=request.user,
            type='GRINDER',
            name=grinder_name,
            grinder_purpose='ALL_PURPOSE',
            is_favorite=True,
            created_at=timezone.now().replace(microsecond=0)
        )

        return JsonResponse({'success': True})

    except Exception as e:
        logger.error(f"新手引导提交失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'创建失败：{str(e)}'
        })

@login_required
def whats_new(request):
    """显示网站更新日志页面"""
    context = {
        'quote': CoffeeQuote.get_random_quote()
    }

    return render(request, 'my/brewlog/brewlog_whatsnew.html', context)

def generate_distinct_colors(n):
    """生成n个有区分度的颜色"""
    colors = []
    for i in range(n):
        hue = i * (360 / n)  # 均匀分布的色相
        # 生成两种颜色：实心(图例用)和半透明(日历事件用)
        base_color = f"hsl({hue} 70% 50%)"
        colors.append({
            'solid': base_color,  # 图例用
            'transparent': f"color-mix(in oklab, {base_color} 50%, transparent)"  # 日历事件用
        })
    return colors

@login_required
@cache_response(lambda request: f'bean_calendar:{request.user.id}:{request.GET.get("date", "")}',
                timeout=CACHE_TIMEOUT['SHORT'])
def bean_calendar(request):
    """咖啡豆日历视图"""
    # 获取当前年月
    today = timezone.now().date()

    # 从日期选择器获取年月
    selected_date = request.GET.get('date')
    if selected_date:
        try:
            year, month = selected_date.split('-')
            current_date = date(int(year), int(month), 1)
        except (ValueError, TypeError):
            current_date = today.replace(day=1)
    else:
        current_date = today.replace(day=1)

    # 获取当前显示月份
    month = current_date.month

    # 计算日历需要显示的日期范围
    first_day = current_date.replace(day=1)
    last_day = (first_day + timedelta(days=32)).replace(day=1) - timedelta(days=1)

    # 计算日历起始日期（包括上月残余日期）
    calendar_start = first_day - timedelta(days=first_day.weekday())
    # 计算日历结束日期（包括下月残余日期）
    calendar_end = last_day + timedelta(days=(6 - last_day.weekday()))

    # 获取活跃的咖啡豆
    active_beans = CoffeeBean.get_calendar_beans(request.user, calendar_start, calendar_end)

    # 为每个咖啡豆生成唯一颜色
    colors = generate_distinct_colors(len(active_beans))
    beans_with_colors = []
    bean_colors = {}  # 用于在事件中查找颜色

    for bean, color in zip(active_beans, colors):
        bean_info = bean.get_calendar_display_info(color=color)
        beans_with_colors.append(bean_info)
        bean_colors[bean.id] = color

    # 构建日历数据
    calendar_data = []
    current = calendar_start
    while current <= calendar_end:
        week = []
        for _ in range(7):
            day_events = {
                'date': current,
                'is_current_month': current.month == month,
                'is_today': current == today,
                'purchase_events': [],
                'roast_events': [],
                'rest_events': []
            }

            for bean in active_beans:
                color = bean_colors[bean.id]

                # 购买日期
                if bean.created_at.date() == current:
                    day_events['purchase_events'].append(bean.get_calendar_display_info(color=color))

                # 烘焙日期
                if bean.roast_date and bean.roast_date == current:
                    day_events['roast_events'].append(bean.get_calendar_display_info(color=color))

                # 最佳赏味期
                rest_start, rest_end = bean.get_rest_period_dates()
                if rest_start and rest_end and rest_start <= current <= rest_end:
                    day_events['rest_events'].append(bean.get_calendar_display_info(color=color))

            week.append(day_events)
            current += timedelta(days=1)
        calendar_data.append(week)

    context = {
        'calendar_data': calendar_data,
        'current_date': current_date,
        'prev_month': (current_date - timedelta(days=1)).replace(day=1),
        'next_month': (current_date + timedelta(days=32)).replace(day=1),
        'show_purchase': request.GET.get('show_purchase', '1') == '1',
        'show_roast': request.GET.get('show_roast', '1') == '1',
        'show_rest': request.GET.get('show_rest', '1') == '1',
        'beans': beans_with_colors
    }

    return render(request, 'my/brewlog/bean_calendar.html', context)

@login_required
def recipe_list(request):
    """配方列表页面"""
    # 获取筛选参数
    tag_id = request.GET.get('tag')
    sort_by = request.GET.get('sort_by', 'recent')  # 默认按最近使用排序

    # 获取所有配方主题
    recipes = RecipeThread.get_user_recipes(request.user)

    # 标签筛选
    if tag_id:
        try:
            tag = RecipeTag.objects.get(id=tag_id, user=request.user)
            recipes = [r for r in recipes if tag in r.tags.all()]
        except RecipeTag.DoesNotExist:
            pass

    # 排序
    if sort_by == 'count':
        recipes = sorted(recipes, key=lambda x: (-x.use_count, x.recipe_name))
    else:  # recent
        recipes = sorted(recipes, key=lambda x: (x.last_used_at or x.created_at).timestamp(), reverse=True)

    # 获取所有标签
    tags = RecipeTag.objects.filter(user=request.user).order_by('name')

    context = {
        'recipes': recipes,
        'tags': tags,
        'current_tag': tag_id,
        'current_sort': sort_by
    }

    return render(request, 'my/brewlog/recipe_list.html', context)

@login_required
def manage_recipe_tag(request):
    """管理配方标签"""
    if request.method == 'POST':
        action = request.POST.get('action')
        tag_id = request.POST.get('tag_id')
        tag_name = request.POST.get('tag_name')

        if action == 'add':
            # 创建新标签
            tag = RecipeTag.objects.create(
                user=request.user,
                name=tag_name
            )
            return JsonResponse({'status': 'success', 'tag_id': tag.id})

        elif action == 'edit':
            # 编辑标签
            try:
                tag = RecipeTag.objects.get(id=tag_id, user=request.user)
                tag.name = tag_name
                tag.save()
                # 清除配方相关缓存
                invalidate_recipe_cache(request.user.id)

                return JsonResponse({'status': 'success'})
            except RecipeTag.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': '标签不存在'})

        elif action == 'delete':
            # 删除标签
            try:
                tag = RecipeTag.objects.get(id=tag_id, user=request.user)
                # 获取所有关联的配方主题
                recipe_threads = tag.recipe_threads.all()
                # 先清除与配方的关联关系
                for recipe in recipe_threads:
                    recipe.tags.remove(tag)
                # 然后删除标签
                tag.delete()
                # 清除配方相关缓存
                invalidate_recipe_cache(request.user.id)

                return JsonResponse({'status': 'success'})
            except RecipeTag.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': '标签不存在'})

    return JsonResponse({'status': 'error', 'message': '无效请求'})

@login_required
def toggle_recipe_tag(request, recipe_id, tag_id):
    if request.method == 'POST':
        try:
            recipe = RecipeThread.objects.get(id=recipe_id, user=request.user)
            tag = RecipeTag.objects.get(id=tag_id, user=request.user)

            if tag in recipe.tags.all():
                recipe.tags.remove(tag)
                action = 'removed'
            else:
                recipe.tags.add(tag)
                action = 'added'

            # 清除配方相关缓存
            invalidate_recipe_cache(request.user.id)

            # 返回更新后的标签列表
            tags = [{'id': t.id, 'name': t.name} for t in recipe.tags.all()]

            return JsonResponse({
                'status': 'success',
                'action': action,
                'tags': tags
            })
        except (RecipeThread.DoesNotExist, RecipeTag.DoesNotExist):
            return JsonResponse({'status': 'error', 'message': '配方或标签不存在'})

    return JsonResponse({'status': 'error', 'message': '无效请求'})

@login_required
def quick_brew(request, recipe_id):
    """快速创建冲煮记录"""
    try:
        recipe = RecipeThread.objects.get(id=recipe_id, user=request.user)
        record = recipe.quick_brew()
        if record:
            return JsonResponse({
                'status': 'success',
                'redirect_url': reverse('view_record_page', args=[record.id])
            })
        return JsonResponse({'status': 'error', 'message': '无法创建记录'})
    except RecipeThread.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': '配方不存在'})

@login_required
def rename_recipe(request, recipe_id):
    """批量重命名配方"""
    if request.method == 'POST':
        try:
            recipe = RecipeThread.objects.get(id=recipe_id, user=request.user)
            new_name = request.POST.get('new_name', '').strip()

            if not new_name:
                return JsonResponse({
                    'status': 'error',
                    'message': '配方名称不能为空'
                })

            # 检查新名称是否已存在
            if RecipeThread.objects.filter(user=request.user, recipe_name=new_name).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': '已存在同名配方'
                })

            # 更新所有相关记录
            old_name = recipe.recipe_name
            BrewingRecord.objects.filter(
                user=request.user,
                recipe_name=old_name
            ).update(recipe_name=new_name)

            # 更新配方主题
            recipe.recipe_name = new_name
            recipe.save()

            # 清除配方相关缓存
            invalidate_recipe_cache(request.user.id)

            return JsonResponse({
                'status': 'success',
                'message': '重命名成功'
            })

        except RecipeThread.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': '配方不存在'
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'操作失败：{str(e)}'
            })

    return JsonResponse({
        'status': 'error',
        'message': '无效请求'
    })

@login_required
def preview_quick_brew(request, recipe_id):
    """预览速记功能"""
    try:
        recipe = RecipeThread.objects.get(id=recipe_id, user=request.user)
        params = recipe.get_latest_parameters()

        if not params:
            return JsonResponse({'status': 'error', 'message': '无法获取配方参数'})

        # 获取最近一条有步骤的记录
        latest_record_with_steps = BrewingRecord.objects.filter(
            user=request.user,
            recipe_name=recipe.recipe_name,
            steps__isnull=False  # 确保steps不为null
        ).exclude(
            steps=[]  # 排除空列表
        ).order_by('-created_at').first()

        # 复制最近一条有步骤的记录的步骤，如果没有则使用空列表
        steps = latest_record_with_steps.steps if latest_record_with_steps else []

        # 构建预览数据
        preview_data = {
            'recipe': recipe,
            'preview': {
                'recipe_name': recipe.recipe_name,
                'brewing_equipment': Equipment.objects.get(id=params['brewing_equipment']),
                'grinding_equipment': Equipment.objects.get(id=params['grinding_equipment']),
                'coffee_bean': CoffeeBean.objects.get(id=params['coffee_bean']),
                'grind_size': params['grind_size'],
                'dose_weight': params['dose_weight'],
                'yield_weight': params['yield_weight'],
                'water_temperature': params['water_temperature'],
                'brewing_time': params['brewing_time'],
                'water_quality': params['water_quality'],
                'steps': steps,
            },
            'now': timezone.now(),  # 添加当前时间
        }

        # 如果有小工具组合
        if params['gadget_kit']:
            preview_data['preview']['gadget_kit'] = Equipment.objects.get(id=params['gadget_kit'])
        # 如果有单个小工具
        elif params['gadgets']:
            preview_data['preview']['gadgets'] = Equipment.objects.filter(id__in=params['gadgets'])

        return render(request, 'my/brewlog/recipe_preview.html', preview_data)

    except RecipeThread.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': '配方不存在'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': f'操作失败：{str(e)}'})
