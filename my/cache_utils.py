from django.core.cache import cache
from functools import wraps
import json
from django.core.serializers import serialize
from django.db.models.query import QuerySet
from django.http import HttpResponse
from django_redis import get_redis_connection
import logging
from django.utils.timezone import now
from datetime import timed<PERSON>ta
from typing import Any, Optional
from django.core.serializers.json import DjangoJ<PERSON>NEncoder
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth.models import User
from .models import BlendComponent
from .constants import CACHE_TIMEOUT
import hashlib
import time
from redis.exceptions import ConnectionError, ResponseError
from django.conf import settings
import re

logger = logging.getLogger(__name__)

# 统一缓存前缀
CACHE_PREFIX = 'kfdz'  # kafeidazi 的缩写

def get_cache_key(*args):
    """
    生成统一格式的缓存键
    :param args: 缓存键的组成部分
    :return: 格式化的缓存键字符串
    """
    # 确保所有参数都转换为字符串
    key_parts = [str(arg) for arg in args]
    return f"{CACHE_PREFIX}:" + ":".join(key_parts)

def get_daily_cache_expiry():
    """获取到今天结束的剩余秒数"""
    from django.utils import timezone
    from zoneinfo import ZoneInfo

    # 使用上海时区
    tz = ZoneInfo('Asia/Shanghai')
    now = timezone.now().astimezone(tz)
    # 计算当天23:59:59的时间
    end_of_day = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    # 计算从现在到当天结束的秒数
    return int((end_of_day - now).total_seconds())

class CacheManager:
    """统一的缓存管理类"""

    def __init__(self):
        self.redis = get_redis_connection("default")
        self.fallback_cache = {}  # 内存缓存作为备用

    def get(self, *args) -> Any:
        """获取缓存，支持降级"""
        key = get_cache_key(*args)
        try:
            data = self.redis.get(key)
            return json.loads(data) if data else None
        except Exception as e:
            logger.warning(f"Redis get error: {str(e)}")
            # 降级到内存缓存
            return self.fallback_cache.get(key)

    def set(self, *args, value: Any, timeout: Optional[int] = None, daily_refresh: bool = False) -> bool:
        """
        设置缓存，支持降级
        :param args: 缓存键的组成部分
        :param value: 要缓存的值
        :param timeout: 过期时间(秒)
        :param daily_refresh: 是否每天刷新（如果为True，将在每天结束时过期）
        """
        key = get_cache_key(*args)
        try:
            # 处理不同类型的数据
            if isinstance(value, (QuerySet, HttpResponse)):
                return False

            if hasattr(value, 'to_dict'):
                value = value.to_dict()

            # 使用 DjangoJSONEncoder 处理特殊类型
            serialized_value = json.dumps(value, cls=DjangoJSONEncoder)

            # 如果设置了每日刷新，计算到今天结束的剩余时间
            if daily_refresh:
                timeout = get_daily_cache_expiry()
            elif timeout is None:
                timeout = CACHE_TIMEOUT['DEFAULT']

            with self.redis.pipeline() as pipe:
                pipe.set(key, serialized_value, ex=timeout)
                pipe.execute()

            # 同步更新内存缓存
            self.fallback_cache[key] = value
            return True

        except Exception as e:
            logger.error(f"Cache set error for key {key}: {str(e)}")
            # 降级到内存缓存
            self.fallback_cache[key] = value
            return False

    def delete_pattern(self, pattern: str) -> bool:
        """删除匹配模式的所有缓存"""
        try:
            pattern = f"{CACHE_PREFIX}:{pattern}"
            with self.redis.pipeline() as pipe:
                for key in self.redis.scan_iter(pattern):
                    pipe.delete(key)
                pipe.execute()
            return True
        except Exception as e:
            logger.error(f"Error deleting cache pattern: {str(e)}")
            return False

# 创建全局缓存管理器实例
cache_manager = CacheManager()

def cache_response(cache_key_prefix, timeout=CACHE_TIMEOUT['DEFAULT'], daily_refresh=False):
    """统一的视图缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            # 构建缓存键
            cache_parts = [
                cache_key_prefix,
                request.user.id if request.user.is_authenticated else 'anonymous'
            ]

            # 添加URL参数到缓存键
            if request.GET:
                cache_parts.append(
                    hash(frozenset(request.GET.items()))
                )

            # 添加路径参数到缓存键
            cache_parts.extend(str(v) for v in kwargs.values())

            # 尝试获取缓存
            cached_data = cache_manager.get(*cache_parts)
            if cached_data is not None:
                return cached_data

            # 执行视图函数
            result = func(request, *args, **kwargs)

            # 缓存结果
            cache_manager.set(
                *cache_parts,
                value=result,
                timeout=timeout,
                daily_refresh=daily_refresh
            )
            return result

        return wrapper
    return decorator

def invalidate_bean_cache(user_id=None, bean_id=None):
    """清除咖啡豆相关的缓存"""
    patterns = []

    if user_id and bean_id:
        patterns.append(f"bean:{bean_id}:{user_id}:*")
        # 同时清除与该咖啡豆相关的拼配组件缓存
        patterns.append(f"blend_component:{bean_id}:*")
    elif user_id:
        patterns.append(f"bean:*:{user_id}:*")
        patterns.append(f"blend_component:*:{user_id}:*")
    elif bean_id:
        patterns.append(f"bean:{bean_id}:*")
        patterns.append(f"blend_component:{bean_id}:*")
    else:
        patterns.append("bean:*")
        patterns.append("blend_component:*")

    for pattern in patterns:
        cache_manager.delete_pattern(pattern)

    # 同时清除相关的统计数据缓存
    if user_id:
        invalidate_hindsight_cache(user_id)

    # 清除iOS端相关缓存
    try:
        from iosapp.cache_utils import invalidate_ios_cache
        if user_id:
            invalidate_ios_cache('bean_list', user_id)
            invalidate_ios_cache('bean_calendar_data', user_id)
            invalidate_ios_cache('hindsight_data', user_id)
    except ImportError:
        # 如果无法导入iOS缓存模块，记录但不中断操作
        logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {user_id}")

def invalidate_blend_component_cache(user_id=None, component_id=None):
    """清除拼配组件相关的缓存"""
    patterns = []

    if user_id and component_id:
        patterns.append(f"blend_component:{component_id}:{user_id}:*")
    elif user_id:
        patterns.append(f"blend_component:*:{user_id}:*")
    elif component_id:
        patterns.append(f"blend_component:{component_id}:*")
    else:
        patterns.append("blend_component:*")

    for pattern in patterns:
        cache_manager.delete_pattern(pattern)

    # 同时清除相关的咖啡豆缓存
    invalidate_bean_cache(user_id)

def invalidate_hindsight_cache(user_id):
    """清除用户的统计数据缓存"""
    cache_manager.delete_pattern(f"hindsight:{user_id}:*")

    # 清除iOS端相关缓存
    try:
        from iosapp.cache_utils import invalidate_ios_cache
        if user_id:
            invalidate_ios_cache('hindsight_data', user_id)
            invalidate_ios_cache('heatmap_data', user_id)
    except ImportError:
        # 如果无法导入iOS缓存模块，记录但不中断操作
        logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {user_id}")

def invalidate_recipe_cache(user_id):
    """清除配方相关的缓存"""
    # 清除服务端缓存
    cache_key = f'user_recipes_{user_id}'
    from django.core.cache import cache
    cache.delete(cache_key)

    # 清除iOS端相关缓存
    try:
        from iosapp.cache_utils import invalidate_ios_cache
        if user_id:
            invalidate_ios_cache('recipe_list', user_id)
            logging.info(f"已清除用户 {user_id} 的配方相关缓存")
    except ImportError:
        # 如果无法导入iOS缓存模块，记录但不中断操作
        logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {user_id}")

def invalidate_equipment_cache(user_id=None, equipment_id=None):
    """清除设备相关的缓存"""
    patterns = []

    if user_id and equipment_id:
        patterns.append(f"equipment:{equipment_id}:{user_id}:*")
    elif user_id:
        patterns.append(f"equipment:*:{user_id}:*")
    elif equipment_id:
        patterns.append(f"equipment:{equipment_id}:*")
    else:
        patterns.append("equipment:*")

    for pattern in patterns:
        cache_manager.delete_pattern(pattern)

    # 如果提供了设备ID，清除设备使用统计缓存
    if equipment_id:
        from django.core.cache import cache
        from .models import get_equipment_usage_cache_key, get_equipment_last_used_cache_key

        cache.delete(get_equipment_usage_cache_key(equipment_id))
        cache.delete(get_equipment_last_used_cache_key(equipment_id))

    # 清除iOS端相关缓存
    try:
        from iosapp.cache_utils import invalidate_ios_cache
        if user_id:
            # 清除设备列表缓存
            invalidate_ios_cache('equipment_list', user_id)

            # 清除可能受影响的统计数据缓存
            invalidate_ios_cache('brewlog_statistics', user_id)
            invalidate_ios_cache('hindsight_data', user_id)
            invalidate_ios_cache('heatmap_data', user_id)
    except ImportError:
        # 如果无法导入iOS缓存模块，记录但不中断操作
        logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {user_id}")

def invalidate_record_cache(user_id=None, record_id=None):
    """清除冲煮记录相关的缓存"""
    patterns = []

    if user_id and record_id:
        patterns.append(f"record:{record_id}:{user_id}:*")

        # 同时清除趋势数据缓存
        from django.core.cache import cache
        from .models import get_trend_cache_key

        trend_cache_key = get_trend_cache_key(user_id, record_id)
        cache.delete(trend_cache_key)
    elif user_id:
        patterns.append(f"record:*:{user_id}:*")
    elif record_id:
        patterns.append(f"record:{record_id}:*")
    else:
        patterns.append("record:*")

    for pattern in patterns:
        cache_manager.delete_pattern(pattern)

    # 同时清除相关的统计数据缓存
    if user_id:
        invalidate_hindsight_cache(user_id)

    # 清除iOS端相关缓存
    try:
        from iosapp.cache_utils import invalidate_ios_cache
        if user_id:
            invalidate_ios_cache('brewlog_list', user_id)
            invalidate_ios_cache('brewlog_statistics', user_id)
            invalidate_ios_cache('filtered_brewlog_list', user_id)
            invalidate_ios_cache('hindsight_data', user_id)
            invalidate_ios_cache('heatmap_data', user_id)
    except ImportError:
        # 如果无法导入iOS缓存模块，记录但不中断操作
        logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {user_id}")

def warm_bean_cache(user_id, bean_id=None):
    """预热咖啡豆缓存"""
    from django.test.client import RequestFactory
    from django.contrib.auth import get_user_model
    from .views import view_bean, bean_list

    factory = RequestFactory()
    User = get_user_model()
    user = User.objects.get(id=user_id)

    # 修正路径为正确的URL
    request = factory.get('/my/brewlog/bean/')
    request.user = user
    bean_list(request)

    # 预热特定豆详情
    if bean_id:
        request = factory.get(f'/my/brewlog/bean/{bean_id}/')
        request.user = user
        view_bean(request, bean_id)

def warm_hindsight_cache(user_id):
    """预热统计数据缓存"""
    from django.test.client import RequestFactory
    from django.contrib.auth import get_user_model
    from .views import brewing_hindsight

    factory = RequestFactory()
    User = get_user_model()
    user = User.objects.get(id=user_id)

    # 为每个时间范围预热缓存
    for time_range in ['week', 'month', 'half_year', 'year']:
        request = factory.get(f'/my/brewlog/hindsight/?time_range={time_range}')
        request.user = user
        brewing_hindsight(request)

# 在BlendComponent模型的save和delete方法中添加缓存清除
@receiver([post_save, post_delete], sender=BlendComponent)
def clear_blend_component_cache(sender, instance, **kwargs):
    """当拼配组件发生变化时清除相关缓存"""
    invalidate_blend_component_cache(
        user_id=instance.coffee_bean.user_id,
        component_id=instance.id
    )