{% load calculation_filters time_filters format_filters %}

<div class="card-body text-base-content" x-data="{
    getHighlightStyle() {
        return this.$store.theme.isDark 
            ? 'background-color: {{ highlight_color_dark }}' 
            : 'background-color: {{ highlight_color_light }}';
    },
    isDifferent: {
        rating: {% if record.rating_level != other_record.rating_level %}true{% else %}false{% endif %},
        brewMethod: {% if record.brewing_equipment.get_brew_method_display != other_record.brewing_equipment.get_brew_method_display %}true{% else %}false{% endif %},
        waterQuality: {% if record.water_quality != other_record.water_quality %}true{% else %}false{% endif %},
        roomTemp: {% if record.room_temperature != other_record.room_temperature %}true{% else %}false{% endif %},
        roomHumidity: {% if record.room_humidity != other_record.room_humidity %}true{% else %}false{% endif %},
        doseWeight: {% if record.dose_weight != other_record.dose_weight %}true{% else %}false{% endif %},
        yieldWeight: {% if record.yield_weight != other_record.yield_weight %}true{% else %}false{% endif %},
        ratio: {% if record.yield_weight|divide:record.dose_weight|format_ratio != other_record.yield_weight|divide:other_record.dose_weight|format_ratio %}true{% else %}false{% endif %},
        brewingTime: {% if record.brewing_time != other_record.brewing_time %}true{% else %}false{% endif %},
        waterTemp: {% if record.water_temperature != other_record.water_temperature %}true{% else %}false{% endif %},
        grindSize: {% if record.grind_size != other_record.grind_size %}true{% else %}false{% endif %},
        bean: {% if record.coffee_bean.id != other_record.coffee_bean.id %}true{% else %}false{% endif %},
        brewer: {% if record.brewing_equipment.id != other_record.brewing_equipment.id %}true{% else %}false{% endif %},
        grinder: {% if record.grinding_equipment.id != other_record.grinding_equipment.id %}true{% else %}false{% endif %},
        gadgets: {% if record.gadgets.all|stringformat:'s' != other_record.gadgets.all|stringformat:'s' %}true{% else %}false{% endif %},
        notes: {% if record.notes != other_record.notes %}true{% else %}false{% endif %},
        flavorTags: {% if record.flavor_tags.all|stringformat:'s' != other_record.flavor_tags.all|stringformat:'s' %}true{% else %}false{% endif %},
        aroma: {% if record.aroma != other_record.aroma %}true{% else %}false{% endif %},
        acidity: {% if record.acidity != other_record.acidity %}true{% else %}false{% endif %},
        sweetness: {% if record.sweetness != other_record.sweetness %}true{% else %}false{% endif %},
        aftertaste: {% if record.aftertaste != other_record.aftertaste %}true{% else %}false{% endif %},
        body: {% if record.body != other_record.body %}true{% else %}false{% endif %}
    }
}">
    <!-- 冲煮信息 -->
    <div class="space-y-2">
        <div class="text-center">
            <h3>{{ pro_record.recipe_name|default:"未命名配方" }}</h3>
            <p class="text-sm opacity-60">{{ record.created_at|date:"Y-m-d H:i" }}</p>
        </div>
        <div class="p-2" :style="isDifferent.rating ? getHighlightStyle() : ''">
            <span class="opacity-60">评分：</span>
            {{ record.rating_level }}
        </div>
        <div class="p-2" :style="isDifferent.brewMethod ? getHighlightStyle() : ''">
            <span class="opacity-60">赛道：</span>
            {{ record.brewing_equipment.get_brew_method_display }}
        </div>
    </div>
    <!-- 测量数据 -->
    <div class="space-y-2">
        <div class="p-2" :style="isDifferent.doseWeight ? getHighlightStyle() : ''">
            <span class="opacity-60">粉重：</span>
            {{ record.dose_weight|format_number }}g
        </div>
        <div class="p-2" :style="isDifferent.yieldWeight ? getHighlightStyle() : ''">
            <span class="opacity-60">液重：</span>
            {{ record.yield_weight|format_number }}g
        </div>
        <div class="p-2" :style="isDifferent.ratio ? getHighlightStyle() : ''">
            <span class="opacity-60">粉水比：</span>
            1:{{ record.yield_weight|divide:record.dose_weight|format_ratio }}
        </div>
        <div class="p-2" :style="isDifferent.brewingTime ? getHighlightStyle() : ''">
            <span class="opacity-60">萃取时间：</span>
            {{ record.brewing_time|format_brewing_time }}
        </div>
        {% if record.water_temperature or other_record.water_temperature %}
        <div class="p-2" :style="isDifferent.waterTemp ? getHighlightStyle() : ''">
            <span class="opacity-60">水温：</span>
            {{ record.water_temperature|default:"未记录" }}°C
        </div>
        {% endif %}
        <div class="p-2" :style="isDifferent.grindSize ? getHighlightStyle() : ''">
            <span class="opacity-60">研磨度：</span>
            {{ record.grind_size }}
        </div>
    </div>
    <!-- 环境数据 -->
    <div class="space-y-2">
        {% if record.water_quality or other_record.water_quality %}
        <div class="p-2" :style="isDifferent.waterQuality ? getHighlightStyle() : ''">
            <span class="opacity-60">用水：</span>
            {{ record.water_quality|default:"未记录" }}
        </div>
        {% endif %}
        {% if record.room_temperature or other_record.room_temperature or record.room_humidity or other_record.room_humidity %}
        <div class="p-2" :style="isDifferent.roomTemp || isDifferent.roomHumidity ? getHighlightStyle() : ''">
            <span class="opacity-60">环境温湿度：</span>
            {% if record.room_temperature %}{{ record.room_temperature|format_number }}℃{% endif %}
            {% if record.room_temperature and record.room_humidity %} / {% endif %}
            {% if record.room_humidity %}{{ record.room_humidity }}%{% endif %}
            {% if not record.room_temperature and not record.room_humidity %}未记录{% endif %}
        </div>
        {% endif %}
    </div>
    <!-- 用豆器具 -->
    <div class="space-y-2">
        <div class="p-2" :style="isDifferent.bean ? getHighlightStyle() : ''">
            <span class="opacity-60">咖啡豆：</span>
            {{ record.coffee_bean.roaster }} {{ record.coffee_bean.name }}
        </div>
        <div class="p-2" :style="isDifferent.brewer ? getHighlightStyle() : ''">
            <span class="opacity-60">冲煮器具：</span>
            {{ record.brewing_equipment.brand }} {{ record.brewing_equipment.name }}
        </div>
        <div class="p-2" :style="isDifferent.grinder ? getHighlightStyle() : ''">
            <span class="opacity-60">磨豆机：</span>
            {{ record.grinding_equipment.brand }} {{ record.grinding_equipment.name }}
        </div>
        <!-- 小工具 -->
        {% if record.gadgets.all or other_record.gadgets.all %}
        <div class="p-2" :style="isDifferent.gadgets ? getHighlightStyle() : ''">
            <span class="opacity-60">小工具：</span>
            {% if record.gadgets.all %}
                {% for gadget in record.gadgets.all %}
                    {{ gadget.brand }} {{ gadget.name }}{% if not forloop.last %}, {% endif %}
                {% endfor %}
            {% else %}
                无
            {% endif %}
        </div>
        {% endif %}
    </div>
    
    <!-- 步骤 -->
    {% if record.steps %}
    <div class="mt-4">
    <div class="divider text-base-content/60">步骤</div>
    <div class="space-y-2">
        {% for step in record.steps %}
        <div class="p-2" 
             x-data="{ isDifferent: {% if step != other_record.steps|get_item:forloop.counter0 %}true{% else %}false{% endif %} }"
             :style="isDifferent ? getHighlightStyle() : ''">
        {{ forloop.counter }}. {{ step.text }}
        {% if step.timer %}<span class="opacity-60">⏱ {{ step.timer }}</span>{% endif %}
        </div>
        {% endfor %}
    </div>
    </div>
    {% endif %}
    
    <!-- 备注 -->
    {% if record.notes %}
    <div class="mt-4">
    <div class="divider text-base-content/60">备注</div>
    <div class="p-2" :style="isDifferent.notes ? getHighlightStyle() : ''">
        {{ record.notes|linebreaks }}
    </div>
    </div>
    {% endif %}
    
    <!-- 品鉴笔记 -->
    {% if record.flavor_tags.exists or record.aroma or record.acidity or record.sweetness or record.aftertaste or record.body or other_record.flavor_tags.exists or other_record.aroma or other_record.acidity or other_record.sweetness or other_record.aftertaste or other_record.body %}
    <div class="mt-4">
    <div class="divider text-base-content/60">品鉴笔记</div>
    
    <!-- 风味标签 -->
    {% if record.flavor_tags.exists or other_record.flavor_tags.exists %}
    <div class="p-2" :style="isDifferent.flavorTags ? getHighlightStyle() : ''">
        <span class="opacity-60">风味标签：</span>
        {% if record.flavor_tags.exists %}
            {% for tag in record.flavor_tags.all %}
                <span class="badge badge-outline badge-secondary mr-1">{{ tag.name }}</span>
            {% endfor %}
        {% else %}
            无
        {% endif %}
    </div>
    {% endif %}
    
    <!-- 五个维度 -->
    <div class="space-y-2 mt-2">
        <div class="p-2" :style="isDifferent.aroma ? getHighlightStyle() : ''">
            <span class="opacity-60">香气：</span>
            {{ record.aroma|default:"未评分" }}
        </div>
        <div class="p-2" :style="isDifferent.acidity ? getHighlightStyle() : ''">
            <span class="opacity-60">酸度：</span>
            {{ record.acidity|default:"未评分" }}
        </div>
        <div class="p-2" :style="isDifferent.sweetness ? getHighlightStyle() : ''">
            <span class="opacity-60">甜度：</span>
            {{ record.sweetness|default:"未评分" }}
        </div>
        <div class="p-2" :style="isDifferent.aftertaste ? getHighlightStyle() : ''">
            <span class="opacity-60">余韵：</span>
            {{ record.aftertaste|default:"未评分" }}
        </div>
        <div class="p-2" :style="isDifferent.body ? getHighlightStyle() : ''">
            <span class="opacity-60">醇厚：</span>
            {{ record.body|default:"未评分" }}
        </div>
    </div>
    </div>
    {% endif %}
</div>