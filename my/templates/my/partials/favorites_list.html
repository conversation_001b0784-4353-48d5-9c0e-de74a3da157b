{% load time_filters %}

{% if favorites %}
    <ul class="flex flex-col gap-4 py-8">
        {% for favorite in favorites %}
            <li class="mx-4 lg:mx-8" id="favorite-{{ favorite.id }}">
                <div class="group relative flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4 overflow-hidden rounded-box p-4 duration-300 hover:bg-base-200">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-2">
                            {% if favorite.content_type.model == 'articlepage' %}
                                <span class="badge badge-primary">文章</span>
                            {% elif favorite.content_type.model == 'roastedbeanpage' %}
                                <span class="badge badge-secondary">咖啡豆</span>
                            {% else %}
                                <span class="badge badge-accent">配方</span>
                            {% endif %}
                            <time class="text-sm text-base-content/60">
                                收藏于 {{ favorite.created_at|time_ago }}
                            </time>
                        </div>
                        
                        <a href="{{ favorite.content_object.url }}" class="hover:underline">
                            <h2 class="text-xl font-bold">{{ favorite.content_object.title }}</h2>
                        </a>
                        
                        {% if favorite.content_object.search_description or favorite.content_object.description %}
                            <p class="mt-2 text-sm text-base-content/70">
                                {{ favorite.content_object.search_description|default:favorite.content_object.description|truncatechars:120 }}
                            </p>
                        {% endif %}
                    </div>
                    
                    <div class="flex items-center gap-2">
                        <a href="{{ favorite.content_object.url }}" 
                           class="btn btn-sm btn-outline">
                            查看详情
                        </a>
                        <button class="btn btn-sm btn-outline btn-error"
                                hx-post="{% url 'remove_favorite' favorite.id %}"
                                hx-target="#favorite-{{ favorite.id }}"
                                hx-swap="outerHTML swap:1s"
                                hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                        >
                            取消收藏
                        </button>
                    </div>
                </div>
            </li>
        {% endfor %}
    </ul>
    
    {% if favorites.has_other_pages %}
        <div class="flex justify-center py-4">
            <div class="join">
                {% if favorites.has_previous %}
                    <a hx-get="?page={{ favorites.previous_page_number }}&type={{ active_tab }}"
                       hx-target="#favorites-content"
                       class="join-item btn btn-sm"
                    >«</a>
                {% endif %}
                
                <button class="join-item btn btn-sm">{{ favorites.number }} / {{ favorites.paginator.num_pages }}</button>
                
                {% if favorites.has_next %}
                    <a hx-get="?page={{ favorites.next_page_number }}&type={{ active_tab }}"
                       hx-target="#favorites-content"
                       class="join-item btn btn-sm"
                    >»</a>
                {% endif %}
            </div>
        </div>
    {% endif %}
{% else %}
    <div class="flex flex-col items-center justify-center py-16">
        <div class="text-6xl mb-4">📚</div>
        <h3 class="text-xl font-bold mb-2">还没有收藏内容</h3>
        <p class="text-base-content/60">浏览更多内容并收藏感兴趣的项目吧</p>
    </div>
{% endif %} 