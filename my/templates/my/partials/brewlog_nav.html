{% load static %}

<div class="drawer z-50">
  <input id="my-drawer-3" type="checkbox" class="drawer-toggle" />
  <div class="drawer-content flex flex-row">
    <div class="w-full navbar bg-base-100 text-base-content z-10 backdrop-blur-sm shadow-xs">
      <div class="flex-none lg:hidden">
        <label for="my-drawer-3" aria-label="open sidebar" class="btn btn-circle btn-ghost">
          <span class="icon-[ri--menu-2-fill] text-2xl"></span>
        </label>
      </div>
      <div class="navbar-start lg:px-2 lg:mx-2">
        <a href="/" class="btn btn-ghost">
          <img class="block h-10 w-auto dark:hidden" src="{% static "images/logo.svg" %}" alt="咖啡搭子">
          <img class="hidden h-10 w-auto dark:block" src="{% static "images/logo_dm.svg" %}" alt="咖啡搭子">
        </a>
        <div class="hidden md:inline-block font-mono text-[0.6875rem] text-base-content/60">[咖啡札记]</div>
      </div>
      <div class="navbar-center hidden lg:block">
        <ul class="menu menu-md xl:menu-lg menu-horizontal">
          <!-- Navbar menu content -->
          <li><a href="{% url 'brewlog' %}"><span class="icon-[ph--scroll-duotone]"></span>冲煮记录</a></li>
          <li><a href="{% url 'equipment_list' %}"><span class="icon-[game-icons--coffee-pot]"></span>设备</a></li>
          <li><a href="{% url 'bean_list' %}"><span class="icon-[tabler--paper-bag]"></span>咖啡豆</a></li>
          <li><a href="{% url 'brewing_hindsight' %}"><span class="icon-[icon-park-outline--market-analysis]"></span>后见之明</a></li>
          <li><a href="{% url 'recipe_list' %}"><span class="icon-[tabler--library]"></span>配方册</a></li>
          <li><a href="{% url 'whats_new' %}"><span class="icon-[lucide--wand]"></span>新鲜事</a></li>
        </ul>
      </div>
      <div class="navbar-end">
        <label class="searchbox relative lg:mx-3">
          <div class="form-control">
            <input type="search" placeholder="搜索全站…" autocomplete="off" spellcheck="false"
              class="input w-24 md:w-auto" hx-get="{% url 'search' %}"
              hx-trigger="keyup changed delay:500ms, query" hx-target="#search-results" name="query" {% if search_query %}
              value="{{ search_query }}" {% endif %} />
          </div>
        </label>
        <div class="flex-none">
          <div class="hidden lg:inline-block dropdown dropdown-bottom dropdown-end">
              <div tabindex="0" role="button" class="btn btn-circle btn-ghost text-xl">
                  <span>🎁</span>
              </div>
              <div
                  tabindex="0"
                  class="dropdown-content bg-base-100 rounded-box z-[1] w-52 p-2 shadow-sm grid gap-4 justify-center text-start">
                  <div tabindex="0" class="space-y-2">
                      <div class="text-sm">
                        <p><strong>如果这个工具帮到了你:</strong></p>
                        <ul>
                          <li>
                            <div class="flex items-center gap-2">
                              <span class="icon-[mdi--star-outline]"></span>
                              <div>添加到浏览器收藏夹</div>
                            </div>
                          </li>
                          <li>
                            <div class="flex items-center gap-2">
                              <span class="icon-[mdi--share-all-outline]"></span>
                              <div>转发给需要的朋友</div>
                            </div>
                          </li>
                          <li>
                            <div class="flex items-center gap-2">
                              <span class="icon-[mingcute--battery-charging-line]"></span>
                              <div>扫码为开发者充值灵感</div>
                            </div>
                          </li>
                        </ul>
                      </div>
                      <img src="https://ik.imagekit.io/kol/IMG_2479.jpeg?updatedAt=1742095417489">
                      <div class="text-sm text-center italic space-y-1 mt-2">
                          <p class="text-base-content/80">{{ quote.content|linebreaksbr }}</p>
                          <p class="text-base-content/60">——{{ quote.author }}{% if quote.source %}，《{{ quote.source }}》{% endif %}</p>
                      </div>
                  </div>
              </div>
          </div>
          <div class="dropdown dropdown-bottom dropdown-end">
            <div tabindex="0" role="button" class="btn btn-circle btn-ghost m-1">
              <span class="icon-[bxs--coffee-bean] text-xl text-secondary"></span>
            </div>
            <ul tabindex="0" class="dropdown-content menu menu-lg p-2 shadow-sm bg-base-100 rounded-box w-52">
              <li class="text-lg m-4 opacity-60">{% if user.first_name %}{{ user.first_name }}{% else %}{{ user }}{% endif %}，你好！</li>
              <li><a href="/my/brewlog/"><span class="icon-[ph--scroll-duotone]"></span> 我的咖啡札记</a></li>
              <li><a href="/my/fav/"><span class="icon-[bi--bookmark-star]"></span> 我的收藏</a></li>
              <li>
                <details>
                  <summary><span class="icon-[uil--setting]"></span> 账号设置</summary>
                  <ul>
                    <li><a href="{% url 'account_change_first_name' %}">修改昵称</a></li>
                    <li><a href="{% url 'account_change_password' %}">修改密码</a></li>
                    <li><a href="{% url 'account_email' %}">修改邮箱</a></li>
                    <li><a href="{% url 'account_logout' %}">登出</a></li>
                  </ul>
                </details>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!-- Page content -->
  </div>
  <div class="drawer-side z-50">
    <label for="my-drawer-3" aria-label="close sidebar" class="drawer-overlay"></label>
    <ul class="menu menu-lg p-4 w-80 min-h-full bg-base-100 text-base-content">
      <div class="flex justify-between">
        <a href="/" class="btn btn-ghost">
          <img class="block h-10 w-auto dark:hidden" src="{% static "images/logo.svg" %}" alt="咖啡搭子">
          <img class="hidden h-10 w-auto dark:block" src="{% static "images/logo_dm.svg" %}" alt="咖啡搭子">
        </a>
        <label for="my-drawer-3" aria-label="close sidebar" class="btn btn-circle btn-ghost">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </label>
      </div>
      <div class="mt-1 ml-6 font-mono text-[0.6875rem] text-base-content/60">[咖啡札记]</div>
      <!-- Sidebar content -->
      <li></li>
      <li><a href="{% url 'brewlog' %}"><span class="icon-[ph--scroll-duotone]"></span>冲煮记录</a></li>
      <li><a href="{% url 'equipment_list' %}"><span class="icon-[game-icons--coffee-pot]"></span>设备</a></li>
      <li><a href="{% url 'bean_list' %}"><span class="icon-[tabler--paper-bag]"></span>咖啡豆</a></li>
      <li><a href="{% url 'brewing_hindsight' %}"><span class="icon-[icon-park-outline--market-analysis]"></span>后见之明</a></li>
      <li><a href="{% url 'recipe_list' %}"><span class="icon-[tabler--library]"></span>配方册</a></li>
      <li><a href="{% url 'whats_new' %}"><span class="icon-[lucide--wand]"></span>新鲜事</a></li>
      <li></li>
      <div class="flex flex-col items-center gap-2 px-4 my-2 text-xs md:flex-row text-base-content/80">
        <div class="flex flex-col gap-2">
          <div>如果这个工具帮到了你:</div>
          <div class="flex items-center gap-2">
            <span class="icon-[mdi--star-outline]"></span>
            <div>添加到浏览器收藏夹</div>
          </div>
          <div class="flex items-center gap-2">
            <span class="icon-[mdi--share-all-outline]"></span>
            <div>转发给需要的朋友</div>
          </div>
          <div class="flex items-center gap-2">
            <span class="icon-[mingcute--battery-charging-line]"></span>
            <div>扫码为开发者充值灵感</div>
          </div>
        </div>
      </div>
      <div class="flex flex-col gap-2 items-center">
          <img src="https://ik.imagekit.io/kol/IMG_2479.jpeg?updatedAt=1742095417489" class="w-2/3">
          <div class="text-sm text-center italic space-y-1">
              <p class="text-base-content/80">{{ quote.content|linebreaksbr }}</p>
              <p class="text-base-content/60">——{{ quote.author }}{% if quote.source %}，《{{ quote.source }}》{% endif %}</p>
          </div>
      </div>
    </ul>
  </div>
</div>
<div id="search-results"></div>