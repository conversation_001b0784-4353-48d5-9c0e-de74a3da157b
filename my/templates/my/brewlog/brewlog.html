{% extends "my/brewlog_base.html" %}
{% load static %}
{% load calculation_filters time_filters user_filters format_filters %}
{% block title %}我的咖啡札记{% endblock %}
{% block extra_js %}
<script type="text/javascript" src="{% static 'js/brewlog.js' %}"></script>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/brewlog.css' %}">
{% endblock %}
{% block content %}

<div class="py-2 px-1 bg-base-200 mb-2 text-base-content">
    <div class="navbar w-full max-w-screen-xl mx-auto" x-data x-init="$store.filterState.init()">
        <div class="flex-none w-[var(--nav-width)]">
            <div class="lg:ml-4 btn btn-ghost" @click="$store.filterState.toggle()">
                <span class="icon-[bi--filter-circle] text-lg" 
                      x-show="!$store.filterState.isOpen"></span>
                <span class="icon-[bi--filter-circle-fill] text-lg" 
                      x-show="$store.filterState.isOpen"></span>
                <span class="hidden lg:block">筛选</span>
            </div>
        </div>
        <div class="flex-1 flex justify-center">
            我的冲煮记录
        </div>
        <div class="flex-none w-[var(--nav-width)] flex justify-end">
            <a href="{% url 'add_record_page' %}" 
               class="btn btn-ghost"
               x-data="{ loading: false }"
               @click="loading = true"
               :class="{ 'btn-disabled': loading }">
                <span class="hidden lg:block">新增</span>
                <span x-show="!loading" class="icon-[icons8--plus] text-2xl"></span>
                <span x-show="loading" class="loading loading-spinner"></span>
            </a>
        </div>
    </div>
</div>

<div class="container mx-auto xl:max-w-7xl text-base-content px-2 mb-8" x-data x-init="$store.brewlogList.init()">
    <!-- 搜索和筛选区域 -->
    <form id="filterForm" 
          class="flex flex-col lg:flex-row gap-2 items-center mb-4 px-4 justify-center"
          x-show="$store.filterState.isOpen"
          x-transition:enter="transition ease-out duration-200"
          x-transition:enter-start="opacity-0 transform -translate-y-2"
          x-transition:enter-end="opacity-100 transform translate-y-0"
          x-transition:leave="transition ease-in duration-200"
          x-transition:leave-start="opacity-100 transform translate-y-0"
          x-transition:leave-end="opacity-0 transform -translate-y-2">
        <!-- 日期范围 -->
        <div class="dropdown">
            <div class="join items-center flex flex-row gap-2">
                <input 
                    type="date" 
                    name="date_from"
                    class="join-item input input-sm"
                    value="{{ date_from|default:'' }}"
                    placeholder="开始日期"
                    data-date="{{ date_from|default:'' }}"
                >
                <span class="join-item text-sm">至</span>
                <input 
                    type="date" 
                    name="date_to"
                    class="join-item input input-sm"
                    value="{{ date_to|default:'' }}"
                    placeholder="结束日期"
                    data-date="{{ date_to|default:'' }}"
                >
            </div>
        </div>
        <!-- 搜索框 -->
        <div class="dropdown">
            <div class="join">
                <input type="text" 
                    name="q" 
                    placeholder="搜索配方、步骤、备注..." 
                    class="join-item input input-sm"
                    value="{{ search_query|default:'' }}">
                <button type="submit" class="join-item btn btn-sm">
                    <span class="icon-[bi--search]"></span>
                </button>
            </div>
        </div>
        <!-- 筛选 -->
        <div class="flex flex-wrap gap-2">
            <!-- 冲煮方式 -->
            <div class="dropdown">
                <div tabindex="0" role="button" class="btn btn-sm">
                    冲煮方式
                    {% if current_brew_method %}<span class="status status-success ml-1"></span>{% endif %}
                </div>
                <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                    <li><a href="?{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}&{% endif %}{% if current_coffee_bean %}coffee_bean={{ current_coffee_bean }}&{% endif %}{% if current_rating_range %}rating_range={{ current_rating_range }}&{% endif %}{% if search_query and search_query != '' %}q={{ search_query }}{% endif %}"
                        class="{% if not current_brew_method %}menu-active{% endif %}">全部</a></li>
                    {% for value, label in brew_methods %}
                    <li><a href="?brew_method={{ value }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if current_coffee_bean %}&coffee_bean={{ current_coffee_bean }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if search_query and search_query != '' %}&q={{ search_query }}{% endif %}"
                        class="{% if current_brew_method == value %}menu-active{% endif %}">{{ label }}</a></li>
                    {% endfor %}
                </ul>
            </div>
            <!-- 咖啡豆 -->
            <div class="dropdown">
                <div tabindex="0" role="button" class="btn btn-sm">
                    咖啡豆
                    {% if current_coffee_bean %}<span class="status status-success ml-1"></span>{% endif %}
                </div>
                <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                    <li><a href="?{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}&{% endif %}{% if current_brew_method %}brew_method={{ current_brew_method }}&{% endif %}{% if current_rating_range %}rating_range={{ current_rating_range }}&{% endif %}{% if search_query and search_query != '' %}q={{ search_query }}{% endif %}"
                        class="{% if not current_coffee_bean %}menu-active{% endif %}">全部</a></li>
                    {% for bean in coffee_beans %}
                    <li><a href="?coffee_bean={{ bean.id }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if current_brew_method %}&brew_method={{ current_brew_method }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if search_query and search_query != '' %}&q={{ search_query }}{% endif %}"
                        class="{% if current_coffee_bean == bean.id|stringformat:'s' %}menu-active{% endif %}">{{ bean.name }}</a></li>
                    {% endfor %}
                </ul>
            </div>
            <!-- 评分区间 -->
            <div class="dropdown dropdown-end">
                <div tabindex="0" role="button" class="btn btn-sm">
                    评分
                    {% if current_rating_range %}<span class="status status-success ml-1"></span>{% endif %}
                </div>
                <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                    <li><a href="?{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}&{% endif %}{% if current_brew_method %}brew_method={{ current_brew_method }}&{% endif %}{% if current_coffee_bean %}coffee_bean={{ current_coffee_bean }}&{% endif %}{% if search_query and search_query != '' %}q={{ search_query }}{% endif %}"
                        class="{% if not current_rating_range %}menu-active{% endif %}">全部</a></li>
                    
                    <li><a href="?rating_range=8.1-10{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if current_brew_method %}&brew_method={{ current_brew_method }}{% endif %}{% if current_coffee_bean %}&coffee_bean={{ current_coffee_bean }}{% endif %}{% if search_query and search_query != '' %}&q={{ search_query }}{% endif %}"
                        class="{% if current_rating_range == '8.1-10' %}menu-active{% endif %}">
                        <div class="flex gap-0.5">
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                        </div>
                    </a></li>
                    
                    <li><a href="?rating_range=6.1-8{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if current_brew_method %}&brew_method={{ current_brew_method }}{% endif %}{% if current_coffee_bean %}&coffee_bean={{ current_coffee_bean }}{% endif %}{% if search_query and search_query != '' %}&q={{ search_query }}{% endif %}"
                        class="{% if current_rating_range == '6.1-8' %}menu-active{% endif %}">
                        <div class="flex gap-0.5">
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                        </div>
                    </a></li>
                    
                    <li><a href="?rating_range=4.1-6{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if current_brew_method %}&brew_method={{ current_brew_method }}{% endif %}{% if current_coffee_bean %}&coffee_bean={{ current_coffee_bean }}{% endif %}{% if search_query and search_query != '' %}&q={{ search_query }}{% endif %}"
                        class="{% if current_rating_range == '4.1-6' %}menu-active{% endif %}">
                        <div class="flex gap-0.5">
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                        </div>
                    </a></li>
                    
                    <li><a href="?rating_range=2.1-4{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if current_brew_method %}&brew_method={{ current_brew_method }}{% endif %}{% if current_coffee_bean %}&coffee_bean={{ current_coffee_bean }}{% endif %}{% if search_query and search_query != '' %}&q={{ search_query }}{% endif %}"
                        class="{% if current_rating_range == '2.1-4' %}menu-active{% endif %}">
                        <div class="flex gap-0.5">
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                        </div>
                    </a></li>
                    
                    <li><a href="?rating_range=0-2{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if current_brew_method %}&brew_method={{ current_brew_method }}{% endif %}{% if current_coffee_bean %}&coffee_bean={{ current_coffee_bean }}{% endif %}{% if search_query and search_query != '' %}&q={{ search_query }}{% endif %}"
                        class="{% if current_rating_range == '0-2' %}menu-active{% endif %}">
                        <div class="flex gap-0.5">
                            <span class="icon-[mdi--heart] text-rose-500"></span>
                        </div>
                    </a></li>
                </ul>
            </div>
            <!-- 重置按钮 -->
            <div>
                {% if current_brew_method or current_coffee_bean or current_rating_range or date_from or date_to or search_query and search_query != '' %}
                <a href="{% url 'brewlog' %}" class="btn btn-sm btn-ghost text-success">
                    <span class="icon-[bx--reset] text-lg"></span>
                </a>
                {% endif %}
            </div>
            <!-- 对比模式按钮 -->
            <template x-if="$store.brewlogList.compareMode">
                <button @click="$store.brewlogList.clearCompare()" class="btn btn-sm btn-ghost text-lg text-info">
                    <span class="icon-[material-symbols--compare-arrows] text-lg"></span>
                    <span class="text-xs" x-text="$store.brewlogList.selectedRecords.length + '/2'"></span>
                </button>
            </template>
        </div>
    </form>
    <div class="w-full px-4 md:px-20">
        <div class="flex flex-col">
            <div class="flex flex-col items-center">
                <div class="grid justify-center lg:justify-start">
                    <div class="flex justify-between items-center w-full mb-4">
                        <div class="stats shadow bg-base-100">
                            <div class="stat place-items-center">
                                {% if streak_days > 0 %}
                                    <div class="stat-title">连续打卡</div>
                                    <div class="stat-value">{{ streak_days }}</div>
                                    <div class="stat-desc">天</div>
                                {% else %}
                                    <div class="stat-title">上次打卡</div>
                                    <div class="stat-value">{{ last_streak_days|default:'-' }}</div>
                                    <div class="stat-desc">天前</div>
                                {% endif %}
                            </div>
                            
                            <div class="stat place-items-center">
                                <div class="stat-title">本月冲煮</div>
                                <a href="?{% if current_brew_method %}brew_method={{ current_brew_method }}&{% endif %}{% if current_coffee_bean %}coffee_bean={{ current_coffee_bean }}&{% endif %}{% if current_rating_range %}rating_range={{ current_rating_range }}&{% endif %}{% if search_query and search_query != '' %}q={{ search_query }}&{% endif %}date_from={{ month_start|date:'Y-m-d' }}&date_to={{ month_end|date:'Y-m-d' }}"
                                   class="hover:opacity-60 transition-opacity flex flex-col items-center"
                                   x-data="{ loading: false }"
                                   @click="loading = true">
                                    <div class="stat-value">{{ month_count }}</div>
                                    <div class="stat-desc text-center">次</div>
                                </a>
                            </div>
                            
                            <div class="stat place-items-center">
                                <div class="stat-title">今年冲煮</div>
                                <a href="?{% if current_brew_method %}brew_method={{ current_brew_method }}&{% endif %}{% if current_coffee_bean %}coffee_bean={{ current_coffee_bean }}&{% endif %}{% if current_rating_range %}rating_range={{ current_rating_range }}&{% endif %}{% if search_query and search_query != '' %}q={{ search_query }}&{% endif %}date_from={{ year_start|date:'Y-m-d' }}&date_to={{ year_end|date:'Y-m-d' }}"
                                   class="hover:opacity-60 transition-opacity flex flex-col items-center"
                                   x-data="{ loading: false }"
                                   @click="loading = true">
                                    <div class="stat-value">{{ year_count }}</div>
                                    <div class="stat-desc text-center">次</div>
                                </a>
                                <div class="stat-figure text-secondary">
                                    <a href="{% url 'brewlog_heatmap' %}" ><span class="icon-[file-icons--stata]"></span></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if not has_active_brewer or not has_active_grinder or not has_active_beans %}
                    <div class="alert alert-warning mb-4">
                        <span class="icon-[material-symbols--info-outline] text-2xl"></span>
                        <span>无法新增冲煮记录？请先添加有效的
                            {% if not has_active_brewer %}
                            <a class="link" href="{% url 'equipment_list' %}">冲煮器具</a>{% if not has_active_grinder and not has_active_beans %}、{% elif not has_active_grinder or not has_active_beans %}和{% endif %}
                            {% endif %}
                            {% if not has_active_grinder %}
                            <a class="link" href="{% url 'equipment_list' %}">磨豆机</a>{% if not has_active_beans %}和{% endif %}
                            {% endif %}
                            {% if not has_active_beans %}
                            <a class="link" href="{% url 'bean_list' %}">咖啡豆</a>
                            {% endif %}
                            信息</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            <!-- 冲煮记录列表 -->
            <div class="text-start text-sm text-base-content/60 mb-4"
                 x-data="{ loading: false }"
                 @click.window="loading = $event.target.closest('.stat') !== null && 
                                $event.target.closest('a')?.href?.includes('date_from')">
                <div x-show="!loading" class="flex items-center gap-2">
                    共找到 {{ pagination.total_count }} 条符合条件的记录:
                </div>
                <div x-show="loading" class="flex items-center gap-2">
                    <span class="loading loading-spinner loading-sm"></span>
                    <span>加载中...</span>
                </div>
            </div>
            <div class="grid grid-cols-1 grid-rows-1 md:grid-cols-2 gap-4">
                {% for record in brewing_records %}
                <div class="border-2 border-base-content/5 hover:shadow-lg active:shadow-lg rounded-lg overflow-visible">
                    <div class="p-4">
                        <div class="flex justify-between items-center">
                            <div class="flex gap-4 items-baseline">
                                <span class="text-2xl">
                                {{ record.get_rating_level_display }}
                                </span>
                                {% if record.recipe_name %}
                                <div class="flex items-center gap-2">
                                    <h4 class="text-xl mb-2">{{ record.recipe_name }}</h4>
                                </div>
                                {% endif %}
                                <span x-show="$store.brewlogList.selectedRecords.includes({{ record.id }})" class="text-info text-2xl">⇄</span>
                            </div>
                            <div class="relative dropdown dropdown-end">
                                <div tabindex="0" role="button" class="btn btn-ghost btn-circle text-2xl">
                                    <span class="icon-[mi--options-vertical]"></span>
                                </div>
                                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box w-52 p-2 shadow-lg absolute z-50">
                                    <li><a href="{% url 'view_record_page' record.id %}"><span class="icon-[tabler--eye]"></span> 查看详情</a></li>
                                    <li><a href="{% url 'edit_record_page' record.id %}"><span class="icon-[material-symbols--edit-square-outline-rounded]"></span> 修改配方</a></li>
                                    <li><a href="{% url 'add_record_page' %}?copy_from={{ record.id }}"><span class="icon-[tabler--copy]"></span> 复制配方</a></li>
                                    <li>
                                        <button @click="$store.brewlogList.toggleCompare({{ record.id }})"
                                                :class="{ 'menu-active': $store.brewlogList.selectedRecords.includes({{ record.id }}) }">
                                            <span class="icon-[material-symbols--compare-arrows]"></span> 
                                            <span x-text="$store.brewlogList.selectedRecords.includes({{ record.id }}) ? '取消对比' : '对比'"></span>
                                        </button>
                                    </li>
                                    <li><button class="text-error" onclick="delete_record_modal_{{ record.id }}.showModal()">
                                        <span class="icon-[material-symbols--delete]"></span> 删除
                                    </button></li>
                                </ul>
                            </div>
                        </div>
                        <div class="flex gap-4 items-baseline">
                            <h5 class="text-lg font-semibold mt-2">{{ record.coffee_bean.name }}</h5>
                            <span class="text-xs opacity-60">{{ record.created_at|format_time_ago }}</span>
                        </div>
                        <div class="flex flex-wrap gap-2 items-center mt-2">
                            <div class="join">
                                <span class="badge badge-accent join-item">{{ record.brewing_equipment }}</span>
                                <span class="badge badge-primary join-item">{{ record.brewing_equipment.get_brew_method_display }}</span>
                            </div>
                            <div class="join">
                                <span class="badge badge-accent join-item">{{ record.grinding_equipment }}</span>
                                <span class="badge badge-primary join-item">{{ record.grind_size }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="ml-4 flex justify-between mb-4">
                        <div class="flex flex-wrap gap-2 items-center">
                            <div class="badge badge-ghost gap-2 text-sm"><span class="icon-[iconamoon--clock-light]"></span> {{ record.brewing_time|format_brewing_time }} </div>
                            <div class="badge badge-ghost gap-2 text-sm"><span class="icon-[iconoir--percentage-circle]"></span> 1:{{ record.yield_weight|divide:record.dose_weight|format_ratio }}</div>
                            {% if record.water_temperature %}
                            <div class="badge badge-ghost gap-2 text-sm"><span class="icon-[carbon--temperature-celsius]"></span> {{ record.water_temperature|format_number }}</div>
                            {% endif %}
                            {% if record.notes %}
                            <div class="badge gap-2 text-sm text-info tooltip" data-tip="{{ record.notes }}"><span class="icon-[ph--note-duotone]"></span></div>
                            {% endif %}
                            {% if record.steps %}
                            <div class="badge gap-2 text-sm text-info tooltip" data-tip="{{ record.steps|count_steps }}"><span class="icon-[streamline--steps-number]"></span></div>
                            {% endif %}
                            {% if record.flavor_tags.exists %}
                            <div class="badge gap-2 text-sm text-info tooltip" data-tip="{% for tag in record.flavor_tags.all %}{{ tag.name }}{% if not forloop.last %}、{% endif %}{% endfor %}"><span class="icon-[icon-park-outline--icecream-three]"></span></div>
                            {% endif %}
                            {% with has_dimensions=record.aroma|add:record.acidity|add:record.sweetness|add:record.body|add:record.aftertaste %}
                            {% if has_dimensions > 0 %}
                            <div class="badge gap-2 text-sm text-info tooltip" data-tip="{% if record.aroma > 0 %}香气：{{ record.get_aroma_display }}{% endif %}{% if record.acidity > 0 %}{% if record.aroma > 0 %}&#10;{% endif %}酸质：{{ record.get_acidity_display }}{% endif %}{% if record.sweetness > 0 %}{% if record.aroma > 0 or record.acidity > 0 %}&#10;{% endif %}甜度：{{ record.get_sweetness_display }}{% endif %}{% if record.body > 0 %}{% if record.aroma > 0 or record.acidity > 0 or record.sweetness > 0 %}&#10;{% endif %}醇厚：{{ record.get_body_display }}{% endif %}{% if record.aftertaste > 0 %}{% if record.aroma > 0 or record.acidity > 0 or record.sweetness > 0 or record.body > 0 %}&#10;{% endif %}余韵：{{ record.get_aftertaste_display }}{% endif %}"><span class="icon-[mdi--radar]"></span></div>
                            {% endif %}
                            {% endwith %}
                            {% if record.water_quality or record.room_temperature or record.room_humidity %}
                            <div class="badge gap-2 text-sm text-info tooltip"  data-tip="{% if record.water_quality %}水质：{{ record.water_quality }}&#10;{% endif %}{% if record.room_temperature %}室温：{{ record.room_temperature|format_number }}°C&#10;{% endif %}{% if record.room_humidity %}湿度：{{ record.room_humidity }}&#37;{% endif %}"><span class="icon-[uiw--environment-o]"></span></div>
                            {% endif %}
                        </div>
                        <span class="opacity-10 font-bold text-4xl content-end">{{ record.dose_weight|format_number }}g</span>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if brewing_records %}
                <!-- 分页导航 -->
                {% if pagination.total_pages > 1 %}
                <div class="flex justify-center gap-6 my-8">
                    {% if pagination.has_previous %}
                    <a href="?page={{ pagination.previous_page }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if current_brew_method %}&brew_method={{ current_brew_method }}{% endif %}{% if current_coffee_bean %}&coffee_bean={{ current_coffee_bean }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if search_query and search_query != '' %}&q={{ search_query }}{% endif %}"
                        class="btn">
                        上一页
                    </a>
                    {% endif %}
                
                    <span class="text-sm self-center text-base-content/60">第{{ pagination.current_page }}页 / 共{{ pagination.total_pages }}页</span>
                
                    {% if pagination.has_next %}
                    <a href="?page={{ pagination.next_page }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if current_brew_method %}&brew_method={{ current_brew_method }}{% endif %}{% if current_coffee_bean %}&coffee_bean={{ current_coffee_bean }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if search_query and search_query != '' %}&q={{ search_query }}{% endif %}"
                        class="btn">
                        下一页
                    </a>
                    {% endif %}
                </div>
                {% endif %}
                <!-- 导出记录 -->
                {% if request.user|has_export_limit %}
                <div class="tooltip tooltip-bottom self-center mt-4" data-tip="为缓解服务器压力，导出功能每日限使用一次。">
                    <button class="btn btn-disabled flex flex-col">
                        <span>导出所有记录</span>
                        <span class="text-xs">您今天的导出次数已达上限，请明天再尝试</span>
                    </button>
                </div>
                {% else %}
                <a href="{% url 'export_brewing_records' %}" 
                   class="btn btn-sm btn-outline self-center mt-4"
                   target="_blank"
                   rel="noopener noreferrer">
                   <span class="icon-[mdi--file-download-outline] text-lg"></span>导出所有记录
                </a>
                {% endif %}

            {% endif %}
        </div>
    </div>
</div>
<div x-data>
{% for record in brewing_records %}
<dialog id="delete_record_modal_{{ record.id }}" class="modal text-base-content">
    <div class="modal-box">
        <h3 class="text-lg font-bold">确认删除</h3>
        <p class="py-4">确定要删除这条冲煮记录吗？此操作不可撤销。删除后，所有与这条冲煮记录相关的统计也会被删除。</p>
        <div class="modal-action">
            <form method="dialog">
                <button class="btn btn-error" @click="$store.brewlogList.deleteRecord({{ record.id }})">删除</button>
                <button class="btn">取消</button>
            </form>
        </div>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>
{% endfor %}
</div>
{% endblock %}