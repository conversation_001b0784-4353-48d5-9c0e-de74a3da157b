{% extends "my/brewlog_base.html" %}
{% load static format_filters time_filters %}
{% block title %}后见之明 - 我的冲煮统计{% endblock %}
{% block extra_js %}
<script type="text/javascript" src="{% static 'js/blhdsght.js' %}"></script>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/blhdsght.css' %}">
{% endblock %}

{% block content %}
<div class="py-2 px-1 bg-base-200 mb-2 text-base-content">
    <div class="navbar w-full max-w-screen-xl mx-auto">
        <div class="flex-none w-[var(--nav-width)]"></div>
        <div class="flex-1 flex justify-center">
            我的冲煮统计
        </div>
        <div class="flex-none w-[var(--nav-width)] flex justify-end"></div>
    </div>
</div>

<div class="container mx-auto xl:max-w-5xl text-base-content px-4 mb-8">
    <!-- 时间范围选择 -->
    <div class="flex flex-wrap justify-center gap-2 mb-4">
        <div class="flex flex-wrap justify-center gap-2 w-full md:w-auto">
            {% for range_key, range_data in time_ranges.items %}
            <a href="?time_range={{ range_key }}"
               class="btn btn-sm {% if current_range == range_key and not selected_year %}{% else %}btn-ghost{% endif %}">
                {{ range_data.label }}
            </a>
            {% endfor %}
        </div>

        <div class="flex justify-center gap-2 w-full md:w-auto">
            <!-- 修改年份选择下拉框 -->
            <div class="dropdown dropdown-end dropdown-hover">
                <label tabindex="0" class="btn btn-sm {% if selected_year %}{% else %}btn-ghost{% endif %}">
                    {% if selected_year %}{{ selected_year }}年鉴{% else %}年鉴{% endif %}
                    <span class="icon-[heroicons--chevron-down-20-solid] ml-1"></span>
                </label>
                <ul tabindex="0"
                    class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-32">
                    {% for year in available_years %}
                    <li>
                        <a href="?year={{ year }}"
                           class="{% if selected_year == year|stringformat:'i' %}active{% endif %}">
                            {{ year }}年
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>

            <button
               x-data
               x-show="$store.globalState.hasAnyHiddenPanels()"
               @click="$store.globalState.showAllPanels()"
               class="btn btn-sm btn-ghost text-error">
                <span class="icon-[bx--reset] text-lg"></span>
            </button>
        </div>
    </div>

    {% if total_records > 0 %}
        <!-- 咖啡豆消耗力 -->
        <div x-data="brewlogHindsight('consumption-stats')"
             x-init="init()"
             class="mockup-browser border-base-300 border"
             x-show="!isHidden"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform scale-90"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-300"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-90">
            <div class="mockup-browser-toolbar">
                <div class="w-full px-3 flex justify-between items-center">
                    <div class="flex flex-row gap-3">
                        <button @click="hidePanel" class="w-3 h-3 rounded-full bg-[#ff605c] tooltip" data-tip="隐藏"></button>
                        <button @click="collapse" class="w-3 h-3 rounded-full bg-[#ffbd44] tooltip" data-tip="收起"></button>
                        <button @click="expand" class="w-3 h-3 rounded-full bg-[#00ca4e] tooltip" data-tip="展开"></button>
                    </div>
                    <div class="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-2 text-primary">
                        <span class="icon-[tabler--paper-bag] text-lg"></span>咖啡豆消耗力
                    </div>
                    <div class="flex flex-row gap-3 invisible">
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                    </div>
                </div>
            </div>
            <div class="border-base-300 border-t px-4 py-4 flex justify-center" x-show="!isCollapsed" x-transition>
                <div class="stats stats-vertical shadow bg-base-100 lg:stats-horizontal">
                    <div class="stat place-items-center">
                        <div class="stat-title">总用豆量</div>
                        <div class="stat-value">{{ stats.total_dose|format_number }}</div>
                        <div class="stat-desc">克</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">平均每次用豆</div>
                        <div class="stat-value">{{ stats.avg_dose|format_number }}</div>
                        <div class="stat-desc">克</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">半斤豆子可用</div>
                        <div class="stat-value">{{ stats.days_250g_lasts|format_number }}</div>
                        <div class="stat-desc">天</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">品尝过的豆子</div>
                        <div class="stat-value">{{ stats.tasted_beans_count }}</div>
                        <div class="stat-desc">款</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">冲煮用豆成本</div>
                        {% if stats.brewing_cost_records_count > 0 %}
                            <div class="stat-value">{{ stats.total_brewing_cost|format_number }}</div>
                            <div class="stat-desc">元</div>
                        {% else %}
                            <div class="stat-value">暂无数据</div>
                            <div class="stat-desc">请检查是否正确设置购买时间、价格和规格</div>
                        {% endif %}
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">平均每杯成本</div>
                        {% if stats.brewing_cost_records_count > 0 %}
                            <div class="stat-value">{{ stats.avg_cost_per_cup|format_number }}</div>
                            <div class="stat-desc">元</div>
                        {% else %}
                            <div class="stat-value">暂无数据</div>
                            <div class="stat-desc">请检查是否正确设置购买时间、价格和规格</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 冲煮习惯 -->
        <div x-data="brewlogHindsight('brewing-habits')"
             x-init="init()"
             class="mockup-browser border-base-300 border mt-4"
             x-show="!isHidden"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform scale-90"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-300"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-90">
            <div class="mockup-browser-toolbar">
                <div class="w-full px-3 flex justify-between items-center">
                    <div class="flex flex-row gap-3">
                        <button @click="hidePanel" class="w-3 h-3 rounded-full bg-[#ff605c] tooltip" data-tip="隐藏"></button>
                        <button @click="collapse" class="w-3 h-3 rounded-full bg-[#ffbd44] tooltip" data-tip="收起"></button>
                        <button @click="expand" class="w-3 h-3 rounded-full bg-[#00ca4e] tooltip" data-tip="展开"></button>
                    </div>
                    <div class="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-2 text-primary">
                        <span class="icon-[mdi--kettle-pour-over] text-lg"></span>冲煮习惯
                    </div>
                    <div class="flex flex-row gap-3 invisible">
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                    </div>
                </div>
            </div>
            <div class="border-base-300 border-t px-4 py-4 flex justify-center" x-show="!isCollapsed" x-transition>
                <div class="stats stats-vertical shadow bg-base-100 lg:stats-horizontal">
                    <div class="stat place-items-center">
                        <div class="stat-title">总冲煮次数</div>
                        <div class="stat-value">{{ stats.total_records }}</div>
                        <div class="stat-desc">次</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">平均每天冲煮</div>
                        <div class="stat-value">{{ stats.brews_per_day|format_number }}</div>
                        <div class="stat-desc">次</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">活跃天数</div>
                        <div class="stat-value">{{ stats.active_days }}</div>
                        <div class="stat-desc">天（{{ stats.active_rate }}%）</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">最常用冲煮方式</div>
                        <div class="stat-value">{{ stats.most_used_method|default:"暂无数据" }}</div>
                        <div class="stat-desc"></div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">最常用冲煮器具</div>
                        <div class="stat-value">{{ stats.most_used_brewer|default:"暂无数据" }}</div>
                        <div class="stat-desc">{{ stats.most_used_brewer_brand|default:"" }}</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">最常用磨豆机</div>
                        <div class="stat-value">{{ stats.most_used_grinder|default:"暂无数据" }}</div>
                        <div class="stat-desc">{{ stats.most_used_grinder_brand|default:"" }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 饮用习惯 -->
        <div x-data="brewlogHindsight('drinking-habits')"
             x-init="init()"
             class="mockup-browser border-base-300 border mt-4"
             x-show="!isHidden"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform scale-90"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-300"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-90">
            <div class="mockup-browser-toolbar">
                <div class="w-full px-3 flex justify-between items-center">
                    <div class="flex flex-row gap-3">
                        <button @click="hidePanel" class="w-3 h-3 rounded-full bg-[#ff605c] tooltip" data-tip="隐藏"></button>
                        <button @click="collapse" class="w-3 h-3 rounded-full bg-[#ffbd44] tooltip" data-tip="收起"></button>
                        <button @click="expand" class="w-3 h-3 rounded-full bg-[#00ca4e] tooltip" data-tip="展开"></button>
                    </div>
                    <div class="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-2 text-primary">
                        <span class="icon-[tabler--clock] text-lg"></span>饮用习惯
                    </div>
                    <div class="flex flex-row gap-3 invisible">
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                    </div>
                </div>
            </div>
            <div class="border-base-300 border-t px-4 py-4 flex justify-center" x-show="!isCollapsed" x-transition>
                <div class="stats stats-vertical shadow bg-base-100 lg:stats-horizontal">
                    <div class="stat place-items-center">
                        <div class="stat-title">平均每天喝</div>
                        <div class="stat-value">{{ stats.brews_per_active_day|format_number }}</div>
                        <div class="stat-desc">杯</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">热点时期</div>
                        <div class="stat-value">{{ stats.peak_period|default:"暂无数据" }}</div>
                        <div class="stat-desc">更爱在{{ stats.peak_period }}冲煮</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">热点日期</div>
                        <div class="stat-value">{{ stats.peak_weekday|default:"暂无规律" }}</div>
                        <div class="stat-desc">{% if stats.peak_weekday %}最常在{% if "、" in stats.peak_weekday %}这两天{% else %}这天{% endif %}冲煮{% endif %}</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">热点时刻</div>
                        <div class="stat-value">{{ stats.peak_time|format_peak_time|default:"暂无数据" }}</div>
                        <div class="stat-desc">最爱在这个时候冲煮</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消费习惯 -->
        <div x-data="brewlogHindsight('consumption-habits')"
             x-init="init()"
             class="mockup-browser border-base-300 border mt-4"
             x-show="!isHidden"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform scale-90"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-300"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-90">
            <div class="mockup-browser-toolbar">
                <div class="w-full px-3 flex justify-between items-center">
                    <div class="flex flex-row gap-3">
                        <button @click="hidePanel" class="w-3 h-3 rounded-full bg-[#ff605c] tooltip" data-tip="隐藏"></button>
                        <button @click="collapse" class="w-3 h-3 rounded-full bg-[#ffbd44] tooltip" data-tip="收起"></button>
                        <button @click="expand" class="w-3 h-3 rounded-full bg-[#00ca4e] tooltip" data-tip="展开"></button>
                    </div>
                    <div class="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-2 text-primary">
                        <span class="icon-[tabler--wallet] text-lg"></span>消费习惯
                    </div>
                    <div class="flex flex-row gap-3 invisible">
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                    </div>
                </div>
            </div>
            <div class="border-base-300 border-t px-4 py-4 flex justify-center" x-show="!isCollapsed" x-transition>
                <div class="stats stats-vertical shadow bg-base-100 lg:stats-horizontal">
                    {% if stats.most_repurchased_bean %}
                    <div class="stat place-items-center">
                        <div class="stat-title">最常回购的豆子</div>
                        <div class="stat-value">{{ stats.most_repurchased_bean }}</div>
                        <div class="stat-desc">{{ stats.most_repurchased_bean_roaster }}</div>
                    </div>
                    {% endif %}
                    {% if stats.most_visited_roaster %}
                    <div class="stat place-items-center">
                        <div class="stat-title">最常光顾的豆商</div>
                        <div class="stat-value">{{ stats.most_visited_roaster }}</div>
                        <div class="stat-desc">累计 {{ stats.most_visited_roaster_amount|format_number }} 元</div>
                    </div>
                    {% endif %}
                    <div class="stat place-items-center">
                        <div class="stat-title">咖啡豆支出</div>
                        <div class="stat-value">{{ stats.total_bean_costs|format_number }}</div>
                        <div class="stat-desc">元</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">设备支出</div>
                        <div class="stat-value">{{ stats.equipment_costs|format_number }}</div>
                        <div class="stat-desc">元</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 口味偏好 -->
        <div x-data="brewlogHindsight('drink-preference')"
             x-init="init()"
             class="mockup-browser border-base-300 border mt-4"
             x-show="!isHidden"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform scale-90"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-300"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-90">
            <div class="mockup-browser-toolbar">
                <div class="w-full px-3 flex justify-between items-center">
                    <div class="flex flex-row gap-3">
                        <button @click="hidePanel" class="w-3 h-3 rounded-full bg-[#ff605c] tooltip" data-tip="隐藏"></button>
                        <button @click="collapse" class="w-3 h-3 rounded-full bg-[#ffbd44] tooltip" data-tip="收起"></button>
                        <button @click="expand" class="w-3 h-3 rounded-full bg-[#00ca4e] tooltip" data-tip="展开"></button>
                    </div>
                    <div class="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-2 text-primary">
                        <span class="icon-[material-symbols-light--favorite] text-lg"></span>口味偏好
                    </div>
                    <div class="flex flex-row gap-3 invisible">
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                    </div>
                </div>
            </div>
            <div class="border-base-300 border-t px-4 py-4 flex flex-col gap-2 justify-center" x-show="!isCollapsed" x-transition>
                <div class="stats stats-vertical shadow bg-base-100 lg:stats-horizontal">
                    <div class="stat place-items-center">
                        <div class="stat-title">最爱的豆子</div>
                        <div class="stat-value">{{ stats.favorite_bean_roaster|default:"" }}{% if stats.favorite_bean_roaster and stats.favorite_bean_name %} - {% endif %}{{ stats.favorite_bean_name|default:"暂无数据" }}</div>
                        <div class="stat-desc">{% if stats.favorite_bean_rating %}平均得分 {{ stats.favorite_bean_rating }}{% endif %}</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">最常用的豆子</div>
                        <div class="stat-value">{{ stats.most_used_bean_roaster|default:"" }}{% if stats.most_used_bean_roaster and stats.most_used_bean_name %} - {% endif %}{{ stats.most_used_bean_name|default:"暂无数据" }}</div>
                        <div class="stat-desc">{% if stats.most_used_bean_name %}使用 {{ stats.most_used_bean_count }} 次{% endif %}</div>
                    </div>
                </div>
                <div class="stats stats-vertical shadow bg-base-100 lg:stats-horizontal">
                    <div class="stat place-items-center">
                        <div class="stat-title">最常品的产地</div>
                        <div class="stat-value">{{ stats.most_common_origin|default:"暂无数据" }}</div>
                        <div class="stat-desc"></div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">最常品的豆种</div>
                        <div class="stat-value">{{ stats.most_common_variety|default:"暂无数据" }}</div>
                        <div class="stat-desc"></div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">最常品的风味</div>
                        <div class="stat-value text-sm lg:text-base">{{ stats.most_common_flavors|join:"、"|default:"暂无数据" }}</div>
                        <div class="stat-desc"></div>
                    </div>
                </div>
                <div class="stats stats-vertical shadow bg-base-100 lg:stats-horizontal">
                    <div class="stat place-items-center">
                        <div class="stat-title">最爱的配方</div>
                        <div class="stat-value">{{ stats.favorite_recipe_name|default:"暂无数据" }}</div>
                        <div class="stat-desc">{% if stats.favorite_recipe_name %}平均得分 {{ stats.favorite_recipe_rating }}{% endif %}</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">最常用的配方</div>
                        <div class="stat-value">{{ stats.most_used_recipe_name|default:"暂无数据" }}</div>
                        <div class="stat-desc">{% if stats.most_used_recipe_name %}使用 {{ stats.most_used_recipe_count }} 次{% endif %}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 成就 -->
        {% if not selected_year %}
        <div x-data="brewlogHindsight('other-stats')"
             x-init="init()"
             class="mockup-browser border-base-300 border mt-4"
             x-show="!isHidden"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform scale-90"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-300"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-90">
            <div class="mockup-browser-toolbar">
                <div class="w-full px-3 flex justify-between items-center">
                    <div class="flex flex-row gap-3">
                        <button @click="hidePanel" class="w-3 h-3 rounded-full bg-[#ff605c] tooltip" data-tip="隐藏"></button>
                        <button @click="collapse" class="w-3 h-3 rounded-full bg-[#ffbd44] tooltip" data-tip="收起"></button>
                        <button @click="expand" class="w-3 h-3 rounded-full bg-[#00ca4e] tooltip" data-tip="展开"></button>
                    </div>
                    <div class="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-2 text-primary">
                        <span class="icon-[ph--confetti] text-lg"></span>成就（迄今）
                    </div>
                    <div class="flex flex-row gap-3 invisible">
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                        <div class="w-3 h-3"></div>
                    </div>
                </div>
            </div>
            <div class="border-base-300 border-t px-4 py-4 flex justify-center" x-show="!isCollapsed" x-transition>
                <div class="stats stats-vertical shadow bg-base-100 lg:stats-horizontal">
                    <div class="stat place-items-center">
                        <div class="stat-title">连续打卡记录</div>
                        <div class="stat-value">{{ stats.max_streak }}</div>
                        <div class="stat-desc">天</div>
                    </div>
                    <div class="stat place-items-center">
                        <div class="stat-title">总打卡次数</div>
                        <div class="stat-value">{{ stats.total_all_time_records }}</div>
                        <div class="stat-desc">次</div>
                    </div>
                    {% if stats.days_since_registration >= 1 %}
                    <div class="stat place-items-center">
                        <div class="stat-title">咖啡搭子已陪伴你</div>
                        <div class="stat-value">{{ stats.days_since_registration|timedelta_from_days|precise_timedelta }}</div>
                        <div class="stat-desc"></div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    {% else %}
    <div x-data="brewlogHindsight('no-record')"
            x-init="init()"
            class="mockup-browser border-base-300 border mt-4"
            x-show="!isHidden"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-90"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-90">
        <div class="mockup-browser-toolbar">
            <div class="w-full px-3 flex justify-between items-center">
                <div class="flex flex-row gap-3">
                    <button @click="hidePanel" class="w-3 h-3 rounded-full bg-[#ff605c] tooltip" data-tip="隐藏"></button>
                    <button @click="collapse" class="w-3 h-3 rounded-full bg-[#ffbd44] tooltip" data-tip="收起"></button>
                    <button @click="expand" class="w-3 h-3 rounded-full bg-[#00ca4e] tooltip" data-tip="展开"></button>
                </div>
                <div class="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-2 text-primary">
                    暂无数据
                </div>
                <div class="flex flex-row gap-3 invisible">
                    <div class="w-3 h-3"></div>
                    <div class="w-3 h-3"></div>
                    <div class="w-3 h-3"></div>
                </div>
            </div>
        </div>
        <div class="border-base-300 border-t px-4 py-4 flex justify-center" x-show="!isCollapsed" x-transition>
            <p>在选定的时间范围内没有找到冲煮记录</p>
        </div>
    </div>
    {% endif %}

    {% if error %}
    <div x-data="brewlogHindsight('error-output')" x-init="init()" class="mockup-browser border-base-300 border mt-4"
        x-show="!isHidden" x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-90" x-transition:enter-end="opacity-100 transform scale-100"
        x-transition:leave="transition ease-in duration-300" x-transition:leave-start="opacity-100 transform scale-100"
        x-transition:leave-end="opacity-0 transform scale-90">
        <div class="mockup-browser-toolbar">
            <div class="w-full px-3 flex justify-between items-center">
                <div class="flex flex-row gap-3">
                    <button @click="hidePanel" class="w-3 h-3 rounded-full bg-[#ff605c] tooltip" data-tip="隐藏"></button>
                    <button @click="collapse" class="w-3 h-3 rounded-full bg-[#ffbd44] tooltip" data-tip="收起"></button>
                    <button @click="expand" class="w-3 h-3 rounded-full bg-[#00ca4e] tooltip" data-tip="展开"></button>
                </div>
                <div class="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-2 text-primary">
                    出错了
                </div>
                <div class="flex flex-row gap-3 invisible">
                    <div class="w-3 h-3"></div>
                    <div class="w-3 h-3"></div>
                    <div class="w-3 h-3"></div>
                </div>
            </div>
        </div>
        <div class="border-base-300 border-t px-4 py-4 flex justify-center" x-show="!isCollapsed" x-transition>
            <p>{{ error }}</p>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}