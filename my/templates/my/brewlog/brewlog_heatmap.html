{% extends "my/brewlog_base.html" %}
{% load static %}
{% block title %}我的冲煮热图{% endblock %}
{% block extra_js %}
<script type="text/javascript" src="{% static 'js/blhtmp.js' %}"></script>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/blhtmp.css' %}">
{% endblock %}

{% block content %}
<div class="py-2 px-1 bg-base-200/90 mb-2 text-base-content sticky top-0 z-30 flex w-full justify-center backdrop-blur-sm transition-shadow duration-100 [transform:translate3d(0,0,0)]">
    <div class="navbar w-full max-w-screen-xl mx-auto">
        <div class="navbar-start lg:ml-4">
            <a href="{% url 'brewlog' %}" class="btn btn-ghost"><span class="icon-[ep--back]"></span></span>返回</a>
        </div>
        <div class="navbar-center" x-data="yearSelector()">
            <select class="select" 
                    x-model="selectedYear" 
                    @change="changeYear()">
                <template x-for="year in availableYears" :key="year">
                    <option :value="year" x-text="year + '年'"></option>
                </template>
            </select>
        </div>
        <div class="navbar-end">
        </div>
    </div>
</div>
<div class="container mx-auto xl:max-w-5xl text-base-content px-4 mb-8">
    <div class="heatmap-container">
        {% if error %}
            <div class="text-center py-8">
                <p class="text-error">生成热力图时发生错误，请稍后重试</p>
            </div>
        {% elif not calendar_data %}
            <div class="text-center py-8">
                <p class="text-gray-500">暂无冲煮记录数据</p>
            </div>
        {% else %}
            <div class="heatmap-scroll-container" 
                 x-data="heatmapData"
                 x-init="$nextTick(() => scrollToCurrentMonth())"
                 data-calendar='{{ calendar_data|safe }}'
                 data-current-month="{{ current_month_index }}">
                <div class="heatmap-wrapper">
                    <div class="month-labels">
                        {% for month in months %}
                            <span class="month-label">{{ month }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="weekday-labels">
                        {% for weekday in weekdays %}
                            <span class="weekday-label">{{ weekday }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="heatmap-grid">
                        <template x-for="cell in cells" :key="cell.date">
                            <div class="heatmap-cell"
                                 :style="getCellStyle(cell)"
                                 @click="showDetails(cell)"
                                 :title="`${cell.date}: ${cell.count} 次冲煮`">
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- 详情弹窗 -->
<div x-data="{ showModal: false, selectedDate: '', recordCount: 0 }"
     @show-details.window="showModal = true; selectedDate = $event.detail.date; recordCount = $event.detail.count"
     x-show="showModal"
     class="modal modal-bottom sm:modal-middle"
     :class="{ 'modal-open': showModal }">
    <div class="modal-box text-base-content">
        <h3 class="font-bold text-lg" x-text="selectedDate + ' 的冲煮记录'"></h3>
        <p class="py-4" x-text="'这一天你完成了 ' + recordCount + ' 次冲煮'"></p>
        <div class="modal-action">
            <button class="btn" @click="showModal = false">关闭</button>
            <a :href="selectedDate ? '/my/brewlog?date_from=' + selectedDate + '&date_to=' + selectedDate : '#'" 
               class="btn btn-primary"
               :class="{ 'btn-disabled': !selectedDate }">查看</a>
        </div>
    </div>
</div>
{% endblock %}