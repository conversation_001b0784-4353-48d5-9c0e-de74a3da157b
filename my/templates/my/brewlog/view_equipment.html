{% extends "my/brewlog_base.html" %}
{% load static %}
{% load time_filters format_filters %}
{% block title %}{{ equipment.brand }} {{ equipment }} - 我的咖啡设备{% endblock %}

{% block content %}
<div class="w-screen bg-base-200/90 mb-2 text-base-content sticky top-0 z-30 justify-center backdrop-blur-sm transition-shadow duration-100 [transform:translate3d(0,0,0)]">
    <div class="navbar w-full max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div class="navbar-start lg:ml-4">
            <a href="{% url 'equipment_list' %}" class="btn btn-ghost">
                <span class="icon-[uiw--left] text-lg"></span>
                <span class="hidden md:block">返回</span>
            </a>
        </div>
        <div class="navbar-center" x-data="{ showEquipName: false }" @scroll.window="showEquipName = window.scrollY > 100">
            <span x-show="!showEquipName">设备详情</span>
            <span x-show="showEquipName" x-cloak class="truncate max-w-[200px] md:max-w-[300px]">
                {{ equipment.brand }} {{ equipment }}
            </span>
        </div>
        <div class="navbar-end">
            {% if equipment.is_deleted %}
            <a class="btn btn-disabled">
                <span class="icon-[material-symbols--edit-square-outline-rounded] text-lg"></span>
                <span class="hidden md:block">修改</span>
            </a>
            {% else %}
            <a href="{% url 'edit_equipment_page' equipment.id %}" class="btn btn-ghost">
                <span class="icon-[material-symbols--edit-square-outline-rounded] text-lg"></span>
                <span class="hidden md:block">修改</span>
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
    <div class="mx-auto max-w-2xl xl:max-w-3xl p-4 text-base-content rounded-lg mt-2">
        <div class="flex justify-center items-center dark:bg-gray-800 w-fit mx-auto">
            <div class="relative dark:text-white">
                <div class="relative p-6 bg-white dark:bg-gray-800 border-2 border-primary dark:border-gray-300 rounded-lg">
                    <div class="flex flex-col text-center gap-4">
                        <div class="absolute -left-3 top-7 -translate-y-1/2 flex flex-col gap-0.5">
                            {% if equipment.is_favorite %}
                            <div class="w-1.5 h-6 bg-yellow-500 rounded-full tooltip tooltip-right touch-none" tabindex="0" data-tip="已设为首选：新增冲煮记录时将被自动选取。"></div>
                            <div class="w-1.5 h-6 bg-success rounded-full tooltip tooltip-right touch-none" tabindex="0" data-tip="使用中：非删除或归档状态的设备。"></div>
                            {% elif equipment.is_archived %}
                            <div class="w-1.5 h-6 bg-base-300 rounded-full tooltip tooltip-right touch-none" tabindex="0" data-tip="已归档：不会在冲煮记录的设备选项中出现，不能被设为首选。"></div>
                            {% elif equipment.is_deleted %}
                            <div class="w-1.5 h-6 bg-error rounded-full tooltip tooltip-right touch-none" tabindex="0" data-tip="已删除：不能再次使用。"></div>
                            {% else %}
                            <div class="w-1.5 h-6 bg-success rounded-full tooltip tooltip-right touch-none" tabindex="0" data-tip="使用中：非删除或归档状态的设备。"></div>
                            {% endif %}
                        </div>
                        <h2 class="text-xl font-black lg:text-4xl">{{ equipment }}</h2>
                        {% if equipment.brand %}<span class="opacity-70">{{ equipment.brand }}</span>{% endif %}
                        <p class="text-sm mb-4">
                            {% if equipment.type == 'GADGET_KIT' %}
                                <span class="opacity-50">创建于 {{ equipment.created_at|date:"Y-m-d" }}</span>
                            {% else %}
                                {% if equipment.purchase_price %}💰{{ equipment.purchase_price|format_number }} {% endif %}
                                <span class="opacity-50">购于 {{ equipment.created_at|date:"Y-m-d" }}</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="divider"></div>
                    <div class="mt-2 flex flex-wrap justify-center items-center gap-4">
                        <div class="flex h-20 w-20 flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">类型</div>
                            <div class="flex flex-row items-center justify-center">
                                <span class="font-bold opacity-80">
                                    {% with method=equipment.type %}
                                    {% if method == 'GRINDER' %}磨豆机
                                    {% elif method == 'BREWER' %}冲煮器具
                                    {% elif method == 'GADGET' %}小工具
                                    {% elif method == 'GADGET_KIT' %}小工具组合
                                    {% endif %}
                                    {% endwith %}
                                </span>
                            </div>
                        </div>
                        {% if equipment.get_brew_method_display %}
                        <div class="flex h-20 w-20 flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">赛道</div>
                            <div class="flex flex-row items-center justify-center">
                                <span class="font-bold opacity-80">{{ equipment.get_brew_method_display }}</span>
                            </div>
                        </div>
                        {% endif %}
                        {% if equipment.get_grinder_purpose_display %}
                        <div class="flex h-20 w-20 flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">用途</div>
                            <div class="flex flex-row items-center justify-center">
                                <span class="font-bold opacity-80">{{ equipment.get_grinder_purpose_display }}</span>
                            </div>
                        </div>
                        {% endif %}
                        {% if equipment.type == 'GRINDER' and equipment.grind_size_preset %}
                        <div class="flex h-20 w-20 flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">研磨设置</div>
                            <div class="flex flex-row items-center justify-center">
                                <span class="font-bold opacity-80">{{ equipment.grind_size_preset }}</span>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <div class="mx-auto xl:max-w-3xl p-4 w-full lg:w-2/3">
            {% if equipment.notes %}
            <div role="alert" class="alert mt-4 text-start">
                <span class="icon-[ph--note-duotone]"></span>
                <div class="break-all">{{ equipment.notes|linebreaks }}</div>
            </div>
            {% endif %}
        </div>
        {% if equipment.type == 'GADGET_KIT' and equipment.gadget_components.exists %}
        <div class="mx-auto xl:max-w-3xl p-4 text-base-content">
            <div class="divider text-base-content/30">组合内容</span></div>
            <div class="flex flex-col gap-2">
                {% for gadget in equipment.gadget_components.all %}
                <a href="{% url 'view_equipment' gadget.id %}" 
                    class="flex items-center justify-between p-3 hover:bg-base-200 rounded-lg transition-colors">
                    <div class="flex items-center gap-2 min-w-0 max-w-[calc(100%-2rem)]">
                        <span class="icon-[fa6-solid--kitchen-set] shrink-0"></span>
                        <div class="flex flex-col min-w-0 max-w-full">
                            <span class="truncate max-w-full">{{ gadget.name }}</span>
                            {% if gadget.brand %}
                            <span class="opacity-60 text-sm truncate max-w-full">{{ gadget.brand }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <span class="icon-[material-symbols--chevron-right-rounded] opacity-50 shrink-0"></span>
                </a>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        <div class="mx-auto xl:max-w-3xl p-4 text-base-content">
            <div class="divider text-base-content/30">提要</div>
            <div class="text-center">
                <div class="stats shadow bg-base-100 mt-4">
                    <div class="stat">
                        <div class="stat-title">使用次数</div>
                        <div class="stat-value">{{ equipment.usage_count }}</div>
                        <div class="stat-desc">次</div>
                    </div>
                    
                    <div class="stat">
                        <div class="stat-title">最近使用</div>
                        <div class="stat-value">
                            {% if equipment.usage_count > 0 %}
                                {% with days=equipment.last_used|days_until_now %}
                                    {{ days|days_ago_value }}
                                    <div class="stat-desc">{{ days|days_ago_unit }}</div>
                                {% endwith %}
                            {% else %}
                                -
                                <div class="stat-desc">尚未使用</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 