{% extends "my/brewlog_base.html" %}
{% load static %}
{% load format_filters %}

{% block title %}我的配方{% endblock %}

{% block extra_js %}
<script type="text/javascript" src="{% static 'js/blrcplst.js' %}"></script>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/brewlog.css' %}">
{% endblock %}

{% block content %}
<div x-data x-init="$store.filterState.init()">
    <div class="py-2 px-1 bg-base-200 mb-2 text-base-content">
        <div class="navbar w-full max-w-screen-xl mx-auto">
            <div class="flex-none w-[var(--nav-width)]">
                <div class="lg:ml-4 btn btn-ghost" @click="$store.filterState.toggle()">
                    <span class="icon-[bi--filter-circle] text-lg" 
                          x-show="!$store.filterState.isOpen"></span>
                    <span class="icon-[bi--filter-circle-fill] text-lg" 
                          x-show="$store.filterState.isOpen"></span>
                    <span class="hidden lg:block">筛选</span>
                </div>
            </div>
            <div class="flex-1 flex justify-center">
                我的配方
            </div>
            <div class="flex-none w-[var(--nav-width)] flex justify-end">
                <button @click="document.getElementById('manage_tags_modal').showModal()" class="btn btn-ghost">
                    <span class="hidden lg:block">标签</span>
                    <span class="icon-[mdi--tag-multiple-outline] text-2xl"></span>
                </button>
            </div>
        </div>
    </div>

    <div class="flex flex-wrap gap-2 items-center mb-4 px-4 justify-center text-base-content"
         x-show="$store.filterState.isOpen"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2">
        
        <!-- 标签筛选 -->
        <div class="dropdown">
            <div tabindex="0" role="button" class="btn btn-sm">
                标签
                {% if current_tag %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                <li><a href="?{% if current_sort %}sort_by={{ current_sort }}{% endif %}"
                    class="{% if not current_tag %}menu-active{% endif %}">全部</a></li>
                {% for tag in tags %}
                <li><a href="?tag={{ tag.id }}{% if current_sort %}&sort_by={{ current_sort }}{% endif %}"
                    class="{% if current_tag == tag.id|stringformat:'s' %}menu-active{% endif %}">{{ tag.name }}</a></li>
                {% endfor %}
            </ul>
        </div>

        <!-- 排序 -->
        <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-sm">
                排序
                {% if current_sort %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                <li><a href="?sort_by=recent{% if current_tag %}&tag={{ current_tag }}{% endif %}"
                    class="{% if current_sort == 'recent' %}menu-active{% endif %}">
                    最近使用
                </a></li>
                <li><a href="?sort_by=count{% if current_tag %}&tag={{ current_tag }}{% endif %}"
                    class="{% if current_sort == 'count' %}menu-active{% endif %}">
                    使用次数
                </a></li>
            </ul>
        </div>

        <!-- 重置按钮 -->
        <div>
            {% if current_tag or current_sort %}
            <a href="{% url 'recipe_list' %}" class="btn btn-sm btn-ghost text-success">
                <span class="icon-[bx--reset] text-lg"></span>
            </a>
            {% endif %}
        </div>
    </div>

    <div class="container mx-auto xl:max-w-7xl text-base-content px-2 mb-8" x-data>
        <div class="w-full px-4 md:px-20">
            <div class="grid grid-cols-1 grid-rows-1 md:grid-cols-2 gap-4 mb-8">
                {% for recipe in recipes %}
                    <div class="border-2 border-base-content/5 hover:shadow-lg active:shadow-lg rounded-lg overflow-visible" id="recipe_{{ recipe.id }}">
                        <div class="p-4">
                            <div class="card-actions justify-between flex items-center">
                                <div class="text-xs">
                                    <span class="opacity-50">冲煮过</span>
                                    {{ recipe.use_count }}
                                    <span class="opacity-50">次，最后用于</span>
                                    {{ recipe.last_used_at|timesince }}前
                                </div>
                                <div class="relative dropdown dropdown-end">
                                    <div tabindex="0" role="button" class="btn btn-ghost btn-circle text-2xl">
                                        <span class="icon-[mi--options-vertical]"></span>
                                    </div>
                                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-50 w-52 p-2 shadow-lg absolute">
                                        <li>
                                            <button @click="$store.recipeList.previewQuickBrew({{ recipe.id }})">
                                                <span class="icon-[mdi--eye-outline]"></span> 预览速记
                                            </button>
                                        </li>
                                        <li>
                                            <button @click="$store.recipeList.quickBrew({{ recipe.id }})">
                                                <span class="icon-[mdi--lightning-bolt-outline]"></span> 速记
                                            </button>
                                        </li>
                                        {% with latest_record=recipe.get_latest_record %}
                                        {% if latest_record %}
                                        <li>
                                            <a href="{% url 'view_record_page' latest_record.id %}" target="_blank">
                                                <span class="icon-[game-icons--portal]"></span> 传送门
                                            </a>
                                        </li>
                                        {% endif %}
                                        {% endwith %}
                                        <li>
                                            <button @click="document.getElementById('manage_recipe_tags_modal_{{ recipe.id }}').showModal()">
                                                <span class="icon-[mdi--tag-multiple-outline]"></span> 贴标签
                                            </button>
                                        </li>
                                        <li>
                                            <button @click="document.getElementById('rename_recipe_modal_{{ recipe.id }}').showModal()">
                                                <span class="icon-[mdi--rename-outline]"></span> 重命名
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="flex gap-4 items-baseline">
                                <h5 class="text-lg font-semibold">{{ recipe.recipe_name }}</h5>
                            </div>
                            <div class="flex flex-wrap gap-2 mt-2 tags-container">
                                {% if recipe.tags.all %}
                                {% for tag in recipe.tags.all %}
                                <span class="badge badge-soft rounded-full shadow-xs badge-info">{{ tag.name }}</span>
                                {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- 配方贴标签模态框 -->
                    <dialog id="manage_recipe_tags_modal_{{ recipe.id }}" class="modal text-base-content">
                        <div class="modal-box">
                            <h3 class="text-lg font-bold mb-4">为配方「{{ recipe.recipe_name }}」贴标签 <span class="text-xs font-normal">(可多选)</span></h3>
                            <div class="flex flex-wrap gap-2">
                                {% for tag in tags %}
                                <button class="btn btn-sm {% if tag in recipe.tags.all %}btn-primary{% else %}btn-outline{% endif %}"
                                        @click="$store.recipeList.toggleRecipeTag({{ recipe.id }}, {{ tag.id }})"
                                        data-tag-id="{{ tag.id }}">
                                    {{ tag.name }}
                                </button>
                                {% empty %}
                                <div class="alert alert-warning">
                                    <span class="icon-[material-symbols--info-outline]"></span>
                                    <div>
                                        <p>暂无可用标签</p>
                                        <p class="text-sm opacity-75">请先点击页面右上方的标签管理按钮添加新的标签</p>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <div class="modal-action">
                                <form method="dialog">
                                    <button class="btn">关闭</button>
                                </form>
                            </div>
                        </div>
                        <form method="dialog" class="modal-backdrop">
                            <button>close</button>
                        </form>
                    </dialog>

                    <!-- 重命名模态框 -->
                    <dialog id="rename_recipe_modal_{{ recipe.id }}" class="modal text-base-content">
                        <div class="modal-box" x-data="{ 
                            newName: '{{ recipe.recipe_name }}',
                            isLoading: false,
                            errorMsg: '',
                            async handleSubmit() {
                                if (!this.newName.trim()) {
                                    this.errorMsg = '配方名称不能为空';
                                    return;
                                }
                                this.isLoading = true;
                                this.errorMsg = '';
                                try {
                                    await $store.recipeList.renameRecipe({{ recipe.id }}, this.newName);
                                } catch (error) {
                                    this.errorMsg = error.message || '重命名失败';
                                } finally {
                                    this.isLoading = false;
                                }
                            }
                        }">
                            <h3 class="text-lg font-bold mb-4">重命名配方</h3>
                            <form @submit.prevent="handleSubmit">
                                <fieldset class="fieldset">
                                    <legend class="fieldset-legend">请输入新的配方名称</legend>
                                    <input type="text" x-model="newName" class="input input-bordered w-full" :class="{'input-error': errorMsg}" required>
                                    <p class="fieldset-label text-error" x-text="errorMsg"></p>
                                </fieldset>
                                
                                <div class="alert alert-warning mt-4">
                                    <span class="icon-[material-symbols--info-outline]"></span>
                                    <span>此操作将更新所有使用此配方名称的冲煮记录</span>
                                </div>

                                <div class="modal-action">
                                    <button type="button" class="btn" @click="$el.closest('dialog').close()">取消</button>
                                    <button type="submit" class="btn btn-primary" :disabled="isLoading">
                                        <span class="loading loading-spinner" x-show="isLoading"></span>
                                        确认重命名
                                    </button>
                                </div>
                            </form>
                        </div>
                        <form method="dialog" class="modal-backdrop">
                            <button>close</button>
                        </form>
                    </dialog>
                {% empty %}
                    <div class="text-center py-12 text-base-content/60 col-span-2">
                        <p class="text-lg">暂无配方</p>
                        <p class="text-sm mt-2">在冲煮记录中填写配方名称，系统会自动收集并整理成配方册</p>
                    </div>
                {% endfor %}
            </div>
            <div class="alert bg-base-100 text-sm opacity-50 my-8">
                <span class="icon-[material-symbols--info-outline]"></span>
                <div>
                    <p>在此页面，你可以查看所有相同配方名称的冲煮记录汇总配方，并为每个配方关联自定义标签，方便分类与筛选。</p>
                    <p>通过配方菜单中的「速记」功能，可快速新增冲煮记录——系统会自动复制最近10次使用频率最高的冲煮参数。这一功能比在冲煮记录列表复制配方更快捷！</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 标签管理模态框 -->
<dialog id="manage_tags_modal" class="modal text-base-content" x-data="tagManager">
    <div class="modal-box">
        <h3 class="text-lg font-bold mb-4">管理标签</h3>
        
        <!-- 添加新标签 -->
        <div class="form-control mb-4">
            <div class="join w-full">
                <div class="w-full">
                    <label class="input join-item flex items-center gap-2 w-full">
                        <span class="icon-[mdi--tag-multiple-outline] text-lg"></span>
                        <input type="text" 
                               placeholder="添加一个新的标签" 
                               class="grow input input-ghost focus:outline-hidden"
                               x-model="newTagName"
                               @keyup.enter="addTag()"
                               :disabled="isSubmitting"
                               required/>
                    </label>
                </div>
                <button class="btn btn-neutral join-item" 
                        @click="addTag()"
                        :disabled="isSubmitting">
                    <span x-show="!isSubmitting">保存</span>
                    <span x-show="isSubmitting" class="loading loading-spinner"></span>
                </button>
            </div>
        </div>

        <!-- 现有标签列表 -->
        <div class="flex flex-wrap gap-2">
            {% for tag in tags %}
            <div class="badge badge-ghost badge-lg gap-2" x-data="{ editing: false, name: '{{ tag.name }}' }">
                <template x-if="!editing">
                    <span x-text="name"></span>
                </template>
                <template x-if="editing">
                    <input type="text" 
                           x-model="name"
                           @keyup.enter="$store.recipeList.editTag({{ tag.id }}, name); editing = false"
                           @keyup.escape="editing = false"
                           class="input input-xs w-20">
                </template>
                <button class="btn btn-xs btn-ghost btn-circle"
                        @click="editing ? ($store.recipeList.editTag({{ tag.id }}, name), editing = false) : editing = true"
                        data-tag-id="{{ tag.id }}">
                    <span class="icon-[material-symbols--edit-square-outline-rounded]" x-show="!editing"></span>
                    <span class="icon-[mdi--check]" x-show="editing"></span>
                </button>
                <button class="btn btn-xs btn-ghost btn-circle"
                        @click="$store.recipeList.deleteTag({{ tag.id }})"
                        data-delete-tag-id="{{ tag.id }}">
                    <span class="icon-[material-symbols--delete]"></span>
                </button>
            </div>
            {% endfor %}
        </div>

        <div class="modal-action">
            <form method="dialog">
                <button class="btn">关闭</button>
            </form>
        </div>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>

<!-- 预览速记模态框 -->
<dialog id="preview_quick_brew_modal" class="modal text-base-content" x-data>
    <div class="modal-box max-w-7xl h-[90vh] p-0">
        <div id="preview_content" class="h-full overflow-y-auto">
            <!-- 预览内容将通过HTMX动态加载 -->
        </div>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>

{% endblock %} 