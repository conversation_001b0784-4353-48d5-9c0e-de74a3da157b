{% extends "my/brewlog_base.html" %}
{% load static %}
{% block title %}编辑设备{% endblock %}
{% block extra_js %}
<script type="text/javascript" src="{% static 'js/bldtqpmnt.js' %}"></script>
{% endblock %}

{% block content %}
<div>
    {% if equipment.is_deleted %}
    <div class="alert alert-error shadow-lg m-4 w-fit">
        <span class="icon-[ph--warning-bold]"></span>
        <span>该设备已被删除，无法编辑</span>
    </div>
    {% else %}
    <form method="POST" x-data="{ submitting: false }" @submit="submitting = true">
        {% csrf_token %}
        <div class="py-2 px-1 bg-base-200/90 mb-2 text-base-content sticky top-0 z-30 w-full backdrop-blur-sm transition-shadow duration-100 [transform:translate3d(0,0,0)]">
            <div class="navbar w-full max-w-screen-xl mx-auto">
                <div class="navbar-start lg:ml-4">
                    <a href="{% url 'equipment_list' %}" class="btn btn-ghost"><span class="icon-[ep--back]"></span></span>取消</a>
                </div>
                <div class="navbar-center">
                    <span>编辑冲煮设备</span>
                </div>
                <div class="navbar-end">
                    <button type="submit" class="btn btn-primary" :disabled="submitting">
                        <span x-show="!submitting" class="icon-[mdi--floppy]"></span>
                        <span x-show="submitting" class="loading loading-spinner loading-xs"></span>
                        保存
                    </button>
                </div>
            </div>
        </div>
        <div class="container mx-auto">
            <div class="mx-auto xl:max-w-3xl text-base-content">
                <div class="m-4 p-4 rounded-lg border border-base-300 bg-base-200 grid grid-cols-1 gap-4">
                    <div class="form-group">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">设备类型：<span class="opacity-60">{{ equipment.get_type_display }}</span></span>
                            </label>
                            <input type="hidden" name="type" value="{{ equipment.type }}">
                        </div>
                        {% if form.type.errors %}
                        <div class="text-error text-sm mt-1">
                            {% for error in form.type.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="form-group brew-method-group" style="display: none;">
                        <select name="brew_method" class="select w-full {% if form.brew_method.errors %}select-error{% endif %}">
                            <option value="" disabled>请选择赛道</option>
                            {% for value, label in form.brew_method.field.choices %}
                                {% if value %}
                                    <option value="{{ value }}" {% if form.brew_method.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                        {% if form.brew_method.errors %}
                        <div class="text-error text-sm mt-1">
                            {% for error in form.brew_method.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="form-group grinder-purpose-group" style="display: none;">
                        <select name="grinder_purpose" class="select w-full {% if form.grinder_purpose.errors %}select-error{% endif %}">
                            <option value="" disabled>请选择用途</option>
                            {% for value, label in form.grinder_purpose.field.choices %}
                                {% if value %}
                                    <option value="{{ value }}" {% if form.grinder_purpose.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                        {% if form.grinder_purpose.errors %}
                        <div class="text-error text-sm mt-1">
                            {% for error in form.grinder_purpose.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="form-group grind-size-preset-group" style="display: none;">
                        <label class="input w-full{% if form.grind_size_preset.errors %} input-error{% endif %}">
                            研磨设置
                            <input type="text" name="grind_size_preset" 
                                class="grow input input-ghost focus:outline-hidden" 
                                value="{{ form.grind_size_preset.value|default:'' }}"
                                placeholder="目前所设置的刻度或挡位" />
                            <span class="badge badge-ghost opacity-80">选填</span>
                        </label>
                        {% if form.grind_size_preset.errors %}
                        <div class="text-error text-sm mt-1">
                            {% for error in form.grind_size_preset.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label class="input w-full{% if form.name.errors %} input-error{% endif %}">
                            名称
                            <input type="text" name="name" class="grow input input-ghost focus:outline-hidden" value="{{ form.name.value|default:'' }}" />
                        </label>
                        {% if form.name.errors %}
                        <div class="text-error text-sm mt-1">
                            {% for error in form.name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label class="input w-full{% if form.brand.errors %} input-error{% endif %}">
                            品牌
                            <input type="text" name="brand" class="grow input input-ghost focus:outline-hidden" value="{{ form.brand.value|default:'' }}" />
                            <span class="badge badge-ghost opacity-80">选填</span>
                        </label>
                        {% if form.brand.errors %}
                        <div class="text-error text-sm mt-1">
                            {% for error in form.brand.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="form-control w-full">
                        <label class="input w-full">
                            {{ form.created_at.label }}
                            {{ form.created_at }}
                            {% if form.created_at.errors %}
                            <label class="label">
                                <span class="label-text-alt text-error">{{ form.created_at.errors.0 }}</span>
                            </label>
                            {% endif %}
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="input w-full">
                            购买价格
                            {{ form.purchase_price }}
                            <span class="badge badge-ghost opacity-80">选填</span>
                        </label>
                    </div>
                    <div class="form-group gadget-kit-group" style="display: none;">
                        <div class="form-control flex flex-col gap-4">
                            <label class="label">
                                <span class="label-text">组合</span>
                                <span class="opacity-50 text-xs">(请选择常用组合)</span>
                            </label>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                                {% for gadget in form.gadget_components.field.queryset %}
                                <label class="label cursor-pointer justify-start gap-4">
                                    <input type="checkbox" 
                                           name="gadget_components" 
                                           value="{{ gadget.id }}"
                                           class="checkbox checkbox-primary"
                                           {% if gadget in form.instance.gadget_components.all %}checked{% endif %}
                                           {% if gadget.is_archived %}disabled{% endif %}>
                                    <span class="label-text flex items-center gap-2">
                                        {% if gadget.brand %}
                                        <span class="opacity-60 text-sm">{{ gadget.brand }}</span>
                                        {% endif %}
                                        <span>{{ gadget.name }}</span>
                                        {% if gadget.is_archived %}
                                        <span class="badge">已归档</span>
                                        {% endif %}
                                    </span>
                                </label>
                                {% endfor %}
                            </div>
                            {% if form.gadget_components.errors %}
                            <div class="text-error text-sm mt-1">
                                {% for error in form.gadget_components.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="form-group relative" 
                        x-data="{ charCount: '{{ form.notes.value|default:''|length }}' }" 
                        @input-change.window="charCount = $event.detail">
                        {{ form.notes }}
                        <div class="absolute bottom-2 right-2 text-xs" 
                                :class="charCount >= 500 ? 'text-error' : 'text-neutral-400'"
                                x-text="`${charCount}/500`"></div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    {% endif %}
</div>
{% endblock %}