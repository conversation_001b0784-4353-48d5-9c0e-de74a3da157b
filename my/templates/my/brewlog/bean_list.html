{% extends "my/brewlog_base.html" %}
{% load static %}
{% load calculation_filters format_filters time_filters %}
{% block title %}我的咖啡豆{% endblock %}
{% block extra_js %}
<script type="text/javascript" src="{% static 'js/brewlog.js' %}"></script>
<script type="text/javascript" src="{% static 'js/blbnlst.js' %}"></script>
<script type="text/javascript" src="{% static 'js/blbnccrrnc.js' %}"></script>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/brewlog.css' %}">
{% endblock %}
{% block content %}
<div x-data x-init="$store.filterState.init()">
    <div class="py-2 px-1 bg-base-200 mb-2 text-base-content">
        <div class="navbar w-full max-w-screen-xl mx-auto">
            <div class="flex-none w-[var(--nav-width)]">
                <div class="lg:ml-4 btn btn-ghost" @click="$store.filterState.toggle()">
                    <span class="icon-[bi--filter-circle] text-lg" 
                          x-show="!$store.filterState.isOpen"></span>
                    <span class="icon-[bi--filter-circle-fill] text-lg" 
                          x-show="$store.filterState.isOpen"></span>
                    <span class="hidden lg:block">筛选</span>
                </div>
            </div>
            <div class="flex-1 flex justify-center">
                我的咖啡豆
            </div>
            <div class="flex-none w-[var(--nav-width)] flex justify-end gap-2">
                <a href="{% url 'add_bean_page' %}" 
                   class="btn btn-ghost"
                   x-data="{ loading: false }"
                   @click="loading = true"
                   :class="{ 'btn-disabled': loading }">
                    <span class="hidden lg:block">新增</span>
                    <span x-show="!loading" class="icon-[icons8--plus] text-2xl"></span>
                    <span x-show="loading" class="loading loading-spinner"></span>
                </a>
            </div>
        </div>
    </div>
    <div class="flex flex-wrap gap-2 items-center mb-4 px-4 justify-center text-base-content"
         x-show="$store.filterState.isOpen"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2">
        <div class="dropdown">
            <div tabindex="0" role="button" class="btn btn-sm">
                烘焙度
                {% if current_roast_level %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                <li><a href="?{% if current_sort %}sort_by={{ current_sort }}&{% endif %}{% if current_process %}process={{ current_process }}&{% endif %}{% if current_rating_range %}rating_range={{ current_rating_range }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if not current_roast_level %}menu-active{% endif %}">全部</a></li>
                {% for value, label in roast_levels %}
                <li><a href="?roast_level={{ value }}{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_roast_level == value|stringformat:'i' %}menu-active{% endif %}">{{ label }}</a></li>
                {% endfor %}
            </ul>
        </div>

        <div class="dropdown">
            <div tabindex="0" role="button" class="btn btn-sm">
                处理法
                {% if current_process %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                <li><a href="?{% if current_sort %}sort_by={{ current_sort }}&{% endif %}{% if current_roast_level %}roast_level={{ current_roast_level }}&{% endif %}{% if current_rating_range %}rating_range={{ current_rating_range }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if not current_process %}menu-active{% endif %}">全部</a></li>
                {% for process_code, process_display in processes %}
                <li><a href="?process={{ process_code }}{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_process == process_code %}menu-active{% endif %}">{{ process_display }}</a></li>
                {% endfor %}
            </ul>
        </div>

        <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-sm">
                豆商
                {% if current_roaster %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52 max-h-64 overflow-y-auto">
                <li><a href="?{% if current_sort %}sort_by={{ current_sort }}&{% endif %}{% if current_roast_level %}roast_level={{ current_roast_level }}&{% endif %}{% if current_process %}process={{ current_process }}&{% endif %}{% if current_rating_range %}rating_range={{ current_rating_range }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if not current_roaster %}menu-active{% endif %}">全部</a></li>
                {% for roaster_name in roasters %}
                <li><a href="?roaster={{ roaster_name }}{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_roaster == roaster_name %}menu-active{% endif %}">{{ roaster_name }}</a></li>
                {% endfor %}
            </ul>
        </div>

        <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-sm">
                豆种
                {% if current_variety %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52 max-h-64 overflow-y-auto">
                <li><a href="?{% if current_sort %}sort_by={{ current_sort }}&{% endif %}{% if current_roast_level %}roast_level={{ current_roast_level }}&{% endif %}{% if current_process %}process={{ current_process }}&{% endif %}{% if current_rating_range %}rating_range={{ current_rating_range }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}"
                    class="{% if not current_variety %}menu-active{% endif %}">全部</a></li>
                {% for variety_name in varieties %}
                <li><a href="?variety={{ variety_name }}{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}"
                    class="{% if current_variety == variety_name %}menu-active{% endif %}">{{ variety_name }}</a></li>
                {% endfor %}
            </ul>
        </div>

        <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-sm">
                评分
                {% if current_rating_range %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                <li><a href="?{% if current_sort %}sort_by={{ current_sort }}&{% endif %}{% if current_roast_level %}roast_level={{ current_roast_level }}&{% endif %}{% if current_process %}process={{ current_process }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if not current_rating_range %}menu-active{% endif %}">全部</a></li>
                
                <li><a href="?rating_range=8-10{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_rating_range == '8-10' %}menu-active{% endif %}">
                    <div class="flex gap-0.5">
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                    </div>
                </a></li>
                
                <li><a href="?rating_range=6-8{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_rating_range == '6-8' %}menu-active{% endif %}">
                    <div class="flex gap-0.5">
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                    </div>
                </a></li>
                
                <li><a href="?rating_range=4-6{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_rating_range == '4-6' %}menu-active{% endif %}">
                    <div class="flex gap-0.5">
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                    </div>
                </a></li>
                
                <li><a href="?rating_range=2-4{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_rating_range == '2-4' %}menu-active{% endif %}">
                    <div class="flex gap-0.5">
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                    </div>
                </a></li>
                
                <li><a href="?rating_range=0-2{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_rating_range == '0-2' %}menu-active{% endif %}">
                    <div class="flex gap-0.5">
                        <span class="icon-[mdi--heart] text-rose-500"></span>
                    </div>
                </a></li>
            </ul>
        </div>

        <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-sm">
                排序
                {% if current_sort %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                <li><a href="?sort_by={% if current_sort == 'time' %}time_asc{% else %}time{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_sort == 'time' or current_sort == 'time_asc' %}menu-active{% endif %}">
                    按时间
                    {% if current_sort == 'time' %}
                    <span class="icon-[mdi--arrow-down-thin]"></span>
                    {% elif current_sort == 'time_asc' %}
                    <span class="icon-[mdi--arrow-up-thin]"></span>
                    {% endif %}
                </a></li>
                <li><a href="?sort_by={% if current_sort == 'rating' %}rating_asc{% else %}rating{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_sort == 'rating' or current_sort == 'rating_asc' %}menu-active{% endif %}">
                    按评分
                    {% if current_sort == 'rating' %}
                    <span class="icon-[mdi--arrow-down-thin]"></span>
                    {% elif current_sort == 'rating_asc' %}
                    <span class="icon-[mdi--arrow-up-thin]"></span>
                    {% endif %}
                </a></li>
                <li><a href="?sort_by={% if current_sort == 'roaster' %}roaster_desc{% else %}roaster{% endif %}{% if current_roast_level %}&roast_level={{ current_roast_level }}{% endif %}{% if current_process %}&process={{ current_process }}{% endif %}{% if current_rating_range %}&rating_range={{ current_rating_range }}{% endif %}{% if current_roaster %}&roaster={{ current_roaster }}{% endif %}{% if current_variety %}&variety={{ current_variety }}{% endif %}"
                    class="{% if current_sort == 'roaster' or current_sort == 'roaster_desc' %}menu-active{% endif %}">
                    按豆商
                    {% if current_sort == 'roaster' %}
                    <span class="icon-[mdi--arrow-down-thin]"></span>
                    {% elif current_sort == 'roaster_desc' %}
                    <span class="icon-[mdi--arrow-up-thin]"></span>
                    {% endif %}
                </a></li>
            </ul>
        </div>

        <div>
            {% if current_roast_level or current_process or current_rating_range or current_sort or current_roaster or current_variety %}
            <a href="{% url 'bean_list' %}" class="btn btn-sm btn-ghost text-success">
                <span class="icon-[bx--reset] text-lg"></span>
            </a>
            {% endif %}
        </div>
    </div>
    <div class="container mx-auto xl:max-w-7xl text-base-content px-2 mb-8">
        <div class="w-full px-4 md:px-20">
            <div class="flex flex-col">
                <div class="flex flex-col items-center">
                    <div class="grid justify-center lg:justify-start">
                        <div class="flex justify-between items-center w-full mb-4">
                            <div class="stats shadow bg-base-100">
                                <div class="stat place-items-center cursor-pointer" 
                                     @click="$store.brewlogList.filterByStatus('resting')"
                                     :class="{ 'bg-base-200': $store.brewlogList.currentStatus === 'resting' }">
                                    <div class="stat-title">养豆中</div>
                                    <div class="stat-value hover:opacity-60 transition-opacity">{{ active_stats.resting }}</div>
                                    <div class="stat-desc">款</div>
                                </div>
                                
                                <div class="stat place-items-center cursor-pointer"
                                     @click="$store.brewlogList.filterByStatus('out_of_stock')"
                                     :class="{ 'bg-base-200': $store.brewlogList.currentStatus === 'out_of_stock' }">
                                    <div class="stat-title">已用完</div>
                                    <div class="stat-value hover:opacity-60 transition-opacity">{{ active_stats.out_of_stock }}</div>
                                    <div class="stat-desc">款（未归档）</div>
                                </div>
                                
                                <div class="stat place-items-center cursor-pointer"
                                     @click="$store.brewlogList.filterByStatus('in_use')"
                                     :class="{ 'bg-base-200': $store.brewlogList.currentStatus === 'in_use' }">
                                    <div class="stat-title">使用中</div>
                                    <div class="stat-value hover:opacity-60 transition-opacity">{{ active_stats.in_use }}</div>
                                    <div class="stat-desc">款（未用完）</div>
                                    <div class="stat-figure text-secondary">
                                        <a href="{% url 'bean_calendar' %}" @click.stop><span class="icon-[mdi--calendar] text-xl"></span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% if active_groups %}
                    <!-- 未归档咖啡豆 -->
                    {% for group_key, group_data in active_groups.items %}
                        <div class="divider divider-secondary">
                            {% if group_type == 'month' %}
                                {{ group_key }}
                            {% elif group_type == 'rating' %}
                                {{ group_data.name }}
                            {% else %}
                                {{ group_key }}
                            {% endif %}
                        </div>
                        <div class="grid grid-cols-1 grid-rows-1 md:grid-cols-2 gap-4 mb-8">
                            {% for bean in group_data.beans|default:group_data %}
                                <div class="border-2 border-base-content/5 hover:shadow-lg active:shadow-lg rounded-lg" x-data="{ isFavorite: {{ bean.is_favorite|yesno:'true,false' }} }"
                                     data-bean-id="{{ bean.id }}"
                                     data-archived="false">
                                    <div class="p-4">
                                        <div class="card-actions justify-between flex items-center">
                                            <span>{{ bean.roaster }}</span>
                                            <div class="flex items-center gap-1 z-10">
                                                {% if bean.is_favorite %}
                                                <div class="tooltip tooltip-bottom flex items-center" data-tip="已设为首选">
                                                    <span class="icon-[mdi--star] text-yellow-500 text-lg"></span>
                                                </div>
                                                {% endif %}
                                                
                                                {% with rest_period_progress=bean.calculate_rest_period_progress %}
                                                {% if rest_period_progress.1 %}
                                                <div class="tooltip tooltip-bottom flex items-center" data-tip="正处于最佳赏味期">
                                                    <span class="icon-[icon-park-outline--icecream-three] text-info text-lg"></span>
                                                </div>
                                                {% endif %}
                                                {% endwith %}

                                                {% if bean.is_out_of_stock %}
                                                <div class="tooltip tooltip-bottom flex items-center" data-tip="已用完（未归档）">
                                                    <span class="icon-[oui--token-null] text-error text-lg"></span>
                                                </div>
                                                {% elif bean.is_in_use %}
                                                <div class="tooltip tooltip-bottom flex items-center" data-tip="使用中">
                                                    <span class="icon-[tabler--mug] text-success text-lg"></span>
                                                </div>
                                                {% endif %}

                                                {% if bean.is_resting %}
                                                <div class="tooltip tooltip-bottom flex items-center" data-tip="养豆中">
                                                    <span class="icon-[mingcute--sleep-line] text-base-content/60 text-lg"></span>
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div class="relative dropdown dropdown-end">
                                                <div tabindex="0" role="button" class="btn btn-ghost btn-circle text-2xl">
                                                    <span class="icon-[mi--options-vertical]"></span>
                                                </div>
                                                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-50 w-52 p-2 shadow-lg absolute">
                                                    <li><a href="{% url 'view_bean' bean.id %}"><span class="icon-[mdi--eye-outline]"></span> 查看详情</a></li>
                                                    {% if bean.is_favorite %}
                                                    <li><button @click="$store.brewlogList.toggleFavoriteBean({{ bean.id }})">
                                                    <span class="icon-[mdi--star-off]"></span> 取消首选
                                                    </button></li>
                                                    {% else %}
                                                    <li><button @click="$store.brewlogList.toggleFavoriteBean({{ bean.id }})">
                                                    <span class="icon-[mdi--star]"></span> 设为首选
                                                    </button></li>
                                                    {% endif %}
                                                    <li><a href="#" class="occurrence-btn" 
                                                          data-action="repurchase" 
                                                          data-bean-id="{{ bean.id }}"
                                                          data-rest-period-min="{{ bean.rest_period_min|default:'' }}"
                                                          data-rest-period-max="{{ bean.rest_period_max|default:'' }}"
                                                          data-purchase-price="{{ bean.purchase_price|default:'' }}"
                                                          data-bag-weight="{{ bean.bag_weight|default:'' }}"
                                                          data-bag-remain="{{ bean.bag_remain|default:'' }}"
                                                          data-roast-date="{{ bean.roast_date|date:'Y-m-d'|default:'' }}"><span class="icon-[mdi--plus-circle-multiple-outline]"></span> 回购</a></li>
                                                    <li><button @click="$store.brewlogList.archiveBean({{ bean.id }})"><span class="icon-[material-symbols--archive-outline-rounded]"></span> 归档</button></li>
                                                    <li><a href="{% url 'edit_bean_page' bean.id %}"><span class="icon-[material-symbols--edit-square-outline-rounded]"></span> 修改</a></li>
                                                    <li>
                                                        <button onclick="document.getElementById('delete_bean_modal_{{ bean.id }}').showModal()" class="text-error">
                                                            <span class="icon-[material-symbols--delete]"></span> 删除
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="flex gap-4 items-baseline">
                                            <h5 class="text-lg font-semibold">{{ bean.name }}</h5>
                                            {% if bean.avg_rating %}
                                            <div class="rating-display text-rose-500">
                                                {% with rating_group=bean.avg_rating|get_rating_group %}
                                                    {{ rating_group|get_rating_hearts }}
                                                {% endwith %}
                                            </div>
                                            {% else %}
                                            {% endif %}
                                        </div>
                                        <div class="flex gap-2 justify-between my-2">
                                            <div class="flex flex-wrap gap-2 items-center">
                                                {% if bean.type != "SKIP" %}
                                                    {% if bean.type == "SINGLE" %}
                                                    <div class="badge gap-2 text-sm text-info tooltip" data-tip="单一产地">
                                                        <span class="icon-[lucide--square-dot]"></span>
                                                    </div>
                                                    {% else %}
                                                    <div class="badge gap-2 text-sm text-info tooltip" data-tip="拼配豆">
                                                        <span class="icon-[lucide--square-asterisk]"></span>
                                                    </div>
                                                    {% endif %}
                                                {% endif %}
                                                {% if bean.is_decaf %}
                                                <div class="badge gap-2 text-sm text-info tooltip" data-tip="低因咖啡"><span class="icon-[material-symbols--flash-off-rounded] text-info"></span></div>
                                                {% endif %}
                                                {% if bean.notes %}
                                                <div class="badge gap-2 text-sm text-info tooltip" data-tip="{{ bean.notes }}"><span class="icon-[ph--note-duotone]"></span></div>
                                                {% endif %}
                                                {% if bean.purchase_price %}
                                                <div class="badge gap-2 text-sm text-info tooltip leading-[1.2]" data-tip="{{ bean.purchase_price }}">¥</div>
                                                {% endif %}
                                                {% if bean.type == "SINGLE" or bean.type == "BLEND" and bean.blend_components.exists %}
                                                    {% if bean.type == "SINGLE" %}
                                                    <div class="badge gap-2 text-sm">{{ bean.get_roast_level_display }}</div>
                                                    {% elif bean.type == "BLEND" %}
                                                        {% with bean.blend_components.all|dictsortreversed:"roast_level"|first as highest_roast %}
                                                        {% with bean.blend_components.all|dictsort:"roast_level"|first as lowest_roast %}
                                                            {% if highest_roast.roast_level == lowest_roast.roast_level %}
                                                            <div class="badge gap-2 text-sm">{{ highest_roast.get_roast_level_display }}</div>
                                                            {% endif %}
                                                        {% endwith %}
                                                        {% endwith %}
                                                    {% endif %}
                                                {% endif %}
                                                {% for tag in bean.flavor_tags.all %}
                                                <span class="badge badge-ghost">{{ tag.name|get_tag_value }}</span>
                                                {% endfor %}
                                            </div>
                                            <div class="flex justify-end mt-2">
                                                <div class="relative w-14 h-14">
                                                    <!-- 外层进度环 - 较粗 -->
                                                    <div class="absolute inset-0 rounded-full progress-ring-thick"
                                                            style="background: conic-gradient(rgb(34 139 34) {% if bean.bag_weight and bean.bag_remain %}{{ bean.bag_remain|divide:bean.bag_weight|multiply:100|floatformat:0 }}{% else %}0{% endif %}%, transparent 0);">
                                                    </div>
                                                    <!-- 内层进度环 - 较细 -->
                                                    {% if bean.roast_date %}
                                                    {% with rest_period_progress=bean.calculate_rest_period_progress %}
                                                    <div class="absolute inset-[3px] rounded-full progress-ring-thin {% if rest_period_progress.1 %}progress-ring-rest{% else %}text-warning{% endif %}"
                                                            style="background: conic-gradient(currentColor {{ rest_period_progress.0|floatformat:0 }}%, transparent 0);">
                                                    </div>
                                                    {% endwith %}
                                                    {% endif %}
                                                    <!-- 中心白色圆形和文字 -->
                                                    <div class="absolute inset-[6px] rounded-full bg-base-100 flex flex-col items-center justify-center">
                                                        <span class="text-xs leading-none tooltip tooltip-left" data-tip="外环：直观显示当前剩余库存比例">{% if bean.bag_weight and bean.bag_remain %}{{ bean.bag_remain|floatformat:0 }}g{% else %}-{% endif %}</span>
                                                        {% if bean.roast_date %}
                                                        {% with rest_period_progress=bean.calculate_rest_period_progress %}
                                                        <span class="text-[10px] leading-none mt-0.5 tooltip tooltip-left {% if rest_period_progress.1 %}text-info{% else %}text-warning{% endif %}" 
                                                              data-tip="内环：已养豆天数(即新鲜度)&#10;基于烘焙日期自动计算&#10;{% if bean.rest_period_min %}当前设置的养豆期为{% if bean.rest_period_max and bean.rest_period_max != bean.rest_period_min %}{{ bean.rest_period_min }}-{{ bean.rest_period_max }}{% else %}{{ bean.rest_period_min }}{% endif %}天{% else %}通常超过30天就不在最佳赏味期了{% endif %}">
                                                            {% with days_since=bean.roast_date|days_since %}
                                                            {% if days_since > 60 %}60+天{% else %}{{ days_since }}天{% endif %}
                                                            {% endwith %}
                                                        </span>
                                                        {% endwith %}
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                {% endif %}

                {% if archived_groups %}
                    <!-- 已归档咖啡豆 -->
                    <div class="bg-base-100 border-base-300 collapse collapse-plus border">
                        <input type="checkbox" class="peer" /> 
                        <div class="collapse-title text-lg font-medium bg-base-100 text-base-content peer-checked:bg-base-100 peer-checked:text-base-content">
                            已归档 ({{ archived_beans|length }}款)
                        </div>
                        <div class="collapse-content bg-base-100 text-base-content peer-checked:bg-base-100 peer-checked:text-base-content">
                            {% for group_key, group_data in archived_groups.items %}
                                <div class="divider">
                                    {% if group_type == 'month' %}
                                        {{ group_key }}
                                    {% elif group_type == 'rating' %}
                                        {{ group_data.name }}
                                    {% else %}
                                        {{ group_key }}
                                    {% endif %}
                                </div>
                                <div class="grid grid-cols-1 grid-rows-1 md:grid-cols-2 gap-4 mb-8">
                                    {% for bean in group_data.beans|default:group_data %}
                                        <div class="border-2 border-base-content/5 hover:shadow-lg active:shadow-lg rounded-lg" 
                                             x-data="{ isFavorite: {{ bean.is_favorite|yesno:'true,false' }} }"
                                             data-bean-id="{{ bean.id }}"
                                             data-archived="true">
                                            <div class="p-4">
                                                <div class="card-actions justify-between flex items-center">
                                                    <span>{{ bean.roaster }}</span>
                                                    <div class="relative dropdown dropdown-end">
                                                        <div tabindex="0" role="button" class="btn btn-ghost btn-circle text-2xl">
                                                            <span class="icon-[mi--options-vertical]"></span>
                                                        </div>
                                                        <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-50 w-52 p-2 shadow-lg absolute">
                                                            <li><a href="{% url 'view_bean' bean.id %}"><span class="icon-[mdi--eye-outline]"></span> 查看详情</a></li>
                                                            {% if bean.is_favorite %}
                                                            <li><button @click="$store.brewlogList.toggleFavoriteBean({{ bean.id }})">
                                                            <span class="icon-[mdi--star-off]"></span> 取消首选
                                                            </button></li>
                                                            {% else %}
                                                            <li><button @click="$store.brewlogList.toggleFavoriteBean({{ bean.id }})">
                                                            <span class="icon-[mdi--star]"></span> 设为首选
                                                            </button></li>
                                                            {% endif %}
                                                            <li><a href="#" class="occurrence-btn" 
                                                                data-action="repurchase" 
                                                                data-bean-id="{{ bean.id }}"
                                                                data-rest-period-min="{{ bean.rest_period_min|default:'' }}"
                                                                data-rest-period-max="{{ bean.rest_period_max|default:'' }}"
                                                                data-purchase-price="{{ bean.purchase_price|default:'' }}"
                                                                data-bag-weight="{{ bean.bag_weight|default:'' }}"
                                                                data-bag-remain="{{ bean.bag_remain|default:'' }}"
                                                                data-roast-date="{{ bean.roast_date|date:'Y-m-d'|default:'' }}"><span class="icon-[mdi--plus-circle-multiple-outline]"></span> 回购</a></li>
                                                            <li><button @click="$store.brewlogList.unarchiveBean({{ bean.id }})"><span class="icon-[material-symbols--unarchive-outline-rounded]"></span> 取消归档</button></li>
                                                            <li><a href="{% url 'edit_bean_page' bean.id %}"><span class="icon-[material-symbols--edit-square-outline-rounded]"></span> 修改</a></li>
                                                            <li>
                                                                <button onclick="document.getElementById('delete_bean_modal_{{ bean.id }}').showModal()" class="text-error">
                                                                    <span class="icon-[material-symbols--delete]"></span> 删除
                                                                </button>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="flex gap-4 items-baseline mb-2">
                                                    <h5 class="text-lg font-semibold">{{ bean.name }}</h5>
                                                    {% if bean.avg_rating %}
                                                    <div class="rating-display text-rose-500">
                                                        {% with rating_group=bean.avg_rating|get_rating_group %}
                                                            {{ rating_group|get_rating_hearts }}
                                                        {% endwith %}
                                                    </div>
                                                    {% else %}
                                                    {% endif %}
                                                </div>
                                                <div class="flex flex-wrap gap-2 items-center">
                                                    {% if bean.type != "SKIP" %}
                                                    <div class="badge gap-2 text-sm">
                                                        {% if bean.type == "SINGLE" %}
                                                        <span class="icon-[lucide--square-dot]"></span>
                                                        {% else %}
                                                        <span class="icon-[lucide--square-asterisk]"></span>
                                                        {% endif %}
                                                    </div>
                                                    {% endif %}
                                                    {% if bean.is_decaf %}
                                                    <div class="badge gap-2 text-sm"><span class="icon-[material-symbols--flash-off-rounded]"></span></div>
                                                    {% endif %}
                                                    {% if bean.notes %}
                                                    <div class="badge gap-2 text-sm"><span class="icon-[ph--note-duotone]"></span></div>
                                                    {% endif %}
                                                    {% if bean.purchase_price %}
                                                    <div class="badge gap-2 text-sm">¥</div>
                                                    {% endif %}
                                                    {% if bean.type == "SINGLE" or bean.type == "BLEND" and bean.blend_components.exists %}
                                                        {% if bean.type == "SINGLE" %}
                                                        <div class="badge gap-2 text-sm">{{ bean.get_roast_level_display }}</div>
                                                        {% elif bean.type == "BLEND" %}
                                                            {% with bean.blend_components.all|dictsortreversed:"roast_level"|first as highest_roast %}
                                                            {% with bean.blend_components.all|dictsort:"roast_level"|first as lowest_roast %}
                                                                {% if highest_roast.roast_level == lowest_roast.roast_level %}
                                                                <div class="badge gap-2 text-sm">{{ highest_roast.get_roast_level_display }}</div>
                                                                {% endif %}
                                                            {% endwith %}
                                                            {% endwith %}
                                                        {% endif %}
                                                    {% endif %}
                                                    {% for tag in bean.flavor_tags.all %}
                                                    <span class="badge badge-ghost">{{ tag.name|get_tag_value }}</span>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                {% if not active_groups and not archived_groups %}
                <div class="text-center py-12 text-base-content/60">
                    <p class="text-lg">暂无记录</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
<div x-data>
    {% if active_beans %}
        {% for bean in active_beans %}
        <dialog id="delete_bean_modal_{{ bean.id }}" class="modal text-base-content">
            <div class="modal-box">
                <h3 class="text-lg font-bold">确认删除</h3>
                <p class="py-4">确定要删除这个咖啡豆吗？此操作不可撤销。删除后，相关冲煮记录不会受影响。</p>
                <div class="modal-action">
                    <form method="dialog">
                        <button class="btn btn-error" @click="$store.brewlogList.deleteBean({{ bean.id }})">删除</button>
                        <button class="btn">取消</button>
                    </form>
                </div>
            </div>
            <form method="dialog" class="modal-backdrop">
                <button>close</button>
            </form>
        </dialog>
        {% endfor %}
    {% endif %}
    {% if archived_beans %}
        {% for bean in archived_beans %}
        <dialog id="delete_bean_modal_{{ bean.id }}" class="modal text-base-content">
            <div class="modal-box">
                <h3 class="text-lg font-bold">确认删除</h3>
                <p class="py-4">确定要删除这个咖啡豆记录吗？</p>
                <div class="modal-action">
                    <form method="dialog">
                        <button class="btn btn-error" @click="$store.brewlogList.deleteBean({{ bean.id }})">删除</button>
                        <button class="btn">取消</button>
                    </form>
                </div>
            </div>
            <form method="dialog" class="modal-backdrop">
                <button>close</button>
            </form>
        </dialog>
        {% endfor %}
    {% endif %}
</div>
<dialog id="occurrence-modal" class="modal text-base-content justify-center">
    <div class="modal-box" x-data="{ isRangePeriod: false }">
        <h3 class="font-bold text-lg">回购咖啡豆</h3>
        <span class="text-sm opacity-50">同款咖啡豆的再次补货，不会打断冲煮记录的用豆信息统计。仅更新包装信息，能追踪回购历史。</span>
        <form id="occurrence-form" method="POST" class="flex flex-col gap-4 mt-4">
            {% csrf_token %}
            <fieldset class="fieldset">
                <label class="input flex items-center w-full max-w-md">
                    回购时间
                    <input type="datetime-local" name="created_at" 
                           class="grow input input-ghost focus:outline-hidden" 
                           required>
                </label>
            </fieldset>
            <fieldset class="fieldset">
                <label class="input flex items-center w-full max-w-md">
                    购买价格(元)
                    <input type="number" name="purchase_price" 
                           class="grow input input-ghost focus:outline-hidden" 
                           step="0.01">
                    <span class="badge badge-ghost opacity-80">选填</span>
                </label>
            </fieldset>
            <fieldset class="fieldset">
                <label class="input flex items-center w-full max-w-md">
                    包装规格(g)
                    <input type="number" name="bag_weight" 
                           class="grow input input-ghost focus:outline-hidden" 
                           step="0.01" min="0">
                    <span class="badge badge-ghost opacity-80">选填</span>
                </label>
            </fieldset>
            <fieldset class="fieldset">
                <label class="input flex items-center w-full max-w-md">
                    库存余量(g)
                    <input type="number" name="bag_remain" 
                           class="grow input input-ghost focus:outline-hidden" 
                           step="0.01" min="0">
                    <span class="badge badge-ghost opacity-80">选填</span>
                </label>
            </fieldset>
            <fieldset class="fieldset">
                <label class="input flex items-center w-full max-w-md">
                    烘焙日期
                    <input type="date" name="roast_date" 
                           class="grow input input-ghost focus:outline-hidden">
                    <span class="badge badge-ghost opacity-80">选填</span>
                </label>
            </fieldset>
            <fieldset class="fieldset">
                <legend class="fieldset-legend">养豆期</legend>
                <label class="fieldset-label">
                    <div class="flex items-center gap-2">
                        <span class="text-sm">按单一值</span>
                        <input type="checkbox" class="toggle toggle-primary toggle-sm"
                               x-model="isRangePeriod" 
                               data-rest-period-type />
                        <span class="text-sm">按范围值</span>
                    </div>
                </label>
                <div class="flex flex-col gap-2">
                    <label class="input flex items-center w-full max-w-md">
                        <span class="rest-period-label" x-text="isRangePeriod ? '最短养豆期(天)' : '养豆天数'">养豆天数</span>
                        <input type="number" name="rest_period_min" 
                               class="grow input input-ghost focus:outline-hidden" 
                               min="1" max="60">
                        <span class="badge badge-ghost opacity-80">选填</span>
                    </label>
                    <div class="max-period-container input flex items-center w-full max-w-md" x-show="isRangePeriod" x-cloak>
                        <span>最长养豆期(天)</span>
                        <input type="number" name="rest_period_max" 
                               class="grow input input-ghost focus:outline-hidden" 
                               min="1" max="60">
                        <span class="badge badge-ghost opacity-80">选填</span>
                    </div>
                </div>
            </fieldset>
            <div class="modal-action">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn modal-close-btn">取消</button>
            </div>
        </form>
    </div>
</dialog>
{% endblock %} 