{% extends "my/brewlog_base.html" %}
{% load static %}
{% load calculation_filters format_filters time_filters calendar_filters %}

{% block title %}我的咖啡豆日历{% endblock %}

{% block content %}
<div x-data="{
    showPurchase: {% if show_purchase %}true{% else %}false{% endif %},
    showRoast: {% if show_roast %}true{% else %}false{% endif %},
    showRest: {% if show_rest %}true{% else %}false{% endif %},
    showLegend: true,
    beanVisibility: {
        {% for bean in beans %}
        '{{ bean.id }}': {% if bean.bag_remain and bean.bag_remain > 0 %}true{% else %}false{% endif %}{% if not forloop.last %},{% endif %}
        {% endfor %}
    },
    closeAllTooltips() {
        document.querySelectorAll('[x-ref^=\'tooltip_\']').forEach(el => {
            el.classList.add('hidden');
        });
    }
}">
    <!-- 导航栏 -->
    <div class="py-2 px-1 bg-base-200/90 mb-2 text-base-content sticky top-0 z-30 flex w-full justify-center backdrop-blur-sm transition-shadow duration-100 [transform:translate3d(0,0,0)]">
        <div class="navbar w-full max-w-screen-xl mx-auto">
            <div class="navbar-start lg:ml-4">
                <a href="{% url 'bean_list' %}" class="btn btn-ghost"><span class="icon-[ep--back]"></span></span>返回</a>
            </div>
            <div class="navbar-center">
                <!-- 月份导航 -->
                <form class="flex justify-center items-center gap-2" method="get">
                    <input 
                        type="month" 
                        name="date"
                        class="input"
                        value="{{ current_date|date:'Y-m' }}"
                        @input="$el.form.submit()"
                    >
                    <!-- 保持显示选项的状态 -->
                    <input type="hidden" name="show_purchase" :value="showPurchase ? '1' : '0'">
                    <input type="hidden" name="show_roast" :value="showRoast ? '1' : '0'">
                    <input type="hidden" name="show_rest" :value="showRest ? '1' : '0'">
                </form>
            </div>
            <div class="navbar-end lg:mr-4">
                {% if calendar_data|has_today %}
                <button class="btn btn-ghost" disabled>
                    <span class="icon-[mdi--pin]"></span>今天
                </button>
                {% else %}
                <a href="{% url 'bean_calendar' %}" class="btn btn-ghost">
                    <span class="icon-[mdi--pin-outline]"></span>今天
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="w-full px-4 py-20 md:px-20" @click="closeAllTooltips()">
        <!-- 标题及开关 -->
        <div class="mx-4 flex flex-col justify-between gap-6 lg:flex-row">
            <div class="flex flex-col gap-2">
                <h1 class="font-title text-3xl font-extrabold text-base-content lg:text-4xl">咖啡豆日历</h1>
                <p class="text-sm text-base-content/60">选定月份内，呈现所有在用咖啡豆的关键事件</p>
            </div>
            <div class="flex flex-col gap-3">
                <div class="text-end text-xs text-base-content/50 hidden lg:block">开关</div>
                <div class="flex items-center gap-2">
                    <label class="cursor-pointer flex items-center gap-2">
                        <input type="checkbox" class="toggle toggle-xs" x-model="showPurchase">
                        <span class="text-xs">购买日</span>
                    </label>
                    <label class="cursor-pointer flex items-center gap-2">
                        <input type="checkbox" class="toggle toggle-xs" x-model="showRoast">
                        <span class="text-xs">烘焙日</span>
                    </label>
                    <label class="cursor-pointer flex items-center gap-2">
                        <input type="checkbox" class="toggle toggle-xs" x-model="showRest">
                        <span class="text-xs">最佳赏味期</span>
                    </label>
                </div>
            </div>
        </div>
        <hr class="border-base-content/10 mx-4 mt-10 mb-16">
        <!-- 图例区域 -->
        <div class="my-4" x-show="Object.keys(beanVisibility).length > 0">
            <div class="bg-base-100 border-base-300 collapse collapse-plus border">
                <input type="checkbox" class="peer" /> 
                <div class="collapse-title text-lg font-medium bg-base-100 text-base-content peer-checked:bg-base-100 peer-checked:text-base-content">
                    图例
                </div>
                <div class="collapse-content bg-base-100 text-base-content peer-checked:bg-base-100 peer-checked:text-base-content">
                    <div class="overflow-x-auto">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>开关</th>
                                    <th>颜色</th>
                                    <th>咖啡豆</th>
                                    <th>库存</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for bean in beans %}
                                <tr>
                                    <td>
                                        <label class="swap swap-flip">
                                            <input type="checkbox" 
                                                   class="hidden"
                                                   x-model="beanVisibility['{{ bean.id }}']">
                                            <div class="swap-on text-base">🌝</div>
                                            <div class="swap-off text-base">🌚</div>
                                        </label>
                                    </td>
                                    <td>
                                        <div class="w-4 h-4 rounded-full" 
                                             style="background-color: {{ bean.color.solid }};"></div>
                                    </td>
                                    <td>{{ bean.roaster }} - {{ bean.name }}</td>
                                    <td>
                                        {% if bean.progress is not None %}
                                        <span class="text-sm">
                                            {{ bean.progress|floatformat:0 }}%
                                            {% if bean.bag_remain is not None and bean.bag_weight is not None %}
                                            （{{ bean.bag_remain|floatformat:1 }}/{{ bean.bag_weight|floatformat:0 }}g）
                                            {% endif %}
                                        </span>
                                        {% else %}
                                        <span class="text-sm text-base-content/50">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- 日历主体 -->
        <div class="bg-base-100 border border-base-300 shadow-lg">
            <div class="grid grid-cols-7">
                <!-- 星期标题 -->
                {% for day in "一二三四五六日"|make_list %}
                <div class="text-center p-2 font-medium border-b border-base-300 bg-base-200">周{{ day }}</div>
                {% endfor %}
                
                <!-- 日历格子 -->
                {% for week in calendar_data %}
                    {% for day in week %}
                    <div class="relative min-h-32 border border-base-200 p-2 {% if day.is_today %}border-2{% endif %} {% if not day.is_current_month %}bg-base-200/50{% endif %}">
                        <div class="absolute top-1 right-1 {% if day.is_today %}badge badge-primary{% else %}text-sm opacity-50{% endif %}">
                            {{ day.date|date:"j" }}
                        </div>
                        
                        <div class="mt-6">
                            <!-- 购买事件 -->
                            <div x-show="showPurchase">
                                {% for event in day.purchase_events %}
                                <div x-show="beanVisibility['{{ event.id }}']"
                                     class="relative h-6 leading-6 my-0.5 rounded px-2 text-xs cursor-pointer"
                                     style="background-color: {{ event.color.transparent }};"
                                     @click.stop="
                                         $dispatch('show-details', {
                                             date: '{{ day.date|date:"Y-m-d" }}',
                                             events: {
                                                 purchase: {{ day.purchase_events|safe }},
                                                 roast: {{ day.roast_events|safe }},
                                                 rest: {{ day.rest_events|safe }}
                                             }
                                         });
                                     ">
                                    <span class="md:hidden">💰</span>
                                    <span class="hidden md:inline">💰 {{ event.name }}</span>
                                </div>
                                {% endfor %}
                            </div>
                            
                            <!-- 烘焙事件 -->
                            <div x-show="showRoast">
                                {% for event in day.roast_events %}
                                <div x-show="beanVisibility['{{ event.id }}']"
                                     class="relative h-6 leading-6 my-0.5 rounded px-2 text-xs cursor-pointer"
                                     style="background-color: {{ event.color.transparent }};"
                                     @click.stop="
                                         $dispatch('show-details', {
                                             date: '{{ day.date|date:"Y-m-d" }}',
                                             events: {
                                                 purchase: {{ day.purchase_events|safe }},
                                                 roast: {{ day.roast_events|safe }},
                                                 rest: {{ day.rest_events|safe }}
                                             }
                                         });
                                     ">
                                    <span class="md:hidden">🔥</span>
                                    <span class="hidden md:inline">🔥 {{ event.name }}</span>
                                </div>
                                {% endfor %}
                            </div>
                            
                            <!-- 最佳赏味期 -->
                            <div x-show="showRest">
                                {% for event in day.rest_events %}
                                <div x-show="beanVisibility['{{ event.id }}']"
                                     class="relative h-6 leading-6 my-0.5 rounded px-2 text-xs cursor-pointer"
                                     style="background-color: {{ event.color.transparent }};"
                                     @click.stop="
                                         $dispatch('show-details', {
                                             date: '{{ day.date|date:"Y-m-d" }}',
                                             events: {
                                                 purchase: {{ day.purchase_events|safe }},
                                                 roast: {{ day.roast_events|safe }},
                                                 rest: {{ day.rest_events|safe }}
                                             }
                                         });
                                     ">
                                    <span class="md:hidden">☕</span>
                                    <span class="hidden md:inline">☕ {{ event.name }}赏味期</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<div x-data="{ 
         showModal: false, 
         selectedDate: '', 
         events: {purchase: [], roast: [], rest: []}
     }"
     @show-details.window="
         showModal = true; 
         selectedDate = $event.detail.date;
         events = $event.detail.events;
     "
     x-show="showModal"
     class="modal modal-bottom sm:modal-middle"
     :class="{ 'modal-open': showModal }">
    <div class="modal-box text-base-content">
        <h3 class="font-bold text-lg" x-text="selectedDate + ' 的事件'"></h3>
        <div class="py-4">
            <template x-if="events.purchase.length > 0">
                <div class="mb-2">
                    <template x-for="event in events.purchase" :key="event.id">
                        <div class="text-sm">💰 购入 <span x-text="event.roaster + ' - ' + event.name"></span></div>
                    </template>
                </div>
            </template>
            
            <template x-if="events.roast.length > 0">
                <div class="mb-2">
                    <template x-for="event in events.roast" :key="event.id">
                        <div class="text-sm">🔥 烘焙 <span x-text="event.roaster + ' - ' + event.name"></span></div>
                    </template>
                </div>
            </template>
            
            <template x-if="events.rest.length > 0">
                <div>
                    <template x-for="event in events.rest" :key="event.id">
                        <div class="text-sm">☕ <span x-text="event.roaster + ' - ' + event.name"></span> 最佳赏味期</div>
                    </template>
                </div>
            </template>
        </div>
        <div class="modal-action">
            <button class="btn" @click="showModal = false">关闭</button>
        </div>
    </div>
</div>
{% endblock %} 