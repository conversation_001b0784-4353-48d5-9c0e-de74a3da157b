{% extends "my/brewlog_base.html" %}
{% load static %}
{% load time_filters format_filters %}
{% block title %}{{ bean.roaster }} {{ bean.name }} - 我的咖啡豆{% endblock %}

{% block content %}
<div class="w-screen bg-base-200/90 mb-2 text-base-content sticky top-0 z-30 justify-center backdrop-blur-sm transition-shadow duration-100 [transform:translate3d(0,0,0)]">
    <div class="navbar w-full max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div class="navbar-start lg:ml-4">
            <a href="{% url 'bean_list' %}" class="btn btn-ghost">
                <span class="icon-[uiw--left] text-lg"></span>
                <span class="hidden md:block">返回</span>
            </a>
        </div>
        <div class="navbar-center" x-data="{ showBeanName: false }" @scroll.window="showBeanName = window.scrollY > 100">
            <span x-show="!showBeanName">咖啡豆详情</span>
            <span x-show="showBeanName" x-cloak class="truncate max-w-[200px] md:max-w-[300px]">
                {{ bean.name }}
            </span>
        </div>
        <div class="navbar-end lg:gap-2">
            <button id="shareBtn" class="btn btn-ghost">
                <span class="icon-[material-symbols--screenshot] text-lg"></span>
                <span class="hidden md:block">长截图</span>
            </button>
            {% if bean.is_deleted %}
            <a class="btn btn-disabled">
                <span class="icon-[material-symbols--edit-square-outline-rounded] text-lg"></span>
                <span class="hidden md:block">修改</span>
            </a>
            {% else %}
            <a href="{% url 'edit_bean_page' bean.id %}" class="btn btn-ghost">
                <span class="icon-[material-symbols--edit-square-outline-rounded] text-lg"></span>
                <span class="hidden md:block">修改</span>
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
    <div class="mx-auto max-w-2xl xl:max-w-3xl p-4 text-base-content rounded-lg mt-2">
        <div class="flex flex-col justify-center items-center mx-auto">
            <div class="relative font-serif w-full lg:w-2/3">
                <div class="relative border-2 border-primary rounded-lg">
                    <div class="flex flex-col text-center gap-4 relative">
                        <div class="absolute -left-3 top-7 -translate-y-1/3 flex flex-col gap-0.5">
                            {% if bean.is_favorite %}
                            <div class="w-1.5 h-6 bg-yellow-500 rounded-full tooltip tooltip-right" data-tip="已设为首选：新增冲煮记录时将被自动选取。"></div>
                            {% endif %}
                            
                            {% if bean.is_archived %}
                            <div class="w-1.5 h-6 bg-base-300 rounded-full tooltip tooltip-right" data-tip="已归档：不会在冲煮记录的咖啡豆选项中出现，不能被设为首选。"></div>
                            {% elif bean.is_deleted %}
                            <div class="w-1.5 h-6 bg-error rounded-full tooltip tooltip-right" data-tip="已删除：不能再次使用。"></div>
                            {% else %}
                                {% if bean.bag_remain is not None and bean.bag_remain <= 0 %}
                                <div class="w-1.5 h-6 bg-error rounded-full tooltip tooltip-right" data-tip="已用完：库存耗尽且未归档。"></div>
                                {% elif bean.roast_date and bean.rest_period_min and bean.roast_date|days_since < bean.rest_period_min %}
                                <!-- 养豆中状态单独处理 -->
                                {% else %}
                                <div class="w-1.5 h-6 bg-success rounded-full tooltip tooltip-right" data-tip="使用中：非删除、非归档、非养豆中的咖啡豆。"></div>
                                {% endif %}
                            {% endif %}
                            
                            {% if bean.roast_date %}
                            {% with rest_period_progress=bean.calculate_rest_period_progress %}
                            {% if rest_period_progress.1 %}
                            <div class="w-1.5 h-6 bg-info rounded-full tooltip tooltip-right" data-tip="最佳赏味期：养豆天数正好符合设定的养豆期。"></div>
                            {% endif %}
                            {% endwith %}
                            {% endif %}
                            
                            {% if bean.roast_date and bean.rest_period_min %}
                            {% with days_since_roast=bean.roast_date|days_since %}
                            {% if days_since_roast < bean.rest_period_min %}
                            <div class="w-1.5 h-6 bg-base-content/60 rounded-full tooltip tooltip-right" data-tip="养豆中：尚未达到设定的养豆期。"></div>
                            {% endif %}
                            {% endwith %}
                            {% endif %}
                        </div>
                        {% if bean.flavor_tags.exists %}
                        <div class="flex flex-col p-4">
                            <div class="text-right text-error">风味</div>
                            <div class="grid grid-cols-2 gap-y-2 mt-4">
                                {% for tag in bean.flavor_tags.all %}
                                    {% if forloop.counter|divisibleby:2 %}
                                    <div class="flex items-center justify-start">
                                    {% else %}
                                    <div class="flex items-center justify-end">
                                    {% endif %}
                                        <span>{{ tag.name|get_tag_value }}</span>
                                        {% if not forloop.last %}
                                            {% if forloop.counter|divisibleby:2 %}
                                                <!-- 每行最后一个不显示顿号 -->
                                            {% else %}
                                                <span class="mx-1 opacity-30">、</span>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="divider divider-primary"></div>
                        {% endif %}
                        <div class="p-4 flex flex-col gap-4">
                            {% if bean.is_decaf %}
                                <div class="indicator self-center tooltip" data-tip="低因咖啡">
                                    <span class="icon-[material-symbols--flash-off-rounded] text-info indicator-item"></span>
                                    <h2 class="text-xl font-black lg:text-4xl mx-4">{{ bean.name }}</h2>
                                </div>
                                {% else %}
                                <h2 class="text-xl font-black lg:text-4xl">{{ bean.name }}</h2>
                            {% endif %}
                            {% if bean.roaster %}<span class="opacity-70">{{ bean.roaster }}</span>{% endif %}
                            {% if bean.type == "SINGLE" %}
                            <div class="flex h-10 flex-col items-center justify-center self-center">
                                <div class="flex flex-row items-center justify-center gap-1 tooltip" data-tip="{{ bean.get_roast_level_display }}">
                                    {% with ''|center:7 as range %}
                                    {% for _ in range %}
                                        {% with forloop.counter as level %}
                                            {% if level == bean.roast_level %}
                                            <span class="icon-[bxs--coffee-bean] text-lg text-primary"></span>
                                            {% else %}
                                            <span class="icon-[bxs--coffee-bean] text-xs opacity-50"></span>
                                            {% endif %}
                                        {% endwith %}
                                    {% endfor %}
                                    {% endwith %}
                                </div>
                            </div>
                            {% elif bean.type == "BLEND" %}
                            <div class="flex flex-col items-center justify-center self-center gap-2">
                                {% if bean.blend_components.count > 1 %}
                                    {% with first_component=bean.blend_components.first %}
                                        {% comment %}检查是否所有组件都有相同的烘焙度{% endcomment %}
                                        {% with bean.blend_components.values_list as blend_components_list %}
                                            {% with bean.blend_components.all|dictsortreversed:"roast_level"|first as highest_roast %}
                                                {% with bean.blend_components.all|dictsort:"roast_level"|first as lowest_roast %}
                                                    {% if highest_roast.roast_level == lowest_roast.roast_level %}
                                                    <!-- 所有组件烘焙度相同，只显示一行 -->
                                                    <div class="flex flex-row items-center justify-center gap-1 tooltip" data-tip="{{ first_component.get_roast_level_display }}">
                                                        {% with ''|center:7 as range %}
                                                        {% for _ in range %}
                                                            {% with forloop.counter as level %}
                                                                {% if level == first_component.roast_level %}
                                                                <span class="icon-[bxs--coffee-bean] text-lg text-primary"></span>
                                                                {% else %}
                                                                <span class="icon-[bxs--coffee-bean] text-xs opacity-50"></span>
                                                                {% endif %}
                                                            {% endwith %}
                                                        {% endfor %}
                                                        {% endwith %}
                                                    </div>
                                                    {% else %}
                                                    <!-- 烘焙度不同，正常显示 -->
                                                    {% for component in bean.blend_components.all %}
                                                    <div class="flex flex-row items-center justify-center gap-1 tooltip" data-tip="#{{ forloop.counter }} {{ component.get_roast_level_display }}">
                                                        <span class="text-xs opacity-70">#{{ forloop.counter }}</span>
                                                        {% with ''|center:7 as range %}
                                                        {% for _ in range %}
                                                            {% with forloop.counter as level %}
                                                                {% if level == component.roast_level %}
                                                                <span class="icon-[bxs--coffee-bean] text-lg text-primary"></span>
                                                                {% else %}
                                                                <span class="icon-[bxs--coffee-bean] text-xs opacity-50"></span>
                                                                {% endif %}
                                                            {% endwith %}
                                                        {% endfor %}
                                                        {% endwith %}
                                                    </div>
                                                    {% endfor %}
                                                    {% endif %}
                                                {% endwith %}
                                            {% endwith %}
                                        {% endwith %}
                                    {% endwith %}
                                {% else %}
                                <!-- 只有一个组件，直接显示 -->
                                {% for component in bean.blend_components.all %}
                                <div class="flex flex-row items-center justify-center gap-1 tooltip" data-tip="{{ component.get_roast_level_display }}">
                                    {% with ''|center:7 as range %}
                                    {% for _ in range %}
                                        {% with forloop.counter as level %}
                                            {% if level == component.roast_level %}
                                            <span class="icon-[bxs--coffee-bean] text-lg text-primary"></span>
                                            {% else %}
                                            <span class="icon-[bxs--coffee-bean] text-xs opacity-50"></span>
                                            {% endif %}
                                        {% endwith %}
                                    {% endfor %}
                                    {% endwith %}
                                </div>
                                {% endfor %}
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% if bean.type != "SKIP" %}
                    <div class="divider divider-primary"></div>
                    <div class="flex flex-wrap justify-center h-fit mb-6 mx-4 gap-10">
                        <div class="flex h-fit w-fit flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">类型</div>
                            <div class="flex flex-row items-center justify-center">
                                <span class="font-bold opacity-80">{% if bean.type == "SINGLE" %}单品{% else %}拼配{% endif %}</span>
                            </div>
                        </div>
                        {% if bean.origin or bean.type == "BLEND" and has_component_origin %}
                        <div class="flex h-fit w-fit flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">产地</div>
                            <div class="flex flex-col items-center justify-center">
                                {% if bean.type == "BLEND" %}
                                    {% for component in bean.blend_components.all %}
                                        {% if component.origin %}
                                        <span class="font-bold opacity-80">{% if bean.blend_components.count > 1 %}#{{ forloop.counter }} {{ component.origin }}({{ component.blend_ratio|format_number }}%){% else %}{{ component.origin }}{% endif %}</span>
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <span class="font-bold opacity-80">{{ bean.origin }}</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                        {% if bean.region or bean.type == "BLEND" and has_component_region %}
                        <div class="flex h-fit w-fit flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">产区</div>
                            <div class="flex flex-col items-center justify-center">
                                {% if bean.type == "BLEND" %}
                                    {% for component in bean.blend_components.all %}
                                        {% if component.region %}
                                        <span class="font-bold opacity-80">{% if bean.blend_components.count > 1 %}#{{ forloop.counter }} {% endif %}{{ component.region }}</span>
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <span class="font-bold opacity-80">{{ bean.region }}</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                        {% if bean.finca or bean.type == "BLEND" and has_component_finca %}
                        <div class="flex h-fit w-fit flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">庄园</div>
                            <div class="flex flex-col items-center justify-center">
                                {% if bean.type == "BLEND" %}
                                    {% for component in bean.blend_components.all %}
                                        {% if component.finca %}
                                        <span class="font-bold opacity-80">{% if bean.blend_components.count > 1 %}#{{ forloop.counter }} {% endif %}{{ component.finca }}</span>
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <span class="font-bold opacity-80">{{ bean.finca }}</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                        {% if bean.variety or bean.type == "BLEND" and has_component_variety %}
                        <div class="flex h-fit w-fit flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">品种</div>
                            <div class="flex flex-col items-center justify-center">
                                {% if bean.type == "BLEND" %}
                                    {% for component in bean.blend_components.all %}
                                        {% if component.variety %}
                                        <span class="font-bold opacity-80">{% if bean.blend_components.count > 1 %}#{{ forloop.counter }} {% endif %}{{ component.variety }}</span>
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <span class="font-bold opacity-80">{{ bean.variety }}</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                        {% if bean.process or bean.type == "BLEND" and has_component_process %}
                        <div class="flex h-fit w-fit flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">处理法</div>
                            <div class="flex flex-col items-center justify-center">
                                {% if bean.type == "BLEND" %}
                                    {% for component in bean.blend_components.all %}
                                        {% if component.process %}
                                        <span class="font-bold opacity-80">{% if bean.blend_components.count > 1 %}#{{ forloop.counter }} {% endif %}{{ component.process }}</span>
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <span class="font-bold opacity-80">{{ bean.process }}</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                        {% if bean.altitude_single or bean.altitude_min or bean.altitude_max or bean.type == "BLEND" and has_component_altitude %}
                        <div class="flex h-fit w-fit flex-col items-center justify-center">
                            <div class="mt-2 text-sm opacity-40">海拔</div>
                            <div class="flex flex-col items-center justify-center">
                                {% if bean.type == "BLEND" %}
                                    {% for component in bean.blend_components.all %}
                                        {% if component.altitude_type %}
                                            {% if component.altitude_type == "SINGLE" and component.altitude_single or component.altitude_type == "RANGE" and component.altitude_min and component.altitude_max %}
                                            <span class="font-bold opacity-80">
                                                {% if bean.blend_components.count > 1 %}#{{ forloop.counter }} {% endif %}
                                                {% if component.altitude_type == "SINGLE" %}
                                                    {% if component.altitude_single %}{{ component.altitude_single }}米{% endif %}
                                                {% else %}
                                                    {% if component.altitude_min and component.altitude_max %}
                                                        {{ component.altitude_min }}-{{ component.altitude_max }}米
                                                    {% endif %}
                                                {% endif %}
                                            </span>
                                            {% endif %}
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <span class="font-bold opacity-80">
                                        {% if bean.altitude_type == "SINGLE" %}
                                            {% if bean.altitude_single %}{{ bean.altitude_single }}米{% endif %}
                                        {% else %}
                                            {% if bean.altitude_min and bean.altitude_max %}
                                                {{ bean.altitude_min }}-{{ bean.altitude_max }}米
                                            {% endif %}
                                        {% endif %}
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="mx-auto xl:max-w-3xl p-4 text-base-content">
            <div class="divider menu-title max-w-md mx-auto">附加信息</div>
            <div class="flex flex-col justify-center items-center">
                <div class="w-full lg:w-2/3">
                    <div class="max-w-2xl overflow-hidden">
                        <dl>
                            {% if bean.roast_date %}
                            <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium opacity-40">
                                    烘焙日期
                                </dt>
                                <dd class="mt-1 text-sm opacity-80 sm:mt-0 sm:col-span-2">
                                    {{ bean.roast_date|date:"Y年n月j日" }}
                                    {% if bean.rest_period_min %}
                                    （养豆期: {% if bean.rest_period_max and bean.rest_period_max != bean.rest_period_min %}{{ bean.rest_period_min }}-{{ bean.rest_period_max }}{% else %}{{ bean.rest_period_min }}{% endif %}天）
                                    {% endif %}
                                </dd>
                            </div>
                            {% endif %}
                            <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium opacity-40">
                                    购买日期
                                </dt>
                                <dd class="mt-1 text-sm opacity-80 sm:mt-0 sm:col-span-2">
                                    {{ bean.created_at|date:"Y年n月j日" }}
                                </dd>
                            </div>
                            {% if bean.purchase_price %}
                            <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium opacity-40">
                                    价格
                                </dt>
                                <dd class="mt-1 text-sm opacity-80 sm:mt-0 sm:col-span-2">
                                    {{ bean.purchase_price|format_number }}元{% if bean.price_per_gram %}（{{ bean.price_per_gram|format_number }}元/克）{% endif %}
                                </dd>
                            </div>
                            {% endif %}
                            {% if bean.bag_weight %}
                            <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium opacity-40">
                                    包装规格
                                </dt>
                                <dd class="mt-1 text-sm opacity-80 sm:mt-0 sm:col-span-2">
                                    {{ bean.bag_weight|format_number }}g
                                </dd>
                            </div>
                            {% endif %}
                            {% if bean.bag_remain %}
                            <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium opacity-40">
                                    库存余量
                                </dt>
                                <dd class="mt-1 text-sm opacity-80 sm:mt-0 sm:col-span-2">
                                    {{ bean.bag_remain|format_number }}g
                                    {% if remaining_uses %}
                                    （大约还可以用{{ remaining_uses }}次）
                                    {% endif %}
                                </dd>
                            </div>
                            {% endif %}
                            {% if bean.occurrences.count %}
                            <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium opacity-40">
                                    回购次数
                                </dt>
                                <dd class="mt-1 text-sm opacity-80 sm:mt-0 sm:col-span-2">
                                    {{ bean.occurrences_count }}次{% if avg_repurchase_interval %}（平均间隔{{ avg_repurchase_interval|precise_timedelta }}）{% endif %}
                                </dd>
                            </div>
                            {% endif %}
                        </dl>
                    </div>
                    {% if bean.occurrences.count %}
                    <div class="bg-base-100 p-4 rounded-xl" id="repurchase-history">
                        <details class="relative" x-data="{ open: false }">
                            <summary class="cursor-pointer flex items-center gap-2" @click="open = !open">
                                <span class="icon-[mdi--format-list-bulleted]"></span>
                                <span>查看近期回购记录</span>
                                <span class="ml-auto">
                                    <svg class="w-4 h-4 transform transition-transform duration-300" 
                                         :class="{ 'rotate-180': open }"
                                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </span>
                            </summary>
                            <div class="overflow-x-auto mt-2">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th class="whitespace-nowrap">日期</th>
                                            <th>价格(元)</th>
                                            <th>规格(g)</th>
                                            <th>间隔(距上次回购)</th>
                                            <th>管理</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    {% for occurrence in occurrences|slice:":10" %}
                                        <tr>
                                            <td class="whitespace-nowrap">{{ occurrence.created_at|date:"Y-m-d" }}</td>
                                            <td>{{ occurrence.purchase_price|format_number }}</td>
                                            <td>{{ occurrence.bag_weight|format_number }}</td>
                                            <td>{% if not forloop.last %}{{ occurrence.time_gap|precise_timedelta }}{% else %}{% if bean.initial_created_at %}{{ occurrence.created_at|subtract_datetime:bean.initial_created_at|precise_timedelta }}{% endif %}{% endif %}</td>
                                            <td>
                                                <div class="flex flex-row gap-2">
                                                    <button class="btn btn-xs btn-outline occurrence-btn" data-action="edit" 
                                                            data-bean-id="{{ occurrence.id }}">修改</button>
                                                    <button class="btn btn-xs btn-error occurrence-btn" data-action="delete"
                                                            data-bean-id="{{ occurrence.id }}">删除</button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    {% if bean.initial_purchase_price or bean.initial_bag_weight or bean.initial_roast_date %}
                                        <tr>
                                            <td class="whitespace-nowrap">{% if bean.initial_created_at %}{{ bean.initial_created_at|date:"Y-m-d" }}{% else %}{{ bean.created_at|date:"Y-m-d" }}{% endif %}</td>
                                            <td>{{ bean.initial_purchase_price|format_number }}</td>
                                            <td>{{ bean.initial_bag_weight|format_number }}</td>
                                            <td>初次购买</td>
                                            <td></td>
                                        </tr>
                                    {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </details>
                    </div>
                    {% endif %}
                    {% if bean.notes %}
                    <div role="alert" class="alert mt-4 text-start">
                        <span class="icon-[ph--note-duotone]"></span>
                        <div class="break-all">{{ bean.notes|linebreaks }}</div>
                    </div>
                    {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <div class="mx-auto xl:max-w-3xl p-4 mb-4 text-base-content">
            <div class="divider menu-title max-w-md mx-auto">提要</div>
            <div class="text-center">
                <div class="stats stats-vertical shadow bg-base-100 lg:stats-horizontal mt-4">
                    {% if bean.roast_date and bean.roast_date|days_since <= 60 %}
                    <div class="stat">
                        <div class="stat-title">已养豆</div>
                        <div class="stat-value">
                            {{ bean.roast_date|days_since }}
                        </div>
                        <div class="stat-desc">天</div>
                    </div>
                    {% endif %}
                    <div class="stat">
                        <div class="stat-title">使用次数</div>
                        <div class="stat-value">{{ bean.usage_count }}</div>
                        <div class="stat-desc">次</div>
                    </div>
                    <div class="stat">
                        <div class="stat-title">最近使用</div>
                        {% if bean.usage_count > 0 %}
                            <div class="stat-value">{{ bean.days_since_last_use|days_ago_value }}</div>
                            <div class="stat-desc">{{ bean.days_since_last_use|days_ago_unit }}</div>
                        {% else %}
                            <div class="stat-value">-</div>
                            <div class="stat-desc">尚未使用</div>
                        {% endif %}
                    </div>
                    {% if bean.avg_rating %}
                    <div class="stat">
                        <div class="stat-title">平均得分<button class="text-info text-base" onclick="avg_rating_info_modal.showModal()"><span class="icon-[material-symbols--info-outline] cursor-pointer"></span></button></div>
                        <div class="stat-value">{{ bean.avg_rating|format_number }}</div>
                        <div class="stat-desc">分</div>
                    </div>
                    <dialog id="avg_rating_info_modal" class="modal border-none">
                        <div class="modal-box">
                            <form method="dialog">
                                <button class="absolute right-2 top-2"><span class="icon-[heroicons--x-mark-20-solid] w-5 h-5 cursor-pointer"></span></button>
                            </form>
                            <h3 class="text-lg font-bold">关于平均得分</h3>
                            <p class="py-4">根据使用该咖啡豆的冲煮记录计算得出的平均评分。</p>
                        </div>
                    </dialog>
                    <div class="stat">
                        <div class="stat-title">最常用于</div>
                        <div class="stat-value">{{ most_used_equipment.brewing_equipment__brew_method|get_brew_method_display }}</div>
                        <div class="stat-desc">冲煮方法</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% if dimensions_avg or unique_flavor_tags %}
<div class="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 pb-4">
    <div class="divider menu-title max-w-md mx-auto my-8">我的品鉴笔记</div>
    
    <div class="flex flex-col items-center gap-4">
        {% if unique_flavor_tags %}
        <div class="flex flex-col gap-2">
            <div class="flex flex-wrap gap-2">
                {% for tag in unique_flavor_tags %}
                <div class="badge badge-lg badge-outline {% if tag in bean.flavor_tags.all %}badge-error{% else %}badge-secondary{% endif %}">
                    {{ tag.name }}
                </div>
                {% endfor %}
                {% with overlap=flavor_accuracy %}
                    {% if overlap is not None %}
                    <div class="badge badge-lg badge-outline gap-2 tooltip" data-tip="与咖啡豆风味标签的重叠度">
                        <div class="inline-flex items-center gap-2">
                            <span class="icon-[ph--intersect-duotone]"></span>
                            <span class="leading-none">{{ overlap }}%</span>
                        </div>
                    </div>
                    {% endif %}
                {% endwith %}
            </div>
        </div>
        {% endif %}
        
        {% if dimensions_avg %}
        <div class="w-auto h-64">
            <canvas id="dimensionChart" 
                data-values='[
                    {{ dimensions_avg.avg_aroma|default:0|floatformat:1 }}, 
                    {{ dimensions_avg.avg_acidity|default:0|floatformat:1 }}, 
                    {{ dimensions_avg.avg_sweetness|default:0|floatformat:1 }}, 
                    {{ dimensions_avg.avg_aftertaste|default:0|floatformat:1 }}, 
                    {{ dimensions_avg.avg_body|default:0|floatformat:1 }}
                ]'>
            </canvas>
        </div>
        {% endif %}
        {% if tasting_count > 1 %}
        <div class="alert bg-base-100 text-sm opacity-50 my-8"><span class="icon-[material-symbols--info-outline]"></span> <div>基于 {{ tasting_count }} 条品鉴记录汇总后计算平均值。</div></div>
        {% endif %}
    </div>
</div>
{% endif %}

<dialog id="occurrence-modal" class="modal text-base-content">
    <div class="modal-box" x-data="{ isRangePeriod: false }">
        <h3 class="font-bold text-lg">修改回购记录</h3>
        <form id="occurrence-form" class="space-y-4" x-data="{ occurrenceId: '' }" x-init="$watch('occurrenceId', value => { if(value) { $el.setAttribute('hx-post', `/my/occurrence/${value}/edit/`); } })" hx-target="#repurchase-history">
            {% csrf_token %}
            <div class="form-control">
                <label class="input flex items-center w-full max-w-md">
                回购时间
                <input type="datetime-local" name="created_at" class="grow input focus:outline-hidden input-sm w-1/2" required>
                </label>
            </div>
            <div class="form-control">
                <label class="input flex items-center w-full max-w-md">
                购买价格
                <input type="number" name="purchase_price" class="grow input focus:outline-hidden input-sm w-1/2" step="0.01">
                <span class="badge badge-ghost opacity-80">选填</span>
                </label>
            </div>
            <div class="form-control">
                <label class="input flex items-center w-full max-w-md">
                包装规格(g)
                <input type="number" name="bag_weight" class="grow input focus:outline-hidden input-sm w-1/2" step="0.01"
                    min="0">
                <span class="badge badge-ghost opacity-80">选填</span>
                </label>
            </div>
            <div class="form-control">
                <label class="input flex items-center w-full max-w-md">
                库存余量(g)
                <input type="number" name="bag_remain" class="grow input focus:outline-hidden input-sm w-1/2" step="0.01"
                    min="0">
                <span class="badge badge-ghost opacity-80">选填</span>
                </label>
            </div>
            <div class="form-control">
                <label class="input flex items-center w-full max-w-md">
                烘焙日期
                <input type="date" name="roast_date" class="grow input focus:outline-hidden input-sm w-1/2">
                <span class="badge badge-ghost opacity-80">选填</span>
                </label>
            </div>
            <fieldset class="fieldset">
              <legend class="fieldset-legend">养豆期</legend>
              <label class="fieldset-label">
                <div class="flex items-center gap-2">
                  <span class="text-sm">按单一值</span>
                  <input type="checkbox" class="toggle toggle-primary toggle-sm" 
                         x-model="isRangePeriod"
                         data-rest-period-type />
                  <span class="text-sm">按范围值</span>
                </div>
              </label>
              <div class="flex flex-col gap-2">
                <label class="input flex items-center w-full max-w-md">
                  <span class="rest-period-label" x-text="isRangePeriod ? '最短养豆期(天)' : '养豆天数'">养豆天数</span>
                  <input type="number" name="rest_period_min" class="grow input input-ghost focus:outline-hidden" min="1" max="60">
                  <span class="badge badge-ghost opacity-80">选填</span>
                </label>
                <div class="max-period-container input flex items-center w-full max-w-md" x-show="isRangePeriod" x-cloak>
                  <span>最长养豆期(天)</span>
                  <input type="number" name="rest_period_max" class="grow input input-ghost focus:outline-hidden" min="1" max="60">
                  <span class="badge badge-ghost opacity-80">选填</span>
                </div>
              </div>
            </fieldset>
            <div class="modal-action">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn modal-close-btn">取消</button>
            </div>
        </form>
    </div>
</dialog>

<dialog id="delete-occurrence-modal" class="modal text-base-content">
    <div class="modal-box">
        <h3 class="font-bold text-lg">删除回购记录</h3>
        <p class="py-4">确定要删除这条回购记录吗？此操作不可撤销。</p>
        <div class="modal-action">
            <button id="confirm-delete-btn" class="btn btn-error">删除</button>
            <button class="btn modal-close-btn">取消</button>
        </div>
    </div>
</dialog>

<div class="hidden">
    <span class="text-base-content dark:text-base-100 bg-light-highlight dark:bg-dark-highlight">g</span>
</div>
{% endblock %}

{% block extra_js %}
<script type="text/javascript" src="{% static 'js/blbnccrrnc.js' %}"></script>
<script defer type="text/javascript" src="{% static 'js/blvwrcrd.js' %}"></script>
<script defer type="text/javascript" src="{% static 'js/blvwsht.js' %}"></script>
{% endblock %} 