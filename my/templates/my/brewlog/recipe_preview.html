{% load static %}
{% load calculation_filters time_filters format_filters %}

<div class="bg-base-200/90 mb-2 text-base-content sticky top-0 z-30 justify-center backdrop-blur-sm transition-shadow duration-100">
    <div class="navbar px-4">
        <div class="navbar-start lg:ml-4">
            <button @click="document.getElementById('preview_quick_brew_modal').close()" class="btn btn-ghost">
                <span class="icon-[uiw--left] text-lg"></span>
                <span class="hidden md:block">返回</span>
            </button>
        </div>
        <div class="navbar-center">
            <span>速记预览</span>
        </div>
        <div class="navbar-end">
            <button class="btn btn-primary" @click="$store.recipeList.quickBrew({{ recipe.id }})">
                <span class="icon-[mdi--lightning-bolt-outline]"></span>
                <span>确认速记</span>
            </button>
        </div>
    </div>
</div>

<div class="px-4">
    <div class="p-4 text-base-content rounded-lg mt-2">
        <div class="flex flex-col">
            <h1 class="text-2xl font-bold mb-4">
                {% if preview.recipe_name %}
                    {{ preview.recipe_name }}
                {% else %}
                    <span class="opacity-80">无名配方</span>
                {% endif %}
            </h1>

            <div class="text-sm mb-4 flex items-center gap-2 flex-wrap">
                <span>
                    {% if user.first_name %}{{ user.first_name }}{% else %}{{ user }}{% endif %}
                    <span class="opacity-50">记于 {{ now|cn_datetime }}</span>
                </span>
            </div>
        </div>
        <div class="flex flex-wrap gap-2 items-center">
            <div class="badge badge-ghost badge-lg gap-2">
                {% with equip=preview.brewing_equipment.get_brew_method_display %}
                {% if equip == '意式' %}<span class="icon-[tabler--coffee]"></span>
                {% elif equip == '手冲' %}<span class="icon-[mdi--kettle-pour-over]"></span>
                {% elif equip == '爱乐压' %}<span class="icon-[mdi--alpha-a]"></span>
                {% elif equip == '冷萃' %}<span class="icon-[mdi--snowflake]"></span>
                {% elif equip == '摩卡壶' %}<span class="icon-[game-icons--moka-pot]"></span>
                {% elif equip == '法压壶' %}<span class="icon-[openmoji--french-press]"></span>
                {% elif equip == '自动滴滤' %}<span class="icon-[mdi--coffee-maker-outline]"></span>
                {% endif %}
                {% endwith %}
                {{ preview.brewing_equipment.get_brew_method_display }}
            </div>
            <div class="badge badge-ghost badge-lg gap-2"><span class="icon-[iconamoon--clock-light]"></span> {{ preview.brewing_time|format_brewing_time }}</div>
            <div class="badge badge-ghost badge-lg gap-2"><span class="icon-[iconoir--percentage-circle]"></span> 1:{{ preview.yield_weight|divide:preview.dose_weight|format_ratio }}</div>
        </div>
        <div class="grid grid-cols-3 gap-4 mt-4">
            <div class="card bg-base-100 w-auto">
                <figure class="text-5xl px-4 pt-4">
                    <span class="icon-[openmoji--coffee-grinder] mr-2"></span>
                </figure>
                <div class="card-body items-center text-center p-2">
                    <h2 class="card-title">{{ preview.grind_size }}</h2>
                </div>
            </div>
            <div class="card bg-base-100 w-auto indicator">
                <figure class="text-5xl px-4 pt-4">
                    <span class="icon-[openmoji--roasted-coffee-bean]"></span>
                </figure>
                <div class="card-body items-center text-center p-2">
                    <h2 class="card-title">{{ preview.dose_weight|format_number }}g</h2>
                </div>
            </div>
            <div class="card bg-base-100 w-auto">
                <figure class="text-5xl px-4 pt-4">
                    <span class="icon-[openmoji--droplet]"></span>
                </figure>
                <div class="card-body items-center text-center p-2">
                    <h2 class="card-title">{{ preview.yield_weight|format_number }}g</h2>
                    {% if preview.water_temperature %}
                    <p>{{ preview.water_temperature|format_number }}°C</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="flex flex-col gap-2 py-4 text-sm">
            <div class="flex gap-3">
                <p class="opacity-60 w-12">用豆</p>
                <p class="flex-1">{{ preview.coffee_bean.roaster }} {{ preview.coffee_bean.name }}{% if preview.coffee_bean.type == '跳过' %}{% else %}，{% with level=preview.coffee_bean.roast_level %}{% if level == 1 %}极浅烘{% elif level == 2 %}浅烘{% elif level == 3 %}浅中烘{% elif level == 4 %}中烘{% elif level == 5 %}中深烘{% elif level == 6 %}深烘{% elif level == 7 %}极深烘{% endif %}{% endwith %}{{ preview.coffee_bean.process }}{{ preview.coffee_bean.variety }}
                {% if preview.coffee_bean.origin or preview.coffee_bean.region or preview.coffee_bean.finca %}（{{ preview.coffee_bean.origin }}{{ preview.coffee_bean.region }}{{ preview.coffee_bean.finca }}）{% endif %}
                {% endif %}</p>
            </div>
            {% if preview.water_quality %}
            <div class="flex gap-3">
                <p class="opacity-60 w-12">用水</p>
                <p class="flex-1">{{ preview.water_quality }}</p>
            </div>
            {% endif %}
            <div class="flex gap-3">
                <p class="opacity-60 w-12">器具</p>
                <p class="flex-1">
                    {% with brewing_brand=preview.brewing_equipment.brand %}
                        {% if brewing_brand in preview.brewing_equipment.name %}
                            {{ preview.brewing_equipment.name }}
                        {% else %}
                            {{ brewing_brand }} {{ preview.brewing_equipment.name }}
                        {% endif %}
                    {% endwith %}
                    + 
                    {% with grinding_brand=preview.grinding_equipment.brand %}
                        {% if grinding_brand in preview.grinding_equipment.name %}
                            {{ preview.grinding_equipment.name }}
                        {% else %}
                            {{ grinding_brand }} {{ preview.grinding_equipment.name }}
                        {% endif %}
                    {% endwith %}
                </p>
            </div>
            {% if preview.gadget_kit %}
            <div class="flex gap-3">
                <p class="opacity-60 w-12">小物</p>
                <div class="flex-1">
                    <div class="flex flex-col md:flex-row md:flex-wrap gap-y-1 md:gap-x-1">
                        <div class="flex items-center">
                            {% with gadget_brand=preview.gadget_kit.brand %}
                                {% if gadget_brand in preview.gadget_kit.name %}
                                    {{ preview.gadget_kit }}
                                {% else %}
                                    {{ gadget_brand }} {{ preview.gadget_kit }}
                                {% endif %}
                            {% endwith %}
                            <span class="badge badge-sm badge-ghost ml-1">组合</span>
                        </div>
                    </div>
                </div>
            </div>
            {% elif preview.gadgets %}
            <div class="flex gap-3">
                <p class="opacity-60 w-12">小物</p>
                <div class="flex-1">
                    <div class="flex flex-col md:flex-row md:flex-wrap gap-y-1 md:gap-x-1">
                        {% for gadget in preview.gadgets %}
                            <div class="flex items-center">
                                {% with gadget_brand=gadget.brand %}
                                    {% if gadget_brand in gadget.name %}
                                        {{ gadget }}
                                    {% else %}
                                        {{ gadget_brand }} {{ gadget }}
                                    {% endif %}
                                {% endwith %}
                                {% if not forloop.last %}<span class="hidden md:inline">,&nbsp;</span>{% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        {% if preview.steps %}
        <div class="divider menu-title max-w-md mx-auto mt-8">详细步骤</div>
        <div class="mt-4">
            <ul class="list">
                {% for step in preview.steps %}
                    <li class="list-row items-start">
                        <div class="text-4xl font-thin opacity-30 tabular-nums text-center">{{ forloop.counter }}</div>
                        
                        <div class="list-col-grow space-y-2">
                            <div class="whitespace-normal break-words">{{ step.text|highlight_measurements|safe|linebreaks }}</div>
                            {% if step.timer %}
                            <span class="opacity-50">⏱ {{ step.timer }}</span>
                            {% endif %}
                        </div>
                    </li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}
    </div>
</div>

<div class="alert bg-base-100 text-sm shadow-lg my-8 mx-auto max-w-[calc(100%-2rem)]">
    <span class="icon-[material-symbols--info-outline]"></span>
    <div>
        <p>这是速记配方的预览，将基于最近10次使用该配方的冲煮记录，选取使用频率最高的参数。</p>
        <p class="text-sm opacity-75">如果有小工具组合，会优先使用组合；否则使用单个小工具。</p>
        <p class="text-sm opacity-75">环境数据和品鉴笔记不纳入速记，如需记录，可先保存再修改。</p>
        <p class="text-sm opacity-75">咖啡豆将选取最近一次用到的豆子，而不是按配方历史选取，以免用到已删除或已归档的咖啡豆。</p>
    </div>
</div>