{% extends "my/brewlog_base.html" %}
{% load static %}
{% block title %}编辑咖啡豆{% endblock %}
{% block extra_js %}
<script type="text/javascript" src="{% static 'js/bldtbn.js' %}"></script>
{% endblock %}

{% block content %}
<div x-data="{
        sections: {
            general: true,
            product: true,
            details: false
        },
        typeValue: '{{ form.type.value|default:'SKIP' }}',
        altitudeType: '{{ form.altitude_type.value|default:'SINGLE' }}',
        roastDateEnabled: {% if form.roast_date %}true{% else %}false{% endif %},
        initialRoastDate: '{{ form.roast_date|date:'Y-m-d' }}',
        get showDetails() {
            return this.typeValue !== 'SKIP';
        }
     }"
     x-init="
        typeValue = '{{ form.type.value|default:'SKIP' }}';
        if (typeValue !== 'SKIP') {
            sections.details = true;
        }
        $watch('typeValue', value => {
            if (value !== 'SKIP') {
                sections.details = true;
            }
        })
     "> 
    <!-- 验证提示 -->
    <div x-data="{ showWarning: false, message: '' }"
         x-show="showWarning"
         x-cloak
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-300"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2"
         @show-warning.window="showWarning = true; message = $event.detail; setTimeout(() => showWarning = false, 3000)"
         class="fixed left-0 right-0 mx-auto top-4 z-50 w-auto min-w-[320px] max-w-[90vw] shadow-lg alert alert-warning" style="width: fit-content;">
        <span class="icon-[material-symbols--info-outline] text-2xl"></span>
        <span x-text="message"></span>
    </div>

    {% if bean.is_deleted %}
    <div class="alert alert-error shadow-lg m-4 w-fit">
        <span class="icon-[ph--warning-bold]"></span>
        <span>该咖啡豆已被删除，无法编辑</span>
    </div>
    {% else %}
    <form method="POST" x-data="beanForm" 
          @submit.prevent="handleSubmit($event) && $event.target.submit()">
        {% csrf_token %}
        <div class="py-2 px-1 bg-base-200/90 mb-2 text-base-content sticky top-0 z-30 w-full backdrop-blur-sm transition-shadow duration-100 [transform:translate3d(0,0,0)]">
            <div class="navbar w-full max-w-screen-xl mx-auto">
                <div class="navbar-start lg:ml-4">
                    <a href="{% url 'bean_list' %}" class="btn btn-ghost"><span class="icon-[ep--back]"></span></span>取消</a>
                </div>
                <div class="navbar-center">
                    <span>编辑咖啡豆</span>
                </div>
                <div class="navbar-end">
                    <button type="submit" class="btn btn-primary" :disabled="submitting">
                        <span x-show="!submitting" class="icon-[mdi--floppy]"></span>
                        <span x-show="submitting" class="loading loading-spinner"></span>
                        保存
                    </button>
                </div>
            </div>
        </div>
        <div class="container mx-auto">
            <div class="mx-auto xl:max-w-3xl p-4 text-base-content rounded-lg">
                <!-- 基本信息 -->
                <div class="mb-4 border border-base-300 bg-base-200 rounded-lg">
                    <div class="p-4 flex justify-between items-center cursor-pointer"
                                :class="{ 'border-b border-base-300': sections.general }"
                                @click="sections.general = !sections.general">
                        <h2 class="text-xl font-medium">基本信息</h2>
                        <svg class="w-6 h-6 transform transition-transform" 
                                :class="{ 'rotate-180': sections.general }"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    <div class="p-4 space-y-6" x-show="sections.general" x-transition>
                    <fieldset class="fieldset">
                        <legend class="fieldset-legend text-base font-normal">咖啡豆类型</legend>
                        <div class="flex gap-4 text-base">
                                {% for radio in form.type %}
                                <label class="flex items-center gap-2 cursor-pointer">
                                    <input type="radio" 
                                            name="type" 
                                            value="{{ radio.data.value }}" 
                                            class="radio"
                                            {% if radio.data.value == form.type.value %}checked{% endif %}
                                            @change="typeValue = $event.target.value">
                                    <span>{{ radio.choice_label }}</span>
                                </label>
                                {% endfor %}
                            </div>
                    </fieldset>

                    <fieldset class="fieldset">
                        <legend class="fieldset-legend text-base font-normal">豆商</legend>
                            {{ form.roaster }}
                    </fieldset>
                        
                    <fieldset class="fieldset">
                        <legend class="fieldset-legend text-base font-normal">咖啡豆名称</legend>
                            {{ form.name }}
                    </fieldset>
                        
                    <fieldset class="fieldset">
                        <legend class="fieldset-legend text-base font-normal flex items-center gap-2">
                                <span>是否低因咖啡？</span>
                                <label class="toggle text-base-content">
                                    <input type="checkbox" 
                                           name="is_decaf" 
                                           {% if form.is_decaf.value %}checked{% endif %}>
                                    <svg aria-label="disabled" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                    <svg aria-label="enabled" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g stroke-linejoin="round" stroke-linecap="round" stroke-width="4" fill="none" stroke="currentColor"><path d="M20 6 9 17l-5-5"></path></g></svg>
                                </label>
                        </legend>
                    </fieldset>
                        
                    <fieldset class="fieldset relative">
                        <legend class="fieldset-legend text-base font-normal">风味标签<span class="opacity-50 text-sm font-normal">(选填)</span></legend>
                        <div class="flex flex-wrap gap-2 p-2 rounded-lg mb-2 text-base" id="selected-tags">
                                <!-- 预渲染的标签 -->
                            </div>
                            <div class="relative">
                                <input type="text" 
                                        name="flavor_tags" 
                                        id="{{ form.flavor_tags.id_for_label }}"
                                        class="{{ form.flavor_tags.field.widget.attrs.class }}"
                                        data-tags-url="{% url 'get_flavor_tags' %}"
                                        data-initial-tags="{{ existing_tags }}"
                                        value="{{ existing_tags }}"
                                        placeholder="{{ form.flavor_tags.field.widget.attrs.placeholder }}">
                                <ul id="tags-dropdown" class="menu bg-base-200 rounded-box shadow-lg absolute w-full z-50 max-h-[200px] overflow-y-auto hidden">
                                    <!-- 下拉选项会在这里显示 -->
                                </ul>
                            </div>
                            <input type="hidden" name="flavor_tags_data" id="flavor-tags-data">
                    </fieldset>

                    <fieldset class="fieldset relative" 
                            x-data="{ charCount: '{{ form.notes.value|default:''|length }}' }" 
                            @input-change.window="charCount = $event.detail">
                        <legend class="fieldset-legend text-base font-normal">备注<span class="opacity-50 text-sm font-normal">(选填)</span></legend>
                            {{ form.notes }}
                            <div class="absolute bottom-2 right-2 text-xs" 
                                :class="charCount >= 500 ? 'text-error' : 'text-neutral-400'"
                                x-text="`${charCount}/500`"></div>
                    </fieldset>
                    </div>
                </div>

                <!-- 包装信息 -->
                <div class="mb-4 border border-base-300 bg-base-200 rounded-lg">
                    <div class="p-4 flex justify-between items-center cursor-pointer"
                            :class="{ 'border-b border-base-300': sections.product }"
                            @click="sections.product = !sections.product">
                        <h2 class="text-xl font-medium">包装信息 <span class="text-base font-normal opacity-50">（选填）</span></h2>
                        <svg class="w-6 h-6 transform transition-transform" 
                                :class="{ 'rotate-180': sections.product }"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    <div class="p-4 space-y-6" x-show="sections.product" x-transition>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <fieldset class="fieldset">
                            <legend class="fieldset-legend text-base font-normal">包装规格 (g)</legend>
                                {{ form.bag_weight }}
                        </fieldset>
                            
                        <fieldset class="fieldset">
                            <legend class="fieldset-legend text-base font-normal">库存余量 (g)</legend>
                                {{ form.bag_remain }}
                                <p class="fieldset-label">📌 此处主要用于手动调整余量。如果您新购买了一包同款咖啡豆，建议在咖啡豆列表卡片菜单点击「回购」，这样您的消费记录将更加准确。</p>
                        </fieldset>
                            
                        <fieldset class="fieldset">
                            <legend class="fieldset-legend text-base font-normal">购买时间</legend>
                                {{ form.created_at }}
                                {% if form.created_at.errors %}
                            <div class="text-error text-sm mt-1">{{ form.created_at.errors.0 }}</div>
                                {% endif %}
                            </fieldset>

                        <fieldset class="fieldset">
                            <legend class="fieldset-legend text-base font-normal">购买价格 (元)</legend>
                                {{ form.purchase_price }}
                                {% if form.purchase_price.errors %}
                            <div class="text-error text-sm mt-1">{{ form.purchase_price.errors.0 }}</div>
                                {% endif %}
                        </fieldset>

                        <fieldset class="fieldset">
                            <legend class="fieldset-legend text-base font-normal flex items-center gap-2">
                              <span>是否知道烘焙日期？</span>
                              <label class="toggle text-base-content">
                                <input type="checkbox" 
                                       name="roast_date_enabled" 
                                       x-model="roastDateEnabled"
                                       {% if form.roast_date %}checked{% endif %}>
                                <svg aria-label="disabled" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                <svg aria-label="enabled" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g stroke-linejoin="round" stroke-linecap="round" stroke-width="4" fill="none" stroke="currentColor"><path d="M20 6 9 17l-5-5"></path></g></svg>
                              </label>
                            </legend>
                            <div x-show="roastDateEnabled" x-transition>
                                <div x-ref="roastDateWrapper">
                                    {{ form.roast_date }}
                                </div>
                                <div class="mt-4" x-data="{ restPeriodType: '{{ form.rest_period_max.value|yesno:"RANGE,SINGLE" }}' }">
                                    <legend class="fieldset-legend text-base font-normal">养豆期 (天)<span class="opacity-50 text-sm font-normal">(选填，最多60天)</span></legend>
                                    <div class="flex items-center gap-2 pl-2 text-sm">
                                        <span>按单一值</span>
                                        <input type="checkbox" 
                                               class="toggle toggle-primary toggle-sm" 
                                               x-model="restPeriodType"
                                               @change="restPeriodType = $event.target.checked ? 'RANGE' : 'SINGLE'"
                                               :checked="restPeriodType === 'RANGE'">
                                        <span>按范围值</span>
                                    </div>

                                    <div class="grid" :class="{ 'grid-cols-2 gap-4': restPeriodType === 'RANGE' }">
                                        <fieldset class="fieldset">
                                            <legend class="fieldset-legend text-base font-normal" x-text="restPeriodType === 'SINGLE' ? '养豆天数' : '最短养豆期'"></legend>
                                            <input type="number"
                                                   name="rest_period_min"
                                                   min="1"
                                                   max="60"
                                                   class="input w-full"
                                                   value="{{ form.rest_period_min.value }}">
                                        </fieldset>
                                        
                                        <fieldset class="fieldset" x-show="restPeriodType === 'RANGE'" x-transition>
                                            <legend class="fieldset-legend text-base font-normal">最长养豆期</legend>
                                            <input type="number"
                                                   name="rest_period_max"
                                                   min="1"
                                                   max="60"
                                                   class="input w-full"
                                                   value="{{ form.rest_period_max.value }}">
                                        </fieldset>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        </div>
                    </div>
                </div>

                <!-- 详细属性 -->
                <div class="mb-4 border border-base-300 bg-base-200 rounded-lg">
                    <div class="p-4 flex justify-between items-center cursor-pointer"
                            :class="{ 
                                'border-b border-base-300': sections.details,
                                'opacity-50': typeValue === 'SKIP'
                            }"
                            @click="typeValue !== 'SKIP' && (sections.details = !sections.details)">
                        <h2 class="text-xl font-medium">
                            详细属性
                            <span x-show="typeValue === 'SKIP'" class="text-base font-normal opacity-50">（跳过）</span>
                        </h2>
                        <svg class="w-6 h-6 transform transition-transform" 
                                :class="{ 'rotate-180': sections.details && typeValue !== 'SKIP' }"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    <div class="p-4 space-y-6" x-show="sections.details && typeValue !== 'SKIP'" x-transition>
                        <template x-if="typeValue === 'BLEND'">
                            <div class="mb-4" x-data="blendComponents">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-medium">拼配豆信息</h3>
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm text-base-content/70" x-text="`${$store.blendComponents.components.length}/5`"></span>
                                        <button type="button" 
                                                class="btn btn-outline btn-secondary btn-sm"
                                                @click="
                                                    // 先折叠所有组件
                                                    $store.blendComponents.components.forEach(c => c.isExpanded = false);
                                                    
                                                    // 添加新组件
                                                    const newComponent = {
                                                        id: $store.blendComponents.components.length + 1,
                                                        blend_ratio: '0',
                                                        roast_level: 4,
                                                        origin: '',
                                                        region: '',
                                                        finca: '',
                                                        variety: '',
                                                        altitude_type: 'SINGLE',
                                                        altitude_single: '',
                                                        altitude_min: '',
                                                        altitude_max: '',
                                                        process: '',
                                                        isExpanded: true
                                                    };
                                                    $store.blendComponents.components.push(newComponent);
                                                    $store.blendComponents.updateRatios();
                                                "
                                                x-show="$store.blendComponents.components.length < 5">
                                            <span class="icon-[mdi--plus]"></span>追加拼配
                                        </button>
                                    </div>
                                </div>
                                
                                <template x-for="(component, index) in $store.blendComponents.components" :key="component.id">
                                    <div class="mt-4 px-4 pt-4 border border-base-300 bg-base-100 rounded-lg">
                                        <div class="flex justify-between items-center mb-4">
                                            <h3 class="text-lg font-medium">拼配豆 #<span x-text="index + 1"></span></h3>
                                            <div class="flex items-center gap-2">
                                                <button type="button" 
                                                        class="btn btn-ghost text-error"
                                                        @click="$store.blendComponents.components = $store.blendComponents.components.filter((_, i) => i !== index); $store.blendComponents.updateRatios();"
                                                        x-show="$store.blendComponents.components.length > 1">
                                                    <span class="icon-[mdi--delete] text-lg"></span>
                                                </button>
                                                <button type="button"
                                                        class="btn btn-ghost"
                                                        @click="component.isExpanded = !component.isExpanded">
                                                    <span class="icon-[mdi--chevron-down] text-lg"
                                                          :class="{ 'rotate-180': component.isExpanded }"
                                                          class="transform transition-transform duration-200"></span>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div x-show="component.isExpanded" x-transition>
                                            <fieldset class="fieldset" x-show="$store.blendComponents.components.length > 1">
                                            <legend class="fieldset-legend text-base font-normal">拼配比例(%)</legend>
                                            <label class="opacity-50 text-xs">(如果不清楚可以不修改，保持均摊比例)</label>
                                                <input type="number" 
                                                       :name="'blend_components-' + index + '-blend_ratio'"
                                                       :value="component.blend_ratio"
                                                       @input="component.blend_ratio = $event.target.value; $store.blendComponents.updateRatios();"
                                                       class="input w-full"
                                                       step="0.01" min="0" max="100">
                                            </fieldset>
                                            
                                            <!-- 烘焙程度滑块 -->
                                        <fieldset class="fieldset">
                                            <legend class="fieldset-legend text-base font-normal">烘焙程度</legend>
                                                <input type="range" min="1" max="7" 
                                                    :value="component.roast_level || 4"
                                                    :name="'blend_components-' + index + '-roast_level'"
                                                    class="range range-primary w-full" step="1">
                                                <div class="w-full flex justify-between mt-2">
                                                    <span>极浅</span>
                                                    <span>浅</span>
                                                    <span>浅中</span>
                                                    <span>中</span>
                                                    <span>中深</span>
                                                    <span>深</span>
                                                    <span>极深</span>
                                                </div>
                                        </fieldset>
                                            
                                            <!-- 其他详细属性字段 -->
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <fieldset class="fieldset">
                                                <legend class="fieldset-legend text-base font-normal">产地</legend>
                                                    <input type="text" 
                                                           :name="'blend_components-' + index + '-origin'"
                                                           :value="component.origin || ''"
                                                           class="input w-full">
                                            </fieldset>
                                                
                                            <fieldset class="fieldset">
                                                <legend class="fieldset-legend text-base font-normal">产区</legend>
                                                    <input type="text" 
                                                           :name="'blend_components-' + index + '-region'"
                                                           :value="component.region || ''"
                                                           class="input w-full">
                                            </fieldset>

                                            <fieldset class="fieldset">
                                                <legend class="fieldset-legend text-base font-normal">庄园</legend>
                                                    <input type="text" 
                                                           :name="'blend_components-' + index + '-finca'"
                                                           :value="component.finca || ''"
                                                           class="input w-full">
                                            </fieldset>

                                            <fieldset class="fieldset">
                                                <legend class="fieldset-legend text-base font-normal">品种</legend>
                                                    <input type="text" 
                                                           :name="'blend_components-' + index + '-variety'"
                                                           :value="component.variety || ''"
                                                           class="input w-full">
                                            </fieldset>

                                                <fieldset class="fieldset">
                                                    <legend class="fieldset-legend text-base font-normal">处理法</legend>
                                                    <input type="text" 
                                                           :name="'blend_components-' + index + '-process'"
                                                           :value="component.process || ''"
                                                           class="input w-full">
                                                </fieldset>

                                                <fieldset class="fieldset">
                                                <legend class="fieldset-legend text-base font-normal">海拔信息</legend>
                                                    <div class="flex items-center gap-2 pl-2 text-sm">
                                                        <span>按单一值</span>
                                                        <input type="checkbox" 
                                                               class="toggle toggle-primary toggle-sm" 
                                                               :checked="component.altitude_type === 'RANGE'"
                                                               @change="component.altitude_type = $event.target.checked ? 'RANGE' : 'SINGLE'">
                                                        <span>按范围值</span>
                                                        <input type="hidden" 
                                                               :name="'blend_components-' + index + '-altitude_type'"
                                                               :value="component.altitude_type || 'SINGLE'">
                                                    </div>

                                                    <div class="grid" :class="{ 'grid-cols-2 gap-4': component.altitude_type === 'RANGE' }">
                                                        <fieldset class="fieldset">
                                                            <legend class="fieldset-legend text-base font-normal" x-text="component.altitude_type === 'SINGLE' ? '种植海拔(米)' : '最低海拔(米)'"></legend>
                                                            <input type="number"
                                                                   :name="'blend_components-' + index + '-' + (component.altitude_type === 'SINGLE' ? 'altitude_single' : 'altitude_min')"
                                                                   min="0"
                                                                   max="9999"
                                                                   class="input w-full"
                                                                   :value="component.altitude_type === 'SINGLE' ? component.altitude_single : component.altitude_min">
                                                        </fieldset>
                                                        
                                                        <fieldset class="fieldset" x-show="component.altitude_type === 'RANGE'" x-transition>
                                                            <legend class="fieldset-legend text-base font-normal">最高海拔(米)</legend>
                                                            <input type="number"
                                                                   :name="'blend_components-' + index + '-altitude_max'"
                                                                   min="0"
                                                                   max="9999"
                                                                   class="input w-full"
                                                                   :value="component.altitude_max">
                                                        </fieldset>
                                                    </div>
                                                </fieldset>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                
                                <div x-show="$store.blendComponents.components.length > 1" class="mt-4">
                                    <div class="text-right">
                                        <span :class="{ 'text-error': Math.abs($store.blendComponents.totalRatio - 100) > 0.02 }">
                                            总比例：<span x-text="$store.blendComponents.totalRatio"></span>%
                                            <template x-if="Math.abs($store.blendComponents.totalRatio - 100) > 0.02">
                                                <span>(必须等于100%，允许0.01的误差)</span>
                                            </template>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </template>

                        <!-- 非拼配时的详细属性字段 -->
                        <template x-if="typeValue !== 'BLEND'">
                            <div>
                            <fieldset class="fieldset mb-4">
                                <legend class="fieldset-legend text-base font-normal">烘焙程度</legend>
                                    <input type="range" min="1" max="7" value="{{ form.roast_level.value|default:4 }}" 
                                        class="range range-primary w-full" step="1" name="roast_level"
                                        data-field="roast_level">
                                    <div class="w-full flex justify-between mt-2">
                                        <span>极浅</span>
                                        <span>浅</span>
                                        <span>浅中</span>
                                        <span>中</span>
                                        <span>中深</span>
                                        <span>深</span>
                                        <span>极深</span>
                                    </div>
                            </fieldset>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <fieldset class="fieldset">
                                        <legend class="fieldset-legend text-base font-normal">产地</legend>
                                        {{ form.origin }}
                                    </fieldset>
                                    
                                    <fieldset class="fieldset">
                                        <legend class="fieldset-legend text-base font-normal">产区</legend>
                                        {{ form.region }}
                                    </fieldset>

                                    <fieldset class="fieldset">
                                        <legend class="fieldset-legend text-base font-normal">庄园</legend>
                                        {{ form.finca }}
                                    </fieldset>

                                    <fieldset class="fieldset">
                                        <legend class="fieldset-legend text-base font-normal">品种</legend>
                                        {{ form.variety }}
                                    </fieldset>

                                    <fieldset class="fieldset">
                                        <legend class="fieldset-legend text-base font-normal">处理法</legend>
                                        {{ form.process }}
                                    </fieldset>
                                    
                                    <fieldset class="fieldset">
                                        <legend class="fieldset-legend text-base font-normal">海拔信息</legend>
                                        <div class="flex items-center gap-2 pl-2 text-sm">
                                            <span>按单一值</span>
                                            <input type="checkbox" 
                                                   class="toggle toggle-primary toggle-sm" 
                                                   x-model="altitudeType"
                                                   @change="altitudeType = $event.target.checked ? 'RANGE' : 'SINGLE'"
                                                   :checked="altitudeType === 'RANGE'">
                                            <span>按范围值</span>
                                            <input type="hidden" name="altitude_type" :value="altitudeType">
                                        </div>

                                        <div class="grid" :class="{ 'grid-cols-2 gap-4': altitudeType === 'RANGE' }">
                                            <fieldset class="fieldset">
                                                <legend class="fieldset-legend text-base font-normal" x-text="altitudeType === 'SINGLE' ? '种植海拔(米)' : '最低海拔(米)'"></legend>
                                                <input type="number"
                                                       :name="altitudeType === 'SINGLE' ? 'altitude_single' : 'altitude_min'"
                                                       min="0"
                                                       max="9999"
                                                       class="input w-full"
                                                       :value="altitudeType === 'SINGLE' ? 
                                                              '{{ form.altitude_single.value|default:'' }}' : 
                                                              '{{ form.altitude_min.value|default:'' }}'">
                                            </fieldset>
                                            
                                            <fieldset class="fieldset" x-show="altitudeType === 'RANGE'" x-transition>
                                                <legend class="fieldset-legend text-base font-normal">最高海拔(米)</legend>
                                                <input type="number"
                                                       name="altitude_max"
                                                       min="0"
                                                       max="9999"
                                                       class="input w-full"
                                                       value="{{ form.altitude_max.value|default:'' }}">
                                            </fieldset>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </form>
    {% endif %}
</div>

<!-- 在页面底部添加初始化脚本 -->
<!-- Django模板将动态渲染blendComponentsData变量 -->
<script>
    document.addEventListener('alpine:init', () => {
        const blendComponentsData = {{ blend_components|default:'[]'|safe }};
        Alpine.store('blendComponents', {
            components: blendComponentsData.length > 0 ? 
                blendComponentsData.map((comp, index) => ({
                    id: index + 1,
                    ...comp,
                    isExpanded: true
                })) : [{
                    id: 1,
                    blend_ratio: '100',
                    roast_level: 4,
                    origin: '',
                    region: '',
                    finca: '',
                    variety: '',
                    altitude_type: 'SINGLE',
                    altitude_single: '',
                    altitude_min: '',
                    altitude_max: '',
                    process: '',
                    isExpanded: true
                }],
            totalRatio: blendComponentsData.length > 0 ?
                blendComponentsData
                    .reduce((sum, comp) => sum + (parseFloat(comp.blend_ratio) || 0), 0)
                    .toFixed(2) : '100.00',
            updateRatios() {
                const equalRatio = (100 / this.components.length).toFixed(2);
                this.components.forEach(comp => {
                    comp.blend_ratio = equalRatio;
                });
                this.totalRatio = this.components
                    .reduce((sum, comp) => sum + (parseFloat(comp.blend_ratio) || 0), 0)
                    .toFixed(2);
            }
        });
    });
</script>
{% endblock %}