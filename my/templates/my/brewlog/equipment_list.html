{% extends "my/brewlog_base.html" %}
{% load static %}
{% load format_filters %}
{% block title %}我的咖啡设备{% endblock %}
{% block extra_js %}
<script type="text/javascript" src="{% static 'js/brewlog.js' %}"></script>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/brewlog.css' %}">
{% endblock %}
{% block content %}
<div x-data x-init="$store.filterState.init()">
    <div class="py-2 px-1 bg-base-200 mb-2 text-base-content">
        <div class="navbar w-full max-w-screen-xl mx-auto">
            <div class="flex-none w-[var(--nav-width)]">
                <div class="lg:ml-4 btn btn-ghost" @click="$store.filterState.toggle()">
                    <span class="icon-[bi--filter-circle] text-lg" 
                          x-show="!$store.filterState.isOpen"></span>
                    <span class="icon-[bi--filter-circle-fill] text-lg" 
                          x-show="$store.filterState.isOpen"></span>
                    <span class="hidden lg:block">筛选</span>
                </div>
            </div>
            <div class="flex-1 flex justify-center">
                我的咖啡设备
            </div>
            <div class="flex-none w-[var(--nav-width)] flex justify-end">
                <a href="{% url 'add_equipment_page' %}" 
                   class="btn btn-ghost"
                   x-data="{ loading: false }"
                   @click="loading = true"
                   :class="{ 'btn-disabled': loading }">
                    <span class="hidden lg:block">新增</span>
                    <span x-show="!loading" class="icon-[icons8--plus] text-2xl"></span>
                    <span x-show="loading" class="loading loading-spinner"></span>
                </a>
            </div>
        </div>
    </div>
    <div class="flex flex-wrap gap-2 items-center mb-4 px-4 justify-center text-base-content"
         x-show="$store.filterState.isOpen"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2">
        <div class="dropdown">
            <div tabindex="0" role="button" class="btn btn-sm">
                品牌
                {% if current_brand %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                <li><a href="?{% if current_sort %}sort_by={{ current_sort }}&{% endif %}{% if current_type %}type={{ current_type }}{% endif %}"
                    class="{% if not current_brand %}menu-active{% endif %}">全部</a></li>
                {% for brand_name in all_brands %}
                <li><a href="?brand={{ brand_name }}{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}"
                    class="{% if current_brand == brand_name %}menu-active{% endif %}">{{ brand_name }}</a></li>
                {% endfor %}
            </ul>
        </div>

        <div class="dropdown">
            <div tabindex="0" role="button" class="btn btn-sm">
                类型
                {% if current_type %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                <li><a href="?{% if current_sort %}sort_by={{ current_sort }}&{% endif %}{% if current_brand %}brand={{ current_brand }}{% endif %}"
                    class="{% if not current_type %}menu-active{% endif %}">全部</a></li>
                {% for type_value, type_label in equipment_types %}
                <li><a href="?type={{ type_value }}{% if current_sort %}&sort_by={{ current_sort }}{% endif %}{% if current_brand %}&brand={{ current_brand }}{% endif %}"
                    class="{% if current_type == type_value %}menu-active{% endif %}">{{ type_label }}</a></li>
                {% endfor %}
            </ul>
        </div>

        <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-sm">
                排序
                {% if current_sort %}<span class="status status-success ml-1"></span>{% endif %}
            </div>
            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-sm bg-base-100 rounded-box w-52">
                <li><a href="?sort_by={% if current_sort == 'time' %}time_asc{% else %}time{% endif %}{% if current_brand %}&brand={{ current_brand }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}"
                    class="{% if current_sort == 'time' or current_sort == 'time_asc' %}menu-active{% endif %}">
                    按时间
                    {% if current_sort == 'time' %}
                    <span class="icon-[mdi--arrow-down-thin]"></span>
                    {% elif current_sort == 'time_asc' %}
                    <span class="icon-[mdi--arrow-up-thin]"></span>
                    {% endif %}
                </a></li>
                <li><a href="?sort_by={% if current_sort == 'brand' %}brand_desc{% else %}brand{% endif %}{% if current_brand %}&brand={{ current_brand }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}"
                    class="{% if current_sort == 'brand' or current_sort == 'brand_desc' %}menu-active{% endif %}">
                    按品牌
                    {% if current_sort == 'brand' %}
                    <span class="icon-[mdi--arrow-down-thin]"></span>
                    {% elif current_sort == 'brand_desc' %}
                    <span class="icon-[mdi--arrow-up-thin]"></span>
                    {% endif %}
                </a></li>
                <li><a href="?sort_by={% if current_sort == 'type' %}type_desc{% else %}type{% endif %}{% if current_brand %}&brand={{ current_brand }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}"
                    class="{% if current_sort == 'type' or current_sort == 'type_desc' %}menu-active{% endif %}">
                    按类型
                    {% if current_sort == 'type' %}
                    <span class="icon-[mdi--arrow-down-thin]"></span>
                    {% elif current_sort == 'type_desc' %}
                    <span class="icon-[mdi--arrow-up-thin]"></span>
                    {% endif %}
                </a></li>
            </ul>
        </div>

        <div>
            {% if current_brand or current_type or current_sort %}
            <a href="{% url 'equipment_list' %}" class="btn btn-sm btn-ghost text-success">
                <span class="icon-[bx--reset] text-lg"></span>
            </a>
            {% endif %}
        </div>
    </div>
    <div class="container mx-auto xl:max-w-7xl text-base-content px-2 mb-8" x-data>
        <div class="w-full px-4 md:px-20">
            <div class="flex flex-col">
                {% if active_groups %}
                    <!-- 未归档设备 -->
                    {% for group_key, group_data in active_groups.items %}
                        <div class="divider divider-secondary">{{ group_key }}</div>
                        <div class="grid grid-cols-1 grid-rows-1 md:grid-cols-2 gap-4 mb-8">
                            {% for equip in group_data %}
                                <div class="border-2 border-base-content/5 hover:shadow-lg active:shadow-lg rounded-lg overflow-visible"
                                     data-equipment-id="{{ equip.id }}"
                                     data-archived="false">
                                    <div class="p-4">
                                        <div class="card-actions justify-between flex items-center">
                                            <span>{{ equip.brand }}</span>
                                            {% if equip.is_favorite %}
                                            <span class="icon-[mdi--star] text-yellow-500"></span>
                                            {% endif %}
                                            <div class="relative dropdown dropdown-end">
                                                <div tabindex="0" role="button" class="btn btn-ghost btn-circle text-2xl">
                                                    <span class="icon-[mi--options-vertical]"></span>
                                                </div>
                                                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-50 w-52 p-2 shadow-lg absolute">
                                                    <li><a href="{% url 'view_equipment' equip.id %}"><span class="icon-[mdi--eye-outline]"></span> 查看详情</a></li>
                                                    {% if equip.is_favorite %}
                                                    <li><button @click="$store.brewlogList.toggleFavoriteEquipment({{ equip.id }})">
                                                        <span class="icon-[mdi--star-off]"></span> 取消首选
                                                    </button></li>
                                                    {% else %}
                                                    <li><button @click="$store.brewlogList.toggleFavoriteEquipment({{ equip.id }})">
                                                        <span class="icon-[mdi--star]"></span> 设为首选
                                                    </button></li>
                                                    {% endif %}
                                                    <li><button @click="$store.brewlogList.archiveEquipment({{ equip.id }})"><span class="icon-[material-symbols--archive-outline-rounded]"></span> 归档</button></li>
                                                    <li><a href="{% url 'edit_equipment_page' equip.id %}"><span class="icon-[material-symbols--edit-square-outline-rounded]"></span> 修改</a></li>
                                                    <li>
                                                        <button onclick="document.getElementById('delete_equipment_modal_{{ equip.id }}').showModal()" class="text-error">
                                                            <span class="icon-[material-symbols--delete]"></span> 删除
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="flex gap-4 items-baseline">
                                            <h5 class="text-lg font-semibold">{{ equip.name }}</h5>
                                        </div>
                                        <div class="flex gap-2 justify-between my-2">
                                            <div class="flex flex-wrap gap-2 items-center">
                                                {% if equip.type == 'BREWER' %}
                                                <div class="join">
                                                    <span class="badge join-item">{{ equip.get_brew_method_display|default_if_none:'-' }}</span>
                                                    <span class="badge badge-ghost join-item">冲煮器具</span>
                                                </div>
                                                {% endif %}
                                                {% if equip.type == 'GRINDER' %}
                                                <div class="join">
                                                    <span class="badge join-item">{{ equip.get_grinder_purpose_display|default_if_none:'-' }}</span>
                                                    <span class="badge badge-ghost join-item">磨豆机</span>
                                                </div>
                                                {% endif %}
                                                {% if equip.type == 'GADGET' %}
                                                <span class="badge badge-ghost">小工具</span>
                                                {% endif %}
                                                {% if equip.type == 'GADGET_KIT' %}
                                                <span class="badge badge-ghost">小工具组合</span>
                                                {% endif %}
                                                {% if equip.notes %}
                                                <div class="badge gap-2 text-sm text-info tooltip" data-tip="{{ equip.notes }}" x-data x-init="$el.setAttribute('data-tip', $el.getAttribute('data-tip').trim())">
                                                    <span class="icon-[ph--note-duotone]"></span>
                                                </div>
                                                {% endif %}
                                                {% if equip.purchase_price %}
                                                <div class="badge gap-2 text-sm text-info tooltip leading-[1.2]" data-tip="{{ equip.purchase_price|format_number }}">¥</div>
                                                {% endif %}
                                                {% if equip.type == 'GRINDER' and equip.grind_size_preset %}
                                                <div class="badge gap-2 text-sm text-info tooltip" data-tip="{{ equip.grind_size_preset }}"><span class="icon-[f7--circle-grid-hex-fill]"></span></div>
                                                {% endif %}
                                                {% if equip.type == 'GADGET_KIT' %}
                                                        {% for gadget in equip.gadget_components.all %}
                                                        <span class="badge">{{ gadget.name }}</span>
                                                        {% endfor %}
                                                {% endif %}
                                            </div>
                                            {% if equip.created_at and equip.type != 'GADGET_KIT' %}
                                            <div class="flex justify-end mt-2">
                                                <div class="relative w-14 h-14">
                                                    <!-- 外层进度环 - 成色 -->
                                                    <div class="absolute inset-0 rounded-full progress-ring-thick"
                                                            style="background: conic-gradient(rgb(34 139 34) {{ equip.depreciation_rate }}%, transparent 0);">
                                                    </div>
                                                    <!-- 内层进度环 - 回本进度 -->
                                                    <div class="absolute inset-[3px] rounded-full progress-ring-thin"
                                                            style="background: conic-gradient(rgb(255 165 0) {{ equip.break_even_progress }}%, transparent 0);">
                                                    </div>
                                                    <!-- 中心白色圆形和文字 -->
                                                    <div class="absolute inset-[6px] rounded-full bg-base-100 flex flex-col items-center justify-center">
                                                        <span class="text-xs leading-none tooltip tooltip-left" data-tip="外环：显示设备剩余价值&#10;基于购买时间自动计算折旧情况">{{ equip.depreciation_rate|floatformat:0 }}新</span>
                                                        <span class="text-[10px] text-warning leading-none mt-0.5 tooltip tooltip-left" data-tip="内环：展示投资回报率&#10;根据使用频率和购买价格估算&#10;可简单理解为回本进度">{{ equip.break_even_progress|floatformat:0 }}%</span>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                {% endif %}

                {% if archived_groups %}
                    <!-- 已归档设备 -->
                    <div class="bg-base-100 border-base-300 collapse collapse-plus border">
                        <input type="checkbox" class="peer" /> 
                        <div class="collapse-title text-lg font-medium bg-base-100 text-base-content peer-checked:bg-base-100 peer-checked:text-base-content">
                            已归档 ({{ archived_equipment|length }}个)
                        </div>
                        <div class="collapse-content bg-base-100 text-base-content peer-checked:bg-base-100 peer-checked:text-base-content">
                            {% for group_key, group_data in archived_groups.items %}
                                <div class="divider">{{ group_key }}</div>
                                <div class="grid grid-cols-1 grid-rows-1 md:grid-cols-2 gap-4 mb-8">
                                    {% for equip in group_data %}
                                        <div class="border-2 border-base-content/5 hover:shadow-lg active:shadow-lg rounded-lg overflow-visible"
                                             data-equipment-id="{{ equip.id }}"
                                             data-archived="true">
                                            <div class="p-4">
                                                <div class="card-actions justify-between flex items-center">
                                                    <span>{{ equip.brand }}</span>
                                                    <div class="relative dropdown dropdown-end">
                                                        <div tabindex="0" role="button" class="btn btn-ghost btn-circle text-2xl">
                                                            <span class="icon-[mi--options-vertical]"></span>
                                                        </div>
                                                        <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-50 w-52 p-2 shadow-lg absolute">
                                                            <li><a href="{% url 'view_equipment' equip.id %}"><span class="icon-[mdi--eye-outline]"></span> 查看详情</a></li>
                                                            {% if equip.is_favorite %}
                                                            <li><button @click="$store.brewlogList.toggleFavoriteEquipment({{ equip.id }})">
                                                                <span class="icon-[mdi--star-off]"></span> 取消首选
                                                            </button></li>
                                                            {% else %}
                                                            <li><button @click="$store.brewlogList.toggleFavoriteEquipment({{ equip.id }})">
                                                                <span class="icon-[mdi--star]"></span> 设为首选
                                                            </button></li>
                                                            {% endif %}
                                                            <li><button @click="$store.brewlogList.unarchiveEquipment({{ equip.id }})"><span class="icon-[material-symbols--unarchive-outline-rounded]"></span> 取消归档</button></li>
                                                            <li><a href="{% url 'edit_equipment_page' equip.id %}"><span class="icon-[material-symbols--edit-square-outline-rounded]"></span> 修改</a></li>
                                                            <li>
                                                                <button onclick="document.getElementById('delete_equipment_modal_{{ equip.id }}').showModal()" class="text-error">
                                                                    <span class="icon-[material-symbols--delete]"></span> 删除
                                                                </button>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="flex gap-4 items-baseline">
                                                    <h5 class="text-lg font-semibold">{{ equip.name }}</h5>
                                                    {% if equip.brew_method %}<span class="text-xs opacity-60">{{ equip.get_brew_method_display }}</span>{% endif %}
                                                    {% if equip.type == 'GRINDER' %}
                                                    <span class="text-xs opacity-60">磨豆机</span>
                                                    {% endif %}
                                                </div>
                                                <div class="flex gap-2 justify-between my-2">
                                                    <div class="flex flex-wrap gap-2 items-center">
                                                        {% if equip.notes %}
                                                        <div class="badge gap-2 text-sm text-info"><span class="icon-[ph--note-duotone]"></span></div>
                                                        {% endif %}
                                                        {% if equip.purchase_price %}
                                                        <div class="badge gap-2 text-sm text-info">¥</div>
                                                        {% endif %}
                                                    </div>
                                                    {% if equip.created_at and equip.type != 'GADGET_KIT' %}
                                                    <div class="flex justify-end mt-2">
                                                        <div class="relative w-14 h-14">
                                                            <!-- 外层进度环 - 成色 -->
                                                            <div class="absolute inset-0 rounded-full progress-ring-thick"
                                                                    style="background: conic-gradient(rgb(34 139 34) {{ equip.depreciation_rate }}%, transparent 0);">
                                                            </div>
                                                            <!-- 内层进度环 - 回本进度 -->
                                                            <div class="absolute inset-[3px] rounded-full progress-ring-thin"
                                                                    style="background: conic-gradient(rgb(255 165 0) {{ equip.break_even_progress }}%, transparent 0);">
                                                            </div>
                                                            <!-- 中心白色圆形和文字 -->
                                                            <div class="absolute inset-[6px] rounded-full bg-base-100 flex flex-col items-center justify-center">
                                                                <span class="text-xs leading-none">{{ equip.depreciation_rate|floatformat:0 }}新</span>
                                                                <span class="text-[10px] text-warning leading-none mt-0.5">{{ equip.break_even_progress|floatformat:0 }}%</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                {% if not active_groups and not archived_groups %}
                <div class="text-center py-12 text-base-content/60">
                    <p class="text-lg">暂无记录</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
<div x-data>
    {% if active_equipment %}
        {% for equip in active_equipment %}
        <dialog id="delete_equipment_modal_{{ equip.id }}" class="modal text-base-content">
            <div class="modal-box">
                <h3 class="text-lg font-bold">确认删除</h3>
                <p class="py-4">确定要删除这个设备吗？</p>
                <div class="modal-action">
                    <form method="dialog">
                        <button class="btn btn-error" @click="$store.brewlogList.deleteEquipment({{ equip.id }})">删除</button>
                        <button class="btn">取消</button>
                    </form>
                </div>
            </div>
            <form method="dialog" class="modal-backdrop">
                <button>close</button>
            </form>
        </dialog>
        {% endfor %}
    {% endif %}
    {% if archived_equipment %}
        {% for equip in archived_equipment %}
        <dialog id="delete_equipment_modal_{{ equip.id }}" class="modal text-base-content">
            <div class="modal-box">
                <h3 class="text-lg font-bold">确认删除</h3>
                <p class="py-4">确定要删除这个设备吗？此操作不可撤销。删除后，相关冲煮记录不会受影响。</p>
                <div class="modal-action">
                    <form method="dialog">
                        <button class="btn btn-error" @click="$store.brewlogList.deleteEquipment({{ equip.id }})">删除</button>
                        <button class="btn">取消</button>
                    </form>
                </div>
            </div>
            <form method="dialog" class="modal-backdrop">
                <button>close</button>
            </form>
        </dialog>
        {% endfor %}
    {% endif %}
</div>
{% endblock %} 