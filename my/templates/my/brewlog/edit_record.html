{% extends "my/brewlog_base.html" %}
{% load static %}
{% load time_filters %}
{% load format_filters %}
{% block title %}编辑冲煮记录{% endblock %}
{% block extra_js %}
<script type="text/javascript" src="{% static 'js/bldtrcrd.js' %}"></script>
{% endblock %}
{% block extra_css %}
<link rel="stylesheet" type="text/css" href="{% static 'css/bldtrcrd.css' %}">
{% endblock %}

{% block content %}
<div x-data="brewingSteps" data-steps-enabled="{{ steps_enabled|yesno:'true,false' }}" data-steps='{{ steps_json }}'>
    <form method="POST" 
        x-data="{ 
            submitting: false,
            sections: {
                brewing: true,
                equipment: true,
                measurement: true,
                steps: true,
                environment: {% if record.water_quality or record.room_temperature or record.room_humidity %}true{% else %}false{% endif %},
                flavor_notes: {{ record.has_tasting_notes|yesno:'true,false' }}
            }
        }" 
        @submit.prevent="
            submitting = true;
            if (!$refs.grindSizeInput.value.trim()) {
                $refs.grindSizeInput._preventEmptySet = true;
                $refs.grindSizeInput.value = '-';
            }
            $event.target.submit();
        ">
        {% csrf_token %}
        <div class="py-2 px-1 bg-base-200/90 mb-2 text-base-content sticky top-0 z-30 w-full backdrop-blur-sm transition-shadow duration-100 [transform:translate3d(0,0,0)]">
            <div class="navbar w-full max-w-screen-xl mx-auto">
                <div class="navbar-start lg:ml-4">
                    <a href="{% url 'brewlog' %}" class="btn btn-ghost"><span class="icon-[ep--back]"></span></span>取消</a>
                </div>
                <div class="navbar-center">
                    <span>编辑冲煮记录</span>
                </div>
                <div class="navbar-end">
                    <button type="submit" class="btn btn-primary" :disabled="submitting">
                        <span x-show="!submitting" class="icon-[mdi--floppy]"></span>
                        <span x-show="submitting" class="loading loading-spinner loading-xs"></span>
                        保存
                    </button>
                </div>
            </div>
        </div>

        <div class="max-w-7xl mx-auto px-2 md:px-4 lg:px-6">
            <div class="mx-auto max-w-2xl xl:max-w-3xl p-4 text-base-content rounded-lg">
                {% if form.errors %}
                <div class="alert alert-error mb-4">
                    <span class="icon-[material-symbols--error]"></span>
                    <span>表单填写有误，请检查标红的输入框</span>
                </div>
                {% endif %}
                
                <!-- 冲煮信息 -->
                <div class="mb-4 border border-base-300 bg-base-200 rounded-lg">
                    <div class="p-4 flex justify-between items-center cursor-pointer"
                        :class="{ 'border-b border-base-300': sections.brewing }"
                        @click="sections.brewing = !sections.brewing">
                        <h2 class="text-xl font-medium">冲煮信息</h2>
                        <svg class="w-6 h-6 transform transition-transform" 
                            :class="{ 'rotate-180': sections.brewing }"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    <div class="p-4 space-y-6" x-show="sections.brewing" x-transition>
                        <div class="space-y-6">
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">配方名称<span class="opacity-50 text-sm font-normal">(选填)</span></legend>
                                <input type="text" name="recipe_name" 
                                    class="input w-full {% if form.recipe_name.errors %}input-error{% endif %}" 
                                    maxlength="50"
                                    placeholder="为这个配方起个名字"
                                    value="{{ form.recipe_name.value|default:'' }}">
                                {% if form.recipe_name.errors %}
                                    <div class="text-error text-sm mt-1">{{ form.recipe_name.errors.0 }}</div>
                                {% endif %}
                            </fieldset>

                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">记录时间</legend>
                                {{ form.created_at }}
                            </fieldset>

                            <fieldset class="fieldset mb-4">
                                <legend class="fieldset-legend text-base font-normal">评价</legend>
                                <input type="range" min="1" max="10" value="{{ form.rating_level.value|default:5 }}" class="range range-accent w-full"
                                    step="1" data-field="rating_level">
                                <div class="w-full flex justify-between text-lg lg:text-2xl mt-2">
                                    <span>😫</span>
                                    <span>☹️</span>
                                    <span>🙁</span>
                                    <span>😕</span>
                                    <span>😐</span>
                                    <span>🙂</span>
                                    <span>😌</span>
                                    <span>😃</span>
                                    <span>😄</span>
                                    <span>😆</span>
                                </div>
                                {{ form.rating_level }}
                            </fieldset>
                            
                            <fieldset class="fieldset relative" 
                                x-data="{ charCount: '{{ form.notes.value|default:''|length }}' }" 
                                @input-change.window="charCount = $event.detail">
                                <legend class="fieldset-legend text-base font-normal">备注<span class="opacity-50 text-sm font-normal">(选填)</span></legend>
                                {{ form.notes }}
                                <div class="absolute bottom-2 right-2 text-xs" 
                                    :class="charCount >= 500 ? 'text-error' : 'text-neutral-400'"
                                    x-text="`${charCount}/500`"></div>
                            </fieldset>
                        </div>
                    </div>
                </div>

                <!-- 用豆和器具 -->
                <div class="mb-4 border border-base-300 bg-base-200 rounded-lg">
                    <div class="p-4 flex justify-between items-center cursor-pointer"
                        :class="{ 'border-b border-base-300': sections.equipment }"
                        @click="sections.equipment = !sections.equipment">
                        <h2 class="text-xl font-medium">用豆和器具</h2>
                        <svg class="w-6 h-6 transform transition-transform" 
                            :class="{ 'rotate-180': sections.equipment }"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    <div class="p-4 space-y-6" x-show="sections.equipment" x-transition>
                        <div class="space-y-6">
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">咖啡豆</legend>
                                <select name="coffee_bean" class="select w-full {% if form.coffee_bean.errors %}select-error{% endif %}">
                                    <option value="">请选择咖啡豆</option>
                                    {% for bean_info in active_beans %}
                                    <option value="{{ bean_info.bean.id }}"
                                        {% if form.coffee_bean.value|stringformat:"s" == bean_info.bean.id|stringformat:"s" %}selected{% endif %}>
                                        {{ bean_info.bean.roaster }} {{ bean_info.bean.name }}
                                        {% if bean_info.bean.is_favorite %}⭐{% endif %}
                                        {% if bean_info.stock_status %} {{ bean_info.stock_status }}{% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                            </fieldset>
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">冲煮器具</legend>
                                <select name="brewing_equipment" class="select w-full">
                                    <option value="">请选择冲煮器具</option>
                                    {% for equip in active_equipment %}
                                        {% if equip.type == 'BREWER' %}
                                            <option value="{{ equip.id }}"
                                                {% if form.initial.brewing_equipment == equip.id %}selected{% endif %}>
                                                {{ equip.brand }} {{ equip.name }}{% if equip.is_favorite %} ⭐{% endif %}
                                            </option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                            </fieldset>
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">磨豆机</legend>
                                <select name="grinding_equipment" class="select w-full">
                                    <option value="">请选择磨豆机</option>
                                    {% for equip in active_equipment %}
                                        {% if equip.type == 'GRINDER' %}
                                            <option value="{{ equip.id }}"
                                                {% if form.initial.grinding_equipment == equip.id %}selected{% endif %}>
                                                {{ equip.brand }} {{ equip.name }}{% if equip.is_favorite %} ⭐{% endif %}
                                            </option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                            </fieldset>
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">研磨设置<span class="opacity-50 text-sm font-normal">(例如32挡或2.1.0刻度，不限格式)</span></legend>
                                <input type="text" name="grind_size" x-ref="grindSizeInput" 
                                    class="input w-full" 
                                    placeholder="请输入磨豆机的刻度或挡位" 
                                    value="{{ form.grind_size.value|default:'' }}"
                                    {% if form.grind_size.errors %}aria-invalid="true"{% endif %}>
                                {% if form.grind_size.errors %}
                                    <div class="text-error text-sm mt-1">{{ form.grind_size.errors.0 }}</div>
                                {% endif %}
                            </fieldset>
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">小工具组合<span class="opacity-50 text-sm font-normal">(选填)</span></legend>
                                <select name="gadget_kit" class="select w-full">
                                    <option value="">请选择小工具组合</option>
                                    {% for kit in active_equipment %}
                                        {% if kit.type == 'GADGET_KIT' %}
                                            <option value="{{ kit.id }}"
                                                {% if form.gadget_kit.value == kit.id %}selected{% endif %}>
                                                {{ kit.name }}{% if kit.is_favorite %} ⭐{% endif %}
                                            </option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                                {% if form.gadget_kit.errors %}
                                    <div class="text-error text-sm mt-1">{{ form.gadget_kit.errors.0 }}</div>
                                {% endif %}
                            </fieldset>
                            {% with gadget_count=form.gadgets.field.queryset.count %}
                            {% if gadget_count > 0 %}
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">小工具<span class="opacity-50 text-sm font-normal">(选填，可多选)</span></legend>
                                <div class="flex flex-wrap gap-4 mt-2">
                                    {% for gadget in form.gadgets %}
                                    <label class="flex items-center gap-2 cursor-pointer">
                                        {{ gadget.tag }}
                                        <span class="text-base">{{ gadget.choice_label }}</span>
                                    </label>
                                    {% endfor %}
                                </div>
                            </fieldset>
                            {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                </div>

                <!-- 测量数据 -->
                <div class="mb-4 border border-base-300 bg-base-200 rounded-lg">
                    <div class="p-4 flex justify-between items-center cursor-pointer"
                        :class="{ 'border-b border-base-300': sections.measurement }"
                        @click="sections.measurement = !sections.measurement">
                        <h2 class="text-xl font-medium">测量数据</h2>
                        <svg class="w-6 h-6 transform transition-transform" 
                            :class="{ 'rotate-180': sections.measurement }"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    <div class="p-4 space-y-6" x-show="sections.measurement" x-transition>
                        <div class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <fieldset class="fieldset">
                                    <legend class="fieldset-legend text-base font-normal">粉重(g)</legend>
                                    <input type="number" name="dose_weight" step="0.01" min="0.01" 
                                        class="input w-full{% if form.dose_weight.errors %} input-error{% endif %}" 
                                        value="{{ form.dose_weight.value }}">
                                    {% if form.dose_weight.errors %}
                                    <div class="text-error text-sm mt-1">{{ form.dose_weight.errors.0 }}</div>
                                    {% endif %}
                                </fieldset>
                                <fieldset class="fieldset">
                                    <legend class="fieldset-legend text-base font-normal">液重(g)</legend>
                                    <input type="number" name="yield_weight" step="0.01" min="0.01" 
                                        class="input w-full{% if form.yield_weight.errors %} input-error{% endif %}" 
                                        value="{{ form.yield_weight.value }}">
                                    {% if form.yield_weight.errors %}
                                    <div class="text-error text-sm mt-1">{{ form.yield_weight.errors.0 }}</div>
                                    {% endif %}
                                </fieldset>
                                <fieldset class="fieldset">
                                    <legend class="fieldset-legend text-base font-normal">水温(℃) <span class="opacity-50 text-sm font-normal">(选填)</span></legend>
                                    {{ form.water_temperature }}
                                </fieldset>
                                <fieldset class="fieldset">
                                    <legend class="fieldset-legend text-base font-normal">萃取时间(时:分:秒)</legend>
                                    <div class="flex items-center gap-2">
                                        <input type="number" id="brewing_hours" class="input w-full" 
                                            min="0" max="99" placeholder="时"
                                            value="{{ record.brewing_time|duration_hours }}">
                                        <span>:</span>
                                        <input type="number" id="brewing_minutes" class="input w-full" 
                                            min="0" max="59" placeholder="分钟"
                                            value="{{ record.brewing_time|duration_minutes }}">
                                        <span>:</span>
                                        <input type="number" id="brewing_seconds" class="input w-full" 
                                            min="0" max="59" placeholder="秒"
                                            value="{{ record.brewing_time|duration_seconds }}">
                                        {% if form.brewing_time.errors %}
                                        <div class="text-error text-sm mt-1">{{ form.brewing_time.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    {{ form.brewing_time }}
                                </fieldset>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 环境数据 -->
                <div class="mb-4 border border-base-300 bg-base-200 rounded-lg">
                    <div class="p-4 flex justify-between items-center cursor-pointer"
                        :class="{ 'border-b border-base-300': sections.environment }"
                        @click="sections.environment = !sections.environment">
                        <h2 class="text-xl font-medium">
                            环境数据
                            <span class="text-base font-normal opacity-50">（选填）</span>
                        </h2>
                        <svg class="w-6 h-6 transform transition-transform" 
                            :class="{ 'rotate-180': sections.environment }"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    <div class="p-4 space-y-6" x-show="sections.environment" x-transition>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">水质</legend>
                                <input type="text" name="water_quality" 
                                    class="input w-full{% if form.water_quality.errors %} input-error{% endif %}" 
                                    maxlength="50"
                                    placeholder="请输入水质或水源，格式不限"
                                    value="{{ record.water_quality|default:'' }}">
                                {% if form.water_quality.errors %}
                                <div class="text-error text-sm mt-1">{{ form.water_quality.errors.0 }}</div>
                                {% endif %}
                            </fieldset>
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">室温(℃)</legend>
                                <input type="number" name="room_temperature" 
                                    class="input w-full{% if form.room_temperature.errors %} input-error{% endif %}" 
                                    step="0.1" min="-50.0" max="50.0"
                                    placeholder="请输入当前室温"
                                    value="{{ record.room_temperature|default:'' }}">
                                {% if form.room_temperature.errors %}
                                <div class="text-error text-sm mt-1">{{ form.room_temperature.errors.0 }}</div>
                                {% endif %}
                            </fieldset>
                            <fieldset class="fieldset">
                                <legend class="fieldset-legend text-base font-normal">环境湿度(%)</legend>
                                <input type="number" name="room_humidity" 
                                    class="input w-full{% if form.room_humidity.errors %} input-error{% endif %}" 
                                    step="1" min="0" max="100"
                                    placeholder="请输入环境湿度值"
                                    value="{{ record.room_humidity|default:'' }}">
                                {% if form.room_humidity.errors %}
                                <div class="text-error text-sm mt-1">{{ form.room_humidity.errors.0 }}</div>
                                {% endif %}
                            </fieldset>
                        </div>
                    </div>
                </div>

                <!-- 配方步骤 -->
                <div class="mb-4 border border-base-300 bg-base-200 rounded-lg">
                    <div class="p-4 flex justify-between items-center cursor-pointer"
                        :class="{ 'border-b border-base-300': sections.steps }"
                        @click="sections.steps = !sections.steps">
                        <h2 class="text-xl font-medium">
                            配方步骤
                            <span class="text-base font-normal opacity-50">（选填）</span>
                        </h2>
                        <svg class="w-6 h-6 transform transition-transform" 
                            :class="{ 'rotate-180': sections.steps }"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    <div class="p-4" x-show="sections.steps" x-transition>
                        <div class="form-group">
                            <div class="flex items-center gap-2">
                                <label class="label cursor-pointer justify-start gap-4">
                                    <input type="checkbox" class="toggle toggle-primary" 
                                        x-model="stepsEnabled"
                                        @change="$dispatch('input', $el.checked)">
                                    <span class="label-text">记录详细步骤</span>
                                </label>
                            </div>


                            <template x-if="stepsEnabled">
                                <div class="mt-4 space-y-4">
                                    <ul class="list bg-base-100 rounded-box shadow-md">
                                        
                                        <template x-for="(step, index) in steps" :key="index">
                                            <li class="list-row items-start">
                                                <div class="text-4xl font-thin opacity-30 tabular-nums text-center" 
                                                    x-text="index + 1"></div>
                                                
                                                <div class="list-col-grow space-y-2">
                                                    <textarea x-model="step.text" 
                                                        :name="'steps[' + index + '][text]'"
                                                        class="textarea w-full bg-base-200" 
                                                        placeholder="输入步骤描述"></textarea>
                                                    
                                                    <div class="flex items-center gap-1">
                                                        <label class="cursor-pointer label justify-start gap-2">
                                                            <input type="checkbox" 
                                                                x-model="step.hasTimer"
                                                                :name="'steps[' + index + '][has_timer]'"
                                                                class="checkbox checkbox-sm checkbox-primary">
                                                            <span class="label-text">计时</span>
                                                        </label>
                                                        
                                                        <template x-if="step.hasTimer">
                                                            <div class="flex items-center gap-1">
                                                                <input type="number" 
                                                                    x-model="step.minutes"
                                                                    :name="'steps[' + index + '][minutes]'"
                                                                    class="input w-auto bg-base-200"
                                                                    min="0" max="59" placeholder="分">
                                                                <span>:</span>
                                                                <input type="number"
                                                                    x-model="step.seconds"
                                                                    :name="'steps[' + index + '][seconds]'"
                                                                    class="input w-auto bg-base-200"
                                                                    min="0" max="59" placeholder="秒">
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                                
                                                <div class="flex flex-col gap-2">
                                                    <button type="button" class="btn btn-ghost btn-square btn-sm" 
                                                        @click="moveStep(index, -1)"
                                                        :disabled="index === 0">
                                                        <span class="icon-[mdi--arrow-up]"></span>
                                                    </button>
                                                    <button type="button" class="btn btn-ghost btn-square btn-sm" 
                                                        @click="moveStep(index, 1)"
                                                        :disabled="index === steps.length - 1">
                                                        <span class="icon-[mdi--arrow-down]"></span>
                                                    </button>
                                                    <button type="button" class="btn btn-ghost btn-square btn-sm text-error hover:text-error" 
                                                        @click="removeStep(index)">
                                                        <span class="icon-[material-symbols--delete]"></span>
                                                    </button>
                                                </div>
                                            </li>
                                        </template>
                                    </ul>

                                    <button type="button" 
                                        class="btn btn-outline btn-primary w-full" 
                                        @click="addStep">
                                        添加步骤
                                    </button>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 品鉴笔记 -->
                <div class="mb-4 border border-base-300 bg-base-200 rounded-lg">
                    <div class="p-4 flex justify-between items-center cursor-pointer"
                        :class="{ 'border-b border-base-300': sections.flavor_notes }"
                        @click="sections.flavor_notes = !sections.flavor_notes">
                        <h2 class="text-xl font-medium">
                            品鉴笔记
                            <span class="text-base font-normal opacity-50">（选填）</span>
                        </h2>
                        <svg class="w-6 h-6 transform transition-transform" 
                            :class="{ 'rotate-180': sections.flavor_notes }"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    <div class="p-4" x-show="sections.flavor_notes" x-transition>
                        <fieldset class="fieldset w-full">
                            <legend class="fieldset-legend text-base font-normal">我尝到了哪些风味？</legend>
                            <div class="form-group relative">
                                <div class="flex flex-wrap gap-2 p-2 rounded-lg mb-2 text-base" id="selected-tags">
                                    <!-- 预渲染的标签 -->
                                    {% for tag in record.flavor_tags.all %}
                                    <div class="bg-primary/10 text-primary px-2 py-1 rounded-lg flex items-center gap-2">
                                        <span>{{ tag.name }}</span>
                                        <button type="button" class="hover:text-error" data-tag-id="{{ tag.id }}">×</button>
                                    </div>
                                    {% endfor %}
                                </div>
                                <div class="relative">
                                    <input type="text" 
                                        class="input"
                                        data-tags-url="{% url 'get_flavor_tags' %}"
                                        data-initial-tags="{{ initial_flavor_tags_json|safe }}"
                                        placeholder="输入标签，按回车添加">
                                    <ul id="tags-dropdown" class="menu bg-base-200 rounded-box shadow-lg absolute w-full z-50 max-h-[200px] overflow-y-auto hidden">
                                        <!-- 下拉选项会在这里显示 -->
                                    </ul>
                                </div>
                                <input type="hidden" name="flavor-tags-data" id="flavor-tags-data">
                            </div>
                        </fieldset>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <fieldset class="fieldset w-full">
                                <legend class="fieldset-legend text-base font-normal">香气</legend>
                                <div class="w-full px-2">
                                    <input type="range" name="aroma" min="0" max="5" 
                                        value="{{ record.aroma|default:0 }}" class="range w-full" step="1" />
                                    <div class="w-full flex justify-between text-xs mt-1">
                                        <span>-</span>
                                        <span>1</span>
                                        <span>2</span>
                                        <span>3</span>
                                        <span>4</span>
                                        <span>5</span>
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset class="fieldset w-full">
                                <legend class="fieldset-legend text-base font-normal">酸质</legend>
                                <div class="w-full px-2">
                                    <input type="range" name="acidity" min="0" max="5" 
                                        value="{{ record.acidity|default:0 }}" class="range w-full" step="1" />
                                    <div class="w-full flex justify-between text-xs mt-1">
                                        <span>-</span>
                                        <span>1</span>
                                        <span>2</span>
                                        <span>3</span>
                                        <span>4</span>
                                        <span>5</span>
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset class="fieldset w-full">
                                <legend class="fieldset-legend text-base font-normal">醇厚</legend>
                                <div class="w-full px-2">
                                    <input type="range" name="body" min="0" max="5" 
                                        value="{{ record.body|default:0 }}" class="range w-full" step="1" />
                                    <div class="w-full flex justify-between text-xs mt-1">
                                        <span>-</span>
                                        <span>1</span>
                                        <span>2</span>
                                        <span>3</span>
                                        <span>4</span>
                                        <span>5</span>
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset class="fieldset w-full">
                                <legend class="fieldset-legend text-base font-normal">甜度</legend>
                                <div class="w-full px-2">
                                    <input type="range" name="sweetness" min="0" max="5" 
                                        value="{{ record.sweetness|default:0 }}" class="range w-full" step="1" />
                                    <div class="w-full flex justify-between text-xs mt-1">
                                        <span>-</span>
                                        <span>1</span>
                                        <span>2</span>
                                        <span>3</span>
                                        <span>4</span>
                                        <span>5</span>
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset class="fieldset w-full">
                                <legend class="fieldset-legend text-base font-normal">余韵</legend>
                                <div class="w-full px-2">
                                    <input type="range" name="aftertaste" min="0" max="5" 
                                        value="{{ record.aftertaste|default:0 }}" class="range w-full" step="1" />
                                    <div class="w-full flex justify-between text-xs mt-1">
                                        <span>-</span>
                                        <span>1</span>
                                        <span>2</span>
                                        <span>3</span>
                                        <span>4</span>
                                        <span>5</span>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}