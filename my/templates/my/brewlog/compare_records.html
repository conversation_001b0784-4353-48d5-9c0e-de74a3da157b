{% extends "my/brewlog_base.html" %}
{% load static %}
{% load calculation_filters time_filters format_filters user_filters %}

{% block title %}对比冲煮记录{% endblock %}

{% block content %}
<div class="w-screen bg-base-200/90 mb-2 text-base-content sticky top-0 z-30 justify-center backdrop-blur-sm transition-shadow duration-100 [transform:translate3d(0,0,0)]">
    <div class="navbar w-full max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div class="navbar-start lg:ml-4">
            <a href="{% url 'brewlog' %}" 
               class="btn btn-ghost"
               onclick="localStorage.removeItem('compareState')">
                <span class="icon-[ep--back]"></span></span>返回
            </a>
        </div>
        <div class="navbar-center">
            <span>对比详情</span>
        </div>
        <div class="navbar-end">
        </div>
    </div>
</div>

<div class="container mx-auto xl:max-w-7xl px-4 mb-8">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
        <!-- Pro 记录 -->
        <div class="grid shrink-0 gap-6">
            <div class="card col-span-3 col-start-1 row-start-1 flex flex-col border border-dashed border-base-content/10 will-change-auto motion-reduce:!transform-none" style="transform:translateX(0%)">
            {% include "my/partials/compare_record_details.html" with record=pro_record other_record=con_record highlight_color_light="#def4ff" highlight_color_dark="#142339" %}
            </div>
        </div>
        
        <!-- Con 记录 -->
        <div class="grid shrink-0 gap-6">
            <div class="card col-span-3 col-start-1 row-start-1 flex flex-col border border-dashed border-base-content/10 will-change-auto motion-reduce:!transform-none" style="transform:translateX(0%)">
            {% include "my/partials/compare_record_details.html" with record=con_record other_record=pro_record highlight_color_light="#fff1e6" highlight_color_dark="#2b1e1c" %}
            </div>
        </div>
    </div>
</div>
{% endblock %} 