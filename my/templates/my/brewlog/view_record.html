{% extends "my/brewlog_base.html" %}
{% load static %}
{% load calculation_filters time_filters format_filters %}

{% block title %}{{ record.created_at|date:"Y-m-d" }} 我喝了什么咖啡{% endblock %}

{% block extra_css %}
<style>
[x-cloak] { display: none !important; }
.trend-tooltip {
    position: absolute;
    background: var(--b1);
    border: 1px solid var(--b3);
    padding: 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    pointer-events: none;
    z-index: 50;
    max-width: 300px;
}
.tooltip:before {
    white-space: pre-line;
}
</style>
{% endblock %}

{% block extra_js %}
<script defer type="text/javascript" src="{% static 'js/blvwrcrd.js' %}"></script>
<script defer type="text/javascript" src="{% static 'js/blvwsht.js' %}"></script>
{% endblock %}

{% block content %}
{% if history_data %}
    <script id="history-data" type="application/json">
        {{ history_data|safe }}
    </script>
{% endif %}

<div class="w-screen bg-base-200/90 mb-2 text-base-content sticky top-0 z-30 justify-center backdrop-blur-sm transition-shadow duration-100 [transform:translate3d(0,0,0)]">
    <div class="navbar w-full max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div class="navbar-start lg:ml-4">
            <a href="{% url 'brewlog' %}" class="btn btn-ghost">
                <span class="icon-[uiw--left] text-lg"></span>
                <span class="hidden md:block">返回</span>
            </a>
        </div>
        <div class="navbar-center" x-data="{ showRecipeName: false }" @scroll.window="showRecipeName = window.scrollY > 100">
            <span x-show="!showRecipeName">冲煮详情</span>
            <span x-show="showRecipeName" x-cloak class="truncate max-w-[200px] md:max-w-[300px]">
                {% if record.recipe_name %}
                    {{ record.recipe_name }}
                {% else %}
                    无名配方
                {% endif %}
            </span>
        </div>
        <div class="navbar-end lg:gap-2">
            <button id="shareBtn" class="btn btn-ghost">
                <span class="icon-[material-symbols--screenshot] text-lg"></span>
                <span class="hidden md:block">长截图</span>
            </button>
            <a href="{% url 'edit_record_page' record.id %}" class="btn btn-ghost">
                <span class="icon-[material-symbols--edit-square-outline-rounded] text-lg"></span>
                <span class="hidden md:block">修改</span>
            </a>
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
    <div class="mx-auto xl:max-w-3xl p-4 text-base-content rounded-lg mt-2">
        <div class="flex flex-col">
            <h1 class="text-2xl font-bold mb-4">
                {% if record.recipe_name %}
                    {{ record.recipe_name }}
                {% else %}
                    <span class="opacity-80">无名配方</span>
                {% endif %}
            </h1>

            <div class="text-sm mb-4 flex items-center gap-2 flex-wrap">
                <span>
                    {% if user.first_name %}{{ user.first_name }}{% else %}{{ user }}{% endif %}
                    <span class="opacity-50">记于 {{ record.created_at|cn_datetime }}</span>
                </span>

                {% if record.room_temperature or record.room_humidity %}
                    <span class="opacity-30">·</span>
                    <span class="flex items-center gap-2">
                        {% if record.room_temperature %}
                            <span class="text-error/80">{{ record.room_temperature|format_number }}ºc</span>
                        {% endif %}
                        {% if record.room_humidity %}
                            {% if record.room_temperature %}
                                <span class="opacity-30">·</span>
                            {% endif %}
                            <span class="text-info/80">{{ record.room_humidity }}%rh</span>
                        {% endif %}
                    </span>
                {% endif %}
            </div>
        </div>
        <div class="flex flex-wrap gap-2 items-center">
            <div class="badge badge-ghost badge-lg gap-2">
                {% with equip=record.brewing_equipment.get_brew_method_display %}
                {% if equip == '意式' %}<span class="icon-[tabler--coffee]"></span>
                {% elif equip == '手冲' %}<span class="icon-[mdi--kettle-pour-over]"></span>
                {% elif equip == '爱乐压' %}<span class="icon-[mdi--alpha-a]"></span>
                {% elif equip == '冷萃' %}<span class="icon-[mdi--snowflake]"></span>
                {% elif equip == '摩卡壶' %}<span class="icon-[game-icons--moka-pot]"></span>
                {% elif equip == '法压壶' %}<span class="icon-[openmoji--french-press]"></span>
                {% elif equip == '自动滴滤' %}<span class="icon-[mdi--coffee-maker-outline]"></span>
                {% endif %}
                {% endwith %}
                {{ record.brewing_equipment.get_brew_method_display }}
            </div>
            <div class="badge badge-ghost badge-lg gap-2"><span class="icon-[iconamoon--clock-light]"></span> {{ record.brewing_time|format_brewing_time }}</div>
            <div class="badge badge-ghost badge-lg gap-2"><span class="icon-[iconoir--percentage-circle]"></span> 1:{{ record.yield_weight|divide:record.dose_weight|format_ratio }}</div>
        </div>
        <div class="my-4">
            <div class="chat chat-start">
            <div class="chat-image avatar">
                <div class="w-7 rounded-full">
                <span class="text-2xl">
                {{ record.get_rating_level_display }}
                </span>
                </div>
            </div>
            {% if record.notes %}
            <div class="chat-bubble break-all">{{ record.notes|linebreaks }}</div>
            {% else %}
            <div class="chat-bubble chat-bubble-accent">
            {% with rating=record.rating_level|default:1 %}
                {% if rating == 1 %}浪费了
                {% elif rating == 2 %}差劲
                {% elif rating == 3 %}不满意
                {% elif rating == 4 %}失望
                {% elif rating == 5 %}普通
                {% elif rating == 6 %}及格
                {% elif rating == 7 %}不错
                {% elif rating == 8 %}很好！
                {% elif rating == 9 %}出色！
                {% elif rating == 10 %}完美！
                {% endif %}
            {% endwith %}
            </div>
            {% endif %}
            </div>
        </div>
        <div class="grid grid-cols-3 gap-4">
            <div class="card bg-base-100 w-auto">
                <figure class="text-5xl px-4 pt-4">
                    <span class="icon-[openmoji--coffee-grinder] mr-2"></span>
                </figure>
                <div class="card-body items-center text-center p-2">
                    <h2 class="card-title">{{ record.grind_size }}</h2>
                </div>
            </div>
            <div class="card bg-base-100 w-auto indicator">
                <figure class="text-5xl px-4 pt-4">
                    <span class="icon-[openmoji--roasted-coffee-bean]"></span>
                </figure>
                <div class="card-body items-center text-center p-2">
                    <h2 class="card-title">{{ record.dose_weight|format_number }}g</h2>
                </div>
            </div>
            <div class="card bg-base-100 w-auto">
                <figure class="text-5xl px-4 pt-4">
                    <span class="icon-[openmoji--droplet]"></span>
                </figure>
                <div class="card-body items-center text-center p-2">
                    <h2 class="card-title">{{ record.yield_weight|format_number }}g</h2>
                    {% if record.water_temperature %}
                    <p>{{ record.water_temperature|format_number }}°C</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="flex flex-col gap-2 py-4 text-sm">
            <div class="flex gap-3">
                <p class="opacity-60 w-12">用豆</p>
                <p class="flex-1">{{ record.coffee_bean.roaster }} {{ record.coffee_bean.name }}{% if record.coffee_bean.is_deleted %}<span class="badge badge-sm badge-error ml-1">已删除</span>{% endif %}{% if record.coffee_bean.type == '跳过' %}{% else %}，{% with level=record.coffee_bean.roast_level %}{% if level == 1 %}极浅烘{% elif level == 2 %}浅烘{% elif level == 3 %}浅中烘{% elif level == 4 %}中烘{% elif level == 5 %}中深烘{% elif level == 6 %}深烘{% elif level == 7 %}极深烘{% endif %}{% endwith %}{{ record.coffee_bean.process }}{{ record.coffee_bean.variety }}
                {% if record.coffee_bean.origin or record.coffee_bean.region or record.coffee_bean.finca %}（{{ record.coffee_bean.origin }}{{ record.coffee_bean.region }}{{ record.coffee_bean.finca }}）{% endif %}
                {% endif %}</p>
            </div>
            {% if record.water_quality %}
            <div class="flex gap-3">
                <p class="opacity-60 w-12">用水</p>
                <p class="flex-1">{{ record.water_quality }}</p>
            </div>
            {% endif %}
            <div class="flex gap-3">
                <p class="opacity-60 w-12">器具</p>
                <p class="flex-1">
                    {% with brewing_brand=record.brewing_equipment.brand %}
                        {% if brewing_brand in record.brewing_equipment.name %}
                            {{ record.brewing_equipment.name }}{% if record.brewing_equipment.is_deleted %}<span class="badge badge-sm badge-error ml-1">已删除</span>{% endif %}
                        {% else %}
                            {{ brewing_brand }} {{ record.brewing_equipment.name }}{% if record.brewing_equipment.is_deleted %}<span class="badge badge-sm badge-error ml-1">已删除</span>{% endif %}
                        {% endif %}
                    {% endwith %}
                    +
                    {% with grinding_brand=record.grinding_equipment.brand %}
                        {% if grinding_brand in record.grinding_equipment.name %}
                            {{ record.grinding_equipment.name }}{% if record.grinding_equipment.is_deleted %}<span class="badge badge-sm badge-error ml-1">已删除</span>{% endif %}
                        {% else %}
                            {{ grinding_brand }} {{ record.grinding_equipment.name }}{% if record.grinding_equipment.is_deleted %}<span class="badge badge-sm badge-error ml-1">已删除</span>{% endif %}
                        {% endif %}
                    {% endwith %}
                </p>
            </div>
            {% comment %}优先显示 gadget_kit 中的小工具，其次显示直接关联的小工具{% endcomment %}
            {% if record.gadget_kit and record.gadget_kit.gadget_components.all %}
            <div class="flex gap-3">
                <p class="opacity-60 w-12">小物</p>
                <div class="flex-1">
                    <div class="flex flex-col md:flex-row md:flex-wrap gap-y-1 md:gap-x-1">
                        {% for gadget in record.gadget_kit.gadget_components.all %}
                            <div class="flex items-center">
                                {% with gadget_brand=gadget.brand %}
                                    {% if gadget_brand in gadget.name %}
                                        {{ gadget }}{% if gadget.is_deleted %}<span class="badge badge-sm badge-error ml-1">已删除</span>{% endif %}
                                    {% else %}
                                        {{ gadget_brand }} {{ gadget }}{% if gadget.is_deleted %}<span class="badge badge-sm badge-error ml-1">已删除</span>{% endif %}
                                    {% endif %}
                                {% endwith %}
                                {% if not forloop.last %}<span class="hidden md:inline">,&nbsp;</span>{% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% elif record.gadgets.all %}
            <div class="flex gap-3">
                <p class="opacity-60 w-12">小物</p>
                <div class="flex-1">
                    <div class="flex flex-col md:flex-row md:flex-wrap gap-y-1 md:gap-x-1">
                        {% for gadget in record.gadgets.all %}
                            <div class="flex items-center">
                                {% with gadget_brand=gadget.brand %}
                                    {% if gadget_brand in gadget.name %}
                                        {{ gadget }}{% if gadget.is_deleted %}<span class="badge badge-sm badge-error ml-1">已删除</span>{% endif %}
                                    {% else %}
                                        {{ gadget_brand }} {{ gadget }}{% if gadget.is_deleted %}<span class="badge badge-sm badge-error ml-1">已删除</span>{% endif %}
                                    {% endif %}
                                {% endwith %}
                                {% if not forloop.last %}<span class="hidden md:inline">,&nbsp;</span>{% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        {% if record.steps %}
        <div class="divider menu-title max-w-md mx-auto mt-8">详细步骤</div>
        <!-- 原始步骤列表 -->
        <div class="mt-4">
            <ul class="list">

                {% for step in record.steps %}
                    <li class="list-row items-start">
                        <div class="text-4xl font-thin opacity-30 tabular-nums text-center">{{ forloop.counter }}</div>

                        <div class="list-col-grow space-y-2">
                            <div class="whitespace-normal break-words">{{ step.text|highlight_measurements|safe|linebreaks }}</div>
                            {% if step.timer %}
                            <span class="opacity-50">⏱ {{ step.timer }}</span>
                            {% endif %}
                        </div>
                    </li>
                {% endfor %}
            </ul>
        </div>
        <div class="mt-4 flex justify-center">
            <div x-data="stepGuide">
                <button @click="open()" class="btn btn-outline btn-info btn-sm gap-2">
                    <span class="icon-[material-symbols--directions] text-lg"></span>
                    步骤指引
                </button>

                <!-- 步骤指引全屏模态框 -->
                <div x-show="showGuide"
                        x-cloak
                        class="fixed inset-0 bg-base-100 text-base-content z-50 flex flex-col">

                    <!-- 顶部导航栏 -->
                    <div class="w-full bg-base-200 p-4 flex justify-between items-center">
                        <h2 class="text-xl font-bold">步骤指引</h2>
                        <button @click="close()" class="btn btn-ghost">
                            <span class="icon-[tabler--logout] text-xl"></span>退出指引
                        </button>
                    </div>

                    <!-- 主要内容区域 -->
                    <div class="flex-1 flex flex-col min-h-0">
                        <!-- 步骤卡片区域（自适应高度） -->
                        <div class="flex-none p-4">
                            <div class="w-full max-w-2xl mx-auto">
                                {% for step in record.steps %}
                                    <div x-show="currentStep === {{ forloop.counter0 }}"
                                            x-cloak
                                            class="card bg-neutral text-neutral-content w-auto text-center shadow-md">
                                        <div class="card-body items-center text-center">
                                            <h2 class="card-title">步骤 {{ forloop.counter }}</h2>
                                            <div class="text-2xl leading-relaxed">{{ step.text|highlight_measurements|safe|linebreaks }}</div>
                                            <div class="card-actions gap-2">
                                                {% if not forloop.first %}
                                                <button @click="prev()"
                                                        class="lg:btn lg:btn-ghost lg:btn-md text-lg">
                                                    ⏪
                                                </button>
                                                {% endif %}

                                                {% if step.timer %}
                                                <div x-data="stepTimer('{{ step.timer }}')"
                                                        data-step="{{ forloop.counter0 }}"
                                                        class="flex items-center gap-2">
                                                    <span>⏱</span>
                                                    <span class="countdown font-mono text-lg">
                                                        <span x-bind:style="'--value:' + formatTime().minutes"></span>:
                                                        <span x-bind:style="'--value:' + formatTime().seconds"></span>
                                                    </span>
                                                    <button @click="isRunning ? pauseTimer() : startTimer()"
                                                            class="lg:btn lg:btn-ghost lg:btn-md text-lg"
                                                            x-text="isRunning ? '⏸️' : '▶️'">
                                                    </button>
                                                    <button @click="resetTimer()"
                                                            class="lg:btn lg:btn-ghost lg:btn-md text-lg"
                                                            x-text="isCompleted ? '🔄' : '⏹'">
                                                    </button>
                                                </div>
                                                {% endif %}

                                                {% if not forloop.last %}
                                                <button @click="next()"
                                                        class="lg:btn lg:btn-ghost lg:btn-md text-lg"
                                                        :disabled="isCompleted">
                                                    ⏩
                                                </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- 步骤列表区域（可滚动） -->
                        <div class="flex-1 overflow-y-auto p-4">
                            <div class="max-w-2xl mx-auto">
                                <ul class="steps steps-vertical w-full" x-ref="stepsList">
                                    {% for step in record.steps %}
                                        <li class="step cursor-pointer"
                                            :class="{ 'step-primary': currentStep >= {{ forloop.counter0 }} }"
                                            @click="goToStep({{ forloop.counter0 }})"
                                            :id="'step-{{ forloop.counter0 }}'">
                                            <div class="text-left text-sm">{{ step.text|linebreaks }}</div>
                                        </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 品鉴笔记展示 -->
        {% if record.flavor_tags.exists or record.aroma or record.acidity or record.sweetness or record.aftertaste or record.body %}
        <div class="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 pb-4">
            <div class="divider menu-title max-w-md mx-auto my-8">品鉴笔记</div>

            <div class="flex flex-col items-center gap-4">
                {% if record.flavor_tags.exists %}
                <div class="flex flex-col gap-2">
                    <div class="flex flex-wrap gap-2">
                        {% for tag in record.flavor_tags.all %}
                        <div class="badge badge-lg badge-outline badge-secondary">{{ tag.name }}</div>
                        {% endfor %}
                        {% with overlap=record.calculate_tag_overlap %}
                            {% if overlap is not None %}
                            <div class="badge badge-lg badge-outline gap-2 tooltip" data-tip="与咖啡豆风味标签的重叠度&#10;帮助您了解是否与预期风味相符">
                                <div class="inline-flex items-center gap-2">
                                    <span class="icon-[ph--intersect-duotone]"></span>
                                    <span class="leading-none">{{ overlap }}%</span>
                                </div>
                            </div>
                            {% endif %}
                        {% endwith %}
                    </div>
                </div>
                {% endif %}
                {% if record.aroma or record.acidity or record.sweetness or record.aftertaste or record.body %}
                <div class="w-auto h-64">
                    <canvas id="dimensionChart"
                        data-values='[{{ record.aroma }}, {{ record.acidity }}, {{ record.sweetness }}, {{ record.aftertaste }}, {{ record.body }}]'>
                    </canvas>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

    </div>
</div>

<!-- 趋势分析图表 -->
{% if history_data %}
    <div class="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div class="mx-auto xl:max-w-3xl">
            <div class="divider menu-title max-w-md mx-auto mt-8">趋势分析</div>
            <div class="h-[300px] md:h-[400px]">
                <canvas id="trendChart"></canvas>
            </div>
            <div class="alert bg-base-100 text-sm opacity-50 my-8"><span class="icon-[material-symbols--info-outline]"></span> <div>由于水温和液重的总数值较高，为了便于观察变化，我们对这部分数据的折线图进行了比例缩放。因此，像90度的水温在参数值坐标轴上呈现为9。</div></div>
        </div>
    </div>
{% elif record.recipe_name %}
{% endif %}

<div class="hidden">
    <span class="text-base-content dark:text-base-100 bg-light-highlight dark:bg-dark-highlight">g</span>
</div>
{% endblock %}