{% extends "my/brewlog_base.html" %}
{% load static %}

{% block title %}新手引导 - 咖啡札记{% endblock %}

{% block content %}
<div class="container mx-auto max-w-4xl px-4 py-8" 
     x-data="onboarding"
     x-init="init()">
  <span class="text-xs opacity-60">为了更好地为你服务，在开始记录前，请先回答几个简单问题，我们会自动为你准备所需数据。</span>
  <!-- 进度条 -->
  <progress 
    class="progress progress-primary w-full mb-8 mt-2" 
    :value="currentStep"
    max="4">
  </progress>

  <!-- 表单 -->
  <form id="onboardingForm" class="mx-auto max-w-lg">
    {% csrf_token %}
    
    <!-- 步骤卡片 -->
    <div>
      <!-- 步骤1：咖啡豆名称 -->
      <div class="card transition-opacity duration-300"
           x-cloak
           x-show.immediate="currentStep === 1">
        <div class="card-body items-center">
          <h2 class="card-title">你正在喝哪款咖啡豆？</h2>
          <fieldset class="fieldset">
            <input 
              type="text" 
              x-model="formData.beanName"
              class="input" 
              placeholder="例如：埃塞水洗"
              @keydown.enter.prevent
              :required="currentStep === 1">
          </fieldset>
          <div class="card-actions justify-around">
            <button type="button" class="btn btn-primary" @click="nextStep()">继续</button>
          </div>
        </div>
      </div>

      <!-- 步骤2：烘焙来源 -->
      <div class="card transition-opacity duration-300"
           x-cloak
           x-show.immediate="currentStep === 2">
        <div class="card-body items-center">
          <h2 class="card-title">买的是生豆还是熟豆？</h2>
          <div class="my-6 grid divide-y divide-base-300 overflow-hidden rounded-box border border-base-300 text-xs w-auto">
            <div>
              <label class="flex w-full cursor-pointer items-start p-6 capitalize hover:bg-base-200 relative"
                     :class="{'before:absolute before:inset-0 before:border-2 before:border-primary before:pointer-events-none before:rounded-t-box': formData.roasterType === 'commercial'}">
                <input type="radio" 
                       name="roasterType" 
                       x-model="formData.roasterType"
                       value="commercial"
                       class="hidden"
                       :required="currentStep === 2">
                <div class="flex items-start gap-4">
                  <span class="icon-[tabler--paper-bag] text-2xl flex-shrink-0 mt-0.5"></span>
                  <div class="text-left">
                    <h3 class="font-bold">从烘焙商处购买熟豆</h3>
                    <p class="text-base-content/60">省时又省心</p>
                  </div>
                </div>
              </label>
            </div>
            <div>
              <label class="flex w-full cursor-pointer items-start p-6 capitalize hover:bg-base-200 relative"
                     :class="{'before:absolute before:inset-0 before:border-2 before:border-primary before:pointer-events-none before:rounded-b-box': formData.roasterType === 'home'}">
                <input type="radio"
                       name="roasterType"
                       x-model="formData.roasterType"
                       value="home"
                       class="hidden"
                       :required="currentStep === 2">
                <div class="flex items-start gap-4">
                  <span class="icon-[streamline--food-barbeque-pot-cook-grill-bbq-drink-cooking-nutrition-pot-barbecue-grilling-food-cauldron] text-2xl flex-shrink-0 mt-0.5"></span>
                  <div class="text-left">
                    <h3 class="font-bold">自己选购生豆</h3>
                    <p class="text-base-content/60">享受烘焙的乐趣</p>
                  </div>
                </div>
              </label>
            </div>
          </div>
          <div class="card-actions w-full justify-around">
            <button type="button" class="btn btn-ghost" @click="prevStep()">返回</button>
            <button type="button" class="btn btn-primary" @click="nextStep()">继续</button>
          </div>
        </div>
      </div>

      <!-- 步骤3：冲煮方式 -->
      <div class="card transition-opacity duration-300"
           x-cloak
           x-show.immediate="currentStep === 3">
        <div class="card-body items-center">
          <h2 class="card-title">你平常用哪种方式制作咖啡？</h2>
          <div class="my-6 grid divide-y divide-base-300 overflow-hidden rounded-box border border-base-300 text-xs w-auto">
            <template x-for="(method, index) in brewMethods" :key="method.value">
              <div>
                <label class="flex w-full cursor-pointer p-6 capitalize hover:bg-base-200 relative"
                       :class="{'before:absolute before:inset-0 before:border-2 before:border-primary before:pointer-events-none': formData.brewMethod === method.value,
                               'before:rounded-t-box': index === 0 && formData.brewMethod === method.value,
                               'before:rounded-b-box': index === brewMethods.length - 1 && formData.brewMethod === method.value}">
                  <input type="radio"
                         name="brewMethod"
                         x-model="formData.brewMethod"
                         :value="method.value"
                         class="hidden"
                         :required="currentStep === 3">
                  <div class="flex items-start gap-4">
                    <span :class="method.icon + ' text-2xl flex-shrink-0 mt-0.5'"></span>
                    <div class="text-left">
                      <h3 class="font-bold" x-text="method.label"></h3>
                      <p class="text-base-content/60" x-text="method.description"></p>
                    </div>
                  </div>
                </label>
              </div>
            </template>
          </div>
          <div class="card-actions w-full justify-around">
            <button type="button" class="btn btn-ghost" @click="prevStep()">返回</button>
            <button type="button" class="btn btn-primary" @click="nextStep()">继续</button>
          </div>
        </div>
      </div>

      <!-- 步骤4：磨豆机 -->
      <div class="card transition-opacity duration-300"
           x-cloak
           x-show.immediate="currentStep === 4">
        <div class="card-body items-center">
          <h2 class="card-title">你有专用磨豆机吗？</h2>
          <div class="my-6 grid gap-px divide-y divide-base-300 overflow-hidden rounded-box border border-base-300 text-xs w-auto">
            <div>
              <label class="flex w-full cursor-pointer items-start p-6 capitalize hover:bg-base-200 relative"
                     :class="{'before:absolute before:inset-0 before:border-2 before:border-primary before:pointer-events-none before:rounded-t-box': formData.grinderType === 'dedicated'}">
                <input type="radio"
                       name="grinderType"
                       x-model="formData.grinderType"
                       value="dedicated"
                       class="hidden"
                       :required="currentStep === 4">
                <div class="flex items-center gap-4">
                  <span class="icon-[openmoji--coffee-grinder] text-2xl flex-shrink-0"></span>
                  <div class="text-left">
                    <h3 class="font-bold">有专用磨豆机</h3>
                    <p class="text-base-content/60">可以自由升级研磨体验</p>
                  </div>
                </div>
              </label>
            </div>
            <div>
              <label class="flex w-full cursor-pointer items-start p-6 capitalize hover:bg-base-200 relative"
                     :class="{'before:absolute before:inset-0 before:border-2 before:border-primary before:pointer-events-none': formData.grinderType === 'built_in'}">
                <input type="radio"
                       name="grinderType"
                       x-model="formData.grinderType"
                       value="built_in"
                       class="hidden"
                       :required="currentStep === 4">
                <div class="flex items-center gap-4">
                  <span class="icon-[openmoji--espresso-machine] text-2xl"></span>
                  <div class="text-left">
                    <h3 class="font-bold">咖啡机自带研磨功能</h3>
                    <p class="text-base-content/60">一体式设计更便捷</p>
                  </div>
                </div>
              </label>
            </div>
            <div>
              <label class="flex w-full cursor-pointer items-start p-6 capitalize hover:bg-base-200 relative"
                     :class="{'before:absolute before:inset-0 before:border-2 before:border-primary before:pointer-events-none before:rounded-b-box': formData.grinderType === 'store'}">
                <input type="radio"
                       name="grinderType"
                       x-model="formData.grinderType"
                       value="store"
                       class="hidden"
                       :required="currentStep === 4">
                <div class="flex items-center gap-4">
                  <span class="icon-[flat-color-icons--shop] text-2xl"></span>
                  <div class="text-left">
                    <h3 class="font-bold">商家代磨</h3>
                    <p class="text-base-content/60">购买时请店家代为研磨</p>
                  </div>
                </div>
              </label>
            </div>
          </div>
          <div class="card-actions w-full justify-around">
            <button type="button" class="btn btn-ghost" @click="prevStep()">返回</button>
            <button type="button" 
                    class="btn btn-primary" 
                    @click="submitForm()"
                    :disabled="isSubmitting">
              <span x-show="!isSubmitting">开始记录</span>
              <span x-show="isSubmitting" class="loading loading-spinner"></span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </form>

  <!-- 错误提示 -->
  <div class="toast toast-top toast-center z-50" x-show="errorMessage" x-transition>
    <div class="alert alert-error">
      <span class="icon-[material-symbols--error] text-2xl"></span>
      <span x-text="errorMessage"></span>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('alpine:init', () => {
  Alpine.data('onboarding', () => ({
    currentStep: 1,
    totalSteps: 4,
    isSubmitting: false,
    errorMessage: '',
    formData: {
      beanName: '',
      roasterType: '',
      brewMethod: '',
      grinderType: ''
    },
    brewMethods: [
      {
        value: 'ESPRESSO',
        label: '意式浓缩',
        description: '使用意式咖啡机萃取',
        icon: 'icon-[tabler--coffee]'
      },
      {
        value: 'POUR_OVER',
        label: '手冲咖啡',
        description: '手动注水冲煮',
        icon: 'icon-[mdi--kettle-pour-over]'
      },
      {
        value: 'COLD_BREW',
        label: '冷萃咖啡',
        description: '冷水浸泡萃取',
        icon: 'icon-[mdi--snowflake]'
      },
      {
        value: 'AEROPRESS',
        label: '爱乐压',
        description: '便携的压力萃取',
        icon: 'icon-[mdi--alpha-a]'
      },
      {
        value: 'MOKA_POT',
        label: '摩卡壶',
        description: '经典的意大利煮法',
        icon: 'icon-[game-icons--moka-pot]'
      },
      {
        value: 'FRENCH_PRESS',
        label: '法压壶',
        description: '浸泡式冲煮方法',
        icon: 'icon-[openmoji--french-press]'
      },
      {
        value: 'AUTO_DRIP',
        label: '自动滴滤',
        description: '使用美式咖啡机',
        icon: 'icon-[mdi--coffee-maker-outline]'
      }
    ],
    init() {
      // 初始化逻辑
    },
    nextStep() {
      if (this.validateCurrentStep()) {
        this.currentStep++
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }
    },
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep--
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }
    },
    validateCurrentStep() {
      this.errorMessage = ''
      switch(this.currentStep) {
        case 1:
          if (!this.formData.beanName.trim()) {
            this.errorMessage = '请输入咖啡豆名称'
            return false
          }
          break
        case 2:
          if (!this.formData.roasterType) {
            this.errorMessage = '请选择咖啡豆来源'
            return false
          }
          break
        case 3:
          if (!this.formData.brewMethod) {
            this.errorMessage = '请选择冲煮方式'
            return false
          }
          break
        case 4:
          if (!this.formData.grinderType) {
            this.errorMessage = '请选择磨豆机类型'
            return false
          }
          break
      }
      return true
    },
    async submitForm() {
      if (!this.validateCurrentStep()) return

      this.isSubmitting = true
      this.errorMessage = ''

      try {
        const response = await fetch('{% url "onboarding_submit" %}', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('input[name="csrfmiddlewaretoken"]').value
          },
          body: JSON.stringify(this.formData)
        })

        if (!response.ok) {
          throw new Error('提交失败')
        }

        const result = await response.json()
        if (result.success) {
          window.location.href = '{% url "add_record_page" %}'
        } else {
          throw new Error(result.message || '提交失败')
        }
      } catch (error) {
        this.errorMessage = error.message
      } finally {
        this.isSubmitting = false
      }
    }
  }))
})
</script>
{% endblock %} 