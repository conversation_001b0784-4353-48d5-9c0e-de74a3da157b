from django import template

register = template.Library()

@register.filter
def divide(value, arg):
    # 添加空值检查
    if value is None or arg is None:
        return 0
    try:
        return float(value) / float(arg)
    except (ValueError, ZeroDivisionError):
        return 0

@register.filter
def multiply(value, arg):
    """将值乘以参数，支持 timedelta 类型"""
    try:
        if hasattr(value, 'total_seconds'):  # 检查是否是 timedelta 对象
            return int(value.total_seconds() / 60)  # 转换为分钟数
        return float(value) * float(arg)
    except (ValueError, TypeError, AttributeError):
        return 0

@register.filter
def subtract_from(value, arg):
    """从arg中减去value"""
    try:
        return float(arg) - float(value)
    except (ValueError, TypeError):
        return value