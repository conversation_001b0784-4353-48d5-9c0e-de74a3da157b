from django import template
import json
import re
from my.models import Equipment

register = template.Library()

@register.filter
def format_ratio(value):
    """格式化粉水比，保留一位小数，末尾为0时省略"""
    if not value and value != 0:
        return "未设置"
        
    try:
        num = float(value)
        # 四舍五入到一位小数
        rounded_value = round(num, 1)
        
        # 如果是整数，直接返回整数格式
        if rounded_value.is_integer():
            return str(int(rounded_value))
        
        # 返回格式化字符串，去掉末尾的0
        return f"{rounded_value:.1f}".rstrip('0').rstrip('.')
    except (ValueError, TypeError):
        return "未设置"

@register.filter
def format_number(value):
    """格式化数字，去掉不必要的小数位"""
    if not value and value != 0:
        return "未设置"
        
    try:
        # 转换为浮点数
        num = float(value)
        # 如果是整数，直接返回整数格式
        if num.is_integer():
            return str(int(num))
        # 处理小数，去掉末尾的0
        formatted_value = f"{num:.2f}".rstrip('0').rstrip('.')
        return formatted_value
    except (ValueError, TypeError):
        return "未设置"

@register.filter
def get_tag_value(tag_name):
    try:
        # 清理字符串，确保它是有效的JSON
        cleaned = re.sub(r'\n', '', tag_name)  # 移除换行符
        cleaned = cleaned.strip()  # 移除首尾空白
        if not cleaned.startswith('['):
            cleaned = '[' + cleaned
        if not cleaned.endswith(']'):
            cleaned = cleaned + ']'
        
        # 解析JSON
        data = json.loads(cleaned)
        if isinstance(data, list) and len(data) > 0:
            return data[0].get('value', '')
        return tag_name
    except:
        # 如果JSON解析失败，尝试直接提取value值
        match = re.search(r'"value":"([^"]+)"', tag_name)
        if match:
            return match.group(1)
        return tag_name

@register.filter
def get_rating_group(rating):
    if rating is None:
        return 0
    elif rating > 8:
        return 5
    elif rating > 6:
        return 4
    elif rating > 4:
        return 3
    elif rating > 2:
        return 2
    else:
        return 1

@register.filter
def get_rating_hearts(rating_group):
    if rating_group == 0:
        return '未评分'
    return '♥' * rating_group

@register.filter
def get_brew_method_display(value):
    """将冲煮方法代码转换为显示名称"""
    if not value:
        return ''
    return dict(Equipment.BREW_METHODS).get(value, value)

@register.filter
def count_steps(steps_json):
    try:
        if isinstance(steps_json, list):  # 如果已经是列表，直接使用
            return f"有{len(steps_json)}个步骤"
        
        if isinstance(steps_json, str):  # 如果是字符串，尝试解析
            # 先尝试直接解析
            try:
                steps = json.loads(steps_json)
                return f"有{len(steps)}个步骤"
            except:
                # 如果直接解析失败，尝试替换单引号
                steps = json.loads(steps_json.replace("'", '"'))
                return f"有{len(steps)}个步骤"
        
        steps = json.loads(steps_json.replace("'", '"'))
        return f"有{len(steps)}个步骤"
    except:
        if hasattr(steps_json, '__len__'):  # 如果对象有长度，直接使用
            return f"有{len(steps_json)}个步骤"
        return "有步骤"

@register.filter(is_safe=True)
def highlight_measurements(text):
    """Highlight measurements in text with badge-neutral class."""
    patterns = [
        # 容积单位模式，优先于时间单位
        r'(\d+(?:\.\d+)?)(?:[-~～至到]?\s*(\d+(?:\.\d+)?))?\s*(?:升|盎司|毫升|ml|L|oz)',
        # Time patterns, including ranges like 10~15秒
        r'(\d+(?:\.\d+)?)(?:[-~～至到]?\s*(\d+(?:\.\d+)?))?\s*(?:秒钟|秒|分钟|分|小时|天|s|min|m|hrs|h)',
        # Weight patterns, including ranges and connectors
        r'(\d+(?:\.\d+)?)(?:[-~～至到]?\s*(\d+(?:\.\d+)?))?\s*(?:克|g|G)',
        # Temperature patterns, including ranges and connectors
        r'(\d+(?:\.\d+)?)(?:[-~～至到]?\s*(\d+(?:\.\d+)?))?\s*(?:度|摄氏度|°C|℃)',
        # Pressure patterns, including ranges and connectors
        r'(\d+(?:\.\d+)?)(?:[-~～至到]?\s*(\d+(?:\.\d+)?))?\s*(?:bar|Bar|BAR|巴)'
    ]
    
    for pattern in patterns:
        text = re.sub(
            pattern,
            r'<span class="inline text-base-content dark:text-base-100 bg-light-highlight dark:bg-dark-highlight">\g<0></span>',
            text,
            flags=re.IGNORECASE
        )
    
    return text

@register.filter
def get_item(lst, index):
    try:
        return lst[index]
    except (IndexError, TypeError):
        return None

@register.filter
def replace(value, arg):
    """
    用于替换字符串中的特定字符
    用法: {{ value|replace:"old,new" }}
    """
    if value is None:
        return ''
    old, new = arg.split(',')
    return value.replace(old, new)

@register.filter
def get_attribute(obj, attr):
    """
    获取对象的属性值
    用法: {{ object|get_attribute:"attribute_name" }}
    """
    try:
        return getattr(obj, attr)
    except (AttributeError, TypeError):
        return None

@register.filter
def highlight_flavor_tags(tasted_tags, bean_tags):
    """
    高亮显示重叠的风味标签
    用法: {{ tasted_tags|highlight_flavor_tags:bean_tags }}
    """
    if not tasted_tags or not bean_tags:
        return ""
        
    # 将 bean_tags 转换为名称集合
    bean_tag_names = set(tag.name for tag in bean_tags.all())
    
    # 处理每个品尝标签
    highlighted_tags = []
    for tag in tasted_tags:
        if tag in bean_tag_names:
            highlighted_tags.append(f'<span class="text-error">{tag}</span>')
        else:
            highlighted_tags.append(tag)
    
    return "、".join(highlighted_tags)