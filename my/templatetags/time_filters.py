from django import template
from datetime import datetime, timedelta
from django.utils import timezone
from zoneinfo import ZoneInfo
from django.utils.timezone import localtime

register = template.Library()

@register.filter
def format_brewing_time(value):
    if not value:
        return "未设置"
        
    if not isinstance(value, timedelta):
        return "未设置"
    
    total_seconds = int(value.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60
    
    # 构建可读性强的时间格式
    result = []
    if hours > 0:
        result.append(f"{hours}小时")
    if minutes > 0:
        # 当只有分钟时使用"分钟"，否则使用"分"
        if hours == 0 and seconds == 0:
            result.append(f"{minutes}分钟")
        else:
            result.append(f"{minutes}分")
    if seconds > 0:
        result.append(f"{seconds}秒")
    
    # 如果没有任何时间单位，返回0秒
    if not result:
        return "0秒"
    
    return "".join(result)

@register.filter
def format_time_ago(value):
    if not value:
        return "未设置"
    
    now = timezone.now()
    
    # 获取日期部分，忽略时分秒
    value_date = value.date()
    now_date = now.date()
    
    # 计算日期差异（天数）
    date_diff = (now_date - value_date).days
    
    if date_diff == 0:  # 同一天
        # 如果是同一天，再计算具体的时分秒
        diff = now - value
        seconds = diff.seconds
        
        if seconds < 60:
            return f"{seconds}秒前"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes}分钟前"
        else:
            hours = seconds // 3600
            return f"{hours}小时前"
    elif date_diff > 0 and date_diff < 7:  # 7天内的过去日期
        return f"{date_diff}天前"
    elif date_diff < 0 and date_diff > -7:  # 7天内的未来日期
        return f"{abs(date_diff)}天后"
    else:  # 超过7天
        return value.strftime("%Y-%m-%d %H:%M")

@register.filter
def days_until_now(value):
    """
    计算给定日期到当前时间的天数差
    """
    if not value:
        return 0
        
    # 确保value是aware datetime并转换到上海时区
    if timezone.is_naive(value):
        value = timezone.make_aware(value)
    value = value.astimezone(ZoneInfo('Asia/Shanghai'))
    
    # 获取当前时间(使用上海时区)
    now = timezone.now().astimezone(ZoneInfo('Asia/Shanghai'))
    
    # 转换为日期进行比较
    value_date = value.date()
    now_date = now.date()
    
    # 计算天数差
    delta = now_date - value_date
    
    return delta.days

@register.filter
def days_since(value):
    if not value:
        return 0
    return (timezone.now().date() - value).days

@register.filter
def precise_timedelta(delta):
    """
    计算时间差的人性化描述
    """
    if not delta:
        return ''
    
    # 计算各个时间单位
    total_seconds = abs(delta.total_seconds())
    total_days = delta.days
    
    # 计算年和剩余天数（使用365天作为一年）
    years = total_days // 365
    days = total_days % 365
    
    hours = int(total_seconds // 3600) % 24
    minutes = int((total_seconds % 3600) // 60)
    seconds = int(total_seconds % 60)
    
    # 构建人性化的时间差描述
    if years > 0:
        if days > 0:
            return f"{years}年{days}天"
        return f"{years}年"
    elif days > 0:
        if hours > 0:
            return f"{days}天{hours}小时"
        return f"{days}天"
    elif hours > 0:
        if minutes > 0:
            return f"{hours}小时{minutes}分钟"
        return f"{hours}小时"
    elif minutes > 0:
        if seconds > 0:
            return f"{minutes}分钟{seconds}秒"
        return f"{minutes}分钟"
    else:
        return f"{seconds}秒"

@register.filter
def duration_hours(timedelta):
    if not timedelta:
        return "00"
    return f"{int(timedelta.total_seconds() // 3600):02d}"

@register.filter
def duration_minutes(timedelta):
    if not timedelta:
        return "00"
    total_seconds = int(timedelta.total_seconds())
    return f"{(total_seconds % 3600) // 60:02d}"

@register.filter
def duration_seconds(timedelta):
    if not timedelta:
        return "00"
    return f"{int(timedelta.total_seconds() % 60):02d}"

@register.filter
def days_ago_value(days):
    """
    格式化天数显示的数值部分
    0 = 今天
    1 = 昨天
    2及以上 = X
    """
    if not isinstance(days, int):
        return '-'
        
    if days == 0:
        return '今天'
    elif days == 1:
        return '昨天'
    else:
        return str(days)

@register.filter
def days_ago_unit(days):
    """
    格式化天数显示的单位部分
    0或1 = 空字符串
    2及以上 = 天前
    """
    if not isinstance(days, int):
        return ''
        
    if days < 2:
        return ''
    else:
        return '天前'

@register.filter
def subtract_datetime(dt1, dt2):
    """计算两个日期时间的差值"""
    if not dt1 or not dt2:
        return None
    return dt1 - dt2

@register.filter
def time_ago(value):
    """
    将时间转换为"多久之前"的格式
    """
    now = timezone.now()
    diff = now - value

    if diff < timedelta(minutes=1):
        return '刚刚'
    elif diff < timedelta(hours=1):
        minutes = int(diff.total_seconds() / 60)
        return f'{minutes} 分钟前'
    elif diff < timedelta(days=1):
        hours = int(diff.total_seconds() / 3600)
        return f'{hours} 小时前'
    elif diff < timedelta(days=30):
        days = diff.days
        return f'{days} 天前'
    elif diff < timedelta(days=365):
        months = int(diff.days / 30)
        return f'{months} 个月前'
    else:
        years = int(diff.days / 365)
        return f'{years} 年前'

@register.filter
def timedelta_from_days(days):
    """
    将天数转换为timedelta对象
    """
    if not isinstance(days, (int, float)):
        return timedelta()
    return timedelta(days=days)

@register.filter
def format_peak_time(time_str):
    """
    格式化时间显示
    
    Args:
        time_str: 时间字符串 (格式: "HH:00") 或整数小时
    
    Returns:
        str: 格式化后的时间字符串 (格式: "早上7点")
    """
    if not time_str:
        return None
        
    # 如果输入是整数，转换为时间字符串格式
    if isinstance(time_str, int):
        hour = time_str
    else:
        # 从时间字符串中提取小时数
        hour = int(time_str.split(':')[0])
    
    # 定义时间段
    if 0 <= hour < 6:
        period = '凌晨'
    elif 6 <= hour < 12:
        period = '早上'
    elif 12 <= hour < 18:
        period = '下午'
    else:
        period = '晚上'
    
    # 转换为12小时制
    display_hour = hour if hour <= 12 else hour - 12
    
    return f"{period}{display_hour}点"

@register.filter
def cn_datetime(value):
    """将日期时间转换为中文格式，使用更详细的时间段划分"""
    if not value:
        return ''
    
    # 确保时间是 timezone-aware 的
    if timezone.is_naive(value):
        value = timezone.make_aware(value)
    dt = timezone.localtime(value)
    
    # 获取当前时间
    now = timezone.now()
    
    weekdays = ['一','二','三','四','五','六','日']
    weekday = weekdays[dt.weekday()]
    
    hour = dt.hour
    if 0 <= hour < 2:
        am_pm = '午夜'
    elif 2 <= hour < 4:
        am_pm = '凌晨'
    elif 4 <= hour < 6:
        am_pm = '黎明'
    elif 6 <= hour < 8:
        am_pm = '早晨'
    elif 8 <= hour < 11:
        am_pm = '上午'
    elif 11 <= hour < 13:
        am_pm = '中午'
        if hour > 12:
            hour -= 12
    elif 13 <= hour < 17:
        am_pm = '下午'
        hour -= 12
    elif hour == 17:
        am_pm = '傍晚'
        hour -= 12
    elif 18 <= hour < 23:
        am_pm = '晚上'
        hour -= 12
    else:  # 23点
        am_pm = '深夜'
        hour -= 12
    
    # 如果是当前年份，则不显示年份
    date_format = f"{dt.month}月{dt.day}日 周{weekday} {am_pm}{hour}:{dt.minute:02d}"
    if dt.year != now.year:
        date_format = f"{dt.year}年{date_format}"
    
    return date_format