from django import template
from django.core.cache import cache
from django.utils import timezone
from django_ratelimit.core import get_usage
from django.http import HttpRequest
from zoneinfo import ZoneInfo

register = template.Library()

@register.filter
def has_export_limit(user):
    if not user.is_authenticated:
        return False
        
    # 检查缓存中的导出时间限制
    cache_key = f'export_limit_{user.id}'
    last_export_time = cache.get(cache_key)
    
    if last_export_time:
        # 获取上次导出时间和当前时间（使用Asia/Shanghai时区）
        tz = ZoneInfo('Asia/Shanghai')
        last_export_date = last_export_time.astimezone(tz).date()
        current_date = timezone.now().astimezone(tz).date()
        
        # 如果是同一天，则限制导出
        if last_export_date == current_date:
            return True
    
    # 创建一个模拟的请求对象
    request = HttpRequest()
    request.user = user
    
    # 检查用户的请求频率限制
    usage = get_usage(request, group='export', key='user', rate='1/d')
    if usage.get('count', 0) >= usage.get('limit', 1):
        return True
    
    return False