# Generated by Django 4.2.7 on 2025-03-09 19:47

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0037_beanoccurrence_rest_period_max_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='blendcomponent',
            name='altitude_max',
            field=models.IntegerField(blank=True, help_text='单位:米', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(9999)], verbose_name='最高海拔'),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='altitude_min',
            field=models.IntegerField(blank=True, help_text='单位:米', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(9999)], verbose_name='最低海拔'),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='altitude_single',
            field=models.IntegerField(blank=True, help_text='单位:米', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(9999)], verbose_name='种植海拔'),
        ),
        migrations.AlterField(
            model_name='coffeebean',
            name='altitude_max',
            field=models.IntegerField(blank=True, help_text='单位:米', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(9999)], verbose_name='最高海拔'),
        ),
        migrations.AlterField(
            model_name='coffeebean',
            name='altitude_min',
            field=models.IntegerField(blank=True, help_text='单位:米', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(9999)], verbose_name='最低海拔'),
        ),
        migrations.AlterField(
            model_name='coffeebean',
            name='altitude_single',
            field=models.IntegerField(blank=True, help_text='单位:米', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(9999)], verbose_name='种植海拔'),
        ),
    ]
