# Generated by Django 4.2.20 on 2025-03-30 20:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('my', '0039_brewingrecord_room_humidity_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='RecipeTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='标签名')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '配方标签',
                'verbose_name_plural': '配方标签',
                'unique_together': {('name', 'user')},
            },
        ),
        migrations.CreateModel(
            name='RecipeThread',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recipe_name', models.CharField(max_length=50, verbose_name='配方名称')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('use_count', models.IntegerField(default=0, verbose_name='使用次数')),
                ('last_used_at', models.DateTimeField(blank=True, null=True)),
                ('tags', models.ManyToManyField(blank=True, related_name='recipe_threads', to='my.recipetag')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '配方主题',
                'verbose_name_plural': '配方主题',
                'ordering': ['-last_used_at'],
                'unique_together': {('user', 'recipe_name')},
            },
        ),
    ]
