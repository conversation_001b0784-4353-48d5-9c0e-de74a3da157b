from django.db import migrations, models
from django.utils import timezone

def set_created_at(apps, schema_editor):
    CoffeeBean = apps.get_model('my', 'CoffeeBean')
    for bean in CoffeeBean.objects.all():
        # 如果有 roast_date，使用 roast_date
        if bean.roast_date:
            bean.created_at = timezone.make_aware(
                timezone.datetime.combine(bean.roast_date, timezone.datetime.min.time())
            )
        else:
            # 否则使用当前时间
            bean.created_at = timezone.now()
        bean.save()

class Migration(migrations.Migration):

    dependencies = [
        ('my', '0004_brewingrecord_gadgets_equipment_notes_and_more'),  # 替换为实际的前一个迁移
    ]

    operations = [
        migrations.AddField(
            model_name='coffeebean',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间', null=True),
        ),
        migrations.RunPython(set_created_at),
        migrations.AlterField(
            model_name='coffeebean',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
    ]