# Generated by Django 4.2.7 on 2024-12-15 15:22

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0012_blendcomponent'),
    ]

    operations = [
        migrations.AlterField(
            model_name='blendcomponent',
            name='altitude',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='blend_ratio',
            field=models.DecimalField(decimal_places=2, default=100.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='finca',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='order',
            field=models.IntegerField(default=0),
        ),
        migrations.Alter<PERSON>ield(
            model_name='blendcomponent',
            name='origin',
            field=models.Char<PERSON>ield(blank=True, max_length=100),
        ),
        migrations.Alter<PERSON>ield(
            model_name='blendcomponent',
            name='process',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='region',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='roast_level',
            field=models.IntegerField(default=4, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(7)]),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='variety',
            field=models.CharField(blank=True, max_length=100),
        ),
    ]
