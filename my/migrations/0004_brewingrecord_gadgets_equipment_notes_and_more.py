# Generated by Django 4.2.7 on 2024-12-06 10:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0003_coffeebean_equipment_brewingrecord_flavortag_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='brewingrecord',
            name='gadgets',
            field=models.ManyToManyField(blank=True, limit_choices_to={'type': 'GADGET'}, related_name='brewing_records_as_gadget', to='my.equipment', verbose_name='小工具'),
        ),
        migrations.AddField(
            model_name='equipment',
            name='notes',
            field=models.TextField(blank=True, max_length=500, verbose_name='备注'),
        ),
        migrations.AlterField(
            model_name='equipment',
            name='type',
            field=models.CharField(choices=[('GRINDER', '磨豆机'), ('BREWER', '冲煮器具'), ('GADGET', '小工具')], max_length=10),
        ),
    ]
