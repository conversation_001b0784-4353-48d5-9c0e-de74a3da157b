# Generated by Django 4.2.7 on 2024-07-16 10:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import my.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contenttypes', '0002_remove_content_type_name'),
        ('my', '0001_initial'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='favorite',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='favorite',
            name='content_type',
            field=models.ForeignKey(default=my.models.get_default_content_type, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='favorite',
            name='object_id',
            field=models.PositiveIntegerField(default=my.models.get_default_object_id),
        ),
        migrations.AlterUniqueTogether(
            name='favorite',
            unique_together={('user', 'content_type', 'object_id')},
        ),
        migrations.RemoveField(
            model_name='favorite',
            name='article',
        ),
    ]
