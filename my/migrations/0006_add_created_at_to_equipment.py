from django.db import migrations, models
from django.utils import timezone

def set_created_at(apps, schema_editor):
    Equipment = apps.get_model('my', 'Equipment')
    for equipment in Equipment.objects.all():
        equipment.created_at = timezone.now()
        equipment.save()

class Migration(migrations.Migration):

    dependencies = [
        ('my', '0005_add_created_at_to_coffeebean'),
    ]

    operations = [
        migrations.AddField(
            model_name='equipment',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=timezone.now, verbose_name='创建时间'),
            preserve_default=False,
        ),
        migrations.RunPython(set_created_at),
    ]  