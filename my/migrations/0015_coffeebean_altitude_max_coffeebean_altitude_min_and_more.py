# Generated by Django 4.2.7 on 2024-12-15 18:30

import django.core.validators
from django.db import migrations, models

def migrate_altitude_data(apps, schema_editor):
    CoffeeBean = apps.get_model('my', 'CoffeeBean')
    for bean in CoffeeBean.objects.all():
        if bean.altitude:
            # 尝试解析现有的海拔数据
            altitude_text = bean.altitude.lower().replace('m', '').replace('米', '').strip()
            try:
                if '-' in altitude_text:
                    # 处理范围格式
                    min_alt, max_alt = map(int, altitude_text.split('-'))
                    bean.altitude_type = 'RANGE'
                    bean.altitude_min = min_alt
                    bean.altitude_max = max_alt
                else:
                    # 处理单一值格式
                    single_alt = int(altitude_text)
                    bean.altitude_type = 'SINGLE'
                    bean.altitude_single = single_alt
                bean.save()
            except (ValueError, TypeError):
                # 如果解析失败，保持原样
                pass

class Migration(migrations.Migration):

    dependencies = [
        ('my', '0014_coffeebean_purchase_price'),  # 替换为实际的前置迁移
    ]

    operations = [
        migrations.AddField(
            model_name='coffeebean',
            name='altitude_type',
            field=models.CharField(
                max_length=10,
                choices=[('SINGLE', '单一海拔'), ('RANGE', '海拔区间')],
                default='SINGLE',
                verbose_name='海拔类型'
            ),
        ),
        migrations.AddField(
            model_name='coffeebean',
            name='altitude_single',
            field=models.IntegerField(
                null=True,
                blank=True,
                verbose_name='种植海拔',
                help_text='单位:米',
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(3000)
                ]
            ),
        ),
        migrations.AddField(
            model_name='coffeebean',
            name='altitude_min',
            field=models.IntegerField(
                null=True,
                blank=True,
                verbose_name='最低海拔',
                help_text='单位:米',
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(3000)
                ]
            ),
        ),
        migrations.AddField(
            model_name='coffeebean',
            name='altitude_max',
            field=models.IntegerField(
                null=True,
                blank=True,
                verbose_name='最高海拔',
                help_text='单位:米',
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(3000)
                ]
            ),
        ),
        migrations.RunPython(migrate_altitude_data),
    ]