# Generated by Django 4.2.20 on 2025-05-11 15:31

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0041_coffeebean_initial_rest_period_max_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='beanoccurrence',
            name='purchase_price',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='购买价格'),
        ),
        migrations.AlterField(
            model_name='coffeebean',
            name='initial_purchase_price',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='初始购买价格'),
        ),
        migrations.AlterField(
            model_name='coffeebean',
            name='purchase_price',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='购买价格'),
        ),
        migrations.AlterField(
            model_name='equipment',
            name='purchase_price',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='购买价格'),
        ),
    ]
