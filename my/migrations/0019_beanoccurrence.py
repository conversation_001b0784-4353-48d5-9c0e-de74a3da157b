# Generated by Django 4.2.7 on 2024-12-18 20:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0018_alter_coffeebean_roast_level'),
    ]

    operations = [
        migrations.CreateModel(
            name='BeanOccurrence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bag_weight', models.DecimalField(blank=True, decimal_places=1, max_digits=6, null=True, verbose_name='包装重量(g)')),
                ('bag_remain', models.DecimalField(blank=True, decimal_places=1, max_digits=6, null=True, verbose_name='剩余重量(g)')),
                ('purchase_price', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True, verbose_name='购买价格')),
                ('roast_date', models.DateField(blank=True, null=True, verbose_name='烘焙日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='购买时间')),
                ('coffee_bean', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='occurrences', to='my.coffeebean')),
            ],
            options={
                'verbose_name': '咖啡豆回购记录',
                'verbose_name_plural': '咖啡豆回购记录',
                'ordering': ['-created_at'],
            },
        ),
    ]
