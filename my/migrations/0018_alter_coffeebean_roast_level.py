# Generated by Django 4.2.7 on 2024-12-18 14:53

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0017_alter_blendcomponent_options_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='coffeebean',
            name='roast_level',
            field=models.IntegerField(choices=[(1, '极浅烘'), (2, '浅烘'), (3, '浅中烘'), (4, '中烘'), (5, '中深烘'), (6, '深烘'), (7, '极深烘')], default=4, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(7)], verbose_name='烘焙程度'),
        ),
    ]
