# Generated by Django 4.2.7 on 2024-12-17 16:34

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0016_brewingrecord_recipe_name'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='blendcomponent',
            options={'verbose_name': '拼配组件', 'verbose_name_plural': '拼配组件'},
        ),
        migrations.RemoveField(
            model_name='blendcomponent',
            name='altitude',
        ),
        migrations.AddField(
            model_name='blendcomponent',
            name='altitude_max',
            field=models.IntegerField(blank=True, help_text='单位:米', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(3000)], verbose_name='最高海拔'),
        ),
        migrations.AddField(
            model_name='blendcomponent',
            name='altitude_min',
            field=models.IntegerField(blank=True, help_text='单位:米', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(3000)], verbose_name='最低海拔'),
        ),
        migrations.AddField(
            model_name='blendcomponent',
            name='altitude_single',
            field=models.IntegerField(blank=True, help_text='单位:米', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(3000)], verbose_name='种植海拔'),
        ),
        migrations.AddField(
            model_name='blendcomponent',
            name='altitude_type',
            field=models.CharField(choices=[('SINGLE', '单一海拔'), ('RANGE', '海拔范围')], default='SINGLE', max_length=10, verbose_name='海拔类型'),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='blend_ratio',
            field=models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='拼配比例'),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='finca',
            field=models.CharField(blank=True, max_length=100, verbose_name='庄园'),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='order',
            field=models.IntegerField(default=0, verbose_name='排序'),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='origin',
            field=models.CharField(blank=True, max_length=100, verbose_name='产地'),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='process',
            field=models.CharField(blank=True, max_length=100, verbose_name='处理法'),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='region',
            field=models.CharField(blank=True, max_length=100, verbose_name='产区'),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='roast_level',
            field=models.IntegerField(default=4, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(7)], verbose_name='烘焙程度'),
        ),
        migrations.AlterField(
            model_name='blendcomponent',
            name='variety',
            field=models.CharField(blank=True, max_length=100, verbose_name='品种'),
        ),
    ]
