# Generated by Django 4.2.7 on 2025-03-08 11:48

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0036_brewingrecord_acidity_brewingrecord_aftertaste_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='beanoccurrence',
            name='rest_period_max',
            field=models.IntegerField(blank=True, help_text='单位:天', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(60)], verbose_name='最长养豆期'),
        ),
        migrations.AddField(
            model_name='beanoccurrence',
            name='rest_period_min',
            field=models.IntegerField(blank=True, help_text='单位:天', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(60)], verbose_name='最短养豆期'),
        ),
        migrations.AddField(
            model_name='coffeebean',
            name='rest_period_max',
            field=models.IntegerField(blank=True, help_text='单位:天', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(60)], verbose_name='最长养豆期'),
        ),
        migrations.AddField(
            model_name='coffeebean',
            name='rest_period_min',
            field=models.IntegerField(blank=True, help_text='单位:天', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(60)], verbose_name='最短养豆期'),
        ),
    ]
