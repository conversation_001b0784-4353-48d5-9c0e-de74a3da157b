# Generated by Django 4.2.7 on 2025-01-24 09:58

from django.db import migrations, models
from django.utils import timezone

def copy_created_at_to_initial(apps, schema_editor):
    CoffeeBean = apps.get_model('my', 'CoffeeBean')
    for bean in CoffeeBean.objects.all():
        if not bean.initial_created_at:
            bean.initial_created_at = bean.created_at
            bean.save()

class Migration(migrations.Migration):

    dependencies = [
        ('my', '0031_alter_equipment_grind_size_preset'),
    ]

    operations = [
        migrations.AddField(
            model_name='coffeebean',
            name='initial_created_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='初始购买时间'),
        ),
        migrations.RunPython(copy_created_at_to_initial),
    ]