# Generated by Django 4.2.7 on 2024-12-21 09:24

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0022_alter_beanoccurrence_bag_remain_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='coffeebean',
            name='bag_remain',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='库存余量'),
        ),
        migrations.AlterField(
            model_name='coffeebean',
            name='bag_weight',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='包装规格'),
        ),
    ]
