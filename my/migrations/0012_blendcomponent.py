# Generated by Django 4.2.7 on 2024-12-15 11:24

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0011_coffeequote_content_original'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlendComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('origin', models.CharField(blank=True, max_length=50)),
                ('region', models.CharField(blank=True, max_length=50)),
                ('finca', models.CharField(blank=True, max_length=50)),
                ('variety', models.CharField(blank=True, max_length=50)),
                ('altitude', models.CharField(blank=True, max_length=50)),
                ('process', models.CharField(blank=True, max_length=50)),
                ('roast_level', models.IntegerField(choices=[(1, '极浅烘'), (2, '浅烘'), (3, '浅中烘'), (4, '中烘'), (5, '中深烘'), (6, '深烘'), (7, '极深烘')], default=4, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(6)])),
                ('blend_ratio', models.DecimalField(decimal_places=2, help_text='拼配比例（百分比）', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('order', models.PositiveIntegerField(default=0)),
                ('coffee_bean', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blend_components', to='my.coffeebean')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
    ]
