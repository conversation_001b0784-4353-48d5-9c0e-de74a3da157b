# Generated by Django 4.2.7 on 2025-02-28 11:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0035_alter_brewingrecord_gadget_kit'),
    ]

    operations = [
        migrations.AddField(
            model_name='brewingrecord',
            name='acidity',
            field=models.IntegerField(choices=[(0, '未评分'), (1, '1分'), (2, '2分'), (3, '3分'), (4, '4分'), (5, '5分')], default=0, verbose_name='酸质'),
        ),
        migrations.AddField(
            model_name='brewingrecord',
            name='aftertaste',
            field=models.IntegerField(choices=[(0, '未评分'), (1, '1分'), (2, '2分'), (3, '3分'), (4, '4分'), (5, '5分')], default=0, verbose_name='余韵'),
        ),
        migrations.AddField(
            model_name='brewingrecord',
            name='aroma',
            field=models.IntegerField(choices=[(0, '未评分'), (1, '1分'), (2, '2分'), (3, '3分'), (4, '4分'), (5, '5分')], default=0, verbose_name='香气'),
        ),
        migrations.AddField(
            model_name='brewingrecord',
            name='body',
            field=models.IntegerField(choices=[(0, '未评分'), (1, '1分'), (2, '2分'), (3, '3分'), (4, '4分'), (5, '5分')], default=0, verbose_name='醇厚'),
        ),
        migrations.AddField(
            model_name='brewingrecord',
            name='flavor_tags',
            field=models.ManyToManyField(blank=True, related_name='brewing_records', to='my.flavortag', verbose_name='实际品尝风味'),
        ),
        migrations.AddField(
            model_name='brewingrecord',
            name='sweetness',
            field=models.IntegerField(choices=[(0, '未评分'), (1, '1分'), (2, '2分'), (3, '3分'), (4, '4分'), (5, '5分')], default=0, verbose_name='甜度'),
        ),
    ]
