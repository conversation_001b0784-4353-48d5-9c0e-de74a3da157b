# Generated by Django 4.2.7 on 2025-02-24 13:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0034_cleanup_gadget_kit_data'),
    ]

    operations = [
        migrations.AlterField(
            model_name='brewingrecord',
            name='gadget_kit',
            field=models.ForeignKey(blank=True, limit_choices_to=models.Q(('gadget_components__isnull', False), ('is_archived', False), ('is_deleted', False), ('type', 'GADGET_KIT'), models.Q(('brewing_records_as_gadget__isnull', False), _negated=True), models.Q(('type', 'GADGET'), _negated=True)), null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='brewing_records_as_kit', to='my.equipment', verbose_name='小工具组合'),
        ),
    ]
