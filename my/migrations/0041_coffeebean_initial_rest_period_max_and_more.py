# Generated by Django 4.2.20 on 2025-05-08 09:34

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0040_recipetag_recipethread'),
    ]

    operations = [
        migrations.AddField(
            model_name='coffeebean',
            name='initial_rest_period_max',
            field=models.IntegerField(blank=True, help_text='单位:天', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(60)], verbose_name='初始最长养豆期'),
        ),
        migrations.AddField(
            model_name='coffeebean',
            name='initial_rest_period_min',
            field=models.IntegerField(blank=True, help_text='单位:天', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(60)], verbose_name='初始最短养豆期'),
        ),
    ]
