# Generated by Django 4.2.7 on 2024-12-18 21:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0019_beanoccurrence'),
    ]

    operations = [
        migrations.AddField(
            model_name='coffeebean',
            name='initial_bag_remain',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=6, null=True, verbose_name='初始剩余重量(g)'),
        ),
        migrations.AddField(
            model_name='coffeebean',
            name='initial_bag_weight',
            field=models.DecimalField(blank=True, decimal_places=1, max_digits=6, null=True, verbose_name='初始包装重量(g)'),
        ),
        migrations.AddField(
            model_name='coffeebean',
            name='initial_purchase_price',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True, verbose_name='初始购买价格'),
        ),
        migrations.AddField(
            model_name='coffeebean',
            name='initial_roast_date',
            field=models.DateField(blank=True, null=True, verbose_name='初始烘焙日期'),
        ),
    ]
