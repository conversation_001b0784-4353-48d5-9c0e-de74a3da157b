# Generated by Django 4.2.7 on 2025-01-17 21:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0029_brewingrecord_gadget_kit'),
    ]

    operations = [
        migrations.AddField(
            model_name='equipment',
            name='grind_size_preset',
            field=models.CharField(default='-', help_text='磨豆机的预设研磨度', max_length=50, verbose_name='预设研磨度'),
        ),
        migrations.AlterField(
            model_name='equipment',
            name='brew_method',
            field=models.CharField(blank=True, choices=[('ESPRESSO', '意式'), ('POUR_OVER', '手冲'), ('AEROPRESS', '爱乐压'), ('COLD_BREW', '冷萃'), ('MOKA_POT', '摩卡壶'), ('FRENCH_PRESS', '法压壶'), ('AUTO_DRIP', '自动滴滤')], max_length=20, null=True, verbose_name='冲煮方式'),
        ),
    ]
