# Generated by Django 4.2.7 on 2025-02-23 17:37

from django.db import migrations

def cleanup_gadget_kit_data(apps, schema_editor):
    """清理小工具组合的不相关字段"""
    Equipment = apps.get_model('my', 'Equipment')
    
    # 获取所有小工具组合
    gadget_kits = Equipment.objects.filter(type='GADGET_KIT')
    
    # 记录修改数量
    updated_count = 0
    
    for kit in gadget_kits:
        has_changes = False
        
        # 检查并清除不相关字段
        if kit.brand:
            kit.brand = ''
            has_changes = True
            
        if kit.purchase_price is not None:
            kit.purchase_price = None
            has_changes = True
            
        if kit.grinder_purpose:
            kit.grinder_purpose = None
            has_changes = True
            
        if kit.grind_size_preset:
            kit.grind_size_preset = None
            has_changes = True
            
        if kit.brew_method:
            kit.brew_method = None
            has_changes = True
            
        # 如果有修改，保存记录
        if has_changes:
            kit.save()
            updated_count += 1
    
    if updated_count > 0:
        print(f"已清理 {updated_count} 个小工具组合的不相关字段")

def reverse_cleanup(apps, schema_editor):
    """
    这个迁移不需要回滚操作
    因为我们无法知道原始数据是什么
    """
    pass

class Migration(migrations.Migration):
    dependencies = [
        ('my', '0033_coffeebean_barcode'),
    ]

    operations = [
        migrations.RunPython(
            cleanup_gadget_kit_data,
            reverse_cleanup
        ),
    ] 