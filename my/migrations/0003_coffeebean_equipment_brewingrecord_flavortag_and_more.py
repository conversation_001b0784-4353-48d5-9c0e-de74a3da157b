# Generated by Django 4.2.7 on 2024-12-04 22:18

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('my', '0002_alter_favorite_unique_together_favorite_content_type_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CoffeeBean',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('SINGLE', '单品'), ('BLEND', '拼配'), ('SKIP', '跳过')], max_length=10)),
                ('roaster', models.CharField(max_length=50)),
                ('name', models.Char<PERSON>ield(max_length=50)),
                ('roast_date', models.DateField(blank=True, null=True)),
                ('roast_level', models.IntegerField(choices=[(1, '极浅烘'), (2, '浅烘'), (3, '浅中烘'), (4, '中烘'), (5, '中深烘'), (6, '深烘'), (7, '极深烘')], default=4, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(6)], verbose_name='烘焙程度')),
                ('origin', models.CharField(blank=True, max_length=50)),
                ('region', models.CharField(blank=True, max_length=50)),
                ('finca', models.CharField(blank=True, max_length=50)),
                ('variety', models.CharField(blank=True, max_length=50)),
                ('altitude', models.CharField(blank=True, max_length=50)),
                ('process', models.CharField(blank=True, max_length=50)),
                ('is_archived', models.BooleanField(default=False, verbose_name='已归档')),
                ('is_deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('is_favorite', models.BooleanField(default=False, verbose_name='首选')),
                ('bag_weight', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='包装规格')),
                ('bag_remain', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0)], verbose_name='库存余量')),
                ('is_decaf', models.BooleanField(default=False, help_text='是否为低因咖啡', verbose_name='低因咖啡')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Equipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('GRINDER', '磨豆机'), ('BREWER', '冲煮器具')], max_length=10)),
                ('name', models.CharField(max_length=50)),
                ('brand', models.CharField(blank=True, max_length=50, verbose_name='品牌')),
                ('brew_method', models.CharField(blank=True, choices=[('ESPRESSO', '意式'), ('POUR_OVER', '手冲'), ('AEROPRESS', '爱乐压'), ('COLD_BREW', '冷萃'), ('MOKA_POT', '摩卡壶')], max_length=20, null=True, verbose_name='冲煮方式')),
                ('is_archived', models.BooleanField(default=False, verbose_name='已归档')),
                ('is_deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('is_favorite', models.BooleanField(default=False, verbose_name='首选')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='BrewingRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grind_size', models.CharField(max_length=50)),
                ('dose_weight', models.DecimalField(decimal_places=2, max_digits=5)),
                ('yield_weight', models.DecimalField(decimal_places=2, max_digits=5)),
                ('water_temperature', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True)),
                ('rating_level', models.IntegerField(choices=[(1, '😫'), (2, '☹️'), (3, '🙁'), (4, '😕'), (5, '😐'), (6, '🙂'), (7, '😌'), (8, '😃'), (9, '😄'), (10, '😆')], default=6, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(10)], verbose_name='评分')),
                ('brewing_time', models.DurationField()),
                ('notes', models.TextField(blank=True, max_length=500)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='记录时间')),
                ('steps', models.JSONField(blank=True, default=list, help_text='存储格式：[{"text": "步骤描述", "timer": "MM:SS", "order": 1}, ...]', verbose_name='详细步骤')),
                ('brewing_equipment', models.ForeignKey(limit_choices_to={'type': 'BREWER'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='brewing_records', to='my.equipment')),
                ('coffee_bean', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='my.coffeebean')),
                ('grinding_equipment', models.ForeignKey(limit_choices_to={'type': 'GRINDER'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='grinding_records', to='my.equipment')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='FlavorTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('coffee_beans', models.ManyToManyField(related_name='flavor_tags', to='my.coffeebean')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('name', 'user')},
            },
        ),
        migrations.AddConstraint(
            model_name='equipment',
            constraint=models.UniqueConstraint(condition=models.Q(('is_deleted', False)), fields=('user', 'type', 'name'), name='unique_active_equipment'),
        ),
        migrations.AlterUniqueTogether(
            name='brewingrecord',
            unique_together={('user', 'brewing_equipment', 'coffee_bean', 'created_at')},
        ),
    ]
