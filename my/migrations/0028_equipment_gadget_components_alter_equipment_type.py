# Generated by Django 4.2.7 on 2025-01-07 21:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0027_alter_equipment_brew_method'),
    ]

    operations = [
        migrations.AddField(
            model_name='equipment',
            name='gadget_components',
            field=models.ManyToManyField(blank=True, limit_choices_to={'type': 'GADGET'}, related_name='kit_components', to='my.equipment', verbose_name='组合内容'),
        ),
        migrations.AlterField(
            model_name='equipment',
            name='type',
            field=models.CharField(choices=[('GRINDER', '磨豆机'), ('BREWER', '冲煮器具'), ('GADGET', '小工具'), ('GADGET_KIT', '小工具组合')], max_length=10),
        ),
    ]
