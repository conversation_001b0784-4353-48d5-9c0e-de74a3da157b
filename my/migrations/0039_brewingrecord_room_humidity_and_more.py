# Generated by Django 4.2.7 on 2025-03-11 15:35

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('my', '0038_alter_blendcomponent_altitude_max_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='brewingrecord',
            name='room_humidity',
            field=models.IntegerField(blank=True, help_text='单位：%', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='环境湿度'),
        ),
        migrations.AddField(
            model_name='brewingrecord',
            name='room_temperature',
            field=models.DecimalField(blank=True, decimal_places=1, help_text='单位：℃', max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(Decimal('-50.0')), django.core.validators.MaxValueValidator(Decimal('50.0'))], verbose_name='室温'),
        ),
        migrations.AddField(
            model_name='brewingrecord',
            name='water_quality',
            field=models.CharField(blank=True, help_text='水质或水源信息', max_length=50, null=True, verbose_name='水质'),
        ),
    ]
