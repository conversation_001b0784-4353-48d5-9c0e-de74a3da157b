const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const projectBase = "core";

const options = {
    entry: {
        main: './static_src/js/main.js',
        footer: './static_src/js/footer.js',
        toukei: './static_src/js/toukei.js',
        article: './static_src/js/article.js',
        brewlog: './static_src/js/brewlog.js',
        bldtrcrd: './static_src/js/brewlog_edit_record.js',
        bldtqpmnt: './static_src/js/brewlog_edit_equipment.js',
        bldtbn: './static_src/js/brewlog_edit_bean.js',
        blddbn: './static_src/js/brewlog_add_bean.js',
        blhtmp: './static_src/js/brewlog_heatmap.js',
        blbnccrrnc: './static_src/js/brewlog_bean_occurrence.js',
        blhdsght: './static_src/js/brewlog_hindsight.js',
        blvwrcrd: './static_src/js/brewlog_view_record.js',
        blvwsht: './static_src/js/brewlog_view_screenshot.js',
        blbnlst: './static_src/js/brewlog_bean_list.js',
        blrcplst: './static_src/js/brewlog_recipe_list.js',
        calclatte: './static_src/js/latte_calculator.js',
        isppp: './static_src/js/ios_popup.js',
        shblbt: './static_src/js/equipment_carousel.js',
        rcpsht: './static_src/js/recipe_screenshot.js',
    },
    output: {
        path: path.resolve(`./${projectBase}/static/`),
        filename: 'js/[name].js',
        publicPath: '/static/',
    },

    plugins: [
        new MiniCssExtractPlugin({
            filename: 'css/[name].css',
        }),
    ],
    module: {
        rules: [
            {
                test: /\.css$/i,
                use: [
                    MiniCssExtractPlugin.loader,
                    'css-loader',
                    'postcss-loader'
                ],
            },
            {
                test: /\.m?js$/,
                exclude: /node_modules/,
                use: {
                    loader: "babel-loader",
                }
            }
        ]
    },
    devServer: {
        hot: false,
        proxy: {
            '*': 'http://127.0.0.1:8000',
        },
    },
};

module.exports = options;