//
//  brewlogTests.swift
//  brewlogTests
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/29.
//

import Testing
@testable import brewlog

struct brewlogTests {

    @Test func example() async throws {
        // Write your test here and use APIs like `#expect(...)` to check expected conditions.
    }

    @Test func testCreateBeanAPI() async throws {
        // 测试创建咖啡豆API
        let apiClient = MockAPIClient()
        
        // 创建请求数据模拟
        let beanData: [String: Any] = [
            "rest_period_max": NSNull(),
            "bag_remain": 200,
            "bag_weight": 200,
            "altitude_single": 0,
            "roast_date": 1746085560,
            "finca": "Chelchele",
            "created_at": 1746085560,
            "is_favorite": false,
            "flavor_tags": ["花香", "草莓", "柑橘", "葡萄", "肉桂"],
            "name": "埃塞切尔切勒2025",
            "process": "日晒",
            "variety": "埃塞原生种",
            "roast_level": 2,
            "barcode": "",
            "altitude_max": 2100,
            "roaster": "KK",
            "notes": "",
            "type": "SINGLE",
            "region": "Gedeo",
            "origin": "埃塞俄比亚",
            "purchase_price": 177,
            "is_decaf": false,
            "altitude_type": "RANGE",
            "rest_period_min": 3,
            "altitude_min": 2020,
            "is_archived": false
        ]
        
        // 设置模拟响应
        let responseData: [String: Any] = [
            "id": 85,
            "name": "埃塞切尔切勒2025",
            "roaster": "KK",
            "origin": "埃塞俄比亚",
            "process": "日晒",
            "type": "SINGLE",
            "type_display": "单品",
            "roast_level": 2,
            "roast_level_display": "浅烘",
            "roast_date": 1746028800.0,
            "purchase_date": 1746085560.0,
            "price": 177.0,
            "weight": 200.0,
            "description": "",
            "notes": "",
            "rating": 0,
            "created_at": 1746085560.0,
            "updated_at": 1746085560.0,
            "is_deleted": false,
            "is_archived": false,
            "is_favorite": false,
            "is_finished": false,
            "is_decaf": false,
            "taste_notes": ["草莓", "花香", "柑橘", "葡萄", "肉桂"],
            "altitude_type": "RANGE",
            "altitude_single": NSNull(),
            "altitude_min": 2020,
            "altitude_max": 2100,
            "region": "Gedeo",
            "finca": "Chelchele",
            "variety": "埃塞原生种",
            "barcode": "",
            "rest_period_min": 3,
            "rest_period_max": NSNull(),
            "rest_period_progress": NSNull(),
            "stock_status": "充足",
            "deleted_at": NSNull(),
            "bag_remain": 200.0
        ]
        
        apiClient.mockResponse(for: "beans/create", statusCode: 201, responseData: responseData)
        
        // 执行创建咖啡豆请求
        let beanService = BeanService(apiClient: apiClient)
        let result = try await beanService.createBean(beanData)
        
        // 验证请求和响应
        #expect(apiClient.lastRequestPath == "beans/create")
        #expect(apiClient.lastRequestMethod == "POST")
        #expect(apiClient.lastRequestHeaders?["Content-Type"] == "application/json")
        #expect(apiClient.lastRequestHeaders?["Authorization"]?.starts(with: "Bearer "))
        
        // 验证创建的咖啡豆数据
        #expect(result.id == 85)
        #expect(result.name == "埃塞切尔切勒2025")
        #expect(result.roaster == "KK")
        #expect(result.origin == "埃塞俄比亚")
        #expect(result.process == "日晒")
        #expect(result.type == "SINGLE")
        #expect(result.roastLevel == 2)
        #expect(result.roastLevelDisplay == "浅烘")
        #expect(result.price == 177.0)
        #expect(result.weight == 200.0)
        #expect(result.bagRemain == 200.0)
        #expect(result.isFinished == false)
        #expect(result.isDecaf == false)
        #expect(result.isArchived == false)
        #expect(result.isFavorite == false)
        
        // 验证咖啡豆海拔信息
        #expect(result.altitudeType == "RANGE")
        #expect(result.altitudeMin == 2020)
        #expect(result.altitudeMax == 2100)
        
        // 验证咖啡豆产地信息
        #expect(result.region == "Gedeo")
        #expect(result.finca == "Chelchele")
        #expect(result.variety == "埃塞原生种")
        
        // 验证咖啡豆风味标签
        #expect(result.tasteNotes?.count == 5)
        #expect(result.tasteNotes?.contains("草莓"))
        #expect(result.tasteNotes?.contains("花香"))
        #expect(result.tasteNotes?.contains("柑橘"))
        #expect(result.tasteNotes?.contains("葡萄"))
        #expect(result.tasteNotes?.contains("肉桂"))
        
        // 验证养豆期信息
        #expect(result.restPeriodMin == 3)
        #expect(result.restPeriodMax == nil)
        #expect(result.stockStatus == "充足")
    }

    @Test func testCreateAndFetchBeanAPI() async throws {
        // 测试创建咖啡豆后获取列表
        let apiClient = MockAPIClient()
        
        // 1. 创建咖啡豆
        // 创建请求数据模拟
        let beanData: [String: Any] = [
            "rest_period_max": NSNull(),
            "bag_remain": 200,
            "bag_weight": 200,
            "altitude_single": 0,
            "roast_date": 1746085560,
            "finca": "Chelchele",
            "created_at": 1746085560,
            "is_favorite": false,
            "flavor_tags": ["花香", "草莓", "柑橘", "葡萄", "肉桂"],
            "name": "埃塞切尔切勒2025",
            "process": "日晒",
            "variety": "埃塞原生种",
            "roast_level": 2,
            "barcode": "",
            "altitude_max": 2100,
            "roaster": "KK",
            "notes": "",
            "type": "SINGLE",
            "region": "Gedeo",
            "origin": "埃塞俄比亚",
            "purchase_price": 177,
            "is_decaf": false,
            "altitude_type": "RANGE",
            "rest_period_min": 3,
            "altitude_min": 2020,
            "is_archived": false
        ]
        
        // 创建咖啡豆的响应数据
        let createResponseData: [String: Any] = [
            "id": 85,
            "name": "埃塞切尔切勒2025",
            "roaster": "KK",
            "origin": "埃塞俄比亚",
            "process": "日晒",
            "type": "SINGLE",
            "type_display": "单品",
            "roast_level": 2,
            "roast_level_display": "浅烘",
            "roast_date": 1746028800.0,
            "purchase_date": 1746085560.0,
            "price": 177.0,
            "weight": 200.0,
            "description": "",
            "notes": "",
            "rating": 0,
            "created_at": 1746085560.0,
            "updated_at": 1746085560.0,
            "is_deleted": false,
            "is_archived": false,
            "is_favorite": false,
            "is_finished": false,
            "is_decaf": false,
            "taste_notes": ["草莓", "花香", "柑橘", "葡萄", "肉桂"],
            "altitude_type": "RANGE",
            "altitude_single": NSNull(),
            "altitude_min": 2020,
            "altitude_max": 2100,
            "region": "Gedeo",
            "finca": "Chelchele",
            "variety": "埃塞原生种",
            "barcode": "",
            "rest_period_min": 3,
            "rest_period_max": NSNull(),
            "rest_period_progress": NSNull(),
            "stock_status": "充足",
            "deleted_at": NSNull(),
            "bag_remain": 200.0
        ]
        
        // 获取咖啡豆列表的响应数据
        let listResponseData: [String: Any] = [
            "count": 1,
            "next": nil,
            "previous": nil,
            "results": [
                [
                    "id": 85,
                    "name": "埃塞切尔切勒2025",
                    "roaster": "KK",
                    "origin": "埃塞俄比亚",
                    "process": "日晒",
                    "type": "SINGLE",
                    "type_display": "单品",
                    "roast_level": 2,
                    "roast_level_display": "浅烘",
                    "roast_date": 1746028800.0,
                    "purchase_date": 1746085560.0,
                    "price": 177.0,
                    "weight": 200.0,
                    "description": "",
                    "notes": "",
                    "rating": 0,
                    "created_at": 1746085560.0,
                    "updated_at": 1746085560.0,
                    "is_deleted": false,
                    "is_archived": false,
                    "is_favorite": false,
                    "is_finished": false,
                    "is_decaf": false,
                    "taste_notes": ["草莓", "花香", "柑橘", "葡萄", "肉桂"],
                    "altitude_type": "RANGE",
                    "altitude_single": NSNull(),
                    "altitude_min": 2020,
                    "altitude_max": 2100,
                    "region": "Gedeo",
                    "finca": "Chelchele",
                    "variety": "埃塞原生种",
                    "barcode": "",
                    "rest_period_min": 3,
                    "rest_period_max": NSNull(),
                    "rest_period_progress": NSNull(),
                    "stock_status": "充足",
                    "deleted_at": NSNull(),
                    "bag_remain": 200.0,
                    "occurrences_count": 0,
                    "usage_count": 0
                ]
            ]
        ]
        
        // 设置模拟响应
        apiClient.mockResponse(for: "beans/create", statusCode: 201, responseData: createResponseData)
        apiClient.mockResponse(for: "beans", statusCode: 200, responseData: listResponseData)
        
        // 创建Bean服务
        let beanService = BeanService(apiClient: apiClient)
        
        // 1. 首先创建咖啡豆
        let createdBean = try await beanService.createBean(beanData)
        
        // 验证创建的咖啡豆数据
        #expect(createdBean.id == 85)
        #expect(createdBean.name == "埃塞切尔切勒2025")
        
        // 2. 然后获取咖啡豆列表
        let beans = try await beanService.fetchBeans(forceRefresh: true)
        
        // 验证获取的咖啡豆列表
        #expect(beans.count == 1)
        #expect(beans[0].id == 85)
        #expect(beans[0].name == "埃塞切尔切勒2025")
        #expect(beans[0].roaster == "KK")
        
        // 验证两个实例是否一致
        #expect(beans[0].id == createdBean.id)
        #expect(beans[0].name == createdBean.name)
        #expect(beans[0].roaster == createdBean.roaster)
        #expect(beans[0].type == createdBean.type)
        #expect(beans[0].altitudeType == createdBean.altitudeType)
        #expect(beans[0].altitudeMin == createdBean.altitudeMin)
        #expect(beans[0].altitudeMax == createdBean.altitudeMax)
        
        // 验证API请求路径和方法
        #expect(apiClient.lastRequestPath == "beans")
        #expect(apiClient.lastRequestMethod == "GET")
        
        // 验证查询参数
        let queryItems = [
            URLQueryItem(name: "page_size", value: "20"),
            URLQueryItem(name: "force_refresh", value: "true")
        ]
        #expect(apiClient.lastRequestHeaders?["Authorization"]?.starts(with: "Bearer "))
    }

}
