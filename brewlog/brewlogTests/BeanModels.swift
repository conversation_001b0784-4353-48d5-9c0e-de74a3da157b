//
//  BeanModels.swift
//  brewlogTests
//
//  Created for testing on 2025/3/29.
//

import Foundation
@testable import brewlog

// MARK: - Bean 模型
struct Bean: Codable {
    let id: Int
    let name: String
    let roaster: String
    let origin: String?
    let process: String?
    let type: String
    let typeDisplay: String?
    let roastLevel: Int
    let roastLevelDisplay: String?
    let roastDate: Double?
    let purchaseDate: Double?
    let price: Double?
    let weight: Double?
    let description: String?
    let notes: String?
    let rating: Int?
    let createdAt: Double
    let updatedAt: Double?
    let isDeleted: Bool
    let isArchived: Bool
    let isFavorite: Bool
    let isFinished: Bool
    let isDecaf: Bool
    let tasteNotes: [String]?
    let altitudeType: String?
    let altitudeSingle: Int?
    let altitudeMin: Int?
    let altitudeMax: Int?
    let region: String?
    let finca: String?
    let variety: String?
    let barcode: String?
    let restPeriodMin: Int?
    let restPeriodMax: Int?
    let restPeriodProgress: Double?
    let stockStatus: String?
    let deletedAt: Double?
    let bagRemain: Double?
    
    // 初始包装信息（0号记录）
    let initialBagWeight: Double?
    let initialBagRemain: Double?
    let initialPurchasePrice: Double?
    let initialRoastDate: Double?
    let initialCreatedAt: Double?
    let initialRestPeriodMin: Int?
    let initialRestPeriodMax: Int?
    
    // 额外属性，用于测试
    var occurrencesCount: Int?
    var avgRating: Double?
    var usageCount: Int?
    var lastUsed: Double?
    var daysSinceLastUse: Int?
    var mostUsedEquipment: String?
    var remainingUses: Int?
}

// MARK: - APIClientProtocol
protocol APIClientProtocol {
    func request<T: Decodable>(
        _ path: String,
        method: String,
        body: Data?,
        headers: [String: String]?,
        queryItems: [URLQueryItem]?
    ) async throws -> T
    
    func requestData(
        _ path: String,
        method: String,
        body: Data?,
        headers: [String: String]?,
        queryItems: [URLQueryItem]?
    ) async throws -> (Data, Int)
}

// MARK: - BeanService
class BeanService {
    private let apiClient: APIClientProtocol
    
    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }
    
    /// 创建新的咖啡豆
    func createBean(_ beanData: [String: Any]) async throws -> Bean {
        // 序列化请求体
        let bodyData = try JSONSerialization.data(withJSONObject: beanData, options: [])
        
        // 设置请求头
        let headers = [
            "Content-Type": "application/json",
            "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ2NTg2MzYwLCJpYXQiOjE3NDYyMzUwMjAsImp0aSI6Ijg0NzhiZGY1MDIxOTQ0ODM4OGE5ZGQzNjcwYzMwYjVlIiwidXNlcl9pZCI6MywiZGV2aWNlX2lkIjoiRTg2RTExQ0UtREUzMS00RUMxLUI1Q0ItODk1N0QxNUZEQ0IxIiwidXNlcm5hbWUiOiJ3ZWJtYXN0ZXIifQ.kZW1j5F2YoBBYUjS7Rp9mRCUFUvsUmvsofHusKIkYbs"
        ]
        
        // 发送请求
        return try await apiClient.request(
            "beans/create",
            method: "POST",
            body: bodyData,
            headers: headers,
            queryItems: nil
        )
    }
    
    /// 获取咖啡豆列表
    func fetchBeans(pageSize: Int = 20, forceRefresh: Bool = false) async throws -> [Bean] {
        // 设置查询参数
        var queryItems = [URLQueryItem(name: "page_size", value: "\(pageSize)")]
        if forceRefresh {
            queryItems.append(URLQueryItem(name: "force_refresh", value: "true"))
        }
        
        // 设置请求头
        let headers = [
            "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ2NTg2MzYwLCJpYXQiOjE3NDYyMzUwMjAsImp0aSI6Ijg0NzhiZGY1MDIxOTQ0ODM4OGE5ZGQzNjcwYzMwYjVlIiwidXNlcl9pZCI6MywiZGV2aWNlX2lkIjoiRTg2RTExQ0UtREUzMS00RUMxLUI1Q0ItODk1N0QxNUZEQ0IxIiwidXNlcm5hbWUiOiJ3ZWJtYXN0ZXIifQ.kZW1j5F2YoBBYUjS7Rp9mRCUFUvsUmvsofHusKIkYbs"
        ]
        
        // 定义响应模型结构
        struct BeanListResponse: Codable {
            let count: Int
            let next: String?
            let previous: String?
            let results: [Bean]
        }
        
        // 发送请求
        let response: BeanListResponse = try await apiClient.request(
            "beans",
            method: "GET",
            body: nil,
            headers: headers,
            queryItems: queryItems
        )
        
        return response.results
    }
} 