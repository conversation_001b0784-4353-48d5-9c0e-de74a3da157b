//
//  MockAPIClient.swift
//  brewlogTests
//
//  Created for testing on 2025/3/29.
//

import Foundation
@testable import brewlog

/// 用于测试的模拟API客户端
class MockAPIClient: APIClientProtocol {
    // 存储mock响应的映射表
    private var mockResponses: [String: (statusCode: Int, data: Data)] = [:]
    
    // 记录最后一次请求的信息，用于验证
    var lastRequestPath: String?
    var lastRequestMethod: String?
    var lastRequestBody: Data?
    var lastRequestHeaders: [String: String]?
    
    /// 设置模拟响应
    func mockResponse(for path: String, statusCode: Int, responseData: [String: Any]) {
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: responseData, options: [])
            mockResponses[path] = (statusCode, jsonData)
        } catch {
            fatalError("无法序列化模拟响应数据: \(error)")
        }
    }
    
    /// 实现APIClientProtocol的请求方法
    func request<T: Decodable>(
        _ path: String,
        method: String,
        body: Data?,
        headers: [String: String]?,
        queryItems: [URLQueryItem]?
    ) async throws -> T {
        // 记录请求信息
        lastRequestPath = path
        lastRequestMethod = method
        lastRequestBody = body
        lastRequestHeaders = headers
        
        // 查找模拟响应
        guard let mockResponse = mockResponses[path] else {
            throw NSError(domain: "MockAPIClient", code: 404, userInfo: [
                NSLocalizedDescriptionKey: "未找到路径'\(path)'的模拟响应"
            ])
        }
        
        // 模拟网络延迟
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        
        // 检查状态码
        if mockResponse.statusCode >= 400 {
            throw NSError(domain: "MockAPIClient", code: mockResponse.statusCode, userInfo: [
                NSLocalizedDescriptionKey: "模拟HTTP错误: \(mockResponse.statusCode)"
            ])
        }
        
        // 解码响应数据
        do {
            let decoder = JSONDecoder()
            decoder.keyDecodingStrategy = .convertFromSnakeCase
            return try decoder.decode(T.self, from: mockResponse.data)
        } catch {
            throw NSError(domain: "MockAPIClient", code: -1, userInfo: [
                NSLocalizedDescriptionKey: "解码响应失败: \(error.localizedDescription)"
            ])
        }
    }
    
    /// 原始请求方法，返回未解码的Data
    func requestData(
        _ path: String,
        method: String,
        body: Data?,
        headers: [String: String]?,
        queryItems: [URLQueryItem]?
    ) async throws -> (Data, Int) {
        // 记录请求信息
        lastRequestPath = path
        lastRequestMethod = method
        lastRequestBody = body
        lastRequestHeaders = headers
        
        // 查找模拟响应
        guard let mockResponse = mockResponses[path] else {
            throw NSError(domain: "MockAPIClient", code: 404, userInfo: [
                NSLocalizedDescriptionKey: "未找到路径'\(path)'的模拟响应"
            ])
        }
        
        // 模拟网络延迟
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        
        return (mockResponse.data, mockResponse.statusCode)
    }
} 