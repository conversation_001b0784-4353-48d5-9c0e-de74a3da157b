// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		E7A615B82DA25259007118DD /* SubscriptionProducts.storekit in Resources */ = {isa = PBXBuildFile; fileRef = E7A615B72DA25259007118DD /* SubscriptionProducts.storekit */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		E72328A12D981F05004856D3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E72328882D981F04004856D3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E723288F2D981F04004856D3;
			remoteInfo = brewlog;
		};
		E72328AB2D981F05004856D3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E72328882D981F04004856D3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E723288F2D981F04004856D3;
			remoteInfo = brewlog;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		E72328902D981F04004856D3 /* brewlog.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = brewlog.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E72328A02D981F05004856D3 /* brewlogTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = brewlogTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		E72328AA2D981F05004856D3 /* brewlogUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = brewlogUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		E7A615B72DA25259007118DD /* SubscriptionProducts.storekit */ = {isa = PBXFileReference; lastKnownFileType = text; name = SubscriptionProducts.storekit; path = StoreKit/SubscriptionProducts.storekit; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		E72328F52D98E72A004856D3 /* Exceptions for "brewlog" folder in "brewlog" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = E723288F2D981F04004856D3 /* brewlog */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		E72328922D981F04004856D3 /* brewlog */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				E72328F52D98E72A004856D3 /* Exceptions for "brewlog" folder in "brewlog" target */,
			);
			path = brewlog;
			sourceTree = "<group>";
		};
		E72328A32D981F05004856D3 /* brewlogTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = brewlogTests;
			sourceTree = "<group>";
		};
		E72328AD2D981F05004856D3 /* brewlogUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = brewlogUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		E723288D2D981F04004856D3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E723289D2D981F05004856D3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E72328A72D981F05004856D3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		E72328872D981F04004856D3 = {
			isa = PBXGroup;
			children = (
				E7A615B72DA25259007118DD /* SubscriptionProducts.storekit */,
				E72328922D981F04004856D3 /* brewlog */,
				E72328A32D981F05004856D3 /* brewlogTests */,
				E72328AD2D981F05004856D3 /* brewlogUITests */,
				E72328912D981F04004856D3 /* Products */,
			);
			sourceTree = "<group>";
		};
		E72328912D981F04004856D3 /* Products */ = {
			isa = PBXGroup;
			children = (
				E72328902D981F04004856D3 /* brewlog.app */,
				E72328A02D981F05004856D3 /* brewlogTests.xctest */,
				E72328AA2D981F05004856D3 /* brewlogUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		E723288F2D981F04004856D3 /* brewlog */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E72328B42D981F05004856D3 /* Build configuration list for PBXNativeTarget "brewlog" */;
			buildPhases = (
				E723288C2D981F04004856D3 /* Sources */,
				E723288D2D981F04004856D3 /* Frameworks */,
				E723288E2D981F04004856D3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				E72328922D981F04004856D3 /* brewlog */,
			);
			name = brewlog;
			packageProductDependencies = (
			);
			productName = brewlog;
			productReference = E72328902D981F04004856D3 /* brewlog.app */;
			productType = "com.apple.product-type.application";
		};
		E723289F2D981F05004856D3 /* brewlogTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E72328B72D981F05004856D3 /* Build configuration list for PBXNativeTarget "brewlogTests" */;
			buildPhases = (
				E723289C2D981F05004856D3 /* Sources */,
				E723289D2D981F05004856D3 /* Frameworks */,
				E723289E2D981F05004856D3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E72328A22D981F05004856D3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E72328A32D981F05004856D3 /* brewlogTests */,
			);
			name = brewlogTests;
			packageProductDependencies = (
			);
			productName = brewlogTests;
			productReference = E72328A02D981F05004856D3 /* brewlogTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		E72328A92D981F05004856D3 /* brewlogUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E72328BA2D981F05004856D3 /* Build configuration list for PBXNativeTarget "brewlogUITests" */;
			buildPhases = (
				E72328A62D981F05004856D3 /* Sources */,
				E72328A72D981F05004856D3 /* Frameworks */,
				E72328A82D981F05004856D3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E72328AC2D981F05004856D3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E72328AD2D981F05004856D3 /* brewlogUITests */,
			);
			name = brewlogUITests;
			packageProductDependencies = (
			);
			productName = brewlogUITests;
			productReference = E72328AA2D981F05004856D3 /* brewlogUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E72328882D981F04004856D3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					E723288F2D981F04004856D3 = {
						CreatedOnToolsVersion = 16.2;
					};
					E723289F2D981F05004856D3 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = E723288F2D981F04004856D3;
					};
					E72328A92D981F05004856D3 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = E723288F2D981F04004856D3;
					};
				};
			};
			buildConfigurationList = E723288B2D981F04004856D3 /* Build configuration list for PBXProject "brewlog" */;
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				"zh-Hans",
				Base,
			);
			mainGroup = E72328872D981F04004856D3;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = E72328912D981F04004856D3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				E723288F2D981F04004856D3 /* brewlog */,
				E723289F2D981F05004856D3 /* brewlogTests */,
				E72328A92D981F05004856D3 /* brewlogUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		E723288E2D981F04004856D3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E7A615B82DA25259007118DD /* SubscriptionProducts.storekit in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E723289E2D981F05004856D3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E72328A82D981F05004856D3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E723288C2D981F04004856D3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E723289C2D981F05004856D3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E72328A62D981F05004856D3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		E72328A22D981F05004856D3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E723288F2D981F04004856D3 /* brewlog */;
			targetProxy = E72328A12D981F05004856D3 /* PBXContainerItemProxy */;
		};
		E72328AC2D981F05004856D3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E723288F2D981F04004856D3 /* brewlog */;
			targetProxy = E72328AB2D981F05004856D3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		E72328B22D981F05004856D3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = J6L9YT98ZJ;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		E72328B32D981F05004856D3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = J6L9YT98ZJ;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		E72328B52D981F05004856D3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"brewlog/Assets\"";
				DEVELOPMENT_TEAM = R9KUJTA9N8;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = brewlog/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "咖啡札记";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "需要日历权限以便为您创建定期数据备份提醒";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的照片库以保存应用内截图";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kafeidazi.brewlog;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited) ALLOW_INSECURE_HTTP LOG_NETWORK_CALLS";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		E72328B62D981F05004856D3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"brewlog/Assets\"";
				DEVELOPMENT_TEAM = R9KUJTA9N8;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = brewlog/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "咖啡札记";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "需要日历权限以便为您创建定期数据备份提醒";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的照片库以保存应用内截图";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kafeidazi.brewlog;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		E72328B82D981F05004856D3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = R9KUJTA9N8;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kafeidazi.brewlogTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/brewlog.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/brewlog";
			};
			name = Debug;
		};
		E72328B92D981F05004856D3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = R9KUJTA9N8;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kafeidazi.brewlogTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/brewlog.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/brewlog";
			};
			name = Release;
		};
		E72328BB2D981F05004856D3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = R9KUJTA9N8;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kafeidazi.brewlogUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_TARGET_NAME = brewlog;
			};
			name = Debug;
		};
		E72328BC2D981F05004856D3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = R9KUJTA9N8;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kafeidazi.brewlogUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_TARGET_NAME = brewlog;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		E723288B2D981F04004856D3 /* Build configuration list for PBXProject "brewlog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E72328B22D981F05004856D3 /* Debug */,
				E72328B32D981F05004856D3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E72328B42D981F05004856D3 /* Build configuration list for PBXNativeTarget "brewlog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E72328B52D981F05004856D3 /* Debug */,
				E72328B62D981F05004856D3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E72328B72D981F05004856D3 /* Build configuration list for PBXNativeTarget "brewlogTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E72328B82D981F05004856D3 /* Debug */,
				E72328B92D981F05004856D3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E72328BA2D981F05004856D3 /* Build configuration list for PBXNativeTarget "brewlogUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E72328BB2D981F05004856D3 /* Debug */,
				E72328BC2D981F05004856D3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = E72328882D981F04004856D3 /* Project object */;
}
