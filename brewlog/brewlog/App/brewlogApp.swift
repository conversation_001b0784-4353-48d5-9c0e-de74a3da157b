//
//  brewlogApp.swift
//  brewlog
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/29.
//

import SwiftUI
#if canImport(UIKit)
import UIKit
#endif
import UserNotifications

@main
struct brewlogApp: App {
    @StateObject private var authService = AuthService.shared
    @StateObject private var appState = AppState.shared
    @StateObject private var subscriptionService = SubscriptionService.shared
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var networkMonitor = NetworkMonitor.shared
    @Environment(\.colorScheme) private var systemColorScheme

    // 持有TraitChangeObserver的引用，确保其不会被释放
    private let traitObserver = TraitChangeObserver.shared

    // 添加通知代理控制器
    private let notificationDelegate = NotificationDelegate()

    // 添加通知服务引用
    private let beanNotificationService = BeanNotificationService.shared
    private let brewReminderService = BrewReminderService.shared

    // 标记是否显示订阅视图
    @State private var showSubscriptionView = false

    // 延后URL处理，避免应用一启动就请求StoreKit验证
    @State private var pendingURL: URL? = nil

    init() {
        // 设置导航栏标题对齐方式为左对齐
        configureNavigationBarAppearance()

        // 设置通知中心代理
        UNUserNotificationCenter.current().delegate = notificationDelegate

        // 注册用于处理通知服务重新调度的观察者
        registerNotificationObservers()
    }

    var body: some Scene {
        WindowGroup {
            SplashScreenContainer()
                .environmentObject(authService)
                .environmentObject(appState)
                .environmentObject(subscriptionService)
                .environmentObject(themeManager)
                .environmentObject(networkMonitor)
                .onAppear {
                    // 确保初始化时正确应用主题
                    DispatchQueue.main.async {
                        // 只在首次启动时调用一次强制更新，之后通过通知来处理变化
                        themeManager.forceApplyAppearance()

                        // 检查并刷新通知权限状态
                        checkNotificationPermission()

                        // 处理延迟的URL请求（如果有）
                        if let url = pendingURL {
                            handleIncomingURL(url, appState: appState)
                            pendingURL = nil
                        }
                    }
                }
                .preferredColorScheme(colorSchemeFromThemeMode(themeManager.themeMode))
                .onReceive(NotificationCenter.default.publisher(for: Notification.Name("NavigateToBeanDetail"))) { notification in
                    // 处理通知点击导航
                    if let beanId = notification.userInfo?["bean_id"] as? Int {
                        // 导航到相应的豆子详情页面
                        // 这里需要根据应用的实际导航结构来实现
                        appState.selectedTab = .beans
                        appState.selectedBeanId = beanId
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: Notification.Name("NavigateToAddBrewLog"))) { notification in
                    // 处理冲煮提醒通知点击导航
                    appState.selectedTab = .brewLog

                    // 检查是否需要复制上次记录
                    if let userInfo = notification.userInfo,
                       let copyLastRecord = userInfo["copy_last_record"] as? Bool,
                       copyLastRecord {
                        appState.shouldCopyLastRecord = true
                    }

                    appState.shouldShowAddBrewLog = true
                }
                .onOpenURL { url in
                    // 延迟处理URL，避免应用启动时立即触发StoreKit
                    if appState.isAppInitialized {
                        // 如果应用已初始化完成，直接处理URL
                        handleIncomingURL(url, appState: appState)
                    } else {
                        // 否则，将URL保存起来，等待应用初始化完成后再处理
                        pendingURL = url
                    }
                }
                .sheet(isPresented: $showSubscriptionView) {
                    NavigationView {
                        SubscriptionView()
                    }
                }
        }
    }

    // 注册通知观察者
    private func registerNotificationObservers() {
        // 监听应用程序进入前台的通知，用于刷新通知调度
        NotificationCenter.default.addObserver(
            forName: UIApplication.willEnterForegroundNotification,
            object: nil,
            queue: .main
        ) { _ in
            // 应用回到前台时，检查通知权限并刷新通知
            checkNotificationPermission()
        }

        // 监听用户登录状态变化
        NotificationCenter.default.addObserver(
            forName: Notification.Name("UserLoggedIn"),
            object: nil,
            queue: .main
        ) { _ in
            // 用户登录后，重新调度通知
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                checkNotificationPermission()

                // 执行每日自动检查
                if self.brewReminderService.getBrewReminderSettings() {
                    self.brewReminderService.performDailyAutoCheckIfNeeded()
                }
            }
        }

        // 监听用户注销状态变化
        NotificationCenter.default.addObserver(
            forName: Notification.Name("UserLoggedOut"),
            object: nil,
            queue: .main
        ) { _ in
            // 用户注销后，移除所有通知
            beanNotificationService.removeAllBeanNotifications()
            brewReminderService.removeAllBrewReminders()
        }
    }

    // 检查通知权限并根据需要刷新通知
    private func checkNotificationPermission() {
        // 检查通知设置
        let beanNotificationSettings = beanNotificationService.getNotificationSettings()
        let brewReminderEnabled = brewReminderService.getBrewReminderSettings()

        // 如果用户已启用任何通知设置
        if beanNotificationSettings.enabled || brewReminderEnabled {
            // 检查系统通知权限
            UNUserNotificationCenter.current().getNotificationSettings { settings in
                if settings.authorizationStatus == .authorized {
                    // 有权限且设置已启用，重新调度通知
                    DispatchQueue.main.async {
                        // 调度养豆期通知
                        if beanNotificationSettings.enabled {
                            self.beanNotificationService.scheduleBeanNotifications()
                        }

                        // 调度冲煮提醒通知
                        if brewReminderEnabled {
                            // 检查当前状态并调度
                            print("🔄 应用前台时重新调度冲煮提醒")
                            self.brewReminderService.scheduleBrewReminder()

                            // 执行每日自动检查（如果需要）
                            self.brewReminderService.performDailyAutoCheckIfNeeded()
                        }

                        // 确保BeanListViewModel已加载，则清理通知记录
                        if let viewModel = SharedViewModels.shared.beanListViewModel {
                            Task { @MainActor in
                                // 获取当前所有咖啡豆ID
                                let currentBeanIds = viewModel.coffeeBeans.map { $0.id }

                                // 维护通知记录
                                self.beanNotificationService.cleanupNotificationRecords(currentBeanIds: currentBeanIds)
                            }
                        }
                    }
                }
            }
        }
    }

    // 配置导航栏外观，将标题对齐方式设置为左对齐
    private func configureNavigationBarAppearance() {
        // 创建标准外观
        let standardAppearance = UINavigationBarAppearance()
        let scrollEdgeAppearance = UINavigationBarAppearance()

        // 配置标准外观（滚动时）
        standardAppearance.configureWithDefaultBackground()
        // 设置标题位置为左对齐
        standardAppearance.titlePositionAdjustment = UIOffset(horizontal: -CGFloat.greatestFiniteMagnitude, vertical: 0)

        // 配置边缘外观（初始状态）为透明
        scrollEdgeAppearance.configureWithTransparentBackground()
        // 设置标题位置为左对齐
        scrollEdgeAppearance.titlePositionAdjustment = UIOffset(horizontal: -CGFloat.greatestFiniteMagnitude, vertical: 0)

        // 应用于所有导航栏
        UINavigationBar.appearance().standardAppearance = standardAppearance
        UINavigationBar.appearance().compactAppearance = standardAppearance
        UINavigationBar.appearance().scrollEdgeAppearance = scrollEdgeAppearance
    }

    // 根据主题模式返回合适的 ColorScheme
    private func colorSchemeFromThemeMode(_ mode: ThemeMode) -> ColorScheme? {
        switch mode {
        case .system:
            return nil // nil 表示跟随系统
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }

    // URL处理方法 - 仅处理外部深度链接（付费功能）
    private func handleIncomingURL(_ url: URL, appState: AppState) {
        print("处理外部深度链接: \(url.absoluteString)")

        // 确保scheme是brewlog
        guard url.scheme == "brewlog" else { return }

        // 所有通过 URL Scheme 的访问都视为外部深度链接，需要付费检查
        // 检查用户是否已订阅URL Scheme功能
        let urlSchemeFeature = SubscriptionFeature(
            title: "URL Scheme访问",
            description: "通过URL Scheme深度链接访问应用功能",
            isPremiumOnly: true,
            icon: "link"
        )

        let hasUrlSchemeAccess = subscriptionService.hasAccess(to: urlSchemeFeature)

        // 如果用户没有URL Scheme访问权限，显示订阅页面
        if !hasUrlSchemeAccess {
            // 显示订阅视图
            DispatchQueue.main.async {
                self.showSubscriptionView = true
            }

            // 记录尝试访问高级功能的事件
            print("免费用户尝试使用外部URL Scheme功能，显示订阅页面")

            // 不执行后续导航操作
            return
        }

        print("付费用户访问外部深度链接，允许继续")

        // 根据URL的host和路径进行相应处理
        switch url.host {
        case "account":
            // 跳转到账号设置页面
            appState.selectedTab = .more
            appState.shouldNavigateToAccountSettings = true
            print("导航至账号设置页")

        case "beans", "bean":
            // 处理咖啡豆相关导航
            appState.selectedTab = .beans

            // 检查是否有ID参数
            if let components = URLComponents(url: url, resolvingAgainstBaseURL: true),
               let idParam = components.queryItems?.first(where: { $0.name == "id" }),
               let beanId = Int(idParam.value ?? "") {
                // 导航到特定咖啡豆详情
                appState.selectedBeanId = beanId
                print("导航至咖啡豆详情，ID: \(beanId)")
            }

        case "brew", "brewlog":
            // 处理冲煮记录相关导航
            print("🔗 URL Scheme: 切换到冲煮记录tab")
            appState.selectedTab = .brewLog

            // 检查是否有ID参数
            if let components = URLComponents(url: url, resolvingAgainstBaseURL: true),
               let idParam = components.queryItems?.first(where: { $0.name == "id" }),
               let brewId = Int(idParam.value ?? "") {
                print("🔗 URL Scheme: 解析到冲煮记录ID: \(brewId)")
                // 延迟设置导航ID，确保tab切换完成
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    print("🔗 URL Scheme: 延迟0.3秒后设置selectedBrewLogId = \(brewId)")
                    appState.selectedBrewLogId = brewId
                    print("🔗 URL Scheme: 导航至冲煮记录详情，ID: \(brewId)")
                }
            } else {
                print("🔗 URL Scheme: 没有找到ID参数")
            }

        case "equipment", "equipments":
            // 处理设备相关导航
            appState.selectedTab = .equipment

            // 检查是否有ID参数
            if let components = URLComponents(url: url, resolvingAgainstBaseURL: true),
               let idParam = components.queryItems?.first(where: { $0.name == "id" }),
               let equipmentId = Int(idParam.value ?? "") {
                // 导航到特定设备详情
                appState.selectedEquipmentId = equipmentId
                print("导航至设备详情，ID: \(equipmentId)")
            }

        default:
            print("未识别的URL路径: \(url.host ?? "无host")")
        }
    }
}

// 通知代理类，用于处理接收到的通知
class NotificationDelegate: NSObject, UNUserNotificationCenterDelegate {
    private let beanNotificationService = BeanNotificationService.shared
    private let brewReminderService = BrewReminderService.shared

    // 在应用程序处于前台时接收到通知
    func userNotificationCenter(_ center: UNUserNotificationCenter,
                               willPresent notification: UNNotification,
                               withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // 允许在前台显示通知的横幅、声音和标记
        completionHandler([.banner, .sound, .badge])
    }

    // 用户与通知交互时的处理
    func userNotificationCenter(_ center: UNUserNotificationCenter,
                               didReceive response: UNNotificationResponse,
                               withCompletionHandler completionHandler: @escaping () -> Void) {
        // 判断通知类型，根据categoryIdentifier调用不同的处理方法
        let categoryIdentifier = response.notification.request.content.categoryIdentifier

        if categoryIdentifier == "bean_reminder_category" || categoryIdentifier == "BEAN_REMINDER_CATEGORY" {
            // 如果是咖啡豆提醒通知，交给专门的服务处理
            beanNotificationService.handleNotificationResponse(response, completionHandler: completionHandler)
        } else if categoryIdentifier == "brew_reminder_category" {
            // 如果是冲煮提醒通知，交给冲煮提醒服务处理
            brewReminderService.handleNotificationResponse(response, completionHandler: completionHandler)
        } else {
            // 其他类型的通知处理
            completionHandler()
        }
    }
}

struct MainTabView: View {
    @EnvironmentObject var appState: AppState
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var subscriptionService: SubscriptionService
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var networkMonitor: NetworkMonitor

    var body: some View {
        TabView(selection: $appState.selectedTab) {
            ForEach(appState.tabOrder, id: \.self) { tab in
                tabView(for: tab)
                    .tabItem {
                        Label {
                            Text(tab.rawValue)
                        } icon: {
                            tab.icon
                        }
                    }
                    .tag(tab)
            }

            MoreView()
                .tabItem {
                    Label {
                        Text("更多")
                    } icon: {
                        AppState.Tab.more.icon
                    }
                }
                .tag(AppState.Tab.more)
        }
        .accentColor(.functionText)
        .withNetworkBanner()
        .ignoresSafeArea(.keyboard)
        .preferredColorScheme(themeManager.preferredColorScheme())
    }

    @ViewBuilder
    private func tabView(for tab: AppState.Tab) -> some View {
        NavigationStack {
            switch tab {
            case .brewLog:
                BrewLogListView()
            case .equipment:
                EquipmentListView()
            case .beans:
                BeanListView()
            case .recipes:
                RecipeListView()
            case .hindsight:
                HindsightView()
            case .heatmap:
                HeatmapView()
            case .beanCalendar:
                BeanCalendarView()
            case .more:
                EmptyView()
            }
        }
    }
}

struct StatsView: View {
    var body: some View {
        List {
            NavigationLink("热力图", destination: HeatmapView())
            NavigationLink("后见之明", destination: HindsightView())
            NavigationLink("咖啡豆日历", destination: BeanCalendarView())
        }
        .navigationTitle("统计")
    }
}

#if DEBUG
struct brewlogApp_Previews: PreviewProvider {
    static var previews: some View {
        Text("App Preview")
            .environmentObject(AuthService.shared)
    }
}
#endif
