import SwiftUI

struct GestureSettingsView: View {
    @StateObject private var gestureSettings = GestureSettings.shared
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var showingSubscription = false

    var body: some View {
        PremiumFeatureWrapper {
            List {
                ForEach(ListSectionType.allCases) { section in
                    Section(header: sectionHeader(for: section)) {
                        // 左滑设置
                        NavigationLink(destination: SwipeActionSelectorView(
                            sectionType: section,
                            direction: .left,
                            selectedAction: gestureSettings.getAction(for: section, direction: .left)
                        )) {
                            HStack {
                                Label {
                                    Text("向左轻扫")
                                } icon: {
                                    Image("swipeLeft.symbols")
                                }

                                Spacer()

                                // 显示当前选择的操作
                                let action = gestureSettings.getAction(for: section, direction: .left)
                                HStack(spacing: 4) {
                                    // 根据操作类型使用不同的图标显示方式
                                    if action == .edit || action == .restock || action == .quickBrew || action == .portal || action == .tag || action == .rename {
                                        Image(action.icon)
                                            .font(.footnote)
                                            .foregroundColor(.secondary)
                                    } else if action != .none {
                                        Image(systemName: action.icon)
                                            .font(.footnote)
                                            .foregroundColor(.secondary)
                                    }
                                    Text(action.rawValue)
                                        .foregroundColor(.secondary)
                                        .font(.subheadline)
                                }
                            }
                        }

                        // 右滑设置
                        NavigationLink(destination: SwipeActionSelectorView(
                            sectionType: section,
                            direction: .right,
                            selectedAction: gestureSettings.getAction(for: section, direction: .right)
                        )) {
                            HStack {
                                Label {
                                    Text("向右轻扫")
                                } icon: {
                                    Image("swipeRight.symbols")
                                }

                                Spacer()

                                // 显示当前选择的操作
                                let action = gestureSettings.getAction(for: section, direction: .right)
                                HStack(spacing: 4) {
                                    // 根据操作类型使用不同的图标显示方式
                                    if action == .edit || action == .restock || action == .quickBrew || action == .portal || action == .tag || action == .rename {
                                        Image(action.icon)
                                            .font(.footnote)
                                            .foregroundColor(.secondary)
                                    } else if action != .none {
                                        Image(systemName: action.icon)
                                            .font(.footnote)
                                            .foregroundColor(.secondary)
                                    }
                                    Text(action.rawValue)
                                        .foregroundColor(.secondary)
                                        .font(.subheadline)
                                }
                            }
                        }
                    }
                }
                .listRowBackground(Color.primaryBg)
            }
            .navigationTitle("轻扫选项")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("重置") {
                        gestureSettings.resetToDefault()
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .scrollContentBackground(.hidden) // 隐藏默认背景
            .background(Color.secondaryBg) // 设置自定义背景色
        }
    }

    // 创建带有图标的自定义章节标题
    private func sectionHeader(for section: ListSectionType) -> some View {
        HStack {
            Image(section.icon)
                .foregroundColor(.accentColor)
                .font(.footnote)

            Text(section.rawValue)
                .font(.footnote)
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    NavigationView {
        GestureSettingsView()
            .environmentObject(SubscriptionService.shared)
    }
}