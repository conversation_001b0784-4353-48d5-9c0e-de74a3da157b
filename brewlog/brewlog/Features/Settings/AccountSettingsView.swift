import SwiftUI

// 订阅状态行
struct SubscriptionStatusRow: View {
    @ObservedObject private var subscriptionService = SubscriptionService.shared
    @State private var showingSubscription = false
    
    var body: some View {
        VStack {
            if subscriptionService.currentSubscriptionType == .premium {
                // 高级版用户显示无点击效果的视图
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("当前套餐")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Text(subscriptionService.currentSubscriptionType.description)
                                .font(.headline)
                            
                            Image(systemName: "checkmark.seal.fill")
                                .foregroundColor(.green)
                                .font(.caption)
                        }
                    }
                    
                    Spacer()
                    
                    if let expirationDate = subscriptionService.expirationDate {
                        VStack(alignment: .trailing, spacing: 4) {
                            Text("下次续订日期")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text(expirationDate, style: .date)
                                .font(.subheadline)
                        }
                    }
                }
                .padding(.vertical, 4)
                
                // 添加感谢支持的小字
                Text("感谢你对咖啡搭子的支持！")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.top, 2)
            } else {
                // 非高级版用户保持原有的可点击按钮
                Button(action: {
                    showingSubscription = true
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("当前套餐")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            HStack {
                                Text(subscriptionService.currentSubscriptionType.description)
                                    .font(.headline)
                            }
                        }
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.subheadline)
                            .foregroundColor(.functionText)
                    }
                    .padding(.vertical, 4)
                }
            }
        }
        .sheet(isPresented: $showingSubscription) {
            NavigationView {
                SubscriptionView()
            }
        }
    }
}

// 账号设置视图
struct AccountSettingsView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var themeManager: ThemeManager
    @State private var showingNicknameEditor = false
    @State private var refreshTrigger = UUID() // 添加刷新触发器
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var hasExportLimit = false // 添加导出限制状态
    @State private var isExporting = false // 添加导出中状态
    
    // 导出限制的UserDefaults键
    private let exportLimitKey = "export_limit_date"
    
    var body: some View {
        List {
            // 订阅状态部分
            Section {
                SubscriptionStatusRow()
                    .listRowBackground(Color.primaryBg)
            }
            
            // 用户基本信息卡片
            Section(header: Text("资料").font(.footnote)) {
                // 昵称
                HStack {
                    Image("nickname.symbols")
                        .foregroundColor(Color.noteText)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("昵称")
                            .font(.caption)
                            .foregroundColor(.detailText)
                        
                        if let firstName = authService.currentUser?.firstName, !firstName.isEmpty {
                            Text(firstName)
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                        } else {
                            Text("未设置昵称")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                        }
                    }
                    
                    Spacer()
                }
                .padding(.vertical, 4)
                .id(refreshTrigger) // 使用刷新触发器作为视图ID
                
                // 用户名
                HStack {
                    Image("defaultAvatar.symbols")
                        .foregroundColor(.noteText)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("用户名")
                            .font(.caption)
                            .foregroundColor(.detailText)
                        
                        if let username = authService.currentUser?.username {
                            Text("\(username)")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                        }
                    }
                    
                    Spacer()
                }
                .padding(.vertical, 4)
                
                // 邮箱
                if let email = authService.currentUser?.email {
                    HStack {
                        Image("email.symbols")
                            .foregroundColor(.noteText)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("邮箱")
                                .font(.caption)
                                .foregroundColor(.detailText)
                            
                            Text(email)
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                        }
                        
                        Spacer()
                    }
                    .padding(.vertical, 4)
                }
            }
            .listRowBackground(Color.primaryBg)
            // 账号设置选项
            Section(header: Text("设置").font(.footnote)) {
                Button(action: {
                    showingNicknameEditor = true
                }) {
                    HStack {
                        Label {
                            Text("修改昵称")
                                .foregroundColor(.primaryText)
                        } icon: {
                            Image("nicknameEdit.symbols")
                                .font(.subheadline)
                                .foregroundColor(.functionText)
                        }
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.subheadline)
                            .foregroundColor(.functionText)
                    }
                }
                
                Link(destination: URL(string: "\(APIService.environment == .development ? "http://192.168.2.41:8000" : "https://www.kafeidazi.com")/u/password/change/")!) {
                    HStack {
                        Label {
                            Text("修改密码")
                                .foregroundColor(.primaryText)
                        } icon: {
                            Image("pswEdit.symbols")
                                .font(.subheadline)
                                .foregroundColor(.functionText)
                        }
                        Spacer()
                        Image(systemName: "arrow.up.right.square")
                            .font(.subheadline)
                            .foregroundColor(.functionText)
                    }
                }
                
                Link(destination: URL(string: "\(APIService.environment == .development ? "http://192.168.2.41:8000" : "https://www.kafeidazi.com")/u/email/")!) {
                    HStack {
                        Label {
                            Text("修改邮箱")
                                .foregroundColor(.primaryText)
                        } icon: {
                            Image("emailEdit.symbols")
                                .font(.subheadline)
                                .foregroundColor(.functionText)
                        }
                        Spacer()
                        Image(systemName: "arrow.up.right.square")
                            .font(.subheadline)
                            .foregroundColor(.functionText)
                    }
                }
            }
            .listRowBackground(Color.primaryBg)
            
            // 账号数据部分
            Section(header: Text("数据").font(.footnote)) {
                // 添加导出功能按钮
                Button(action: {
                    exportBrewingRecords()
                }) {
                    HStack {
                        Label {
                            Text("导出所有冲煮记录")
                                .foregroundColor(hasExportLimit || isExporting ? Color.archivedText : Color.primaryText)
                        } icon: {
                            Image("export.symbols")
                                .foregroundColor(hasExportLimit || isExporting ? Color.archivedText : Color.functionText)
                        }
                        
                        Spacer()
                        
                        if isExporting {
                            BlinkingLoader(
                                color: .secondaryText,
                                width: 8,
                                height: 12,
                                duration: 1.0,
                                showText: false
                            )
                        }
                    }
                }
                .disabled(hasExportLimit || isExporting)
                
                // 添加频率限制说明
                Text(hasExportLimit ? "您今天的导出次数已达上限，请明天再尝试" : "为缓解服务器压力，导出功能每日限使用一次")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .padding(.top, 4)
                    .padding(.bottom, 2)
            }
            .listRowBackground(Color.primaryBg)
            
            // 登出
            Section {
                Button(action: {
                    authService.logout()
                }) {
                    HStack {
                        Spacer()
                        Text("退出登录")
                            .foregroundColor(Color("ErrorColor"))
                        Spacer()
                    }
                }
            }
            .listRowBackground(Color.primaryBg)
        }
        .navigationTitle("我的账号")
        .background(Color.secondaryBg.ignoresSafeArea())
        .scrollContentBackground(.hidden)
        .sheet(isPresented: $showingNicknameEditor, onDismiss: {
            // 当昵称修改器关闭时，刷新用户资料并更新UI
            Task {
                await authService.fetchUserProfile()
                refreshTrigger = UUID() // 生成新的UUID触发UI刷新
            }
        }) {
            NicknameEditView()
        }
        .onAppear {
            // 确保用户信息已加载
            Task {
                await authService.fetchUserProfile()
                refreshTrigger = UUID() // 每次页面出现时也刷新UI
                checkLocalExportLimit() // 只检查本地存储的限制
            }
        }
    }
    
    // 检查本地导出限制
    private func checkLocalExportLimit() {
        // 检查本地存储的限制日期
        if let savedDateString = UserDefaults.standard.string(forKey: exportLimitKey) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            
            if let savedDate = formatter.date(from: savedDateString) {
                let today = Calendar.current.startOfDay(for: Date())
                
                if Calendar.current.isDate(savedDate, inSameDayAs: today) {
                    // 如果是同一天，维持限制状态
                    self.hasExportLimit = true
                } else {
                    // 如果不是同一天，清除本地限制记录
                    UserDefaults.standard.removeObject(forKey: exportLimitKey)
                    self.hasExportLimit = false
                }
            }
        } else {
            self.hasExportLimit = false
        }
    }
    
    // 导出记录
    private func exportBrewingRecords() {
        guard let token = authService.getStoredToken() else { return }
        
        isExporting = true
        
        Task {
            let baseURL = APIService.environment == .development ? "http://192.168.2.41:8000" : "https://www.kafeidazi.com"
            guard let url = URL(string: "\(baseURL)/my/brewlog/export/") else {
                isExporting = false
                return
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            
            do {
                let (data, response) = try await URLSession.shared.data(for: request)
                
                if let httpResponse = response as? HTTPURLResponse {
                    if httpResponse.statusCode == 200 {
                        // 保存文件并显示分享sheet
                        await saveExportedData(data)
                        
                        // 注意：限制状态的更新移动到了saveExportedData方法中，在分享sheet关闭后执行
                    } else if httpResponse.statusCode == 403 {
                        // 达到限制立即更新状态
                        self.hasExportLimit = true
                        
                        // 保存当前日期到本地
                        let formatter = DateFormatter()
                        formatter.dateFormat = "yyyy-MM-dd"
                        let today = formatter.string(from: Date())
                        UserDefaults.standard.set(today, forKey: self.exportLimitKey)
                    }
                }
            } catch {
                print("导出记录失败: \(error)")
            }
            
            isExporting = false
        }
    }
    
    // 保存导出文件
    private func saveExportedData(_ data: Data) async {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let dateString = dateFormatter.string(from: Date())
        let fileName = "咖啡札记_\(dateString).csv"
        
        // 获取文档目录
        if let documentDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first {
            let fileURL = documentDirectory.appendingPathComponent(fileName)
            
            do {
                try data.write(to: fileURL)
                
                // 使用系统分享功能
                await withCheckedContinuation { continuation in
                    DispatchQueue.main.async {
                        let activityVC = UIActivityViewController(activityItems: [fileURL], applicationActivities: nil)
                        
                        // 在分享sheet关闭后更新限制状态
                        activityVC.completionWithItemsHandler = { _, _, _, _ in
                            // 设置导出限制标记
                            self.hasExportLimit = true
                            
                            // 保存当前日期到本地，作为限制日期
                            let formatter = DateFormatter()
                            formatter.dateFormat = "yyyy-MM-dd"
                            let today = formatter.string(from: Date())
                            UserDefaults.standard.set(today, forKey: self.exportLimitKey)
                            
                            continuation.resume()
                        }
                        
                        // 获取当前的UIWindow
                        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                           let rootViewController = windowScene.windows.first?.rootViewController {
                            // 在iPad上需要设置popoverPresentationController的sourceView和sourceRect
                            if UIDevice.current.userInterfaceIdiom == .pad {
                                activityVC.popoverPresentationController?.sourceView = rootViewController.view
                                activityVC.popoverPresentationController?.sourceRect = CGRect(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2, width: 0, height: 0)
                            }
                            
                            rootViewController.present(activityVC, animated: true, completion: nil)
                        } else {
                            continuation.resume()
                        }
                    }
                }
            } catch {
                print("保存导出文件失败: \(error)")
            }
        }
    }
}

struct AccountSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            AccountSettingsView()
                .environmentObject(AuthService.shared)
                .environmentObject(ThemeManager.shared)
        }
    }
} 