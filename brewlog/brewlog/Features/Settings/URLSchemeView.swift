import SwiftUI

struct URLSchemeView: View {
    @EnvironmentObject var themeManager: ThemeManager
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var showSubscriptionView = false
    
    // 深度链接数据，只保留已实现的URL Scheme
    private let urlSchemes = [
        URLSchemeItem(title: "冲煮记录列表", scheme: "brewlog://brew", description: "打开冲煮记录列表页。"),
        URLSchemeItem(title: "冲煮记录详情", scheme: "brewlog://brew?id={指定ID}", description: "打开指定ID（不带花括号）的冲煮记录详情页，ID可通过冲煮记录列表页长按卡片并分享文字版获取。"),
        URLSchemeItem(title: "咖啡豆列表", scheme: "brewlog://bean", description: "打开咖啡豆列表页。"),
        URLSchemeItem(title: "咖啡豆详情", scheme: "brewlog://bean?id={指定ID}", description: "打开指定ID（不带花括号）的咖啡豆详情页，ID可通过咖啡豆列表页长按卡片并分享文字版获取。"),
        URLSchemeItem(title: "设备列表", scheme: "brewlog://equipment", description: "打开设备列表页。"),
        URLSchemeItem(title: "设备详情", scheme: "brewlog://equipment?id={指定ID}", description: "打开指定ID（不带花括号）的设备详情页，ID可通过设备列表页长按卡片并分享文字版获取。"),
        URLSchemeItem(title: "账号设置", scheme: "brewlog://account", description: "打开账号设置页面。")
    ]
    
    var body: some View {
        Group {
            if subscriptionService.currentSubscriptionType == .premium {
                // 高级用户可以查看内容
                List {
                    Section(header: Text("URL Scheme 介绍")) {
                        Text("URL Scheme是应用间通信的一种方式，可以通过特定格式的链接直接打开应用的特定页面或执行特定操作。")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .padding(.vertical, 8)
                            .listRowBackground(Color.primaryBg)
                        
                        Text("您可以在自动化工具（如快捷指令）或其他应用中使用这些链接。")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .padding(.bottom, 8)
                            .listRowBackground(Color.primaryBg)
                    }
                    
                    Section(header: Text("有效链接（长按可复制）")) {
                        ForEach(urlSchemes) { item in
                            VStack(alignment: .leading, spacing: 8) {
                                Text(item.title)
                                    .font(.headline)
                                    .foregroundColor(.primary)
                                
                                Text(item.scheme)
                                    .font(.system(.subheadline, design: .monospaced))
                                    .foregroundColor(.accentColor)
                                    .padding(8)
                                    .background(Color.accentColor.opacity(0.1))
                                    .cornerRadius(6)
                                
                                Text(item.description)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.vertical, 6)
                            .listRowBackground(Color.primaryBg)
                            .contextMenu {
                                Button(action: {
                                    UIPasteboard.general.string = item.scheme
                                }) {
                                    Label("复制链接", systemImage: "doc.on.doc")
                                }
                            }
                        }
                    }
                    
                    Section(header: Text("使用示例")) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("在快捷指令中使用")
                                .font(.headline)
                            
                            Text("1. 创建新的快捷指令\n2. 添加「打开URL」动作\n3. 输入上述URL链接\n4. 保存并运行")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 6)
                        .listRowBackground(Color.primaryBg)
                    }
                }
                .listStyle(InsetGroupedListStyle())
                .background(Color.secondaryBg.ignoresSafeArea())
                .scrollContentBackground(.hidden)
                .navigationTitle("URL 深度链接")
            } else {
                // 免费用户显示升级提示
                VStack(spacing: 20) {
                    Image(systemName: "link.circle.fill")
                        .font(.system(size: 64))
                        .foregroundColor(.accentColor)
                    
                    Text("高级功能：URL 深度链接")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("URL深度链接功能允许您通过特定链接直接打开应用的特定页面，是实现自动化的强大工具。")
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    Text("升级至高级版即可使用此功能")
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .padding(.top)
                    
                    Button("升级到高级版") {
                        showSubscriptionView = true
                    }
                    .buttonStyle(.borderedProminent)
                    .padding(.top)
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.secondaryBg.ignoresSafeArea())
                .sheet(isPresented: $showSubscriptionView) {
                    NavigationView {
                        SubscriptionView()
                            .environmentObject(themeManager)
                    }
                }
                .navigationTitle("URL 深度链接")
            }
        }
        .preferredColorScheme(themeManager.preferredColorScheme())
    }
}

// 深度链接项数据模型
struct URLSchemeItem: Identifiable {
    let id = UUID()
    let title: String
    let scheme: String
    let description: String
}

struct URLSchemeView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            URLSchemeView()
                .environmentObject(ThemeManager.shared)
        }
    }
} 