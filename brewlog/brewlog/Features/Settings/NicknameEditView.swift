import SwiftUI

struct NicknameEditView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var authService: AuthService
    
    @State private var nickname: String = ""
    @State private var originalNickname: String = ""
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var showSuccessMessage = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 自定义导航栏
                topNavigationBar
                
                Form {
                    Section {
                        HStack {
                            TextField("昵称", text: $nickname, prompt: Text("请输入3~12个字符"))
                                .autocapitalization(.none)
                                .disableAutocorrection(true)
                                .onChange(of: nickname) { newValue in
                                    if newValue.count > 12 {
                                        nickname = String(newValue.prefix(12))
                                    }
                                    errorMessage = nil
                                }
                            
                            Text("\(nickname.count)/12")
                                .font(.caption)
                                .foregroundColor(nickname.count >= 3 ? .noteText : .red)
                        }
                    } header: {
                        Text("")
                    } footer: {
                        Text("请勿使用稀有字符或不常见符号")
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                    
                    // 信息提示部分
                    if let error = errorMessage {
                        Section {
                            Text(error)
                                .foregroundColor(.red)
                                .font(.footnote)
                        }
                    } else if showSuccessMessage {
                        Section {
                            Text("昵称修改成功！")
                                .foregroundColor(.green)
                                .font(.footnote)
                        }
                    }
                    
                    // 简化的提示信息
                    Section {
                        Text("⚠️ 昵称仅用于应用内展示。您的注册用户名 \(authService.currentUser?.username ?? "") 仍然用于登录，且无法更改。")
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                    .listRowBackground(Color.clear)
                }
                .listStyle(InsetGroupedListStyle())
                .background(Color.secondaryBg.ignoresSafeArea())
                .scrollContentBackground(.hidden)
                .onAppear {
                    Task {
                        await authService.fetchUserProfile()
                        if let nickname = authService.currentUser?.firstName {
                            self.nickname = nickname
                            self.originalNickname = nickname
                        }
                    }
                }
            }
            .navigationBarHidden(true)
        }
    }
    
    // 顶部导航栏
    private var topNavigationBar: some View {
        ZStack {
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(.linkText)
                }
                
                Spacer()
                
                saveButton
            }
            
            Text("修改昵称")
                .font(.headline)
                .foregroundColor(.primaryText)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.secondaryBg)
    }
    
    private var saveButton: some View {
        Button(action: saveNickname) {
            ZStack {
                if isLoading {
                    BlinkingLoader(
                        color: .primaryBg,
                        width: 8,
                        height: 12,
                        duration: 1.0,
                        showText: false
                    )
                } else {
                    Text("保存")
                        .bold()
                }
            }
            .frame(minWidth: 50) // 确保按钮有固定的最小宽度
        }
        .disabled(!isFormValid() || isLoading)
    }
    
    // 验证表单是否有效
    private func isFormValid() -> Bool {
        return !nickname.isEmpty && 
               nickname.count >= 3 && 
               nickname.count <= 12 &&
               nickname != originalNickname
    }
    
    // 保存昵称
    private func saveNickname() {
        guard isFormValid() else { return }
        
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let success = try await APIService.shared.updateNickname(nickname: nickname)
                
                await MainActor.run {
                    isLoading = false
                    
                    if success {
                        showSuccessMessage = true
                        originalNickname = nickname
                        authService.updateNickname(nickname)
                        
                        // 2秒后关闭窗口
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                            showSuccessMessage = false
                            dismiss()
                        }
                    } else {
                        errorMessage = "昵称修改失败，请稍后重试"
                    }
                }
            } catch let error as APIError {
                await MainActor.run {
                    isLoading = false
                    errorMessage = error.userFriendlyMessage
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "发生未知错误，请稍后重试"
                }
            }
        }
    }
}

struct NicknameEditView_Previews: PreviewProvider {
    static var previews: some View {
        NicknameEditView()
            .environmentObject(AuthService.shared)
    }
} 
