import SwiftUI

struct MoreView: View {
    @EnvironmentObject var appState: AppState
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var themeManager: ThemeManager
    @State private var showingTabOrderEditor = false
    @State private var showingWhatsNew = false
    @State private var showingOnboarding = false
    @State private var showingSubscription = false
    @State private var showingNicknameEditor = false
    @StateObject private var subscriptionService = SubscriptionService.shared

    // 添加导航状态
    @State private var navigateToAccount = false

    var body: some View {
        NavigationView {
            List {
                if !appState.moreItems.isEmpty {
                    Section(header: Spacer().frame(height: 16)) {
                        ForEach(appState.moreItems, id: \.self) { tab in
                            NavigationLink(destination: destinationView(for: tab)) {
                                Label {
                                    Text(tab.title)
                                } icon: {
                                    tab.icon
                                }
                            }
                            .listRowBackground(Color.primaryBg)
                        }
                    }
                }

                // 用户账号部分
                Section {
                    NavigationLink(destination: AccountSettingsView(), isActive: $navigateToAccount) {
                        HStack {
                            Label {
                                Text("我的账号")
                            } icon: {
                                Image("defaultAvatar.symbols")
                                    .foregroundColor(.functionText)
                            }
                            Spacer()
                            if subscriptionService.currentSubscriptionType == .premium {
                                Image(systemName: "checkmark.seal.fill")
                                    .foregroundColor(.green)
                                    .font(.caption)
                            } else {
                                Text("免费版")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .listRowBackground(Color.primaryBg)
                }

                // 高级功能部分
                Section(header: Text("个性化")) {
                    NavigationLink(destination: TabOrderEditorView()) {
                        HStack {
                            Label("菜单顺序", systemImage: "list.bullet")
                            if subscriptionService.currentSubscriptionType != .premium {
                                Spacer()
                                PremiumBadge()
                            }
                        }
                    }
                    .listRowBackground(Color.primaryBg)

                    NavigationLink(destination: GestureSettingsView()) {
                        HStack {
                            Label {
                                Text("轻扫选项")
                            } icon: {
                                Image("swipeSetting.symbols")
                            }
                            if subscriptionService.currentSubscriptionType != .premium {
                                Spacer()
                                PremiumBadge()
                            }
                        }
                    }
                    .listRowBackground(Color.primaryBg)

                    NavigationLink {
                        ThemeSettingsView()
                            .onAppear {
                                themeManager.updateThemeColorsOnly()
                            }
                            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ThemeDidChange"))) { _ in
                                // 刷新视图
                            }
                            .preferredColorScheme(themeManager.preferredColorScheme())
                    } label: {
                        HStack {
                            Label("主题设置", systemImage: "paintpalette")
                            if subscriptionService.currentSubscriptionType != .premium {
                                Spacer()
                                PremiumBadge()
                            }
                        }
                    }
                    .listRowBackground(Color.primaryBg)
                }

                // 自动化部分
                Section(header: Text("自动化")) {
                    // 通知和提醒入口
                    NavigationLink(destination: NotificationsSettingsView()) {
                        HStack {
                            Label {
                                Text("通知和提醒")
                            } icon: {
                                Image(systemName: "bell.badge")
                                    .foregroundColor(.functionText)
                            }
                            if subscriptionService.currentSubscriptionType != .premium {
                                Spacer()
                                PremiumBadge()
                            }
                        }
                    }
                    .listRowBackground(Color.primaryBg)

                    // URL深度链接入口
                    NavigationLink(destination: URLSchemeView().environmentObject(themeManager)) {
                        HStack {
                            Label {
                                Text("URL深度链接")
                            } icon: {
                                Image(systemName: "link.circle")
                                    .foregroundColor(.functionText)
                            }
                            if subscriptionService.currentSubscriptionType != .premium {
                                Spacer()
                                PremiumBadge()
                            }
                        }
                    }
                    .listRowBackground(Color.primaryBg)
                }

                Section {
                    NavigationLink(destination: HelpView()) {
                        Label("帮助中心", systemImage: "questionmark.circle")
                    }
                    .listRowBackground(Color.primaryBg)

                    NavigationLink(destination: AboutView()) {
                        Label("关于", systemImage: "info.circle")
                    }
                    .listRowBackground(Color.primaryBg)
                }

                // 添加底部小字
                HStack {
                    Spacer()
                    Text("咖啡搭子谨制")
                        .font(.footnote)
                        .foregroundColor(Color.noteText.opacity(0.6))
                        .padding(.vertical, 12)
                    Spacer()
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
            }
            .listStyle(InsetGroupedListStyle())
            .background(Color.secondaryBg.ignoresSafeArea())
            .scrollContentBackground(.hidden)
            .navigationTitle("更多")
            .sheet(isPresented: $showingSubscription) {
                NavigationView {
                    SubscriptionView()
                        .environmentObject(themeManager)
                }
            }
            .sheet(isPresented: $showingNicknameEditor) {
                NicknameEditView()
                    .environmentObject(themeManager)
            }
            .onAppear {
                // 确保用户信息已加载
                Task {
                    await authService.fetchUserProfile()
                }

                // 确保应用了正确的主题设置
                if themeManager.themeMode == .system {
                    themeManager.updateThemeColorsOnly()
                }

                // 检查是否需要导航到账号设置
                if appState.shouldNavigateToAccountSettings {
                    navigateToAccount = true
                    appState.shouldNavigateToAccountSettings = false
                }
            }
            // 添加主题变化通知监听
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ThemeDidChange"))) { _ in
                // 强制刷新视图状态以应用新主题
                // 这里不需要执行任何操作，视图会自动重新渲染
            }
            // 监听导航到账号设置的变化
            .onChange(of: appState.shouldNavigateToAccountSettings) { newValue in
                if newValue {
                    navigateToAccount = true
                    appState.shouldNavigateToAccountSettings = false
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle()) // 使用堆栈导航视图样式，确保主题变化能够传递
        // 添加全局主题设置
        .preferredColorScheme(themeManager.preferredColorScheme())
    }

    @ViewBuilder
    private func destinationView(for tab: AppState.Tab) -> some View {
        switch tab {
        case .brewLog:
            BrewLogListView()
        case .equipment:
            EquipmentListView()
        case .beans:
            BeanListView()
        case .recipes:
            RecipeListView()
        case .hindsight:
            HindsightView()
        case .heatmap:
            HeatmapView()
        case .beanCalendar:
            BeanCalendarView()
        case .more:
            EmptyView()
        }
    }
}

struct MoreView_Previews: PreviewProvider {
    static var previews: some View {
        MoreView()
            .environmentObject(AppState.shared)
            .environmentObject(AuthService.shared)
    }
}

struct HelpView: View {
    @State private var showingOnboarding = false
    @State private var showingWhatsNew = false

    var body: some View {
        List {
            Button("新手引导") {
                showingOnboarding = true
            }
            .listRowBackground(Color.primaryBg)

            Button("更新日志") {
                showingWhatsNew = true
            }
            .listRowBackground(Color.primaryBg)

            Link("去App Store评分", destination: URL(string: "https://apps.apple.com/app/id123456789")!)
                .listRowBackground(Color.primaryBg)

            Link("反馈问题", destination: URL(string: "https://www.kafeidazi.com/feedback/")!)
                .listRowBackground(Color.primaryBg)
        }
        .listStyle(InsetGroupedListStyle())
        .background(Color.secondaryBg.ignoresSafeArea())
        .scrollContentBackground(.hidden)
        .navigationTitle("帮助中心")
        .sheet(isPresented: $showingWhatsNew) {
            WhatsNewView()
        }
        .sheet(isPresented: $showingOnboarding) {
            OnboardingView()
        }
    }
}

struct AboutView: View {
    @EnvironmentObject var appState: AppState
    @State private var showTestButtons = false
    @StateObject private var brewLogViewModel = BrewLogViewModel()

    var body: some View {
        List {
            HStack {
                Text("版本")
                Spacer()
                Text(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0")
                    .foregroundColor(.secondary)
            }
            .listRowBackground(Color.primaryBg)

            Link("访问网站", destination: URL(string: "https://www.kafeidazi.com")!)
                .listRowBackground(Color.primaryBg)

            // 添加一个隐藏的按钮，点击可切换测试按钮显示
            Button(action: {
                showTestButtons.toggle()
            }) {
                HStack {
                    Text("测试模式")
                    Spacer()
                    Text(showTestButtons ? "已开启" : "已关闭")
                        .foregroundColor(.secondary)
                }
            }
            .listRowBackground(Color.primaryBg)

            // 内部导航测试部分
            if showTestButtons {
                Section(header: Text("内部导航测试")) {
                    Button("测试账号设置导航") {
                        appState.navigateToAccountSettings()
                    }
                    .listRowBackground(Color.primaryBg)

                    Button("测试咖啡豆详情导航") {
                        // 使用第一个咖啡豆ID（如果有）
                        if let viewModel = SharedViewModels.shared.beanListViewModel,
                           let firstBean = viewModel.coffeeBeans.first {
                            appState.navigateToBean(id: firstBean.id)
                        } else {
                            // 使用一个假的ID
                            appState.navigateToBean(id: 1)
                        }
                    }
                    .listRowBackground(Color.primaryBg)

                    Button("测试冲煮记录导航") {
                        // 使用第一个冲煮记录ID（如果有）
                        Task {
                            await brewLogViewModel.fetchFilteredRecords()
                            if let firstBrew = brewLogViewModel.brewingRecords.first {
                                appState.navigateToBrewLog(id: firstBrew.id)
                            } else {
                                // 使用一个假的ID
                                appState.navigateToBrewLog(id: 1)
                            }
                        }
                    }
                    .listRowBackground(Color.primaryBg)
                }
            }
        }
        .listStyle(InsetGroupedListStyle())
        .background(Color.secondaryBg.ignoresSafeArea())
        .scrollContentBackground(.hidden)
        .navigationTitle("关于")
        .task {
            // 加载冲煮记录数据
            if showTestButtons {
                await brewLogViewModel.fetchFilteredRecords()
            }
        }
    }


}
