import SwiftUI
import UserNotifications
import EventKit
import EventKitUI

struct NotificationsSettingsView: View {
    @EnvironmentObject var themeManager: ThemeManager
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var notificationsEnabled = false
    @State private var beanReminderEnabled = false
    @State private var reminderDaysBefore = 0
    @State private var brewReminderEnabled = false
    @State private var isCheckingBrewData = false
    @State private var showingPermissionAlert = false
    @State private var showingCalendarPermissionAlert = false
    @State private var showingSubscription = false
    @State private var isCheckingPermissions = true
    @State private var calendarAccessGranted = false

    // 添加通知服务
    private let beanNotificationService = BeanNotificationService.shared
    private let brewReminderService = BrewReminderService.shared

    // 提醒消息
    private let notificationPermissionMessage = "需要开启通知权限才能接收养豆期提醒。\n\n操作步骤：\n1. 点击\"前往设置\"\n2. 在应用设置中找到\"通知\"\n3. 开启\"允许通知\"开关\n4. 返回应用继续设置"
    private let calendarPermissionMessage = "需要开启日历权限才能创建定期备份提醒。\n\n操作步骤：\n1. 点击\"前往设置\"\n2. 在应用设置中找到\"日历\"\n3. 开启\"写入权限\"选项\n4. 返回应用继续设置"

    // 添加一个通知样式提示
    private let notificationStyleMessage = "更好的体验：\n在系统设置的通知选项中，点亮横幅并将横幅风格从“临时”改为“持续”，这样可以确保您不会错过养豆期到达的提醒。"

    var body: some View {
        PremiumFeatureWrapper {
            List {
                // 养豆期提醒部分
                Section(header: Text("养豆期提醒")) {
                    if isCheckingPermissions {
                        HStack {
                            Spacer()
                            BlinkingLoader(
                                color: .secondaryText,
                                width: 12,
                                height: 18,
                                duration: 1.5,
                                text: "检查权限中..."
                            )
                            Spacer()
                        }
                        .padding(.vertical, 8)
                        .listRowBackground(Color.primaryBg)
                    } else if !notificationsEnabled {
                        VStack(spacing: 8) {
                            HStack {
                                Image(systemName: "exclamationmark.triangle")
                                    .foregroundColor(.yellow)
                                Text("未开启通知权限")
                                    .foregroundColor(.primaryText)
                                Spacer()
                                Button("前往设置") {
                                    if let url = URL(string: UIApplication.openSettingsURLString) {
                                        UIApplication.shared.open(url)
                                    }
                                }
                                .font(.footnote)
                                .foregroundColor(.linkText)
                            }

                            Text("在系统设置中，找到\"通知\"选项，开启\"允许通知\"开关，然后再返回应用继续设置。")
                                .font(.caption)
                                .foregroundColor(.detailText)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .padding(.vertical, 4)
                        .listRowBackground(Color.primaryBg)
                    }

                    Toggle(isOn: $beanReminderEnabled) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("开启养豆期提醒")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)

                            Text("当咖啡豆到达最佳赏味期时接收通知")
                                .font(.caption)
                                .foregroundColor(.detailText)
                        }
                    }
                    .onChange(of: beanReminderEnabled) { newValue in
                        if newValue {
                            requestNotificationPermission()
                        } else {
                            // 通过服务清除所有养豆期通知
                            beanNotificationService.removeAllBeanNotifications()
                            // 保存设置
                            beanNotificationService.saveNotificationSettings(
                                enabled: false,
                                daysBefore: reminderDaysBefore
                            )
                        }
                    }
                    .disabled(!notificationsEnabled)

                    if beanReminderEnabled {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("提前提醒天数")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)

                            Text("设置在咖啡豆到达最佳赏味期前几天收到提醒")
                                .font(.caption)
                                .foregroundColor(.detailText)

                            Picker("提前天数", selection: $reminderDaysBefore) {
                                Text("当天提醒").tag(0)
                                Text("提前1天").tag(1)
                                Text("提前2天").tag(2)
                                Text("提前3天").tag(3)
                            }
                            .pickerStyle(SegmentedPickerStyle())
                            .padding(.vertical, 8)
                            .onChange(of: reminderDaysBefore) { newValue in
                                // 保存设置并重新安排通知
                                beanNotificationService.saveNotificationSettings(
                                    enabled: true,
                                    daysBefore: newValue
                                )

                                // 开发测试：添加示例通知
                                beanNotificationService.scheduleExampleNotification(daysBefore: newValue)
                            }

                            HStack {
                                Image(systemName: "lightbulb")
                                    .foregroundColor(.yellow)
                                Text(notificationStyleMessage)
                                    .font(.caption)
                                    .foregroundColor(.detailText)
                            }
                            .padding(.top, 4)

                            Button(action: {
                                if let url = URL(string: UIApplication.openSettingsURLString) {
                                    UIApplication.shared.open(url)
                                }
                            }) {
                                Text("前往通知设置调整风格")
                                    .font(.caption)
                                    .foregroundColor(.linkText)
                            }
                            .padding(.top, 2)
                        }
                        .padding(.vertical, 8)
                    }
                }
                .listRowBackground(Color.primaryBg)

                // 冲煮记录提醒部分
                Section(header: Text("冲煮记录提醒")) {
                    let hasBrewRecords = brewReminderService.hasRecentBrewRecords()

                    Toggle(isOn: $brewReminderEnabled) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("开启冲煮提醒")
                                .font(.subheadline)
                                .foregroundColor(hasBrewRecords ? .primaryText : .detailText)

                            Text(hasBrewRecords ?
                                "每天在您习惯冲煮咖啡的时间，及时收到提醒。长按通知横幅，快捷进入新增记录界面。" :
                                "每天在您习惯冲煮咖啡的时间，及时收到提醒。请先添加冲煮记录，以激活提醒功能。")
                                .font(.caption)
                                .foregroundColor(.detailText)
                        }
                    }
                    .onChange(of: brewReminderEnabled) { newValue in
                        if newValue {
                            // 用户尝试启用冲煮提醒
                            if hasBrewRecords {
                                // 如果有记录，直接启用
                                requestNotificationPermissionForBrewReminder()
                            } else {
                                // 如果没有缓存的记录，主动获取 Hindsight 数据
                                isCheckingBrewData = true

                                Task {
                                    await brewReminderService.fetchHindsightDataForBrewReminder()

                                    // 等待一段时间让数据更新
                                    try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒

                                    // 重新检查是否有数据
                                    await MainActor.run {
                                        isCheckingBrewData = false
                                        let updatedHasRecords = brewReminderService.hasRecentBrewRecords()
                                        if updatedHasRecords {
                                            requestNotificationPermissionForBrewReminder()
                                        } else {
                                            // 如果仍然没有数据，重置开关状态
                                            brewReminderEnabled = false
                                        }
                                    }
                                }
                            }
                        } else {
                            // 禁用冲煮提醒
                            brewReminderService.saveBrewReminderSettings(enabled: false)
                        }
                    }
                    .disabled(!notificationsEnabled || isCheckingBrewData)

                    if isCheckingBrewData {
                        HStack {
                            BlinkingLoader(
                                color: .secondaryText,
                                width: 8,
                                height: 12,
                                duration: 1.0,
                                showText: false
                            )
                            Text("正在检查您的冲煮记录...")
                                .font(.caption)
                                .foregroundColor(.detailText)
                        }
                        .padding(.top, 4)
                    } else if !hasBrewRecords {
                        HStack {
                            Image(systemName: "info.circle")
                                .foregroundColor(.blue)
                            Text("添加一些冲煮记录后，系统将分析您的习惯冲煮时间并提供个性化提醒。")
                                .font(.caption)
                                .foregroundColor(.detailText)
                        }
                        .padding(.top, 4)
                    }


                }
                .listRowBackground(Color.primaryBg)

                // 数据备份提醒部分
                Section(header: Text("数据备份提醒")) {
                    if !calendarAccessGranted {
                        VStack(spacing: 8) {
                            HStack {
                                Image(systemName: "exclamationmark.triangle")
                                    .foregroundColor(.yellow)
                                Text("需要日历权限")
                                    .foregroundColor(.primaryText)
                                Spacer()
                            }

                            Text("首次点击创建提醒时，系统会请求日历权限。如果您已拒绝权限，需要在系统设置中重新开启。")
                                .font(.caption)
                                .foregroundColor(.detailText)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .padding(.vertical, 4)
                        .listRowBackground(Color.primaryBg)
                    }

                    Button(action: {
                        requestCalendarPermission()
                    }) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("创建定期备份提醒")
                                    .font(.subheadline)
                                    .foregroundColor(.primaryText)

                                Text("在您的日历中添加日程，以便定期备份冲煮记录")
                                    .font(.caption)
                                    .foregroundColor(.detailText)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.functionText)
                        }
                    }
                    .listRowBackground(Color.primaryBg)
                }
            }
            .navigationTitle("通知和提醒")
            .background(Color.secondaryBg.ignoresSafeArea())
            .scrollContentBackground(.hidden)
            .alert(isPresented: $showingPermissionAlert) {
                Alert(
                    title: Text("需要通知权限"),
                    message: Text(notificationPermissionMessage),
                    primaryButton: .default(Text("前往设置"), action: {
                        if let url = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(url)
                        }
                    }),
                    secondaryButton: .cancel(Text("取消"))
                )
            }
            .alert(isPresented: $showingCalendarPermissionAlert) {
                Alert(
                    title: Text("需要日历权限"),
                    message: Text(calendarPermissionMessage),
                    primaryButton: .default(Text("前往设置"), action: {
                        if let url = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(url)
                        }
                    }),
                    secondaryButton: .cancel(Text("取消"))
                )
            }
            .sheet(isPresented: $showingSubscription) {
                NavigationView {
                    SubscriptionView()
                        .environmentObject(themeManager)
                }
            }
            .onAppear {
                // 仅当为高级版用户时才检查权限
                if subscriptionService.currentSubscriptionType == .premium {
                    // 检查并请求通知权限
                    checkAndRequestNotificationPermission()

                    // 检查并请求日历权限
                    checkAndRequestCalendarPermission()

                    // 加载保存的通知设置
                    let settings = beanNotificationService.getNotificationSettings()
                    beanReminderEnabled = settings.enabled
                    reminderDaysBefore = settings.daysBefore

                    // 加载冲煮提醒设置
                    brewReminderEnabled = brewReminderService.getBrewReminderSettings()
                } else {
                    // 非高级版用户，不检查权限
                    isCheckingPermissions = false
                }
            }
        }
    }

    // 检查并请求通知权限的组合方法
    private func checkAndRequestNotificationPermission() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                switch settings.authorizationStatus {
                case .authorized, .provisional, .ephemeral:
                    // 已获得权限
                    self.notificationsEnabled = true
                    self.isCheckingPermissions = false
                case .notDetermined:
                    // 用户尚未做出选择，主动请求权限
                    self.requestNotificationPermission()
                case .denied:
                    // 用户已拒绝，显示当前状态
                    self.notificationsEnabled = false
                    self.isCheckingPermissions = false
                @unknown default:
                    // 处理未来可能的新状态
                    self.notificationsEnabled = false
                    self.isCheckingPermissions = false
                }
            }
        }
    }

    // 请求通知权限
    private func requestNotificationPermission() {
        // 请求所有相关通知权限，包括声音、横幅、徽章和关键提醒
        let options: UNAuthorizationOptions = [
            .alert,          // 允许弹出警告
            .sound,          // 允许声音
            .badge,          // 允许应用图标上显示徽章
            .carPlay,        // 在CarPlay中显示(如果适用)
            .provisional,    // 允许临时授权(iOS 12+)
            .providesAppNotificationSettings  // 指示应用提供自己的通知设置UI
        ]

        UNUserNotificationCenter.current().requestAuthorization(options: options) { granted, error in
            DispatchQueue.main.async {
                if granted {
                    self.notificationsEnabled = true

                    // 保存通知设置并安排通知
                    self.beanNotificationService.saveNotificationSettings(
                        enabled: self.beanReminderEnabled,
                        daysBefore: self.reminderDaysBefore
                    )

                    // 保存冲煮提醒设置
                    if self.brewReminderEnabled {
                        self.brewReminderService.saveBrewReminderSettings(enabled: true)
                    }

                    // 开发测试：添加示例通知
                    if self.beanReminderEnabled {
                        self.beanNotificationService.scheduleExampleNotification(daysBefore: self.reminderDaysBefore)
                    }

                    // 设置默认通知类别
                    self.setupNotificationCategories()
                } else {
                    self.beanReminderEnabled = false
                    self.showingPermissionAlert = true
                }
                self.isCheckingPermissions = false
            }
        }
    }

    // 请求冲煮提醒的通知权限
    private func requestNotificationPermissionForBrewReminder() {
        if notificationsEnabled {
            // 如果已有通知权限，直接保存设置
            brewReminderService.saveBrewReminderSettings(enabled: true)
        } else {
            // 如果没有通知权限，先请求权限
            requestNotificationPermission()
        }
    }

    // 设置通知类别，用于定义通知的显示方式
    private func setupNotificationCategories() {
        // 创建养豆期通知的类别
        let beanCategory = UNNotificationCategory(
            identifier: "BEAN_REMINDER_CATEGORY",
            actions: [],
            intentIdentifiers: [],
            // 通知选项：允许在锁屏、通知中心和横幅中显示
            options: [.customDismissAction]
        )

        // 创建冲煮提醒通知的类别
        let addBrewAction = UNNotificationAction(
            identifier: "ADD_BREW_ACTION",
            title: "新增记录",
            options: [.foreground]
        )

        let copyLastBrewAction = UNNotificationAction(
            identifier: "COPY_LAST_BREW_ACTION",
            title: "复制上次记录",
            options: [.foreground]
        )

        let brewCategory = UNNotificationCategory(
            identifier: "brew_reminder_category",
            actions: [addBrewAction, copyLastBrewAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        // 注册通知类别
        UNUserNotificationCenter.current().setNotificationCategories([beanCategory, brewCategory])
    }

    // 检查并请求日历权限
    private func checkAndRequestCalendarPermission() {
        let checkStatus = { () -> Bool in
            if #available(iOS 17.0, *) {
                // iOS 17+使用新API检查权限
                let status = EKEventStore.authorizationStatus(for: .event)
                // 写入权限或完全访问权限都可以
                return status == .writeOnly || status == .fullAccess
            } else {
                // iOS 17以下版本
                let status = EKEventStore.authorizationStatus(for: .event)
                return status == .authorized
            }
        }

        // 检查当前权限状态
        if checkStatus() {
            // 已有权限
            self.calendarAccessGranted = true
        } else {
            // 没有权限，暂时不主动请求，等待用户点击创建备份提醒时再请求
            self.calendarAccessGranted = false
        }
    }

    // 请求日历权限
    private func requestCalendarPermission() {
        let eventStore = EKEventStore()

        // 首先检查是否已授予权限
        let checkStatus = { () -> Bool in
            if #available(iOS 17.0, *) {
                // iOS 17+使用新API检查权限
                let status = EKEventStore.authorizationStatus(for: .event)
                // 写入权限或完全访问权限都可以
                return status == .writeOnly || status == .fullAccess
            } else {
                // iOS 17以下版本
                let status = EKEventStore.authorizationStatus(for: .event)
                return status == .authorized
            }
        }

        // 如果已有权限，直接创建事件
        if checkStatus() {
            self.calendarAccessGranted = true
            self.createCalendarEvent()
            return
        }

        // 没有权限，尝试请求
        print("尝试请求日历写入权限...")

        if #available(iOS 17.0, *) {
            print("使用iOS 17+ API请求日历写入权限")
            // 只请求写入权限，而非完全访问
            eventStore.requestWriteOnlyAccessToEvents { granted, error in
                DispatchQueue.main.async {
                    print("日历写入权限请求结果: \(granted ? "获得了权限" : "拒绝了权限")")
                    if let error = error {
                        print("请求日历权限时发生错误: \(error.localizedDescription)")
                    }
                    self.handleCalendarPermissionResult(granted: granted, error: error)
                }
            }
        } else {
            print("使用iOS 17以下版本API请求日历权限")
            eventStore.requestAccess(to: .event) { granted, error in
                DispatchQueue.main.async {
                    print("日历权限请求结果: \(granted ? "获得了权限" : "拒绝了权限")")
                    if let error = error {
                        print("请求日历权限时发生错误: \(error.localizedDescription)")
                    }
                    self.handleCalendarPermissionResult(granted: granted, error: error)
                }
            }
        }
    }

    // 处理日历权限结果
    private func handleCalendarPermissionResult(granted: Bool, error: Error?) {
        DispatchQueue.main.async {
            if granted {
                self.calendarAccessGranted = true
                self.createCalendarEvent()
            } else {
                self.showingCalendarPermissionAlert = true
            }
        }
    }

    // 创建日历事件
    private func createCalendarEvent() {
        let eventStore = EKEventStore()
        let event = EKEvent(eventStore: eventStore)

        // 设置事件属性
        event.title = "备份我的冲煮记录数据(咖啡札记)"
        event.notes = "点击URL链接打开咖啡札记App的账号设置页面，找到并点击「导出所有冲煮记录」按钮即可备份您的所有冲煮记录数据为.csv表格文件。"
        event.isAllDay = true

        // 设置URL(假设有自定义URL Scheme)
        let urlString = "brewlog://account"
        event.url = URL(string: urlString)

        // 设置开始时间为明天
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: Date())!
        event.startDate = tomorrow
        event.endDate = tomorrow

        // 设置提醒
        let alarm = EKAlarm(relativeOffset: -3600) // 提前1小时提醒
        event.addAlarm(alarm)

        // 设置重复规则 - 每月重复，永不结束
        let recurrenceRule = EKRecurrenceRule(
            recurrenceWith: .monthly,
            interval: 1,
            end: nil // 永不结束
        )
        event.recurrenceRules = [recurrenceRule]

        // 创建一个编辑控制器
        let eventEditViewController = EKEventEditViewController()
        eventEditViewController.event = event
        eventEditViewController.eventStore = eventStore

        // 创建并保存委托，确保在控制器显示期间不会被释放
        let delegate = EventEditViewControllerDelegate() { success in
            // 事件编辑完成后的回调
            print("日历事件编辑完成，结果：\(success ? "成功" : "取消")")
        }
        // 保存对委托的强引用
        eventEditViewController.editViewDelegate = delegate

        // 使用属性保存对委托的引用，防止被释放
        _delegate = delegate

        // 确保在主线程中呈现视图控制器
        DispatchQueue.main.async {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let rootViewController = windowScene.windows.first?.rootViewController {
                rootViewController.present(eventEditViewController, animated: true)
            }
        }
    }

    // 用于保持对委托的强引用
    @State private var _delegate: EventEditViewControllerDelegate?
}

// 日历事件编辑视图控制器的委托
class EventEditViewControllerDelegate: NSObject, EKEventEditViewDelegate {
    private let completion: (Bool) -> Void

    init(completion: @escaping (Bool) -> Void = { _ in }) {
        self.completion = completion
        super.init()
    }

    func eventEditViewController(_ controller: EKEventEditViewController, didCompleteWith action: EKEventEditViewAction) {
        controller.dismiss(animated: true) {
            // 根据用户操作决定回调结果
            let success = action != .canceled
            self.completion(success)
        }
    }
}

struct NotificationsSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            NotificationsSettingsView()
                .environmentObject(ThemeManager.shared)
        }
    }
}