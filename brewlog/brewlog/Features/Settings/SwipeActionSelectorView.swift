import SwiftUI

struct SwipeActionSelectorView: View {
    let sectionType: ListSectionType
    let direction: SwipeDirection

    @State private var selectedAction: SwipeAction
    @ObservedObject private var gestureSettings = GestureSettings.shared
    @Environment(\.presentationMode) var presentationMode

    init(sectionType: ListSectionType, direction: SwipeDirection, selectedAction: SwipeAction) {
        self.sectionType = sectionType
        self.direction = direction
        self._selectedAction = State(initialValue: selectedAction)
    }

    var body: some View {
        List {
            actionSection
            helpSection
        }
        .navigationTitle("\(direction.rawValue)轻扫设置")
        .navigationBarTitleDisplayMode(.inline)
        .background(Color.secondaryBg)
        .scrollContentBackground(.hidden)
    }

    // 操作选择区域
    private var actionSection: some View {
        Section(header: Text("可用操作").font(.footnote).foregroundColor(.secondary)) {
            ForEach(SwipeAction.availableActions(for: sectionType)) { action in
                actionRow(for: action)
            }
        }
    }

    // 帮助说明区域
    private var helpSection: some View {
        Section(header: Text("说明").font(.footnote).foregroundColor(.secondary)) {
            Text("为\(sectionType.rawValue)的\(direction.rawValue)轻扫选择一个动作。\n\n选择\"无\"将禁用此方向的轻扫选项。\n\n已在其他方向选择的动作不可重复选择。")
                .font(.footnote)
                .foregroundColor(.secondary)
        }
    }

    // 单个操作行
    private func actionRow(for action: SwipeAction) -> some View {
        Button(action: {
            selectAction(action)
        }) {
            HStack {
                actionIcon(for: action)
                actionTitle(for: action)
                Spacer()
                actionStatus(for: action)
            }
        }
        .disabled(isActionDisabled(action))
    }

    // 操作图标
    private func actionIcon(for action: SwipeAction) -> some View {
        Group {
            if action == .edit || action == .restock || action == .quickBrew || action == .portal || action == .tag || action == .rename {
                // 使用自定义图标
                Image(action.icon)
                    .foregroundColor(action.color)
                    .frame(width: 28, height: 28)
                    .background(
                        Circle()
                            .fill(action.color.opacity(0.15))
                    )
            } else {
                // 使用系统图标
                Image(systemName: action.icon)
                    .foregroundColor(action.color)
                    .frame(width: 28, height: 28)
                    .background(
                        Circle()
                            .fill(action.color.opacity(0.15))
                    )
            }
        }
    }

    // 操作标题
    private func actionTitle(for action: SwipeAction) -> some View {
        Text(action.rawValue)
            .foregroundColor(isUsedInOtherDirection(action) ? Color.archivedText : .primary)
    }

    // 操作状态（选中标记或使用提示）
    private func actionStatus(for action: SwipeAction) -> some View {
        Group {
            if selectedAction == action {
                Image(systemName: "checkmark")
                    .foregroundColor(.accentColor)
            } else if isUsedInOtherDirection(action) {
                // 显示在哪个方向已经使用了
                let otherDirection: SwipeDirection = direction == .left ? .right : .left
                Text("(\(otherDirection.rawValue)轻扫)")
                    .font(.caption2)
                    .foregroundColor(Color.archivedText)
            }
        }
    }

    // 选择操作
    private func selectAction(_ action: SwipeAction) {
        selectedAction = action
        gestureSettings.updateSetting(sectionType: sectionType, direction: direction, action: action)

        // 操作选择后返回上一页
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            presentationMode.wrappedValue.dismiss()
        }
    }

    // 检查操作是否应该被禁用（已在其他方向选择的非"无"操作）
    private func isActionDisabled(_ action: SwipeAction) -> Bool {
        // "无"选项始终可选
        if action == .none {
            return false
        }

        // 当前已选择的动作始终可选
        if action == selectedAction {
            return false
        }

        // 获取该列表类型的所有已使用的操作
        let usedActions = gestureSettings.getUsedActions(for: sectionType)

        // 如果该操作已被其他方向使用，则禁用
        return usedActions.contains(action)
    }

    // 检查操作是否已在其他方向使用
    private func isUsedInOtherDirection(_ action: SwipeAction) -> Bool {
        // 获取该列表类型的所有已使用的操作
        let usedActions = gestureSettings.getUsedActions(for: sectionType)

        // 如果该操作已被其他方向使用，则返回true
        return usedActions.contains(action)
    }
}

struct SwipeActionSelectorView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            SwipeActionSelectorView(
                sectionType: .brewLog,
                direction: .left,
                selectedAction: .compare
            )
        }
    }
}