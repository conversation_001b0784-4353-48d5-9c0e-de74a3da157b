import SwiftUI

struct TabOrderEditorView: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.dismiss) var dismiss
    @EnvironmentObject var themeManager: ThemeManager
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var showingSubscription = false
    
    // 本地状态
    @State private var tabOrder: [AppState.Tab] = []
    @State private var moreItems: [AppState.Tab] = []
    
    var body: some View {
        PremiumFeatureWrapper {
            List {
                Section(header: Text("菜单栏显示的页面").font(.footnote).foregroundColor(.noteText)) {
                    ForEach(tabOrder, id: \.self) { tab in
                        HStack {
                            Label {
                                Text(tab.title)
                            } icon: {
                                tab.icon
                            }
                            
                            Spacer()
                            
                            // 添加下移到更多菜单按钮
                            Button {
                                moveToMoreMenu(tab)
                            } label: {
                                Image(systemName: "pin.slash.fill")
                                    .foregroundColor(.linkText)
                            }
                        }
                        .listRowBackground(Color.primaryBg)
                    }
                    .onMove { source, destination in
                        // 在显示菜单内部移动
                        tabOrder.move(fromOffsets: source, toOffset: destination)
                        saveChanges()
                    }
                }
                
                Section(header: Text("更多菜单中的页面").font(.footnote).foregroundColor(.noteText)) {
                    if moreItems.isEmpty {
                        Text("没有更多页面")
                            .foregroundColor(.secondary)
                            .italic()
                    } else {
                        ForEach(moreItems, id: \.self) { tab in
                            HStack {
                                Label {
                                    Text(tab.title)
                                } icon: {
                                    tab.icon
                                }
                                
                                Spacer()
                                
                                // 添加上移到菜单栏按钮
                                if tabOrder.count < 4 {
                                    Button {
                                        moveToMainMenu(tab)
                                    } label: {
                                        Image(systemName: "pin.fill")
                                            .foregroundColor(.linkText)
                                    }
                                }
                            }
                            .listRowBackground(Color.primaryBg)
                        }
                        .onMove { source, destination in
                            // 在更多菜单内部移动
                            moreItems.move(fromOffsets: source, toOffset: destination)
                            saveChanges()
                        }
                    }
                }
                
                // 说明部分
                Section(header: Text("说明").font(.footnote).foregroundColor(.noteText)) {
                    VStack(alignment: .leading, spacing: 8) {
                        if tabOrder.count >= 4 {
                            HStack {
                                Image(systemName: "pin.slash.fill")
                                    .foregroundColor(.linkText)
                                Text("底部菜单栏最多显示4个页面，如需添加新页面，请先点击移除一个现有页面。")
                                    .font(.footnote)
                                    .foregroundColor(.detailText)
                            }
                        }
                        
                        HStack {
                            Image(systemName: "hand.draw")
                                .foregroundColor(.linkText)
                            Text("长按并拖动可在同一区域内排序。")
                                .font(.footnote)
                                .foregroundColor(.detailText)
                        }
                    }
                }
                .listRowBackground(Color.primaryBg)
            }
            .listStyle(InsetGroupedListStyle())
            .background(Color.secondaryBg.ignoresSafeArea())
            .scrollContentBackground(.hidden)
        }
        .navigationTitle("菜单顺序")
        .environment(\.editMode, .constant(.active))  // 自动开启修改模式
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("重置") {
                    resetOrder()
                }
            }
        }
        .onAppear {
            // 加载数据
            tabOrder = appState.tabOrder
            moreItems = appState.moreItems
            
            // 打印调试信息
            print("菜单栏页面: \(tabOrder.count)个")
            print("更多菜单页面: \(moreItems.count)个")
        }
        .onDisappear {
            // 保存变更
            saveChanges()
        }
    }
    
    // 将某个标签从更多菜单移动到主菜单
    private func moveToMainMenu(_ tab: AppState.Tab) {
        if tabOrder.count < 4 && moreItems.contains(tab) {
            // 从更多菜单移除
            moreItems.removeAll { $0 == tab }
            
            // 添加到菜单栏
            tabOrder.append(tab)
            
            // 保存更改
            saveChanges()
        }
    }
    
    // 将某个标签从主菜单移动到更多菜单
    private func moveToMoreMenu(_ tab: AppState.Tab) {
        if tabOrder.contains(tab) {
            // 从菜单栏移除
            tabOrder.removeAll { $0 == tab }
            
            // 添加到更多菜单
            moreItems.append(tab)
            
            // 保存更改
            saveChanges()
        }
    }
    
    // 保存更改到 AppState
    private func saveChanges() {
        // 确保两个列表之间没有重复项
        let duplicates = Set(tabOrder).intersection(Set(moreItems))
        for duplicate in duplicates {
            // 如果出现重复，优先保留在主菜单中的标签
            moreItems.removeAll { $0 == duplicate }
        }
        
        appState.tabOrder = tabOrder
        appState.moreItems = moreItems
        appState.saveTabOrder()
    }
    
    // 重置为默认顺序
    private func resetOrder() {
        appState.resetTabOrder()
        tabOrder = appState.tabOrder
        moreItems = appState.moreItems
    }
}

#Preview {
    NavigationView {
        TabOrderEditorView()
            .environmentObject(AppState.shared)
            .environmentObject(ThemeManager.shared)
    }
}