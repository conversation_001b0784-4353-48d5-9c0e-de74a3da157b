import SwiftUI

struct ThemeSettingsView: View {
    @EnvironmentObject private var themeManager: ThemeManager
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var showingSubscription = false
    
    // 使用默认值初始化状态变量
    @State private var selectedLightTheme: ThemeType = .latte
    @State private var selectedDarkTheme: ThemeType = .espresso
    @State private var selectedMode: ThemeMode = .system
    
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.colorScheme) var colorScheme
    
    // 添加UI刷新触发器
    @State private var refreshTrigger = UUID()
    
    // 定义浅色和深色主题列表
    private let lightThemes: [ThemeType] = [.latte, .matcha, .caramel, .mocha]
    private let darkThemes: [ThemeType] = [.espresso, .coldBrew, .americano, .macchiato]
    
    var body: some View {
        PremiumFeatureWrapper {
            List {
                // 主题模式选择部分
                Section(header: Text("主题模式")) {
                    ForEach(ThemeMode.allCases, id: \.self) { mode in
                        Button(action: {
                            // 仅当选择了不同的模式时才应用更改
                            if selectedMode != mode {
                                selectedMode = mode
                                themeManager.setThemeMode(mode)
                            }
                        }) {
                            HStack {
                                // 添加不同模式对应的图标
                                getIconForMode(mode)
                                    .font(.system(size: 20))
                                    .frame(width: 32, height: 32)
                                    .foregroundColor(getColorForMode(mode))
                                
                                Text(mode.rawValue)
                                Spacer()
                                if selectedMode == mode {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(Color.secondaryText)
                                }
                            }
                            .contentShape(Rectangle()) // 确保整行都是可点击的
                        }
                        .buttonStyle(PlainButtonStyle())
                        .frame(maxWidth: .infinity) // 使按钮占据整行
                        .id("\(mode.rawValue)_\(refreshTrigger)") // 添加刷新ID
                    }
                }
                .listRowBackground(Color.primaryBg)
                
                // 浅色主题设置部分
                Section(header: Text("浅色主题")) {
                    ForEach(lightThemes, id: \.self) { theme in
                        ThemeCell(theme: theme, isSelected: selectedLightTheme == theme) {
                            // 仅当选择了不同的主题时才应用更改
                            if selectedLightTheme != theme {
                                selectedLightTheme = theme
                                themeManager.setTheme(light: theme, dark: selectedDarkTheme)
                            }
                        }
                        .id("\(theme.rawValue)_\(refreshTrigger)") // 添加刷新ID
                    }
                }
                .listRowBackground(Color.primaryBg)
                
                // 深色主题设置部分
                Section(header: Text("深色主题")) {
                    ForEach(darkThemes, id: \.self) { theme in
                        ThemeCell(theme: theme, isSelected: selectedDarkTheme == theme) {
                            // 仅当选择了不同的主题时才应用更改
                            if selectedDarkTheme != theme {
                                selectedDarkTheme = theme
                                themeManager.setTheme(light: selectedLightTheme, dark: theme)
                            }
                        }
                        .id("\(theme.rawValue)_\(refreshTrigger)") // 添加刷新ID
                    }
                }
                .listRowBackground(Color.primaryBg)
                
                // 高级版功能提示部分
                if subscriptionService.currentSubscriptionType != .premium {
                    Section {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("高级版功能")
                                .font(.headline)
                            
                            Text("升级到高级版，即可为浅色和深色模式分别设置不同的主题色")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Button {
                                showingSubscription = true
                            } label: {
                                Text("升级到高级版")
                                    .frame(maxWidth: .infinity)
                            }
                            .buttonStyle(.borderedProminent)
                            .padding(.top, 4)
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
            .navigationTitle("主题设置")
            .scrollContentBackground(.hidden)
            .background(Color.secondaryBg)
            .sheet(isPresented: $showingSubscription) {
                NavigationView {
                    SubscriptionView()
                        .environmentObject(themeManager)
                }
            }
            .onAppear {
                // 在视图出现时更新选中主题状态
                updateUIState()
                
                // 应用正确的主题（特别是在系统模式下）
                if themeManager.themeMode == .system {
                    themeManager.updateThemeColorsOnly()
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ThemeDidChange"))) { _ in
                // 主题变化时更新UI状态并触发刷新
                updateUIState()
                refreshTrigger = UUID() // 强制刷新UI
            }
            // 添加系统特性变化监听
            .onReceive(NotificationCenter.default.publisher(for: TraitChangeObserver.traitChangeNotification)) { _ in
                if themeManager.themeMode == .system {
                    // 更新UI并强制刷新
                    themeManager.updateThemeColorsOnly()
                    refreshTrigger = UUID()
                }
            }
            .preferredColorScheme(themeManager.preferredColorScheme())
        }
    }
    
    // 更新UI状态的统一方法
    private func updateUIState() {
        self.selectedLightTheme = themeManager.currentLightTheme
        self.selectedDarkTheme = themeManager.currentDarkTheme
        self.selectedMode = themeManager.themeMode
    }
    
    // 获取主题模式对应的图标
    private func getIconForMode(_ mode: ThemeMode) -> Image {
        switch mode {
        case .system:
            return Image("systemmode.symbols")
        case .light:
            return Image("daymode.symbols")
        case .dark:
            return Image("nightmode.symbols")
        }
    }
    
    // 获取主题模式对应的图标颜色
    private func getColorForMode(_ mode: ThemeMode) -> Color {
        switch mode {
        case .system:
            return .primary
        case .light:
            return Color.yellow // 太阳的黄色
        case .dark:
            return Color.blue // 月亮的蓝色
        }
    }
}

// 主题预览单元格
struct ThemeCell: View {
    let theme: ThemeType
    let isSelected: Bool
    let onSelect: () -> Void
    
    @EnvironmentObject private var themeManager: ThemeManager
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 12) {
                // 主题色样本
                RoundedRectangle(cornerRadius: 6)
                    .fill(themeManager.themeColorsFor(theme).secondaryTextColor)
                    .frame(width: 22, height: 22)
                    .clipShape(Capsule())
                
                Text(theme.rawValue)
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundColor(Color.secondaryText)
                }
            }
            .contentShape(Rectangle()) // 确保整行可点击
        }
        .buttonStyle(PlainButtonStyle())
        .frame(maxWidth: .infinity) // 使按钮占据整行
        .disabled(themeManager.isChangingTheme) // 主题切换过程中禁用按钮
    }
}