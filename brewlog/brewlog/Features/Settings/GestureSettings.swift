import Foundation
import SwiftUI

// 列表页面类型
enum ListSectionType: String, Codable, CaseIterable, Identifiable {
    case brewLog = "冲煮记录列表"
    case coffeeBean = "咖啡豆列表"
    case equipment = "设备列表"
    case recipe = "配方列表"

    var id: String { self.rawValue }

    var icon: String {
        switch self {
        case .brewLog:
            return "record.symbols"
        case .coffeeBean:
            return "paperbag.symbols"
        case .equipment:
            return "equipment.symbols"
        case .recipe:
            return "book.closed"
        }
    }
}

enum SwipeAction: String, Codable, CaseIterable, Identifiable {
    case none = "无"
    case delete = "删除"
    case edit = "修改"
    case compare = "对比"
    case favorite = "首选"
    case archive = "归档"
    case copyRecipe = "复制配方"
    case restock = "回购"
    case quickBrew = "速记"
    case portal = "传送门"
    case tag = "贴标签"
    case rename = "重命名"

    var id: String { self.rawValue }

    var icon: String {
        switch self {
        case .none:
            return "xmark.circle"
        case .delete:
            return "trash"
        case .edit:
            return "edit.symbols"
        case .compare:
            return "arrow.left.arrow.right"
        case .favorite:
            return "star"
        case .archive:
            return "archivebox"
        case .copyRecipe:
            return "doc.on.doc"
        case .restock:
            return "beanOccurrence.symbols"
        case .quickBrew:
            return "bolt"
        case .portal:
            return "portal.symbols"
        case .tag:
            return "recipeTags.symbols"
        case .rename:
            return "recipeRename.symbols"
        }
    }

    var color: Color {
        switch self {
        case .none:
            return .gray
        case .delete:
            return .red
        case .edit:
            return .green
        case .compare:
            return .blue
        case .favorite:
            return .yellow
        case .archive:
            return .purple
        case .copyRecipe:
            return .orange
        case .restock:
            return .teal
        case .quickBrew:
            return .blue
        case .portal:
            return .indigo
        case .tag:
            return .mint
        case .rename:
            return .cyan
        }
    }

    // 根据列表类型返回可用的操作
    static func availableActions(for sectionType: ListSectionType) -> [SwipeAction] {
        switch sectionType {
        case .brewLog:
            // 冲煮记录列表不显示首选和归档选项，但显示复制配方选项
            return [.none, .delete, .edit, .compare, .copyRecipe]
        case .coffeeBean:
            // 咖啡豆列表和设备列表不显示对比选项和复制配方选项，但咖啡豆列表显示回购选项
            return [.none, .delete, .edit, .favorite, .archive, .restock]
        case .equipment:
            // 设备列表不显示对比选项、复制配方选项和回购选项
            return [.none, .delete, .edit, .favorite, .archive]
        case .recipe:
            // 配方列表显示速记、传送门、贴标签、重命名选项
            return [.none, .quickBrew, .portal, .tag, .rename]
        }
    }
}

enum SwipeDirection: String, Codable, CaseIterable, Identifiable {
    case left = "向左"
    case right = "向右"

    var id: String { self.rawValue }
}

struct SwipeGestureSetting: Codable, Identifiable {
    var id = UUID()
    var sectionType: ListSectionType
    var direction: SwipeDirection
    var action: SwipeAction

    // 用于NSCoding兼容
    private enum CodingKeys: String, CodingKey {
        case id, sectionType, direction, action
    }
}

class GestureSettings: ObservableObject {
    static let shared = GestureSettings()

    @Published var swipeGestureSettings: [SwipeGestureSetting] = []

    private let userDefaultsKey = "swipe_gesture_settings_v2" // 更新key以避免与旧版本冲突
    private let subscriptionService = SubscriptionService.shared

    private init() {
        loadSettings()

        // 如果没有设置，创建默认设置
        if swipeGestureSettings.isEmpty {
            createDefaultSettings()
        } else {
            // 验证和修复现有设置
            validateAndFixSettings()
        }
    }

    // 创建默认设置
    private func createDefaultSettings() {
        // 为冲煮记录列表设置默认值（删除和修改）
        swipeGestureSettings = [
            SwipeGestureSetting(sectionType: .brewLog, direction: .left, action: .delete),
            SwipeGestureSetting(sectionType: .brewLog, direction: .right, action: .edit),
            // 为咖啡豆列表设置默认值（删除和编辑）
            SwipeGestureSetting(sectionType: .coffeeBean, direction: .left, action: .delete),
            SwipeGestureSetting(sectionType: .coffeeBean, direction: .right, action: .edit),
            // 为设备列表设置默认值（删除和编辑）
            SwipeGestureSetting(sectionType: .equipment, direction: .left, action: .delete),
            SwipeGestureSetting(sectionType: .equipment, direction: .right, action: .edit),
            // 为配方列表设置默认值（传送门和速记）
            SwipeGestureSetting(sectionType: .recipe, direction: .left, action: .portal),
            SwipeGestureSetting(sectionType: .recipe, direction: .right, action: .tag)
        ]
        saveSettings()
    }

    // 加载设置
    private func loadSettings() {
        guard let data = UserDefaults.standard.data(forKey: userDefaultsKey) else { return }

        do {
            let settings = try JSONDecoder().decode([SwipeGestureSetting].self, from: data)
            self.swipeGestureSettings = settings
        } catch {
            print("无法加载轻扫手势设置: \(error)")
        }
    }

    // 保存设置
    func saveSettings() {
        do {
            let data = try JSONEncoder().encode(swipeGestureSettings)
            UserDefaults.standard.set(data, forKey: userDefaultsKey)
        } catch {
            print("无法保存轻扫手势设置: \(error)")
        }
    }

    // 获取特定列表页面和方向的轻扫操作
    func getAction(for sectionType: ListSectionType, direction: SwipeDirection) -> SwipeAction {
        if let setting = swipeGestureSettings.first(where: { $0.sectionType == sectionType && $0.direction == direction }) {
            return setting.action
        }

        // 默认设置
        switch sectionType {
        case .recipe:
            return direction == .left ? .portal : .quickBrew
        default:
            return direction == .left ? .delete : .edit
        }
    }

    // 更新轻扫设置
    func updateSetting(sectionType: ListSectionType, direction: SwipeDirection, action: SwipeAction) {
        // 检查操作是否在可用列表中
        let availableActions = SwipeAction.availableActions(for: sectionType)
        guard availableActions.contains(action) else {
            print("试图设置不可用的操作: \(action.rawValue) 对于 \(sectionType.rawValue)")
            return
        }

        // 检查是否已存在该设置
        if let index = swipeGestureSettings.firstIndex(where: { $0.sectionType == sectionType && $0.direction == direction }) {
            // 更新现有设置
            swipeGestureSettings[index].action = action
        } else {
            // 添加新设置
            let newSetting = SwipeGestureSetting(sectionType: sectionType, direction: direction, action: action)
            swipeGestureSettings.append(newSetting)
        }
        saveSettings()
    }

    // 获取已使用的动作列表（用于检查某个列表页面中已选择的操作）
    func getUsedActions(for sectionType: ListSectionType) -> [SwipeAction] {
        return swipeGestureSettings
            .filter { $0.sectionType == sectionType && $0.action != .none }
            .map { $0.action }
    }

    // 重置为默认设置
    func resetToDefault() {
        createDefaultSettings()
    }

    // 检查用户是否有权使用轻扫选项（自定义手势）
    var canUseCustomGestures: Bool {
        // 检查是否有权访问此高级功能
        let gestureFeature = SubscriptionFeature.allFeatures.first { $0.title == "轻扫选项" }
        if let feature = gestureFeature {
            return subscriptionService.hasAccess(to: feature)
        }
        return false
    }

    // 验证并修复设置，确保所有设置都符合可用操作规则
    private func validateAndFixSettings() {
        var needsSaving = false

        // 检查每个设置项
        for (index, setting) in swipeGestureSettings.enumerated() {
            let availableActions = SwipeAction.availableActions(for: setting.sectionType)

            // 如果当前操作不在可用列表中，修复它
            if setting.action != .none && !availableActions.contains(setting.action) {
                // 将操作修改为默认操作（左滑删除，右滑编辑）
                if setting.direction == .left {
                    swipeGestureSettings[index].action = .delete
                } else {
                    swipeGestureSettings[index].action = .edit
                }
                needsSaving = true
            }
        }

        // 如果有修改，保存设置
        if needsSaving {
            saveSettings()
        }
    }
}