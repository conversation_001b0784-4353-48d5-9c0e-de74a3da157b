import SwiftUI

// 私有进度环组件
private struct CircularProgressView: View {
    var progress: Double
    var strokeWidth: CGFloat
    var color: Color
    
    var body: some View {
        ZStack {
            Circle()
                .trim(from: 0, to: CGFloat(min(progress, 1.0)))
                .stroke(style: StrokeStyle(
                    lineWidth: strokeWidth,
                    lineCap: .round,
                    lineJoin: .round
                ))
                .foregroundColor(color)
                .rotationEffect(.degrees(-90))
        }
    }
}

struct EquipmentProgressRings: View {
    // 折旧率（外环）
    var depreciationRate: Double?
    // 回本进度（内环）
    var breakEvenProgress: Double?
    
    // 添加环境变量，获取主题管理器
    @EnvironmentObject private var themeManager: ThemeManager
    
    // 获取外环颜色 - 固定为绿色
    private var depreciationColor: Color {
        // 无值时显示灰色
        guard depreciationRate != nil else { return .gray }
        return .green
    }
    
    // 获取内环颜色 - 固定为橙色
    private var breakEvenColor: Color {
        // 无值时显示灰色
        guard breakEvenProgress != nil else { return .gray }
        return .orange
    }
    
    // 判断是否显示外环
    private var shouldShowDepreciationRing: Bool {
        depreciationRate != nil
    }
    
    // 判断是否显示内环
    private var shouldShowBreakEvenRing: Bool {
        breakEvenProgress != nil
    }
    
    // 计算折旧率百分比 (0-1)
    private var depreciationPercentage: Double {
        guard let rate = depreciationRate else { return 0 }
        return rate / 100.0
    }
    
    // 计算回本进度百分比 (0-1)
    private var breakEvenPercentage: Double {
        guard let progress = breakEvenProgress else { return 0 }
        return min(progress, 100) / 100.0
    }
    
    var body: some View {
        ZStack {
            // 外环(折旧率) - 仅当有折旧率时显示
            if shouldShowDepreciationRing {
                CircularProgressView(
                    progress: depreciationPercentage,
                    strokeWidth: 4,
                    color: depreciationColor
                )
            }
            
            // 内环(回本进度) - 仅当有回本进度时显示
            if shouldShowBreakEvenRing {
                CircularProgressView(
                    progress: breakEvenPercentage,
                    strokeWidth: 4,
                    color: breakEvenColor
                )
                .padding(5)  // 设置内环与外环的间距
            }
            
            // 中心文字
            VStack(spacing: 2) {
                // 折旧率显示 - 仅当有折旧率时显示
                if shouldShowDepreciationRing, let rate = depreciationRate {
                    if rate >= 99.5 || Int(rate) == 100 {
                        Text("全新")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(themeManager.currentThemeColors.primaryTextColor)
                    } else {
                        Text("\(NumberFormatters.formatWithPrecision(rate, precision: 0))新")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(themeManager.currentThemeColors.primaryTextColor)
                    }
                }
                
                // 回本进度显示 - 仅当有回本进度时显示
                if shouldShowBreakEvenRing, let progress = breakEvenProgress {
                    Text("\(NumberFormatters.formatWithPrecision(progress, precision: 0))%")
                        .font(.system(size: 10))
                        .foregroundColor(breakEvenColor)
                }
            }
            .frame(width: 34, height: 34)
            .clipShape(Circle())
        }
        .frame(width: 56, height: 56)
    }
}

#if DEBUG
struct EquipmentProgressRings_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 无数据
            EquipmentProgressRings()
            
            // 仅折旧率
            EquipmentProgressRings(
                depreciationRate: 85,
                breakEvenProgress: nil
            )
            
            // 仅回本进度
            EquipmentProgressRings(
                depreciationRate: nil,
                breakEvenProgress: 75
            )
            
            // 两者都有（良好状态，投资中）
            EquipmentProgressRings(
                depreciationRate: 95,
                breakEvenProgress: 30
            )
            
            // 两者都有（中等状态，接近回本）
            EquipmentProgressRings(
                depreciationRate: 65,
                breakEvenProgress: 85
            )
            
            // 两者都有（较旧状态，已回本）
            EquipmentProgressRings(
                depreciationRate: 45,
                breakEvenProgress: 100
            )
        }
        .environmentObject(ThemeManager.shared)
        // 添加深色模式预览
        .preferredColorScheme(.dark)
    }
}
#endif 