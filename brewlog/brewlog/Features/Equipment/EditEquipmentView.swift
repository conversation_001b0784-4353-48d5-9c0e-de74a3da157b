import SwiftUI

struct EditEquipmentView: View {
    let viewModel: EquipmentViewModel
    
    @Environment(\.dismiss) var dismiss
    @FocusState private var focusedField: String?
    let equipment: Equipment
    
    @State private var name: String
    @State private var type: String
    @State private var brand: String
    @State private var brewMethod: String
    @State private var notes: String
    @State private var purchaseDate: Date
    @State private var purchasePrice: String
    @State private var grindSizePreset: String
    @State private var grinderPurpose: String
    @State private var selectedGadgets: [Int] = []
    
    @State private var availableGadgets: [Equipment] = []
    @State private var isSaving = false
    @State private var errorMessage: String?
    
    init(equipment: Equipment, viewModel: EquipmentViewModel) {
        self.equipment = equipment
        self.viewModel = viewModel
        
        _name = State(initialValue: equipment.name)
        _type = State(initialValue: equipment.type)
        _brand = State(initialValue: equipment.brand ?? "")
        _notes = State(initialValue: equipment.notes ?? "")
        _purchaseDate = State(initialValue: equipment.purchaseDate ?? Date())
        _purchasePrice = State(initialValue: equipment.purchasePrice.map { String($0) } ?? "")
        _brewMethod = State(initialValue: equipment.brewMethod ?? "POUR_OVER")
        _grindSizePreset = State(initialValue: equipment.grindSizePreset ?? "")
        _grinderPurpose = State(initialValue: equipment.grinderPurpose ?? "ALL_PURPOSE")
        
        // 解析gadgetComponents数组并初始化selectedGadgets
        if let components = equipment.gadgetComponents {
            _selectedGadgets = State(initialValue: components.compactMap { $0["id"] as? Int })
        } else {
            _selectedGadgets = State(initialValue: [])
        }
    }
    
    // 检查表单是否已被修改
    private var isFormModified: Bool {
        // 基本信息比较
        if name != equipment.name ||
           brand != (equipment.brand ?? "") ||
           notes != (equipment.notes ?? "") {
            return true
        }
        
        // 购买日期比较
        let equipmentPurchaseDate = equipment.purchaseDate ?? Date()
        // 直接比较时间戳，而不是检查是否为同一天
        if abs(purchaseDate.timeIntervalSince(equipmentPurchaseDate)) > 1 { // 允许1秒的误差
            return true
        }
        
        // 购买价格比较
        let originalPriceString = equipment.purchasePrice.map { String($0) } ?? ""
        if purchasePrice != originalPriceString {
            return true
        }
        
        // 冲煮器具特定字段比较
        if type == Equipment.typeBrewingEquipment {
            if brewMethod != (equipment.brewMethod ?? "POUR_OVER") {
                return true
            }
        }
        
        // 磨豆机特定字段比较
        if type == Equipment.typeGrindingEquipment {
            if grindSizePreset != (equipment.grindSizePreset ?? "") ||
               grinderPurpose != (equipment.grinderPurpose ?? "ALL_PURPOSE") {
                return true
            }
        }
        
        // 小工具组合特定字段比较
        if type == Equipment.typeGadgetKit {
            let originalGadgets = equipment.gadgetComponents?.compactMap { $0["id"] as? Int } ?? []
            if selectedGadgets.count != originalGadgets.count {
                return true
            }
            
            // 对比所有选中的小工具ID是否一致
            let sortedOriginal = originalGadgets.sorted()
            let sortedSelected = selectedGadgets.sorted()
            for (index, gadgetId) in sortedSelected.enumerated() {
                if index >= sortedOriginal.count || gadgetId != sortedOriginal[index] {
                    return true
                }
            }
        }
        
        return false
    }
    
    var body: some View {
        ZStack(alignment: .top) {
            // 固定的顶部导航栏
            topNavigationBar
                .background(Color.primaryBg)
                .zIndex(1)
            
            ScrollView {
                VStack(spacing: 8) {
                    // 添加一个占位符，高度等于导航栏高度
                    Color.clear
                        .frame(height: 52)
                    
                    // 主要内容区域
                    VStack(spacing: 16) {
                        // 设备类型选择区域（只读展示类型，不可更改）
                        equipmentTypeSection
                        
                        // 基本信息区域
                        basicInfoSection
                        
                        // 详细信息区域（根据设备类型显示不同内容）
                        detailsSection
                        
                        // 小工具组合内容（仅当类型是小工具组合时显示）
                        if type == Equipment.typeGadgetKit {
                            gadgetKitSection
                        }
                        
                        // 备注区域
                        notesSection
                    }
                    .padding(.horizontal, 12)
                    .padding(.bottom, 20)
                }
            }
            .background(
                Color.primaryBg
                    .onTapGesture {
                        hideKeyboard()
                    }
            )
        }
        .alert(isPresented: Binding<Bool>(
            get: { errorMessage != nil },
            set: { if !$0 { errorMessage = nil } }
        )) {
            Alert(
                title: Text("保存失败"),
                message: Text(errorMessage ?? "发生未知错误"),
                dismissButton: .default(Text("确定"))
            )
        }
        .overlay(
            Group {
                if isSaving {
                    ZStack {
                        Color.black.opacity(0.4)

                        BlinkingLoader(
                            color: .primaryBg,
                            width: 12,
                            height: 18,
                            duration: 1.5,
                            text: "保存中..."
                        )
                        .padding()
                        .background(Color.secondaryBg.opacity(0.9))
                        .cornerRadius(10)
                        .shadow(radius: 5)
                    }
                }
            }
        )
        .onAppear {
            if type == Equipment.typeGadgetKit {
                loadAvailableGadgets()
            }
        }
    }
    
    // 顶部导航栏
    private var topNavigationBar: some View {
        ZStack {
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(.linkText)
                }
                
                Spacer()
                
                Button(action: saveEquipment) {
                    Text("保存")
                        .fontWeight(.medium)
                        .foregroundColor((!name.isEmpty && (type != Equipment.typeGadgetKit || !selectedGadgets.isEmpty)) && isFormModified && !isSaving ? .linkText : Color.gray.opacity(0.5))
                }
                .disabled(name.isEmpty || (type == Equipment.typeGadgetKit && selectedGadgets.isEmpty) || !isFormModified || isSaving)
            }
            
            Text("修改设备")
                .font(.headline)
                .foregroundColor(.primaryText)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
    }
    
    // 设备类型选择区域 - 只读展示，不可修改
    private var equipmentTypeSection: some View {
        VStack(spacing: 20) {
            // 设备类型显示
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Text("设备类型")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(不可更改)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
                // 显示当前类型（不可选择）
                HStack {
                    Text(getTypeDisplay(type))
                        .font(.system(size: 16))
                        .foregroundColor(.detailText)
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .cornerRadius(12)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(.horizontal, 4)
            }
            .padding(.horizontal, 16)
            
            // 冲煮器具类型的额外选项
            if type == Equipment.typeBrewingEquipment {
                VStack(alignment: .leading, spacing: 10) {
                    Text("赛道")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Menu {
                        Button("意式") { brewMethod = "ESPRESSO" }
                        Button("手冲") { brewMethod = "POUR_OVER" }
                        Button("爱乐压") { brewMethod = "AEROPRESS" }
                        Button("冷萃") { brewMethod = "COLD_BREW" }
                        Button("摩卡壶") { brewMethod = "MOKA_POT" }
                        Button("法压壶") { brewMethod = "FRENCH_PRESS" }
                        Button("自动滴滤") { brewMethod = "AUTO_DRIP" }
                    } label: {
                        HStack {
                            Text(getBrewMethodDisplay(brewMethod))
                                .font(.system(size: 16))
                                .foregroundColor(.primaryText)
                            Spacer()
                            Image(systemName: "chevron.down")
                                .foregroundColor(.primaryText)
                        }
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                    }
                }
                .padding(.horizontal, 16)
            }
            
            // 磨豆机类型的额外选项
            if type == Equipment.typeGrindingEquipment {
                VStack(alignment: .leading, spacing: 10) {
                    Text("磨豆机用途")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Menu {
                        Button("意式") { grinderPurpose = "ESPRESSO" }
                        Button("手冲") { grinderPurpose = "POUR_OVER" }
                        Button("通用") { grinderPurpose = "ALL_PURPOSE" }
                    } label: {
                        HStack {
                            Text(getGrinderPurposeDisplay(grinderPurpose))
                                .font(.system(size: 16))
                                .foregroundColor(.primaryText)
                            Spacer()
                            Image(systemName: "chevron.down")
                                .foregroundColor(.primaryText)
                        }
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                    }
                    
                    formFieldSimple(title: "研磨设置", binding: $grindSizePreset, placeholder: "目前所设置的刻度或档位", removePadding: true, isOptional: true)
                }
                .padding(.horizontal, 16)
            }
        }
        .padding(.top, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 基本信息区域
    private var basicInfoSection: some View {
        VStack(spacing: 20) {
            if type != Equipment.typeGadgetKit {
                formFieldSimple(title: "名称", binding: $name, placeholder: "请输入设备名称")
                formFieldSimple(title: "品牌", binding: $brand, placeholder: "请输入品牌", isOptional: true)
            } else {
                formFieldSimple(title: "名称", binding: $name, placeholder: "请输入设备名称")
            }
        }
        .padding(.top, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 详细信息区域
    private var detailsSection: some View {
        Group {
            if type != Equipment.typeGadgetKit {
                VStack(spacing: 20) {
                    // 购买日期和时间选择器
                    VStack(alignment: .leading, spacing: 8) {
                        Text("购买时间")
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(.primaryText)
                        
                        // 使用系统的直接可见DatePicker但自定义样式
                        DatePicker(
                            "",
                            selection: $purchaseDate,
                            displayedComponents: [.date, .hourAndMinute]
                        )
                        .datePickerStyle(CompactDatePickerStyle())
                        .labelsHidden()
                        .accentColor(.functionText)
                        .cornerRadius(12)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 16)
                    
                    // 购买价格
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("购买价格 (元)")
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.primaryText)
                            
                            Text("(选填)")
                                .font(.caption)
                                .foregroundColor(.noteText)
                            
                            Spacer()
                        }
                        
                        TextField("0.00", text: $purchasePrice)
                            .focused($focusedField, equals: "购买价格_field")
                            .keyboardType(.decimalPad)
                            .submitLabel(.done)
                            .font(.system(size: 17))
                            .padding(.vertical, 14)
                            .padding(.horizontal, 16)
                            .background(Color.secondaryBg)
                            .cornerRadius(12)
                            .onChange(of: purchasePrice) { newValue in
                                // 过滤非法输入
                                let filtered = newValue.filter { "0123456789.".contains($0) }
                                
                                // 确保只有一个小数点
                                var components = filtered.components(separatedBy: ".")
                                if components.count > 2 {
                                    components = [components[0], components[1]]
                                    purchasePrice = components.joined(separator: ".")
                                } else if filtered != newValue {
                                    purchasePrice = filtered
                                }
                                
                                // 如果开头是小数点，在前面添加0
                                if purchasePrice.first == "." {
                                    purchasePrice = "0" + purchasePrice
                                }
                            }
                    }
                    .padding(.horizontal, 16)
                }
                .padding(.top, 16)
                .background(Color.primaryBg)
                .cornerRadius(10)
            }
        }
    }
    
    // 小工具组合内容区域
    private var gadgetKitSection: some View {
        VStack(spacing: 20) {
            Text("组合内容")
                .font(.system(size: 16, weight: .regular))
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal, 16)
            
            if availableGadgets.isEmpty {
                Text("没有可用的小工具，请先添加小工具")
                    .foregroundColor(.secondary)
                    .padding(.vertical, 20)
                    .frame(maxWidth: .infinity)
                    .background(Color.secondaryBg.opacity(0.15))
                    .cornerRadius(8)
                    .padding(.horizontal, 16)
            } else {
                VStack(spacing: 0) {
                    ForEach(availableGadgets) { gadget in
                        Button(action: {
                            // 只允许切换未禁用的小工具
                            if !gadget.isDisabled {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0.2)) {
                                    toggleGadgetSelection(gadgetId: gadget.id)
                                }
                            }
                        }) {
                            HStack(spacing: 12) {
                                // 圆形勾选框
                                ZStack {
                                    Circle()
                                        .stroke(selectedGadgets.contains(gadget.id) ? Color.functionText : Color.gray.opacity(0.5), lineWidth: 1.5)
                                        .frame(width: 22, height: 22)
                                        .opacity(gadget.isDisabled ? 0.5 : 1)
                                    
                                    if selectedGadgets.contains(gadget.id) {
                                        Circle()
                                            .fill(Color.functionText)
                                            .frame(width: 22, height: 22)
                                            .opacity(gadget.isDisabled ? 0.5 : 1)
                                        
                                        Image(systemName: "checkmark")
                                            .font(.system(size: 12, weight: .bold))
                                            .foregroundColor(.white)
                                            .opacity(gadget.isDisabled ? 0.5 : 1)
                                            .transition(.scale.combined(with: .opacity))
                                    }
                                }
                                .contentShape(Rectangle())
                                
                                // 文本内容，使用VStack在需要时能自动换行而不会产生过多右侧留白
                                VStack(alignment: .leading, spacing: 2) {
                                    if gadget.brand != nil && !gadget.brand!.isEmpty {
                                        Text("\(gadget.brand!)")
                                            .foregroundColor(gadget.isDisabled ? .gray : .noteText)
                                            + Text(" \(gadget.name)")
                                            .foregroundColor(gadget.isDisabled ? .gray : .primaryText)
                                    } else {
                                        Text(gadget.name)
                                            .foregroundColor(gadget.isDisabled ? .gray : .primaryText)
                                    }
                                    
                                    // 如果小工具已被归档或删除，显示状态标签
                                    if gadget.isDisabled {
                                        if gadget.isArchived {
                                            Text("已归档")
                                                .font(.caption)
                                                .foregroundColor(.gray)
                                                .padding(.horizontal, 8)
                                                .padding(.vertical, 2)
                                                .background(Color.gray.opacity(0.1))
                                                .cornerRadius(8)
                                        } else if gadget.isDeleted {
                                            Text("已删除")
                                                .font(.caption)
                                                .foregroundColor(.gray)
                                                .padding(.horizontal, 8)
                                                .padding(.vertical, 2)
                                                .background(Color.gray.opacity(0.1))
                                                .cornerRadius(8)
                                        }
                                    }
                                }
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .layoutPriority(1)
                            }
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)
                            .contentShape(Rectangle())
                            .background(
                                selectedGadgets.contains(gadget.id) 
                                ? Color.secondaryBg.opacity(gadget.isDisabled ? 0.15 : 0.3) 
                                : Color.clear
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                        .disabled(gadget.isDisabled)
                        
                        if gadget.id != availableGadgets.last?.id {
                            Divider()
                                .padding(.horizontal, 16)
                        }
                    }
                }
                .background(Color.secondaryBg)
                .cornerRadius(12)
                .padding(.horizontal, 16)
            }
        }
        .padding(.top, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 备注区域
    private var notesSection: some View {
        VStack(spacing: 20) {
            // 备注文本编辑器
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Text("备注")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                    
                    Spacer()
                    
                    // 字数统计
                    Text("\(notes.count)/500")
                        .font(.caption)
                        .foregroundColor(notes.count >= 500 ? .error : .primaryText)
                }
                
                ZStack(alignment: .topLeading) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                        .submitLabel(.done)
                        .focused($focusedField, equals: "备注_field")
                        .scrollContentBackground(.hidden)
                        .background(Color.clear)
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.secondaryBg)
                        )
                        .onChange(of: notes) { newValue in
                            if newValue.count > 500 {
                                notes = String(newValue.prefix(500))
                            }
                        }
                    
                    if notes.isEmpty {
                        Text("请输入备注信息")
                            .foregroundColor(.gray)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 12)
                            .background(Color.clear)
                            .allowsHitTesting(false)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 简化版表单字段
    private func formFieldSimple(title: String, binding: Binding<String>, placeholder: String? = nil, removePadding: Bool = false, isOptional: Bool = false) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                if isOptional {
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
                
                Spacer()
            }
            
            TextField(placeholder ?? "请输入\(title)", text: binding)
                .focused($focusedField, equals: "\(title)_field")
                .submitLabel(.done)
                .font(.system(size: 17))
                .padding(.vertical, 14)
                .padding(.horizontal, 16)
                .background(Color.secondaryBg)
                .cornerRadius(12)
        }
        .padding(.horizontal, removePadding ? 0 : 16)
    }
    
    // 获取设备类型显示名称
    private func getTypeDisplay(_ type: String) -> String {
        switch type {
        case Equipment.typeBrewingEquipment:
            return "冲煮器具"
        case Equipment.typeGrindingEquipment:
            return "磨豆机"
        case Equipment.typeGadget:
            return "小工具"
        case Equipment.typeGadgetKit:
            return "小工具组合"
        default:
            return "未知"
        }
    }
    
    // 隐藏键盘方法
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    private func toggleGadgetSelection(gadgetId: Int) {
        if let index = selectedGadgets.firstIndex(of: gadgetId) {
            selectedGadgets.remove(at: index)
        } else {
            selectedGadgets.append(gadgetId)
        }
    }
    
    private func loadAvailableGadgets() {
        Task {
            let gadgets = await viewModel.fetchAvailableGadgets(forEquipment: equipment.id)
            await MainActor.run {
                self.availableGadgets = gadgets
            }
        }
    }
    
    private func getBrewMethodDisplay(_ method: String) -> String {
        switch method {
        case "POUR_OVER": return "手冲"
        case "ESPRESSO": return "意式"
        case "AEROPRESS": return "爱乐压"
        case "COLD_BREW": return "冷萃"
        case "MOKA_POT": return "摩卡壶"  
        case "FRENCH_PRESS": return "法压壶"
        case "AUTO_DRIP": return "自动滴滤"
        default: return "其他"
        }
    }
    
    private func getGrinderPurposeDisplay(_ purpose: String) -> String {
        switch purpose {
        case "ESPRESSO": return "意式"
        case "POUR_OVER": return "手冲"
        case "ALL_PURPOSE": return "通用"
        default: return "未知"
        }
    }
    
    private func saveEquipment() {
        isSaving = true
        
        // 准备API请求数据 - 只包含客户端可修改的字段
        var requestData: [String: Any] = [
            "id": equipment.id,
            "name": name,
            "type": type,
            "is_favorite": equipment.isFavorite,
            "is_archived": equipment.isArchived,
            "is_active": equipment.isActive
        ]
        
        // 添加非空的可选字段
        if !brand.isEmpty { 
            requestData["brand"] = brand 
        }
        
        if !notes.isEmpty { 
            requestData["notes"] = notes 
        }
        
        // 设置购买日期 - 通过created_at字段
        requestData["created_at"] = Int(purchaseDate.timeIntervalSince1970)
        
        // 购买价格（如果有）
        if !purchasePrice.isEmpty, let price = Double(purchasePrice), price >= 0 {
            requestData["purchase_price"] = price
        }
        
        // 根据设备类型添加特定字段
        if type == Equipment.typeBrewingEquipment {
            requestData["brew_method"] = brewMethod
        } else if type == Equipment.typeGrindingEquipment {
            if !grindSizePreset.isEmpty {
                requestData["grind_size_preset"] = grindSizePreset
            }
            requestData["grinder_purpose"] = grinderPurpose
        } else if type == Equipment.typeGadgetKit && !selectedGadgets.isEmpty {
            requestData["gadget_components"] = selectedGadgets
        }
        
        // 注意：不包含以下服务端计算字段
        // - depreciation_rate（折旧率）
        // - last_used（最后使用时间）
        // - usage_count（使用次数）
        // - break_even_progress（收支平衡进度）
        
        Task {
            do {
                print("💾 正在保存设备数据: ID \(equipment.id)")
                // 使用更新API调用更新设备
                try await viewModel.updateEquipmentWithData(requestData)
                
                // 添加短暂延迟确保服务器处理完成
                try? await Task.sleep(nanoseconds: 500_000_000) // 500毫秒延迟
                
                // 创建临时的EquipmentDetailViewModel来获取最新数据
                let detailViewModel = EquipmentDetailViewModel()
                // 从服务器获取更新后的设备数据
                if let updatedEquipment = await detailViewModel.loadEquipmentById(equipment.id, forceRefresh: true) {
                    // 确保修改已写入服务器数据库后再发送通知
                    await MainActor.run {
                        // 检查设备是否从未使用过，如果使用次数为0并且最后使用时间与购买时间相同
                        // 则创建一个修正的设备对象，将last_used设为nil
                        var equipmentToNotify = updatedEquipment
                        
                        if (updatedEquipment.usageCount == 0 || updatedEquipment.usageCount == nil) && 
                           updatedEquipment.lastUsed != nil && 
                           updatedEquipment.purchaseDate != nil && 
                           abs(updatedEquipment.lastUsed!.timeIntervalSince(updatedEquipment.purchaseDate!)) < 1 {
                            // 创建一个包含相同数据但last_used为nil的新设备对象
                            equipmentToNotify = Equipment(
                                id: updatedEquipment.id,
                                name: updatedEquipment.name,
                                type: updatedEquipment.type,
                                typeDisplay: updatedEquipment.typeDisplay,
                                brand: updatedEquipment.brand,
                                model: updatedEquipment.model,
                                description: updatedEquipment.description,
                                purchaseDate: updatedEquipment.purchaseDate,
                                purchasePrice: updatedEquipment.purchasePrice,
                                notes: updatedEquipment.notes,
                                createdAt: updatedEquipment.createdAt,
                                updatedAt: updatedEquipment.updatedAt,
                                isDeleted: updatedEquipment.isDeleted,
                                isArchived: updatedEquipment.isArchived,
                                isActive: updatedEquipment.isActive,
                                isFavorite: updatedEquipment.isFavorite,
                                isDisabled: updatedEquipment.isDisabled,
                                brewMethod: updatedEquipment.brewMethod,
                                brewMethodDisplay: updatedEquipment.brewMethodDisplay,
                                gadgetComponents: updatedEquipment.gadgetComponents,
                                grindSizePreset: updatedEquipment.grindSizePreset,
                                grinderPurpose: updatedEquipment.grinderPurpose,
                                grinderPurposeDisplay: updatedEquipment.grinderPurposeDisplay,
                                usageCount: updatedEquipment.usageCount,
                                lastUsed: nil, // 显式设置为nil
                                breakEvenProgress: updatedEquipment.breakEvenProgress,
                                depreciationRate: updatedEquipment.depreciationRate
                            )
                        }
                        
                        // 发送单一通知，传递完整的更新后的设备对象
                        NotificationCenter.default.post(
                            name: NSNotification.Name("EquipmentUpdated"),
                            object: equipmentToNotify
                        )
                        print("📣 已发送设备更新通知（包含完整设备数据）: ID \(equipment.id)")
                        
                        // 关闭编辑表单
                        isSaving = false
                        dismiss()
                    }
                } else {
                    // 如果无法获取更新后的数据，显示错误
                    throw APIError.serverErrorSimple("无法获取更新后的设备数据")
                }
            } catch {
                await MainActor.run {
                    isSaving = false
                    errorMessage = error.localizedDescription
                    print("❌ 保存设备失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 添加一个格式化日期的辅助函数
    private func formattedDate(_ date: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy年MM月dd日 HH:mm"
        dateFormatter.locale = Locale(identifier: "zh_CN")
        return dateFormatter.string(from: date)
    }
}

#if DEBUG
struct EditEquipmentView_Previews: PreviewProvider {
    static var previews: some View {
        EditEquipmentView(equipment: Equipment.previewBrewing, viewModel: EquipmentViewModel())
    }
}
#endif 