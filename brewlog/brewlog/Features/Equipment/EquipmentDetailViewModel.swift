import Foundation
import SwiftUI

class EquipmentDetailViewModel: ObservableObject {
    // 状态属性
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    func loadEquipmentById(_ id: Int, forceRefresh: Bool = false) async -> Equipment? {
        // 如果正在加载，就返回nil
        guard !isLoading else { return nil }
        
        // 设置加载状态
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        // 在函数结束时清除加载状态
        defer {
            Task { @MainActor in
                isLoading = false
            }
        }
        
        do {
            // 构建API URL，添加强制刷新参数
            var urlComponents = URLComponents(string: "\(APIService.shared.getBaseURL())/ios/api/equipment/\(id)/")
            
            // 添加URL参数
            var queryItems: [URLQueryItem] = []
            
            // 添加时间戳参数避免缓存问题
            queryItems.append(URLQueryItem(name: "_", value: "\(Date().timeIntervalSince1970)"))
            
            // 如果强制刷新，添加force_refresh参数
            if forceRefresh {
                queryItems.append(URLQueryItem(name: "force_refresh", value: "true"))
            }
            
            urlComponents?.queryItems = queryItems
            
            guard let finalUrl = urlComponents?.url else {
                throw APIError.invalidURL
            }
            
            // 创建请求
            var request = URLRequest(url: finalUrl)
            request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            // 设置缓存策略
            if forceRefresh {
                // 强制刷新时忽略本地缓存
                request.cachePolicy = URLRequest.CachePolicy.reloadIgnoringLocalCacheData
                request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
                request.addValue("no-cache", forHTTPHeaderField: "Pragma")
                print("🔍 正在强制刷新设备详情数据: ID \(id)")
            } else {
                print("🔍 正在获取设备详情数据: ID \(id)")
            }
            
            // 发送请求
            let (data, response) = try await URLSession.shared.data(for: request)
            
            // 检查响应状态码
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }
            
            if httpResponse.statusCode == 200 {
                // 打印响应数据用于调试
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("📥 设备详情响应: \(jsonString.prefix(200))...")
                }
                
                // 使用JSONDecoder解析数据
                let decoder = JSONDecoder()
                
                // 使用灵活的日期解码策略
                decoder.dateDecodingStrategy = .custom({ decoder in
                    let container = try decoder.singleValueContainer()
                    if let dateString = try? container.decode(String.self) {
                        let formatter = DateFormatter()
                        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
                        if let date = formatter.date(from: dateString) {
                            return date
                        }
                        
                        // 尝试更多格式
                        let formats = ["yyyy-MM-dd'T'HH:mm:ssZ", "yyyy-MM-dd"]
                        for format in formats {
                            formatter.dateFormat = format
                            if let date = formatter.date(from: dateString) {
                                return date
                            }
                        }
                        return Date()
                    } else if let timestamp = try? container.decode(Double.self) {
                        return Date(timeIntervalSince1970: timestamp)
                    }
                    return Date()
                })
                
                // 在解码前检查JSON中是否last_used字段与purchase_date相同
                if let jsonObject = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    let lastUsed = jsonObject["last_used"] as? TimeInterval
                    let purchaseDate = jsonObject["purchase_date"] as? TimeInterval
                    let usageCount = jsonObject["usage_count"] as? Int
                    
                    // 如果设备没有被使用过（usage_count为0或nil）且last_used与purchase_date相同，
                    // 则手动修改JSON，将last_used设为null
                    if (usageCount == 0 || usageCount == nil) && lastUsed == purchaseDate {
                        var modifiedJSON = jsonObject
                        modifiedJSON["last_used"] = NSNull()
                        
                        // 使用修改后的JSON重新编码为Data
                        let modifiedData = try JSONSerialization.data(withJSONObject: modifiedJSON)
                        let equipment = try decoder.decode(Equipment.self, from: modifiedData)
                        return equipment
                    }
                }
                
                // 如果不需要特殊处理，则直接解码
                let equipment = try decoder.decode(Equipment.self, from: data)
                return equipment
            } else {
                // 处理错误响应
                if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorData["detail"] as? String {
                    throw APIError.serverError(httpResponse.statusCode, errorMessage)
                } else {
                    throw APIError.serverError(httpResponse.statusCode, "服务器返回错误：\(httpResponse.statusCode)")
                }
            }
        } catch let error as APIError {
            // 在主线程更新UI状态
            await MainActor.run {
                self.errorMessage = error.userFriendlyMessage
            }
            print("❌ 加载设备数据失败: \(error.userFriendlyMessage)")
            return nil
        } catch {
            // 处理其他错误
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
            print("❌ 加载设备数据失败: \(error.localizedDescription)")
            return nil
        }
    }
} 