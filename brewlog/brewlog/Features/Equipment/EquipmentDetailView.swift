import SwiftUI

// 导入共享组件
// 注意：我们依赖于这些组件，它们在项目中有定义
// SectionDivider 在 Components/SectionDivider.swift
// FlowLayout、FlexableStack 和 CustomDivider 在 BeanDetailView.swift 也定义
// ShareImageView 在 BrewLogDetailView.swift 中定义

// 自定义点击按钮组件，支持轻点和长按反馈
struct TapableButton<Content: View>: View {
    var action: () -> Void
    var content: () -> Content

    @State private var isTapped = false

    init(action: @escaping () -> Void, @ViewBuilder content: @escaping () -> Content) {
        self.action = action
        self.content = content
    }

    var body: some View {
        content()
            .background(
                ZStack {
                    // 背景高亮
                    RoundedRectangle(cornerRadius: 8)
                        .fill(isTapped ? Color.secondaryBg : Color.clear)

                    // 边框
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(isTapped ? Color.secondaryText.opacity(0.3) : Color.clear, lineWidth: 1)
                }
            )
            .scaleEffect(isTapped ? 0.98 : 1.0)
            .opacity(isTapped ? 0.9 : 1.0)
            .animation(.easeInOut(duration: 0.08), value: isTapped)
            .onTapGesture {
                // 轻点效果
                withAnimation(.easeInOut(duration: 0.08)) {
                    isTapped = true
                }

                // 延迟后恢复并执行操作
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                    withAnimation(.easeInOut(duration: 0.08)) {
                        isTapped = false
                    }
                    action()
                }
            }
            .contentShape(Rectangle()) // 确保整个区域可点击
    }
}

// 保留原来的PressableButtonStyle为备用
struct PressableButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .background(
                ZStack {
                    // 背景高亮
                    RoundedRectangle(cornerRadius: 8)
                        .fill(configuration.isPressed ? Color.secondaryBg : Color.clear)

                    // 边框
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(configuration.isPressed ? Color.secondaryText.opacity(0.3) : Color.clear, lineWidth: 1)
                }
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .opacity(configuration.isPressed ? 0.9 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// 添加一个ObservableObject来管理EquipmentDetailView的状态
class EquipmentDetailViewState: ObservableObject {
    @Published var refreshedEquipment: Equipment?
    @Published var isRefreshing: Bool = false
    @Published var refreshError: Error?

    // 截图相关状态
    @Published var isGeneratingImage: Bool = false
}

struct EquipmentDetailView: View {
    let equipment: Equipment
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var appState: AppState
    @StateObject private var viewModel = EquipmentDetailViewModel()
    @State private var showingEditView = false
    @Environment(\.displayScale) var displayScale
    @Environment(\.colorScheme) var colorScheme
    @ObservedObject private var themeManager = ThemeManager.shared

    // 使用ObservableObject来管理状态
    @StateObject private var viewState = EquipmentDetailViewState()

    // 移除内部导航状态，改为使用AppState统一管理

    // 添加一个ID标识，确保视图重建时使用新的实例
    let viewID = UUID()

    init(equipment: Equipment) {
        self.equipment = equipment
    }

    var body: some View {
        ZStack {
            ScrollView {
                equipmentDetailContent
                    .overlay(
                        Group {
                            if viewState.isRefreshing {
                                BlinkingLoader(
                                    color: .primaryText,
                                    width: 12,
                                    height: 18,
                                    duration: 1.5,
                                    text: "刷新中..."
                                )
                                .frame(width: 120, height: 50)
                                .background(Color.primaryBg.opacity(0.8))
                                .cornerRadius(10)
                            }
                        }
                    )
            }
            .background(Color.primaryBg)
            .navigationBarTitleDisplayMode(.inline)

            // 移除内部NavigationLink，改为使用AppState统一管理导航
        }
        .toolbar {
            ToolbarItem(placement: .principal) {
                Text("设备详情")
                    .font(.headline)
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                HStack {
                    Button(action: {
                        // 长截图功能
                        Task {
                            await generateImage()
                        }
                    }) {
                        if viewState.isGeneratingImage {
                            BlinkingLoader(
                                color: .primaryText,
                                width: 8,
                                height: 12,
                                duration: 1.0,
                                showText: false
                            )
                        } else {
                            Image("screenshot.symbols")
                        }
                    }
                    .disabled(viewState.isGeneratingImage)

                    Button(action: {
                        showingEditView = true
                    }) {
                        Image("edit.symbols")
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditView) {
            NavigationView {
                // 设备编辑表单
                EditEquipmentView(equipment: currentEquipment, viewModel: EquipmentViewModel())
                    .onDisappear {
                        // 当编辑表单关闭时，强制刷新数据，确保显示最新内容
                        Task {
                            await refreshEquipmentData(forceRefresh: true)
                        }
                    }
            }
        }
        .id(viewID) // 确保视图使用唯一ID，避免复用
        .onAppear {
            themeManager.updateThemeColorsOnly()

            // 重置状态，确保每次打开都是新的状态
            viewState.refreshedEquipment = nil
            viewState.isGeneratingImage = false

            // 移除内部导航状态重置

            print("🔄 EquipmentDetailView出现 - 设备ID: \(equipment.id), 名称: \(equipment.name)")

            // 延迟数据刷新，避免在导航过程中立即触发刷新导致状态冲突
            Task {
                // 等待导航动画完成
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                await refreshEquipmentData(forceRefresh: false) // 改为非强制刷新，减少副作用
            }
        }
        .onDisappear {
            // 确保截图状态被重置
            viewState.isGeneratingImage = false

            // 移除内部导航状态重置

            print("👋 EquipmentDetailView消失 - 设备ID: \(equipment.id), 名称: \(equipment.name)")
        }
        .onReceive(NotificationCenter.default.publisher(for: TraitChangeObserver.traitChangeNotification)) { _ in
            themeManager.updateThemeColorsOnly()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("EquipmentUpdated"))) { notification in
            if let updatedEquipment = notification.object as? Equipment, updatedEquipment.id == equipment.id {
                // 接收到当前设备的更新通知，直接使用通知中的数据更新视图
                Task {
                    print("🔄 收到设备更新通知，更新视图: ID \(updatedEquipment.id)")
                    await MainActor.run {
                        self.viewState.refreshedEquipment = updatedEquipment
                        print("✅ 设备详情数据已通过通知更新: ID \(updatedEquipment.id)")

                        // 触发视图刷新
                        self.viewState.objectWillChange.send()
                    }
                }
            }
        }
        // 移除内部导航逻辑，改为使用AppState统一管理
    }

    // 获取当前显示的设备数据
    private var currentEquipment: Equipment {
        viewState.refreshedEquipment ?? equipment
    }

    // 刷新设备数据的方法，增加强制刷新选项
    private func refreshEquipmentData(forceRefresh: Bool = false) async {
        // 设置加载状态
        await MainActor.run {
            viewState.isRefreshing = true
            viewState.refreshError = nil
        }

        // 使用viewModel获取最新的设备数据，传入强制刷新参数
        if let refreshedEquipment = await viewModel.loadEquipmentById(equipment.id, forceRefresh: forceRefresh) {
            // 在主线程更新UI
            await MainActor.run {
                // 将刷新后的设备数据设置为当前设备，更新视图
                self.viewState.refreshedEquipment = refreshedEquipment
                print("✅ 设备详情数据已刷新: ID \(refreshedEquipment.id)")

                // 触发视图刷新
                self.viewState.objectWillChange.send()
                viewState.isRefreshing = false
            }
        } else {
            await MainActor.run {
                print("❌ 刷新设备详情失败: ID \(equipment.id)")
                viewState.isRefreshing = false
            }
        }
    }

    // 设备详情内容
    var equipmentDetailContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 卡片视图
            ZStack(alignment: .leading) {
                // 卡片主体 - 居中展示
                ZStack(alignment: .topLeading) {
                    // 外边框
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.primaryBg)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.functionText, lineWidth: 2)
                        )

                    VStack(spacing: 0) {
                        // 中层：设备基本信息
                        VStack(alignment: .center, spacing: 8) {
                            // 设备名称
                            Text(currentEquipment.name)
                                .font(.title3)
                                .fontWeight(.bold)
                                .multilineTextAlignment(.center)
                                .padding(.top, 32)
                                .padding(.horizontal, 16)
                                .frame(maxWidth: .infinity)

                            // 品牌名称
                            if let brand = currentEquipment.brand {
                                Text(brand)
                                    .font(.headline)
                                    .fontWeight(.regular)
                                    .foregroundColor(.primaryText.opacity(0.7))
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                            }

                            // 购买价格(选填)和时间
                            if let purchaseDate = currentEquipment.purchaseDate {
                                VStack(alignment: .leading, spacing: 4) {
                                    if let price = currentEquipment.purchasePrice, price > 0 {
                                        // 有购买价格时显示价格和日期
                                        HStack(spacing: 4) {
                                            Text("💰 \(NumberFormatters.formatWithPrecision(price, precision: 2))元")
                                            .font(.caption)
                                            Text("\(currentEquipment.type == "GADGET_KIT" ? "创建于" : "购于") \(purchaseDate.formattedDateShort())")
                                            .font(.caption)
                                            .foregroundColor(.primaryText.opacity(0.5))
                                        }
                                    } else {
                                        // 没有购买价格时只显示日期
                                        Text("\(currentEquipment.type == "GADGET_KIT" ? "创建于" : "购于") \(purchaseDate.formattedDateShort())")
                                        .font(.caption)
                                        .foregroundColor(.primaryText.opacity(0.5))
                                    }
                                }
                            }

                            Group {
                                Divider()
                                    .frame(height: 2.0)
                                    .background(Color.functionText)
                            }
                            .frame(width: 260)
                            .padding(.vertical, 28)

                            // 详细属性
                            attributesContainer()
                                .padding(.horizontal, 16)
                                .padding(.bottom, 16)
                        }
                    }
                    .padding(16)
                    .frame(maxWidth: .infinity)
                }
                .padding(.horizontal, 30)
                .padding(.bottom, 10)

                // 设备状态指示条
                EquipmentStatusBar(equipment: currentEquipment)
                    .frame(width: 6)
                    .padding(.top, 10)
                    .padding(.leading, 16)
            }
            .padding(.top, 8)

            // 备注(选填)
            if !(currentEquipment.notes?.isEmpty ?? true) {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 8) {
                        Image("note.symbols")
                            .font(.callout)
                            .foregroundColor(.functionText)

                        Text("备注")
                            .font(.callout)
                            .fontWeight(.medium)
                            .foregroundColor(.functionText)
                    }

                    Text(currentEquipment.notes ?? "")
                        .font(.callout)
                        .foregroundColor(.primaryText)
                        .fixedSize(horizontal: false, vertical: true)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(16)
                .frame(maxWidth: .infinity)
                .background(Color.navbarBg)
                .cornerRadius(8)
                .padding(.horizontal, 30)
            }

            // 组合内容 (如果是小工具组合且有组件)
            if currentEquipment.type == "GADGET_KIT", let components = currentEquipment.gadgetComponents, !components.isEmpty {
                SectionDivider(title: "组合内容")
                    .padding(.horizontal, 32)

                VStack(alignment: .leading, spacing: 2) {
                    ForEach(components.indices, id: \.self) { index in
                        if let componentName = components[index]["name"] as? String,
                           let componentId = components[index]["id"] as? Int {

                            let brand = components[index]["brand"] as? String ?? ""

                            NavigationLink(destination: EquipmentDetailView(equipment: Equipment.placeholder(id: componentId, name: componentName, brand: brand))) {
                                HStack {
                                    HStack(alignment: .center, spacing: 8) {
                                        Image("gadgetKit")
                                            .resizable()
                                            .renderingMode(.template)
                                            .foregroundColor(Color.secondaryText.opacity(0.5))
                                            .frame(width: 20, height: 17.777)

                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(componentName)
                                                .foregroundColor(.primaryText)
                                                .lineLimit(1)

                                            if !brand.isEmpty {
                                                Text(brand)
                                                    .font(.caption)
                                                    .foregroundColor(.primaryText.opacity(0.6))
                                                    .lineLimit(1)
                                            }
                                        }
                                    }

                                    Spacer()

                                    // 添加导航箭头指示
                                    Image(systemName: "chevron.right")
                                        .font(.caption)
                                        .foregroundColor(.secondaryText.opacity(0.5))
                                }
                                .padding(.vertical, 12)
                                .padding(.horizontal, 16)
                                .background(Color.clear)
                                .contentShape(Rectangle())
                            }
                        }
                    }
                }
                .padding(.horizontal, 32)
            }

            // 使用数据
            SectionDivider(title: "提要")
                .padding(.horizontal, 32)

            // 居中容器
            VStack(alignment: .center) {
                // 使用FlowLayout替代HStack，实现自动换行
                FlowLayout(spacing: 12) {
                    // 使用次数
                    VStack(spacing: 2) {
                        Text("使用次数")
                            .font(.footnote)
                            .foregroundColor(.detailText)

                        Text("\(currentEquipment.usageCount ?? 0)")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.primaryText)

                        Text("次")
                            .font(.caption)
                            .foregroundColor(.detailText)
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.secondaryBg)
                    .cornerRadius(8)

                    // 最近使用
                    if let lastUsed = currentEquipment.lastUsed {
                        let daysSince = Calendar.current.dateComponents([.day], from: lastUsed, to: Date()).day ?? 0
                        VStack(spacing: 2) {
                            Text("最近使用")
                                .font(.footnote)
                                .foregroundColor(.detailText)

                            // 根据天数显示不同的文本
                            if daysSince == 0 {
                                Text("今天")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.primaryText)

                                // 添加空白占位符，确保高度一致
                                Text(" ")
                                    .font(.caption)
                                    .foregroundColor(.clear)
                            } else if daysSince == 1 {
                                Text("昨天")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.primaryText)

                                // 添加空白占位符，确保高度一致
                                Text(" ")
                                    .font(.caption)
                                    .foregroundColor(.clear)
                            } else {
                                Text("\(daysSince)")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.primaryText)

                                Text("天前")
                                    .font(.caption)
                                    .foregroundColor(.detailText)
                            }
                        }
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(Color.secondaryBg)
                        .cornerRadius(8)
                    } else {
                        // 设备还未使用过的显示状态
                        VStack(spacing: 2) {
                            Text("最近使用")
                                .font(.footnote)
                                .foregroundColor(.detailText)

                            Text("-")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.primaryText)

                            Text("尚未使用")
                                .font(.caption)
                                .foregroundColor(.detailText)
                        }
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(Color.secondaryBg)
                        .cornerRadius(8)
                    }
                }
                .frame(maxWidth: .infinity)
            }
            .padding(.horizontal, 32)
            .padding(.bottom, 32)
        }
        .foregroundColor(.primaryText)
        .padding(.vertical, 16)
    }

    // 获取详细属性列表
    private func getDetailAttributes() -> [(String, String?, [(Int, String)]?)] {
        var attributes: [(String, String?, [(Int, String)]?)] = []

        // 添加设备类型信息
        let typeDisplay: String
        if currentEquipment.type == "BREWER" {
            typeDisplay = "冲煮器具"
        } else if currentEquipment.type == "GRINDER" {
            typeDisplay = "磨豆机"
        } else if currentEquipment.type == "GADGET" {
            typeDisplay = "小工具"
        } else if currentEquipment.type == "GADGET_KIT" {
            typeDisplay = "小工具组合"
        } else {
            typeDisplay = "未知设备"
        }
        attributes.append(("类型", typeDisplay, nil))

        // 型号信息
        if let model = currentEquipment.model, !model.isEmpty {
            attributes.append(("型号", model, nil))
        }

        // 根据设备类型添加特定属性
        if currentEquipment.type == "BREWER" {
            if let brewMethod = currentEquipment.brewMethodDisplay {
                attributes.append(("赛道", brewMethod, nil))
            }
        } else if currentEquipment.type == "GRINDER" {
            if let grinderPurpose = currentEquipment.grinderPurposeDisplay {
                attributes.append(("用途", grinderPurpose, nil))
            }
            if let grindSizePreset = currentEquipment.grindSizePreset, !grindSizePreset.isEmpty {
                attributes.append(("研磨设置", grindSizePreset, nil))
            }
        }

        // 添加描述信息
        if let description = currentEquipment.description, !description.isEmpty {
            attributes.append(("描述", description, nil))
        }

        return attributes
    }

    // 用于长截图的包装视图
    private var screenshotWrapperView: some View {
        ZStack {
            // 外层背景色
            Color(red: 0xAB/255.0, green: 0xB8/255.0, blue: 0xC3/255.0)
                .edgesIgnoringSafeArea(.all)

            // 通过嵌套ZStack确保阴影只应用于最外层
            ZStack {
                // 内容卡片背景 - 用于应用圆角、边框和阴影
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.primaryBg)
                    .shadow(color: Color.black.opacity(0.15), radius: 15, x: 0, y: 12)

                // 内容区域 - 不应用阴影
                VStack(spacing: 0) {
                    // 去掉顶部导航标题

                    // 使用专为截图优化的内容视图
                    shareDetailContent

                    // 底部分享水印 - 直接作为卡片内容的一部分
                    HStack {
                        Spacer()
                        Text("via 咖啡札记")
                            .font(.caption)
                            .foregroundColor(.detailText)
                            .padding(.trailing, 16)
                    }
                    .padding(.vertical, 8)
                }
            }
            .padding(.horizontal, 18)
            .padding(.vertical, 20)
        }
    }

    // 专为截图优化的内容视图，使用纯SwiftUI组件
    private var shareDetailContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 卡片视图
            ZStack(alignment: .leading) {
                // 卡片主体 - 居中展示
                ZStack(alignment: .topLeading) {
                    // 外边框 - 恢复原始的边框样式
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.primaryBg)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.functionText, lineWidth: 2)
                        )

                    VStack(spacing: 0) {
                        // 中层：设备基本信息
                        VStack(alignment: .center, spacing: 8) {
                            // 设备名称
                            Text(currentEquipment.name)
                                .font(.title3)
                                .fontWeight(.bold)
                                .multilineTextAlignment(.center)
                                .padding(.top, 32)
                                .padding(.horizontal, 16)
                                .frame(maxWidth: .infinity)

                            // 品牌名称
                            if let brand = currentEquipment.brand {
                                Text(brand)
                                    .font(.headline)
                                    .fontWeight(.regular)
                                    .foregroundColor(.primaryText.opacity(0.7))
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                            }

                            Group {
                                Divider()
                                    .frame(height: 2.0)
                                    .background(Color.functionText)
                            }
                            .frame(width: 260)
                            .padding(.vertical, 28)

                            // 详细属性
                            attributesContainer()
                                .padding(.horizontal, 16)
                                .padding(.bottom, 16)
                        }
                    }
                    .padding(16)
                    .frame(maxWidth: .infinity)
                }
                .padding(.top, 16)
                .padding(.horizontal, 8)

                // 设备状态指示条
                EquipmentStatusBar(equipment: currentEquipment)
                    .frame(width: 6)
                    .padding(.top, 26)
                    .padding(.leading, -4)
            }
            .padding(.top, 8)

            // 组合内容 (如果是小工具组合且有组件)
            if currentEquipment.type == "GADGET_KIT", let components = currentEquipment.gadgetComponents, !components.isEmpty {
                SectionDivider(title: "组合内容")
                    .padding(.horizontal, 32)

                VStack(alignment: .leading, spacing: 2) {
                    ForEach(components.indices, id: \.self) { index in
                        if let componentName = components[index]["name"] as? String,
                           let componentId = components[index]["id"] as? Int {

                            let brand = components[index]["brand"] as? String ?? ""

                            NavigationLink(destination: EquipmentDetailView(equipment: Equipment.placeholder(id: componentId, name: componentName, brand: brand))) {
                                HStack {
                                    HStack(alignment: .center, spacing: 8) {
                                        Image("gadgetKit")
                                            .resizable()
                                            .renderingMode(.template)
                                            .foregroundColor(Color.secondaryText.opacity(0.5))
                                            .frame(width: 20, height: 17.777)

                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(componentName)
                                                .foregroundColor(.primaryText)
                                                .lineLimit(1)

                                            if !brand.isEmpty {
                                                Text(brand)
                                                    .font(.caption)
                                                    .foregroundColor(.primaryText.opacity(0.6))
                                                    .lineLimit(1)
                                            }
                                        }
                                    }

                                    Spacer()

                                    // 添加导航箭头指示
                                    Image(systemName: "chevron.right")
                                        .font(.caption)
                                        .foregroundColor(.secondaryText.opacity(0.5))
                                }
                                .padding(.vertical, 12)
                                .padding(.horizontal, 16)
                                .background(Color.clear)
                                .contentShape(Rectangle())
                            }
                        }
                    }
                }
                .padding(.horizontal, 32)
            }

            // 移除提要部分
        }
        .padding(.horizontal)
        .padding(.bottom, 16)
    }

    @MainActor private func generateImage() async {
        // 确保不在生成图片过程中重复触发
        guard !viewState.isGeneratingImage else {
            print("⚠️ 已在生成图片，跳过")
            return
        }

        // 标记正在生成图片，显示加载指示器
        viewState.isGeneratingImage = true

        // 使用UIKit屏幕尺寸来设置宽度
        let screenWidth = UIScreen.main.bounds.width

        // 确保主题颜色最新
        themeManager.updateThemeColorsOnly()

        // 延时一小段时间确保视图已加载完毕
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

        do {
            // 首先计算内容的实际高度
            let sizingContent = screenshotWrapperView
                .frame(width: screenWidth)
                .fixedSize(horizontal: true, vertical: true) // 让高度适应内容

            // 使用GeometryReader测量内容实际高度（放在后台队列中执行以不阻塞UI）
            let contentSize = await withCheckedContinuation { (continuation: CheckedContinuation<CGSize, Never>) in
                DispatchQueue.main.async {
                    let controller = UIHostingController(rootView: sizingContent)
                    controller.view.layoutIfNeeded()

                    // 获取实际内容尺寸并添加一些缓冲区
                    var size = controller.sizeThatFits(in: CGSize(width: screenWidth, height: UIView.layoutFittingCompressedSize.height))

                    // 确保内容大小是有效的
                    if size.height <= 0 {
                        size.height = UIScreen.main.bounds.height
                    }

                    continuation.resume(returning: size)
                }
            }

            // 创建一个渲染器并设置内容
            let renderer = ImageRenderer(
                content: screenshotWrapperView
                    .frame(width: screenWidth, height: contentSize.height)
                    .environment(\.colorScheme, colorScheme) // 确保渲染时使用当前的颜色方案
            )

            // 设置正确的显示比例
            renderer.scale = displayScale

            // 确保生成图像的上下文是不透明的
            renderer.proposedSize = ProposedViewSize(width: screenWidth, height: contentSize.height)

            if let uiImage = renderer.uiImage {
                // 保存为PNG格式而非JPEG
                let finalImage: UIImage
                if let pngData = uiImage.pngData(),
                   let pngImage = UIImage(data: pngData) {
                    finalImage = pngImage
                } else {
                    finalImage = uiImage
                }

                // 等待一小段时间确保图片已经准备好
                try? await Task.sleep(nanoseconds: 200_000_000) // 0.2秒

                // 使用UIActivityViewController分享图片
                let activityVC = UIActivityViewController(
                    activityItems: [finalImage],
                    applicationActivities: nil
                )

                // 在iPad上，设置弹出位置
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let rootViewController = windowScene.windows.first?.rootViewController {

                    if UIDevice.current.userInterfaceIdiom == .pad {
                        activityVC.popoverPresentationController?.sourceView = rootViewController.view
                        activityVC.popoverPresentationController?.sourceRect = CGRect(
                            x: UIScreen.main.bounds.width / 2,
                            y: UIScreen.main.bounds.height / 2,
                            width: 0,
                            height: 0
                        )
                    }

                    // 关键修改：加入completion回调，在分享控制器显示后重置isGeneratingImage状态
                    rootViewController.present(activityVC, animated: true) {
                        // 分享控制器显示后，重置生成状态
                        self.viewState.isGeneratingImage = false
                        print("📸 图片分享控制器已显示，重置状态")
                    }
                } else {
                    // 如果无法获取rootViewController，则只重置状态
                    self.viewState.isGeneratingImage = false
                    print("⚠️ 无法获取rootViewController来显示分享表单")
                }
            } else {
                viewState.isGeneratingImage = false
                print("⚠️ 渲染器未能生成图片")

                // 显示错误提示
                showErrorToast("生成图片失败")
            }
        } catch {
            print("❌ 生成图片时发生错误: \(error.localizedDescription)")
            viewState.isGeneratingImage = false
            showErrorToast("生成图片失败: \(error.localizedDescription)")
        }
    }

    // 添加这个帮助函数，以便在视图中使用
    func attributesContainer() -> some View {
        EquipmentFlexibleAttributesContainer(attributes: getDetailAttributes())
    }

    // 显示错误提示
    private func showErrorToast(_ message: String) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first
        else { return }

        let toastView = UIView()
        toastView.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.9)
        toastView.layer.cornerRadius = 10
        toastView.layer.shadowColor = UIColor.black.cgColor
        toastView.layer.shadowOpacity = 0.2
        toastView.layer.shadowOffset = CGSize(width: 0, height: 2)
        toastView.layer.shadowRadius = 4

        let label = UILabel()
        label.text = message
        label.textColor = UIColor.label
        label.textAlignment = .center
        label.numberOfLines = 0

        toastView.addSubview(label)
        window.addSubview(toastView)

        label.translatesAutoresizingMaskIntoConstraints = false
        toastView.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            label.leadingAnchor.constraint(equalTo: toastView.leadingAnchor, constant: 16),
            label.trailingAnchor.constraint(equalTo: toastView.trailingAnchor, constant: -16),
            label.topAnchor.constraint(equalTo: toastView.topAnchor, constant: 12),
            label.bottomAnchor.constraint(equalTo: toastView.bottomAnchor, constant: -12),

            toastView.centerXAnchor.constraint(equalTo: window.centerXAnchor),
            toastView.bottomAnchor.constraint(equalTo: window.safeAreaLayoutGuide.bottomAnchor, constant: -30),
            toastView.widthAnchor.constraint(lessThanOrEqualTo: window.widthAnchor, constant: -40)
        ])

        // 动画显示
        toastView.alpha = 0
        UIView.animate(withDuration: 0.3) {
            toastView.alpha = 1
        }

        // 2秒后消失
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            UIView.animate(withDuration: 0.3, animations: {
                toastView.alpha = 0
            }) { _ in
                toastView.removeFromSuperview()
            }
        }
    }
}

// 设备状态指示条
struct EquipmentStatusBar: View {
    let equipment: Equipment

    var body: some View {
        // 垂直排列多个状态指示条
        VStack(spacing: 2) {  // 减少间距，使指示条更紧凑
            // 归档状态
            if equipment.isArchived {
                statusIndicator(color: .navbarBg)
            } else if equipment.isActive {
                // 活跃设备
                statusIndicator(color: .green)
            }

            // 首选状态始终单独判断
            if equipment.isFavorite {
                statusIndicator(color: .yellow)
            }

            // 添加一个占位Spacer，让指示条始终保持在上方
            Spacer()
        }
    }

    // 单个状态指示条
    private func statusIndicator(color: Color) -> some View {
        Rectangle()
            .fill(color)
            .cornerRadius(3)
            .frame(height: 26)
    }
}

// 预览
struct EquipmentDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            EquipmentDetailView(equipment: .previewSample)
        }
    }
}

// 为了预览创建一个示例设备
extension Equipment {
    static var previewSample: Equipment {
        Equipment(
            id: 1,
            name: "1Zpresso Q2",
            type: "GRINDER",
            typeDisplay: "磨豆机",
            brand: "1Zpresso",
            model: "Q2 Heptagonal",
            description: "便携式手动磨豆机",
            purchaseDate: Date().addingTimeInterval(-90 * 24 * 60 * 60), // 90天前
            purchasePrice: 599.0,
            notes: "特别适合户外使用，研磨速度快，均匀度好。",
            createdAt: Date().addingTimeInterval(-90 * 24 * 60 * 60),
            updatedAt: Date(),
            isDeleted: false,
            isArchived: false,
            isActive: true,
            isFavorite: true,
            isDisabled: false,
            brewMethod: nil,
            brewMethodDisplay: nil,
            gadgetComponents: nil,
            grindSizePreset: "20点(中度研磨)",
            grinderPurpose: "FILTER",
            grinderPurposeDisplay: "手冲",
            usageCount: 45,
            lastUsed: Date().addingTimeInterval(-3 * 24 * 60 * 60), // 3天前
            breakEvenProgress: 37.5,
            depreciationRate: 85.0
        )
    }
}

// 设备特性区域
struct EquipmentAttributeSection: View {
    let title: String
    let value: String?
    let components: [(Int, String)]?

    var body: some View {
        if (value != nil && !value!.isEmpty) || (components != nil && !components!.isEmpty) {
            VStack(alignment: .center, spacing: 4) {
                Text(title)
                    .font(.footnote)
                    .foregroundColor(.archivedText)
                    .frame(maxWidth: .infinity, alignment: .center)

                if let value = value, !value.isEmpty {
                    Text(value)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primaryText)
                        .frame(maxWidth: .infinity, alignment: .center)
                }

                if let components = components {
                    ForEach(components, id: \.0) { index, value in
                        Text("#\(index+1) \(value)")
                            .font(.body)
                            .fontWeight(.medium)
                            .foregroundColor(.primaryText)
                            .frame(maxWidth: .infinity, alignment: .center)
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .center)
            .padding(16)
            .cornerRadius(8)
        }
    }
}

// 为设备详情视图创建自定义的FlexibleAttributesContainer
struct EquipmentFlexibleAttributesContainer: View {
    let attributes: [(String, String?, [(Int, String)]?)]

    var body: some View {
        FlexableStack(spacing: 12, alignment: .center) {
            ForEach(attributes.indices, id: \.self) { index in
                let attr = attributes[index]
                EquipmentAttributeSection(
                    title: attr.0,
                    value: attr.1,
                    components: attr.2
                )
                .fixedSize()
            }
        }
    }
}