import Foundation

enum EquipmentType: String, Codable {
    case brewing = "brewing"
    case grinding = "grinding"

    var displayName: String {
        switch self {
        case .brewing: return "冲煮器具"
        case .grinding: return "研磨器具"
        }
    }
}

struct Equipment: Identifiable, Codable, Hashable {
    let id: Int
    var name: String
    var type: String
    var typeDisplay: String?
    var brand: String?
    var model: String?
    var description: String?
    var purchaseDate: Date?
    var purchasePrice: Double?
    var notes: String?
    var createdAt: Date
    var updatedAt: Date
    var isDeleted: Bool
    var isArchived: Bool
    var isActive: Bool
    var isFavorite: Bool
    var isDisabled: Bool = false  // 新增属性，表示是否禁用（已归档或已删除）
    var brewMethod: String?
    var brewMethodDisplay: String?
    var gadgetComponents: [[String: Any]]?
    var grindSizePreset: String?
    var grinderPurpose: String?
    var grinderPurposeDisplay: String?
    var usageCount: Int?
    var lastUsed: Date?
    var breakEvenProgress: Double?
    var depreciationRate: Double?

    enum CodingKeys: String, CodingKey {
        case id, name, type
        case typeDisplay = "type_display"
        case brand, model
        case description
        case brewMethod = "brew_method"
        case brewMethodDisplay = "brew_method_display"
        case purchaseDate = "purchase_date"
        case purchasePrice = "purchase_price"
        case notes
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case isDeleted = "is_deleted"
        case isArchived = "is_archived"
        case isActive = "is_active"
        case isFavorite = "is_favorite"
        case isDisabled = "is_disabled"
        case gadgetComponents = "gadget_components"
        case grindSizePreset = "grind_size_preset"
        case grinderPurpose = "grinder_purpose"
        case grinderPurposeDisplay = "grinder_purpose_display"
        case usageCount = "usage_count"
        case lastUsed = "last_used"
        case breakEvenProgress = "break_even_progress"
        case depreciationRate = "depreciation_rate"
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: Equipment, rhs: Equipment) -> Bool {
        lhs.id == rhs.id
    }

    // MARK: - 自定义解码初始化方法，处理服务器可能返回空对象而不是null的情况
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 检查是否为空对象
        if container.allKeys.isEmpty {
            // 返回一个表示"无设备"的实例
            id = -1
            name = "无"
            type = ""
            typeDisplay = nil
            brand = nil
            model = nil
            description = nil
            purchaseDate = nil
            purchasePrice = nil
            notes = nil
            createdAt = Date(timeIntervalSince1970: 0)
            updatedAt = Date(timeIntervalSince1970: 0)
            isDeleted = false
            isArchived = false
            isActive = false
            isFavorite = false
            isDisabled = false
            brewMethod = nil
            brewMethodDisplay = nil
            gadgetComponents = nil
            grindSizePreset = nil
            grinderPurpose = nil
            grinderPurposeDisplay = nil
            usageCount = nil
            lastUsed = nil
            breakEvenProgress = nil
            depreciationRate = nil
            return
        }

        // 正常解码
        do {
            id = try container.decode(Int.self, forKey: .id)
        } catch {
            // 打印调试信息
            print("解码ID时出错: \(error)")

            // 检查是否在嵌套结构中
            let pathDescription = decoder.codingPath.map { $0.stringValue }.joined(separator: ".")
            print("解码路径: \(pathDescription)")

            if pathDescription.contains("coffee_bean") ||
               pathDescription.contains("brewing_equipment") ||
               pathDescription.contains("grinding_equipment") ||
               pathDescription.contains("results") {
                print("警告: 在嵌套结构中找到设备ID缺失，使用默认ID")
                // 使用随机ID作为临时解决方案
                id = Int.random(in: 1000000...9999999)
            } else {
                // 如果不在嵌套结构中，抛出错误
                throw error
            }
        }

        name = try container.decode(String.self, forKey: .name)
        type = try container.decode(String.self, forKey: .type)
        typeDisplay = try container.decodeIfPresent(String.self, forKey: .typeDisplay)
        brand = try container.decodeIfPresent(String.self, forKey: .brand)
        model = try container.decodeIfPresent(String.self, forKey: .model)
        description = try container.decodeIfPresent(String.self, forKey: .description)
        purchaseDate = try container.decodeIfPresent(Date.self, forKey: .purchaseDate)
        purchasePrice = try container.decodeIfPresent(Double.self, forKey: .purchasePrice)
        notes = try container.decodeIfPresent(String.self, forKey: .notes)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        updatedAt = try container.decode(Date.self, forKey: .updatedAt)
        isDeleted = try container.decode(Bool.self, forKey: .isDeleted)
        isArchived = try container.decode(Bool.self, forKey: .isArchived)
        isActive = try container.decode(Bool.self, forKey: .isActive)
        isFavorite = try container.decodeIfPresent(Bool.self, forKey: .isFavorite) ?? false
        isDisabled = try container.decodeIfPresent(Bool.self, forKey: .isDisabled) ?? false
        brewMethod = try container.decodeIfPresent(String.self, forKey: .brewMethod)
        brewMethodDisplay = try container.decodeIfPresent(String.self, forKey: .brewMethodDisplay)

        // 解析组件字典数组
        if let anyComponents = try? container.decodeIfPresent(AnyCodable.self, forKey: .gadgetComponents) {
            if let componentsArray = anyComponents.value as? [[String: Any]] {
                gadgetComponents = componentsArray
            } else {
                gadgetComponents = nil
            }
        } else {
            gadgetComponents = nil
        }

        grindSizePreset = try container.decodeIfPresent(String.self, forKey: .grindSizePreset)
        grinderPurpose = try container.decodeIfPresent(String.self, forKey: .grinderPurpose)
        grinderPurposeDisplay = try container.decodeIfPresent(String.self, forKey: .grinderPurposeDisplay)
        usageCount = try container.decodeIfPresent(Int.self, forKey: .usageCount)
        lastUsed = try container.decodeIfPresent(Date.self, forKey: .lastUsed)
        breakEvenProgress = try container.decodeIfPresent(Double.self, forKey: .breakEvenProgress)
        depreciationRate = try container.decodeIfPresent(Double.self, forKey: .depreciationRate)
    }

    // MARK: - 自定义编码方法，处理特殊类型如gadgetComponents
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(type, forKey: .type)
        try container.encodeIfPresent(typeDisplay, forKey: .typeDisplay)
        try container.encodeIfPresent(brand, forKey: .brand)
        try container.encodeIfPresent(model, forKey: .model)
        try container.encodeIfPresent(description, forKey: .description)
        try container.encodeIfPresent(purchaseDate, forKey: .purchaseDate)
        try container.encodeIfPresent(purchasePrice, forKey: .purchasePrice)
        try container.encodeIfPresent(notes, forKey: .notes)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
        try container.encode(isDeleted, forKey: .isDeleted)
        try container.encode(isArchived, forKey: .isArchived)
        try container.encode(isActive, forKey: .isActive)
        try container.encode(isFavorite, forKey: .isFavorite)
        try container.encode(isDisabled, forKey: .isDisabled)
        try container.encodeIfPresent(brewMethod, forKey: .brewMethod)
        try container.encodeIfPresent(brewMethodDisplay, forKey: .brewMethodDisplay)

        // 处理gadgetComponents
        if let components = gadgetComponents {
            // 将组件数组转换为AnyCodable数组
            let codableComponents = components.map { component -> [String: AnyCodable] in
                // 将每个组件的字典转换为可编码的格式
                return component.mapValues { AnyCodable($0) }
            }
            try container.encode(codableComponents, forKey: .gadgetComponents)
        }

        try container.encodeIfPresent(grindSizePreset, forKey: .grindSizePreset)
        try container.encodeIfPresent(grinderPurpose, forKey: .grinderPurpose)
        try container.encodeIfPresent(grinderPurposeDisplay, forKey: .grinderPurposeDisplay)
        try container.encodeIfPresent(usageCount, forKey: .usageCount)
        try container.encodeIfPresent(lastUsed, forKey: .lastUsed)
        try container.encodeIfPresent(breakEvenProgress, forKey: .breakEvenProgress)
        try container.encodeIfPresent(depreciationRate, forKey: .depreciationRate)
    }

    // 添加占位符方法，用于创建临时Equipment对象
    static func placeholder(id: Int, name: String, brand: String? = nil) -> Equipment {
        return Equipment(
            id: id,
            name: name,
            type: Equipment.typeGadget, // 默认为小工具类型
            typeDisplay: "小工具",
            brand: brand,
            model: nil,
            description: nil,
            purchaseDate: nil,
            purchasePrice: nil,
            notes: nil,
            createdAt: Date(),
            updatedAt: Date(),
            isDeleted: false,
            isArchived: false,
            isActive: true,
            isFavorite: false,
            isDisabled: false,
            brewMethod: nil,
            brewMethodDisplay: nil,
            gadgetComponents: nil,
            grindSizePreset: nil,
            grinderPurpose: nil,
            grinderPurposeDisplay: nil,
            usageCount: nil,
            lastUsed: nil,
            breakEvenProgress: nil,
            depreciationRate: nil
        )
    }

    // 添加初始化方法
    init(id: Int, name: String, type: String, typeDisplay: String?, brand: String?, model: String?, description: String?, purchaseDate: Date?, purchasePrice: Double?, notes: String?, createdAt: Date, updatedAt: Date, isDeleted: Bool, isArchived: Bool, isActive: Bool, isFavorite: Bool, isDisabled: Bool, brewMethod: String?, brewMethodDisplay: String?, gadgetComponents: [[String: Any]]?, grindSizePreset: String?, grinderPurpose: String?, grinderPurposeDisplay: String?, usageCount: Int?, lastUsed: Date?, breakEvenProgress: Double?, depreciationRate: Double?) {
        self.id = id
        self.name = name
        self.type = type
        self.typeDisplay = typeDisplay
        self.brand = brand
        self.model = model
        self.description = description
        self.purchaseDate = purchaseDate
        self.purchasePrice = purchasePrice
        self.notes = notes
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.isDeleted = isDeleted
        self.isArchived = isArchived
        self.isActive = isActive
        self.isFavorite = isFavorite
        self.isDisabled = isDisabled
        self.brewMethod = brewMethod
        self.brewMethodDisplay = brewMethodDisplay
        self.gadgetComponents = gadgetComponents
        self.grindSizePreset = grindSizePreset
        self.grinderPurpose = grinderPurpose
        self.grinderPurposeDisplay = grinderPurposeDisplay
        self.usageCount = usageCount
        self.lastUsed = lastUsed
        self.breakEvenProgress = breakEvenProgress
        self.depreciationRate = depreciationRate
    }
}

// MARK: - Equipment Type Constants
extension Equipment {
    static let typeBrewingEquipment = "BREWER"
    static let typeGrindingEquipment = "GRINDER"
    static let typeGadget = "GADGET"
    static let typeGadgetKit = "GADGET_KIT"

    // 设备类型的使用寿命映射（年）
    private static let usefulLifeMap: [String: Double] = [
        typeBrewingEquipment: 7.0,
        typeGrindingEquipment: 5.0,
        typeGadget: 3.0,
        typeGadgetKit: 3.0
    ]

    // 设备类型的残值率映射
    private static let salvageRateMap: [String: Double] = [
        typeBrewingEquipment: 0.15,
        typeGrindingEquipment: 0.10,
        typeGadget: 0.05,
        typeGadgetKit: 0.05
    ]

    // 设备类型的单次使用价值映射（元）
    private static let usageValueMap: [String: Double] = [
        typeGrindingEquipment: 3.0,  // 磨豆机每次使用价值3元
        typeBrewingEquipment: 5.0,   // 冲煮设备每次使用价值5元
        typeGadget: 1.0,             // 小工具每次使用价值1元
        typeGadgetKit: 1.0           // 小工具组合每次使用价值1元
    ]

    /// 计算设备折旧率
    func calculateDepreciationRate() -> Double {
        // 获取设备类型对应的使用寿命和残值率
        let usefulLife = Self.usefulLifeMap[self.type] ?? 5.0
        let salvageRate = Self.salvageRateMap[self.type] ?? 0.10

        // 计算年度折旧率
        let annualDepreciationRate = (1.0 - salvageRate) / usefulLife

        // 计算使用年龄
        let calendar = Calendar.current
        let ageInDays = calendar.dateComponents([.day], from: createdAt, to: Date()).day ?? 0
        let age = Double(ageInDays) / 365.0

        // 计算当前价值率
        let currentValueRate = 1.0 - (annualDepreciationRate * min(age, usefulLife))

        // 确保不低于残值率，并转为百分比
        return round(max(currentValueRate, salvageRate) * 100 * 100) / 100
    }

    /// 计算设备回本进度
    func calculateBreakEvenProgress() -> Double {
        guard let price = self.purchasePrice, price > 0, let usageCount = self.usageCount else {
            return 0.0
        }

        // 获取设备类型对应的单次使用价值
        let usageValue = Self.usageValueMap[self.type] ?? 3.0

        // 根据购买价格调整单次使用价值
        // 价格越高，单次使用价值越高，但增长是非线性的
        let priceFactor = sqrt(price / 1000.0)  // 使用平方根使增长更平缓
        let adjustedUsageValue = usageValue * priceFactor

        // 计算总节省成本
        let totalSavings = Double(usageCount) * adjustedUsageValue

        // 计算回本百分比，最大为100%
        let breakEvenPercentage = min(totalSavings / price, 1.0)

        return round(breakEvenPercentage * 100 * 100) / 100
    }

    var isBrewingEquipment: Bool {
        type == Self.typeBrewingEquipment
    }

    var isGrindingEquipment: Bool {
        type == Self.typeGrindingEquipment
    }

    var isGadget: Bool {
        type == Self.typeGadget
    }

    var isGadgetKit: Bool {
        type == Self.typeGadgetKit
    }

    var displayType: String {
        return typeDisplay ?? {
            switch type {
            case Self.typeBrewingEquipment: return "冲煮器具"
            case Self.typeGrindingEquipment: return "磨豆机"
            case Self.typeGadget: return "小工具"
            case Self.typeGadgetKit: return "小工具组合"
            default: return type
            }
        }()
    }

    // 设备是否有使用数据
    var hasUsageData: Bool {
        return usageCount != nil && usageCount! > 0
    }

    // 计算回本进度的显示
    var breakEvenProgressDisplay: String {
        // 如果已经有服务端传来的数据，优先使用
        if let progress = breakEvenProgress {
            return "\(Int(progress))%"
        }
        // 否则本地计算
        return "\(Int(calculateBreakEvenProgress()))%"
    }

    // 折旧率显示
    var depreciationRateDisplay: String {
        // 如果已经有服务端传来的数据，优先使用
        if let rate = depreciationRate {
            return "\(Int(rate))%"
        }
        // 否则本地计算
        return "\(Int(calculateDepreciationRate()))%"
    }

    // 获取此小工具组合中所包含的小工具ID
    func getGadgetComponentIds() -> [String] {
        guard let components = gadgetComponents, !components.isEmpty else {
            return []
        }

        // 从组件字典数组中提取ID
        var componentIds: [String] = []
        for component in components {
            if let id = component["id"] {
                if let intId = id as? Int {
                    componentIds.append(String(intId))
                } else if let stringId = id as? String {
                    componentIds.append(stringId)
                }
            }
        }
        return componentIds
    }
}