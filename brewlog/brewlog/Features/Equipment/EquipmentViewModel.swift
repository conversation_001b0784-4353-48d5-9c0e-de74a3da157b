import Foundation
import SwiftUI
import Combine

// 确保ViewModel可以访问模型类型
// 由于Swift在同一个模块中的类型可以直接访问，所以不需要特别的import语句
// Models.swift中定义的APIResponse类型应该可以在这里使用

// 定义设备筛选器
struct EquipmentFilter {
    var brandFilter: String? = nil
    var typeFilter: String? = nil
    var sortBy: EquipmentSortOption = .defaultOption
    var showArchived: Bool = false
    
    var hasFilters: Bool {
        return brandFilter != nil || typeFilter != nil || sortBy != .defaultOption
    }
}

// 设备排序类型
enum SortEquipmentType: String, CaseIterable, Identifiable {
    case time = "按时间"
    case brand = "按品牌"
    case type = "按类型"
    
    var id: String { rawValue }
}

// 设备排序选项
enum EquipmentSortOption: Identifiable, Equatable {
    case time(ascending: Bool)
    case brand(ascending: Bool)
    case type(ascending: Bool)
    
    var id: String { rawValue }
    
    // 构造基于排序类型的实例（默认降序）
    init(type: SortEquipmentType) {
        switch type {
        case .time: self = .time(ascending: false)
        case .brand: self = .brand(ascending: false)
        case .type: self = .type(ascending: false)
        }
    }
    
    // 切换升序/降序
    func toggleDirection() -> EquipmentSortOption {
        switch self {
        case .time(let ascending): return .time(ascending: !ascending)
        case .brand(let ascending): return .brand(ascending: !ascending)
        case .type(let ascending): return .type(ascending: !ascending)
        }
    }
    
    // 获取排序类型
    var sortType: SortEquipmentType {
        switch self {
        case .time: return .time
        case .brand: return .brand
        case .type: return .type
        }
    }
    
    // 是否为升序
    var isAscending: Bool {
        switch self {
        case .time(let ascending), .brand(let ascending), .type(let ascending):
            return ascending
        }
    }
    
    // 排序键
    var sortKey: String {
        switch self {
        case .time: return "time"
        case .brand: return "brand"
        case .type: return "type"
        }
    }
    
    // 显示名称
    var rawValue: String {
        let direction = isAscending ? "↑" : "↓"
        switch self {
        case .time: return "\(SortEquipmentType.time.rawValue)\(direction)"
        case .brand: return "\(SortEquipmentType.brand.rawValue)\(direction)"
        case .type: return "\(SortEquipmentType.type.rawValue)\(direction)"
        }
    }
    
    // 默认排序选项
    static let defaultOption: EquipmentSortOption = .type(ascending: false)
    
    // 获取所有排序选项
    static var allCases: [EquipmentSortOption] {
        return SortEquipmentType.allCases.map { EquipmentSortOption(type: $0) }
    }
    
    // Equatable协议实现
    static func == (lhs: EquipmentSortOption, rhs: EquipmentSortOption) -> Bool {
        switch (lhs, rhs) {
        case (.time(let a1), .time(let a2)): return a1 == a2
        case (.brand(let a1), .brand(let a2)): return a1 == a2
        case (.type(let a1), .type(let a2)): return a1 == a2
        default: return false
        }
    }
}

// 设备分组类型
enum EquipmentGroupType {
    case month
    case brand
    case type
}

// 设备分组
struct EquipmentGroup: Identifiable {
    let id = UUID()
    let name: String
    var equipment: [Equipment]
}

@MainActor
class EquipmentViewModel: ObservableObject {
    private let apiService: APIService
    
    // MARK: - Published Properties
    @Published var equipment: [Equipment] = []
    @Published var filteredEquipment: [Equipment] = []
    @Published var filter = EquipmentFilter()
    @Published var activeGroups: [EquipmentGroup] = []
    @Published var archivedGroups: [EquipmentGroup] = []
    @Published var isLoading = false
    @Published var error: Error?
    @Published var currentPage = 1
    @Published var hasMoreEquipment = true
    @Published var searchText = ""
    @Published var selectedEquipment: Equipment?
    @Published var shouldNavigateToDetail = false
    
    // 可用的品牌
    @Published var availableBrands: [String] = []
    
    // 当前分组类型
    @Published var groupType: EquipmentGroupType = .type
    
    // 添加数据版本跟踪相关属性
    private var localDataVersion: Int = 0
    
    // 添加用于打开修改表单的方法
    @Published var equipmentToEdit: Equipment? = nil
    @Published var shouldShowEditSheet = false
    
    init(apiService: APIService = .shared) {
        self.apiService = apiService
        
        // 从UserDefaults加载上次保存的数据版本
        self.localDataVersion = UserDefaults.standard.integer(forKey: "equipment_data_version")
        
        // 初始加载
        Task {
            // 先检查数据版本，如果需要更新则会自动刷新数据
            await checkDataVersion()
        }
    }
    
    // MARK: - 数据版本检查
    func checkDataVersion() async {
        guard !isLoading else { return }
        
        do {
            // 这里使用[String: Any]类型会导致错误
            // 因为Decodable要求泛型参数也必须遵循Decodable协议
            // 将不使用泛型方法执行请求，而是直接使用URLSession获取数据
            let urlString = "\(apiService.getBaseURL())/ios/api/data/version/?data_types=equipment"
            guard let url = URL(string: urlString) else { return }
            
            var request = URLRequest(url: url)
            if let token = apiService.getAuthToken() {
                request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            }
            
            let (data, _) = try await URLSession.shared.data(for: request)
            if let response = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let versions = response["versions"] as? [String: Any],
               let equipmentVersion = versions["equipment"] as? Int {
                
                // 获取本地存储的版本号
                let storedVersion = UserDefaults.standard.integer(forKey: "equipment_data_version")
                
                // 如果远程版本比本地版本新，或者是第一次加载（本地版本为0）
                if storedVersion == 0 || equipmentVersion > storedVersion {
                    // 更新本地存储的版本号
                    UserDefaults.standard.set(equipmentVersion, forKey: "equipment_data_version")
                    localDataVersion = equipmentVersion
                    
                    // 强制刷新数据
                    await fetchEquipment(forceRefresh: true)
                    
                    // 添加调试日志
                    print("数据版本更新: \(storedVersion) -> \(equipmentVersion), 强制刷新设备列表")
                }
            }
        } catch {
            // 版本检查失败时，不更新版本号，但仍然可以尝试刷新数据
            if self.equipment.isEmpty {
                await fetchEquipment(forceRefresh: false)
            }
        }
    }
    
    // MARK: - 数据加载方法
    func fetchEquipment(forceRefresh: Bool = false) async {
        guard !isLoading else { return }
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 构建URL参数
            var parameters: [String: String] = ["page": "1", "page_size": "50"]
            
            // 添加强制刷新参数
            if forceRefresh {
                parameters["force_refresh"] = "true"
            }
            
            // 使用自定义处理逻辑获取设备数据
            let urlString = "\(apiService.getBaseURL())/ios/api/equipment/"
            var urlComponents = URLComponents(string: urlString)!
            
            // 添加查询参数
            var queryItems: [URLQueryItem] = []
            for (key, value) in parameters {
                queryItems.append(URLQueryItem(name: key, value: value))
            }
            urlComponents.queryItems = queryItems
            
            // 创建请求
            var request = URLRequest(url: urlComponents.url!)
            request.addValue("Bearer \(apiService.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            // 添加缓存控制
            if forceRefresh {
                request.cachePolicy = URLRequest.CachePolicy.reloadIgnoringLocalCacheData
                request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
                request.addValue("no-cache", forHTTPHeaderField: "Pragma")
            }
            
            print("🔍 开始获取设备数据: \(request.url?.absoluteString ?? "")")
            
            // 发送请求
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 {
                // 设置解码器
                let decoder = JSONDecoder()
                
                // 设置日期解码策略
                decoder.dateDecodingStrategy = .custom({ decoder in
                    let container = try decoder.singleValueContainer()
                    if let dateString = try? container.decode(String.self) {
                        let formatter = DateFormatter()
                        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
                        if let date = formatter.date(from: dateString) {
                            return date
                        }
                        
                        // 尝试更多格式
                        let formats = ["yyyy-MM-dd'T'HH:mm:ssZ", "yyyy-MM-dd"]
                        for format in formats {
                            formatter.dateFormat = format
                            if let date = formatter.date(from: dateString) {
                                return date
                            }
                        }
                        return Date()
                    } else if let timestamp = try? container.decode(Double.self) {
                        return Date(timeIntervalSince1970: timestamp)
                    }
                    return Date()
                })
                
                // 注释: 修复JSON解析错误
                // 这里我们对API返回的数据进行更灵活的处理，同时支持两种可能的格式:
                // 1. 分页格式: {"count": 10, "results": [...], "next": ...}
                // 2. 数组格式: [...]
                // 通过先检查JSON是否包含"results"字段，我们可以确定使用哪种解析方式
                // 这样可以解决"typeMismatch(Swift.Array<Any>, Swift.DecodingError.Context(codingPath: [], debugDescription: 'Expected to decode Array<Any> but found a dictionary instead.', underlyingError: nil))"错误
                
                // 检查是否可以解析为分页响应格式
                if let jsonObject = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   jsonObject["results"] != nil {
                    // 使用分页格式
                    print("✅ 使用分页格式解析设备数据")
                    let response = try decoder.decode(APIResponse<Equipment>.self, from: data)
                    equipment = response.results
                    
                    // 检查next字段是否存在且有效
                    hasMoreEquipment = false
                    if let next = response.next?.value as? Bool {
                        hasMoreEquipment = next
                    } else if let next = response.next?.value as? String, !next.isEmpty {
                        hasMoreEquipment = true
                    }
                } else {
                    // 尝试直接解析为数组
                    print("✅ 使用数组格式解析设备数据")
                    let equipmentArray = try decoder.decode([Equipment].self, from: data)
                    equipment = equipmentArray
                    hasMoreEquipment = false
                }
                
                // 更新可用品牌列表
                updateAvailableBrands()
                
                // 应用过滤和分组
                applyFiltersAndGrouping()
                
                currentPage = 1
                error = nil
                
                // 如果服务器返回了数据版本信息，更新本地版本
                if let jsonObject = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let dataVersion = jsonObject["data_version"] as? Int {
                    localDataVersion = dataVersion
                    UserDefaults.standard.set(dataVersion, forKey: "equipment_data_version")
                    print("📊 从服务器更新数据版本: \(dataVersion)")
                }
            } else {
                throw APIError.invalidResponse
            }
        } catch {
            self.error = error
            print("❌ 获取设备数据失败: \(error)")
        }
    }
    
    func loadMoreEquipment() async {
        guard !isLoading && hasMoreEquipment else { return }
        isLoading = true
        defer { isLoading = false }
        
        do {
            let nextPage = currentPage + 1
            
            // 构建URL
            let urlString = "\(apiService.getBaseURL())/ios/api/equipment/"
            var urlComponents = URLComponents(string: urlString)!
            
            // 添加查询参数
            urlComponents.queryItems = [URLQueryItem(name: "page", value: "\(nextPage)")]
            
            // 创建请求
            var request = URLRequest(url: urlComponents.url!)
            request.addValue("Bearer \(apiService.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            print("🔍 加载更多设备数据: \(request.url?.absoluteString ?? ""), 页码: \(nextPage)")
            
            // 发送请求
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 {
                // 设置解码器
                let decoder = JSONDecoder()
                
                // 设置日期解码策略
                decoder.dateDecodingStrategy = .custom({ decoder in
                    let container = try decoder.singleValueContainer()
                    if let dateString = try? container.decode(String.self) {
                        let formatter = DateFormatter()
                        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
                        if let date = formatter.date(from: dateString) {
                            return date
                        }
                        
                        // 尝试更多格式
                        let formats = ["yyyy-MM-dd'T'HH:mm:ssZ", "yyyy-MM-dd"]
                        for format in formats {
                            formatter.dateFormat = format
                            if let date = formatter.date(from: dateString) {
                                return date
                            }
                        }
                        return Date()
                    } else if let timestamp = try? container.decode(Double.self) {
                        return Date(timeIntervalSince1970: timestamp)
                    }
                    return Date()
                })
                
                var newEquipment: [Equipment] = []
                var newHasMore = false
                
                // 检查是否可以解析为分页响应格式
                if let jsonObject = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   jsonObject["results"] != nil {
                    // 使用分页格式
                    print("✅ 使用分页格式解析加载的更多设备数据")
                    let response = try decoder.decode(APIResponse<Equipment>.self, from: data)
                    newEquipment = response.results
                    
                    // 检查next字段是否存在且有效
                    if let next = response.next?.value as? Bool {
                        newHasMore = next
                    } else if let next = response.next?.value as? String, !next.isEmpty {
                        newHasMore = true
                    }
                } else {
                    // 尝试直接解析为数组
                    print("✅ 使用数组格式解析加载的更多设备数据")
                    newEquipment = try decoder.decode([Equipment].self, from: data)
                    newHasMore = false
                }
                
                // 添加新加载的设备
                equipment.append(contentsOf: newEquipment)
                
                // 更新可用品牌列表
                updateAvailableBrands()
                
                // 重新应用过滤和分组
                applyFiltersAndGrouping()
                
                // 更新分页状态
                hasMoreEquipment = newHasMore
                currentPage = nextPage
                error = nil
            } else {
                throw APIError.invalidResponse
            }
        } catch {
            self.error = error
            print("❌ 加载更多设备数据失败: \(error)")
        }
    }
    
    // MARK: - 筛选与分组方法
    
    // 更新可用品牌列表
    private func updateAvailableBrands() {
        // 筛选有品牌的设备，排除小工具组合(GADGET_KIT)和没有品牌的设备
        let brands = Set(equipment.compactMap { equip -> String? in
            // 如果是GADGET_KIT或没有品牌，返回nil
            if equip.type == "GADGET_KIT" || equip.brand == nil || equip.brand!.isEmpty {
                return nil
            }
            return equip.brand
        }).filter { !$0.isEmpty }
        
        availableBrands = Array(brands).sorted()
    }
    
    // 应用筛选条件和分组
    func applyFiltersAndGrouping() {
        // 筛选设备
        filteredEquipment = equipment.filter { equipment in
            // 筛选已删除的设备
            guard !equipment.isDeleted else { return false }
            
            // 根据归档状态筛选 - 修正逻辑
            // 不管filter.showArchived是什么值，都让它通过，我们将在groupEquipment中将它们分离
            // 这样可以确保在ApplyFilter时归档和未归档设备都通过相同的筛选条件
            
            // 根据品牌筛选 - 当选择了品牌筛选时，小工具组合(GADGET_KIT)类型的设备应该被排除
            if let brandFilter = filter.brandFilter {
                // 如果是小工具组合，直接排除
                if equipment.type == "GADGET_KIT" {
                    return false
                }
                
                // 检查品牌是否匹配
                let equipmentBrand = equipment.brand ?? ""
                if equipmentBrand.isEmpty || equipmentBrand != brandFilter {
                    return false
                }
            }
            
            // 根据类型筛选
            if let typeFilter = filter.typeFilter, equipment.type != typeFilter {
                return false
            }
            
            // 根据搜索文本筛选
            if !searchText.isEmpty {
                let searchLower = searchText.lowercased()
                
                // 检查名称、品牌和型号
                let nameMatch = equipment.name.lowercased().contains(searchLower)
                let brandMatch = equipment.brand?.lowercased().contains(searchLower) ?? false
                let modelMatch = equipment.model?.lowercased().contains(searchLower) ?? false
                let notesMatch = equipment.notes?.lowercased().contains(searchLower) ?? false
                let typeMatch = equipment.typeDisplay?.lowercased().contains(searchLower) ?? false
                
                return nameMatch || brandMatch || modelMatch || notesMatch || typeMatch
            }
            
            return true
        }
        
        // 应用排序
        sortEquipment()
        
        // 分组设备 - 修改为专门显示归档和活跃设备
        groupEquipment()
        
        // 打印调试信息
        print("筛选后设备数量: \(filteredEquipment.count)")
        print("活跃设备组数量: \(activeGroups.count)，设备数量: \(activeGroups.flatMap { $0.equipment }.count)")
        print("归档设备组数量: \(archivedGroups.count)，设备数量: \(archivedGroups.flatMap { $0.equipment }.count)")
    }
    
    // MARK: - 排序设备
    private func sortEquipment() {
        filteredEquipment.sort { (equipment1: Equipment, equipment2: Equipment) in
            switch filter.sortBy {
            case .time(let ascending):
                return ascending ? equipment1.createdAt < equipment2.createdAt : equipment1.createdAt > equipment2.createdAt
            case .brand(let ascending):
                return ascending ? (equipment1.brand ?? "").lowercased() < (equipment2.brand ?? "").lowercased() : (equipment1.brand ?? "").lowercased() > (equipment2.brand ?? "").lowercased()
            case .type(let ascending):
                return ascending ? equipment1.type < equipment2.type : equipment1.type > equipment2.type
            }
        }
    }
    
    // 设备分组
    private func groupEquipment() {
        // 分离活跃和归档设备 - 改进逻辑，无论filter.showArchived是什么值，都分别处理归档和非归档设备
        let activeEquipment = filteredEquipment.filter { !$0.isArchived }
        
        // 修改：使归档设备也使用相同的筛选条件，而不是从全部设备中获取所有归档设备
        // 注意：这里不再从equipment直接获取所有归档设备，而是使用相同的筛选条件
        // 对filteredEquipment应用上面相同的筛选条件，但筛选归档的设备
        let archivedEquipment = filteredEquipment.filter { $0.isArchived }
        
        // 打印调试信息
        print("原始设备总数: \(equipment.count)")
        print("过滤后的设备: \(filteredEquipment.count)")
        print("活跃设备: \(activeEquipment.count)")
        print("归档设备: \(archivedEquipment.count)")
        
        // 根据分组类型应用分组逻辑
        switch groupType {
        case .month:
            activeGroups = groupByMonth(activeEquipment)
            archivedGroups = groupByMonth(archivedEquipment)
        case .brand:
            activeGroups = groupByBrand(activeEquipment)
            archivedGroups = groupByBrand(archivedEquipment)
        case .type:
            activeGroups = groupByType(activeEquipment)
            archivedGroups = groupByType(archivedEquipment)
        }
        
        // 确保归档组只包含已归档设备，活跃组只包含未归档设备
        for i in 0..<activeGroups.count {
            activeGroups[i].equipment = activeGroups[i].equipment.filter { !$0.isArchived }
        }
        
        for i in 0..<archivedGroups.count {
            archivedGroups[i].equipment = archivedGroups[i].equipment.filter { $0.isArchived }
        }
        
        // 移除空组
        activeGroups = activeGroups.filter { !$0.equipment.isEmpty }
        archivedGroups = archivedGroups.filter { !$0.equipment.isEmpty }
    }
    
    // 按月份分组
    private func groupByMonth(_ equipment: [Equipment]) -> [EquipmentGroup] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy年MM月"
        
        // 创建月份分组字典
        var monthGroups: [String: [Equipment]] = [:]
        
        for equip in equipment {
            let date = equip.createdAt
            let monthKey = dateFormatter.string(from: date)
            
            if monthGroups[monthKey] == nil {
                monthGroups[monthKey] = []
            }
            monthGroups[monthKey]?.append(equip)
        }
        
        // 转换为分组数组
        let result = monthGroups.map { key, value in
            EquipmentGroup(name: key, equipment: value)
        }
        
        // 按月份排序（最新的月份在前）
        return result.sorted { group1, group2 in
            let date1 = parseMonthString(group1.name)
            let date2 = parseMonthString(group2.name)
            return date1 > date2
        }
    }
    
    // 解析月份字符串为日期
    private func parseMonthString(_ monthString: String) -> Date {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy年MM月"
        return dateFormatter.date(from: monthString) ?? Date.distantPast
    }
    
    // 按品牌分组
    private func groupByBrand(_ equipment: [Equipment]) -> [EquipmentGroup] {
        // 创建品牌分组字典
        var brandGroups: [String: [Equipment]] = [:]
        
        for equip in equipment {
            let brandKey = equip.brand ?? "无品牌"
            
            if brandGroups[brandKey] == nil {
                brandGroups[brandKey] = []
            }
            brandGroups[brandKey]?.append(equip)
        }
        
        // 转换为分组数组
        let result = brandGroups.map { key, value in
            EquipmentGroup(name: key, equipment: value)
        }
        
        // 按品牌名称排序
        return result.sorted { $0.name < $1.name }
    }
    
    // 按类型分组
    private func groupByType(_ equipment: [Equipment]) -> [EquipmentGroup] {
        // 创建类型分组字典
        var typeGroups: [String: [Equipment]] = [:]
        
        for equip in equipment {
            // 使用 typeDisplay 或转换类型代码为中文显示
            let typeKey = getTypeDisplayName(equip.type)
            
            if typeGroups[typeKey] == nil {
                typeGroups[typeKey] = []
            }
            typeGroups[typeKey]?.append(equip)
        }
        
        // 转换为分组数组
        let result = typeGroups.map { key, value in
            EquipmentGroup(name: key, equipment: value)
        }
        
        // 按类型排序顺序：冲煮器具 > 磨豆机 > 小工具 > 小工具组合
        return result.sorted { group1, group2 in
            let order1 = getTypeOrder(group1.name)
            let order2 = getTypeOrder(group2.name)
            return order1 < order2
        }
    }
    
    // 获取设备类型的中文显示名称
    private func getTypeDisplayName(_ type: String) -> String {
        switch type {
        case "BREWER": return "冲煮器具"
        case "GRINDER": return "磨豆机"
        case "GADGET": return "小工具"
        case "GADGET_KIT": return "小工具组合"
        default: return "其他设备"
        }
    }
    
    // 获取类型的排序权重
    private func getTypeOrder(_ typeName: String) -> Int {
        switch typeName {
        case "冲煮器具": return 0
        case "磨豆机": return 1
        case "小工具": return 2
        case "小工具组合": return 3
        default: return 4
        }
    }
    
    // MARK: - 设备操作方法
    
    // 添加一个方法来发送设备更新通知
    private func notifyEquipmentUpdated(_ equipment: Equipment) {
        // 在主线程上发送通知
        DispatchQueue.main.async {
            NotificationCenter.default.post(
                name: NSNotification.Name("EquipmentUpdated"),
                object: equipment
            )
            print("📣 已发送设备更新通知（包含完整设备数据）: ID \(equipment.id)")
        }
    }
    
    // 设置分组类型
    func setGroupType(_ type: EquipmentGroupType) {
        groupType = type
        applyFiltersAndGrouping()
    }
    
    // 设置排序选项
    func setSortOption(_ option: EquipmentSortOption) {
        filter.sortBy = option
        
        // 根据排序选项更新分组类型
        switch option {
        case .time:
            groupType = .month
        case .brand:
            groupType = .brand
        case .type:
            groupType = .type
        }
        
        applyFiltersAndGrouping()
    }
    
    // 设置品牌筛选
    func setBrandFilter(_ brand: String?) {
        filter.brandFilter = brand
        applyFiltersAndGrouping()
    }
    
    // 设置类型筛选
    func setTypeFilter(_ type: String?) {
        filter.typeFilter = type
        applyFiltersAndGrouping()
    }
    
    // 设置归档显示筛选
    func setShowArchived(_ show: Bool) {
        filter.showArchived = show
        applyFiltersAndGrouping()
    }
    
    // 重置筛选条件
    func resetFilters() {
        // 创建新的筛选器对象，确保使用默认排序选项
        filter = EquipmentFilter()
        // 确保分组类型与默认排序选项一致
        groupType = .type
        searchText = ""
        applyFiltersAndGrouping()
    }
    
    // 查看设备详情
    func viewEquipmentDetails(equipment: Equipment) -> Equipment {
        // 移除导航状态设置，只返回设备对象
        return equipment
    }
    
    // 重置导航状态的方法
    func resetNavigationState() {
        self.selectedEquipment = nil
        self.shouldNavigateToDetail = false
    }
    
    // 修改收藏状态
    func toggleFavorite(_ equipment: Equipment) async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 先检查当前设备的收藏状态
            let isCurrentlyFavorite = equipment.isFavorite
            
            // 如果当前设备不是首选且准备设为首选，需要先在本地更新状态
            if !isCurrentlyFavorite {
                // 如果设备已归档且准备设为首选，先执行取消归档操作
                if equipment.isArchived {
                    print("🔄 检测到设备已归档且准备设为首选，先执行取消归档操作")
                    do {
                        // 调用取消归档API
                        let unarchiveEndpoint = "/ios/api/equipment/\(equipment.id)/unarchive/"
                        let _: APIEmptyResponse = try await apiService.post(unarchiveEndpoint, body: [:])
                        print("✅ 成功取消归档设备: ID=\(equipment.id), 名称=\(equipment.name)")
                        
                        // 更新本地数据模型中的归档状态
                        if let index = self.equipment.firstIndex(where: { $0.id == equipment.id }) {
                            self.equipment[index].isArchived = false
                        }
                    } catch {
                        print("❌ 取消归档失败: \(error.localizedDescription)")
                        // 继续处理，即使取消归档失败，仍然设置首选
                        // 服务端也会自动处理这种情况
                    }
                }
                
                // 手动清除同类型的其他首选设备状态
                for i in 0..<self.equipment.count {
                    if self.equipment[i].type == equipment.type && 
                       self.equipment[i].id != equipment.id && 
                       self.equipment[i].isFavorite {
                        // 更新本地数据模型中的状态
                        self.equipment[i].isFavorite = false
                    }
                }
            }
            
            // 调用API
            let response: APIEmptyResponse = try await apiService.post("/ios/api/equipment/\(equipment.id)/toggle_favorite/", body: [:])
            
            // 检查响应中是否包含更新的数据版本
            if let dataVersion = response.dataVersion {
                localDataVersion = dataVersion
                UserDefaults.standard.set(dataVersion, forKey: "equipment_data_version")
            }
            
            // 更新本地数据模型
            if let index = self.equipment.firstIndex(where: { $0.id == equipment.id }) {
                // 切换当前设备的收藏状态
                self.equipment[index].isFavorite.toggle()
                
                // 对于GADGET_KIT类型，特殊处理以确保组件不被清空
                if equipment.type == "GADGET_KIT" {
                    // 保留原有的组件数据
                    // 注意：由于我们已经处理了同类型设备的首选状态，这里只需要确保组件引用不丢失
                    if let components = equipment.gadgetComponents {
                        self.equipment[index].gadgetComponents = components
                    }
                }
            }
            
            // 重新应用筛选和分组
            applyFiltersAndGrouping()
            
            // 如果操作是设为首选，刷新设备数据以确保正确性
            if !isCurrentlyFavorite && equipment.type == "GADGET_KIT" {
                // 对于小工具组合类型，设置为首选后立即刷新数据
                Task {
                    // 给服务器一点时间处理
                    try? await Task.sleep(nanoseconds: 500_000_000) // 500毫秒
                    // 检查数据版本并在需要时更新
                    await checkDataVersion()
                }
            }
            
        } catch {
            self.error = error
            
            // 发生错误时强制刷新数据，确保UI状态与服务器一致
            Task {
                await checkDataVersion()
            }
        }
    }
    
    // 归档设备
    func toggleArchive(_ equipment: Equipment) async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 如果准备归档且设备为首选状态，先取消首选状态
            if !equipment.isArchived && equipment.isFavorite {
                print("🔄 检测到设备是首选状态且准备归档，需要先取消首选: ID=\(equipment.id)")
                do {
                    // 调用切换首选的API，这会取消首选状态
                    await toggleFavorite(equipment)
                    print("✅ 成功取消首选状态: ID=\(equipment.id), 名称=\(equipment.name)")
                } catch {
                    print("⚠️ 取消首选状态失败: \(error.localizedDescription)")
                    // 继续处理，即使取消首选失败，仍然执行归档
                }
            }
            
            // 调用API
            let endpoint = equipment.isArchived 
                ? "/ios/api/equipment/\(equipment.id)/unarchive/" 
                : "/ios/api/equipment/\(equipment.id)/archive/"
                
            let response: APIEmptyResponse = try await apiService.post(endpoint, body: [:])
            
            // 检查响应中是否包含更新的数据版本
            if let dataVersion = response.dataVersion {
                localDataVersion = dataVersion
                UserDefaults.standard.set(dataVersion, forKey: "equipment_data_version")
            }
            
            // 更新本地数据模型
            if let index = self.equipment.firstIndex(where: { $0.id == equipment.id }) {
                self.equipment[index].isArchived.toggle()
            }
            
            // 重新应用筛选和分组
            applyFiltersAndGrouping()
            
        } catch {
            self.error = error
            
            // 发生错误时检查数据版本
            Task {
                await checkDataVersion()
            }
        }
    }
    
    // 删除设备
    func deleteEquipment(_ equipment: Equipment) async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            let response: APIEmptyResponse = try await apiService.delete("/ios/api/equipment/\(equipment.id)/")
            
            // 检查响应中是否包含更新的数据版本
            if let dataVersion = response.dataVersion {
                localDataVersion = dataVersion
                UserDefaults.standard.set(dataVersion, forKey: "equipment_data_version")
            }
            
            // 从本地数据中移除
            self.equipment.removeAll { $0.id == equipment.id }
            
            // 重新应用筛选和分组
            applyFiltersAndGrouping()
            
        } catch {
            self.error = error
            
            // 发生错误时检查数据版本
            Task {
                await checkDataVersion()
            }
        }
    }
    
    // 更新设备
    @MainActor
    func updateEquipment(_ equipment: Equipment) async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 构建请求体
            var body: [String: Any] = [
                "name": equipment.name,
                "type": equipment.type,
                "is_active": equipment.isActive
            ]
            
            // 添加可选字段
            if let brand = equipment.brand { body["brand"] = brand }
            if let model = equipment.model { body["model"] = model }
            if let description = equipment.description { body["description"] = description }
            if let purchaseDate = equipment.purchaseDate {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd"
                body["purchase_date"] = formatter.string(from: purchaseDate)
            }
            if let purchasePrice = equipment.purchasePrice { body["purchase_price"] = purchasePrice }
            if let notes = equipment.notes { body["notes"] = notes }
            if let brewMethod = equipment.brewMethod { body["brew_method"] = brewMethod }
            if let grindSizePreset = equipment.grindSizePreset { body["grind_size_preset"] = grindSizePreset }
            if let grinderPurpose = equipment.grinderPurpose { body["grinder_purpose"] = grinderPurpose }
            
            // 如果是小工具组合，添加组件信息
            if equipment.type == Equipment.typeGadgetKit {
                if let components = equipment.gadgetComponents {
                    // 修改：处理不同类型的gadgetComponents，避免冗余类型转换
                    if let dictArray = components as? [[String: Any]] {
                        let gadgetIds = dictArray.compactMap { $0["id"] as? Int }
                        if !gadgetIds.isEmpty {
                            body["gadget_components"] = gadgetIds
                        }
                    } else if let components = components as? [Any] {
                        // 处理其他可能的类型
                        print("gadgetComponents类型为[Any]，尝试解析")
                        let gadgetIds = components.compactMap { component -> Int? in
                            if let dict = component as? [String: Any],
                               let id = dict["id"] as? Int {
                                return id
                            }
                            return nil
                        }
                        if !gadgetIds.isEmpty {
                            body["gadget_components"] = gadgetIds
                        }
                    } else {
                        print("gadgetComponents类型无法处理: \(type(of: components))")
                    }
                }
            }
            
            // 调用API
            let response: APIEmptyResponse = try await apiService.put("/ios/api/equipment/\(equipment.id)/", body: body)
            
            // 检查响应中是否包含更新的数据版本
            if let dataVersion = response.dataVersion {
                localDataVersion = dataVersion
                UserDefaults.standard.set(dataVersion, forKey: "equipment_data_version")
            }
            
            // 更新本地数据模型
            if let index = self.equipment.firstIndex(where: { $0.id == equipment.id }) {
                self.equipment[index] = equipment
            }
            
            // 重新应用筛选和分组
            applyFiltersAndGrouping()
            
            // 发送设备更新通知
            notifyEquipmentUpdated(equipment)
            
        } catch {
            self.error = error
            
            // 发生错误时检查数据版本
            Task {
                await checkDataVersion()
            }
        }
    }
    
    // 创建设备
    @MainActor
    func createEquipment(_ equipment: Equipment) async throws {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 构建请求体
            var body: [String: Any] = [
                "name": equipment.name,
                "type": equipment.type
            ]
            
            // 添加可选字段
            if let brand = equipment.brand { body["brand"] = brand }
            if let model = equipment.model { body["model"] = model }
            if let description = equipment.description { body["description"] = description }
            if let purchaseDate = equipment.purchaseDate {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd"
                body["purchase_date"] = formatter.string(from: purchaseDate)
            }
            if let purchasePrice = equipment.purchasePrice { body["purchase_price"] = purchasePrice }
            if let notes = equipment.notes { body["notes"] = notes }
            if let brewMethod = equipment.brewMethod { body["brew_method"] = brewMethod }
            if let grindSizePreset = equipment.grindSizePreset { body["grind_size_preset"] = grindSizePreset }
            if let grinderPurpose = equipment.grinderPurpose { body["grinder_purpose"] = grinderPurpose }
            
            // 如果是小工具组合，添加组件信息
            if equipment.type == Equipment.typeGadgetKit {
                if let components = equipment.gadgetComponents {
                    // 修改：处理不同类型的gadgetComponents，避免冗余类型转换
                    if let dictArray = components as? [[String: Any]] {
                        let gadgetIds = dictArray.compactMap { $0["id"] as? Int }
                        if !gadgetIds.isEmpty {
                            body["gadget_components"] = gadgetIds
                        }
                    } else if let components = components as? [Any] {
                        // 处理其他可能的类型
                        print("gadgetComponents类型为[Any]，尝试解析")
                        let gadgetIds = components.compactMap { component -> Int? in
                            if let dict = component as? [String: Any],
                               let id = dict["id"] as? Int {
                                return id
                            }
                            return nil
                        }
                        if !gadgetIds.isEmpty {
                            body["gadget_components"] = gadgetIds
                        }
                    } else {
                        print("gadgetComponents类型无法处理: \(type(of: components))")
                    }
                }
            }
            
            // 调用API
            let response: Equipment = try await apiService.post("/ios/api/equipment/", body: body)
            
            // 添加到本地数据模型
            self.equipment.append(response)
            
            // 重新应用筛选和分组
            applyFiltersAndGrouping()
            
            // 强制刷新以确保UI更新
            await fetchEquipment(forceRefresh: true)
            
        } catch {
            self.error = error
            throw error
        }
    }
    
    // 使用直接数据创建设备（与服务端模型匹配）
    @MainActor
    func createEquipmentWithData(_ data: [String: Any]) async throws {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 创建可变的数据副本，以便修改
            var modifiedData = data
            
            // 处理created_at字段，如果是ISO8601字符串，转换为时间戳
            if let createdAtString = data["created_at"] as? String {
                let dateFormatter = ISO8601DateFormatter()
                if let date = dateFormatter.date(from: createdAtString) {
                    // 转换为时间戳（秒）
                    modifiedData["created_at"] = Int(date.timeIntervalSince1970)
                    print("🕒 将ISO日期 \(createdAtString) 转换为时间戳: \(Int(date.timeIntervalSince1970))")
                }
            }
            
            // 使用专门用于创建设备的API端点
            let response: Equipment = try await apiService.post("/ios/api/equipment/", body: modifiedData)
            
            // 添加到本地数据模型
            self.equipment.append(response)
            
            // 重新应用筛选和分组
            applyFiltersAndGrouping()
            
            // 强制刷新以确保UI完全更新，忽略缓存
            try? await Task.sleep(nanoseconds: 500_000_000) // 等待500毫秒确保服务器已处理
            await fetchEquipment(forceRefresh: true)
            
            // 更新本地数据版本号
            if let dataJSON = try? JSONSerialization.jsonObject(with: JSONEncoder().encode(response)) as? [String: Any],
               let dataVersion = dataJSON["updated_at"] as? Int {
                localDataVersion = dataVersion
                UserDefaults.standard.set(dataVersion, forKey: "equipment_data_version")
            }
            
        } catch {
            self.error = error
            throw error
        }
    }
    
    // 修改updateEquipmentWithData方法，添加通知功能
    func updateEquipmentWithData(_ data: [String: Any]) async throws {
        guard !isLoading else { return }
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 首先确保ID字段存在
            guard let equipmentId = data["id"] as? Int else {
                throw APIError.invalidData("设备ID缺失")
            }
            
            // 创建可变的数据副本，以便修改
            var modifiedData = data
            
            // 移除purchase_date字段，避免调用只读属性
            modifiedData.removeValue(forKey: "purchase_date")
            
            // 保留created_at字段为时间戳格式，服务器端会将其作为购买日期处理
            if let createdAtTimestamp = modifiedData["created_at"] as? Int {
                // 已经是时间戳格式，无需修改
                print("🕒 使用提供的created_at时间戳: \(createdAtTimestamp)")
            } else if let createdAtString = modifiedData["created_at"] as? String {
                // 如果是日期字符串，尝试转换为时间戳
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                if let date = dateFormatter.date(from: createdAtString) {
                    modifiedData["created_at"] = Int(date.timeIntervalSince1970)
                    print("🕒 将日期字符串 \(createdAtString) 转换为时间戳: \(Int(date.timeIntervalSince1970))")
                }
            }
            
            // 构建请求URL - 使用正确的API端点路径，匹配服务器配置
            let urlString = "\(apiService.getBaseURL())/ios/api/equipment/\(equipmentId)/update/"
            guard let url = URL(string: urlString) else {
                throw APIError.invalidURL
            }
            
            // 创建请求
            var request = URLRequest(url: url)
            request.httpMethod = "POST" // 使用POST方法，匹配服务器端update_equipment视图的要求
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // 设置认证头
            if let token = apiService.getAuthToken() {
                request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            } else {
                throw APIError.authenticationError("未登录或认证过期")
            }
            
            // 序列化请求数据
            let jsonData = try JSONSerialization.data(withJSONObject: modifiedData)
            request.httpBody = jsonData
            
            print("📤 正在更新设备 ID \(equipmentId): \(String(data: jsonData, encoding: .utf8) ?? "无数据")")
            
            // 发送请求
            let (responseData, response) = try await URLSession.shared.data(for: request)
            
            // 检查响应状态码
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.networkError(NSError(domain: "APIError", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法获取HTTP响应"]))
            }
            
            // 处理成功的HTTP响应
            if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                // 解析响应数据
                guard let responseObject = try? JSONSerialization.jsonObject(with: responseData) as? [String: Any] else {
                    throw APIError.decodingError(NSError(domain: "APIError", code: -1, userInfo: [NSLocalizedDescriptionKey: "解析响应数据失败"]))
                }
                print("✅ 设备更新成功: \(responseObject)")
                
                // 发送设备更新通知
                notifyEquipmentUpdated(Equipment(id: equipmentId, name: "", type: "", typeDisplay: nil, brand: nil, model: nil, description: nil, purchaseDate: nil, purchasePrice: nil, notes: nil, createdAt: Date(), updatedAt: Date(), isDeleted: false, isArchived: false, isActive: true, isFavorite: false, isDisabled: false, brewMethod: nil, brewMethodDisplay: nil, gadgetComponents: nil, grindSizePreset: nil, grinderPurpose: nil, grinderPurposeDisplay: nil, usageCount: nil, lastUsed: nil, breakEvenProgress: nil, depreciationRate: nil))
                
                // 更新本地缓存的设备列表
                await fetchEquipment(forceRefresh: true)
            } else {
                // 处理各种HTTP错误
                if let errorData = try? JSONSerialization.jsonObject(with: responseData) as? [String: Any],
                   let errorMessage = errorData["detail"] as? String {
                    throw APIError.serverErrorSimple(errorMessage)
                } else {
                    throw APIError.httpError(httpResponse.statusCode)
                }
            }
        } catch let error as APIError {
            // 直接抛出APIError
            throw error
        } catch {
            // 处理其他错误
            print("❌ 更新设备失败: \(error.localizedDescription)")
            throw APIError.unknownError(error.localizedDescription)
        }
    }
    
    // 加载可用的小工具（用于小工具组合功能）
    func fetchAvailableGadgets(forEquipment equipmentId: Int? = nil) async -> [Equipment] {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 从已加载的设备中过滤出可用的小工具
            let availableGadgets = self.equipment.filter { 
                $0.type == Equipment.typeGadget && 
                !$0.isDeleted && 
                !$0.isArchived 
            }
            
            // 如果已经有加载好的设备，直接返回
            if !availableGadgets.isEmpty && equipmentId == nil {
                return availableGadgets
            }
            
            // 构建API URL，如果有设备ID则添加为查询参数
            var urlString = "/ios/api/equipment/gadgets/"
            if let id = equipmentId {
                urlString += "?equipment_id=\(id)"
            }
            
            // 从API加载
            let gadgets: [Equipment] = try await apiService.get(urlString)
            return gadgets
            
        } catch {
            self.error = error
            return []
        }
    }
    
    // 添加一个方法用于在视图出现或应用从后台恢复时检查更新
    func checkForUpdates() async {
        await checkDataVersion()
    }
    
    // 添加用于打开修改表单的方法
    func openEditEquipmentForm(equipment: Equipment) {
        self.equipmentToEdit = equipment
        self.shouldShowEditSheet = true
    }
} 