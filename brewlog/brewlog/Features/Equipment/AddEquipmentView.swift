import SwiftUI

// 表单草稿结构体，需要遵循Codable协议以便JSON序列化
struct EquipmentFormDraft: Codable {
    // 基本信息
    var name: String = ""
    var type: String = Equipment.typeBrewingEquipment
    var brand: String = ""
    var brewMethod: String = "POUR_OVER"
    var notes: String = ""
    
    // 详细信息
    var purchaseDate: Date = Date()
    var purchasePrice: String = ""
    var grindSizePreset: String = ""
    var grinderPurpose: String = "ALL_PURPOSE"
    
    // 小工具组合
    var selectedGadgets: [Int] = []
    
    // 自定义CodingKeys以便于处理日期
    enum CodingKeys: String, CodingKey {
        case name, type, brand, brewMethod, notes
        case purchaseDateTimestamp // 使用时间戳存储日期
        case purchasePrice, grindSizePreset, grinderPurpose
        case selectedGadgets
    }
    
    // 自定义编码方法
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(name, forKey: .name)
        try container.encode(type, forKey: .type)
        try container.encode(brand, forKey: .brand)
        try container.encode(brewMethod, forKey: .brewMethod)
        try container.encode(notes, forKey: .notes)
        try container.encode(purchaseDate.timeIntervalSince1970, forKey: .purchaseDateTimestamp)
        try container.encode(purchasePrice, forKey: .purchasePrice)
        try container.encode(grindSizePreset, forKey: .grindSizePreset)
        try container.encode(grinderPurpose, forKey: .grinderPurpose)
        try container.encode(selectedGadgets, forKey: .selectedGadgets)
    }
    
    // 自定义解码方法
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        name = try container.decode(String.self, forKey: .name)
        type = try container.decode(String.self, forKey: .type)
        brand = try container.decode(String.self, forKey: .brand)
        brewMethod = try container.decode(String.self, forKey: .brewMethod)
        notes = try container.decode(String.self, forKey: .notes)
        let timestamp = try container.decode(Double.self, forKey: .purchaseDateTimestamp)
        purchaseDate = Date(timeIntervalSince1970: timestamp)
        purchasePrice = try container.decode(String.self, forKey: .purchasePrice)
        grindSizePreset = try container.decode(String.self, forKey: .grindSizePreset)
        grinderPurpose = try container.decode(String.self, forKey: .grinderPurpose)
        selectedGadgets = try container.decode([Int].self, forKey: .selectedGadgets)
    }
    
    // 默认初始化方法
    init(name: String = "", 
         type: String = Equipment.typeBrewingEquipment,
         brand: String = "",
         brewMethod: String = "POUR_OVER",
         notes: String = "",
         purchaseDate: Date = Date(),
         purchasePrice: String = "",
         grindSizePreset: String = "",
         grinderPurpose: String = "ALL_PURPOSE",
         selectedGadgets: [Int] = []) {
        self.name = name
        self.type = type
        self.brand = brand
        self.brewMethod = brewMethod
        self.notes = notes
        self.purchaseDate = purchaseDate
        self.purchasePrice = purchasePrice
        self.grindSizePreset = grindSizePreset
        self.grinderPurpose = grinderPurpose
        self.selectedGadgets = selectedGadgets
    }
}

struct AddEquipmentView: View {
    let viewModel: EquipmentViewModel
    
    @Environment(\.dismiss) var dismiss
    @FocusState private var focusedField: String?
    
    @State private var name: String = ""
    @State private var type: String = Equipment.typeBrewingEquipment
    @State private var brand: String = ""
    @State private var brewMethod: String = "POUR_OVER"
    @State private var notes: String = ""
    @State private var purchaseDate: Date = Date()
    @State private var purchasePrice: String = ""
    @State private var grindSizePreset: String = ""
    @State private var grinderPurpose: String = "ALL_PURPOSE"
    @State private var selectedGadgets: [Int] = []
    
    @State private var availableGadgets: [Equipment] = []
    @State private var hasAvailableGadgets: Bool = false
    @State private var isSaving = false
    @State private var errorMessage: String?
    
    // 草稿状态控制
    @State private var hasDraft: Bool = false
    @State private var draftNoticeShown = false
    
    var body: some View {
        ZStack(alignment: .top) {
            // 固定的顶部导航栏
            topNavigationBar
                .background(Color.primaryBg)
                .zIndex(1)
            
            ScrollView {
                VStack(spacing: 8) {
                    // 添加一个占位符，高度等于导航栏高度
                    Color.clear
                        .frame(height: 52)
                    
                    // 如果有草稿，显示提示信息（只在视图首次加载时显示）
                    if hasDraft && !draftNoticeShown {
                        HStack(spacing: 8) {
                            Image(systemName: "doc.append")
                                .foregroundColor(.green)
                            Text("已从上次编辑恢复草稿")
                                .font(.footnote)
                                .foregroundColor(.primaryText)
                            Spacer()
                            Button(action: {
                                clearDraft()
                                draftNoticeShown = true
                            }) {
                                Text("清除草稿")
                                    .font(.footnote)
                                    .foregroundColor(.error)
                            }
                        }
                        .padding(10)
                        .background(Color.navbarBg)
                        .cornerRadius(6)
                        .padding(.horizontal, 12)
                        .padding(.bottom, 4)
                        .transition(.opacity)
                        .id("draftNotice-\(draftNoticeShown)")
                        .onAppear {
                            // 5秒后自动隐藏提示
                            DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                                withAnimation {
                                    draftNoticeShown = true
                                }
                            }
                        }
                    }
                    
                    // 主要内容区域
                    VStack(spacing: 16) {
                        // 设备类型选择区域
                        equipmentTypeSection
                        
                        // 基本信息区域
                        basicInfoSection
                        
                        // 详细信息区域（根据设备类型显示不同内容）
                        detailsSection
                        
                        // 小工具组合内容（仅当类型是小工具组合时显示）
                        if type == Equipment.typeGadgetKit {
                            gadgetKitSection
                        }
                        
                        // 备注区域
                        notesSection
                    }
                    .padding(.horizontal, 12)
                    .padding(.bottom, 20)
                }
            }
            .background(
                Color.primaryBg
                    .onTapGesture {
                        hideKeyboard()
                    }
            )
        }
        .alert(isPresented: Binding<Bool>(
            get: { errorMessage != nil },
            set: { if !$0 { errorMessage = nil } }
        )) {
            Alert(
                title: Text("保存失败"),
                message: Text(errorMessage ?? "发生未知错误"),
                dismissButton: .default(Text("确定"))
            )
        }
        .overlay(
            Group {
                if isSaving {
                    ZStack {
                        Color.black.opacity(0.4)

                        BlinkingLoader(
                            color: .primaryBg,
                            width: 12,
                            height: 18,
                            duration: 1.5,
                            text: "保存中..."
                        )
                        .padding()
                        .background(Color.secondaryBg.opacity(0.9))
                        .cornerRadius(10)
                        .shadow(radius: 5)
                    }
                }
            }
        )
        .onAppear {
            checkAvailableGadgets()
            loadDraft()
        }
        .onDisappear(perform: saveDraft)
        .onChange(of: type) { newType in
            if newType == Equipment.typeGadgetKit {
                loadAvailableGadgets()
            }
            // 类型变化时保存草稿
            saveDraft()
        }
        // 添加onChange监听各个字段变化，自动保存草稿
        .onChange(of: name) { _ in saveDraft() }
        .onChange(of: brand) { _ in saveDraft() }
        .onChange(of: brewMethod) { _ in saveDraft() }
        .onChange(of: notes) { _ in saveDraft() }
        .onChange(of: purchaseDate) { _ in saveDraft() }
        .onChange(of: purchasePrice) { _ in saveDraft() }
        .onChange(of: grindSizePreset) { _ in saveDraft() }
        .onChange(of: grinderPurpose) { _ in saveDraft() }
        .onChange(of: selectedGadgets) { _ in saveDraft() }
    }
    
    // 草稿保存和加载函数
    private func saveDraft() {
        // 如果正在保存设备，不要保存草稿
        if isSaving {
            return
        }
        
        // 创建一个草稿副本
        let draftCopy = EquipmentFormDraft(
            name: name,
            type: type,
            brand: brand,
            brewMethod: brewMethod,
            notes: notes,
            purchaseDate: purchaseDate,
            purchasePrice: purchasePrice,
            grindSizePreset: grindSizePreset,
            grinderPurpose: grinderPurpose,
            selectedGadgets: selectedGadgets
        )
        
        // 将草稿序列化为JSON并保存到UserDefaults
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(draftCopy)
            UserDefaults.standard.set(data, forKey: "EquipmentFormDraft")
            print("✅ 设备草稿已保存")
        } catch {
            print("❌ 保存设备草稿失败: \(error.localizedDescription)")
        }
    }
    
    private func loadDraft() {
        guard let data = UserDefaults.standard.data(forKey: "EquipmentFormDraft") else {
            print("ℹ️ 没有找到设备草稿数据")
            return
        }
        
        do {
            let decoder = JSONDecoder()
            let draft = try decoder.decode(EquipmentFormDraft.self, from: data)
            
            // 检查草稿是否只包含默认值
            if isEmptyDraft(draft) {
                // 如果草稿只包含默认值，则清除草稿并返回
                print("ℹ️ 设备草稿只包含默认值，不恢复")
                UserDefaults.standard.removeObject(forKey: "EquipmentFormDraft")
                return
            }
            
            // 恢复所有状态
            self.name = draft.name
            self.type = draft.type
            self.brand = draft.brand
            self.brewMethod = draft.brewMethod
            self.notes = draft.notes
            self.purchaseDate = draft.purchaseDate
            self.purchasePrice = draft.purchasePrice
            self.grindSizePreset = draft.grindSizePreset
            self.grinderPurpose = draft.grinderPurpose
            self.selectedGadgets = draft.selectedGadgets
            
            hasDraft = true
            print("✅ 设备草稿已加载")
            
            // 如果草稿是小工具组合类型，加载可用的小工具
            if type == Equipment.typeGadgetKit {
                loadAvailableGadgets()
            }
        } catch {
            print("❌ 加载设备草稿失败: \(error.localizedDescription)")
        }
    }
    
    // 检查草稿是否只包含默认值
    private func isEmptyDraft(_ draft: EquipmentFormDraft) -> Bool {
        // 检查必填字段
        if !draft.name.isEmpty {
            return false
        }
        
        // 检查其他非默认值字段
        if draft.type != Equipment.typeBrewingEquipment ||
           !draft.brand.isEmpty ||
           draft.brewMethod != "POUR_OVER" ||
           !draft.notes.isEmpty ||
           draft.purchasePrice != "" ||
           !draft.grindSizePreset.isEmpty ||
           draft.grinderPurpose != "ALL_PURPOSE" ||
           !draft.selectedGadgets.isEmpty {
            return false
        }
        
        // 检查购买日期是否与当前日期相差超过1分钟
        // 注意：这里不做精确比较，因为保存和加载过程中可能有微小差异
        let currentTime = Date()
        let timeDifference = abs(draft.purchaseDate.timeIntervalSince(currentTime))
        if timeDifference > 60 { // 如果时差超过60秒，则认为是用户设置的时间
            return false
        }
        
        // 所有检查通过，草稿只包含默认值
        return true
    }
    
    private func clearDraft() {
        // 清除UserDefaults中的草稿
        UserDefaults.standard.removeObject(forKey: "EquipmentFormDraft")
        hasDraft = false
        
        // 重置所有表单字段为默认值
        name = ""
        type = Equipment.typeBrewingEquipment
        brand = ""
        brewMethod = "POUR_OVER"
        notes = ""
        purchaseDate = Date()
        purchasePrice = ""
        grindSizePreset = ""
        grinderPurpose = "ALL_PURPOSE"
        selectedGadgets = []
        
        print("🗑️ 设备草稿已清除并重置表单")
    }
    
    // 顶部导航栏
    private var topNavigationBar: some View {
        ZStack {
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(.linkText)
                }
                
                Spacer()
                
                Button(action: saveEquipment) {
                    Text("保存")
                        .fontWeight(.medium)
                        .foregroundColor((!name.isEmpty && (type != Equipment.typeGadgetKit || !selectedGadgets.isEmpty)) && !isSaving ? .linkText : Color.gray.opacity(0.5))
                }
                .disabled(name.isEmpty || (type == Equipment.typeGadgetKit && selectedGadgets.isEmpty) || isSaving)
            }
            
            Text("新增设备")
                .font(.headline)
                .foregroundColor(.primaryText)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
    }
    
    // 设备类型选择区域
    private var equipmentTypeSection: some View {
        VStack(spacing: 20) {
            // 设备类型选择
            VStack(alignment: .leading, spacing: 10) {
                Text("设备类型")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                HStack(spacing: 12) {
                    typeRadioButton(title: "冲煮器具", value: Equipment.typeBrewingEquipment)
                    typeRadioButton(title: "磨豆机", value: Equipment.typeGrindingEquipment)
                    Spacer()
                }
                .padding(.horizontal, 4)
                
                HStack(spacing: 12) {
                    typeRadioButton(title: "小工具", value: Equipment.typeGadget)
                    typeRadioButton(title: "小工具组合", value: Equipment.typeGadgetKit, disabled: !hasAvailableGadgets)
                    Spacer()
                }
                .padding(.horizontal, 4)
            }
            .padding(.horizontal, 16)
            
            // 冲煮器具类型的额外选项
            if type == Equipment.typeBrewingEquipment {
                VStack(alignment: .leading, spacing: 10) {
                    Text("赛道")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Menu {
                        Button("意式") { brewMethod = "ESPRESSO" }
                        Button("手冲") { brewMethod = "POUR_OVER" }
                        Button("爱乐压") { brewMethod = "AEROPRESS" }
                        Button("冷萃") { brewMethod = "COLD_BREW" }
                        Button("摩卡壶") { brewMethod = "MOKA_POT" }
                        Button("法压壶") { brewMethod = "FRENCH_PRESS" }
                        Button("自动滴滤") { brewMethod = "AUTO_DRIP" }
                    } label: {
                        HStack {
                            Text(getBrewMethodDisplay(brewMethod))
                                .font(.system(size: 16))
                                .foregroundColor(.primaryText)
                            Spacer()
                            Image(systemName: "chevron.down")
                                .foregroundColor(.primaryText)
                        }
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                    }
                }
                .padding(.horizontal, 16)
            }
            
            // 
            if type == Equipment.typeGrindingEquipment {
                VStack(alignment: .leading, spacing: 10) {
                    Text("磨豆机用途")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Menu {
                        Button("意式") { grinderPurpose = "ESPRESSO" }
                        Button("手冲") { grinderPurpose = "POUR_OVER" }
                        Button("通用") { grinderPurpose = "ALL_PURPOSE" }
                    } label: {
                        HStack {
                            Text(getGrinderPurposeDisplay(grinderPurpose))
                                .font(.system(size: 16))
                                .foregroundColor(.primaryText)
                            Spacer()
                            Image(systemName: "chevron.down")
                                .foregroundColor(.primaryText)
                        }
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                    }
                    
                    formFieldSimple(title: "研磨设置", binding: $grindSizePreset, placeholder: "目前所设置的刻度或档位", removePadding: true, isOptional: true)
                }
                .padding(.horizontal, 16)
            }
        }
        .padding(.top, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 基本信息区域
    private var basicInfoSection: some View {
        VStack(spacing: 20) {
            if type != Equipment.typeGadgetKit {
                formFieldSimple(title: "名称", binding: $name, placeholder: "请输入设备名称")
                formFieldSimple(title: "品牌", binding: $brand, placeholder: "请输入品牌", isOptional: true)
            } else {
                formFieldSimple(title: "名称", binding: $name, placeholder: "请输入设备名称")
            }
        }
        .padding(.top, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 详细信息区域
    private var detailsSection: some View {
        Group {
            if type != Equipment.typeGadgetKit {
                VStack(spacing: 20) {
                    // 购买日期和时间选择器
                    VStack(alignment: .leading, spacing: 8) {
                        Text("购买时间")
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(.primaryText)
                        
                        // 使用系统的直接可见DatePicker但自定义样式
                        DatePicker(
                            "",
                            selection: $purchaseDate,
                            displayedComponents: [.date, .hourAndMinute]
                        )
                        .datePickerStyle(CompactDatePickerStyle())
                        .labelsHidden()
                        .accentColor(.functionText)
                        .cornerRadius(12)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 16)
                    
                    // 购买价格
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("购买价格 (元)")
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.primaryText)
                            
                            Text("(选填)")
                                .font(.caption)
                                .foregroundColor(.noteText)
                            
                            Spacer()
                        }
                        
                        TextField("0.00", text: $purchasePrice)
                            .focused($focusedField, equals: "购买价格_field")
                            .keyboardType(.decimalPad)
                            .submitLabel(.done)
                            .font(.system(size: 17))
                            .padding(.vertical, 14)
                            .padding(.horizontal, 16)
                            .background(Color.secondaryBg)
                            .cornerRadius(12)
                            .onChange(of: purchasePrice) { newValue in
                                // 过滤非法输入
                                let filtered = newValue.filter { "0123456789.".contains($0) }
                                
                                // 确保只有一个小数点
                                var components = filtered.components(separatedBy: ".")
                                if components.count > 2 {
                                    components = [components[0], components[1]]
                                    purchasePrice = components.joined(separator: ".")
                                } else if filtered != newValue {
                                    purchasePrice = filtered
                                }
                                
                                // 如果开头是小数点，在前面添加0
                                if purchasePrice.first == "." {
                                    purchasePrice = "0" + purchasePrice
                                }
                            }
                    }
                    .padding(.horizontal, 16)
                }
                .padding(.top, 16)
                .background(Color.primaryBg)
                .cornerRadius(10)
            }
        }
    }
    
    // 小工具组合内容区域
    private var gadgetKitSection: some View {
        VStack(spacing: 20) {
            Text("组合内容")
                .font(.system(size: 16, weight: .regular))
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal, 16)
            
            if availableGadgets.isEmpty {
                Text("没有可用的小工具，请先添加小工具")
                    .foregroundColor(.secondary)
                    .padding(.vertical, 20)
                    .frame(maxWidth: .infinity)
                    .background(Color.secondaryBg.opacity(0.15))
                    .cornerRadius(8)
                    .padding(.horizontal, 16)
            } else {
                VStack(spacing: 0) {
                    ForEach(availableGadgets) { gadget in
                        Button(action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0.2)) {
                                toggleGadgetSelection(gadgetId: gadget.id)
                            }
                        }) {
                            HStack(spacing: 12) {
                                // 圆形勾选框
                                ZStack {
                                    Circle()
                                        .stroke(selectedGadgets.contains(gadget.id) ? Color.functionText : Color.gray.opacity(0.5), lineWidth: 1.5)
                                        .frame(width: 22, height: 22)
                                    
                                    if selectedGadgets.contains(gadget.id) {
                                        Circle()
                                            .fill(Color.functionText)
                                            .frame(width: 22, height: 22)
                                        
                                        Image(systemName: "checkmark")
                                            .font(.system(size: 12, weight: .bold))
                                            .foregroundColor(.white)
                                            .transition(.scale.combined(with: .opacity))
                                    }
                                }
                                .contentShape(Rectangle())
                                
                                // 文本内容，使用VStack在需要时能自动换行而不会产生过多右侧留白
                                VStack(alignment: .leading, spacing: 2) {
                                    if gadget.brand != nil {
                                        Text("\(gadget.brand!)")
                                            .foregroundColor(.noteText)
                                            + Text(" \(gadget.name)")
                                            .foregroundColor(.primaryText)
                                    } else {
                                        Text(gadget.name)
                                            .foregroundColor(.primaryText)
                                    }
                                }
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .layoutPriority(1)
                            }
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)
                            .contentShape(Rectangle())
                            .background(
                                selectedGadgets.contains(gadget.id) 
                                ? Color.secondaryBg.opacity(0.3) 
                                : Color.clear
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        if gadget.id != availableGadgets.last?.id {
                            Divider()
                                .padding(.horizontal, 16)
                        }
                    }
                }
                .background(Color.secondaryBg)
                .cornerRadius(12)
                .padding(.horizontal, 16)
            }
        }
        .padding(.top, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 备注区域
    private var notesSection: some View {
        VStack(spacing: 20) {
            // 备注文本编辑器
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Text("备注")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                    
                    Spacer()
                    
                    // 字数统计
                    Text("\(notes.count)/500")
                        .font(.caption)
                        .foregroundColor(notes.count >= 500 ? .error : .primaryText)
                }
                
                ZStack(alignment: .topLeading) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                        .submitLabel(.done)
                        .focused($focusedField, equals: "备注_field")
                        .scrollContentBackground(.hidden)
                        .background(Color.clear)
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.secondaryBg)
                        )
                        .onChange(of: notes) { newValue in
                            if newValue.count > 500 {
                                notes = String(newValue.prefix(500))
                            }
                        }
                    
                    if notes.isEmpty {
                        Text("请输入备注信息")
                            .foregroundColor(.gray)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 12)
                            .background(Color.clear)
                            .allowsHitTesting(false)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 类型单选按钮组件
    private func typeRadioButton(title: String, value: String, disabled: Bool = false) -> some View {
        Button(action: {
            if !disabled {
                type = value
            }
        }) {
            HStack(spacing: 8) {
                ZStack {
                    Circle()
                        .stroke(type == value ? Color.functionText : (disabled ? Color.gray.opacity(0.5) : Color.gray), lineWidth: 1.5)
                        .frame(width: 20, height: 20)
                    
                    if type == value {
                        Circle()
                            .fill(Color.functionText)
                            .frame(width: 12, height: 12)
                    }
                }
                
                Text(title)
                    .font(.system(size: 15))
                    .foregroundColor(disabled ? Color.gray.opacity(0.5) : (type == value ? .primary : .primaryText))
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 14)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(type == value ? Color.functionText.opacity(0.1) : Color.clear)
            )
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(disabled)
        .opacity(disabled ? 0.6 : 1)
    }
    
    // 简化版表单字段
    private func formFieldSimple(title: String, binding: Binding<String>, placeholder: String? = nil, removePadding: Bool = false, isOptional: Bool = false) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                if isOptional {
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
                
                Spacer()
            }
            
            TextField(placeholder ?? "请输入\(title)", text: binding)
                .focused($focusedField, equals: "\(title)_field")
                .submitLabel(.done)
                .font(.system(size: 17))
                .padding(.vertical, 14)
                .padding(.horizontal, 16)
                .background(Color.secondaryBg)
                .cornerRadius(12)
        }
        .padding(.horizontal, removePadding ? 0 : 16)
    }
    
    // 隐藏键盘方法
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    private func toggleGadgetSelection(gadgetId: Int) {
        if let index = selectedGadgets.firstIndex(of: gadgetId) {
            selectedGadgets.remove(at: index)
        } else {
            selectedGadgets.append(gadgetId)
        }
        
        // 这里不需要调用saveDraft()，因为已经在selectedGadgets的onChange中处理了
    }
    
    private func checkAvailableGadgets() {
        Task {
            let gadgets = await viewModel.fetchAvailableGadgets()
            await MainActor.run {
                self.hasAvailableGadgets = !gadgets.isEmpty
                // 如果当前选择了小工具组合但没有可用小工具，则改为选择冲煮器具
                if type == Equipment.typeGadgetKit && !hasAvailableGadgets {
                    type = Equipment.typeBrewingEquipment
                }
            }
        }
    }
    
    private func loadAvailableGadgets() {
        Task {
            let gadgets = await viewModel.fetchAvailableGadgets()
            await MainActor.run {
                self.availableGadgets = gadgets
                self.hasAvailableGadgets = !gadgets.isEmpty
                
                // 过滤selectedGadgets，确保只包含有效的gadget.id
                if !selectedGadgets.isEmpty {
                    let validGadgetIds = gadgets.map { $0.id }
                    selectedGadgets = selectedGadgets.filter { validGadgetIds.contains($0) }
                }
            }
        }
    }
    
    private func getBrewMethodDisplay(_ method: String) -> String {
        switch method {
        case "POUR_OVER": return "手冲"
        case "ESPRESSO": return "意式"
        case "AEROPRESS": return "爱乐压"
        case "COLD_BREW": return "冷萃"
        case "MOKA_POT": return "摩卡壶"  
        case "FRENCH_PRESS": return "法压壶"
        case "AUTO_DRIP": return "自动滴滤"
        default: return "其他"
        }
    }
    
    private func getGrinderPurposeDisplay(_ purpose: String) -> String {
        switch purpose {
        case "ESPRESSO": return "意式"
        case "POUR_OVER": return "手冲"
        case "ALL_PURPOSE": return "通用"
        default: return "未知"
        }
    }
    
    private func saveEquipment() {
        isSaving = true
        
        // 准备API请求数据
        var requestData: [String: Any] = [
            "name": name,
            "type": type,
            "is_favorite": false,
            "is_archived": false,
        ]
        
        // 添加非空的可选字段
        if !brand.isEmpty { 
            requestData["brand"] = brand 
        }
        
        if !notes.isEmpty { 
            requestData["notes"] = notes 
        }
        
        // 设置购买日期和时间
        // 转换为服务器需要的ISO格式，包含日期和时间
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime]
        requestData["created_at"] = formatter.string(from: purchaseDate)
        
        if !purchasePrice.isEmpty, let price = Double(purchasePrice), price >= 0 {
            requestData["purchase_price"] = price
        }
        
        // 根据设备类型添加特定字段
        if type == Equipment.typeBrewingEquipment {
            requestData["brew_method"] = brewMethod
        } else if type == Equipment.typeGrindingEquipment {
            if !grindSizePreset.isEmpty {
                requestData["grind_size_preset"] = grindSizePreset
            }
            requestData["grinder_purpose"] = grinderPurpose
        } else if type == Equipment.typeGadgetKit && !selectedGadgets.isEmpty {
            requestData["gadget_components"] = selectedGadgets
        }
        
        Task {
            do {
                // 使用更新后的API调用创建设备 - 注意：我们使用PUT方法
                try await viewModel.createEquipmentWithData(requestData)
                
                await MainActor.run {
                    isSaving = false
                    // 保存成功后清除草稿
                    clearDraft()
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isSaving = false
                    errorMessage = error.localizedDescription
                }
            }
        }
    }
    
    // 添加一个格式化日期的辅助函数
    private func formattedDate(_ date: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy年MM月dd日 HH:mm"
        dateFormatter.locale = Locale(identifier: "zh_CN")
        return dateFormatter.string(from: date)
    }
}

#if DEBUG
struct AddEquipmentView_Previews: PreviewProvider {
    static var previews: some View {
        AddEquipmentView(viewModel: EquipmentViewModel())
    }
}
#endif 