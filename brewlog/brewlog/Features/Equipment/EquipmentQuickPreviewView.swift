import SwiftUI

// 设备详情预览视图 - 基于咖啡豆预览视图逻辑
struct EquipmentQuickPreviewView: View {
    // 接收 viewModel 但不使用 ObservedObject
    let viewModel: EquipmentViewModel
    
    // 传入的设备ID
    let equipmentId: Int
    
    // 初始设备数据，仅用于首次渲染
    let initialEquipment: Equipment
    
    // 本地state绑定
    @State private var favoriteState: Bool
    // 添加刷新触发器
    @State private var forceRefresh: UUID = UUID()
    // 添加状态更新锁，避免重入
    @State private var isUpdating: Bool = false
    
    // 初始化方法，直接使用传入的设备
    init(viewModel: EquipmentViewModel, equipment: Equipment) {
        self.viewModel = viewModel
        self.equipmentId = equipment.id
        self.initialEquipment = equipment
        self._favoriteState = State(initialValue: equipment.isFavorite)
    }
    
    // 计算属性：检查是否有任何设备详情数据需要显示
    private func hasDetailInfo(_ equipment: Equipment) -> Bool {
        // 如果任何一个条件满足，则返回true
        return (equipment.type == "GRINDER" && equipment.grindSizePreset != nil && !equipment.grindSizePreset!.isEmpty) ||
               (equipment.purchasePrice != nil && equipment.purchasePrice! > 0) ||
               (equipment.notes != nil && !equipment.notes!.isEmpty) ||
               (equipment.type == "GADGET_KIT" && equipment.gadgetComponents != nil && !equipment.gadgetComponents!.isEmpty) ||
               (equipment.usageCount != nil && equipment.usageCount! > 0)
    }
    
    var body: some View {
        // 尝试获取最新的设备数据，如果找不到则使用初始值
        let equipment = viewModel.equipment.first(where: { $0.id == equipmentId }) ?? initialEquipment
        
        VStack(alignment: .leading, spacing: 12) {
            // 品牌
            HStack {
                Text(equipment.brand ?? "无品牌")
                    .font(.system(size: 16, weight: .light, design: .default))
                    .foregroundColor(.primaryText)
                    .lineLimit(1)
                
                Spacer()
                
                // 显示本地状态变量
                Text(favoriteState ? "★" : "☆")
                    .font(.caption)
                    .opacity(0) // 在UI中不可见，仅作为依赖触发更新
                
                // 添加首选标记
                if favoriteState {
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .font(.subheadline)
                }
            }

            // 设备名称
            Text(equipment.name)
                .font(.headline)
                .lineLimit(1)
            
            // 设备类型和冲煮方式（使用EquipmentBadgeView）
            HStack(spacing: 8) {
                // 冲煮器具
                if equipment.type == "BREWER", let brewMethod = equipment.brewMethodDisplay, !brewMethod.isEmpty {
                    EquipmentBadgeView(
                        leftText: brewMethod,
                        rightText: "冲煮器具",
                        leftBgColor: Color.secondaryBg,
                        rightBgColor: Color.navbarBg,
                        leftTextColor: Color.primaryText,
                        rightTextColor: Color.primaryText
                    )
                }
                
                // 磨豆机
                else if equipment.type == "GRINDER" {
                    EquipmentBadgeView(
                        leftText: equipment.grinderPurposeDisplay ?? "通用",
                        rightText: "磨豆机",
                        leftBgColor: Color.secondaryBg,
                        rightBgColor: Color.navbarBg,
                        leftTextColor: Color.primaryText,
                        rightTextColor: Color.primaryText
                    )
                }
                
                // 小工具
                else if equipment.type == "GADGET" {
                    EquipmentBadgeView(
                        leftText: "小工具",
                        rightText: "",
                        leftBgColor: Color.secondaryBg,
                        rightBgColor: Color.clear,
                        leftTextColor: Color.primaryText,
                        rightTextColor: Color.clear
                    )
                }
                
                // 小工具组合
                else if equipment.type == "GADGET_KIT" {
                    EquipmentBadgeView(
                        leftText: "小工具组合",
                        rightText: "",
                        leftBgColor: Color.secondaryBg,
                        rightBgColor: Color.clear,
                        leftTextColor: Color.primaryText,
                        rightTextColor: Color.clear
                    )
                }
            }
            
            // 只有当有设备详情数据需要显示时才添加分隔线
            if hasDetailInfo(equipment) {
                Divider()
                
                // 设备详情
                VStack(alignment: .leading, spacing: 8) {
                    // 研磨度预设 - 仅磨豆机显示
                    if equipment.type == "GRINDER", let preset = equipment.grindSizePreset, !preset.isEmpty {
                        HStack(spacing: 4) {
                            Image("grindSize.symbols")
                                .font(.callout)
                                .foregroundColor(.linkText)
                            
                            Text("研磨度：" + preset)
                                .font(.callout)
                                .foregroundColor(.primaryText)
                        }
                    }
                    
                    // 价格信息
                    if let price = equipment.purchasePrice, price > 0 {
                        HStack(spacing: 4) {
                            Image("price.symbols")
                                .font(.callout)
                                .foregroundColor(.linkText)
                            
                            Text("\(Int(price))元")
                                .font(.callout)
                                .foregroundColor(.primaryText)
                        }
                    }
                    
                    // 备注信息
                    if let notes = equipment.notes, !notes.isEmpty {
                        HStack(spacing: 4) {
                            Image("note.symbols")
                                .font(.callout)
                                .foregroundColor(.linkText)
                            
                            Text(notes)
                                .font(.callout)
                                .foregroundColor(.primaryText)
                                .lineLimit(2)
                                .truncationMode(.tail)
                        }
                    }
                    
                    // 组件信息 - 仅小工具组合显示
                    if equipment.type == "GADGET_KIT", let components = equipment.gadgetComponents, !components.isEmpty {
                        HStack(spacing: 4) {
                            Image(systemName: "square.grid.2x2")
                                .font(.callout)
                                .foregroundColor(.linkText)
                            
                            Text("包含\(components.count)个小工具")
                                .font(.callout)
                                .foregroundColor(.primaryText)
                        }
                    }
                }
            }
        }
        .padding()
        .frame(width: 320)
        .background(Color(.systemBackground))
        // 强制使用ID刷新整个视图
        .id("equipment-preview-\(equipmentId)-\(favoriteState)-\(forceRefresh)")
        // 在视图显示时立即检查最新状态
        .onAppear {
            // 立即获取最新状态
            updateFavoriteState()
        }
        // 使用 SwiftUI 原生方式接收通知
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("EquipmentStatusUpdated"))) { notification in
            handleStatusNotification(notification)
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("GlobalEquipmentUpdated"))) { _ in
            handleGlobalNotification()
        }
    }
    
    // 处理特定状态通知
    private func handleStatusNotification(_ notification: Notification) {
        // 如果正在更新，忽略此通知以防止重入
        guard !isUpdating,
              let data = notification.userInfo as? [String: Any],
              let updatedId = data["id"] as? Int,
              let newFavoriteState = data["isFavorite"] as? Bool,
              updatedId == equipmentId else {
            return
        }
        
        // 防止重入
        isUpdating = true
        
        // 检查状态是否有变化
        if favoriteState != newFavoriteState {
            DispatchQueue.main.async {
                favoriteState = newFavoriteState
                // 强制刷新
                forceRefresh = UUID()
                
                // 重置锁
                isUpdating = false
            }
        } else {
            isUpdating = false
        }
    }
    
    // 处理全局通知
    private func handleGlobalNotification() {
        // 如果正在更新，忽略此通知以防止重入
        guard !isUpdating else { return }
        
        // 标记为正在更新
        isUpdating = true
        
        // 使用相同的 ID 直接调用更新方法
        DispatchQueue.main.async {
            updateFavoriteState()
            
            // 重置锁
            isUpdating = false
        }
    }
    
    // 更新首选状态
    private func updateFavoriteState() {
        // 如果正在更新，忽略此请求以防止重入
        guard !isUpdating else { return }
        
        // 标记为正在更新
        isUpdating = true
        
        let currentId = equipmentId // 捕获当前ID
        
        // 确保在视图上下文中获取最新数据
        if let updatedEquipment = viewModel.equipment.first(where: { $0.id == currentId }) {
            if favoriteState != updatedEquipment.isFavorite {
                // 在主线程更新状态以确保UI响应
                DispatchQueue.main.async {
                    favoriteState = updatedEquipment.isFavorite
                    // 强制刷新
                    forceRefresh = UUID()
                    
                    // 重置锁
                    isUpdating = false
                }
            } else {
                isUpdating = false
            }
        } else {
            isUpdating = false
        }
    }
}

// 添加EquipmentBadgeView组件
private struct EquipmentBadgeView: View {
    let leftText: String
    let rightText: String
    let leftBgColor: Color
    let rightBgColor: Color
    let leftTextColor: Color
    let rightTextColor: Color
    
    var body: some View {
        // 根据是否有右侧文本来选择不同的视图构建方式
        if rightText.isEmpty {
            // 无右侧文本，仅显示左侧胶囊形状
            singleBadge
        } else {
            // 有右侧文本，显示组合Badge
            combinedBadge
        }
    }
    
    // 单个胶囊形状Badge
    private var singleBadge: some View {
        Text(leftText)
            .font(.caption)
            .padding(.vertical, 2)
            .padding(.horizontal, 6)
            .background(leftBgColor)
            .foregroundColor(leftTextColor)
            .clipShape(Capsule())
            .overlay(
                Capsule()
                    .stroke(Color.detailText.opacity(0.3), lineWidth: 0.5)
            )
    }
    
    // 组合双部分Badge
    private var combinedBadge: some View {
        HStack(spacing: 0) {
            // 左侧部分
            Text(leftText)
                .font(.caption)
                .padding(.vertical, 2)
                .padding(.horizontal, 6)
                .background(leftBgColor)
                .foregroundColor(leftTextColor)
                .clipShape(
                    UnevenRoundedRectangle(
                        topLeadingRadius: 4,
                        bottomLeadingRadius: 4,
                        bottomTrailingRadius: 0,
                        topTrailingRadius: 0
                    )
                )
            
            // 右侧部分
            Text(rightText)
                .font(.caption)
                .padding(.vertical, 2)
                .padding(.horizontal, 6)
                .background(rightBgColor)
                .foregroundColor(rightTextColor)
                .clipShape(
                    UnevenRoundedRectangle(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 0,
                        bottomTrailingRadius: 4,
                        topTrailingRadius: 4
                    )
                )
        }
        .overlay(
            RoundedRectangle(cornerRadius: 4)
                .stroke(Color.detailText.opacity(0.3), lineWidth: 0.5)
        )
    }
} 