import SwiftUI

struct EquipmentListView: View {
    @StateObject private var viewModel = EquipmentViewModel()
    @EnvironmentObject private var themeManager: ThemeManager
    @EnvironmentObject private var appState: AppState
    @State private var searchText = ""
    @State private var showingAddSheet = false
    @State private var showingFilterSheet = false
    @State private var showArchivedEquipment = false
    @State private var resetSwipeStates: Bool = false

    // 删除相关的状态变量
    @State private var equipmentToDelete: Equipment? = nil
    @State private var showDeleteAlert = false
    @State private var isDeleting = false
    @State private var deleteError: String? = nil
    @State private var showDeleteErrorAlert = false

    // 导航相关状态变量
    @State private var selectedEquipment: Equipment? = nil

    // 访问手势设置
    let gestureSettings = GestureSettings.shared

    // 添加状态变量来跟踪活跃设备总数
    @State private var activeEquipmentCount: Int = 0

    // 添加滚动跟踪
    @State private var scrollOffset: CGFloat = 0

    // 添加状态变量来记录是否已初始化
    @State private var isInitialized = false

    var body: some View {
        ZStack {
            contentView
                .navigationTitle("我的咖啡设备")
                .navigationBarTitleDisplayMode(.large)

            // 使用传统的NavigationLink替代navigationDestination
            NavigationLink(
                destination: Group {
                    if let equipment = selectedEquipment {
                        EquipmentDetailView(equipment: equipment)
                    } else {
                        EmptyView()
                    }
                },
                isActive: Binding(
                    get: { selectedEquipment != nil },
                    set: { isActive in
                        if !isActive {
                            // 延迟重置状态，避免在导航过程中立即重置
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                selectedEquipment = nil
                                viewModel.resetNavigationState()
                            }
                        }
                    }
                ),
                label: { EmptyView() }
            )
        }
        .task {
            // 确保总是显示所有设备，包括已归档设备
            viewModel.filter.showArchived = true

            if !isInitialized {
                // 检查数据版本并在需要时更新
                await viewModel.checkDataVersion()
                isInitialized = true
            }
        }
        .onAppear {
            // 确保视图每次出现时都显示所有设备（包括归档设备）
            viewModel.filter.showArchived = true
            viewModel.applyFiltersAndGrouping()

            // 只在没有选中设备时才重置导航状态，避免在导航过程中意外重置
            if selectedEquipment == nil {
                // 在视图出现时检查更新
                Task {
                    await viewModel.fetchEquipment(forceRefresh: true)
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // 当应用从后台恢复时检查更新
            Task {
                await viewModel.checkForUpdates()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("EquipmentUpdated"))) { notification in
            // 当收到设备更新通知时刷新列表
            Task {
                if let updatedEquipment = notification.object as? Equipment {
                    print("🔄 收到设备更新通知，刷新设备列表: ID \(updatedEquipment.id)")
                    // 立即更新本地数据
                    if let index = viewModel.equipment.firstIndex(where: { $0.id == updatedEquipment.id }) {
                        viewModel.equipment[index] = updatedEquipment
                        viewModel.applyFiltersAndGrouping()
                    }
                    // 只有在没有选中设备时才刷新完整列表，避免在导航过程中刷新
                    if selectedEquipment == nil {
                        await viewModel.fetchEquipment(forceRefresh: true)
                    }
                }
            }
        }
        .onChange(of: appState.selectedEquipmentId) { equipmentId in
            if let id = equipmentId {
                // 在设备列表中查找该ID的设备
                if let equipment = viewModel.equipment.first(where: { $0.id == id }) {
                    print("📱 EquipmentListView: 设置选中设备 ID \(id), 名称: \(equipment.name)")
                    selectedEquipment = equipment
                    // 清除ID，防止重复导航
                    appState.selectedEquipmentId = nil
                } else {
                    print("⚠️ EquipmentListView: 未找到设备 ID \(id)")
                }
            }
        }
        .onChange(of: selectedEquipment) { equipment in
            if let equipment = equipment {
                print("🔗 EquipmentListView: selectedEquipment 变为 \(equipment.name)")
            } else {
                print("🔄 EquipmentListView: selectedEquipment 被重置为 nil")
            }
        }
    }

    // 内容视图组件
    private var contentView: some View {
        mainContentView
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    toolbarButtons
                }
            }
            .sheet(isPresented: $showingAddSheet) {
                NavigationView {
                    AddEquipmentView(viewModel: viewModel)
                }
                .onDisappear {
                    // 当添加设备表单关闭时，强制刷新设备列表
                    Task {
                        await viewModel.fetchEquipment(forceRefresh: true)
                    }
                }
            }
            .sheet(isPresented: $showingFilterSheet) {
                EquipmentListFilterPopover(
                    viewModel: viewModel,
                    isPresented: $showingFilterSheet
                )
            }
            // 添加修改设备的sheet
            .sheet(isPresented: $viewModel.shouldShowEditSheet) {
                if let equipment = viewModel.equipmentToEdit {
                    NavigationView {
                        EditEquipmentView(equipment: equipment, viewModel: viewModel)
                    }
                    .onDisappear {
                        // 当修改设备表单关闭时，强制刷新设备列表
                        Task {
                            print("🔄 编辑设备表单关闭，刷新设备列表")
                            await viewModel.fetchEquipment(forceRefresh: true)
                        }
                    }
                }
            }
            .confirmationDialog(
                "确认删除",
                isPresented: $showDeleteAlert,
                titleVisibility: .visible
            ) {
                Button("取消", role: .cancel) {
                    equipmentToDelete = nil
                }

                Button("删除", role: .destructive) {
                    if let equipment = equipmentToDelete {
                        deleteEquipment(equipment)
                    }
                }
                .disabled(isDeleting)
            } message: {
                if equipmentToDelete != nil {
                    if isDeleting {
                        Text("正在删除...")
                    } else {
                        Text("确定要删除这个设备吗？此操作不可撤销。删除后，相关冲煮记录不会受影响。")
                    }
                }
            }
            .alert(
                "删除失败",
                isPresented: $showDeleteErrorAlert,
                actions: {
                    Button("确定", role: .cancel) {}
                },
                message: {
                    Text(deleteError ?? "未知错误")
                }
            )
    }

    // 工具栏按钮
    private var toolbarButtons: some View {
        HStack(spacing: 12) {
            // 筛选按钮
            Button {
                showingFilterSheet = true
            } label: {
                ZStack {
                    Image(systemName: "line.3.horizontal.decrease")
                        .foregroundColor(Color.functionText)
                        .font(.system(size: 15))
                        .frame(width: 30, height: 30)
                        .background(Circle().fill(Color.navbarBg))

                    // 如果有筛选条件，显示指示器
                    if viewModel.filter.hasFilters {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                            .offset(x: 8, y: -8)
                    }
                }
            }

            // 添加按钮
            Button {
                showingAddSheet = true
            } label: {
                Image(systemName: "plus")
                    .foregroundColor(Color.functionText)
                    .font(.system(size: 16))
                    .frame(width: 30, height: 30)
                    .background(Circle().fill(Color.navbarBg))
            }
        }
    }

    // 主要内容视图
    private var mainContentView: some View {
        Group {
            if viewModel.equipment.isEmpty {
                if viewModel.isLoading {
                    VStack {
                        Spacer()
                        // 使用BlinkingLoader替换ProgressView
                        BlinkingLoader(
                            color: .secondaryText,
                            width: 12,
                            height: 18,
                            duration: 1.5,
                            text: "正在加载设备..."
                        )
                        Spacer()
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.secondaryBg)
                } else if let _ = viewModel.error {
                    errorStateView
                } else {
                    emptyStateView
                }
            } else {
                equipmentListView
            }
        }
    }

    // 错误状态视图
    private var errorStateView: some View {
        VStack {
            Spacer()

            VStack(spacing: 20) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 50))
                    .foregroundColor(.error)

                Text("未能读取数据")
                    .font(.headline)

                if let apiError = viewModel.error as? APIError {
                    Text(apiError.userFriendlyMessage)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                } else if let error = viewModel.error {
                    Text(error.localizedDescription)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                } else {
                    Text("因为数据缺失")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }

                VStack(spacing: 12) {
                    Button(action: {
                        Task {
                            await viewModel.fetchEquipment(forceRefresh: true)
                        }
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("刷新")
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(Color.blue)
                        .foregroundColor(.primaryBg)
                        .cornerRadius(8)
                    }

                    Button(action: {
                        Task {
                            URLCache.shared.removeAllCachedResponses()
                            try? await Task.sleep(nanoseconds: 500_000_000) // 等待500毫秒
                            await viewModel.fetchEquipment(forceRefresh: true)
                        }
                    }) {
                        HStack {
                            Image(systemName: "clear")
                            Text("清除缓存并刷新")
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(Color.secondaryBg)
                        .foregroundColor(.primaryText)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray, lineWidth: 1)
                        )
                    }
                }
            }

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.secondaryBg)
    }

    // 空状态视图
    private var emptyStateView: some View {
        VStack {
            Spacer()

            VStack(spacing: 20) {
                Image(systemName: "cup.and.saucer")
                    .font(.system(size: 50))
                    .foregroundColor(Color.secondaryText)
                Text("暂无咖啡设备")
                    .font(.title2)
                    .foregroundColor(Color.primaryText)
                Text("点击右上角 + 添加新的设备吧")
                    .font(.subheadline)
                    .foregroundColor(Color.detailText)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 20)

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.secondaryBg)
    }

    // 设备列表视图
    private var equipmentListView: some View {
        ZStack(alignment: .top) {
            Color.secondaryBg
                .edgesIgnoringSafeArea(.all)

            ScrollViewReader { scrollProxy in
                equipmentsList
                    .searchable(text: $searchText, prompt: "搜索设备")
                    .onChange(of: searchText) { newValue in
                        viewModel.searchText = newValue
                        viewModel.applyFiltersAndGrouping()
                        resetSwipeStates = true
                    }
            }
        }
    }

    // 设备列表
    private var equipmentsList: some View {
        List {
            // 在列表中添加筛选状态标题（在活跃设备区域之前）
            HStack {
                Text(getFilterStatusText())
                    .font(.caption)
                    .foregroundColor(Color.noteText)
                    .padding(.vertical, 4)
            }
            .listRowBackground(Color.secondaryBg)
            .listRowSeparator(.hidden)
            .listRowInsets(EdgeInsets(top: 2, leading: 16, bottom: 4, trailing: 16))
            .id("headerRow")
            .trackScrollOffset() // 添加滚动跟踪

            // 活跃设备区域
            if !viewModel.activeGroups.isEmpty {
                ForEach(viewModel.activeGroups) { group in
                    Section(header:
                        HStack {
                            Text(group.name)
                                .font(.footnote)
                                .foregroundColor(.secondaryText)
                                .padding(.vertical, 4)
                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .listRowInsets(EdgeInsets())
                        .background(Color.secondaryBg)
                    ) {
                        ForEach(group.equipment, id: \.id) { equipment in
                            equipmentRowView(for: equipment)
                                .id(equipment.id)
                        }
                    }
                    .listRowSeparator(.hidden)
                    .listRowInsets(EdgeInsets())
                    .listRowBackground(Color.secondaryBg)
                }
            }

            // 已归档设备区域 - 重构为与活跃设备相同的结构
            if !viewModel.archivedGroups.isEmpty {
                // 添加分隔线增强视觉分隔
                Section {
                    Rectangle()
                        .fill(Color.navbarBg)
                        .frame(height: 8)
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0))

                // 归档标题区域
                Section {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showArchivedEquipment.toggle()
                        }
                    }) {
                        HStack {
                            Spacer()

                            Image(systemName: showArchivedEquipment ? "arrowtriangle.down.fill" : "arrowtriangle.right.fill")
                                .foregroundColor(.detailText)
                                .font(.system(size: 13))

                            Text("已归档设备")
                                .font(.system(size: 15, weight: .semibold))
                                .foregroundColor(.primaryText)

                            let archivedCount = viewModel.archivedGroups.flatMap { $0.equipment }.count
                            Text("(\(archivedCount)个)")
                                .foregroundColor(.secondaryText)
                                .font(.footnote)

                            Spacer()
                        }
                        .contentShape(Rectangle())
                        .padding(.horizontal, 12)
                        .padding(.bottom, 8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets(top: 8, leading: 12, bottom: 0, trailing: 12))

                // 已归档内容区域 - 仅在展开状态显示
                if showArchivedEquipment {
                    ForEach(viewModel.archivedGroups) { group in
                        Section(header:
                            HStack {
                                Text(group.name)
                                    .font(.footnote)
                                    .foregroundColor(.secondaryText)
                                    .padding(.vertical, 4)
                                Spacer()
                            }
                            .padding(.horizontal, 12)
                            .listRowInsets(EdgeInsets())
                            .background(Color.secondaryBg)
                        ) {
                            ForEach(group.equipment, id: \.id) { equipment in
                                equipmentRowView(for: equipment)
                                    .id(equipment.id)
                            }
                        }
                        .listRowSeparator(.hidden)
                        .listRowInsets(EdgeInsets())
                        .listRowBackground(Color.secondaryBg)
                        .transition(.opacity)
                    }
                }
            }

            // 底部加载更多
            if viewModel.hasMoreEquipment {
                HStack {
                    Spacer()
                    // 使用BlinkingLoader替换ProgressView
                    BlinkingLoader(
                        color: .secondaryText,
                        width: 12,
                        height: 15,
                        duration: 1.5,
                        text: "加载更多..."
                    )
                    .onAppear {
                        Task {
                            await viewModel.loadMoreEquipment()
                        }
                    }
                    Spacer()
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
            }

            // 添加列表底部视图 - 到底了提示
            equipmentListFooterView
        }
        .listStyle(PlainListStyle())
        .background(Color.secondaryBg)
        .scrollContentBackground(.hidden) // 隐藏默认的滚动内容背景
        .simultaneousGesture(
            // 监听列表的点击手势，点击空白区域时重置所有轻扫状态
            TapGesture().onEnded { _ in
                resetSwipeStates = true
            }
        )
        .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
            if abs(scrollOffset - value) > 5 {
                // 当滚动距离超过阈值时，如果已归档展开，则收起
                if showArchivedEquipment && abs(scrollOffset - value) > 50 {
                    withAnimation(.easeInOut(duration: 0.25)) {
                        showArchivedEquipment = false
                    }
                }
                scrollOffset = value
                // 同时重置所有轻扫状态，避免在滚动时轻扫菜单保持打开
                resetSwipeStates = true
            }
        }
    }

    // 列表底部视图
    private var equipmentListFooterView: some View {
        Group {
            if !viewModel.equipment.isEmpty && !viewModel.hasMoreEquipment {
                HStack {
                    Spacer()
                    if viewModel.isLoading && !viewModel.equipment.isEmpty {
                        // 使用BlinkingLoader替换ProgressView
                        BlinkingLoader(
                            color: .secondaryText,
                            width: 12,
                            height: 15,
                            duration: 1.5,
                            text: "加载更多..."
                        )
                        .padding(.vertical, 10)
                    } else if viewModel.equipment.count > 0 {
                        Text("— 到底了 —")
                            .font(.footnote)
                            .foregroundColor(Color.noteText.opacity(0.6))
                            .padding(.vertical, 10)
                    }
                    Spacer()
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets(top: 4, leading: 12, bottom: 4, trailing: 12))
            }
        }
    }

    // 设备行视图
    private func equipmentRowView(for equipment: Equipment) -> some View {
        // 使用SwipeItem来实现自定义左右轻扫菜单
        ZStack {
            // 获取左右轻扫设置
            let leftAction = gestureSettings.getAction(for: .equipment, direction: .left)
            let rightAction = gestureSettings.getAction(for: .equipment, direction: .right)

            // 根据设备是否已设为首选，动态调整显示的操作文本和颜色
            let isFavorited = equipment.isFavorite

            // 使用SwipeItem替换原生swipeActions
            SwipeItem(content: {
                ZStack {
                    CardWithPressEffect { isPressed in
                        equipmentCardContent(equipment, isPressed: isPressed)
                    } onTap: {
                        // 导航到详情页
                        selectedEquipment = equipment
                    }
                    .contentShape(Rectangle())
                }
            }, left: {
                // 左侧轻扫内容 (向右轻扫)
                VStack(spacing: 4) {
                    // 为首选操作动态更改图标
                    if rightAction == .favorite && isFavorited {
                        Image(systemName: "star.slash")
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                            .symbolRenderingMode(.monochrome)
                    } else if rightAction == .edit {
                        // 使用自定义图标
                        Image(rightAction.icon)
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                    } else {
                        Image(systemName: rightAction.icon)
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                            .symbolRenderingMode(.monochrome)
                    }

                    // 判断是否是首选操作且已设为首选
                    if rightAction == .favorite && isFavorited {
                        Text("取消首选")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if rightAction == .archive && equipment.isArchived {
                        Text("取消归档")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else {
                        Text(rightAction.rawValue)
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .contentShape(Rectangle())
                .onTapGesture {
                    // 触觉反馈
                    let generator = UIImpactFeedbackGenerator(style: .medium)
                    generator.impactOccurred()

                    // 根据设置的操作执行相应的动作
                    if rightAction == .edit {
                        // 打开修改表单
                        viewModel.openEditEquipmentForm(equipment: equipment)
                    } else if rightAction == .delete {
                        // 调用删除确认方法
                        showDeleteConfirmation(for: equipment)
                    } else if rightAction == .favorite {
                        // 处理首选操作
                        toggleFavorite(equipment)
                    } else if rightAction == .archive {
                        // 处理归档操作
                        toggleArchive(equipment)
                    }

                    // 点击后自动关闭轻扫菜单
                    resetSwipeStates = true
                }
            }, right: { deleteAction in
                // 右侧轻扫内容 (向左轻扫)
                VStack(spacing: 4) {
                    // 为首选操作动态更改图标
                    if leftAction == .favorite && isFavorited {
                        Image(systemName: "star.slash")
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                            .symbolRenderingMode(.monochrome)
                    } else if leftAction == .edit {
                        // 使用自定义图标
                        Image(leftAction.icon)
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                    } else {
                        Image(systemName: leftAction.icon)
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                            .symbolRenderingMode(.monochrome)
                    }

                    // 根据轻扫状态和操作类型显示不同文本
                    if deleteAction && leftAction == .delete {
                        Text("删除")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if deleteAction && leftAction == .edit {
                        Text("修改")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if deleteAction && leftAction == .favorite {
                        // 根据是否已首选显示不同文本
                        Text(isFavorited ? "取消首选" : "设为首选")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if deleteAction && leftAction == .archive {
                        // 根据是否已归档显示不同文本
                        Text(equipment.isArchived ? "取消归档" : "归档")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if leftAction == .favorite && isFavorited {
                        Text("取消首选")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if leftAction == .archive && equipment.isArchived {
                        Text("取消归档")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else {
                        Text(leftAction.rawValue)
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .contentShape(Rectangle())
                .onTapGesture {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.warning)

                    // 根据设置的操作执行相应的动作
                    if leftAction == .delete {
                        // 调用删除确认方法
                        showDeleteConfirmation(for: equipment)
                    } else if leftAction == .edit {
                        // 打开修改表单
                        viewModel.openEditEquipmentForm(equipment: equipment)
                    } else if leftAction == .favorite {
                        // 处理首选操作
                        toggleFavorite(equipment)
                    } else if leftAction == .archive {
                        // 处理归档操作
                        toggleArchive(equipment)
                    }

                    // 点击后自动关闭轻扫菜单
                    resetSwipeStates = true
                }
            }, onDelete: {
                // 向左轻扫完成动作
                if leftAction == .delete {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.warning)

                    // 调用删除确认方法
                    showDeleteConfirmation(for: equipment)
                } else if leftAction == .edit {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 打开修改表单
                    viewModel.openEditEquipmentForm(equipment: equipment)
                } else if leftAction == .favorite {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 处理首选操作
                    toggleFavorite(equipment)
                } else if leftAction == .archive {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 处理归档操作
                    toggleArchive(equipment)
                }
            }, onEdit: {
                // 向右轻扫完成动作
                if rightAction == .edit {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 打开修改表单
                    viewModel.openEditEquipmentForm(equipment: equipment)
                } else if rightAction == .delete {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.warning)

                    // 调用删除确认方法
                    showDeleteConfirmation(for: equipment)
                } else if rightAction == .favorite {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 处理首选操作
                    toggleFavorite(equipment)
                } else if rightAction == .archive {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 处理归档操作
                    toggleArchive(equipment)
                }
            }, resetTrigger: $resetSwipeStates,
               leftActionType: leftAction,
               rightActionType: rightAction,
               isLeftCompareSelected: false,
               isRightCompareSelected: false)
        }
        .frame(height: 136) // 确保整个容器高度固定为136
        .padding(.horizontal, 2)
        .contextMenu {
            // 点击查看详情菜单项
            Button(action: {
                selectedEquipment = equipment
            }) {
                Label("查看详情", systemImage: "eye")
            }

            // 分享选项
            Button {
                shareEquipment(equipment)
            } label: {
                Label("分享文字版", systemImage: "square.and.arrow.up")
            }

            Divider()

            // 首选选项
            if equipment.isFavorite {
                Button {
                    toggleFavorite(equipment)
                } label: {
                    Label("取消首选", systemImage: "star.slash")
                }
            } else {
                Button {
                    toggleFavorite(equipment)
                } label: {
                    Label("设为首选", systemImage: "star")
                }
            }

            // 归档选项
            if equipment.isArchived {
                Button {
                    toggleArchive(equipment)
                } label: {
                    Label("取消归档", systemImage: "archivebox.circle")
                }
            } else {
                Button {
                    toggleArchive(equipment)
                } label: {
                    Label("归档", systemImage: "archivebox")
                }
            }

            Divider()

            // 修改选项 (暂未实现)
            Button {
                viewModel.openEditEquipmentForm(equipment: equipment)
            } label: {
                Label {
                    Text("修改")
                } icon: {
                    Image("edit.symbols")
                }
            }

            // 删除选项
            Button(role: .destructive) {
                showDeleteConfirmation(for: equipment)
            } label: {
                Label("删除", systemImage: "trash")
            }
        } preview: {
            EquipmentQuickPreviewView(viewModel: viewModel, equipment: equipment)
        }
        .listRowBackground(Color.secondaryBg)
        .listRowSeparator(.hidden)
        .listRowInsets(EdgeInsets(top: 8, leading: 12, bottom: 4, trailing: 12))
    }

    // 设备卡片内容
    private func equipmentCardContent(_ equipment: Equipment, isPressed: Bool) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            // 第一行：品牌和收藏图标
            HStack {
                Text(equipment.brand ?? "无品牌")
                    .font(.system(size: 16, weight: .light, design: .default))
                    .foregroundColor(.primaryText)
                    .lineLimit(1)

                Spacer()

                if equipment.isFavorite {
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .font(.subheadline)
                }
            }

            // 第二行：设备名称
            Text(equipment.name)
                .font(.headline)
                .lineLimit(1)

            Spacer(minLength: 0)

            // 第三行和第四行
            HStack(alignment: .top) {
                // 左侧信息区域
                VStack(alignment: .leading, spacing: 8) {
                    // 设备类型标签 - 第三行仅显示设备类型
                    HStack(spacing: 8) {
                        // 设备类型标签 - 合并为BadgeView风格
                        if equipment.type == "BREWER" {
                            EquipmentBadgeView(
                                leftText: equipment.brewMethodDisplay ?? "未知方式",
                                rightText: "冲煮器具",
                                leftBgColor: Color.secondaryBg,
                                rightBgColor: Color.navbarBg,
                                leftTextColor: Color.primaryText,
                                rightTextColor: Color.primaryText
                            )
                        } else if equipment.type == "GRINDER" {
                            EquipmentBadgeView(
                                leftText: equipment.grinderPurposeDisplay ?? "通用",
                                rightText: "磨豆机",
                                leftBgColor: Color.secondaryBg,
                                rightBgColor: Color.navbarBg,
                                leftTextColor: Color.primaryText,
                                rightTextColor: Color.primaryText
                            )
                        } else if equipment.type == "GADGET" {
                            EquipmentBadgeView(
                                leftText: "小工具",
                                rightText: "",
                                leftBgColor: Color.secondaryBg,
                                rightBgColor: Color.clear,
                                leftTextColor: Color.primaryText,
                                rightTextColor: Color.clear
                            )
                        } else if equipment.type == "GADGET_KIT" {
                            EquipmentBadgeView(
                                leftText: "小工具组合",
                                rightText: "",
                                leftBgColor: Color.secondaryBg,
                                rightBgColor: Color.clear,
                                leftTextColor: Color.primaryText,
                                rightTextColor: Color.clear
                            )
                        }

                        // 对于GADGET_KIT类型，额外信息仍然显示在第三行
                        if equipment.type == "GADGET_KIT" {
                            // 备注图标
                            if let notes = equipment.notes, !notes.isEmpty {
                                Image("note.symbols")
                                    .foregroundColor(.linkText)
                                    .font(.callout)
                            }

                            // 价格图标
                            if let price = equipment.purchasePrice, price > 0 {
                                Image("price.symbols")
                                    .foregroundColor(.linkText)
                                    .font(.callout)
                            }
                        }
                    }

                    // 第四行：额外信息或小工具组件
                    VStack(alignment: .leading) {
                        if equipment.type == "GADGET_KIT", let components = equipment.gadgetComponents, !components.isEmpty {
                            // GADGET_KIT的小工具组件显示
                            HStack(spacing: 6) {
                                // 最多显示2个组件
                                let maxComponents = 2
                                let displayCount = min(components.count, maxComponents)
                                let hasMoreComponents = components.count > maxComponents

                                // 显示前两个组件的名称
                                ForEach(0..<displayCount, id: \.self) { index in
                                    componentTagView(text: getComponentName(components[index]))
                                }

                                // 如果有更多组件，显示"+N"
                                if hasMoreComponents {
                                    componentTagView(text: "+\(components.count - maxComponents)")
                                }
                            }
                        } else if equipment.type != "GADGET_KIT" {
                            // 非GADGET_KIT类型设备的额外信息移到第四行
                            HStack(spacing: 8) {
                                // 备注图标
                                if let notes = equipment.notes, !notes.isEmpty {
                                    Image("note.symbols")
                                        .foregroundColor(.linkText)
                                        .font(.callout)
                                }

                                // 价格图标
                                if let price = equipment.purchasePrice, price > 0 {
                                    Image("price.symbols")
                                        .foregroundColor(.linkText)
                                        .font(.callout)
                                }

                                // 研磨设置
                                if equipment.type == "GRINDER", let preset = equipment.grindSizePreset, !preset.isEmpty {
                                    Image("grindSize.symbols")
                                        .foregroundColor(.linkText)
                                        .font(.callout)
                                }

                                Spacer()
                            }
                        }
                    }
                    .frame(height: 22) // 固定高度确保所有卡片一致
                    .padding(.bottom, 1) // 添加一点底部间距，确保视觉上的一致性
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                Spacer(minLength: 8)

                // 右侧进度环 - 如果设备类型不是小工具组合，才显示
                if equipment.type != "GADGET_KIT" {
                    // 使用EquipmentProgressRings组件替代原来的进度环实现
                    EquipmentProgressRings(
                        depreciationRate: equipment.depreciationRate,
                        breakEvenProgress: equipment.breakEvenProgress
                    )
                } else {
                    // 为小工具组合提供一个空占位区域，保持布局一致性
                    ZStack {
                        Circle()
                            .stroke(Color.secondaryBg, lineWidth: 4)
                            .frame(width: 56, height: 56)

                        Image("gadgetKit")
                            .resizable()
                            .renderingMode(.template)
                            .foregroundColor(Color.secondaryText.opacity(0.5))
                            .frame(width: 20, height: 17.777)
                    }
                }
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 14)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isPressed ? Color.focusBg : Color.primaryBg)
                .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
        )
        .frame(height: 136)
    }

    // 小工具组件标签视图
    private func componentTagView(text: String) -> some View {
        Text(text)
            .font(.caption)
            .foregroundColor(.primaryText)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.navbarBg)
            .clipShape(Capsule())
            .overlay(
                Capsule()
                    .stroke(Color.detailText.opacity(0.3), lineWidth: 0.5)
            )
    }

    // 更新组件名称获取方法
    private func getComponentName(_ component: [String: Any]) -> String {
        // 直接从组件字典中获取名称
        if let name = component["name"] as? String {
            return name
        }
        // 如果没有名称，尝试获取ID
        if let id = component["id"] as? Int {
            return "工具#\(id)"
        }
        // 完全无法识别时返回默认值
        return "未知工具"
    }

    // 获取组件详细信息（包含品牌信息）
    private func getComponentDetailText(_ component: [String: Any]) -> String {
        var text = ""

        // 获取品牌信息
        if let brand = component["brand"] as? String, !brand.isEmpty {
            text += brand + " "
        }

        // 获取名称
        if let name = component["name"] as? String {
            text += name
        } else if let id = component["id"] as? Int {
            text += "工具#\(id)"
        } else {
            text += "未知工具"
        }

        return text.isEmpty ? "未知工具" : text
    }

    // 删除确认
    private func showDeleteConfirmation(for equipment: Equipment) {
        equipmentToDelete = equipment
        showDeleteAlert = true
    }

    // 删除设备
    private func deleteEquipment(_ equipment: Equipment) {
        isDeleting = true

        Task {
            await viewModel.deleteEquipment(equipment)
            isDeleting = false
            equipmentToDelete = nil
        }
    }

    // 切换收藏状态
    private func toggleFavorite(_ equipment: Equipment) {
        Task {
            await viewModel.toggleFavorite(equipment)
        }
    }

    // 切换归档状态
    private func toggleArchive(_ equipment: Equipment) {
        Task {
            await viewModel.toggleArchive(equipment)
        }
    }

    // 分享设备信息
    private func shareEquipment(_ equipment: Equipment) {
        // 构建分享文本，按照AddEquipmentView表单字段顺序
        var shareText = "🔧 设备信息 (ID: \(equipment.id))\n\n"

        // 1. 设备类型和基本信息
        shareText += "设备类型: \(equipment.displayType)\n"

        // 根据设备类型添加特定信息
        if equipment.type == Equipment.typeBrewingEquipment {
            if let brewMethod = equipment.brewMethodDisplay {
                shareText += "赛道: \(brewMethod)\n"
            }
        } else if equipment.type == Equipment.typeGrindingEquipment {
            if let grinderPurpose = equipment.grinderPurposeDisplay {
                shareText += "磨豆机用途: \(grinderPurpose)\n"
            }
            if let grindSizePreset = equipment.grindSizePreset, !grindSizePreset.isEmpty {
                shareText += "研磨设置: \(grindSizePreset)\n"
            }
        }

        shareText += "名称: \(equipment.name)\n"

        if let brand = equipment.brand, !brand.isEmpty {
            shareText += "品牌: \(brand)\n"
        }

        // 2. 详细信息（购买信息）
        var hasDetailInfo = false

        if let purchaseDate = equipment.purchaseDate {
            shareText += "\n购买时间: \(DateFormatter.shareFormatter.string(from: purchaseDate))\n"
            hasDetailInfo = true
        }

        if let purchasePrice = equipment.purchasePrice, purchasePrice > 0 {
            shareText += "购买价格: ¥\(String(format: "%.2f", purchasePrice))\n"
            hasDetailInfo = true
        }

        // 3. 小工具组合内容（仅当类型是小工具组合时显示）
        if equipment.type == Equipment.typeGadgetKit {
            if let components = equipment.gadgetComponents, !components.isEmpty {
                shareText += "\n小工具组合内容:\n"
                for (index, component) in components.enumerated() {
                    let componentText = getComponentDetailText(component)
                    shareText += "\(index + 1). \(componentText)\n"
                }
                hasDetailInfo = true
            }
        }

        // 4. 备注
        if let notes = equipment.notes, !notes.isEmpty {
            if hasDetailInfo {
                shareText += "\n"
            }
            shareText += "备注: \(notes)\n"
        }

        // 5. 使用统计（如果有的话）
        var hasUsageStats = false
        if let usageCount = equipment.usageCount, usageCount > 0 {
            shareText += "\n使用次数: \(usageCount)次\n"
            hasUsageStats = true
        }

        if let lastUsed = equipment.lastUsed {
            shareText += "最后使用: \(DateFormatter.shareFormatter.string(from: lastUsed))\n"
            hasUsageStats = true
        }

        // 使用系统分享功能
        let activityViewController = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)

        // 获取当前的UIViewController
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootViewController = window.rootViewController {

            // 对于iPad，需要设置popover的源
            if let popover = activityViewController.popoverPresentationController {
                popover.sourceView = window
                popover.sourceRect = CGRect(x: window.bounds.midX, y: window.bounds.midY, width: 0, height: 0)
                popover.permittedArrowDirections = []
            }

            rootViewController.present(activityViewController, animated: true)
        }
    }

    // 添加一个新函数，用于获取过滤状态文本
    private func getFilterStatusText() -> String {
        let activeCount = viewModel.activeGroups.flatMap { $0.equipment }.count
        let archivedCount = viewModel.archivedGroups.flatMap { $0.equipment }.count

        if !searchText.isEmpty {
            return "共 \(activeCount) 个符合搜索条件的设备"
        }

        // 基础文本部分
        var baseText = "共 \(activeCount) 个"

        // 如果有筛选条件，添加筛选信息
        if viewModel.filter.hasFilters {
            baseText += "符合条件的"
        }

        // 添加设备类型和归档信息
        baseText += "设备"
        if archivedCount > 0 {
            baseText += " · 已归档 \(archivedCount) 个"
        }

        return baseText
    }
}

// 筛选弹窗组件
struct EquipmentListFilterPopover: View {
    @ObservedObject var viewModel: EquipmentViewModel
    @Binding var isPresented: Bool
    @State private var isApplying: Bool = false

    // 临时存储筛选条件的状态变量
    @State private var tempBrandFilter: String? = nil
    @State private var tempTypeFilter: String? = nil
    @State private var tempSortBy: EquipmentSortOption = .defaultOption

    // 判断是否所有筛选项都是默认值
    private var isAllDefaultFilters: Bool {
        return tempBrandFilter == nil &&
               tempTypeFilter == nil &&
               tempSortBy == .defaultOption
    }

    var body: some View {
        NavigationView {
            List {
                // 条件筛选部分
                Section(header: Text("条件")) {
                    // 品牌筛选
                    HStack {
                        Text("品牌")
                        Spacer()
                        Menu {
                            Button("全部") {
                                tempBrandFilter = nil
                            }
                            ForEach(viewModel.availableBrands, id: \.self) { brand in
                                Button(action: {
                                    tempBrandFilter = brand
                                }) {
                                    HStack {
                                        Text(brand)
                                        if tempBrandFilter == brand {
                                            Image(systemName: "checkmark")
                                        }
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Text(tempBrandFilter ?? "全部")
                                    .foregroundColor(tempBrandFilter == nil ? Color.primaryText : Color.linkText)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(tempBrandFilter == nil ? Color.primaryText : Color.linkText)
                            }
                        }
                    }

                    // 类型筛选
                    HStack {
                        Text("类型")
                        Spacer()
                        Menu {
                            Button("全部") {
                                tempTypeFilter = nil
                            }
                            ForEach([
                                ("BREWER", "冲煮器具"),
                                ("GRINDER", "磨豆机"),
                                ("GADGET", "小工具"),
                                ("GADGET_KIT", "小工具组合")
                            ], id: \.0) { type, display in
                                Button(action: {
                                    tempTypeFilter = type
                                }) {
                                    HStack {
                                        Text(display)
                                        if tempTypeFilter == type {
                                            Image(systemName: "checkmark")
                                        }
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Text(getTypeDisplayName(for: tempTypeFilter))
                                    .foregroundColor(tempTypeFilter == nil ? Color.primaryText : Color.linkText)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(tempTypeFilter == nil ? Color.primaryText : Color.linkText)
                            }
                        }
                    }
                }
                .listRowBackground(Color.primaryBg)

                // 排序选项
                Section(header: Text("排序")) {
                    HStack {
                        Text("排序方式")
                        Spacer()
                        Menu {
                            ForEach(SortEquipmentType.allCases) { sortType in
                                Button(action: {
                                    // 如果已经是相同类型的排序，则切换升序/降序
                                    if tempSortBy.sortType == sortType {
                                        tempSortBy = tempSortBy.toggleDirection()
                                    } else {
                                        // 否则使用该类型的默认排序（降序）
                                        tempSortBy = EquipmentSortOption(type: sortType)
                                    }

                                    // 添加触觉反馈
                                    let generator = UIImpactFeedbackGenerator(style: .light)
                                    generator.impactOccurred()

                                    // 预览排序效果，不关闭弹窗
                                    previewSortChange(tempSortBy)
                                }) {
                                    HStack {
                                        Text(tempSortBy.sortType == sortType ? tempSortBy.rawValue : sortType.rawValue)
                                        if tempSortBy.sortType == sortType {
                                            Image(systemName: "checkmark")
                                        }
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Text(tempSortBy.rawValue)
                                    .foregroundColor(Color.linkText)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(Color.linkText)
                            }
                        }
                    }
                }
                .listRowBackground(Color.primaryBg)
            }
            .listStyle(InsetGroupedListStyle())
            .scrollContentBackground(.hidden)
            .onAppear {
                initializeFilters()
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("重置") {
                        resetFilters()
                    }
                    .foregroundColor(isAllDefaultFilters ? Color.gray : Color.linkText)
                    .font(.system(size: 17, weight: .regular))
                    .disabled(isAllDefaultFilters)
                    .animation(.easeInOut(duration: 0.2), value: isAllDefaultFilters)
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("筛选") {
                        applyFilters()
                    }
                    .disabled(isApplying)
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(isApplying ? .gray : Color.linkText)
                }
            }
            .background(Color.secondaryBg)
            .environment(\.locale, Locale(identifier: "zh_CN"))
        }
        .presentationDetents([.height(340), .large])
        .presentationDragIndicator(.visible)
        .interactiveDismissDisabled(isApplying)
        .background(Color.secondaryBg)
        .environment(\.locale, Locale(identifier: "zh_CN"))
    }

    // 获取设备类型显示名称
    private func getTypeDisplayName(for type: String?) -> String {
        if type == nil {
            return "全部"
        }

        switch type {
        case "BREWER": return "冲煮器具"
        case "GRINDER": return "磨豆机"
        case "GADGET": return "小工具"
        case "GADGET_KIT": return "小工具组合"
        default: return "全部"
        }
    }

    // 初始化筛选值
    private func initializeFilters() {
        tempBrandFilter = viewModel.filter.brandFilter
        tempTypeFilter = viewModel.filter.typeFilter
        tempSortBy = viewModel.filter.sortBy

        // 在初始化完成后强制触发UI更新，确保按钮状态正确
        _ = isAllDefaultFilters
    }

    // 重置所有筛选条件
    private func resetFilters() {
        // 添加轻微的触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()

        // 清除所有筛选条件
        withAnimation(.easeInOut(duration: 0.2)) {
            tempBrandFilter = nil
            tempTypeFilter = nil
            tempSortBy = .defaultOption
        }

        // 预览重置后的排序效果
        previewSortChange(tempSortBy)
    }

    // 应用筛选条件
    private func applyFilters() {
        guard !isApplying else { return }

        // 设置正在应用状态，防止多次点击
        isApplying = true

        // 更新筛选条件
        viewModel.filter.brandFilter = tempBrandFilter
        viewModel.filter.typeFilter = tempTypeFilter
        viewModel.filter.sortBy = tempSortBy

        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()

        // 使用与系统一致的动画参数平滑关闭视图
        withAnimation(.spring(response: 0.35, dampingFraction: 1, blendDuration: 0)) {
            isPresented = false
        }

        // 在关闭动画完成后应用筛选条件
        Task {
            try? await Task.sleep(nanoseconds: 350_000_000) // 等待350毫秒，让关闭动画完成

            // 确保应用正确的排序选项
            viewModel.setSortOption(tempSortBy)
            viewModel.applyFiltersAndGrouping()
            isApplying = false
        }
    }

    // 预览排序效果
    private func previewSortChange(_ option: EquipmentSortOption) {
        // 临时应用排序选项，但不保存其他筛选条件的更改
        viewModel.setSortOption(option)

        // 添加额外的振动反馈，表示正在预览效果
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
    }
}

// 添加EquipmentBadgeView组件
private struct EquipmentBadgeView: View {
    let leftText: String
    let rightText: String
    let leftBgColor: Color
    let rightBgColor: Color
    let leftTextColor: Color
    let rightTextColor: Color

    var body: some View {
        // 根据是否有右侧文本来选择不同的视图构建方式
        if rightText.isEmpty {
            // 无右侧文本，仅显示左侧胶囊形状
            singleBadge
        } else {
            // 有右侧文本，显示组合Badge
            combinedBadge
        }
    }

    // 单个胶囊形状Badge
    private var singleBadge: some View {
        Text(leftText)
            .font(.caption)
            .padding(.vertical, 2)
            .padding(.horizontal, 6)
            .background(leftBgColor)
            .foregroundColor(leftTextColor)
            .clipShape(Capsule())
            .overlay(
                Capsule()
                    .stroke(Color.detailText.opacity(0.3), lineWidth: 0.5)
            )
    }

    // 组合双部分Badge
    private var combinedBadge: some View {
        HStack(spacing: 0) {
            // 左侧部分
            Text(leftText)
                .font(.caption)
                .padding(.vertical, 2)
                .padding(.horizontal, 6)
                .background(leftBgColor)
                .foregroundColor(leftTextColor)
                .clipShape(
                    UnevenRoundedRectangle(
                        topLeadingRadius: 4,
                        bottomLeadingRadius: 4,
                        bottomTrailingRadius: 0,
                        topTrailingRadius: 0
                    )
                )

            // 右侧部分
            Text(rightText)
                .font(.caption)
                .padding(.vertical, 2)
                .padding(.horizontal, 6)
                .background(rightBgColor)
                .foregroundColor(rightTextColor)
                .clipShape(
                    UnevenRoundedRectangle(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 0,
                        bottomTrailingRadius: 4,
                        topTrailingRadius: 4
                    )
                )
        }
        .overlay(
            RoundedRectangle(cornerRadius: 4)
                .stroke(Color.detailText.opacity(0.3), lineWidth: 0.5)
        )
    }
}

struct EquipmentListView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            EquipmentListView()
                .environmentObject(ThemeManager.shared)
        }
    }
}
