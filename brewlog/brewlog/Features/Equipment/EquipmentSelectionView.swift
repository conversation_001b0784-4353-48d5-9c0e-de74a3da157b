import SwiftUI

struct EquipmentSelectionView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = BrewLogViewModel()
    @Binding var selectedEquipment: Equipment?
    @State private var searchText = ""
    let type: String
    
    init(selectedEquipment: Binding<Equipment?>, type: String = Equipment.typeBrewingEquipment) {
        self._selectedEquipment = selectedEquipment
        self.type = type
    }
    
    var filteredEquipment: [Equipment] {
        let typeFiltered = viewModel.equipment.filter { $0.type == type }
        if searchText.isEmpty {
            return typeFiltered
        } else {
            return typeFiltered.filter { equipment in
                equipment.name.localizedCaseInsensitiveContains(searchText) ||
                (equipment.brand ?? "").localizedCaseInsensitiveContains(searchText) ||
                (equipment.model ?? "").localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var body: some View {
        List(filteredEquipment, id: \.id) { equipment in
            VStack(alignment: .leading, spacing: 4) {
                Text(equipment.name)
                    .font(.headline)
                if let brand = equipment.brand {
                    Text(brand)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            .contentShape(Rectangle())
            .onTapGesture {
                selectedEquipment = equipment
                dismiss()
            }
        }
        .navigationTitle(type == Equipment.typeBrewingEquipment ? "选择冲煮器具" : "选择研磨器具")
        .searchable(text: $searchText, prompt: "搜索器具")
        .task {
            await viewModel.fetchEquipments()
        }
    }
}

#if DEBUG
struct EquipmentSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            EquipmentSelectionView(selectedEquipment: .constant(nil))
        }
    }
}
#endif 