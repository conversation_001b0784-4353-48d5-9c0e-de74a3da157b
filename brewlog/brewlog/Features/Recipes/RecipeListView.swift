import SwiftUI
import UIKit

// MARK: - 预览Sheet数据模型
struct PreviewSheetData: Identifiable {
    let id = UUID()
    let previewData: PreviewData
    let recipeId: Int
}

struct RecipeListView: View {
    @StateObject private var viewModel = RecipeViewModel()
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var themeManager: ThemeManager
    @StateObject private var gestureSettings = GestureSettings.shared
    @State private var showingFilterSheet = false
    @State private var showingTagManagementSheet = false
    @State private var selectedPreviewData: PreviewData?
    @State private var selectedRecipeId: Int?
    @State private var resetSwipeStates: Bool = false
    @State private var searchText = ""
    @State private var selectedRecipeForAction: RecipeThread?
    @State private var showingRenameAlert = false
    @State private var newRecipeName = ""
    @State private var showingTagSelectionSheet = false

    // 添加视图更新计数器，用于强制刷新视图
    @State private var viewUpdateCounter = 0

    var body: some View {
        ZStack {
            contentView
                .navigationTitle("配方册")
                .navigationBarTitleDisplayMode(.large)
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                toolbarButtons
            }
        }
        .sheet(isPresented: $showingFilterSheet) {
            NavigationStack {
                RecipeFilterSheet(viewModel: viewModel)
            }
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible)
        }
        .sheet(item: Binding<PreviewSheetData?>(
            get: {
                if let previewData = selectedPreviewData,
                   let recipeId = selectedRecipeId {
                    return PreviewSheetData(previewData: previewData, recipeId: recipeId)
                }
                return nil
            },
            set: { newValue in
                if newValue == nil {
                    selectedPreviewData = nil
                    selectedRecipeId = nil
                }
            }
        )) { sheetData in
            NavigationStack {
                RecipePreviewSheet(
                    previewData: sheetData.previewData,
                    recipeId: sheetData.recipeId,
                    viewModel: viewModel
                )
            }
            .presentationDetents([.large])
            .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $showingTagManagementSheet) {
            NavigationStack {
                RecipeTagManagementView(viewModel: viewModel)
            }
            .presentationDetents([.large])
            .presentationDragIndicator(.visible)
        }
        .sheet(item: Binding<RecipeThread?>(
            get: { showingTagSelectionSheet ? selectedRecipeForAction : nil },
            set: { newValue in
                if newValue == nil {
                    showingTagSelectionSheet = false
                    selectedRecipeForAction = nil
                }
            }
        )) { recipe in
            NavigationStack {
                RecipeTagSelectionView(recipe: recipe, viewModel: viewModel)
            }
            .presentationDetents([.large])
            .presentationDragIndicator(.visible)
        }
        .task {
            // 确保视图出现时加载数据
            await viewModel.loadRecipes()
        }
        .onAppear {
            // 视图出现时检查是否需要刷新数据
            Task {
                await viewModel.loadRecipes()
            }
            // 进入视图时强制更新主题颜色
            themeManager.updateThemeColorsOnly()
        }
        // 监听系统主题变化通知
        .onReceive(NotificationCenter.default.publisher(for: TraitChangeObserver.traitChangeNotification)) { _ in
            themeManager.updateThemeColorsOnly()
            // 强制刷新视图以应用新主题
            viewUpdateCounter += 1
        }
        // 监听主题变化通知
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ThemeDidChange"))) { _ in
            // 主题变化时强制刷新视图
            viewUpdateCounter += 1
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("RecipeListNeedsRefresh"))) { _ in
            print("⚡️ 收到RecipeListNeedsRefresh通知，刷新配方列表")

            // 确保在主线程执行UI更新
            Task { @MainActor in
                // 先清除缓存
                URLCache.shared.removeAllCachedResponses()

                // 短暂延迟确保缓存已清除
                try? await Task.sleep(nanoseconds: 100_000_000) // 100毫秒

                // 强制刷新数据
                await viewModel.loadRecipes()

                // 再次延迟确保数据已更新
                try? await Task.sleep(nanoseconds: 100_000_000) // 100毫秒

                // 触发视图完全重新加载
                viewModel.objectWillChange.send()

                print("♻️ 配方列表数据刷新完成并触发视图重载")
            }
        }
        .alert("重命名配方", isPresented: $showingRenameAlert) {
            TextField("配方名称", text: $newRecipeName)
            Button("取消", role: .cancel) {
                newRecipeName = ""
                selectedRecipeForAction = nil
            }
            Button("确认") {
                if let recipe = selectedRecipeForAction {
                    renameRecipe(recipe)
                }
            }
            .disabled(newRecipeName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
        } message: {
            Text("请输入新的配方名称")
        }
    }

    // 内容视图组件
    private var contentView: some View {
        mainContentView
    }

    // 主内容视图
    private var mainContentView: some View {
        recipeListView
    }

    // 工具栏按钮
    private var toolbarButtons: some View {
        HStack(spacing: 12) {
            // 标签管理按钮
            Button {
                showingTagManagementSheet = true
            } label: {
                Image(systemName: "tag")
                    .foregroundColor(.functionText)
                    .font(.system(size: 15))
                    .frame(width: 30, height: 30)
                    .background(Circle().fill(Color.navbarBg))
            }

            // 筛选按钮
            Button {
                showingFilterSheet = true
            } label: {
                ZStack {
                    Image(systemName: "line.3.horizontal.decrease")
                        .foregroundColor(.functionText)
                        .font(.system(size: 15))
                        .frame(width: 30, height: 30)
                        .background(Circle().fill(Color.navbarBg))

                    // 如果有筛选条件，显示指示器
                    if viewModel.viewState.selectedTag != nil || viewModel.viewState.sortOption != .recent {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                            .offset(x: 8, y: -8)
                    }
                }
            }
        }
    }

    // 配方列表视图
    private var recipeListView: some View {
        ZStack(alignment: .top) {
            Color.secondaryBg
                .ignoresSafeArea(.all)

            ScrollViewReader { scrollProxy in
                recipesList
                    .searchable(text: $searchText, prompt: "搜索配方")
                    .onChange(of: searchText) { newValue in
                        viewModel.updateSearchText(newValue)
                        resetSwipeStates = true
                        // 强制更新视图
                        viewUpdateCounter += 1
                    }
                    .onChange(of: viewModel.viewState.selectedTag) { _ in
                        // 当筛选条件改变时，强制刷新视图
                        resetSwipeStates = true
                        viewUpdateCounter += 1
                    }
                    .onChange(of: viewModel.viewState.sortOption) { _ in
                        // 当排序选项改变时，强制刷新视图
                        resetSwipeStates = true
                        viewUpdateCounter += 1
                    }
            }
        }
    }

    // 配方列表
    private var recipesList: some View {
        List {
            // 在列表中添加筛选状态标题
            HStack {
                Text(getFilterStatusText())
                    .font(.caption)
                    .foregroundColor(.noteText)
                    .padding(.vertical, 4)
            }
            .listRowBackground(Color.secondaryBg)
            .listRowSeparator(.hidden)
            .listRowInsets(EdgeInsets(top: 2, leading: 16, bottom: 4, trailing: 16))
            .id("headerRow")



            // 配方列表内容
            if viewModel.viewState.isLoading {
                HStack {
                    Spacer()
                    ProgressView("加载中...")
                    Spacer()
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
            } else if viewModel.viewState.filteredRecipes.isEmpty {
                emptyStateView
                    .listRowBackground(Color.secondaryBg)
                    .listRowSeparator(.hidden)
                    .listRowInsets(EdgeInsets())
            } else {
                ForEach(viewModel.viewState.filteredRecipes) { recipe in
                    recipeRowView(for: recipe)
                        .id("recipe-\(recipe.id)-\(viewUpdateCounter)")
                }
            }

            // 添加列表底部视图 - 到底了提示
            recipeListFooterView
        }
        .listStyle(PlainListStyle())
        .background(Color.secondaryBg)
        .scrollContentBackground(.hidden)
        .simultaneousGesture(
            TapGesture().onEnded { _ in
                resetSwipeStates = true
            }
        )
        .id("recipe-list-\(viewUpdateCounter)")
    }

    // 列表底部视图
    private var recipeListFooterView: some View {
        Group {
            if !viewModel.viewState.filteredRecipes.isEmpty && !viewModel.viewState.isLoading {
                HStack {
                    Spacer()
                    Text("— 到底了 —")
                        .font(.footnote)
                        .foregroundColor(Color.noteText.opacity(0.6))
                        .padding(.vertical, 10)
                    Spacer()
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets(top: 4, leading: 12, bottom: 4, trailing: 12))
            }
        }
    }

    // 配方行视图
    private func recipeRowView(for recipe: RecipeThread) -> some View {
        // 使用SwipeItem来实现自定义左右轻扫菜单
        ZStack {
            // 获取左右轻扫设置
            let leftAction = gestureSettings.getAction(for: .recipe, direction: .left)
            let rightAction = gestureSettings.getAction(for: .recipe, direction: .right)

            // 使用SwipeItem替换原生swipeActions
            SwipeItem(content: {
                ZStack {
                    CardWithPressEffect { isPressed in
                        recipeCardContent(recipe, isPressed: isPressed)
                    } onTap: {
                        // 预览配方
                        print("🔍 点击配方卡片: \(recipe.recipeName)")
                        Task {
                            do {
                                let previewData = try await RecipeService.shared.previewQuickBrew(recipeId: recipe.id)
                                await MainActor.run {
                                    selectedPreviewData = previewData
                                    selectedRecipeId = recipe.id
                                    print("✅ 预览数据加载成功，显示预览页面")
                                }
                            } catch {
                                print("❌ 预览数据加载失败: \(error)")
                                // 确保失败时清理状态
                                await MainActor.run {
                                    selectedPreviewData = nil
                                    selectedRecipeId = nil
                                }
                            }
                        }
                    }
                    .contentShape(Rectangle())
                }
            }, left: {
                // 左轻扫按钮（向右轻扫显示）
                VStack(spacing: 4) {
                    // 根据操作类型使用不同的图标显示方式
                    if leftAction == .quickBrew {
                        // 速记使用系统图标
                        Image(systemName: "bolt")
                            .font(.title2)
                            .foregroundColor(.primaryBg)
                    } else if leftAction == .portal || leftAction == .tag || leftAction == .rename {
                        // 其他自定义图标
                        Image(leftAction.icon)
                            .font(.title2)
                            .foregroundColor(.primaryBg)
                    } else {
                        // 使用系统图标
                        Image(systemName: leftAction.icon)
                            .font(.title2)
                            .foregroundColor(.primaryBg)
                    }
                    Text(leftAction.rawValue)
                        .font(.caption)
                        .foregroundColor(.primaryBg)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .contentShape(Rectangle())
                .onTapGesture {
                    // 触觉反馈
                    let generator = UIImpactFeedbackGenerator(style: .medium)
                    generator.impactOccurred()

                    // 根据设置的操作执行相应的动作
                    if leftAction == .portal {
                        // 传送门
                        if let latestRecord = recipe.latestRecord {
                            print("🌀 传送门跳转到记录: \(latestRecord.id)")

                            // 使用优化的导航请求
                            DispatchQueue.main.async {
                                print("📡 设置待处理的传送门导航，记录ID: \(latestRecord.id)")

                                // 先切换到冲煮记录标签页
                                appState.selectedTab = .brewLog
                                print("📍 切换到冲煮记录标签页")

                                // 设置待处理的导航请求，让BrewLogListView在数据加载完成后处理
                                appState.pendingBrewLogNavigation = latestRecord.id
                                print("📝 设置待处理导航请求: \(latestRecord.id)")
                            }
                        }
                    } else if leftAction == .quickBrew {
                        // 速记
                        print("🚀 执行速记操作: \(recipe.recipeName)")
                        Task {
                            do {
                                let record = try await RecipeService.shared.quickBrew(recipeId: recipe.id)
                                // 更新配方册缓存
                                await viewModel.refreshRecipes()

                                // 发送通知更新配方册缓存（确保其他地方也能收到更新）
                                await MainActor.run {
                                    NotificationCenter.default.post(name: NSNotification.Name("RecipeListNeedsRefresh"), object: nil)
                                    NotificationCenter.default.post(name: NSNotification.Name("BrewLogListNeedsRefresh"), object: nil)
                                    print("📣 发送RecipeListNeedsRefresh和BrewLogListNeedsRefresh通知 - 配方册速记")
                                }

                                // 通过安全的内部导航跳转到冲煮记录详情页
                                await MainActor.run {
                                    appState.navigateToBrewLog(id: record.id)
                                }
                            } catch {
                                print("❌ 速记失败: \(error)")
                            }
                        }
                    } else if leftAction == .tag {
                        // 贴标签
                        print("🏷️ 设置贴标签配方 (左侧): \(recipe.recipeName)")
                        selectedRecipeForAction = recipe
                        showingTagSelectionSheet = true
                        print("🏷️ 显示标签选择 sheet (左侧)")
                    } else if leftAction == .rename {
                        // 重命名
                        selectedRecipeForAction = recipe
                        newRecipeName = recipe.recipeName
                        showingRenameAlert = true
                    }

                    // 点击后自动关闭轻扫菜单
                    resetSwipeStates = true
                }
            }, right: { deleteAction in
                // 右轻扫按钮（向左轻扫显示）
                VStack(spacing: 4) {
                    // 根据操作类型使用不同的图标显示方式
                    if rightAction == .quickBrew {
                        // 速记使用系统图标
                        Image(systemName: "bolt")
                            .font(.title2)
                            .foregroundColor(.primaryBg)
                    } else if rightAction == .portal || rightAction == .tag || rightAction == .rename {
                        // 其他自定义图标
                        Image(rightAction.icon)
                            .font(.title2)
                            .foregroundColor(.primaryBg)
                    } else {
                        // 使用系统图标
                        Image(systemName: rightAction.icon)
                            .font(.title2)
                            .foregroundColor(.primaryBg)
                    }
                    Text(rightAction.rawValue)
                        .font(.caption)
                        .foregroundColor(.primaryBg)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .contentShape(Rectangle())
                .onTapGesture {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.warning)

                    // 根据设置的操作执行相应的动作
                    if rightAction == .portal {
                        // 传送门
                        if let latestRecord = recipe.latestRecord {
                            print("🌀 传送门跳转到记录: \(latestRecord.id)")

                            // 使用优化的导航请求
                            DispatchQueue.main.async {
                                print("📡 设置待处理的传送门导航，记录ID: \(latestRecord.id)")

                                // 先切换到冲煮记录标签页
                                appState.selectedTab = .brewLog
                                print("📍 切换到冲煮记录标签页")

                                // 设置待处理的导航请求，让BrewLogListView在数据加载完成后处理
                                appState.pendingBrewLogNavigation = latestRecord.id
                                print("📝 设置待处理导航请求: \(latestRecord.id)")
                            }
                        }
                    } else if rightAction == .quickBrew {
                        // 速记
                        print("🚀 执行速记操作: \(recipe.recipeName)")
                        Task {
                            do {
                                let record = try await RecipeService.shared.quickBrew(recipeId: recipe.id)
                                // 更新配方册缓存
                                await viewModel.refreshRecipes()

                                // 发送通知更新配方册缓存（确保其他地方也能收到更新）
                                await MainActor.run {
                                    NotificationCenter.default.post(name: NSNotification.Name("RecipeListNeedsRefresh"), object: nil)
                                    NotificationCenter.default.post(name: NSNotification.Name("BrewLogListNeedsRefresh"), object: nil)
                                    print("📣 发送RecipeListNeedsRefresh和BrewLogListNeedsRefresh通知 - 配方册速记")
                                }

                                // 通过安全的内部导航跳转到冲煮记录详情页
                                await MainActor.run {
                                    appState.navigateToBrewLog(id: record.id)
                                }
                            } catch {
                                print("❌ 速记失败: \(error)")
                            }
                        }
                    } else if rightAction == .tag {
                        // 贴标签
                        print("🏷️ 设置贴标签配方: \(recipe.recipeName)")
                        selectedRecipeForAction = recipe
                        showingTagSelectionSheet = true
                    } else if rightAction == .rename {
                        // 重命名
                        selectedRecipeForAction = recipe
                        newRecipeName = recipe.recipeName
                        showingRenameAlert = true
                    }

                    // 点击后自动关闭轻扫菜单
                    resetSwipeStates = true
                }
            }, onDelete: {
                // 长拉左轻扫触发的操作
                print("🔥 长拉左轻扫触发操作: \(leftAction.rawValue)")

                // 触觉反馈
                let generator = UINotificationFeedbackGenerator()
                generator.notificationOccurred(.warning)

                // 根据设置的操作执行相应的动作
                if leftAction == .portal {
                    // 传送门
                    if let latestRecord = recipe.latestRecord {
                        print("🌀 传送门跳转到记录: \(latestRecord.id)")

                        // 使用优化的导航请求
                        DispatchQueue.main.async {
                            print("📡 设置待处理的传送门导航，记录ID: \(latestRecord.id)")

                            // 先切换到冲煮记录标签页
                            appState.selectedTab = .brewLog
                            print("📍 切换到冲煮记录标签页")

                            // 设置待处理的导航请求，让BrewLogListView在数据加载完成后处理
                            appState.pendingBrewLogNavigation = latestRecord.id
                            print("📝 设置待处理导航请求: \(latestRecord.id)")
                        }
                    }
                } else if leftAction == .quickBrew {
                    // 速记
                    print("🚀 执行速记操作: \(recipe.recipeName)")
                    Task {
                        do {
                            let record = try await RecipeService.shared.quickBrew(recipeId: recipe.id)
                            // 更新配方册缓存
                            await viewModel.refreshRecipes()

                            // 发送通知更新配方册缓存（确保其他地方也能收到更新）
                            await MainActor.run {
                                NotificationCenter.default.post(name: NSNotification.Name("RecipeListNeedsRefresh"), object: nil)
                                NotificationCenter.default.post(name: NSNotification.Name("BrewLogListNeedsRefresh"), object: nil)
                                print("📣 发送RecipeListNeedsRefresh和BrewLogListNeedsRefresh通知 - 配方册速记")
                            }

                            // 通过安全的内部导航跳转到冲煮记录详情页
                            await MainActor.run {
                                appState.navigateToBrewLog(id: record.id)
                            }
                        } catch {
                            print("❌ 速记失败: \(error)")
                        }
                    }
                } else if leftAction == .tag {
                    // 贴标签
                    selectedRecipeForAction = recipe
                    showingTagSelectionSheet = true
                } else if leftAction == .rename {
                    // 重命名
                    selectedRecipeForAction = recipe
                    newRecipeName = recipe.recipeName
                    showingRenameAlert = true
                }

                // 操作后自动关闭轻扫菜单
                resetSwipeStates = true
            }, onEdit: {
                // 长拉右轻扫触发的操作
                print("✏️ 长拉右轻扫触发操作: \(rightAction.rawValue)")

                // 触觉反馈
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()

                // 根据设置的操作执行相应的动作
                if rightAction == .quickBrew {
                    // 速记
                    print("🚀 执行速记操作: \(recipe.recipeName)")
                    Task {
                        do {
                            let record = try await RecipeService.shared.quickBrew(recipeId: recipe.id)
                            // 更新配方册缓存
                            await viewModel.refreshRecipes()

                            // 发送通知更新配方册缓存（确保其他地方也能收到更新）
                            await MainActor.run {
                                NotificationCenter.default.post(name: NSNotification.Name("RecipeListNeedsRefresh"), object: nil)
                                NotificationCenter.default.post(name: NSNotification.Name("BrewLogListNeedsRefresh"), object: nil)
                                print("📣 发送RecipeListNeedsRefresh和BrewLogListNeedsRefresh通知 - 配方册速记")
                            }

                            // 通过安全的内部导航跳转到冲煮记录详情页
                            await MainActor.run {
                                appState.navigateToBrewLog(id: record.id)
                            }
                        } catch {
                            print("❌ 速记失败: \(error)")
                        }
                    }
                } else if rightAction == .portal {
                    // 传送门
                    if let latestRecord = recipe.latestRecord {
                        print("🌀 传送门跳转到记录: \(latestRecord.id)")

                        // 使用优化的导航请求
                        DispatchQueue.main.async {
                            print("📡 设置待处理的传送门导航，记录ID: \(latestRecord.id)")

                            // 先切换到冲煮记录标签页
                            appState.selectedTab = .brewLog
                            print("📍 切换到冲煮记录标签页")

                            // 设置待处理的导航请求，让BrewLogListView在数据加载完成后处理
                            appState.pendingBrewLogNavigation = latestRecord.id
                            print("📝 设置待处理导航请求: \(latestRecord.id)")
                        }
                    }
                } else if rightAction == .tag {
                    // 贴标签
                    selectedRecipeForAction = recipe
                    showingTagSelectionSheet = true
                } else if rightAction == .rename {
                    // 重命名
                    selectedRecipeForAction = recipe
                    newRecipeName = recipe.recipeName
                    showingRenameAlert = true
                }

                // 操作后自动关闭轻扫菜单
                resetSwipeStates = true
            }, resetTrigger: $resetSwipeStates,
               leftActionType: leftAction,
               rightActionType: rightAction)
            .frame(height: 120)
        }
        .padding(.horizontal, 2)
        .contextMenu(menuItems: {
            recipeContextMenu(recipe)
        }, preview: {
            // 使用与列表卡片一致的预览内容
            recipeCardContent(recipe, isPressed: false)
                .frame(width: UIScreen.main.bounds.width - 40)
        })
        .listRowBackground(Color.secondaryBg)
        .listRowSeparator(.hidden)
        .listRowInsets(EdgeInsets(top: 6, leading: 12, bottom: 6, trailing: 12))
    }

    // 配方卡片内容
    private func recipeCardContent(_ recipe: RecipeThread, isPressed: Bool) -> some View {
        VStack(alignment: .leading, spacing: 0) {
            // 使用统计（移到顶部，和 web 版一致的格式）
            HStack {
                HStack(spacing: 0) {
                    Text("冲煮过")
                        .font(.caption)
                        .foregroundColor(.noteText)
                    Text(" \(recipe.useCount) ")
                        .font(.caption)
                        .foregroundColor(.primaryText)
                    Text("次，最后用于")
                        .font(.caption)
                        .foregroundColor(.noteText)
                    Text(" \(recipe.formattedLastUsedTime)")
                        .font(.caption)
                        .foregroundColor(.primaryText)
                }

                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)

            // 配方名称
            Text(recipe.recipeName)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)
                .lineLimit(2)
                .padding(.horizontal, 16)
                .padding(.top, 8)

            // 标签
            if !recipe.tags.isEmpty {
                TagFlowView(recipeTags: recipe.tags, maxLines: 1)
                    .padding(.horizontal, 16)
                    .padding(.top, 12)
            }

            Spacer()
        }
        .background(Color.primaryBg)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.1), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
    }

    // 配方上下文菜单
    @ViewBuilder
    private func recipeContextMenu(_ recipe: RecipeThread) -> some View {
        // 预览速记
        Button(action: {
            Task {
                do {
                    let previewData = try await RecipeService.shared.previewQuickBrew(recipeId: recipe.id)
                    await MainActor.run {
                        selectedPreviewData = previewData
                        selectedRecipeId = recipe.id
                    }
                } catch {
                    print("❌ 预览数据加载失败: \(error)")
                    // 确保失败时清理状态
                    await MainActor.run {
                        selectedPreviewData = nil
                        selectedRecipeId = nil
                    }
                }
            }
        }) {
            Label("预览速记", systemImage: "eye")
        }

        Divider()

        // 速记
        Button(action: {
            Task {
                do {
                    let record = try await RecipeService.shared.quickBrew(recipeId: recipe.id)
                    // 更新配方册缓存
                    await viewModel.refreshRecipes()

                    // 发送通知更新配方册缓存（确保其他地方也能收到更新）
                    await MainActor.run {
                        NotificationCenter.default.post(name: NSNotification.Name("RecipeListNeedsRefresh"), object: nil)
                        NotificationCenter.default.post(name: NSNotification.Name("BrewLogListNeedsRefresh"), object: nil)
                        print("📣 发送RecipeListNeedsRefresh和BrewLogListNeedsRefresh通知 - 配方册速记")
                    }

                    // 通过安全的内部导航跳转到冲煮记录详情页
                    await MainActor.run {
                        appState.navigateToBrewLog(id: record.id)
                    }
                } catch {
                    print("❌ 速记失败: \(error)")
                }
            }
        }) {
            Label {
                Text("速记")
            } icon: {
                Image(systemName: "bolt")
            }
        }

        // 传送门
        if recipe.latestRecord != nil {
            Button(action: {
                if let latestRecord = recipe.latestRecord {
                    print("🌀 传送门跳转到记录: \(latestRecord.id)")

                    // 使用优化的导航请求
                    DispatchQueue.main.async {
                        print("📡 设置待处理的传送门导航，记录ID: \(latestRecord.id)")

                        // 先切换到冲煮记录标签页
                        appState.selectedTab = .brewLog
                        print("📍 切换到冲煮记录标签页")

                        // 设置待处理的导航请求，让BrewLogListView在数据加载完成后处理
                        appState.pendingBrewLogNavigation = latestRecord.id
                        print("📝 设置待处理导航请求: \(latestRecord.id)")
                    }
                }
            }) {
                Label {
                    Text("传送门")
                } icon: {
                    Image("portal.symbols")
                }
            }
        }

        // 贴标签
        Button(action: {
            selectedRecipeForAction = recipe
            showingTagSelectionSheet = true
        }) {
            Label {
                Text("贴标签")
            } icon: {
                Image("recipeTags.symbols")
            }
        }

        Divider()

        // 重命名
        Button(action: {
            selectedRecipeForAction = recipe
            newRecipeName = recipe.recipeName
            showingRenameAlert = true
        }) {
            Label {
                Text("重命名")
            } icon: {
                Image("recipeRename.symbols")
            }
        }
    }

    // 获取筛选状态文本
    private func getFilterStatusText() -> String {
        let totalCount = viewModel.viewState.filteredRecipes.count

        if !searchText.isEmpty {
            return "共 \(totalCount) 个符合搜索条件的配方"
        }

        var baseText = "共 \(totalCount) 个"

        // 如果有筛选条件，添加筛选信息
        if viewModel.viewState.selectedTag != nil {
            baseText += "符合条件的"
        }

        baseText += "配方"

        return baseText
    }

    // MARK: - 重命名配方
    private func renameRecipe(_ recipe: RecipeThread) {
        let trimmedName = newRecipeName.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty else { return }

        Task {
            do {
                let updatedRecipe = try await RecipeService.shared.renameRecipe(recipeId: recipe.id, newName: trimmedName)
                await MainActor.run {
                    // 刷新配方列表
                    Task {
                        await viewModel.refreshRecipes()
                    }

                    // 发送通知刷新相关视图
                    NotificationCenter.default.post(name: NSNotification.Name("RecipeListNeedsRefresh"), object: nil)
                    NotificationCenter.default.post(name: NSNotification.Name("BrewLogListNeedsRefresh"), object: nil)

                    // 清理状态
                    newRecipeName = ""
                    selectedRecipeForAction = nil
                    print("✅ 配方重命名成功: \(updatedRecipe.recipeName)")
                    print("📡 已发送配方和冲煮记录列表刷新通知")
                }
            } catch {
                await MainActor.run {
                    print("❌ 配方重命名失败: \(error)")
                    // 清理状态
                    newRecipeName = ""
                    selectedRecipeForAction = nil
                }
            }
        }
    }

    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "book.closed")
                .font(.system(size: 60))
                .foregroundColor(.noteText)

            Text("暂无配方")
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.primaryText)

            Text("在冲煮记录中填写配方名称，系统会自动收集并整理成配方册")
                .font(.body)
                .foregroundColor(.noteText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)

            Button {
                appState.selectedTab = .brewLog
            } label: {
                Text("去添加冲煮记录")
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.primaryBg)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.primaryAccent)
                    .cornerRadius(8)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.secondaryBg.ignoresSafeArea())
    }
}

// MARK: - 筛选芯片组件
struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .primaryBg : .primaryText)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? Color.blue : Color.primaryBg)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 筛选表单
struct RecipeFilterSheet: View {
    @ObservedObject var viewModel: RecipeViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var isApplying: Bool = false

    // 临时存储筛选条件的状态变量
    @State private var tempSelectedTag: RecipeTag?
    @State private var tempSortOption: RecipeSortOption

    // 初始化器，正确设置临时状态变量
    init(viewModel: RecipeViewModel) {
        self.viewModel = viewModel
        // 使用当前的筛选状态初始化临时变量
        let currentTag = viewModel.viewState.selectedTag
        let currentSort = viewModel.viewState.sortOption

        self._tempSelectedTag = State(initialValue: currentTag)
        self._tempSortOption = State(initialValue: currentSort)
    }

    // 判断是否所有筛选项都是默认值
    private var isAllDefaultFilters: Bool {
        return tempSelectedTag == nil && tempSortOption == .recent
    }

    var body: some View {
        List {
            // 条件筛选部分
            Section(header: Text("条件")) {
                // 排序方式
                HStack {
                    Text("排序方式")
                    Spacer()
                    Menu {
                        ForEach(RecipeSortOption.allCases, id: \.self) { option in
                            Button(action: {
                                tempSortOption = option
                            }) {
                                HStack {
                                    Text(option.displayName)
                                    if tempSortOption == option {
                                        Image(systemName: "checkmark")
                                    }
                                }
                            }
                        }
                    } label: {
                        HStack {
                            Text(tempSortOption.displayName)
                                .foregroundColor(tempSortOption == .recent ? Color.primaryText : Color.linkText)
                            Image(systemName: "chevron.down")
                                .font(.caption)
                                .foregroundColor(tempSortOption == .recent ? Color.primaryText : Color.linkText)
                        }
                    }
                }

                // 标签筛选
                HStack {
                    Text("标签筛选")
                    Spacer()
                    Menu {
                        Button("全部") {
                            tempSelectedTag = nil
                        }
                        ForEach(viewModel.viewState.tags) { tag in
                            Button(action: {
                                tempSelectedTag = tag
                            }) {
                                HStack {
                                    Text(tag.name)
                                    if tempSelectedTag?.id == tag.id {
                                        Image(systemName: "checkmark")
                                    }
                                }
                            }
                        }
                    } label: {
                        HStack {
                            Text(tempSelectedTag?.name ?? "全部")
                                .foregroundColor(tempSelectedTag == nil ? Color.primaryText : Color.linkText)
                            Image(systemName: "chevron.down")
                                .font(.caption)
                                .foregroundColor(tempSelectedTag == nil ? Color.primaryText : Color.linkText)
                        }
                    }
                }
            }
            .listRowBackground(Color.primaryBg)
        }
        .listStyle(InsetGroupedListStyle())
        .scrollContentBackground(.hidden)
        .navigationTitle("")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("重置") {
                    resetFilters()
                }
                .foregroundColor(isAllDefaultFilters ? Color.gray : Color.linkText)
                .font(.system(size: 17, weight: .regular))
                .disabled(isAllDefaultFilters)
                .animation(.easeInOut(duration: 0.2), value: isAllDefaultFilters)
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("筛选") {
                    applyFilters()
                }
                .disabled(isApplying)
                .font(.system(size: 17, weight: .semibold))
                .foregroundColor(isApplying ? .gray : Color.linkText)
            }
        }
        .presentationDetents([.height(280), .large])
        .presentationDragIndicator(.visible)
        .interactiveDismissDisabled(isApplying)
        .background(Color.secondaryBg)
        .environment(\.locale, Locale(identifier: "zh_CN"))
    }

    // 重置所有筛选条件
    private func resetFilters() {
        // 添加轻微的触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()

        // 清除所有筛选条件
        withAnimation(.easeInOut(duration: 0.2)) {
            tempSelectedTag = nil
            tempSortOption = .recent
        }
    }

    // 应用筛选条件
    private func applyFilters() {
        guard !isApplying else { return }

        // 设置正在应用状态，防止多次点击
        isApplying = true

        // 更新筛选条件
        viewModel.selectTag(tempSelectedTag)
        viewModel.changeSortOption(tempSortOption)

        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()

        // 使用与系统一致的动画参数平滑关闭视图
        withAnimation(.spring(response: 0.35, dampingFraction: 1, blendDuration: 0)) {
            dismiss()
        }

        // 在关闭动画完成后重置状态
        Task {
            try? await Task.sleep(nanoseconds: 350_000_000) // 等待350毫秒，让关闭动画完成
            isApplying = false
        }
    }
}

// MARK: - 参数卡片
struct ParameterCard: View {
    let title: String
    let value: String

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.noteText)

            Text(value)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(.primaryText)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(12)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
    }
}





// MARK: - 预览
#if DEBUG
struct RecipeListView_Previews: PreviewProvider {
    static var previews: some View {
        RecipeListView()
            .environmentObject(AppState.shared)
    }
}
#endif
