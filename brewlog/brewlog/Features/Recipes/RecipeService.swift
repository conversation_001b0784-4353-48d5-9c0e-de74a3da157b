import Foundation
import SwiftUI

class RecipeService: ObservableObject {
    static let shared = RecipeService()
    private let apiService = APIService.shared

    private init() {}

    // MARK: - 获取配方列表
    func fetchRecipes(tagId: Int? = nil, sortBy: RecipeSortOption = .recent) async throws -> [RecipeThread] {
        var parameters: [String: String] = [
            "sort_by": sortBy.rawValue
        ]

        if let tagId = tagId {
            parameters["tag"] = "\(tagId)"
        }

        let recipes: [RecipeThread] = try await apiService.get(
            "/ios/api/recipes/",
            parameters: parameters
        )

        return recipes
    }

    // MARK: - 获取配方标签列表
    func fetchRecipeTags() async throws -> [RecipeTag] {
        return try await apiService.get("/ios/api/recipes/tags/")
    }

    // MARK: - 快速冲煮
    func quickBrew(recipeId: Int) async throws -> BrewingRecord {
        let response: QuickBrewResponse = try await apiService.post(
            "/ios/api/recipes/\(recipeId)/quick-brew/",
            body: [:]
        )

        guard response.status == "success", let record = response.record else {
            throw APIError.serverErrorSimple(response.message ?? "快速冲煮失败")
        }

        return record
    }

    // MARK: - 预览速记
    func previewQuickBrew(recipeId: Int) async throws -> PreviewData {
        let response: PreviewQuickBrewResponse = try await apiService.get("/ios/api/recipes/\(recipeId)/preview/")

        guard response.status == "success", let preview = response.preview else {
            throw APIError.serverErrorSimple(response.message ?? "获取预览数据失败")
        }

        return preview
    }

    // MARK: - 重命名配方
    func renameRecipe(recipeId: Int, newName: String) async throws -> RecipeThread {
        let response: RenameRecipeResponse = try await apiService.post(
            "/ios/api/recipes/\(recipeId)/rename/",
            body: ["new_name": newName]
        )

        guard response.status == "success", let recipe = response.recipe else {
            throw APIError.serverErrorSimple(response.message ?? "重命名配方失败")
        }

        return recipe
    }

    // MARK: - 更新配方标签
    func updateRecipeTags(recipeId: Int, tagIds: [Int]) async throws -> RecipeThread {
        let response: UpdateRecipeTagsResponse = try await apiService.post(
            "/ios/api/recipes/\(recipeId)/tags/",
            body: ["tag_ids": tagIds]
        )

        guard response.status == "success", let recipe = response.recipe else {
            throw APIError.serverErrorSimple(response.message ?? "更新配方标签失败")
        }

        return recipe
    }

    // MARK: - 创建标签
    func createTag(name: String) async throws -> RecipeTag {
        let response: CreateTagResponse = try await apiService.post(
            "/ios/api/recipes/tags/",
            body: ["name": name]
        )

        guard response.status == "success", let tag = response.tag else {
            throw APIError.serverErrorSimple(response.message ?? "创建标签失败")
        }

        return tag
    }

    // MARK: - 更新标签
    func updateTag(id: Int, name: String) async throws -> RecipeTag {
        let response: CreateTagResponse = try await apiService.put(
            "/ios/api/recipes/tags/\(id)/",
            body: ["name": name]
        )

        guard response.status == "success", let tag = response.tag else {
            throw APIError.serverErrorSimple(response.message ?? "更新标签失败")
        }

        return tag
    }

    // MARK: - 删除标签
    func deleteTag(id: Int) async throws {
        let response: DeleteTagResponse = try await apiService.delete("/ios/api/recipes/tags/\(id)/")

        guard response.status == "success" else {
            throw APIError.serverErrorSimple(response.message ?? "删除标签失败")
        }
    }
}

// MARK: - 配方视图模型
@MainActor
class RecipeViewModel: ObservableObject {
    @Published var viewState = RecipeViewState()
    private let recipeService = RecipeService.shared

    init() {
        // 在初始化时立即加载缓存数据（如果有）
        loadCachedDataIfAvailable()
    }

    // MARK: - 加载缓存数据
    private func loadCachedDataIfAvailable() {
        if let cachedRecipes = UserDefaults.standard.data(forKey: "cachedRecipes"),
           let cachedTags = UserDefaults.standard.data(forKey: "cachedTags"),
           let recipes = try? JSONDecoder().decode([RecipeThread].self, from: cachedRecipes),
           let tags = try? JSONDecoder().decode([RecipeTag].self, from: cachedTags) {
            // 立即显示缓存数据
            viewState.recipes = recipes
            viewState.tags = tags
            print("✅ 已加载缓存的配方数据，配方数量: \(recipes.count)，标签数量: \(tags.count)")
        } else {
            print("🔄 没有找到缓存的配方数据")
        }
    }

    // MARK: - 加载配方列表
    func loadRecipes() async {
        // 如果已经有数据且不在加载中，则不需要显示加载状态
        let hasExistingData = !viewState.recipes.isEmpty
        if !hasExistingData {
            viewState.isLoading = true
        }
        viewState.errorMessage = nil

        do {
            // 并行加载配方和标签
            async let recipesTask = recipeService.fetchRecipes(
                tagId: viewState.selectedTag?.id,
                sortBy: viewState.sortOption
            )
            async let tagsTask = recipeService.fetchRecipeTags()

            let (recipes, tags) = try await (recipesTask, tagsTask)

            // 更新数据
            viewState.recipes = recipes
            viewState.tags = tags

            // 缓存数据
            if let encodedRecipes = try? JSONEncoder().encode(recipes) {
                UserDefaults.standard.set(encodedRecipes, forKey: "cachedRecipes")
            }

            if let encodedTags = try? JSONEncoder().encode(tags) {
                UserDefaults.standard.set(encodedTags, forKey: "cachedTags")
            }

            viewState.isLoading = false
        } catch {
            viewState.errorMessage = error.localizedDescription
            viewState.isLoading = false
        }
    }

    // MARK: - 刷新配方列表
    func refreshRecipes() async {
        do {
            let recipes = try await recipeService.fetchRecipes(
                tagId: viewState.selectedTag?.id,
                sortBy: viewState.sortOption
            )

            await MainActor.run {
                viewState.recipes = recipes
                // 强制触发视图更新
                objectWillChange.send()
            }
        } catch {
            await MainActor.run {
                viewState.errorMessage = error.localizedDescription
            }
        }
    }

    // MARK: - 快速冲煮
    func quickBrew(recipeId: Int) async -> BrewingRecord? {
        do {
            let record = try await recipeService.quickBrew(recipeId: recipeId)
            // 刷新配方列表以更新使用次数
            await refreshRecipes()
            return record
        } catch {
            viewState.errorMessage = error.localizedDescription
            return nil
        }
    }

    // MARK: - 预览速记
    func previewQuickBrew(recipeId: Int) async -> PreviewData? {
        do {
            return try await recipeService.previewQuickBrew(recipeId: recipeId)
        } catch {
            viewState.errorMessage = error.localizedDescription
            return nil
        }
    }

    // MARK: - 筛选和排序
    func selectTag(_ tag: RecipeTag?) {
        viewState.selectedTag = tag
        // 手动触发更新通知，确保视图立即响应
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
        Task {
            await refreshRecipes()
        }
    }

    func changeSortOption(_ option: RecipeSortOption) {
        viewState.sortOption = option
        // 手动触发更新通知，确保视图立即响应
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
        Task {
            await refreshRecipes()
        }
    }

    func updateSearchText(_ text: String) {
        viewState.searchText = text
        // 手动触发更新通知，确保视图立即响应
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }

    func clearFilters() {
        viewState.clearFilters()
        // 手动触发更新通知，确保视图立即响应
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
        Task {
            await refreshRecipes()
        }
    }
}

// MARK: - 预览数据扩展
extension PreviewData {
    // 格式化冲煮时间
    var formattedBrewingTime: String {
        let minutes = brewingTime / 60
        let seconds = brewingTime % 60

        if minutes > 0 {
            return String(format: "%d:%02d", minutes, seconds)
        } else {
            return "\(seconds)秒"
        }
    }

    // 格式化水温
    var formattedWaterTemperature: String {
        if waterTemperature > 0 {
            return String(format: "%.0f°C", waterTemperature)
        } else {
            return "未设置"
        }
    }

    // 格式化重量
    var formattedDoseWeight: String {
        return String(format: "%.1fg", doseWeight)
    }

    var formattedYieldWeight: String {
        return String(format: "%.1fg", yieldWeight)
    }

    // 计算粉水比
    var brewRatio: String {
        if doseWeight > 0 && yieldWeight > 0 {
            let ratio = yieldWeight / doseWeight
            return String(format: "1:%.1f", ratio)
        }
        return "未知"
    }
}
