import SwiftUI

// MARK: - 预览表单
struct RecipePreviewSheet: View {
    let previewData: PreviewData
    let recipeId: Int
    @ObservedObject var viewModel: RecipeViewModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var appState: AppState
    @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 8) {
                // 顶部标题区域 - 用户名称和日期
                VStack(alignment: .leading, spacing: 0) {
                    HStack {
                        Text("\(previewData.userName)")
                            .foregroundColor(.primaryText)

                        Text("记于 \(formattedDate(Date()))")
                            .foregroundColor(.primaryText.opacity(0.5))
                    }
                    .font(.subheadline)
                    .lineLimit(nil)
                    .fixedSize(horizontal: false, vertical: true)
                    .padding(.bottom, 8)
                    .frame(maxWidth: .infinity, alignment: .leading)

                    // 冲煮信息标签
                    FlowLayout(alignment: .leading, spacing: 8) {
                        // 显示真实的冲煮方法
                        Badge {
                            BrewMethodIcon(method: previewData.brewMethod)
                            Text(getMethodDisplayText(method: previewData.brewMethod))
                        }
                        .modifier(CustomBadgeBackground(color: Color.navbarBg))

                        Badge {
                            Image("clock.symbols")
                            Text(NumberFormatters.formatBrewingTime(Double(previewData.brewingTime)))
                        }
                        .modifier(CustomBadgeBackground(color: Color.navbarBg))

                        Badge {
                            Image("ratio.symbols")
                            Text(NumberFormatters.formatBrewRatio(previewData.yieldWeight / previewData.doseWeight))
                        }
                        .modifier(CustomBadgeBackground(color: Color.navbarBg))
                    }
                    .padding(.top, 4)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // 预览说明（替代评价与备注）
                HStack(alignment: .bottom, spacing: 4) {
                    ZStack {
                        Text("📝")
                            .font(.title2)
                    }

                    BubbleView(notesEmpty: false) {
                        Text("这是配方的速记预览，确认后将创建一条冲煮记录")
                            .foregroundColor(Color.primaryText)
                    }
                }
                .padding(.bottom, 4)

                // 关键参数卡片
                HStack(spacing: 12) {
                    KeyParamCard(icon: "grinder", title: previewData.grindSize == "-" ? "-" : "#\(previewData.grindSize)", subtitle: previewData.waterTemperature > 0 ? " " : nil)

                    KeyParamCard(icon: "bean", title: NumberFormatters.formatWeight(previewData.doseWeight), subtitle: previewData.waterTemperature > 0 ? " " : nil)

                    KeyParamCard(icon: "droplet", title: NumberFormatters.formatWeight(previewData.yieldWeight), subtitle: previewData.waterTemperature > 0 ? NumberFormatters.formatTemperature(previewData.waterTemperature) + "°C" : nil)
                }
                .padding(.vertical, 8)

                // 详细信息列表
                VStack(alignment: .leading, spacing: 10) {
                    // 显示真实的咖啡豆信息（使用 getBeanDetailText 结构）
                    DetailItem(label: "用豆", value: getBeanDetailText())

                    if !previewData.waterQuality.isEmpty {
                        DetailItem(label: "用水", value: previewData.waterQuality)
                    }

                    // 显示真实的器具信息（冲煮器具+磨豆机）
                    DetailItem(label: "器具", value: getEquipmentDetailText())

                    // 显示小工具信息（匹配 BrewLogDetailView 的样式）
                    if !previewData.gadgetNames.isEmpty {
                        VStack(alignment: .leading, spacing: 6) {
                            HStack(alignment: .top, spacing: 12) {
                                Text("小物")
                                    .font(.system(size: 15))
                                    .frame(width: 40, alignment: .leading)
                                    .foregroundColor(.primaryText.opacity(0.6))

                                VStack(alignment: .leading, spacing: 6) {
                                    ForEach(previewData.gadgetNames, id: \.self) { gadgetName in
                                        HStack(spacing: 4) {
                                            Text(gadgetName)
                                                .font(.system(size: 15))

                                            // 如果是小工具组合，显示组合标志（参考 web 端样式）
                                            if previewData.gadgetNames.count == 1 && previewData.gadgetKit != nil {
                                                Text("组合")
                                                    .font(.caption)
                                                    .foregroundColor(.secondaryText)
                                                    .padding(.horizontal, 6)
                                                    .padding(.vertical, 2)
                                                    .background(Color.secondaryBg)
                                                    .clipShape(Capsule())
                                                    .overlay(
                                                        Capsule()
                                                            .stroke(Color.detailText.opacity(0.3), lineWidth: 0.5)
                                                    )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .font(.system(size: 15))
                .padding(.vertical, 8)

                // 冲煮步骤
                if !previewData.steps.isEmpty {
                    SectionDivider(title: "详细步骤")

                    // 步骤列表
                    VStack(alignment: .leading, spacing: 16) {
                        ForEach(Array(previewData.steps.enumerated()), id: \.offset) { index, step in
                            HStack(alignment: .top, spacing: 12) {
                                Text("\(index + 1)")
                                    .font(.system(size: 32, weight: .light))
                                    .foregroundColor(.primaryText.opacity(0.3))
                                    .frame(width: 30, alignment: .leading)

                                VStack(alignment: .leading, spacing: 8) {
                                    // 使用高亮文本替换普通文本
                                    AdaptiveHighlightedText(
                                        step.description,
                                        font: .subheadline,
                                        foregroundColor: Color.primaryText
                                    )
                                    .fixedSize(horizontal: false, vertical: true)

                                    if let timer = step.timer, !timer.isEmpty {
                                        Text("⏱ \(timer)")
                                            .font(.subheadline)
                                            .foregroundColor(.primaryText.opacity(0.5))
                                    }
                                }
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                    }
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 24)
        }
        .background(Color.primaryBg)
        .navigationTitle(previewData.recipeName)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("确认速记") {
                    Task {
                        if let record = await viewModel.quickBrew(recipeId: recipeId) {
                            dismiss()

                            // 发送通知更新相关列表
                            await MainActor.run {
                                NotificationCenter.default.post(name: NSNotification.Name("RecipeListNeedsRefresh"), object: nil)
                                NotificationCenter.default.post(name: NSNotification.Name("BrewLogListNeedsRefresh"), object: nil)
                                print("📣 发送RecipeListNeedsRefresh和BrewLogListNeedsRefresh通知 - 预览速记")
                            }

                            // 通过安全的内部导航跳转到冲煮记录详情页
                            await MainActor.run {
                                appState.navigateToBrewLog(id: record.id)
                            }
                        }
                    }
                }
                .fontWeight(.semibold)
                .foregroundColor(.linkText)
            }
        }
    }

    // MARK: - 辅助函数
    private func getBeanDetailText() -> String {
        // 构建咖啡豆详细信息，匹配 BrewLogDetailView 的格式
        var text = "\(previewData.coffeeBeanRoaster) \(previewData.coffeeBeanName)"

        // 添加烘焙度
        if !previewData.coffeeBeanRoastLevelDisplay.isEmpty {
            text += "，\(previewData.coffeeBeanRoastLevelDisplay)"
        }

        // 添加处理法
        if !previewData.coffeeBeanProcess.isEmpty {
            text += previewData.coffeeBeanProcess
        }

        // 添加产地信息（优先级：origin > region > finca）
        if !previewData.coffeeBeanOrigin.isEmpty {
            text += "（\(previewData.coffeeBeanOrigin)）"
        } else if !previewData.coffeeBeanRegion.isEmpty {
            text += "（\(previewData.coffeeBeanRegion)）"
        } else if !previewData.coffeeBeanFinca.isEmpty {
            text += "（\(previewData.coffeeBeanFinca)）"
        }

        return text
    }

    private func getEquipmentDetailText() -> String {
        // 构建器具详细信息，匹配 BrewLogDetailView 的格式：冲煮器具 + 磨豆机
        var text = previewData.brewingEquipmentName
        text += " + "
        text += previewData.grindingEquipmentName
        return text
    }

    private func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60

        if minutes > 0 {
            return String(format: "%d:%02d", minutes, remainingSeconds)
        } else {
            return String(format: "00:%02d", remainingSeconds)
        }
    }

    private func formattedDate(_ date: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "M月d日 HH:mm"
        return dateFormatter.string(from: date)
    }

    /// 将冲煮方式代码转换为显示名称
    private func getMethodDisplayText(method: String) -> String {
        // 转换为大写以便统一处理
        let uppercaseMethod = method.uppercased()

        switch uppercaseMethod {
        case "ESPRESSO":
            return "意式"
        case "POUR_OVER":
            return "手冲"
        case "AEROPRESS":
            return "爱乐压"
        case "COLD_BREW":
            return "冷萃"
        case "MOKA_POT":
            return "摩卡壶"
        case "FRENCH_PRESS":
            return "法压壶"
        case "AUTO_DRIP":
            return "自动滴滤"
        default:
            return method
        }
    }

    @ViewBuilder
    private func BrewMethodIcon(method: String) -> some View {
        // 转换为大写以便统一处理
        let uppercaseMethod = method.uppercased()

        switch uppercaseMethod {
        case "ESPRESSO", "意式":
            Image("espresso.symbols")
        case "POUR_OVER", "手冲":
            Image("pourover.symbols")
        case "AEROPRESS", "爱乐压":
            Image("aeropresso.symbols")
        case "COLD_BREW", "冷萃":
            Image("coldbrew.symbols")
        case "MOKA_POT", "摩卡壶":
            Image("mokapot.symbols")
        case "FRENCH_PRESS", "法压壶":
            Image("frenchpress.symbols")
        case "AUTO_DRIP", "自动滴滤":
            Image("autodrip.symbols")
        default:
            Image(systemName: "cup.and.saucer")
        }
    }
}

