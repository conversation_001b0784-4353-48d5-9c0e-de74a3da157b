import SwiftUI
import Foundation

struct RecipeTagManagementView: View {
    @ObservedObject var viewModel: RecipeViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showingCreateTagAlert = false
    @State private var newTagName = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var editingTag: RecipeTag?
    @State private var editTagName = ""
    @State private var showingEditAlert = false
    @State private var deletingTag: RecipeTag?
    @State private var showingDeleteAlert = false

    var body: some View {
        VStack(spacing: 0) {
            // 标签列表
            if viewModel.viewState.tags.isEmpty {
                emptyStateView
            } else {
                tagListView
            }
        }
        .background(Color.secondaryBg)
        .navigationTitle("标签管理")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button {
                    showingCreateTagAlert = true
                } label: {
                    Image(systemName: "plus")
                }
                .foregroundColor(Color.linkText)
            }
        }
        .alert("创建新标签", isPresented: $showingCreateTagAlert) {
            TextField("标签名称", text: $newTagName)
            Button("取消", role: .cancel) {
                newTagName = ""
            }
            Button("创建") {
                createTag()
            }
            .disabled(newTagName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
        } message: {
            Text("请输入新标签的名称")
        }
        .alert("编辑标签", isPresented: $showingEditAlert) {
            TextField("标签名称", text: $editTagName)
            Button("取消", role: .cancel) {
                editingTag = nil
                editTagName = ""
            }
            Button("保存") {
                if let tag = editingTag {
                    updateTag(tag)
                }
            }
            .disabled(editTagName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
        } message: {
            Text("请输入新的标签名称")
        }
        .overlay {
            if isLoading {
                ProgressView("处理中...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.3))
            }
        }
        .alert("删除标签", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) {
                deletingTag = nil
            }
            Button("删除", role: .destructive) {
                if let tag = deletingTag {
                    deleteTag(tag)
                }
                deletingTag = nil
            }
        } message: {
            if let tag = deletingTag {
                Text("确定要删除标签「\(tag.name)」吗？此操作无法撤销。")
            }
        }
        .alert("错误", isPresented: .constant(errorMessage != nil)) {
            Button("确定") {
                errorMessage = nil
            }
        } message: {
            if let errorMessage = errorMessage {
                Text(errorMessage)
            }
        }
    }

    // 标签列表视图
    private var tagListView: some View {
        List {
            Section(header: Text("所有标签 (左右滑动可编辑/删除标签)").font(.footnote).foregroundColor(.secondary)) {
                ForEach(viewModel.viewState.tags) { tag in
                    tagRowView(tag: tag)
                        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                            // 向右滑动 = 删除
                            Button {
                                deletingTag = tag
                                showingDeleteAlert = true
                            } label: {
                                Label("删除", systemImage: "trash")
                            }
                            .tint(.red)
                        }
                        .swipeActions(edge: .leading, allowsFullSwipe: false) {
                            // 向左滑动 = 编辑
                            Button {
                                editingTag = tag
                                editTagName = tag.name
                                showingEditAlert = true
                            } label: {
                                Label("编辑", systemImage: "pencil")
                            }
                            .tint(.blue)
                        }
                }
            }
            .listRowBackground(Color.primaryBg)
        }
        .listStyle(InsetGroupedListStyle())
        .scrollContentBackground(.hidden)
        .background(Color.secondaryBg)
    }

    // 标签行视图
    private func tagRowView(tag: RecipeTag) -> some View {
        HStack {
            // 标签名称
            Text(tag.name)
                .font(.body)
                .foregroundColor(.primary)

            Spacer()

            // 使用次数
            Text("使用次数: \(getTagUsageCount(tag))")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 4)
    }

    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "tag")
                .font(.system(size: 60))
                .foregroundColor(.archivedText)

            Text("暂无标签")
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.detailText)

            Text("点击右上角的 + 按钮创建新标签")
                .font(.body)
                .foregroundColor(.noteText)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.secondaryBg)
    }

    // 获取标签使用次数
    private func getTagUsageCount(_ tag: RecipeTag) -> Int {
        return viewModel.viewState.recipes.filter { recipe in
            recipe.tags.contains { $0.id == tag.id }
        }.count
    }

    // 创建标签
    private func createTag() {
        let trimmedName = newTagName.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty else { return }

        isLoading = true
        Task {
            do {
                let newTag = try await RecipeService.shared.createTag(name: trimmedName)

                await MainActor.run {
                    isLoading = false
                    newTagName = ""

                    // 立即更新本地标签列表
                    viewModel.viewState.tags.append(newTag)

                    // 触发视图更新
                    viewModel.objectWillChange.send()

                    print("✅ 标签创建成功: \(newTag.name)")
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "创建标签失败: \(error.localizedDescription)"
                }
            }
        }
    }

    // 更新标签
    private func updateTag(_ tag: RecipeTag) {
        let trimmedName = editTagName.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty else { return }

        isLoading = true
        Task {
            do {
                let updatedTag = try await RecipeService.shared.updateTag(id: tag.id, name: trimmedName)

                await MainActor.run {
                    isLoading = false
                    editingTag = nil
                    editTagName = ""

                    // 立即更新本地标签列表
                    if let index = viewModel.viewState.tags.firstIndex(where: { $0.id == tag.id }) {
                        viewModel.viewState.tags[index] = updatedTag
                    }

                    // 触发视图更新
                    viewModel.objectWillChange.send()

                    print("✅ 标签更新成功: \(updatedTag.name)")
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    editingTag = nil
                    editTagName = ""
                    errorMessage = "更新标签失败: \(error.localizedDescription)"
                }
            }
        }
    }

    // 删除标签
    private func deleteTag(_ tag: RecipeTag) {
        isLoading = true
        Task {
            do {
                try await RecipeService.shared.deleteTag(id: tag.id)

                await MainActor.run {
                    isLoading = false

                    // 立即从本地标签列表中移除
                    viewModel.viewState.tags.removeAll { $0.id == tag.id }

                    // 如果当前选中的标签被删除，清除选择
                    if viewModel.viewState.selectedTag?.id == tag.id {
                        viewModel.viewState.selectedTag = nil
                    }

                    // 触发视图更新
                    viewModel.objectWillChange.send()

                    print("✅ 标签删除成功: \(tag.name)")
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "删除标签失败: \(error.localizedDescription)"
                }
            }
        }
    }
}

#if DEBUG
struct RecipeTagManagementView_Previews: PreviewProvider {
    static var previews: some View {
        RecipeTagManagementView(viewModel: RecipeViewModel())
    }
}
#endif
