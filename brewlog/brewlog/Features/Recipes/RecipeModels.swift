import Foundation
import SwiftUI

// MARK: - 配方标签模型
struct RecipeTag: Codable, Identifiable, Hashable {
    let id: Int
    let name: String
    let createdAt: Double

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case createdAt = "created_at"
    }

    // 用于预览的初始化方法
    #if DEBUG
    init(id: Int, name: String, createdAt: Double = Date().timeIntervalSince1970) {
        self.id = id
        self.name = name
        self.createdAt = createdAt
    }
    #endif
}

// MARK: - 配方主题模型
struct RecipeThread: Codable, Identifiable, Hashable {
    let id: Int
    let recipeName: String
    let useCount: Int
    let createdAt: Double
    let updatedAt: Double
    let lastUsedAt: Double?
    let tags: [RecipeTag]
    let latestRecord: LatestRecord?

    enum CodingKeys: String, CodingKey {
        case id
        case recipeName = "recipe_name"
        case useCount = "use_count"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case lastUsedAt = "last_used_at"
        case tags
        case latestRecord = "latest_record"
    }

    // 计算属性：格式化的最后使用时间
    var formattedLastUsedTime: String {
        guard let lastUsedAt = lastUsedAt else {
            return "从未使用"
        }

        let lastUsedDate = Date(timeIntervalSince1970: lastUsedAt)
        let now = Date()
        let timeInterval = now.timeIntervalSince(lastUsedDate)

        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else {
            let days = Int(timeInterval / 86400)
            return "\(days)天前"
        }
    }

    // 用于预览的初始化方法
    #if DEBUG
    init(id: Int, recipeName: String, useCount: Int, createdAt: Double = Date().timeIntervalSince1970, updatedAt: Double = Date().timeIntervalSince1970, lastUsedAt: Double? = nil, tags: [RecipeTag] = [], latestRecord: LatestRecord? = nil) {
        self.id = id
        self.recipeName = recipeName
        self.useCount = useCount
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.lastUsedAt = lastUsedAt
        self.tags = tags
        self.latestRecord = latestRecord
    }
    #endif
}

// MARK: - 最新记录信息
struct LatestRecord: Codable, Hashable {
    let id: Int
    let createdAt: Double

    enum CodingKeys: String, CodingKey {
        case id
        case createdAt = "created_at"
    }
}

// MARK: - 配方列表响应
struct RecipeListResponse: Codable {
    let recipes: [RecipeThread]
    let tags: [RecipeTag]

    // 用于预览的初始化方法
    #if DEBUG
    init(recipes: [RecipeThread], tags: [RecipeTag]) {
        self.recipes = recipes
        self.tags = tags
    }
    #endif
}

// MARK: - 快速冲煮响应
struct QuickBrewResponse: Codable {
    let status: String
    let record: BrewingRecord?
    let message: String?
}

// MARK: - 速记预览响应
struct PreviewQuickBrewResponse: Codable {
    let status: String
    let preview: PreviewData?
    let message: String?
}

// MARK: - 重命名配方响应
struct RenameRecipeResponse: Codable {
    let status: String
    let recipe: RecipeThread?
    let message: String?
}

// MARK: - 创建标签响应
struct CreateTagResponse: Codable {
    let status: String
    let tag: RecipeTag?
    let message: String?
}

// MARK: - 删除标签响应
struct DeleteTagResponse: Codable {
    let status: String
    let message: String?
}

// MARK: - 预览数据
struct PreviewData: Codable {
    let recipeName: String
    let brewingEquipment: Int
    let grindingEquipment: Int
    let coffeeBean: Int
    let grindSize: String
    let doseWeight: Double
    let yieldWeight: Double
    let waterTemperature: Double
    let brewingTime: Int
    let waterQuality: String
    let steps: [PreviewStep]
    let gadgetKit: Int?
    let gadgets: [Int]?
    // 新增显示名称字段
    let brewingEquipmentName: String
    let grindingEquipmentName: String
    let coffeeBeanName: String
    let brewMethod: String
    let gadgetNames: [String]
    let userName: String

    // 咖啡豆详细信息字段
    let coffeeBeanRoaster: String
    let coffeeBeanRoastLevelDisplay: String
    let coffeeBeanProcess: String
    let coffeeBeanOrigin: String
    let coffeeBeanRegion: String
    let coffeeBeanFinca: String
    let coffeeBeanVariety: String

    enum CodingKeys: String, CodingKey {
        case recipeName = "recipe_name"
        case brewingEquipment = "brewing_equipment"
        case grindingEquipment = "grinding_equipment"
        case coffeeBean = "coffee_bean"
        case grindSize = "grind_size"
        case doseWeight = "dose_weight"
        case yieldWeight = "yield_weight"
        case waterTemperature = "water_temperature"
        case brewingTime = "brewing_time"
        case waterQuality = "water_quality"
        case steps
        case gadgetKit = "gadget_kit"
        case gadgets
        case brewingEquipmentName = "brewing_equipment_name"
        case grindingEquipmentName = "grinding_equipment_name"
        case coffeeBeanName = "coffee_bean_name"
        case brewMethod = "brew_method"
        case gadgetNames = "gadget_names"
        case userName = "user_name"
        case coffeeBeanRoaster = "coffee_bean_roaster"
        case coffeeBeanRoastLevelDisplay = "coffee_bean_roast_level_display"
        case coffeeBeanProcess = "coffee_bean_process"
        case coffeeBeanOrigin = "coffee_bean_origin"
        case coffeeBeanRegion = "coffee_bean_region"
        case coffeeBeanFinca = "coffee_bean_finca"
        case coffeeBeanVariety = "coffee_bean_variety"
    }

    // 自定义解码器，处理可能为 null 或字符串的字段
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        recipeName = try container.decode(String.self, forKey: .recipeName)
        brewingEquipment = try container.decode(Int.self, forKey: .brewingEquipment)
        grindingEquipment = try container.decode(Int.self, forKey: .grindingEquipment)
        coffeeBean = try container.decode(Int.self, forKey: .coffeeBean)
        grindSize = try container.decode(String.self, forKey: .grindSize)
        doseWeight = try container.decode(Double.self, forKey: .doseWeight)
        yieldWeight = try container.decode(Double.self, forKey: .yieldWeight)
        steps = try container.decode([PreviewStep].self, forKey: .steps)
        gadgetKit = try container.decodeIfPresent(Int.self, forKey: .gadgetKit)
        gadgets = try container.decodeIfPresent([Int].self, forKey: .gadgets)

        // 解码新增的显示名称字段
        brewingEquipmentName = try container.decode(String.self, forKey: .brewingEquipmentName)
        grindingEquipmentName = try container.decode(String.self, forKey: .grindingEquipmentName)
        coffeeBeanName = try container.decode(String.self, forKey: .coffeeBeanName)
        brewMethod = try container.decode(String.self, forKey: .brewMethod)
        gadgetNames = try container.decode([String].self, forKey: .gadgetNames)
        userName = try container.decode(String.self, forKey: .userName)

        // 解码咖啡豆详细信息字段
        coffeeBeanRoaster = try container.decode(String.self, forKey: .coffeeBeanRoaster)
        coffeeBeanRoastLevelDisplay = try container.decode(String.self, forKey: .coffeeBeanRoastLevelDisplay)
        coffeeBeanProcess = try container.decode(String.self, forKey: .coffeeBeanProcess)
        coffeeBeanOrigin = try container.decode(String.self, forKey: .coffeeBeanOrigin)
        coffeeBeanRegion = try container.decode(String.self, forKey: .coffeeBeanRegion)
        coffeeBeanFinca = try container.decode(String.self, forKey: .coffeeBeanFinca)
        coffeeBeanVariety = try container.decode(String.self, forKey: .coffeeBeanVariety)

        // 处理 water_temperature 可能为 null 的情况
        if let waterTemp = try? container.decode(Double.self, forKey: .waterTemperature) {
            waterTemperature = waterTemp
        } else {
            waterTemperature = 0.0 // 默认值
        }

        // 处理 water_quality 可能为 null 的情况
        if let quality = try? container.decode(String.self, forKey: .waterQuality) {
            waterQuality = quality
        } else {
            waterQuality = "" // 默认值
        }

        // 处理 brewing_time 可能是字符串或整数的情况
        if let brewingTimeInt = try? container.decode(Int.self, forKey: .brewingTime) {
            brewingTime = brewingTimeInt
        } else if let brewingTimeString = try? container.decode(String.self, forKey: .brewingTime) {
            // 尝试将字符串转换为整数（秒）
            if let doubleValue = Double(brewingTimeString) {
                brewingTime = Int(doubleValue)
            } else {
                brewingTime = 0
            }
        } else {
            brewingTime = 0
        }
    }
}

// MARK: - 预览步骤
struct PreviewStep: Codable, Identifiable {
    let text: String
    let timer: String?
    let order: Int

    // 计算属性，提供与BrewingStep兼容的接口
    var id: Int { order }
    var description: String { text }
    var time: Int? { parseTimer(timer) }
    var waterAmount: Double? { extractWaterAmount(from: text) }

    // 解析计时器字符串为秒数
    private func parseTimer(_ timerString: String?) -> Int? {
        guard let timerString = timerString, !timerString.isEmpty else { return nil }

        // 解析 "MM:SS" 格式
        let components = timerString.split(separator: ":")
        if components.count == 2,
           let minutes = Int(components[0]),
           let seconds = Int(components[1]) {
            return minutes * 60 + seconds
        }

        // 解析纯秒数格式
        if let seconds = Int(timerString) {
            return seconds
        }

        return nil
    }

    // 辅助函数：从文本中提取水量信息
    private func extractWaterAmount(from text: String) -> Double? {
        // 简单实现：尝试从文本中提取数字+ml/g格式
        // 实际项目中应该使用更复杂的正则表达式或自然语言处理
        if let range = text.range(of: "\\d+\\s*(ml|g)", options: .regularExpression) {
            let waterText = String(text[range])
            let numberText = waterText.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
            return Double(numberText)
        }
        return nil
    }

    enum CodingKeys: String, CodingKey {
        case text
        case timer
        case order
    }
}

// MARK: - 配方筛选选项
enum RecipeSortOption: String, CaseIterable {
    case recent = "recent"
    case count = "count"

    var displayName: String {
        switch self {
        case .recent:
            return "最近使用"
        case .count:
            return "使用次数"
        }
    }
}

// MARK: - 配方视图状态
class RecipeViewState: ObservableObject {
    @Published var recipes: [RecipeThread] = []
    @Published var tags: [RecipeTag] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var selectedTag: RecipeTag?
    @Published var sortOption: RecipeSortOption = .recent
    @Published var searchText = ""

    // 筛选后的配方列表
    var filteredRecipes: [RecipeThread] {
        var filtered = recipes

        // 标签筛选
        if let selectedTag = selectedTag {
            filtered = filtered.filter { recipe in
                recipe.tags.contains { $0.id == selectedTag.id }
            }
        }

        // 搜索筛选
        if !searchText.isEmpty {
            filtered = filtered.filter { recipe in
                recipe.recipeName.localizedCaseInsensitiveContains(searchText)
            }
        }

        // 排序
        switch sortOption {
        case .recent:
            filtered.sort { recipe1, recipe2 in
                let time1 = recipe1.lastUsedAt ?? recipe1.createdAt
                let time2 = recipe2.lastUsedAt ?? recipe2.createdAt
                return time1 > time2
            }
        case .count:
            filtered.sort { recipe1, recipe2 in
                if recipe1.useCount == recipe2.useCount {
                    return recipe1.recipeName < recipe2.recipeName
                }
                return recipe1.useCount > recipe2.useCount
            }
        }

        return filtered
    }

    // 清除筛选
    func clearFilters() {
        selectedTag = nil
        searchText = ""
        sortOption = .recent
        // 手动触发更新通知，确保视图立即响应
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }
}

// MARK: - API响应类型
struct UpdateRecipeTagsResponse: Codable {
    let status: String
    let message: String?
    let recipe: RecipeThread?
}