import SwiftUI
import Foundation

struct RecipeTagSelectionView: View {
    let recipe: RecipeThread
    @ObservedObject var viewModel: RecipeViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTagIds: Set<Int> = []
    @State private var isLoading = false
    @State private var isLoadingTags = true // 初始状态设为 true，确保第一次显示加载状态
    @State private var errorMessage: String?

    var body: some View {
        VStack(spacing: 0) {
            // 标签列表
            if isLoadingTags {
                loadingStateView
            } else if viewModel.viewState.tags.isEmpty {
                emptyStateView
            } else {
                tagSelectionList
            }
        }
        .background(Color.secondaryBg)
        .navigationTitle("为「\(recipe.recipeName)」贴标签")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("保存") {
                    saveSelectedTags()
                }
                .disabled(isLoading)
                .foregroundColor(Color.linkText)
            }
        }
        .overlay {
            if isLoading {
                ProgressView("保存中...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.3))
            }
        }
        .alert("错误", isPresented: .constant(errorMessage != nil)) {
            Button("确定") {
                errorMessage = nil
            }
        } message: {
            if let errorMessage = errorMessage {
                Text(errorMessage)
            }
        }
        .onAppear {
            print("🏷️ RecipeTagSelectionView onAppear - 配方: \(recipe.recipeName)")
            print("🏷️ 当前标签数量: \(viewModel.viewState.tags.count)")

            // 初始化已选择的标签
            selectedTagIds = Set(recipe.tags.map { $0.id })

            // 如果已经有标签数据，立即停止加载状态
            if !viewModel.viewState.tags.isEmpty {
                print("🏷️ 标签数据已存在，停止加载状态")
                isLoadingTags = false
            } else {
                // 如果没有标签数据，开始加载
                print("🏷️ 开始加载标签数据...")
                Task {
                    await loadTagsIfNeeded()
                    await MainActor.run {
                        isLoadingTags = false
                        print("🏷️ 标签加载完成，当前标签数量: \(viewModel.viewState.tags.count)")
                    }
                }
            }
        }
    }

    // 标签选择列表
    private var tagSelectionList: some View {
        List {
            Section(header: Text("选择标签（可多选）").font(.footnote).foregroundColor(.secondary)) {
                ForEach(viewModel.viewState.tags) { tag in
                    tagSelectionRow(tag: tag)
                }
            }
            .listRowBackground(Color.primaryBg)
        }
        .listStyle(InsetGroupedListStyle())
        .scrollContentBackground(.hidden)
        .background(Color.secondaryBg)
    }

    // 标签选择行
    private func tagSelectionRow(tag: RecipeTag) -> some View {
        HStack(spacing: 12) {
            // 选择状态 - 使用圆形勾选框样式
            ZStack {
                Circle()
                    .stroke(selectedTagIds.contains(tag.id) ? Color.functionText : Color.gray.opacity(0.5), lineWidth: 1.5)
                    .frame(width: 22, height: 22)
                
                if selectedTagIds.contains(tag.id) {
                    Circle()
                        .fill(Color.functionText)
                        .frame(width: 22, height: 22)
                    
                    Image(systemName: "checkmark")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .contentShape(Rectangle())

            // 标签名称
            Text(tag.name)
                .font(.body)
                .foregroundColor(.primary)

            Spacer()
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 4)
        .contentShape(Rectangle())
        .background(
            selectedTagIds.contains(tag.id)
            ? Color.secondaryBg.opacity(0.3)
            : Color.clear
        )
        .onTapGesture {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0.2)) {
                toggleTagSelection(tag)
            }
        }
    }

    // 加载状态视图
    private var loadingStateView: some View {
        VStack(spacing: 16) {
            BlinkingLoader(
                color: .secondaryText,
                width: 12,
                height: 18,
                duration: 1.5,
                text: "加载标签中..."
            )
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.secondaryBg)
    }

    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "tag")
                .font(.system(size: 60))
                .foregroundColor(.noteText)

            Text("暂无标签")
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.primaryText)

            Text("请先在标签管理中创建标签")
                .font(.body)
                .foregroundColor(.noteText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.secondaryBg)
    }

    // 切换标签选择状态
    private func toggleTagSelection(_ tag: RecipeTag) {
        if selectedTagIds.contains(tag.id) {
            selectedTagIds.remove(tag.id)
        } else {
            selectedTagIds.insert(tag.id)
        }
    }

    // 加载标签数据
    private func loadTagsIfNeeded() async {
        print("🏷️ loadTagsIfNeeded 开始执行")
        do {
            let tags = try await RecipeService.shared.fetchRecipeTags()
            await MainActor.run {
                print("🏷️ 获取到 \(tags.count) 个标签，更新 viewModel")
                viewModel.viewState.tags = tags

                // 缓存标签数据
                if let encodedTags = try? JSONEncoder().encode(tags) {
                    UserDefaults.standard.set(encodedTags, forKey: "cachedTags")
                    print("🏷️ 标签数据已缓存")
                }

                // 强制触发视图更新
                viewModel.objectWillChange.send()

                print("✅ 标签选择页面：成功加载 \(tags.count) 个标签")
            }
        } catch {
            print("❌ 标签选择页面：加载标签失败 - \(error)")
            await MainActor.run {
                errorMessage = "加载标签失败: \(error.localizedDescription)"

                // 尝试从缓存加载
                if let cachedTags = UserDefaults.standard.data(forKey: "cachedTags"),
                   let tags = try? JSONDecoder().decode([RecipeTag].self, from: cachedTags) {
                    print("🏷️ 从缓存加载到 \(tags.count) 个标签")
                    viewModel.viewState.tags = tags
                    viewModel.objectWillChange.send()
                }
            }
        }
    }

    // 保存选择的标签
    private func saveSelectedTags() {
        isLoading = true

        Task {
            do {
                let updatedRecipe = try await RecipeService.shared.updateRecipeTags(
                    recipeId: recipe.id,
                    tagIds: Array(selectedTagIds)
                )

                await MainActor.run {
                    isLoading = false

                    // 更新本地配方数据
                    if let index = viewModel.viewState.recipes.firstIndex(where: { $0.id == recipe.id }) {
                        viewModel.viewState.recipes[index] = updatedRecipe
                    }

                    // 触发视图更新
                    viewModel.objectWillChange.send()

                    print("✅ 配方标签更新成功")
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "保存标签失败: \(error.localizedDescription)"
                }
            }
        }
    }
}

#if DEBUG
struct RecipeTagSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            RecipeTagSelectionView(
                recipe: RecipeThread(
                    id: 1,
                    recipeName: "测试配方",
                    useCount: 5,
                    createdAt: Date().timeIntervalSince1970,
                    updatedAt: Date().timeIntervalSince1970,
                    lastUsedAt: Date().timeIntervalSince1970,
                    tags: [],
                    latestRecord: nil
                ),
                viewModel: RecipeViewModel()
            )
        }
    }
}
#endif
