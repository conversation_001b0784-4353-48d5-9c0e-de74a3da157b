import SwiftUI

struct RecipeCard: View {
    let recipe: RecipeThread
    let onQuickBrew: () -> Void
    let onPreview: () -> Void
    let onPortal: () -> Void

    @State private var showingActionSheet = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 使用统计（移到顶部，和 web 版一致的格式）
            usageStats

            // 配方名称
            recipeName

            // 标签
            if !recipe.tags.isEmpty {
                tagSection
            }

            // 操作按钮
            actionButtons
        }
        .background(Color.primaryBg)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.functionText.opacity(0.1), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        .confirmationDialog("配方操作", isPresented: $showingActionSheet) {
            Button("预览速记") {
                onPreview()
            }

            But<PERSON>("速记") {
                onQuickBrew()
            }

            if recipe.latestRecord != nil {
                But<PERSON>("传送门") {
                    onPortal()
                }
            }

            But<PERSON>("取消", role: .cancel) {}
        }
    }



    // MARK: - 配方名称
    private var recipeName: some View {
        Text(recipe.recipeName)
            .font(.title3)
            .fontWeight(.semibold)
            .foregroundColor(.primaryText)
            .lineLimit(2)
            .padding(.horizontal, 16)
            .padding(.top, 8)
    }

    // MARK: - 使用统计
    private var usageStats: some View {
        HStack {
            HStack(spacing: 0) {
                Text("冲煮过")
                    .font(.caption)
                    .foregroundColor(.noteText)
                Text(" \(recipe.useCount) ")
                    .font(.caption)
                    .foregroundColor(.primaryText)
                Text("次，最后用于")
                    .font(.caption)
                    .foregroundColor(.noteText)
                Text(" \(recipe.formattedLastUsedTime)")
                    .font(.caption)
                    .foregroundColor(.primaryText)
                Text("前")
                    .font(.caption)
                    .foregroundColor(.noteText)
            }

            Spacer()

            // 更多操作按钮
            Button {
                showingActionSheet = true
            } label: {
                Image(systemName: "ellipsis")
                    .font(.body)
                    .foregroundColor(.noteText)
                    .frame(width: 24, height: 24)
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
    }

    // MARK: - 标签区域
    private var tagSection: some View {
        TagFlowView(recipeTags: recipe.tags, maxLines: 1)
            .padding(.horizontal, 16)
            .padding(.top, 12)
    }

    // MARK: - 操作按钮
    private var actionButtons: some View {
        HStack(spacing: 12) {
            // 预览速记按钮
            Button {
                onPreview()
            } label: {
                HStack(spacing: 6) {
                    Image(systemName: "eye")
                        .font(.caption)
                    Text("预览")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(.linkText)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.linkText.opacity(0.1))
                .cornerRadius(8)
            }

            // 速记按钮
            Button {
                onQuickBrew()
            } label: {
                HStack(spacing: 6) {
                    Image(systemName: "bolt.fill")
                        .font(.caption)
                    Text("速记")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.primaryAccent)
                .cornerRadius(8)
            }

            // 传送门按钮
            if recipe.latestRecord != nil {
                Button {
                    onPortal()
                } label: {
                    HStack(spacing: 6) {
                        Image(systemName: "arrow.up.right.circle")
                            .font(.caption)
                        Text("传送门")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.noteText)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.noteText.opacity(0.1))
                    .cornerRadius(8)
                }
            }

            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.top, 12)
        .padding(.bottom, 16)
    }
}

// MARK: - 配方统计项组件
struct RecipeStatItem: View {
    let icon: String
    let title: String
    let value: String

    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: icon)
                .font(.caption2)
                .foregroundColor(.noteText)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption2)
                    .foregroundColor(.noteText)

                Text(value)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primaryText)
            }
        }
    }
}



// MARK: - 标签芯片组件
struct TagChip: View {
    let tag: RecipeTag

    var body: some View {
        Text(tag.name)
            .font(.caption2)
            .fontWeight(.medium)
            .foregroundColor(.noteText)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.noteText.opacity(0.1))
            .cornerRadius(8)
    }
}

// MARK: - 预览
#if DEBUG
struct RecipeCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            RecipeCard(
                recipe: RecipeThread(
                    id: 1,
                    recipeName: "V60 日常冲煮",
                    useCount: 15,
                    lastUsedAt: Date().timeIntervalSince1970 - 3600,
                    tags: [
                        RecipeTag(id: 1, name: "手冲"),
                        RecipeTag(id: 2, name: "日常")
                    ],
                    latestRecord: LatestRecord(id: 123, createdAt: Date().timeIntervalSince1970)
                ),
                onQuickBrew: {},
                onPreview: {},
                onPortal: {}
            )

            RecipeCard(
                recipe: RecipeThread(
                    id: 2,
                    recipeName: "爱乐压快速萃取法",
                    useCount: 8,
                    lastUsedAt: Date().timeIntervalSince1970 - 86400 * 3,
                    tags: [
                        RecipeTag(id: 3, name: "爱乐压"),
                        RecipeTag(id: 4, name: "快速")
                    ]
                ),
                onQuickBrew: {},
                onPreview: {},
                onPortal: {}
            )
        }
        .padding()
        .background(Color.secondaryBg)
    }
}
#endif
