import SwiftUI

struct RecipePreviewView: View {
    let recipe: RecipeThread
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 配方名称
            Text(recipe.recipeName)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)
                .lineLimit(2)
            
            // 使用统计
            HStack(spacing: 16) {
                // 使用次数
                HStack(spacing: 6) {
                    Image(systemName: "arrow.clockwise")
                        .font(.caption2)
                        .foregroundColor(.noteText)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("使用次数")
                            .font(.caption2)
                            .foregroundColor(.noteText)
                        
                        Text("\(recipe.useCount)次")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.primaryText)
                    }
                }
                
                // 最后使用时间
                HStack(spacing: 6) {
                    Image(systemName: "clock")
                        .font(.caption2)
                        .foregroundColor(.noteText)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("最后使用")
                            .font(.caption2)
                            .foregroundColor(.noteText)
                        
                        Text(recipe.formattedLastUsedTime)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.primaryText)
                    }
                }
            }
            
            // 标签
            if !recipe.tags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(recipe.tags) { tag in
                            Text(tag.name)
                                .font(.caption2)
                                .fontWeight(.medium)
                                .foregroundColor(.noteText)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.noteText.opacity(0.1))
                                .cornerRadius(8)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color.primaryBg)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
        .frame(width: UIScreen.main.bounds.width - 40)
    }
}

// MARK: - 预览
#if DEBUG
struct RecipePreviewView_Previews: PreviewProvider {
    static var previews: some View {
        RecipePreviewView(
            recipe: RecipeThread(
                id: 1,
                recipeName: "V60 日常冲煮",
                useCount: 15,
                lastUsedAt: Date().timeIntervalSince1970 - 3600,
                tags: [
                    RecipeTag(id: 1, name: "手冲"),
                    RecipeTag(id: 2, name: "日常")
                ],
                latestRecord: LatestRecord(id: 123, createdAt: Date().timeIntervalSince1970)
            )
        )
        .padding()
        .background(Color.secondaryBg)
    }
}
#endif
