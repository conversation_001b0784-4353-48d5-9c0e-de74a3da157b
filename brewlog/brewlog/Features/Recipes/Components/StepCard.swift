import SwiftUI

// MARK: - 步骤卡片
struct StepCard: View {
    let step: BrewingStep
    let index: Int

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 步骤编号
            Text("\(index)")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 20, height: 20)
                .background(Color.primaryAccent)
                .clipShape(Circle())

            // 步骤内容
            VStack(alignment: .leading, spacing: 4) {
                if let time = step.time {
                    Text("时间: \(formatTime(time))")
                        .font(.caption)
                        .foregroundColor(.noteText)
                }

                Text(step.description)
                    .font(.body)
                    .foregroundColor(.primaryText)
            }

            Spacer()
        }
        .padding(12)
        .background(Color.secondaryBg)
        .cornerRadius(8)
    }
    
    private func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60

        if minutes > 0 {
            return String(format: "%d:%02d", minutes, remainingSeconds)
        } else {
            return "\(remainingSeconds)秒"
        }
    }
}

// MARK: - 预览
#if DEBUG
struct StepCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 12) {
            StepCard(
                step: BrewingStep(
                    id: 1,
                    stepNumber: 1,
                    description: "注水60g，搅拌均匀",
                    time: 30,
                    waterAmount: 60.0
                ),
                index: 1
            )
            
            StepCard(
                step: BrewingStep(
                    id: 2,
                    stepNumber: 2,
                    description: "30秒后注水至150g",
                    time: nil,
                    waterAmount: 90.0
                ),
                index: 2
            )
        }
        .padding()
        .background(Color.primaryBg)
    }
}
#endif
