import SwiftUI
import Foundation
import Combine

/// 养豆日历主视图
struct BeanCalendarView: View {
    @StateObject private var viewModel = BeanCalendarViewModel()
    @EnvironmentObject private var themeManager: ThemeManager

    // 添加一个监视器状态，帮助强制刷新视图
    @State private var viewUpdateCounter = 0

    // 添加一个本地状态跟踪事件详情弹窗
    @State private var isShowingEventDetail = false

    // 添加筛选菜单的状态
    @State private var isShowingFilterMenu = false

    // 添加图例菜单的状态
    @State private var isShowingLegendMenu = false

    // 添加年月选择器的状态
    @State private var isShowingDatePicker = false
    @State private var selectedYear: Int = Calendar.current.component(.year, from: Date())
    @State private var selectedMonth: Int = Calendar.current.component(.month, from: Date())

    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // 月份导航
                HStack {
                    // 左侧年月显示和选择器
                    Button(action: {
                        // 初始化选择器的年月为当前显示的年月
                        let calendar = Calendar.current
                        selectedYear = calendar.component(.year, from: viewModel.viewState.currentDate)
                        selectedMonth = calendar.component(.month, from: viewModel.viewState.currentDate)
                        isShowingDatePicker = true
                    }) {
                        HStack(spacing: 4) {
                            Text(viewModel.viewState.monthTitle)
                                .font(.headline)
                                .foregroundColor(.primaryText)
                                .id("month-title-\(viewUpdateCounter)")

                            Image(systemName: "chevron.down")
                                .font(.caption)
                                .foregroundColor(.linkText)
                        }
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                    }

                    Spacer()

                    // 右侧导航按钮组
                    HStack(spacing: 12) {
                        // 前一月按钮
                        Button(action: {
                            viewModel.previousMonth()
                            viewModel.checkAndRefreshIfNeeded()
                            viewUpdateCounter += 1
                        }) {
                            Image(systemName: "chevron.left")
                                .font(.subheadline)
                                .foregroundColor(.linkText)
                                .padding(8)
                        }

                        // 今天按钮
                        Button(action: {
                            viewModel.goToToday()
                            viewModel.checkAndRefreshIfNeeded()
                            viewUpdateCounter += 1
                        }) {
                            Text("今天")
                                .font(.subheadline)
                                .foregroundColor(viewModel.isCurrentMonth() ? .archivedText : .linkText)
                                .padding(.vertical, 6)
                                .padding(.horizontal, 10)
                        }
                        .disabled(viewModel.isCurrentMonth())
                        .id("today-button-\(viewUpdateCounter)")

                        // 后一月按钮
                        Button(action: {
                            viewModel.nextMonth()
                            viewModel.checkAndRefreshIfNeeded()
                            viewUpdateCounter += 1
                        }) {
                            Image(systemName: "chevron.right")
                                .font(.subheadline)
                                .foregroundColor(.linkText)
                                .padding(8)
                        }
                    }
                }
                .padding(.horizontal)

                // 日历网格
                if viewModel.viewState.isLoading {
                    VStack {
                        BlinkingLoader(text: "加载中...")
                            .frame(height: 60)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .onAppear {
                        print("🔄 显示加载状态 - isLoading: \(viewModel.viewState.isLoading)")
                    }
                } else if let errorMessage = viewModel.viewState.errorMessage {
                    VStack {
                        Text("加载失败")
                            .font(.headline)
                            .foregroundColor(.errorColor)

                        Text(errorMessage)
                            .font(.subheadline)
                            .foregroundColor(.noteText)
                            .multilineTextAlignment(.center)
                            .padding()

                        Button("重试") {
                            viewModel.refresh()
                            viewUpdateCounter += 1
                        }
                        .buttonStyle(.bordered)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .onAppear {
                        print("❌ 显示错误状态: \(errorMessage)")
                    }
                } else if viewModel.viewState.calendarData == nil {
                    VStack {
                        Text("无数据")
                            .font(.headline)

                        Text("日历数据加载失败或暂无数据")
                            .font(.subheadline)
                            .foregroundColor(.noteText)
                            .multilineTextAlignment(.center)
                            .padding()

                        Button("重试") {
                            viewModel.refresh()
                            viewUpdateCounter += 1
                        }
                        .buttonStyle(.bordered)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .onAppear {
                        print("⚠️ 显示无数据状态 - calendarData: nil")
                    }
                } else {
                    let days = viewModel.viewState.calendarDays
                    if days.isEmpty {
                        VStack {
                            Text("日历为空")
                                .font(.headline)

                            Text("未能生成日历数据")
                                .font(.subheadline)
                                .foregroundColor(.noteText)
                                .multilineTextAlignment(.center)
                                .padding()

                            Button("重试") {
                                viewModel.refresh()
                                viewUpdateCounter += 1
                            }
                            .buttonStyle(.bordered)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .onAppear {
                            print("⚠️ 显示空日历状态 - calendarDays为空, calendarData存在")
                        }
                    } else {
                        // 日历网格 - 负值间距以消除网格线
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 0), count: 7), spacing: -2) {
                            // 星期标题
                            ForEach(["一", "二", "三", "四", "五", "六", "日"], id: \.self) { weekday in
                                Text(weekday)
                                    .font(.caption)
                                    .foregroundColor(.noteText)
                                    .frame(height: 30)
                            }

                            // 日历格子 - 使用透明分隔线确保无缝连接
                            ForEach(viewModel.currentMonthData) { day in
                                CalendarDayView(day: day, viewModel: viewModel)
                                    .border(width: 0, edges: [], color: Color(.systemGray4)) // 无边框
                                    .clipShape(Rectangle()) // 确保内容不会溢出
                            }
                        }
                        .padding(.horizontal, 8)
                        .id("calendar-grid-\(viewUpdateCounter)")
                        .onAppear {
                            print("✅ 显示日历视图 - 共\(days.count)天，月份：\(viewModel.viewState.monthTitle)")
                        }
                    }
                }

                // 添加底部空间，确保滚动时有足够的空间
                Spacer()
                    .frame(height: 20)
            }
            .padding(.bottom, 16)
        }
        .background(Color.secondaryBg)
        .navigationTitle("养豆日历")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                HStack(spacing: 12) {
                    // 图例按钮
                    Button(action: {
                        isShowingLegendMenu = true
                    }) {
                        ZStack {
                            Image(systemName: "smallcircle.circle")
                                .foregroundColor(Color.functionText)
                                .font(.system(size: 15))
                                .frame(width: 30, height: 30)
                                .background(Circle().fill(Color.navbarBg))

                            // 如果有隐藏的咖啡豆，显示指示器
                            if let beans = viewModel.viewState.calendarData?.beans,
                               beans.contains(where: { !viewModel.isBeanVisible($0.id) }) {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 8, height: 8)
                                    .offset(x: 8, y: -8)
                            }
                        }
                    }

                    // 筛选按钮
                    Button(action: {
                        isShowingFilterMenu = true
                    }) {
                        ZStack {
                            Image(systemName: "line.3.horizontal.decrease")
                                .foregroundColor(Color.functionText)
                                .font(.system(size: 15))
                                .frame(width: 30, height: 30)
                                .background(Circle().fill(Color.navbarBg))

                            // 如果有筛选条件，显示指示器
                            if !viewModel.viewState.showPurchase || !viewModel.viewState.showRoast || !viewModel.viewState.showRest {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 8, height: 8)
                                    .offset(x: 8, y: -8)
                            }
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $isShowingEventDetail, onDismiss: {
            print("🚪 事件详情弹窗关闭")
            viewModel.hideEventDetail()
        }) {
            if let date = viewModel.viewState.selectedDate,
               let events = viewModel.viewState.selectedEvents {
                EventDetailSheet(
                    date: date,
                    events: events,
                    viewModel: viewModel
                )
                .presentationDetents([.large])
                .presentationDragIndicator(.visible)
                .id("event-detail-\(viewUpdateCounter)")
            }
        }
        .onReceive(viewModel.viewState.$showingEventDetail) { newValue in
            print("🔄 监听到事件详情状态变化: \(newValue)")
            // 确保本地状态与 viewModel 状态同步
            if isShowingEventDetail != newValue {
                isShowingEventDetail = newValue
            }
        }
        .sheet(isPresented: $isShowingDatePicker) {
            YearMonthPickerView(
                selectedYear: $selectedYear,
                selectedMonth: $selectedMonth,
                viewModel: viewModel,
                onSelect: { year, month in
                    navigateToSelectedDate(year: year, month: month)
                }
            )
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $isShowingFilterMenu) {
            EventFilterMenu(viewModel: viewModel)
                .presentationDetents([.medium, .large])
                .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $isShowingLegendMenu) {
            BeanLegendMenu(
                beans: viewModel.viewState.calendarData?.beans ?? [],
                viewModel: viewModel,
                viewUpdateCounter: viewUpdateCounter,
                onViewUpdate: { viewUpdateCounter += 1 }
            )
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible)
        }
        .onAppear {
            // 视图出现时检查是否需要刷新数据
            viewModel.checkAndRefreshIfNeeded()
            // 进入视图时强制更新主题颜色
            themeManager.updateThemeColorsOnly()
        }
        // 监听系统主题变化通知
        .onReceive(NotificationCenter.default.publisher(for: TraitChangeObserver.traitChangeNotification)) { _ in
            themeManager.updateThemeColorsOnly()
        }
    }

    // 跳转到选中的年月
    private func navigateToSelectedDate(year: Int, month: Int) {
        print("📅 跳转到选中的年月: \(year)年\(month)月")
        let calendar = Calendar.current
        var components = DateComponents()
        components.year = year
        components.month = month
        components.day = 1

        if let date = calendar.date(from: components) {
            // 使用 DispatchQueue.main.async 确保在主线程上更新 UI
            DispatchQueue.main.async {
                viewModel.loadCalendarForDate(date)
                viewModel.checkAndRefreshIfNeeded()
                viewUpdateCounter += 1
            }
        }
    }
}

/// 事件开关菜单视图
struct EventFilterMenu: View {
    @ObservedObject var viewModel: BeanCalendarViewModel
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                // 条件筛选部分
                Section(header: Text("请选择要在日历中显示的事件类型")) {
                    ForEach(EventType.allCases, id: \.self) { eventType in
                        HStack {
                            // 事件图标和名称
                            HStack(spacing: 12) {
                                Text(eventType.icon)
                                    .font(.title3)

                                Text(eventType.displayName)
                                    .font(.body)
                            }

                            Spacer()

                            // 开关
                            Toggle("", isOn: Binding(
                                get: {
                                    switch eventType {
                                    case .purchase: return viewModel.viewState.showPurchase
                                    case .roast: return viewModel.viewState.showRoast
                                    case .rest: return viewModel.viewState.showRest
                                    }
                                },
                                set: { _ in
                                    viewModel.toggleEventType(eventType)
                                    // 切换事件类型后不再自动刷新数据
                                }
                            ))
                            .labelsHidden()
                            .tint(Color.functionText)
                        }
                    }
                }
                .listRowBackground(Color.primaryBg)
            }
            .listStyle(InsetGroupedListStyle())
            .scrollContentBackground(.hidden)
            .background(Color.secondaryBg)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("重置") {
                        // 重置所有筛选条件为默认值（全部显示）
                        viewModel.viewState.showPurchase = true
                        viewModel.viewState.showRoast = true
                        viewModel.viewState.showRest = true

                        // 保存设置
                        viewModel.saveFilterSettings()

                        // 不再自动刷新数据

                        // 手动触发UI更新
                        DispatchQueue.main.async {
                            viewModel.objectWillChange.send()
                        }
                    }
                    .foregroundColor(viewModel.viewState.showPurchase && viewModel.viewState.showRoast && viewModel.viewState.showRest ? Color.archivedText : Color.linkText)
                    .font(.system(size: 17, weight: .regular))
                    .disabled(viewModel.viewState.showPurchase && viewModel.viewState.showRoast && viewModel.viewState.showRest)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        dismiss()
                    }
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(Color.linkText)
                }
            }
        }
        .presentationDetents([.medium, .large])
        .presentationDragIndicator(.visible)
    }
}

/// 年月选择器视图
struct YearMonthPickerView: View {
    @Binding var selectedYear: Int
    @Binding var selectedMonth: Int
    let viewModel: BeanCalendarViewModel
    let onSelect: (Int, Int) -> Void

    @Environment(\.dismiss) private var dismiss

    // 可用年份状态
    @State private var availableYears: [Int] = []
    @State private var isLoadingYears = true

    // 月份数组
    private let months = (1...12).map { month -> String in
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "M月"
        let date = Calendar.current.date(from: DateComponents(month: month, day: 1))!
        return dateFormatter.string(from: date)
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                // 年月选择器横向排列
                HStack(spacing: 12) {
                    // 年份选择器
                    VStack(alignment: .leading, spacing: 8) {
                        Text("年份")
                            .font(.subheadline)
                            .foregroundColor(.noteText)

                        if isLoadingYears {
                            VStack {
                                Spacer()
                                BlinkingLoader(text: "加载年份...")
                                Spacer()
                            }
                            .frame(height: 180)
                            .frame(maxWidth: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color.primaryBg)
                            )
                        } else {
                            Picker("年份", selection: $selectedYear) {
                                ForEach(availableYears, id: \.self) { year in
                                    Text(formatYear(year)).tag(year)
                                }
                            }
                            .pickerStyle(.wheel)
                            .frame(height: 180)
                            .clipped()
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color.primaryBg)
                            )
                        }
                    }
                    .frame(maxWidth: .infinity)

                    // 月份选择器
                    VStack(alignment: .leading, spacing: 8) {
                        Text("月份")
                            .font(.subheadline)
                            .foregroundColor(.noteText)

                        Picker("月份", selection: $selectedMonth) {
                            ForEach(1...12, id: \.self) { month in
                                Text(months[month - 1]).tag(month)
                            }
                        }
                        .pickerStyle(.wheel)
                        .frame(height: 180)
                        .clipped()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color.primaryBg)
                        )
                    }
                    .frame(maxWidth: .infinity)
                }
                .padding(.horizontal)

                // 添加说明文本
                Text("本日历仅显示活跃咖啡豆的关键事件，已归档或已删除的咖啡豆不会显示。较早时间段可能没有可见数据。")
                    .font(.footnote)
                    .foregroundColor(.noteText)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                    .padding(.bottom, 8)

                Spacer()
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.secondaryBg)
            .navigationTitle("选择年份和月份")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        onSelect(selectedYear, selectedMonth)
                        dismiss()
                    }
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(Color.linkText)
                    .disabled(isLoadingYears)
                }
            }
            .onAppear {
                loadAvailableYears()
            }
        }
        .presentationDetents([.medium, .large])
        .presentationDragIndicator(.visible)
    }

    // 加载可用年份
    private func loadAvailableYears() {
        isLoadingYears = true

        Task {
            // 获取可用年份
            let years = await viewModel.getAvailableYears()

            // 在主线程更新UI
            await MainActor.run {
                self.availableYears = years.sorted(by: >)  // 降序排列，最近的年份在前

                // 如果当前选择的年份不在可用列表中
                if !years.contains(selectedYear) {
                    let currentYear = Calendar.current.component(.year, from: Date())

                    if years.contains(currentYear) {
                        // 如果当前年份可用，选择当前年份
                        selectedYear = currentYear
                    } else if !years.isEmpty {
                        // 否则选择最接近当前年份的年份
                        let closestYear = years.min(by: { abs($0 - currentYear) < abs($1 - currentYear) }) ?? years.first!
                        selectedYear = closestYear
                        print("📅 初始化年份选择器: 当前年份不可用，选择最接近的年份: \(closestYear)")
                    }
                }

                isLoadingYears = false
            }
        }
    }

    // 格式化年份，确保没有千位分隔符
    private func formatYear(_ year: Int) -> String {
        return "\(year)年"
    }
}

/// 咖啡豆图例菜单视图
struct BeanLegendMenu: View {
    let beans: [BeanInfo]
    @ObservedObject var viewModel: BeanCalendarViewModel
    let viewUpdateCounter: Int
    let onViewUpdate: () -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                Section(header: Text("请选择要在日历中显示的咖啡豆")) {
                    if beans.isEmpty {
                        HStack {
                            Spacer()
                            Text("暂无活跃的咖啡豆数据")
                                .font(.caption)
                                .foregroundColor(.noteText)
                                .padding(.vertical, 12)
                            Spacer()
                        }
                    } else {
                        ForEach(beans) { bean in
                            BeanLegendItem(
                                bean: bean,
                                viewModel: viewModel,
                                onToggle: {
                                    viewModel.toggleBeanVisibility(bean.id)
                                    // 切换豆子可见性后不再自动刷新数据
                                    onViewUpdate()
                                }
                            )
                            .id("bean-legend-\(bean.id)")
                        }
                    }
                }
                .listRowBackground(Color.primaryBg)
            }
            .listStyle(InsetGroupedListStyle())
            .scrollContentBackground(.hidden)
            .background(Color.secondaryBg)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("重置") {
                        // 将所有咖啡豆设为可见
                        for bean in beans {
                            if !viewModel.isBeanVisible(bean.id) {
                                viewModel.toggleBeanVisibility(bean.id)
                            }
                        }
                        // 不再自动刷新数据
                        onViewUpdate()
                    }
                    .foregroundColor(beans.allSatisfy { viewModel.isBeanVisible($0.id) } ? Color.archivedText : Color.linkText)
                    .font(.system(size: 17, weight: .regular))
                    .disabled(beans.allSatisfy { viewModel.isBeanVisible($0.id) })
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        dismiss()
                    }
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(Color.linkText)
                }
            }
        }
        .presentationDetents([.medium, .large])
        .presentationDragIndicator(.visible)
    }
}

/// 咖啡豆图例项
struct BeanLegendItem: View {
    let bean: BeanInfo
    @ObservedObject var viewModel: BeanCalendarViewModel
    let onToggle: () -> Void

    private var isVisible: Bool {
        viewModel.isBeanVisible(bean.id)
    }

    // 格式化库存信息
    private var stockInfo: String? {
        // 如果weight为nil，则不显示库存信息
        guard let weight = bean.bagWeight else {
            return nil
        }
        
        // 获取进度值，如果为nil则默认为0
        let progress = bean.progress ?? 0
        let progressText = String(format: "库存: %.0f%%", progress)
        
        if let remain = bean.bagRemain {
            return "\(progressText) (\(String(format: "%.1f", remain))/\(String(format: "%.0f", weight))g)"
        } else {
            return progressText
        }
    }

    // 格式化豆名显示，包含豆商信息
    private var beanFullName: String {
        return "\(bean.roaster) - \(bean.name)"
    }

    var body: some View {
        Button(action: onToggle) {
            HStack(spacing: 12) {
                // 颜色指示器
                Circle()
                    .fill(Color(parseHSLColor(bean.color.solid)))
                    .frame(width: 16, height: 16)
                    .opacity(isVisible ? 1.0 : 0.5)

                // 咖啡豆名称
                VStack(alignment: .leading, spacing: 2) {
                    Text(beanFullName)
                        .font(.subheadline)
                        .foregroundColor(isVisible ? .primary : .noteText)
                        .lineLimit(1)

                    if let stockInfoText = stockInfo {
                        Text(stockInfoText)
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                }

                Spacer()

                // 可见性状态图标
                Image(systemName: isVisible ? "eye.fill" : "eye.slash.fill")
                    .font(.footnote)
                    .foregroundColor(isVisible ? Color.functionText : .archivedText)
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 6)
            .frame(maxWidth: .infinity, alignment: .leading)
            .contentShape(Rectangle()) // 确保整个区域可点击
            .id("bean-item-content-\(bean.id)-\(isVisible)")
        }
        .buttonStyle(PlainButtonStyle()) // 使用PlainButtonStyle确保点击时没有高亮效果
    }

    private func parseHSLColor(_ hslString: String) -> UIColor {
        // 解析 "hsl(120 70% 50%)" 或 "color-mix(in oklab, hsl(120 70% 50%) 50%, transparent)" 格式的字符串
        var cleanString = hslString

        // 处理 color-mix 格式
        if hslString.contains("color-mix") {
            // 提取 hsl 部分
            if let hslRange = hslString.range(of: "hsl\\([^)]+\\)", options: .regularExpression) {
                cleanString = String(hslString[hslRange])
            } else {
                return UIColor.systemGray5
            }
        }

        // 清理字符串
        cleanString = cleanString
            .replacingOccurrences(of: "hsl(", with: "")
            .replacingOccurrences(of: ")", with: "")
            .replacingOccurrences(of: "%", with: "")

        let components = cleanString.components(separatedBy: " ")

        guard components.count >= 3,
              let hue = Double(components[0]),
              let saturation = Double(components[1]),
              let lightness = Double(components[2]) else {
            return UIColor.systemGray5
        }

        return UIColor(
            hue: hue / 360.0,
            saturation: saturation / 100.0,
            brightness: lightness / 100.0,
            alpha: 1.0
        )
    }
}

// MARK: - 预览

#if DEBUG
struct BeanCalendarView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            BeanCalendarView()
        }
    }
}
#endif
