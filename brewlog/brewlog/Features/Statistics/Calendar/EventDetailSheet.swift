import SwiftUI

/// 事件详情弹窗
struct EventDetailSheet: View {
    let date: Date
    let events: DayEvents
    @ObservedObject var viewModel: BeanCalendarViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // 购买事件
                    if !filteredPurchaseEvents.isEmpty {
                        EventSectionView(
                            title: "购买事件",
                            icon: "💰",
                            events: filteredPurchaseEvents,
                            eventType: .purchase
                        )
                        .id("purchase-section-\(viewModel.viewState.showPurchase)")
                    }
                    
                    // 烘焙事件
                    if !filteredRoastEvents.isEmpty {
                        EventSectionView(
                            title: "烘焙事件",
                            icon: "🔥",
                            events: filteredRoastEvents,
                            eventType: .roast
                        )
                        .id("roast-section-\(viewModel.viewState.showRoast)")
                    }
                    
                    // 赏味期事件
                    if !filteredRestEvents.isEmpty {
                        EventSectionView(
                            title: "最佳赏味期",
                            icon: "☕",
                            events: filteredRestEvents,
                            eventType: .rest
                        )
                        .id("rest-section-\(viewModel.viewState.showRest)")
                    }
                    
                    // 无事件提示
                    if allFilteredEvents.isEmpty {
                        VStack(spacing: 12) {
                            Image(systemName: "calendar.badge.exclamationmark")
                                .font(.system(size: 48))
                                .foregroundColor(.noteText)
                            
                            Text("这一天没有咖啡豆相关事件")
                                .font(.headline)
                                .foregroundColor(.noteText)
                            
                            Text("尝试调整事件类型开关或咖啡豆可见性设置")
                                .font(.caption)
                                .foregroundColor(.noteText)
                                .multilineTextAlignment(.center)
                        }
                        .padding(.vertical, 40)
                    }
                }
                .padding()
                .id("event-details-\(viewModel.viewState.showPurchase)-\(viewModel.viewState.showRoast)-\(viewModel.viewState.showRest)")
            }
            .background(Color.secondaryBg)
            .navigationTitle(formattedDate)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "Y年M月d日的事件"
        return formatter.string(from: date)
    }
    
    private var filteredPurchaseEvents: [BeanEvent] {
        viewModel.getFilteredEvents(events.purchaseEvents, for: .purchase)
    }
    
    private var filteredRoastEvents: [BeanEvent] {
        viewModel.getFilteredEvents(events.roastEvents, for: .roast)
    }
    
    private var filteredRestEvents: [BeanEvent] {
        viewModel.getFilteredEvents(events.restEvents, for: .rest)
    }
    
    private var allFilteredEvents: [BeanEvent] {
        filteredPurchaseEvents + filteredRoastEvents + filteredRestEvents
    }
}

/// 事件分组视图
struct EventSectionView: View {
    let title: String
    let icon: String
    let events: [BeanEvent]
    let eventType: EventType
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 分组标题
            HStack {
                Text(icon)
                    .font(.title2)
                
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primaryText)
                
                Spacer()
                
                Text("\(events.count)个")
                    .font(.caption)
                    .foregroundColor(.noteText)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.archivedText.opacity(0.2))
                    .clipShape(Capsule())
            }
            
            // 事件列表
            LazyVStack(spacing: 8) {
                ForEach(events, id: \.id) { event in
                    EventDetailItemView(event: event, eventType: eventType)
                }
            }
        }
        .padding()
        .background(Color.navbarBg)
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

/// 事件详情项视图
struct EventDetailItemView: View {
    let event: BeanEvent
    let eventType: EventType
    
    var body: some View {
        HStack(spacing: 12) {
            // 颜色指示器
            Circle()
                .fill(Color(parseHSLColor(event.color.solid)))
                .frame(width: 20, height: 20)
            
            // 咖啡豆信息
            VStack(alignment: .leading, spacing: 4) {
                Text("\(event.roaster) - \(event.name)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primaryText)
                
                if let stockInfo = stockInfo {
                    Text(stockInfo)
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
            }
            
            Spacer()
            
            // 事件类型标识
            Text(eventType.displayName)
                .font(.caption)
                .foregroundColor(.primaryBg)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(eventTypeColor)
                .clipShape(Capsule())
        }
        .padding()
        .background(Color.primaryBg)
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .shadow(color: .primaryText.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private var stockInfo: String? {
        // 如果weight为nil，则不显示库存信息
        guard let weight = event.bagWeight else {
            return nil
        }
        
        // 获取进度值，如果为nil则默认为0
        let progress = event.progress ?? 0
        let progressText = String(format: "库存: %.0f%%", progress)
        
        if let remain = event.bagRemain {
            return "\(progressText) (\(String(format: "%.1f", remain))/\(String(format: "%.0f", weight))g)"
        } else {
            return progressText
        }
    }
    
    private var eventTypeColor: Color {
        switch eventType {
        case .purchase:
            return .green
        case .roast:
            return .orange
        case .rest:
            return .blue
        }
    }
    
    /// 解析HSL颜色字符串
    private func parseHSLColor(_ hslString: String) -> UIColor {
        // 解析 "hsl(120 70% 50%)" 格式的字符串
        let cleanString = hslString
            .replacingOccurrences(of: "hsl(", with: "")
            .replacingOccurrences(of: ")", with: "")
            .replacingOccurrences(of: "%", with: "")
        
        let components = cleanString.components(separatedBy: " ")
        
        guard components.count >= 3,
              let hue = Double(components[0]),
              let saturation = Double(components[1]),
              let lightness = Double(components[2]) else {
            return UIColor.systemGray
        }
        
        return UIColor(
            hue: hue / 360.0,
            saturation: saturation / 100.0,
            brightness: lightness / 100.0,
            alpha: 1.0
        )
    }
}

// MARK: - 预览

#if DEBUG
struct EventDetailSheet_Previews: PreviewProvider {
    static var previews: some View {
        let sampleEvents = DayEvents(
            purchaseEvents: [
                BeanEvent(
                    id: 1,
                    name: "耶加雪菲",
                    roaster: "蓝瓶咖啡",
                    color: BeanColor(solid: "hsl(120 70% 50%)", transparent: "hsl(120 70% 50% / 0.5)"),
                    progress: 75.0,
                    bagRemain: 150.0,
                    bagWeight: 200.0
                )
            ],
            roastEvents: [
                BeanEvent(
                    id: 2,
                    name: "哥伦比亚",
                    roaster: "本地烘焙",
                    color: BeanColor(solid: "hsl(240 70% 50%)", transparent: "hsl(240 70% 50% / 0.5)"),
                    progress: 50.0,
                    bagRemain: 100.0,
                    bagWeight: 200.0
                )
            ],
            restEvents: [
                BeanEvent(
                    id: 3,
                    name: "巴西",
                    roaster: "精品咖啡",
                    color: BeanColor(solid: "hsl(60 70% 50%)", transparent: "hsl(60 70% 50% / 0.5)"),
                    progress: 25.0,
                    bagRemain: 50.0,
                    bagWeight: 200.0
                )
            ]
        )
        
        let viewModel = BeanCalendarViewModel()
        
        EventDetailSheet(
            date: Date(),
            events: sampleEvents,
            viewModel: viewModel
        )
    }
}
#endif
