import Foundation

// MARK: - 咖啡豆日历数据模型

/// 咖啡豆日历响应数据
struct BeanCalendarResponse: Codable {
    let dailyEvents: [String: DayEvents]
    let beans: [BeanInfo]
    let year: Int
    let month: Int
    let daysInMonth: Int
    let calendarStart: String
    let calendarEnd: String

    enum CodingKeys: String, CodingKey {
        case dailyEvents = "daily_events"
        case beans
        case year
        case month
        case daysInMonth = "days_in_month"
        case calendarStart = "calendar_start"
        case calendarEnd = "calendar_end"
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        do {
            dailyEvents = try container.decode([String: DayEvents].self, forKey: .dailyEvents)
            beans = try container.decode([BeanInfo].self, forKey: .beans)
            year = try container.decode(Int.self, forKey: .year)
            month = try container.decode(Int.self, forKey: .month)
            daysInMonth = try container.decode(Int.self, forKey: .daysInMonth)
            calendarStart = try container.decode(String.self, forKey: .calendarStart)
            calendarEnd = try container.decode(String.self, forKey: .calendarEnd)

            print("✅ 成功解析BeanCalendarResponse: \(year)年\(month)月, \(beans.count)个咖啡豆, \(dailyEvents.count)天日历数据")
        } catch {
            print("❌ 解析BeanCalendarResponse失败: \(error)")
            throw error
        }
    }

    // 仅供预览使用的初始化方法
    #if DEBUG
    init(dailyEvents: [String: DayEvents], beans: [BeanInfo], year: Int, month: Int, daysInMonth: Int, calendarStart: String, calendarEnd: String) {
        self.dailyEvents = dailyEvents
        self.beans = beans
        self.year = year
        self.month = month
        self.daysInMonth = daysInMonth
        self.calendarStart = calendarStart
        self.calendarEnd = calendarEnd
    }
    #endif
}

/// 每日事件数据
struct DayEvents: Codable {
    let purchaseEvents: [BeanEvent]
    let roastEvents: [BeanEvent]
    let restEvents: [BeanEvent]

    enum CodingKeys: String, CodingKey {
        case purchaseEvents = "purchase_events"
        case roastEvents = "roast_events"
        case restEvents = "rest_events"
    }
}

/// 咖啡豆事件
struct BeanEvent: Codable, Identifiable {
    let id: Int
    let name: String
    let roaster: String
    let color: BeanColor
    let progress: Double?
    let bagRemain: Double?
    let bagWeight: Double?

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case roaster
        case color
        case progress
        case bagRemain = "bag_remain"
        case bagWeight = "bag_weight"
    }
}

/// 咖啡豆颜色信息
struct BeanColor: Codable {
    let solid: String
    let transparent: String
}

/// 咖啡豆信息（用于图例）
struct BeanInfo: Codable, Identifiable {
    let id: Int
    let name: String
    let roaster: String
    let color: BeanColor
    let progress: Double?
    let bagRemain: Double?
    let bagWeight: Double?

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case roaster
        case color
        case progress
        case bagRemain = "bag_remain"
        case bagWeight = "bag_weight"
    }
}

// MARK: - 视图数据模型

/// 咖啡豆日历日期数据
struct BeanCalendarDayData: Identifiable {
    let id = UUID()
    let date: Date
    let events: DayEvents
    let isCurrentMonth: Bool
    let isToday: Bool

    var hasEvents: Bool {
        !events.purchaseEvents.isEmpty ||
        !events.roastEvents.isEmpty ||
        !events.restEvents.isEmpty
    }

    var totalEventCount: Int {
        events.purchaseEvents.count +
        events.roastEvents.count +
        events.restEvents.count
    }
}

/// 事件类型枚举
enum EventType: String, CaseIterable {
    case purchase = "purchase"
    case roast = "roast"
    case rest = "rest"

    var displayName: String {
        switch self {
        case .purchase: return "购买日"
        case .roast: return "烘焙日"
        case .rest: return "最佳赏味期"
        }
    }

    var icon: String {
        switch self {
        case .purchase: return "💰"
        case .roast: return "🔥"
        case .rest: return "☕"
        }
    }
}

// MARK: - 视图状态

/// 日历视图状态
class BeanCalendarViewState: ObservableObject {
    // 当前展示的日期
    @Published var currentDate = Date()

    // 日历数据
    @Published var calendarData: BeanCalendarResponse? {
        didSet {
            print("🔄 日历数据已更新: \(calendarData != nil ? "有数据" : "无数据")")
        }
    }

    // 加载状态
    @Published var isLoading = false {
        didSet {
            print("🔄 加载状态变更: \(isLoading)")
        }
    }

    @Published var errorMessage: String?

    // 事件类型显示开关
    @Published var showPurchase = true {
        didSet {
            print("🔄 购买事件显示状态变更: \(showPurchase)")
        }
    }
    @Published var showRoast = true {
        didSet {
            print("🔄 烘焙事件显示状态变更: \(showRoast)")
        }
    }
    @Published var showRest = true {
        didSet {
            print("🔄 赏味期事件显示状态变更: \(showRest)")
        }
    }

    // 咖啡豆可见性控制
    @Published var beanVisibility: [Int: Bool] = [:]

    // 选中的日期和事件详情
    @Published var selectedDate: Date?
    @Published var selectedEvents: DayEvents?
    @Published var showingEventDetail = false {
        didSet {
            print("🔔 事件详情显示状态变更: \(showingEventDetail ? "显示" : "隐藏")")
        }
    }
    
    // 最后刷新时间
    @Published var lastRefreshTime: Date?

    var monthTitle: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月"
        return formatter.string(from: currentDate)
    }

    var calendarDays: [BeanCalendarDayData] {
        guard let data = calendarData else {
            print("⚠️ 计算日历天数失败: calendarData为nil")
            return []
        }

        let calendar = Calendar.current
        let year = data.year
        let month = data.month

        var days: [BeanCalendarDayData] = []

        // 解析日历起始和结束日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        guard let startDate = dateFormatter.date(from: data.calendarStart),
              let endDate = dateFormatter.date(from: data.calendarEnd) else {
            print("⚠️ 解析日历起始/结束日期失败")
            return []
        }

        var current = startDate
        let today = Date()

        print("🗓 开始构建日历: 从 \(data.calendarStart) 到 \(data.calendarEnd)")

        while current <= endDate {
            let dateString = dateFormatter.string(from: current)
            let events = data.dailyEvents[dateString] ?? DayEvents(
                purchaseEvents: [],
                roastEvents: [],
                restEvents: []
            )

            let isCurrentMonth = calendar.component(.month, from: current) == month
            let isToday = calendar.isDate(current, inSameDayAs: today)

            days.append(BeanCalendarDayData(
                date: current,
                events: events,
                isCurrentMonth: isCurrentMonth,
                isToday: isToday
            ))

            current = calendar.date(byAdding: .day, value: 1, to: current) ?? current
        }

        print("📅 日历构建完成: 共\(days.count)天")
        return days
    }

    func initializeBeanVisibility() {
        guard let beans = calendarData?.beans else {
            print("⚠️ 初始化咖啡豆可见性失败: 无咖啡豆数据")
            return
        }

        // 默认所有咖啡豆都可见
        for bean in beans {
            beanVisibility[bean.id] = true
        }
        
        // 更新最后刷新时间
        lastRefreshTime = Date()

        print("👁 初始化咖啡豆可见性完成: 共\(beans.count)个, 默认全部显示")
    }
}
