import SwiftUI

/// 咖啡豆图例视图
struct BeanLegendView: View {
    @ObservedObject var viewModel: BeanCalendarViewModel
    @State private var isExpanded = false
    let onViewUpdate: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // 图例标题和折叠按钮
            HStack {
                Text("咖啡豆图例")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                }) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            
            // 图例内容
            if isExpanded {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(beans) { bean in
                            BeanLegendItemView(
                                bean: bean,
                                viewModel: viewModel,
                                onToggle: {
                                    viewModel.toggleBeanVisibility(bean.id)
                                    // 切换豆子可见性后不再自动刷新数据
                                    onViewUpdate()
                                }
                            )
                            // 简化ID字符串，避免复杂表达式
                            .id("bean-legend-\(bean.id)")
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                }
                .frame(maxHeight: 300)
                .background(Color(.systemBackground))
            }
        }
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    private var beans: [BeanInfo] {
        viewModel.viewState.calendarData?.beans ?? []
    }
}

/// 咖啡豆图例项视图
struct BeanLegendItemView: View {
    let bean: BeanInfo
    @ObservedObject var viewModel: BeanCalendarViewModel
    let onToggle: () -> Void
    
    private var isVisible: Bool {
        viewModel.isBeanVisible(bean.id)
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // 可见性开关
            Button(action: onToggle) {
                Image(systemName: isVisible ? "eye.fill" : "eye.slash.fill")
                    .foregroundColor(isVisible ? .blue : .gray)
                    .font(.system(size: 16))
            }
            
            // 颜色指示器
            Circle()
                .fill(Color(parseHSLColor(bean.color.solid)))
                .frame(width: 16, height: 16)
            
            // 咖啡豆信息
            VStack(alignment: .leading, spacing: 2) {
                Text("\(bean.roaster) - \(bean.name)")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text(stockText)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
        .opacity(isVisible ? 1.0 : 0.6)
    }
    
    private var stockText: String {
        if let progress = bean.progress {
            let progressText = String(format: "%.0f%%", progress)
            
            if let remain = bean.bagRemain, let weight = bean.bagWeight {
                return "\(progressText) (\(String(format: "%.1f", remain))/\(String(format: "%.0f", weight))g)"
            } else {
                return progressText
            }
        } else {
            return "库存信息不可用"
        }
    }
    
    /// 解析HSL颜色字符串
    /// - Parameter hslString: HSL颜色字符串
    /// - Returns: UIColor
    private func parseHSLColor(_ hslString: String) -> UIColor {
        // 解析 "hsl(120 70% 50%)" 格式的字符串
        let cleanString = hslString
            .replacingOccurrences(of: "hsl(", with: "")
            .replacingOccurrences(of: ")", with: "")
            .replacingOccurrences(of: "%", with: "")
        
        let components = cleanString.components(separatedBy: " ")
        
        guard components.count >= 3,
              let hue = Double(components[0]),
              let saturation = Double(components[1]),
              let lightness = Double(components[2]) else {
            return UIColor.systemGray
        }
        
        return UIColor(
            hue: hue / 360.0,
            saturation: saturation / 100.0,
            brightness: lightness / 100.0,
            alpha: 1.0
        )
    }
}

// MARK: - 预览

#if DEBUG
struct BeanLegendView_Previews: PreviewProvider {
    static var previews: some View {
        let viewModel = BeanCalendarViewModel()
        
        // 模拟数据
        let sampleBeans = [
            BeanInfo(
                id: 1,
                name: "耶加雪菲",
                roaster: "蓝瓶咖啡",
                color: BeanColor(solid: "hsl(120 70% 50%)", transparent: "hsl(120 70% 50% / 0.5)"),
                progress: 75.0,
                bagRemain: 150.0,
                bagWeight: 200.0
            ),
            BeanInfo(
                id: 2,
                name: "哥伦比亚",
                roaster: "本地烘焙",
                color: BeanColor(solid: "hsl(240 70% 50%)", transparent: "hsl(240 70% 50% / 0.5)"),
                progress: 50.0,
                bagRemain: 100.0,
                bagWeight: 200.0
            ),
            BeanInfo(
                id: 3,
                name: "巴西",
                roaster: "精品咖啡",
                color: BeanColor(solid: "hsl(60 70% 50%)", transparent: "hsl(60 70% 50% / 0.5)"),
                progress: nil,
                bagRemain: nil,
                bagWeight: nil
            )
        ]
        
        // 使用预览专用初始化方法创建样本数据
        let sampleResponse = BeanCalendarResponse(
            dailyEvents: [:],
            beans: sampleBeans,
            year: 2024,
            month: 3,
            daysInMonth: 31,
            calendarStart: "2024-02-26",
            calendarEnd: "2024-04-06"
        )
        
        viewModel.viewState.calendarData = sampleResponse
        viewModel.viewState.initializeBeanVisibility()
        
        return BeanLegendView(viewModel: viewModel, onViewUpdate: {})
            .padding()
            .previewLayout(.sizeThatFits)
    }
}
#endif
