import Foundation
import Combine
import SwiftUI

/// 咖啡豆日历视图模型
@MainActor
class BeanCalendarViewModel: ObservableObject {
    @Published var viewState = BeanCalendarViewState()

    private let calendarService = BeanCalendarService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // UserDefaults中保存事件筛选设置的Key
    private let kShowPurchaseKey = "BeanCalendar.ShowPurchase"
    private let kShowRoastKey = "BeanCalendar.ShowRoast"
    private let kShowRestKey = "BeanCalendar.ShowRest"

    init() {
        // 加载用户保存的筛选设置
        loadFilterSettings()
        setupBindings()
        loadCurrentMonth()
    }

    // MARK: - 设置保存与加载
    
    /// 加载用户保存的筛选设置
    private func loadFilterSettings() {
        let defaults = UserDefaults.standard
        viewState.showPurchase = defaults.object(forKey: kShowPurchaseKey) as? Bool ?? true
        viewState.showRoast = defaults.object(forKey: kShowRoastKey) as? Bool ?? true
        viewState.showRest = defaults.object(forKey: kShowRestKey) as? Bool ?? true
        
        print("📱 加载用户筛选设置 - 购买: \(viewState.showPurchase), 烘焙: \(viewState.showRoast), 赏味期: \(viewState.showRest)")
    }
    
    /// 保存用户筛选设置
    func saveFilterSettings() {
        let defaults = UserDefaults.standard
        defaults.set(viewState.showPurchase, forKey: kShowPurchaseKey)
        defaults.set(viewState.showRoast, forKey: kShowRoastKey)
        defaults.set(viewState.showRest, forKey: kShowRestKey)
        
        print("💾 保存用户筛选设置 - 购买: \(viewState.showPurchase), 烘焙: \(viewState.showRoast), 赏味期: \(viewState.showRest)")
    }

    // MARK: - 公共方法

    /// 加载指定日期的数据
    func loadCalendarForDate(_ date: Date) {
        print("🔄 加载指定日期的日历数据: \(formatYearMonth(date))")
        viewState.currentDate = date
        loadCalendarData(for: date)
    }

    /// 加载当前月份数据
    func loadCurrentMonth() {
        loadCalendarForDate(viewState.currentDate)
    }

    /// 切换到上个月
    func previousMonth() {
        let calendar = Calendar.current
        if let newDate = calendar.date(byAdding: .month, value: -1, to: viewState.currentDate) {
            print("⬅️ 切换到上个月: \(formatYearMonth(newDate))")
            loadCalendarForDate(newDate)
            print("📌 previousMonth 触发后 currentDate: \(formatYearMonth(viewState.currentDate))")
            print("📊 是否为当前月份: \(isCurrentMonth())")
        }
    }

    /// 切换到下个月
    func nextMonth() {
        let calendar = Calendar.current
        if let newDate = calendar.date(byAdding: .month, value: 1, to: viewState.currentDate) {
            print("➡️ 切换到下个月: \(formatYearMonth(newDate))")
            loadCalendarForDate(newDate)
            print("📌 nextMonth 触发后 currentDate: \(formatYearMonth(viewState.currentDate))")
            print("📊 是否为当前月份: \(isCurrentMonth())")
        }
    }

    /// 切换到当前月份
    func goToToday() {
        // 检查是否已经在当前月份
        if isCurrentMonth() {
            print("📌 已经在当前月份，无需切换")
            return
        }

        print("🔄 跳转到当前月份")
        let today = Date()
        loadCalendarForDate(today)
        print("📌 goToToday 触发后 currentDate: \(formatYearMonth(viewState.currentDate))")
        print("📊 是否为当前月份: \(isCurrentMonth())")

        // 手动触发更新通知，确保立即更新 UI 状态
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }

    /// 切换事件类型显示状态
    /// - Parameter eventType: 事件类型
    func toggleEventType(_ eventType: EventType) {
        switch eventType {
        case .purchase:
            viewState.showPurchase.toggle()
            print("👁 切换购买事件可见性: \(viewState.showPurchase)")
        case .roast:
            viewState.showRoast.toggle()
            print("👁 切换烘焙事件可见性: \(viewState.showRoast)")
        case .rest:
            viewState.showRest.toggle()
            print("👁 切换赏味期事件可见性: \(viewState.showRest)")
        }
        
        // 保存设置
        saveFilterSettings()

        // 手动触发 objectWillChange 通知，强制视图更新
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }

    /// 切换咖啡豆可见性
    /// - Parameter beanId: 咖啡豆ID
    func toggleBeanVisibility(_ beanId: Int) {
        // 确保键存在，如果不存在则设置为默认值
        if viewState.beanVisibility[beanId] == nil {
            viewState.beanVisibility[beanId] = true
        }

        // 切换可见性
        viewState.beanVisibility[beanId]?.toggle()

        // 获取咖啡豆名称用于日志
        let beanName = viewState.calendarData?.beans.first(where: { $0.id == beanId })?.name ?? "未知咖啡豆"
        let isVisible = viewState.beanVisibility[beanId] ?? false
        print("👁 切换咖啡豆可见性: \(beanName) (\(beanId)) -> \(isVisible ? "显示" : "隐藏")")

        // 手动触发 objectWillChange 通知，强制视图更新
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }

    /// 显示指定日期的事件详情
    /// - Parameter day: 日历日期数据
    func showEventDetail(for day: BeanCalendarDayData) {
        viewState.selectedDate = day.date
        viewState.selectedEvents = day.events
        viewState.showingEventDetail = true
        print("🔍 显示事件详情: \(formatDate(day.date)), 事件数: \(day.totalEventCount)")
    }

    /// 隐藏事件详情
    func hideEventDetail() {
        print("🔍 隐藏事件详情")
        viewState.showingEventDetail = false
        viewState.selectedDate = nil
        viewState.selectedEvents = nil

        // 手动触发更新通知，确保立即更新 UI 状态
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }

    /// 刷新数据
    func refresh() {
        print("🔄 手动刷新日历数据")
        loadCalendarForDate(viewState.currentDate)
    }

    /// 检查并仅在需要时刷新数据
    /// 用于视图加载和月份切换时
    func checkAndRefreshIfNeeded() {
        print("🔍 检查是否需要刷新数据")
        
        // 1. 如果没有数据或者有错误，则刷新
        if viewState.calendarData == nil || viewState.errorMessage != nil {
            print("⚠️ 没有数据或存在错误，需要刷新")
            refresh()
            return
        }
        
        // 2. 检查当前显示的年月是否与数据匹配
        let calendar = Calendar.current
        let currentYear = calendar.component(.year, from: viewState.currentDate)
        let currentMonth = calendar.component(.month, from: viewState.currentDate)
        
        if let data = viewState.calendarData,
           data.year != currentYear || data.month != currentMonth {
            print("⚠️ 数据年月不匹配，需要刷新 - 当前: \(currentYear)年\(currentMonth)月, 数据: \(data.year)年\(data.month)月")
            refresh()
            return
        }
        
        // 3. 检查数据是否过期（超过5分钟）
        if let lastRefreshTime = viewState.lastRefreshTime {
            let now = Date()
            let timeInterval = now.timeIntervalSince(lastRefreshTime)
            let refreshThreshold: TimeInterval = 300 // 5分钟
            
            if timeInterval > refreshThreshold {
                print("⚠️ 数据已过期（超过5分钟），需要刷新 - 上次刷新: \(formatTimeInterval(timeInterval))前")
                refresh()
                return
            } else {
                print("✅ 数据未过期，上次刷新: \(formatTimeInterval(timeInterval))前")
            }
        } else {
            // 如果没有记录上次刷新时间，则刷新
            print("⚠️ 未记录上次刷新时间，需要刷新")
            refresh()
            return
        }
        
        print("✅ 已有最新数据，无需刷新")
    }
    
    /// 格式化时间间隔为易读的字符串
    private func formatTimeInterval(_ interval: TimeInterval) -> String {
        if interval < 60 {
            return "\(Int(interval))秒"
        } else if interval < 3600 {
            return "\(Int(interval / 60))分钟"
        } else {
            return "\(Int(interval / 3600))小时"
        }
    }

    /// 取消当前加载
    func cancelLoading() {
        print("🚫 用户取消加载操作")
        // 取消所有网络请求
        cancellables.forEach { $0.cancel() }
        // 重置加载状态
        viewState.isLoading = false
        // 如果没有数据，设置一个错误信息
        if viewState.calendarData == nil {
            viewState.errorMessage = "加载已取消"
        }
    }

    /// 获取可用年份列表
    func getAvailableYears() async -> [Int] {
        do {
            let apiService = APIService.shared
            let yearsData = try await apiService.getAvailableYearsWithCurrent()
            print("📅 获取到可用年份: \(yearsData.years), 当前年份: \(yearsData.currentYear)")
            return yearsData.years
        } catch {
            print("❌ 获取可用年份失败: \(error)")
            // 如果获取失败，返回当前年份前后5年的范围作为备选
            let currentYear = Calendar.current.component(.year, from: Date())
            return Array((currentYear - 5)...(currentYear + 1))
        }
    }

    // MARK: - 私有方法

    private func setupBindings() {
        // 监听当前日期变化 - 这个可能导致重复加载，但可以作为安全网
        viewState.$currentDate
            .dropFirst() // 忽略初始值
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main) // 防止短时间内多次触发
            .sink { [weak self] date in
                print("🔔 监听到currentDate变化: \(self?.formatYearMonth(date) ?? "unknown")")
                // 不再主动调用loadCalendarData，避免重复加载
                // 由previousMonth和nextMonth方法直接调用
            }
            .store(in: &cancellables)
    }

    private func loadCalendarData(for date: Date) {
        print("🔄 开始加载日历数据...")

        // 设置加载状态，但不要清空现有数据，以避免不必要的闪烁
        viewState.isLoading = true
        viewState.errorMessage = nil

        let calendar = Calendar.current
        let year = calendar.component(.year, from: date)
        let month = calendar.component(.month, from: date)

        print("📅 请求年月: \(year)年\(month)月")

        calendarService.fetchCalendarData(year: year, month: month)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    // 确保在所有情况下都重置加载状态
                    DispatchQueue.main.async {
                        self.viewState.isLoading = false
                        print("🏁 数据加载完成 - isLoading状态: \(self.viewState.isLoading)")

                        if case .failure(let error) = completion {
                            self.viewState.errorMessage = error.localizedDescription
                            print("❌ 加载咖啡豆日历数据失败: \(error)")

                            // 如果获取数据失败但仍有旧数据，保留旧数据显示
                            if self.viewState.calendarData == nil {
                                print("⚠️ 没有数据可显示")
                            } else {
                                print("⚠️ 保留旧数据显示")
                            }
                        }

                        // 手动触发UI更新
                        self.objectWillChange.send()
                    }
                },
                receiveValue: { [weak self] response in
                    guard let self = self else { return }

                    // 确保在主线程上更新UI状态
                    DispatchQueue.main.async {
                        print("📦 接收到日历数据: \(response.year)年\(response.month)月, 咖啡豆: \(response.beans.count)个")
                        self.viewState.calendarData = response
                        self.viewState.initializeBeanVisibility()
                        self.viewState.isLoading = false
                        self.viewState.errorMessage = nil
                        
                        // 更新最后刷新时间
                        self.viewState.lastRefreshTime = Date()

                        // 打印日历天数和咖啡豆可见性状态
                        let daysCount = self.viewState.calendarDays.count
                        let visibleBeans = self.viewState.beanVisibility.filter { $0.value }.count
                        print("📆 日历天数: \(daysCount), 可见咖啡豆: \(visibleBeans)个")
                        print("✅ 成功加载咖啡豆日历数据: \(response.year)年\(response.month)月, isLoading状态: \(self.viewState.isLoading)")

                        // 手动触发UI更新
                        self.objectWillChange.send()
                    }
                }
            )
            .store(in: &cancellables)
    }
}

// MARK: - 辅助方法

extension BeanCalendarViewModel {
    /// 当前月份的日历数据（供视图直接使用）
    var currentMonthData: [BeanCalendarDayData] {
        return viewState.calendarDays
    }
    
    /// 判断当前显示的月份是否为当前月份（今天所在的月份）
    /// - Returns: 是否为当前月份
    func isCurrentMonth() -> Bool {
        let calendar = Calendar.current
        let today = Date()

        let currentYear = calendar.component(.year, from: today)
        let currentMonth = calendar.component(.month, from: today)

        let displayYear = calendar.component(.year, from: viewState.currentDate)
        let displayMonth = calendar.component(.month, from: viewState.currentDate)

        return currentYear == displayYear && currentMonth == displayMonth
    }

    /// 获取指定日期的事件
    /// - Parameter date: 日期
    /// - Returns: 当日事件数据
    func getEvents(for date: Date) -> DayEvents? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)

        return viewState.calendarData?.dailyEvents[dateString]
    }

    /// 检查指定咖啡豆是否可见
    /// - Parameter beanId: 咖啡豆ID
    /// - Returns: 是否可见
    func isBeanVisible(_ beanId: Int) -> Bool {
        return viewState.beanVisibility[beanId] ?? false
    }

    /// 获取可见的咖啡豆列表
    /// - Returns: 可见的咖啡豆信息数组
    func getVisibleBeans() -> [BeanInfo] {
        guard let beans = viewState.calendarData?.beans else { return [] }

        return beans.filter { bean in
            isBeanVisible(bean.id)
        }
    }

    /// 获取过滤后的事件
    /// - Parameters:
    ///   - events: 原始事件数据
    ///   - eventType: 事件类型
    /// - Returns: 过滤后的事件数组
    func getFilteredEvents(_ events: [BeanEvent], for eventType: EventType) -> [BeanEvent] {
        // 检查事件类型是否显示
        let shouldShow: Bool
        switch eventType {
        case .purchase:
            shouldShow = viewState.showPurchase
        case .roast:
            shouldShow = viewState.showRoast
        case .rest:
            shouldShow = viewState.showRest
        }

        guard shouldShow else { return [] }

        // 过滤可见的咖啡豆事件
        return events.filter { event in
            isBeanVisible(event.id)
        }
    }

    // 检查前一天是否有相同ID的事件
    func checkPreviousDayConnection(for eventId: Int, on date: Date) -> Bool {
        guard let calendarData = viewState.calendarData else { return false }
        
        let calendar = Calendar.current
        guard let previousDate = calendar.date(byAdding: .day, value: -1, to: date) else { return false }
        
        // 获取前一天的数据
        if let previousDay = viewState.calendarDays.first(where: { calendar.isDate($0.date, inSameDayAs: previousDate) }) {
            // 只检查前一天是否有相同ID的赏味期事件
            let hasSameEvent = previousDay.events.restEvents.contains(where: { $0.id == eventId })
            
            // 还需要检查该事件是否可见（通过事件类型和豆子ID）
            if hasSameEvent {
                // 获取事件所属的豆子ID
                if let bean = calendarData.beans.first(where: { $0.id == eventId }) {
                    return isBeanVisible(bean.id)
                }
            }
        }
        
        return false
    }

    // 检查后一天是否有相同ID的事件
    func checkNextDayConnection(for eventId: Int, on date: Date) -> Bool {
        guard let calendarData = viewState.calendarData else { return false }
        
        let calendar = Calendar.current
        guard let nextDate = calendar.date(byAdding: .day, value: 1, to: date) else { return false }
        
        // 获取后一天的数据
        if let nextDay = viewState.calendarDays.first(where: { calendar.isDate($0.date, inSameDayAs: nextDate) }) {
            // 只检查后一天是否有相同ID的赏味期事件
            let hasSameEvent = nextDay.events.restEvents.contains(where: { $0.id == eventId })
            
            // 还需要检查该事件是否可见（通过事件类型和豆子ID）
            if hasSameEvent {
                // 获取事件所属的豆子ID
                if let bean = calendarData.beans.first(where: { $0.id == eventId }) {
                    return isBeanVisible(bean.id)
                }
            }
        }
        
        return false
    }
}

// MARK: - 格式化方法

extension BeanCalendarViewModel {
    /// 格式化日期为显示字符串
    /// - Parameter date: 日期
    /// - Returns: 格式化后的日期字符串
    func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "M月d日"
        return formatter.string(from: date)
    }

    /// 格式化年月
    /// - Parameter date: 日期
    /// - Returns: 年月格式字符串，例如 "2024年5月"
    func formatYearMonth(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月"
        return formatter.string(from: date)
    }

    /// 格式化库存信息
    /// - Parameter bean: 咖啡豆信息
    /// - Returns: 格式化后的库存字符串
    func formatStock(_ bean: BeanInfo) -> String {
        if let progress = bean.progress {
            let progressText = String(format: "%.0f%%", progress)

            if let remain = bean.bagRemain, let weight = bean.bagWeight {
                return "\(progressText) (\(String(format: "%.1f", remain))/\(String(format: "%.0f", weight))g)"
            } else {
                return progressText
            }
        } else {
            return "-"
        }
    }
}

// MARK: - 用户交互方法
extension BeanCalendarViewModel {
    /// 关闭事件详情
    func closeEventDetail() {
        viewState.showingEventDetail = false
        print("�� 关闭事件详情")
    }
}
