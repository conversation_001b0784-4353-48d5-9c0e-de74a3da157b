import Foundation
import Combine

/// 咖啡豆日历网络服务
class BeanCalendarService: ObservableObject {
    static let shared = BeanCalendarService()

    private let apiService = APIService.shared
    private var cancellables = Set<AnyCancellable>()

    private init() {}

    /// 获取指定年月的咖啡豆日历数据
    /// - Parameters:
    ///   - year: 年份
    ///   - month: 月份
    /// - Returns: 咖啡豆日历响应数据的发布者
    func fetchCalendarData(year: Int, month: Int) -> AnyPublisher<BeanCalendarResponse, Error> {
        return Future { promise in
            Task {
                do {
                    let endpoint = "/ios/api/beans/calendar/"
                    let parameters = [
                        "year": String(year),
                        "month": String(month)
                    ]

                    print("🌐 发起网络请求: \(endpoint) 参数: \(parameters)")
                    
                    // 设置超时时间
                    let response: BeanCalendarResponse = try await self.apiService.get(endpoint, parameters: parameters)
                    
                    // 验证响应数据
                    if response.beans.isEmpty && response.dailyEvents.isEmpty {
                        print("⚠️ 服务器返回了空数据")
                        promise(.failure(CalendarError.noData))
                    } else {
                        print("✅ 网络请求成功，返回数据有效")
                        promise(.success(response))
                    }
                } catch let error as URLError where error.code == .timedOut {
                    print("⚠️ 网络请求超时: \(error.localizedDescription)")
                    promise(.failure(CalendarError.networkError(error)))
                } catch let error as URLError where error.code == .notConnectedToInternet {
                    print("⚠️ 网络连接错误: \(error.localizedDescription)")
                    promise(.failure(CalendarError.networkError(error)))
                } catch {
                    print("⚠️ 其他错误: \(error.localizedDescription)")
                    promise(.failure(error))
                }
            }
        }
        .handleEvents(receiveSubscription: { _ in
            print("🔄 开始日历数据订阅")
        }, receiveOutput: { _ in
            print("📥 接收到日历数据")
        }, receiveCompletion: { completion in
            switch completion {
            case .finished:
                print("✅ 日历数据订阅完成")
            case .failure(let error):
                print("❌ 日历数据订阅失败: \(error.localizedDescription)")
            }
        }, receiveCancel: {
            print("🚫 日历数据订阅取消")
        })
        .eraseToAnyPublisher()
    }

    /// 异步获取咖啡豆日历数据
    /// - Parameters:
    ///   - year: 年份
    ///   - month: 月份
    /// - Returns: 咖啡豆日历响应数据
    func fetchCalendarData(year: Int, month: Int) async throws -> BeanCalendarResponse {
        return try await withCheckedThrowingContinuation { continuation in
            fetchCalendarData(year: year, month: month)
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                    },
                    receiveValue: { response in
                        continuation.resume(returning: response)
                    }
                )
                .store(in: &cancellables)
        }
    }
}

// MARK: - 扩展方法

extension BeanCalendarService {
    /// 获取当前月份的日历数据
    func fetchCurrentMonthData() -> AnyPublisher<BeanCalendarResponse, Error> {
        let now = Date()
        let calendar = Calendar.current
        let year = calendar.component(.year, from: now)
        let month = calendar.component(.month, from: now)

        return fetchCalendarData(year: year, month: month)
    }

    /// 获取指定日期所在月份的日历数据
    /// - Parameter date: 指定日期
    /// - Returns: 咖啡豆日历响应数据的发布者
    func fetchCalendarData(for date: Date) -> AnyPublisher<BeanCalendarResponse, Error> {
        let calendar = Calendar.current
        let year = calendar.component(.year, from: date)
        let month = calendar.component(.month, from: date)

        return fetchCalendarData(year: year, month: month)
    }
}

// MARK: - 错误处理

extension BeanCalendarService {
    enum CalendarError: LocalizedError {
        case invalidDate
        case noData
        case networkError(Error)

        var errorDescription: String? {
            switch self {
            case .invalidDate:
                return "无效的日期"
            case .noData:
                return "没有找到日历数据"
            case .networkError(let error):
                return "网络错误: \(error.localizedDescription)"
            }
        }
    }
}
