import SwiftUI

/// 事件连接状态
enum EventConnectionState {
    case standalone  // 独立事件
    case leadingConnected  // 左侧连接
    case trailingConnected  // 右侧连接
    case bothConnected  // 两侧都连接
}

/// 边框扩展
struct EdgeBorder: Shape {
    var width: CGFloat = 1
    var edges: [Edge] = []

    func path(in rect: CGRect) -> Path {
        var path = Path()
        for edge in edges {
            var line = Path()
            switch edge {
            case .top:
                line.move(to: CGPoint(x: rect.minX, y: rect.minY))
                line.addLine(to: CGPoint(x: rect.maxX, y: rect.minY))
            case .leading:
                line.move(to: CGPoint(x: rect.minX, y: rect.minY))
                line.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
            case .bottom:
                line.move(to: CGPoint(x: rect.minX, y: rect.maxY))
                line.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
            case .trailing:
                line.move(to: CGPoint(x: rect.maxX, y: rect.minY))
                line.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
            }
            path.addPath(line)
        }
        return path.strokedPath(StrokeStyle(lineWidth: width))
    }
}

extension View {
    func border(width: CGFloat, edges: [Edge], color: Color = Color(.systemGray4)) -> some View {
        overlay(EdgeBorder(width: width, edges: edges).foregroundColor(color))
    }
}

/// 日历单元格视图
struct CalendarDayView: View {
    let day: BeanCalendarDayData
    @ObservedObject var viewModel: BeanCalendarViewModel
    
    // 添加反馈生成器用于触觉反馈
    @State private var feedbackGenerator = UIImpactFeedbackGenerator(style: .medium)

    // 减小单元格高度
    private let cellHeight: CGFloat = 98
    // 每个单元格最多显示的事件数
    private let maxVisibleEvents = 3

    var body: some View {
        VStack(spacing: 0) {
            // 日期标签
            HStack {
                Spacer()
                // 简化条件表达式，避免编译器超时
                let dayNumber = Calendar.current.component(.day, from: day.date)
                let textColor: Color = {
                    if day.isToday {
                        return .primaryBg
                    } else if day.isCurrentMonth {
                        return .primaryText
                    } else {
                        return .noteText
                    }
                }()
                
                Text("\(dayNumber)")
                    .font(.caption)
                    .foregroundColor(textColor)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        Group {
                            if day.isToday {
                                Circle().fill(Color.primaryAccent)
                            } else {
                                Color.clear
                            }
                        }
                    )
            }
            .padding(.top, 4)
            .padding(.trailing, 4)

            // 事件列表
            VStack(spacing: 2) {
                // 显示限制数量的事件
                ForEach(visibleEvents.prefix(maxVisibleEvents), id: \.id) { eventInfo in
                    EventItemView(
                        event: eventInfo.event,
                        eventType: eventInfo.type,
                        isCompact: true,
                        connectionState: getConnectionState(for: eventInfo.event)
                    )
                }
                
                // 如果有更多事件，显示+n
                if remainingEventsCount > 0 {
                    HStack {
                        Text("+\(remainingEventsCount)")
                            .font(.caption2)
                            .foregroundColor(.noteText)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 0) // 减小垂直内边距为0
                        Spacer()
                    }
                    .background(
                        Capsule()
                            .fill(Color.archivedText.opacity(0.2))
                    )
                    .frame(height: 12) // 减小高度从16到12
                }
            }
            .padding(.horizontal, 4)

            Spacer()
        }
        .frame(height: cellHeight)
        .background(
            day.isCurrentMonth ? Color.primaryBg : Color.archivedText.opacity(0.1)
        )
        // 只添加上边线，移除左右边线
        .border(width: 0.5, edges: [.top], color: Color(.systemGray4))
        // 移除内边距，确保格子之间没有间隙
        .contentShape(Rectangle())
        .onTapGesture {
            if day.hasEvents {
                print("📅 点击了日期格子: \(Calendar.current.component(.day, from: day.date))日")
                
                // 提供触觉反馈
                feedbackGenerator.prepare()
                feedbackGenerator.impactOccurred()
                
                // 立即显示事件详情
                DispatchQueue.main.async {
                    viewModel.showEventDetail(for: day)
                }
            }
        }
    }

    // MARK: - 计算属性

    private var filteredPurchaseEvents: [BeanEvent] {
        viewModel.getFilteredEvents(day.events.purchaseEvents, for: .purchase)
    }

    private var filteredRoastEvents: [BeanEvent] {
        viewModel.getFilteredEvents(day.events.roastEvents, for: .roast)
    }

    private var filteredRestEvents: [BeanEvent] {
        viewModel.getFilteredEvents(day.events.restEvents, for: .rest)
    }
    
    // 所有可见事件的总数
    private var totalVisibleEventsCount: Int {
        filteredPurchaseEvents.count + filteredRoastEvents.count + filteredRestEvents.count
    }
    
    // 超出最大显示数量的事件数
    private var remainingEventsCount: Int {
        max(0, totalVisibleEventsCount - maxVisibleEvents)
    }
    
    // 事件信息结构体，包含事件和类型
    private struct EventInfo: Identifiable {
        let id = UUID()
        let event: BeanEvent
        let type: EventType
    }
    
    // 合并所有事件并按优先级排序
    private var visibleEvents: [EventInfo] {
        var allEvents: [EventInfo] = []
        
        // 添加购买事件
        filteredPurchaseEvents.forEach { event in
            allEvents.append(EventInfo(event: event, type: .purchase))
        }
        
        // 添加烘焙事件
        filteredRoastEvents.forEach { event in
            allEvents.append(EventInfo(event: event, type: .roast))
        }
        
        // 添加赏味期事件
        filteredRestEvents.forEach { event in
            allEvents.append(EventInfo(event: event, type: .rest))
        }
        
        return allEvents
    }
    
    // 获取事件的连接状态
    private func getConnectionState(for event: BeanEvent) -> EventConnectionState {
        // 检查事件类型，只有赏味期事件才能有连接状态
        let isRestEvent = filteredRestEvents.contains(where: { $0.id == event.id })
        if !isRestEvent {
            return .standalone
        }
        
        // 检查前一天和后一天是否有相同ID的事件
        let hasPreviousDayConnection = viewModel.checkPreviousDayConnection(for: event.id, on: day.date)
        let hasNextDayConnection = viewModel.checkNextDayConnection(for: event.id, on: day.date)
        
        if hasPreviousDayConnection && hasNextDayConnection {
            return .bothConnected
        } else if hasPreviousDayConnection {
            return .leadingConnected
        } else if hasNextDayConnection {
            return .trailingConnected
        } else {
            return .standalone
        }
    }
}

/// 事件项视图
struct EventItemView: View {
    let event: BeanEvent
    let eventType: EventType
    let isCompact: Bool
    let connectionState: EventConnectionState

    // 连接状态的偏移量和扩展
    private var horizontalPadding: CGFloat {
        switch connectionState {
        case .standalone:
            return 0
        case .leadingConnected:
            return -2.0
        case .trailingConnected:
            return -2.0
        case .bothConnected:
            return -4.0
        }
    }
    
    // 连接状态的宽度扩展 - 不再使用，由capsuleWithConnections处理
    private var widthExtension: CGFloat {
        0 // 简化为固定值，实际连接效果由ConnectedCapsule负责
    }

    var body: some View {
        HStack(spacing: 4) {
            // 事件图标
            Text(eventType.icon)
                .font(.caption2)

            // 咖啡豆名称
            if isCompact {
                // 紧凑模式：只显示图标
                Spacer()
            } else {
                // 完整模式：显示名称
                Text(event.name)
                    .font(.caption2)
                    .lineLimit(1)
                    .truncationMode(.tail)
                Spacer()
            }
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 1) // 减小垂直内边距
        .background(
            // 使用AnyShape包装，避免条件表达式
            getEventShape()
                .fill(Color(parseHSLColor(event.color.transparent)))
        )
        .frame(height: 16) // 减小高度
        // 根据连接状态调整水平内边距和宽度
        .padding(.horizontal, horizontalPadding)
        .zIndex(connectionState == .standalone ? 0 : 1) // 确保连接的胶囊显示在上层
    }
    
    // 获取事件形状，简化条件逻辑
    private func getEventShape() -> AnyShape {
        // 直接使用固定类型返回，避免条件渲染
        let shape: AnyShape
        
        switch connectionState {
        case .standalone:
            shape = AnyShape(Capsule())
        case .leadingConnected:
            shape = AnyShape(ConnectedCapsule(leadingConnected: true, trailingConnected: false))
        case .trailingConnected:
            shape = AnyShape(ConnectedCapsule(leadingConnected: false, trailingConnected: true))
        case .bothConnected:
            shape = AnyShape(ConnectedCapsule(leadingConnected: true, trailingConnected: true))
        }
        
        return shape
    }

    /// 解析HSL颜色字符串
    /// - Parameter hslString: HSL颜色字符串，如 "hsl(120 70% 50%)"
    /// - Returns: SwiftUI Color
    private func parseHSLColor(_ hslString: String) -> UIColor {
        // 解析 "hsl(120 70% 50%)" 或 "color-mix(in oklab, hsl(120 70% 50%) 50%, transparent)" 格式的字符串
        var cleanString = hslString

        // 处理 color-mix 格式
        if hslString.contains("color-mix") {
            // 提取 hsl 部分
            if let hslRange = hslString.range(of: "hsl\\([^)]+\\)", options: .regularExpression) {
                cleanString = String(hslString[hslRange])
            } else {
                return UIColor.systemGray5
            }
        }

        // 清理字符串
        cleanString = cleanString
            .replacingOccurrences(of: "hsl(", with: "")
            .replacingOccurrences(of: ")", with: "")
            .replacingOccurrences(of: "%", with: "")

        let components = cleanString.components(separatedBy: " ")

        guard components.count >= 3,
              let hue = Double(components[0]),
              let saturation = Double(components[1]),
              let lightness = Double(components[2]) else {
            return UIColor.systemGray5
        }

        return UIColor(
            hue: hue / 360.0,
            saturation: saturation / 100.0,
            brightness: lightness / 100.0,
            alpha: hslString.contains("transparent") ? 0.5 : 1.0
        )
    }
}

/// 用于包装任何Shape类型的结构体
struct AnyShape: Shape {
    // 使用@Sendable标记闭包，使其符合Sendable协议
    private let _path: @Sendable (CGRect) -> Path
    
    init<S: Shape>(_ shape: S) {
        self._path = { rect in
            shape.path(in: rect)
        }
    }
    
    func path(in rect: CGRect) -> Path {
        return _path(rect)
    }
}

/// 连接胶囊形状
struct ConnectedCapsule: Shape {
    var leadingConnected: Bool
    var trailingConnected: Bool
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // 胶囊的圆角半径
        let cornerRadius = rect.height / 2
        
        // 添加额外的宽度以确保连接
        let extendedRect = CGRect(
            x: rect.minX - (leadingConnected ? 2.0 : 0),
            y: rect.minY,
            width: rect.width + (leadingConnected ? 2.0 : 0) + (trailingConnected ? 2.0 : 0),
            height: rect.height
        )
        
        if leadingConnected && trailingConnected {
            // 两侧都连接，矩形
            path.addRect(extendedRect)
        } else if leadingConnected {
            // 左侧连接，右侧圆角
            path.move(to: CGPoint(x: extendedRect.minX, y: extendedRect.minY))
            path.addLine(to: CGPoint(x: extendedRect.maxX - cornerRadius, y: extendedRect.minY))
            path.addArc(center: CGPoint(x: extendedRect.maxX - cornerRadius, y: extendedRect.minY + cornerRadius),
                        radius: cornerRadius,
                        startAngle: .degrees(-90),
                        endAngle: .degrees(0),
                        clockwise: false)
            path.addLine(to: CGPoint(x: extendedRect.maxX, y: extendedRect.maxY - cornerRadius))
            path.addArc(center: CGPoint(x: extendedRect.maxX - cornerRadius, y: extendedRect.maxY - cornerRadius),
                        radius: cornerRadius,
                        startAngle: .degrees(0),
                        endAngle: .degrees(90),
                        clockwise: false)
            path.addLine(to: CGPoint(x: extendedRect.minX, y: extendedRect.maxY))
            path.closeSubpath()
        } else if trailingConnected {
            // 右侧连接，左侧圆角
            path.move(to: CGPoint(x: extendedRect.minX + cornerRadius, y: extendedRect.minY))
            path.addLine(to: CGPoint(x: extendedRect.maxX, y: extendedRect.minY))
            path.addLine(to: CGPoint(x: extendedRect.maxX, y: extendedRect.maxY))
            path.addLine(to: CGPoint(x: extendedRect.minX + cornerRadius, y: extendedRect.maxY))
            path.addArc(center: CGPoint(x: extendedRect.minX + cornerRadius, y: extendedRect.maxY - cornerRadius),
                        radius: cornerRadius,
                        startAngle: .degrees(90),
                        endAngle: .degrees(180),
                        clockwise: false)
            path.addLine(to: CGPoint(x: extendedRect.minX, y: extendedRect.minY + cornerRadius))
            path.addArc(center: CGPoint(x: extendedRect.minX + cornerRadius, y: extendedRect.minY + cornerRadius),
                        radius: cornerRadius,
                        startAngle: .degrees(180),
                        endAngle: .degrees(270),
                        clockwise: false)
            path.closeSubpath()
        } else {
            // 标准胶囊
            path.addPath(Capsule().path(in: rect))
        }
        
        return path
    }
}

// MARK: - 预览

#if DEBUG
struct CalendarDayView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleEvents = DayEvents(
            purchaseEvents: [
                BeanEvent(
                    id: 1,
                    name: "耶加雪菲",
                    roaster: "蓝瓶咖啡",
                    color: BeanColor(solid: "hsl(120 70% 50%)", transparent: "hsl(120 70% 50% / 0.5)"),
                    progress: 75.0,
                    bagRemain: 150.0,
                    bagWeight: 200.0
                ),
                BeanEvent(
                    id: 3,
                    name: "埃塞俄比亚",
                    roaster: "星巴克",
                    color: BeanColor(solid: "hsl(180 70% 50%)", transparent: "hsl(180 70% 50% / 0.5)"),
                    progress: 90.0,
                    bagRemain: 180.0,
                    bagWeight: 200.0
                )
            ],
            roastEvents: [
                BeanEvent(
                    id: 4,
                    name: "巴西",
                    roaster: "本地烘焙",
                    color: BeanColor(solid: "hsl(200 70% 50%)", transparent: "hsl(200 70% 50% / 0.5)"),
                    progress: 60.0,
                    bagRemain: 120.0,
                    bagWeight: 200.0
                )
            ],
            restEvents: [
                BeanEvent(
                    id: 2,
                    name: "哥伦比亚",
                    roaster: "本地烘焙",
                    color: BeanColor(solid: "hsl(240 70% 50%)", transparent: "hsl(240 70% 50% / 0.5)"),
                    progress: 50.0,
                    bagRemain: 100.0,
                    bagWeight: 200.0
                ),
                BeanEvent(
                    id: 5,
                    name: "肯尼亚",
                    roaster: "本地烘焙",
                    color: BeanColor(solid: "hsl(300 70% 50%)", transparent: "hsl(300 70% 50% / 0.5)"),
                    progress: 40.0,
                    bagRemain: 80.0,
                    bagWeight: 200.0
                ),
                BeanEvent(
                    id: 6,
                    name: "苏门答腊",
                    roaster: "本地烘焙",
                    color: BeanColor(solid: "hsl(330 70% 50%)", transparent: "hsl(330 70% 50% / 0.5)"),
                    progress: 30.0,
                    bagRemain: 60.0,
                    bagWeight: 200.0
                )
            ]
        )

        let sampleDay = BeanCalendarDayData(
            date: Date(),
            events: sampleEvents,
            isCurrentMonth: true,
            isToday: true
        )

        let viewModel = BeanCalendarViewModel()

        CalendarDayView(day: sampleDay, viewModel: viewModel)
            .frame(width: 150, height: 90)
            .padding()
            .previewLayout(.sizeThatFits)
    }
}
#endif
