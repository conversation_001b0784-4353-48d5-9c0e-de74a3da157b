import SwiftUI

struct HeatmapView: View {
    @StateObject private var viewModel = HeatmapViewModel()
    @Environment(\.calendar) var calendar
    @Environment(\.colorScheme) var colorScheme
    @Environment(\.scenePhase) var scenePhase

    private let weekDays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
    private let monthLabels = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]

    // 添加滚动视图的位置状态
    @State private var scrollPosition: CGPoint = .zero

    // 添加屏幕宽度状态
    @State private var screenWidth: CGFloat = UIScreen.main.bounds.width

    // 添加月份滚动位置控制
    @State private var monthScrollID: String? = nil

    // 添加ScrollViewProxy用于滚动控制
    @State private var scrollProxy: ScrollViewProxy? = nil

    // 添加数据加载状态追踪
    @State private var dataLoaded: Bool = false

    // 添加标记是否第一次出现
    @State private var isFirstAppear: Bool = true

    // 控制详情sheet的显示
    @State private var showingDateDetails: Bool = false

    // 添加导航状态
    @State private var selectedDateForNavigation: Date? = nil
    @State private var isNavigatingToFilteredView: Bool = false

    // 年份格式化器 - 不使用千位分隔符
    private let yearFormatter: NumberFormatter = {
        let formatter = NumberFormatter()
        formatter.usesGroupingSeparator = false
        return formatter
    }()

    // 日期格式化器
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()

    // 添加月宽度常量
    private let monthWidth: CGFloat = 104

    // 添加用于滚动定位的辅助方法
    private func getDateGridID(date: Date) -> String {
        let calendar = Calendar.current
        let year = calendar.component(.year, from: date)
        let month = calendar.component(.month, from: date)
        let day = calendar.component(.day, from: date)
        return "date-\(year)-\(month)-\(day)"
    }

    // 获取某月第一天的日期
    private func getFirstDayOfMonth(month: Int, year: Int) -> Date {
        let calendar = Calendar.current
        var components = DateComponents()
        components.year = year
        components.month = month
        components.day = 1
        return calendar.date(from: components) ?? Date()
    }

    var body: some View {
        ZStack {
            ScrollView {
                VStack(spacing: 20) {
                    // 年份选择器 - 分解复杂表达式
                    yearSelectorView

                    // 统计信息
                    statsView

                    // 热力图
                    heatmapContainerView

                    // 图例
                    legendView
                }
            }
            .background(Color.secondaryBg)
            .navigationTitle("冲煮热图")
            .onAppear {
                if isFirstAppear {
                    Task {
                        await viewModel.fetchHeatmapData()

                        // 标记数据已加载
                        dataLoaded = true

                        // 数据加载完成后，直接滚动到当前月份
                        // 减少延迟时间，加快响应速度
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            scrollToCurrentMonth()
                        }

                        // 更新第一次出现状态
                        isFirstAppear = false
                    }
                } else {
                    // 如果不是第一次出现，但已经有数据，也滚动到当前月份
                    // 减少延迟时间
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                        scrollToCurrentMonth()
                    }
                }
            }
            // 监听scenePhase变化，处理应用从后台回到前台的情况
            .onChange(of: scenePhase) { newPhase in
                if newPhase == .active && !isFirstAppear && dataLoaded {
                    // 当应用重新活跃且非首次加载时滚动到当前月份
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                        scrollToCurrentMonth()
                    }
                }
            }
            // 监听数据加载状态变化
            .onChange(of: dataLoaded) { newValue in
                if newValue {
                    // 数据加载完成时滚动到当前月份
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                        scrollToCurrentMonth()
                    }
                }
            }
            .sheet(isPresented: $showingDateDetails) {
                dateDetailsView
            }

            // 隐藏的导航链接
            hiddenNavigationLink
        }
    }

    // 分解年份选择器为单独的视图
    private var yearSelectorView: some View {
        HStack {
            Spacer()

            Menu {
                ForEach(viewModel.availableYears, id: \.self) { year in
                    if year == viewModel.selectedYear {
                        // 已选中的年份显示打钩图标但不可点击
                        Label(formatYear(year), systemImage: "checkmark")
                            .foregroundColor(.gray)
                    } else {
                        // 未选中的年份可以点击选择
                        Button(action: {
                            viewModel.selectedYear = year
                        }) {
                            Text(formatYear(year))
                        }
                    }
                }
            } label: {
                HStack {
                    Text(formatYear(viewModel.selectedYear))
                        .fontWeight(.medium)
                    Image(systemName: "chevron.down")
                        .font(.caption)
                }
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .background(Color.primaryBg)
                .cornerRadius(8)
                .shadow(radius: 1)
            }
        }
        .padding(.horizontal)
    }

    // 分解热力图容器为单独的视图
    private var heatmapContainerView: some View {
        VStack(alignment: .leading, spacing: 6) {
            // 热力图容器
            HStack(alignment: .top, spacing: 6) {
                // 星期标签 - 固定位置
                VStack(alignment: .trailing, spacing: 6) {
                    // 为月份标签留出空间
                    Text("")
                        .frame(height: 20)
                        .offset(y: 0) // 调整空白区域位置

                    // 星期标签
                    ForEach(weekDays, id: \.self) { day in
                        Text(day)
                            .font(.caption2)
                            .frame(height: 20)
                            .frame(width: 40, alignment: .trailing) // 设置固定宽度并靠右对齐
                            .offset(y: 0) // 调整垂直位置，与格子中心对齐
                    }
                }
                .padding(.trailing, 6)
                .zIndex(1) // 确保在最上层

                // 使用ScrollViewReader和ScrollView包装月份标签和热力图网格，使它们一起滚动
                ScrollViewReader { proxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        VStack(alignment: .leading, spacing: 0) { // 移除垂直间距，使布局更加紧凑
                            // 创建一个单独的月份指示标记层，便于定位
                            HStack(spacing: 0) {
                                // 获取所有月份标记位置
                                createAllMonthMarkers()
                            }
                            .padding(.top, 2)

                            // 月份标签 - 极简版本
                            ZStack(alignment: .topLeading) {
                                // 透明背景
                                Rectangle()
                                    .fill(Color.clear)
                                    .frame(height: 20)

                                // 使用ForEach创建月份标签，使布局更加灵活和一致
                                ForEach(0..<12, id: \.self) { month in
                                    // 获取月份标签文本
                                    let monthLabel = monthLabels[month]

                                    // 创建月标签视图
                                    monthLabelView(for: month, label: monthLabel)
                                }
                            }
                            .offset(y: -8) // 调整月份标签位置

                            // 热力图网格 - 调整垂直位置
                            heatmapGridView
                                .offset(y: -10) // 向上移动约半个格子的距离
                        }
                    }
                    .onAppear {
                        // 保存ScrollViewProxy引用
                        scrollProxy = proxy
                        
                        // 视图出现时，尝试滚动到当前月份
                        // 减少延迟时间确保视图已准备好
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            scrollToCurrentMonth()
                        }
                    }
                }
            }
            .padding(.trailing, 16) // 使用水平内边距
            .padding(.vertical, 12) // 调整垂直内边距
            .background(GeometryReader { geometry in
                Color.clear.preference(key: WidthPreferenceKey.self, value: geometry.size.width)
            })
            .onPreferenceChange(WidthPreferenceKey.self) { width in
                self.screenWidth = width
            }
            .onChange(of: viewModel.selectedYear) { newYear in
                // 年份变化时直接滚动到适当位置
                
                // 直接滚动到目标月份，不再先滚动到开头
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    scrollToCurrentMonth()
                }
            }
            .onChange(of: viewModel.isLoading) { newValue in
                // 当加载状态结束时，可能表示数据已刷新完成
                if !newValue && dataLoaded {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                        scrollToCurrentMonth()
                    }
                }
            }
        }
        .background(Color.primaryBg)
        .cornerRadius(8)
        .padding(.horizontal)
    }

    // 热力图网格视图
    private var heatmapGridView: some View {
        // 热力图网格 - 不使用滚动视图，直接显示所有日期
        LazyHGrid(rows: Array(repeating: GridItem(.fixed(20), spacing: 6), count: 7), spacing: 6) {
            // 将所有日期连续显示，不按月份分组
            ForEach(getAllDatesForYear(year: viewModel.selectedYear), id: \.date) { data in
                // 将格子的构建分解成更简单的部分
                dateSquareView(for: data)
            }
        }
        .padding(.leading, 6) // 添加左侧内边距，与网格内部间距相同
        .padding(.trailing, 6) // 添加右侧内边距，与网格内部间距相同
        .padding(.bottom, 4) // 调整底部内边距
        .padding(.top, 0) // 移除顶部内边距
    }

    // 辅助函数：为每个日期创建格子视图
    private func dateSquareView(for data: HeatmapData) -> some View {
        // 基础矩形
        RoundedRectangle(cornerRadius: 2)
            .fill(colorForCount(data.count))
            .frame(width: 20, height: 20)
            .overlay(tapOverlay(for: data))
            .overlay(selectionIndicator(for: data))
            .background(monthMarker(for: data))
    }

    // 分解格子的各个部分
    private func tapOverlay(for data: HeatmapData) -> some View {
        GeometryReader { geometry in
            Color.clear
                .contentShape(Rectangle())
                .onTapGesture {
                    viewModel.selectedDate = data.date
                    if data.count > 0 {
                        showingDateDetails = true
                    }
                }
        }
    }

    private func selectionIndicator(for data: HeatmapData) -> some View {
        Group {
            if viewModel.selectedDate == data.date {
                RoundedRectangle(cornerRadius: 2)
                    .stroke(Color.primaryAccent, lineWidth: 2)
            }
        }
    }

    private func monthMarker(for data: HeatmapData) -> some View {
        Group {
            // 检查是否为每月第一天
            if Calendar.current.component(.day, from: data.date) == 1 {
                // 获取日期所在的月份
                let monthIndex = Calendar.current.component(.month, from: data.date) - 1

                // 确保是当前年份的日期
                if Calendar.current.component(.year, from: data.date) == viewModel.selectedYear {
                    // 添加一个隐藏的标记，用于月份标签定位
                    Color.clear
                        .id("month-\(monthIndex)")
                }
            }
        }
    }

    // 图例视图
    private var legendView: some View {
        HStack(spacing: 16) {
            ForEach([0, 1, 2, 3, 4], id: \.self) { level in
                HStack(spacing: 6) {
                    RoundedRectangle(cornerRadius: 2)
                        .fill(colorForCount(level))
                        .frame(width: 20, height: 20)
                    Text(legendText(for: level))
                        .font(.caption)
                }
            }
        }
        .padding(.all, 16) // 使用与热力图容器相同的内边距
    }

    // 隐藏的导航链接
    private var hiddenNavigationLink: some View {
        NavigationLink(
            destination: selectedDateForNavigation.map { date in
                FilteredBrewLogListView(date: date)
            },
            isActive: $isNavigatingToFilteredView,
            label: { EmptyView() }
        )
    }

    // 日期详情视图
    private var dateDetailsView: some View {
        let selectedDate = viewModel.selectedDate ?? Date()
        let count = viewModel.getCount(for: selectedDate)

        return VStack(spacing: 10) {
            // 标题
            HStack{
                Text("\(dateFormatter.string(from: selectedDate)) 的冲煮记录")
                    .font(.headline)

                Spacer()

                // 右上角关闭按钮
                Button(action: {
                    showingDateDetails = false
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.archivedText)
                        .font(.system(size: 24))
                }
            }

            // 内容
            Text("这一天你完成了 \(count) 次冲煮")
                .padding(.vertical, 12)
                .frame(maxWidth: .infinity, alignment: .leading)

            Spacer(minLength: 5)

            // 查看按钮 - 使用普通按钮，但样式更接近原来的按钮
            Button(action: {
                // 设置导航目标
                selectedDateForNavigation = selectedDate

                // 先关闭sheet
                showingDateDetails = false

                // 使用短延迟确保sheet已关闭
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    // 触发导航
                    isNavigatingToFilteredView = true
                }
            }) {
                Text("查看")
                    .font(.body)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
            }
            .tint(.functionText)
            .buttonStyle(.bordered)
            .buttonBorderShape(.capsule)
            .controlSize(.small)
            .padding(.horizontal, 24)
        }
        .padding()
        .background(Color.secondaryBg)
        .presentationDetents([.height(200)])
    }

    // 格式化年份，不使用千位分隔符
    private func formatYear(_ year: Int) -> String {
        return "\(year)年"
    }

    private var statsView: some View {
        // 统计信息部分
        VStack(spacing: 12) {
            // 统计卡片
            HStack(spacing: 0) {
                // 总冲煮
                VStack(spacing: 4) {
                    Text("总冲煮")
                        .font(.footnote)
                        .foregroundColor(Color.detailText)

                    Text("\(viewModel.totalBrews)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(Color.primaryText)

                    Text("次")
                        .font(.caption)
                        .foregroundColor(Color.archivedText)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.primaryBg)
                )

                Spacer(minLength: 8)

                // 活跃天数
                VStack(spacing: 4) {
                    Text("活跃天数")
                        .font(.footnote)
                        .foregroundColor(Color.detailText)

                    Text("\(viewModel.activeDays)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(Color.primaryText)

                    Text("天")
                        .font(.caption)
                        .foregroundColor(Color.archivedText)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.primaryBg)
                )

                Spacer(minLength: 8)

                // 日均冲煮
                VStack(spacing: 4) {
                    Text("日均冲煮")
                        .font(.footnote)
                        .foregroundColor(Color.detailText)

                    Text(String(format: "%.1f", viewModel.averageBrewsPerDay))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(Color.primaryText)

                    Text("次")
                        .font(.caption)
                        .foregroundColor(Color.archivedText)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.primaryBg)
                )
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
        }
        .background(Color.secondaryBg)
        .padding(.horizontal)
    }

    // 获取整年的所有日期数据
    private func getAllDatesForYear(year: Int) -> [HeatmapData] {
        var allDates: [HeatmapData] = []
        let calendar = Calendar.current

        // 获取年初第一个周一
        guard let firstMonday = getFirstWeekStartOfYear(year: year) else {
            return []
        }

        // 获取下一年的第一天
        let nextYearComponents = DateComponents(year: year + 1, month: 1, day: 1)
        guard let nextYearFirstDay = calendar.date(from: nextYearComponents) else {
            return []
        }

        // 计算下一年第一天的前一天（即当年的最后一天）
        guard let lastDayOfYear = calendar.date(byAdding: .day, value: -1, to: nextYearFirstDay) else {
            return []
        }

        // 计算最后一天是周几（1表示周日，2表示周一，以此类推）
        let lastDayWeekday = calendar.component(.weekday, from: lastDayOfYear)

        // 计算到最后一个周日的天数
        // 如果最后一天是周日(1)，加0天；否则加(8-weekday)天
        let daysToAdd = lastDayWeekday == 1 ? 0 : (8 - lastDayWeekday)

        // 获取包含最后一天的那一周的周日
        guard let lastSunday = calendar.date(byAdding: .day, value: daysToAdd, to: lastDayOfYear) else {
            return []
        }

        // 计算从第一个周一到最后一个周日的总天数
        let totalDays = calendar.dateComponents([.day], from: firstMonday, to: lastSunday).day ?? 0

        // 计算需要显示的总周数
        let totalWeeks = (totalDays + 6) / 7 // 向上取整

        // 生成完整年份的日期格子，按周排列
        for week in 0..<totalWeeks {
            for weekday in 0..<7 {
                // 创建当前日期格子数据
                let dateData = createDateData(weekStart: firstMonday, week: week, weekday: weekday, year: year)
                allDates.append(dateData)
            }
        }

        return allDates
    }

    // 获取年初第一周的起始日期
    private func getFirstWeekStartOfYear(year: Int) -> Date? {
        let calendar = Calendar.current

        // 创建该年第一天的日期
        let components = DateComponents(year: year, month: 1, day: 1)
        guard let firstDayOfYear = calendar.date(from: components) else {
            return nil
        }

        // 计算该年第一天是周几（1表示周日，2表示周一，以此类推）
        let weekday = calendar.component(.weekday, from: firstDayOfYear)

        // 如果第一天是周一，直接返回
        if weekday == 2 {
            return firstDayOfYear
        }

        // 如果第一天不是周一，计算到下一个周一的天数
        // 如果是周二到周日(3-7,1)，需要加上(9-weekday)天
        let daysToAdd = weekday == 1 ? 1 : (9 - weekday)

        // 返回该年第一个周一
        return calendar.date(byAdding: .day, value: daysToAdd, to: firstDayOfYear)
    }

    // 创建特定位置的日期数据
    private func createDateData(weekStart: Date, week: Int, weekday: Int, year: Int) -> HeatmapData {
        let calendar = Calendar.current

        // 计算日期 - 周一开始的偏移量
        // weekday: 0=周一, 1=周二, ..., 6=周日
        let dayOffset = week * 7 + weekday
        let date = calendar.date(byAdding: .day, value: dayOffset, to: weekStart)!

        // 检查该日期是否在所选年份内
        let dateYear = calendar.component(.year, from: date)

        if dateYear == year {
            // 查找该日期是否有数据
            let count = viewModel.getCount(for: date)
            return HeatmapData(date: date, count: count)
        } else {
            // 非所选年份的日期显示为灰色空格
            return HeatmapData(date: date, count: -1) // 使用-1表示非当前年的日期
        }
    }

    // 颜色函数添加对-1值的处理
    private func colorForCount(_ count: Int) -> Color {
        // 非当前年的日期显示为更淡的颜色
        if count == -1 {
            return colorScheme == .dark ?
                Color(red: 70/255, green: 75/255, blue: 70/255) : // 深色模式下更暗的灰色
                Color(red: 245/255, green: 245/255, blue: 245/255) // 浅色模式下更淡的灰色
        }

        if colorScheme == .dark {
            // 暗色模式颜色
            switch count {
            case 0:
                return Color(red: 100/255, green: 105/255, blue: 100/255) // rgb(100, 105, 100)
            case 1:
                return Color(red: 200/255, green: 170/255, blue: 140/255) // rgb(200, 170, 140)
            case 2:
                return Color(red: 220/255, green: 140/255, blue: 100/255) // rgb(220, 140, 100)
            case 3:
                return Color(red: 240/255, green: 110/255, blue: 60/255)  // rgb(240, 110, 60)
            default:
                return Color(red: 250/255, green: 80/255, blue: 30/255)   // rgb(250, 80, 30)
            }
        } else {
            // 亮色模式颜色
            switch count {
            case 0:
                return Color(red: 240/255, green: 242/255, blue: 240/255) // rgb(240, 242, 240)
            case 1:
                return Color(red: 255/255, green: 243/255, blue: 221/255) // rgb(255, 243, 221)
            case 2:
                return Color(red: 254/255, green: 225/255, blue: 177/255) // rgb(254, 225, 177)
            case 3:
                return Color(red: 253/255, green: 189/255, blue: 111/255) // rgb(253, 189, 111)
            default:
                return Color(red: 252/255, green: 141/255, blue: 49/255)  // rgb(252, 141, 49)
            }
        }
    }

    private func legendText(for level: Int) -> String {
        switch level {
        case 0:
            return "无记录"
        case 1:
            return "1次"
        case 2:
            return "2次"
        case 3:
            return "3次"
        default:
            return "4次+"
        }
    }

    // 获取某年第一天的日期
    private func getFirstDayOfYear(year: Int) -> Date {
        let calendar = Calendar.current
        var components = DateComponents()
        components.year = year
        components.month = 1
        components.day = 1
        return calendar.date(from: components) ?? Date()
    }

    // 获取日期的星期偏移量（从周一开始）
    private func getWeekdayOffset(for date: Date) -> Int {
        let calendar = Calendar.current
        // 获取日期是星期几（1表示周日，2表示周一，以此类推）
        let weekday = calendar.component(.weekday, from: date)
        // 转换为从0开始的索引（0表示周一，1表示周二，...，6表示周日）
        return (weekday + 5) % 7
    }

    // 滚动到当前月份，考虑可见区域
    private func scrollToCurrentMonth() {
        guard let proxy = scrollProxy else {
            return
        }

        guard dataLoaded else {
            return
        }

        // 获取当前月份索引
        let currentMonthIndex = viewModel.getCurrentMonthIndex()
        
        // 使用更强制的滚动方式，确保滚动生效
        let targetID = "month-\(currentMonthIndex)"
        
        // 使用更快速的动画
        withAnimation(.easeOut(duration: 0.4)) {
            // 直接滚动到目标月份，考虑左侧间距
            proxy.scrollTo(targetID, anchor: .leading)
        }
        
        // 添加一次备用尝试，使用不同的锚点
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.easeOut(duration: 0.3)) {
                proxy.scrollTo(targetID, anchor: .leading)
            }
        }
    }

    // 计算某月有多少天
    private func getDaysInMonth(month: Int, year: Int) -> Int {
        let calendar = Calendar.current
        var components = DateComponents()
        components.year = year
        components.month = month

        guard let date = calendar.date(from: components),
              let range = calendar.range(of: .day, in: .month, for: date) else {
            return 30 // 默认值
        }

        return range.count
    }

    // 计算某月占用的完整周数
    private func calculateWeeksInMonth(month: Int, year: Int) -> Int {
        let calendar = Calendar.current

        // 获取月初和月末
        let firstDay = getFirstDayOfMonth(month: month, year: year)

        // 获取月末
        var components = DateComponents()
        components.year = year
        components.month = month + 1
        components.day = 0
        guard let lastDay = calendar.date(from: components) else {
            return 5 // 默认值
        }

        // 计算月初所在周的周一
        let weekdayOfFirst = calendar.component(.weekday, from: firstDay)
        // 调整为周一（2表示周一）
        // 如果是周日(1)，需要减去6天；如果是周一到周六(2-7)，需要减去(weekday-2)天
        let mondayOffset = (weekdayOfFirst == 1) ? 6 : (weekdayOfFirst - 2)
        let firstMonday = calendar.date(byAdding: .day, value: -mondayOffset, to: firstDay)!

        // 计算月末所在周的周日
        let weekdayOfLast = calendar.component(.weekday, from: lastDay)
        // 调整为周日（1表示周日）
        let sundayOffset = (weekdayOfLast == 1) ? 0 : (8 - weekdayOfLast)
        let lastSunday = calendar.date(byAdding: .day, value: sundayOffset, to: lastDay)!

        // 计算这两天之间的周数
        let days = calendar.dateComponents([.day], from: firstMonday, to: lastSunday).day ?? 0
        return (days / 7) + 1
    }

    // 辅助函数：计算月份开始的偏移量
    private func calculateStartOffset(forMonth month: Int, inYear year: Int) -> CGFloat {
        var startOffset: CGFloat = 0
        for prevMonth in 0..<month {
            let prevWeeks = calculateWeeksInMonth(month: prevMonth + 1, year: year)
            startOffset += CGFloat(prevWeeks) * 26
        }
        return startOffset
    }

    // 创建所有月份标记
    private func createAllMonthMarkers() -> AnyView {
        // 获取所有月份的第一天日期
        let firstDaysOfMonths = (1...12).map { month -> Date in
            return getFirstDayOfMonth(month: month, year: viewModel.selectedYear)
        }

        // 获取年初第一周的开始日期
        guard let firstWeekOfYear = getFirstWeekStartOfYear(year: viewModel.selectedYear) else {
            return AnyView(EmptyView())
        }

        let content = ZStack(alignment: .topLeading) {
            // 添加一个背景层，用于调试
            Rectangle()
                .fill(Color.clear)
                .frame(height: 10)
                .overlay(
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: 2)
                )

            ForEach(0..<12, id: \.self) { month in
                // 计算每个月份的位置和宽度
                let firstDayOfMonth = firstDaysOfMonths[month]

                // 计算该月第一天相对于年初第一周的偏移周数（精确计算）
                let startDate = firstWeekOfYear
                let endDate = firstDayOfMonth
                let numberOfDays = Calendar.current.dateComponents([.day], from: startDate, to: endDate).day ?? 0
                let numberOfWeeks = numberOfDays / 7

                // 计算该月占用的周数（确保精确计算）
                let weeksInMonth = calculateWeeksInMonth(month: month + 1, year: viewModel.selectedYear)

                // 计算该月开始的x偏移量（基于天数计算更准确）
                let startOffset = CGFloat(numberOfWeeks) * 26 // 每周宽度为26

                // 计算该月宽度
                let monthWidth = CGFloat(weeksInMonth) * 26

                // 创建月份标记 - 增加高度和可见性
                VStack(spacing: 0) {
                    // 添加一个小点作为视觉标记（调试用）
                    Circle()
                        .fill(Color.clear)
                        .frame(width: 4, height: 4)
                    
                    // 主要标记区域
                    Rectangle()
                        .fill(Color.clear)
                        .frame(width: monthWidth, height: 10)
                        .id("month-\(month)") // 使用month作为ID进行滚动定位
                        .background(Color.clear) // 移除调试文本
                }
                .offset(x: startOffset + 6) // 添加左侧间距偏移，与网格左侧间距相同
                .onAppear {
                    // 不再打印调试信息
                }
            }
        }

        return AnyView(content)
    }

    // 辅助函数：创建月份标签视图
    private func monthLabelView(for month: Int, label: String) -> AnyView {
        // 获取该月第一天的日期
        let firstDayOfMonth = getFirstDayOfMonth(month: month + 1, year: viewModel.selectedYear)

        // 获取年初第一周的开始日期
        guard let firstWeekOfYear = getFirstWeekStartOfYear(year: viewModel.selectedYear) else {
            // 确保返回类型与正常路径相同
            return AnyView(
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.primaryText)
                    .offset(x: 0)
            )
        }

        // 计算该月第一天相对于年初第一周的偏移周数（精确计算）
        let startDate = firstWeekOfYear
        let endDate = firstDayOfMonth
        let numberOfDays = Calendar.current.dateComponents([.day], from: startDate, to: endDate).day ?? 0
        let numberOfWeeks = numberOfDays / 7

        // 计算该月占用的周数
        let weeksInMonth = calculateWeeksInMonth(month: month + 1, year: viewModel.selectedYear)

        // 计算该月开始的x偏移量（基于天数计算更准确）
        let startOffset = CGFloat(numberOfWeeks) * 26 // 每周宽度为26

        // 添加微调偏移，确保标签位于月份中心
        // 计算月份中心位置，并添加间距避免重叠
        var monthMiddleOffset: CGFloat = 0
        
        // 根据月份和周数进行精确定位
        if month == 0 {
            // 1月份特殊处理
            monthMiddleOffset = startOffset + 24
        } else {
            // 其他月份使用通用计算公式
            monthMiddleOffset = startOffset + (CGFloat(weeksInMonth) * 26 / 2) - 15
            
            // 根据月份长度进行微调
            if weeksInMonth <= 4 {
                // 对于较短的月份，稍微向左调整标签位置
                monthMiddleOffset -= 5
            }
        }
        
        // 添加通用左侧间距
        monthMiddleOffset += 6

        // 添加月份垂直间隔，确保足够的间距
        let monthSpacing: CGFloat = CGFloat(weeksInMonth) > 3 ? 0 : 14 // 小月份增加左右边距

        // 返回类型与上面guard中的返回类型相同
        return AnyView(
            Text(label)
                .font(.caption2)
                .foregroundColor(.primaryText)
                .offset(x: monthMiddleOffset)
                .frame(width: min(CGFloat(weeksInMonth) * 26 - monthSpacing, 50)) // 限制宽度防止重叠
                .lineLimit(1) // 确保只有一行
                .minimumScaleFactor(0.7) // 允许字体缩小以适应空间
        )
    }
}

// 用于获取视图宽度的PreferenceKey
struct WidthPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// 过滤后的冲煮记录列表视图
struct FilteredBrewLogListView: View {
    let date: Date
    @StateObject private var viewModel = BrewLogViewModel(skipDefaultFilters: true) // 禁用默认筛选
    @Environment(\.dismiss) private var dismiss

    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()

    var body: some View {
        BrewLogListView(viewModel: viewModel)
            .navigationTitle("\(dateFormatter.string(from: date))的冲煮记录")
            .onAppear {
                // 在视图出现时立即设置筛选条件
                setupFilters()
            }
    }

    // 设置筛选条件并加载数据
    private func setupFilters() {
        // 先完全清除所有筛选条件
        viewModel.filterDateFrom = nil
        viewModel.filterDateTo = nil
        viewModel.filterSearchQuery = ""
        viewModel.filterBrewMethod = ""
        viewModel.filterCoffeeBean = ""
        viewModel.filterRatingRange = ""

        // 等待一帧以确保清除操作已完成
        DispatchQueue.main.async {
            // 设置日期筛选条件
            viewModel.filterDateFrom = date
            viewModel.filterDateTo = date
            viewModel.isFiltering = true

            // 立即加载数据
            Task {
                await viewModel.fetchFilteredRecords(forceRefresh: true)
            }
        }
    }
}

#if DEBUG
struct HeatmapView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            HeatmapView()
        }
    }
}
#endif