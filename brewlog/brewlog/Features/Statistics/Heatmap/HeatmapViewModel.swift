import Foundation
import SwiftUI

// MARK: - Models
struct HeatmapData: Identifiable {
    let date: Date
    let count: Int
    var id: Date { date }
}

struct AvailableYearsResponse: Codable {
    let years: [Int]
    let currentYear: Int
    
    enum CodingKeys: String, CodingKey {
        case years
        case currentYear = "current_year"
    }
}

// MARK: - ViewModel
@MainActor
class HeatmapViewModel: ObservableObject {
    @Published var heatmapData: [HeatmapData] = []
    @Published var selectedYear: Int {
        didSet {
            Task {
                await fetchHeatmapData()
            }
        }
    }
    @Published var totalBrews: Int = 0
    @Published var activeDays: Int = 0
    @Published var averageBrewsPerDay: Double = 0.0
    @Published var isLoading: Bool = false
    @Published var error: Error?
    @Published var selectedDate: Date?
    @Published var availableYears: [Int] = []
    
    private let apiService: APIService
    
    init(apiService: APIService = .shared) {
        self.apiService = apiService
        self.selectedYear = Calendar.current.component(.year, from: Date())
        
        // 初始化时立即获取可用年份
        Task {
            await fetchAvailableYears()
        }
    }
    
    func fetchAvailableYears() async {
        do {
            // 从服务器获取可用年份
            let response: AvailableYearsResponse = try await apiService.get("/ios/api/brewlog/available-years/")
            
            if !response.years.isEmpty {
                // 按照年份降序排序
                self.availableYears = response.years.sorted(by: >)
            } else {
                // 如果没有可用年份，则使用当前年份
                let currentYear = Calendar.current.component(.year, from: Date())
                self.availableYears = [currentYear]
            }
            
            // 确保选中的年份在可用年份列表中
            if !self.availableYears.contains(self.selectedYear) {
                self.selectedYear = self.availableYears.first ?? Calendar.current.component(.year, from: Date())
            }
        } catch {
            // 出错时使用当前年份
            let currentYear = Calendar.current.component(.year, from: Date())
            self.availableYears = [currentYear]
        }
    }
    
    func fetchHeatmapData() async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            let response: HeatmapResponse = try await apiService.get(
                "/ios/api/brewlog/heatmap/",
                parameters: ["year": "\(selectedYear)"]
            )
            
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            
            heatmapData = response.data.map { dateString, count in
                let date = formatter.date(from: dateString) ?? Date()
                return HeatmapData(date: date, count: count)
            }.sorted { $0.date < $1.date }
            
            totalBrews = response.totalBrews
            activeDays = response.activeDays
            averageBrewsPerDay = response.averageBrewsPerDay
            error = nil
        } catch {
            self.error = error
        }
    }
    
    func colorForCount(_ count: Int) -> String {
        switch count {
        case 0:
            return "#ebedf0"
        case 1:
            return "#9be9a8"
        case 2...3:
            return "#40c463"
        case 4...6:
            return "#30a14e"
        default:
            return "#216e39"
        }
    }
    
    func getDateString(for date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    func getCount(for date: Date) -> Int {
        if let data = heatmapData.first(where: { Calendar.current.isDate($0.date, inSameDayAs: date) }) {
            return data.count
        }
        return 0
    }
    
    // 获取当前月份索引（0-11）
    func getCurrentMonthIndex() -> Int {
        let currentDate = Date()
        let currentYear = Calendar.current.component(.year, from: currentDate)
        let currentMonth = Calendar.current.component(.month, from: currentDate) - 1 // 转为0-11的索引
        
        // 如果选择的年份是当前年份
        if selectedYear == currentYear {
            // 根据当前月份计算显示位置
            if currentMonth < 3 {
                // 如果是前3个月，显示年初
                return 0
            } else if currentMonth > 8 {
                // 如果是后4个月，显示当前月份
                return currentMonth
            } else {
                // 对于中间月份，显示当前月份
                return currentMonth
            }
        } else if selectedYear < currentYear {
            // 如果是过去年份，显示年末
            return 11 // 显示12月
        } else {
            // 如果是未来年份，显示年初
            return 0
        }
    }
} 