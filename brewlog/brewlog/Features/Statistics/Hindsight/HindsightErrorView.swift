import SwiftUI

struct HindsightErrorView: View {
    let error: Error
    let retryAction: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 50))
                .foregroundColor(.orange)
                .padding(.bottom, 8)

            Text("出错了")
                .font(.headline)

            Text(errorMessage)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            But<PERSON>(action: retryAction) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                    Text("重试")
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(Color.accentColor)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .padding(.top, 8)
        }
        .padding()
    }

    private var errorMessage: String {
        return error.localizedDescription
    }
}

#if DEBUG
struct HindsightErrorView_Previews: PreviewProvider {
    static var previews: some View {
        HindsightErrorView(error: NSError(domain: "测试错误", code: 0, userInfo: nil)) {
            print("重试")
        }
    }
}
#endif