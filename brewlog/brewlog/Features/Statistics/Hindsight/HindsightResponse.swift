import Foundation

// MARK: - 时间范围枚举
enum TimeRange: String, CaseIterable, Identifiable, Codable {
    case week = "week"
    case month = "month"
    case halfYear = "half_year"
    case year = "year"
    
    var id: String { self.rawValue }
    
    var label: String {
        switch self {
        case .week: return "近一周"
        case .month: return "近一月"
        case .halfYear: return "近半年"
        case .year: return "近一年"
        }
    }
    
    // 从任意字符串安全创建TimeRange
    static func fromString(_ string: String?) -> TimeRange? {
        guard let string = string else { return nil }
        
        // 打印尝试转换的字符串
        print("尝试将字符串 '\(string)' 转换为TimeRange")
        
        // 检查是否直接匹配
        if let range = TimeRange(rawValue: string) {
            print("直接匹配成功: \(range.rawValue)")
            return range
        }
        
        // 尝试修正常见问题
        let normalized = string.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        print("规范化后的字符串: '\(normalized)'")
        
        // 检查是否是规范化后的匹配
        if let range = TimeRange(rawValue: normalized) {
            print("规范化后匹配成功: \(range.rawValue)")
            return range
        }
        
        // 尝试特殊情况匹配
        switch normalized {
        case "w", "wk", "week", "weekly":
            print("特殊匹配为week")
            return .week
        case "m", "mo", "mon", "month", "monthly":
            print("特殊匹配为month")
            return .month
        case "h", "half", "half_year", "half-year", "halfyear", "half year":
            print("特殊匹配为half_year")
            return .halfYear
        case "y", "yr", "year", "yearly":
            print("特殊匹配为year")
            return .year
        default:
            print("无法匹配为任何TimeRange值")
            return nil
        }
    }
}

// MARK: - API Response
struct HindsightResponse: Codable {
    // 所有字段都设为可选
    let totalBrews: Int?
    let activeDays: Int?
    let averageBrewsPerDay: Double?
    let averageRating: Double?
    let equipmentStats: [EquipmentStat]?
    let beanStats: [BeanStat]?
    let monthlyBrews: [MonthlyBrew]?
    let ratingDistribution: [RatingData]?
    let timeRange: String?

    // 服务器返回的核心字段
    let totalRecords: Int?
    let totalDays: Int?

    // 咖啡豆消耗力字段
    let totalDose: Double?
    let avgDose: Double?
    let days250gLasts: Double?
    let tastedBeansCount: Int?
    let totalBrewingCost: Double?
    let avgCostPerCup: Double?
    let brewingCostRecordsCount: Int?

    // 冲煮习惯字段
    let mostUsedMethod: String?
    let mostUsedBrewer: String?
    let mostUsedBrewerBrand: String?
    let mostUsedGrinder: String?
    let mostUsedGrinderBrand: String?
    let activeRate: Double?

    // 饮用习惯字段
    let peakPeriod: String?
    let peakWeekday: String?
    let peakTime: String?
    let brewsPerActiveDay: Double?

    // 消费习惯字段
    let mostRepurchasedBean: String?
    let mostRepurchasedBeanRoaster: String?
    let mostVisitedRoaster: String?
    let mostVisitedRoasterAmount: Double?
    let totalBeanCosts: Double?
    let equipmentCosts: Double?

    // 口味偏好字段
    let favoriteBeanName: String?
    let favoriteBeanRoaster: String?
    let favoriteBeanRating: Double?
    let favoriteBeanCount: Int?
    let mostUsedBeanName: String?
    let mostUsedBeanRoaster: String?
    let mostUsedBeanCount: Int?
    let mostUsedBeanRating: Double?
    let mostCommonOrigin: String?
    let mostCommonVariety: String?
    let mostCommonFlavors: [String]?
    let favoriteRecipeName: String?
    let favoriteRecipeRating: Double?
    let favoriteRecipeCount: Int?
    let mostUsedRecipeName: String?
    let mostUsedRecipeCount: Int?
    let mostUsedRecipeRating: Double?

    // 其他统计字段
    let maxStreak: Int?
    let totalAllTimeRecords: Int?
    let daysSinceRegistration: Int?

    enum CodingKeys: String, CodingKey {
        case totalBrews = "total_brews"
        case totalRecords = "total_records"
        case activeDays = "active_days"
        case totalDays = "total_days"
        case averageBrewsPerDay = "average_brews_per_day"
        case averageRating = "average_rating"
        case equipmentStats = "equipment_stats"
        case beanStats = "bean_stats"
        case monthlyBrews = "monthly_brews"
        case ratingDistribution = "rating_distribution"
        case activeRate = "active_rate"
        case timeRange = "time_range"

        // 咖啡豆消耗力字段
        case totalDose = "total_dose"
        case avgDose = "avg_dose"
        case days250gLasts = "days_250g_lasts"
        case tastedBeansCount = "tasted_beans_count"
        case totalBrewingCost = "total_brewing_cost"
        case avgCostPerCup = "avg_cost_per_cup"
        case brewingCostRecordsCount = "brewing_cost_records_count"

        // 冲煮习惯字段
        case mostUsedMethod = "most_used_method"
        case mostUsedBrewer = "most_used_brewer"
        case mostUsedBrewerBrand = "most_used_brewer_brand"
        case mostUsedGrinder = "most_used_grinder"
        case mostUsedGrinderBrand = "most_used_grinder_brand"

        // 饮用习惯字段
        case peakPeriod = "peak_period"
        case peakWeekday = "peak_weekday"
        case peakTime = "peak_time"
        case brewsPerActiveDay = "brews_per_active_day"

        // 消费习惯字段
        case mostRepurchasedBean = "most_repurchased_bean"
        case mostRepurchasedBeanRoaster = "most_repurchased_bean_roaster"
        case mostVisitedRoaster = "most_visited_roaster"
        case mostVisitedRoasterAmount = "most_visited_roaster_amount"
        case totalBeanCosts = "total_bean_costs"
        case equipmentCosts = "equipment_costs"

        // 口味偏好字段
        case favoriteBeanName = "favorite_bean_name"
        case favoriteBeanRoaster = "favorite_bean_roaster"
        case favoriteBeanRating = "favorite_bean_rating"
        case favoriteBeanCount = "favorite_bean_count"
        case mostUsedBeanName = "most_used_bean_name"
        case mostUsedBeanRoaster = "most_used_bean_roaster"
        case mostUsedBeanCount = "most_used_bean_count"
        case mostUsedBeanRating = "most_used_bean_rating"
        case mostCommonOrigin = "most_common_origin"
        case mostCommonVariety = "most_common_variety"
        case mostCommonFlavors = "most_common_flavors"
        case favoriteRecipeName = "favorite_recipe_name"
        case favoriteRecipeRating = "favorite_recipe_rating"
        case favoriteRecipeCount = "favorite_recipe_count"
        case mostUsedRecipeName = "most_used_recipe_name"
        case mostUsedRecipeCount = "most_used_recipe_count"
        case mostUsedRecipeRating = "most_used_recipe_rating"

        // 其他统计字段
        case maxStreak = "max_streak"
        case totalAllTimeRecords = "total_all_time_records"
        case daysSinceRegistration = "days_since_registration"
    }
}

// MARK: - Response Data Types
struct EquipmentStat: Codable {
    let name: String
    let count: Int
    let percentage: Double
}

struct BeanStat: Codable {
    let name: String
    let count: Int
    let percentage: Double
}

struct MonthlyBrew: Codable {
    let month: String
    let count: Int
}

struct RatingData: Codable {
    let rating: Int
    let count: Int
}

// MARK: - View Model Data Types
struct HindsightData: Identifiable, Codable {
    let id = UUID()
    let totalBrews: Int?
    let totalRecords: Int?  // 添加 totalRecords 字段
    let activeDays: Int?
    let totalDays: Int?
    let averageBrewsPerDay: Double
    let averageRating: Double
    let equipmentStats: [EquipmentStatistics]
    let beanStats: [BeanStatistics]
    let monthlyBrews: [MonthlyBrewsData]
    let ratingDistribution: [RatingDistributionData]
    let activeRate: Double?
    let timeRange: TimeRange?

    // 咖啡豆消耗力字段
    let totalDose: Double?
    let avgDose: Double?
    let days250gLasts: Double?
    let tastedBeansCount: Int?
    let totalBrewingCost: Double?
    let avgCostPerCup: Double?
    let brewingCostRecordsCount: Int?

    // 冲煮习惯字段
    let mostUsedMethod: String?
    let mostUsedBrewer: String?
    let mostUsedBrewerBrand: String?
    let mostUsedGrinder: String?
    let mostUsedGrinderBrand: String?

    // 饮用习惯字段
    let peakPeriod: String?
    let peakWeekday: String?
    let peakTime: String?
    let brewsPerActiveDay: Double?

    // 消费习惯字段
    let mostRepurchasedBean: String?
    let mostRepurchasedBeanRoaster: String?
    let mostVisitedRoaster: String?
    let mostVisitedRoasterAmount: Double?
    let totalBeanCosts: Double?
    let equipmentCosts: Double?

    // 口味偏好字段
    let favoriteBeanName: String?
    let favoriteBeanRoaster: String?
    let favoriteBeanRating: Double?
    let favoriteBeanCount: Int?
    let mostUsedBeanName: String?
    let mostUsedBeanRoaster: String?
    let mostUsedBeanCount: Int?
    let mostUsedBeanRating: Double?
    let mostCommonOrigin: String?
    let mostCommonVariety: String?
    let mostCommonFlavors: [String]?
    let favoriteRecipeName: String?
    let favoriteRecipeRating: Double?
    let favoriteRecipeCount: Int?
    let mostUsedRecipeName: String?
    let mostUsedRecipeCount: Int?
    let mostUsedRecipeRating: Double?

    // 其他统计字段
    let maxStreak: Int?
    let totalAllTimeRecords: Int?
    let daysSinceRegistration: Int?
}

struct EquipmentStatistics: Identifiable, Codable {
    let name: String
    let count: Int
    let percentage: Double
    var id: String { name }
}

struct BeanStatistics: Identifiable, Codable {
    let name: String
    let count: Int
    let percentage: Double
    var id: String { name }
}

struct MonthlyBrewsData: Identifiable, Codable {
    let month: String
    let count: Int
    var id: String { month }
}

struct RatingDistributionData: Identifiable, Codable {
    let rating: Int
    let count: Int
    var id: Int { rating }
}

// MARK: - Conversion Extensions
extension HindsightResponse {
    var toViewData: HindsightData {
        // 总冲煮次数按优先级获取：totalBrews > totalRecords > 0
        // 即使 totalBrews 和 totalRecords 都为 nil，也设置为 0 以确保显示数据
        let brews = totalBrews ?? totalRecords ?? 0

        // 处理可能为nil的数组
        let equipmentStatsArray = equipmentStats?.map { stat in
            EquipmentStatistics(
                name: stat.name,
                count: stat.count,
                percentage: stat.percentage
            )
        } ?? []

        let beanStatsArray = beanStats?.map { stat in
            BeanStatistics(
                name: stat.name,
                count: stat.count,
                percentage: stat.percentage
            )
        } ?? []

        let monthlyBrewsArray = monthlyBrews?.map { data in
            MonthlyBrewsData(
                month: data.month,
                count: data.count
            )
        } ?? []

        let ratingDistributionArray = ratingDistribution?.map { data in
            RatingDistributionData(
                rating: data.rating,
                count: data.count
            )
        } ?? []
        
        // 将String类型的timeRange转换为TimeRange枚举
        let timeRangeEnum: TimeRange? = {
            if let rangeString = timeRange {
                print("尝试将timeRange字符串 '\(rangeString)' 转换为TimeRange枚举")
                let result = TimeRange.fromString(rangeString)
                print("转换结果: \(result?.rawValue ?? "nil")")
                return result
            } else {
                print("原始timeRange字符串为nil")
                return nil
            }
        }()

        #if DEBUG
        // 调试输出，检查关键值
        print("转换API响应 - totalBrews: \(totalBrews), totalRecords: \(totalRecords), 使用值: \(brews)")
        print("转换API响应 - totalDose: \(totalDose), tastedBeansCount: \(tastedBeansCount)")
        print("转换API响应 - 原始timeRange: \(timeRange ?? "nil"), 转换后: \(timeRangeEnum?.rawValue ?? "nil")")
        #endif

        return HindsightData(
            totalBrews: brews,
            totalRecords: totalRecords,  // 设置 totalRecords 字段
            activeDays: activeDays,
            totalDays: totalDays,
            averageBrewsPerDay: averageBrewsPerDay ?? 0,
            averageRating: averageRating ?? 0,
            equipmentStats: equipmentStatsArray,
            beanStats: beanStatsArray,
            monthlyBrews: monthlyBrewsArray,
            ratingDistribution: ratingDistributionArray,
            activeRate: activeRate,
            timeRange: timeRangeEnum,

            // 咖啡豆消耗力字段
            totalDose: totalDose,
            avgDose: avgDose,
            days250gLasts: days250gLasts,
            tastedBeansCount: tastedBeansCount,
            totalBrewingCost: totalBrewingCost,
            avgCostPerCup: avgCostPerCup,
            brewingCostRecordsCount: brewingCostRecordsCount,

            // 冲煮习惯字段
            mostUsedMethod: mostUsedMethod,
            mostUsedBrewer: mostUsedBrewer,
            mostUsedBrewerBrand: mostUsedBrewerBrand,
            mostUsedGrinder: mostUsedGrinder,
            mostUsedGrinderBrand: mostUsedGrinderBrand,

            // 饮用习惯字段
            peakPeriod: peakPeriod,
            peakWeekday: peakWeekday,
            peakTime: peakTime,
            brewsPerActiveDay: brewsPerActiveDay,

            // 消费习惯字段
            mostRepurchasedBean: mostRepurchasedBean,
            mostRepurchasedBeanRoaster: mostRepurchasedBeanRoaster,
            mostVisitedRoaster: mostVisitedRoaster,
            mostVisitedRoasterAmount: mostVisitedRoasterAmount,
            totalBeanCosts: totalBeanCosts,
            equipmentCosts: equipmentCosts,

            // 口味偏好字段
            favoriteBeanName: favoriteBeanName,
            favoriteBeanRoaster: favoriteBeanRoaster,
            favoriteBeanRating: favoriteBeanRating,
            favoriteBeanCount: favoriteBeanCount,
            mostUsedBeanName: mostUsedBeanName,
            mostUsedBeanRoaster: mostUsedBeanRoaster,
            mostUsedBeanCount: mostUsedBeanCount,
            mostUsedBeanRating: mostUsedBeanRating,
            mostCommonOrigin: mostCommonOrigin,
            mostCommonVariety: mostCommonVariety,
            mostCommonFlavors: mostCommonFlavors,
            favoriteRecipeName: favoriteRecipeName,
            favoriteRecipeRating: favoriteRecipeRating,
            favoriteRecipeCount: favoriteRecipeCount,
            mostUsedRecipeName: mostUsedRecipeName,
            mostUsedRecipeCount: mostUsedRecipeCount,
            mostUsedRecipeRating: mostUsedRecipeRating,

            // 其他统计字段
            maxStreak: maxStreak,
            totalAllTimeRecords: totalAllTimeRecords,
            daysSinceRegistration: daysSinceRegistration
        )
    }
}