import SwiftUI

struct StatisticsPanel: View {
    let title: String
    let icon: String
    let isCollapsible: Bool
    @State private var isCollapsed: Bool = false
    let content: AnyView

    init<Content: View>(title: String, icon: String, isCollapsible: Bool = true, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.isCollapsible = isCollapsible
        self.content = AnyView(content())
    }

    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                // 左侧图标和标题
                HStack(spacing: 8) {
                    Image(systemName: icon)
                        .foregroundColor(.accentColor)

                    Text(title)
                        .font(.headline)
                }

                Spacer()

                // 右侧折叠按钮
                if isCollapsible {
                    Button(action: {
                        withAnimation {
                            isCollapsed.toggle()
                        }
                    }) {
                        Image(systemName: isCollapsed ? "chevron.down" : "chevron.up")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))

            // 内容区域
            if !isCollapsed {
                VStack {
                    content
                }
                .padding()
                .background(Color.white)
            }
        }
        .cornerRadius(10)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
        )
    }
}

struct StatisticsGrid: View {
    let columns: [GridItem]
    let items: [HindsightStatItem]

    init(columns: Int = 2, items: [HindsightStatItem]) {
        self.columns = Array(repeating: GridItem(.flexible()), count: columns)
        self.items = items
    }

    var body: some View {
        LazyVGrid(columns: columns, spacing: 16) {
            ForEach(items) { item in
                StatisticCard(
                    title: item.title,
                    value: item.value,
                    icon: item.icon,
                    color: item.color
                )
            }
        }
    }
}

// 重命名为HindsightStatItem以避免冲突
struct HindsightStatItem: Identifiable {
    let id = UUID()
    let title: String
    let value: String
    let icon: String
    var color: Color = .accentColor

    static func fromDouble(_ value: Double?, title: String, icon: String, format: String = "%.1f", color: Color = .accentColor) -> HindsightStatItem {
        HindsightStatItem(
            title: title,
            value: value != nil ? String(format: format, value!) : "暂无数据",
            icon: icon,
            color: color
        )
    }

    static func fromInt(_ value: Int?, title: String, icon: String, color: Color = .accentColor) -> HindsightStatItem {
        HindsightStatItem(
            title: title,
            value: value != nil ? "\(value!)" : "暂无数据",
            icon: icon,
            color: color
        )
    }

    static func fromString(_ value: String?, title: String, icon: String, color: Color = .accentColor) -> HindsightStatItem {
        HindsightStatItem(
            title: title,
            value: value ?? "暂无数据",
            icon: icon,
            color: color
        )
    }
}

#if DEBUG
struct StatisticsPanel_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            StatisticsPanel(title: "基本统计", icon: "chart.bar.fill") {
                StatisticsGrid(items: [
                    HindsightStatItem(title: "总冲煮", value: "123", icon: "cup.and.saucer.fill"),
                    HindsightStatItem(title: "活跃天数", value: "45", icon: "calendar"),
                    HindsightStatItem(title: "日均冲煮", value: "2.7", icon: "chart.bar.fill"),
                    HindsightStatItem(title: "平均评分", value: "4.5", icon: "star.fill", color: .yellow)
                ])
            }

            StatisticsPanel(title: "设备统计", icon: "wrench.and.screwdriver.fill") {
                Text("设备统计内容")
            }
        }
        .padding()
    }
}
#endif
