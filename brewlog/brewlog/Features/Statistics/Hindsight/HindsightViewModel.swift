import Foundation

class HindsightViewModel: ObservableObject {
    @Published var hindsightData: HindsightData?
    @Published var isLoadingHindsight = false
    @Published var error: Error?
    @Published var availableYears: [Int] = []
    @Published var selectedTimeRange: TimeRange = .week
    @Published var selectedYear: Int = Calendar.current.component(.year, from: Date())
    @Published var isYearMode: Bool = false
    @Published var noDataMessage: String? = nil

    private let apiService = APIService.shared

    init() {
        // 初始化可用年份
        let currentYear = Calendar.current.component(.year, from: Date())
        availableYears = Array((currentYear-2)...currentYear).reversed()
        
        // 默认使用时间范围模式，选择"近一周"
        isYearMode = false
        selectedTimeRange = .week
        selectedYear = currentYear
        
        print("初始化HindsightViewModel")
        print("初始模式: 时间范围模式")
        print("初始时间范围: \(selectedTimeRange.rawValue) (\(selectedTimeRange.label))")
        print("初始年份: \(selectedYear) (仅在年份模式下使用)")
        
        // 获取服务器上可用的年份并加载初始数据
        Task { 
            await fetchAvailableYears() 
            // 加载初始数据
            await fetchHindsightData()
        }
    }

    // 获取可用年份
    @MainActor
    func fetchAvailableYears() async {
        do {
            // 使用扩展的方法获取可用年份和当前年份
            let result = try await apiService.getAvailableYearsWithCurrent()
            if !result.years.isEmpty {
                self.availableYears = result.years.sorted(by: >)
                // 如果当前未处于年份模式，则使用服务器返回的当前年份
                if !isYearMode {
                    self.selectedYear = result.currentYear
                }
            }
        } catch {
            // 如果获取失败，使用本地计算的年份范围
            print("获取可用年份失败: \(error.localizedDescription)")
            let currentYear = Calendar.current.component(.year, from: Date())
            self.availableYears = Array((currentYear-2)...currentYear).reversed()
        }
    }

    // 获取后见之明数据
    @MainActor
    func fetchHindsightData(forceRefresh: Bool = false) async {
        isLoadingHindsight = true
        error = nil
        noDataMessage = nil

        do {
            // 根据当前模式选择请求参数
            let response: HindsightResponse
            
            // 打印当前模式和选择的参数
            print("当前模式: \(isYearMode ? "年份模式" : "时间范围模式")")
            if isYearMode {
                print("选择的年份: \(selectedYear)")
            } else {
                print("选择的时间范围: \(selectedTimeRange.rawValue) (\(selectedTimeRange.label))")
            }
            
            // 先获取原始响应数据进行调试
            _ = try await apiService.getHindsightRawResponse(
                year: isYearMode ? selectedYear : nil,
                timeRange: isYearMode ? nil : selectedTimeRange
            )
            
            // 确保在年份模式下只传递year参数，在时间范围模式下只传递timeRange参数
            if isYearMode {
                // 年份模式 - 只传递year参数，确保timeRange为nil
                print("发送年份模式请求，参数: year=\(selectedYear), timeRange=nil, forceRefresh=\(forceRefresh)")
                response = try await apiService.getHindsightData(year: selectedYear, timeRange: nil, forceRefresh: forceRefresh)
            } else {
                // 时间范围模式 - 只传递timeRange参数，确保year为nil
                print("发送时间范围模式请求，参数: year=nil, timeRange=\(selectedTimeRange.rawValue), forceRefresh=\(forceRefresh)")
                response = try await apiService.getHindsightData(year: nil, timeRange: selectedTimeRange, forceRefresh: forceRefresh)
            }

            // 调试输出
            print("HindsightViewModel - 成功获取数据:")
            print("totalBrews: \(response.totalBrews ?? -1)")
            print("totalRecords: \(response.totalRecords ?? -1)")
            print("totalDose: \(response.totalDose ?? -1)")
            print("tastedBeansCount: \(response.tastedBeansCount ?? -1)")
            print("timeRange: \(response.timeRange ?? "nil")")

            // 转换为视图数据
            hindsightData = response.toViewData

            // 打印转换后的数据
            print("转换后的数据:")
            print("totalBrews: \(hindsightData?.totalBrews ?? -1)")
            print("totalRecords: \(hindsightData?.totalRecords ?? -1)")
            print("totalDose: \(hindsightData?.totalDose ?? -1)")
            print("tastedBeansCount: \(hindsightData?.tastedBeansCount ?? -1)")
            print("timeRange: \(hindsightData?.timeRange?.rawValue ?? "nil")")

            // 更新冲煮提醒服务的数据
            if let hindsightData = hindsightData {
                print("🔄 HindsightViewModel 更新冲煮提醒服务数据")
                BrewReminderService.shared.updateWithHindsightData(hindsightData)
            }

            // 检查是否有数据
            checkDataAvailability()

        } catch {
            self.error = error
            print("获取后见之明数据失败: \(error.localizedDescription)")
        }

        isLoadingHindsight = false
    }
    
    // 检查数据可用性
    private func checkDataAvailability() {
        guard let data = hindsightData else {
            noDataMessage = "暂无数据"
            return
        }
        
        // 检查是否有冲煮记录
        if data.totalBrews == 0 || data.totalRecords == 0 {
            if isYearMode {
                noDataMessage = "\(selectedYear)年没有冲煮记录"
            } else {
                noDataMessage = "\(selectedTimeRange.label)没有冲煮记录"
            }
        } else {
            noDataMessage = nil
        }
    }
    
    // 切换到年份模式
    @MainActor
    func switchToYearMode(_ year: Int) {
        print("切换到年份模式: \(year)")
        print("之前模式: \(isYearMode ? "年份模式" : "时间范围模式"), 之前年份: \(selectedYear), 之前时间范围: \(selectedTimeRange.rawValue)")
        
        isYearMode = true
        selectedYear = year
        
        print("切换后 - 当前模式: 年份模式, 选择的年份: \(selectedYear)")
        
        Task {
            await fetchHindsightData()
        }
    }
    
    // 切换到时间范围模式
    @MainActor
    func switchToTimeRangeMode(_ range: TimeRange) {
        print("切换到时间范围模式: \(range.rawValue) (\(range.label))")
        print("之前模式: \(isYearMode ? "年份模式" : "时间范围模式"), 之前年份: \(selectedYear), 之前时间范围: \(selectedTimeRange.rawValue)")
        
        isYearMode = false
        selectedTimeRange = range
        
        print("切换后 - 当前模式: 时间范围模式, 选择的时间范围: \(selectedTimeRange.rawValue) (\(selectedTimeRange.label))")
        
        Task {
            await fetchHindsightData()
        }
    }

    // MARK: - 调试方法
    // 测试所有时间范围参数 - 仅在需要调试时手动调用
    @MainActor
    func testAllTimeRangeParameters() async {
        print("开始测试所有时间范围参数")
        
        // 测试服务器端时间范围参数处理
        await apiService.testAllTimeRangeParameters()
        
        // 测试本地切换并强制刷新获取数据
        print("测试本地时间范围切换并获取数据")
        for timeRange in TimeRange.allCases {
            print("\n---------- 测试时间范围: \(timeRange.rawValue) ----------")
            switchToTimeRangeMode(timeRange)
            // 使用强制刷新获取数据
            await fetchHindsightData(forceRefresh: true)
            // 添加短暂延迟，避免请求过于频繁
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
        }
        
        // 恢复到初始状态
        print("\n---------- 恢复到初始状态: week ----------")
        switchToTimeRangeMode(.week)
        await fetchHindsightData(forceRefresh: true)
        
        print("时间范围参数测试完成")
    }
}