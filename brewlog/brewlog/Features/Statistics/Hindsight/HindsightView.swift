import SwiftUI
import Charts
import Foundation

struct HindsightView: View {
    @StateObject private var viewModel = HindsightViewModel()
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var isInitialLoading = true
    @Namespace private var animation

    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // 筛选控制
                VStack(spacing: 8) {
                    // 时间范围选择器 - 居中显示，带动效
                    HStack(spacing: 8) {
                        Spacer()
                        ForEach(TimeRange.allCases) { range in
                            timeRangeButton(for: range)
                        }
                        Spacer()
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: viewModel.selectedTimeRange)

                    // 年份下拉菜单 - 居中显示
                    HStack {
                        Spacer()
                        Menu {
                            ForEach(viewModel.availableYears, id: \.self) { year in
                                if viewModel.isYearMode && viewModel.selectedYear == year {
                                    // 已选中的年份显示打钩图标，但不可点击
                                    Label(
                                        title: { Text("\(String(year))年") },
                                        icon: { Image(systemName: "checkmark") }
                                    )
                                } else {
                                    // 未选中的年份可以点击
                                    Button(action: {
                                        viewModel.switchToYearMode(year)
                                    }) {
                                        Text("\(String(year))年")
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Text(viewModel.isYearMode ? "\(String(viewModel.selectedYear))年鉴" : "年鉴")
                                    .font(.subheadline)
                                Image(systemName: "chevron.down")
                                    .font(.subheadline)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(viewModel.isYearMode ? Color.functionText : Color.navbarBg)
                            .foregroundColor(viewModel.isYearMode ? .white : .primary)
                            .cornerRadius(16)
                        }
                        Spacer()
                    }
                }
                .padding(.bottom, 8)

                if viewModel.isLoadingHindsight && !isInitialLoading {
                    // 非初次加载时显示内联加载指示器
                    VStack {
                        BlinkingLoader(color: .primaryText, text: "加载中...")
                    }
                    .frame(maxWidth: .infinity, minHeight: 200)
                    .padding()
                    .background(Color.secondaryBg.opacity(0.8))
                    .cornerRadius(10)
                } else if let error = viewModel.error {
                    VStack(spacing: 10) {
                        Text("加载失败")
                            .font(.headline)
                        Text(error.localizedDescription)
                            .font(.caption)
                            .multilineTextAlignment(.center)
                        Button("重试") {
                            Task {
                                if isInitialLoading {
                                    isInitialLoading = true
                                }
                                await viewModel.fetchHindsightData()
                            }
                        }
                        .buttonStyle(.bordered)
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.white)
                    .cornerRadius(10)
                    .shadow(radius: 2)
                    .padding(.horizontal)
                } else if let noDataMessage = viewModel.noDataMessage {
                    // 显示无数据提示
                    VStack(spacing: 10) {
                        Image(systemName: "mug.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.archivedText)
                            .padding(.bottom, 8)

                        Text(noDataMessage)
                            .font(.headline)
                            .foregroundColor(.detailText)

                        Text("尝试选择其他时间范围或年份")
                            .font(.subheadline)
                            .foregroundColor(.noteText.opacity(0.8))
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, minHeight: 200)
                    .background(Color.secondaryBg)
                    .padding(.horizontal)
                } else if let stats = viewModel.hindsightData {
                    // 放宽条件判断，只要有数据就显示，不再检查特定字段是否大于0
                    // 咖啡豆消耗力
                    StatCardGroup(title: "咖啡豆消耗力", systemImage: "leaf") {
                        StatCard(title: "总用豆量", value: "\(formatNumber(stats.totalDose ?? 0))", description: "克")
                        StatCard(title: "平均每次用豆", value: "\(formatNumber(stats.avgDose ?? 0))", description: "克")
                        StatCard(title: "半斤豆子可用", value: "\(formatNumber(stats.days250gLasts ?? 0))", description: "天")
                        StatCard(title: "品尝过的豆子", value: "\(stats.tastedBeansCount ?? 0)", description: "款")

                        if let totalBrewingCost = stats.totalBrewingCost, totalBrewingCost > 0 {
                            StatCard(title: "冲煮用豆成本",
                                    value: "\(formatDecimal(totalBrewingCost))",
                                    description: "元")

                            if let avgCostPerCup = stats.avgCostPerCup, avgCostPerCup > 0 {
                                StatCard(title: "平均每杯成本",
                                        value: "\(formatDecimal(avgCostPerCup))",
                                        description: "元/杯")
                            }
                        }
                    }

                    // 冲煮习惯
                    StatCardGroup(title: "冲煮习惯", systemImage: "mug") {
                        StatCard(title: "总冲煮次数", value: "\(stats.totalBrews ?? 0)", description: "次")
                        StatCard(title: "平均每天冲煮", value: "\(formatDecimal(stats.averageBrewsPerDay))", description: "次")

                        if let activeDays = stats.activeDays, let totalDays = stats.totalDays {
                            let percentage = Double(activeDays) / Double(totalDays) * 100
                            StatCard(title: "活跃天数",
                                    value: "\(activeDays)",
                                    description: "天(\(NumberFormatters.formatWithPrecision(percentage, precision: 1))%)")
                        }

                        if let method = stats.mostUsedMethod {
                            StatCard(title: "最常用冲煮方式",
                                    value: method,
                                    description: " ")
                        }

                        if let brewer = stats.mostUsedBrewer {
                            StatCard(title: "最常用冲煮器具", value: brewer, description: stats.mostUsedBrewerBrand ?? "")
                        }

                        if let grinder = stats.mostUsedGrinder {
                            StatCard(title: "最常用磨豆机", value: grinder, description: stats.mostUsedGrinderBrand ?? "")
                        }
                    }

                    // 饮用习惯
                    StatCardGroup(title: "饮用习惯", systemImage: "clock") {
                        StatCard(title: "平均每天喝",
                                value: "\(formatNumber(stats.brewsPerActiveDay ?? 0))",
                                description: "杯")

                        if let peakPeriod = stats.peakPeriod {
                            StatCard(title: "热点时期",
                                    value: peakPeriod,
                                    description: "更爱在\(peakPeriod)冲煮")
                        }

                        if let peakWeekday = stats.peakWeekday {
                            StatCard(title: "热点日期",
                                    value: peakWeekday,
                                    description: "最常在这天冲煮")
                        }

                        if let peakTime = stats.peakTime {
                            StatCard(title: "热点时刻",
                                    value: formatPeakTime(peakTime),
                                    description: "最爱在这个时候冲煮")
                        }
                    }

                    // 消费习惯
                    StatCardGroup(title: "消费习惯", systemImage: "creditcard") {
                        if let bean = stats.mostRepurchasedBean, let roaster = stats.mostRepurchasedBeanRoaster {
                            StatCard(title: "最常回购的豆子",
                                    value: bean,
                                    description: roaster)
                        }

                        if let roaster = stats.mostVisitedRoaster, let amount = stats.mostVisitedRoasterAmount {
                            StatCard(title: "最常光顾的豆商",
                                    value: roaster,
                                    description: "累计 \(formatDecimal(amount)) 元")
                        }

                        if let beanCosts = stats.totalBeanCosts {
                            StatCard(title: "咖啡豆支出",
                                    value: "\(formatDecimal(beanCosts))",
                                    description: "元")
                        }

                        if let equipmentCosts = stats.equipmentCosts {
                            StatCard(title: "设备支出",
                                    value: "\(formatDecimal(equipmentCosts))",
                                    description: "元")
                        }
                    }

                    // 口味偏好
                    StatCardGroup(title: "口味偏好", systemImage: "heart") {
                        if let favoriteBean = stats.favoriteBeanName {
                            let roasterText = stats.favoriteBeanRoaster ?? ""
                            let displayText = roasterText.isEmpty ? favoriteBean : "\(roasterText) - \(favoriteBean)"

                            StatCard(title: "最爱的豆子",
                                    value: displayText,
                                    description: stats.favoriteBeanRating != nil ? "平均得分 \(formatNumber(stats.favoriteBeanRating!))" : " ",
                                    forceWideCard: true)
                        }

                        if let mostUsedBean = stats.mostUsedBeanName {
                            let roasterText = stats.mostUsedBeanRoaster ?? ""
                            let displayText = roasterText.isEmpty ? mostUsedBean : "\(roasterText) - \(mostUsedBean)"

                            StatCard(title: "最常用的豆子",
                                    value: displayText,
                                    description: stats.mostUsedBeanCount != nil ? "使用 \(stats.mostUsedBeanCount!) 次" : " ",
                                    forceWideCard: true)
                        }

                        if let origin = stats.mostCommonOrigin {
                            StatCard(title: "最常品的产地",
                                    value: origin,
                                    description: " ",
                                    forceWideCard: true)
                        }

                        if let variety = stats.mostCommonVariety {
                            StatCard(title: "最常品的豆种",
                                    value: variety,
                                    description: " ",
                                    forceWideCard: true)
                        }

                        if let flavors = stats.mostCommonFlavors, !flavors.isEmpty {
                            StatCard(title: "最常品的风味",
                                    value: flavors.joined(separator: "、"),
                                    description: " ",
                                    forceWideCard: true)
                        }

                        if let recipeName = stats.favoriteRecipeName {
                            StatCard(title: "最爱的配方",
                                    value: recipeName,
                                    description: stats.favoriteRecipeRating != nil ? "平均得分 \(formatNumber(stats.favoriteRecipeRating!))" : " ",
                                    forceWideCard: true)
                        }

                        if let recipeName = stats.mostUsedRecipeName {
                            StatCard(title: "最常用的配方",
                                    value: recipeName,
                                    description: stats.mostUsedRecipeCount != nil ? "使用 \(stats.mostUsedRecipeCount!) 次" : " ",
                                    forceWideCard: true)
                        }
                    }

                    // 成就（迄今）
                    if !viewModel.isYearMode {
                        StatCardGroup(title: "成就（迄今）", systemImage: "sparkles") {
                            if let maxStreak = stats.maxStreak {
                                StatCard(title: "连续打卡记录",
                                        value: "\(maxStreak)",
                                        description: "天")
                            }

                            if let totalRecords = stats.totalAllTimeRecords {
                                StatCard(title: "总打卡次数",
                                        value: "\(totalRecords)",
                                        description: "次")
                            }

                            if let days = stats.daysSinceRegistration, days >= 1 {
                                StatCard(title: "咖啡搭子已陪伴你",
                                        value: formattedDuration(days: days),
                                        description: " ")
                            }
                        }
                    }
                } else {
                    // 初始数据为空状态
                    VStack(spacing: 10) {
                        Image(systemName: "mug.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.gray)
                            .padding(.bottom, 8)

                        Text("暂无数据")
                            .font(.headline)
                            .foregroundColor(.gray)

                        Button("加载数据") {
                            Task {
                                isInitialLoading = true
                                await viewModel.fetchHindsightData()
                            }
                        }
                        .buttonStyle(.bordered)
                        .padding(.top)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, minHeight: 200)
                    .background(Color.white)
                    .cornerRadius(10)
                    .shadow(radius: 1)
                    .padding(.horizontal)
                    .onAppear {
                        Task {
                            await viewModel.fetchHindsightData()
                        }
                    }
                }
            }
            .padding(.vertical)
        }
        .background(Color.secondaryBg)
        .navigationTitle("后见之明")
        .loadingFullScreen(viewModel.isLoadingHindsight && isInitialLoading, text: "加载中...", showBackground: false)
        .onChange(of: viewModel.isLoadingHindsight) { newValue in
            if !newValue {
                // 加载完成后将初始加载状态设为false
                isInitialLoading = false
            }
        }
        .onAppear {
            // 进入视图时强制更新主题颜色
            themeManager.updateThemeColorsOnly()
        }
        // 监听系统主题变化通知
        .onReceive(NotificationCenter.default.publisher(for: TraitChangeObserver.traitChangeNotification)) { _ in
            themeManager.updateThemeColorsOnly()
        }
    }

    // 时间范围按钮
    private func timeRangeButton(for range: TimeRange) -> some View {
        Button(action: {
            viewModel.switchToTimeRangeMode(range)
        }) {
            ZStack {
                // 背景
                Capsule()
                    .fill(Color.navbarBg)
                    .frame(height: 32)

                // 选中状态动画背景
                if viewModel.selectedTimeRange == range && !viewModel.isYearMode {
                    Capsule()
                        .fill(Color.functionText)
                        .matchedGeometryEffect(id: "timeRangeBackground", in: animation)
                }

                // 按钮文本
                Text(range.label)
                    .font(.subheadline)
                    .fontWeight(viewModel.selectedTimeRange == range && !viewModel.isYearMode ? .medium : .regular)
                    .padding(.horizontal, 14)
                    .foregroundColor(viewModel.selectedTimeRange == range && !viewModel.isYearMode ? .white : .primary)
                    .fixedSize()
            }
            .contentShape(Capsule())
        }
        .buttonStyle(PlainButtonStyle())
        .frame(height: 32)
    }

    // 格式化数字（一位小数）
    private func formatNumber(_ value: Double) -> String {
        return NumberFormatters.formatWithPrecision(value, precision: 1)
    }

    // 格式化数字（两位小数）
    private func formatDecimal(_ value: Double) -> String {
        return NumberFormatters.formatWithPrecision(value, precision: 2)
    }

    // 天数转换为时间段描述
    private func formattedDuration(days: Int) -> String {
        let years = days / 365
        let remainingDays = days % 365
        let months = remainingDays / 30

        if years > 0 {
            if months > 0 {
                return "\(years)年\(months)个月"
            }
            return "\(years)年"
        } else if months > 0 {
            return "\(months)个月"
        } else {
            return "\(days)天"
        }
    }

    // 格式化热点时刻 - 简化版
    private func formatPeakTime(_ timeStr: String?) -> String {
        guard let timeStr = timeStr, let hour = Int(timeStr.split(separator: ":").first ?? "") else {
            return timeStr ?? ""
        }

        // 定义时间段
        let period = hour < 6 ? "凌晨" : hour < 12 ? "早上" : hour < 18 ? "下午" : "晚上"

        // 转换为12小时制
        let displayHour = hour <= 12 ? hour : hour - 12

        return "\(period)\(displayHour)点"
    }
}

// 统计卡片组
struct StatCardGroup<Content: View>: View {
    let title: String
    let systemImage: String
    let content: Content
    @EnvironmentObject private var themeManager: ThemeManager

    init(title: String, systemImage: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.systemImage = systemImage
        self.content = content()
    }

    var body: some View {
        VStack(spacing: 12) {
            // 标题
            HStack {
                Image(systemName: systemImage)
                    .foregroundColor(Color.primaryText)
                Text(title)
                    .font(.headline)
                    .foregroundColor(Color.primaryText)
                Spacer()
            }
            .padding(.horizontal, 12)

            // 内容
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 10) {
                    content
                }
                .padding(.horizontal, 12)
                .padding(.bottom, 4) // 添加底部间距，避免阴影被裁剪
            }
        }
        .padding(.vertical, 8)
        .background(Color.secondaryBg)
        .padding(.horizontal)
        // 监听系统主题变化通知
        .onReceive(NotificationCenter.default.publisher(for: TraitChangeObserver.traitChangeNotification)) { _ in
            themeManager.updateThemeColorsOnly()
        }
    }
}

// 单个统计卡片
struct StatCard: View {
    let title: String
    let value: String
    let description: String
    let forceWideCard: Bool
    @EnvironmentObject private var themeManager: ThemeManager

    // 为现有调用提供默认参数
    init(title: String, value: String, description: String, forceWideCard: Bool = false) {
        self.title = title
        self.value = value
        self.description = description
        self.forceWideCard = forceWideCard
    }

    // 文本长度分级
    private enum TextLength {
        case short        // 短文本 (≤ 6字符)
        case medium       // 中等文本 (7-12字符)
        case long         // 长文本 (13-20字符)
        case veryLong     // 超长文本 (> 20字符)
    }

    // 判断文本长度级别
    private var textLengthCategory: TextLength {
        let count = value.count
        if count <= 6 {
            return .short
        } else if count <= 12 {
            return .medium
        } else if count <= 20 {
            return .long
        } else {
            return .veryLong
        }
    }

    // 根据文本长度获取字体大小
    private var fontSize: Font {
        if forceWideCard {
            return .body
        }

        switch textLengthCategory {
        case .short:
            return .title2
        case .medium:
            return .title3
        case .long, .veryLong:
            return .body
        }
    }

    // 根据文本长度获取行数限制
    private var lineLimit: Int {
        if forceWideCard {
            return 3
        }

        switch textLengthCategory {
        case .short, .medium:
            return 1
        case .long:
            return 2
        case .veryLong:
            return 3
        }
    }

    // 根据文本长度获取最小缩放因子
    private var scaleFactor: CGFloat {
        if forceWideCard {
            return 0.5
        }

        switch textLengthCategory {
        case .short:
            return 0.8
        case .medium:
            return 0.7
        case .long:
            return 0.6
        case .veryLong:
            return 0.5
        }
    }

    // 根据文本长度计算卡片宽度
    private var cardWidth: CGFloat {
        if forceWideCard {
            return 180
        }

        switch textLengthCategory {
        case .short:
            return 120
        case .medium:
            return 130
        case .long:
            return 150
        case .veryLong:
            return 180
        }
    }

    var body: some View {
        VStack(spacing: 4) {
            // 标题
            Text(title)
                .font(.footnote)
                .foregroundColor(Color.detailText)
                .multilineTextAlignment(.center)
                .padding(.top, 2)

            // 数值 - 根据文本长度调整显示
            Text(value)
                .font(fontSize)
                .fontWeight(textLengthCategory == .short ? .bold : .semibold)
                .foregroundColor(Color.primaryText)
                .multilineTextAlignment(.center)
                .lineLimit(lineLimit)
                .minimumScaleFactor(scaleFactor)
                .fixedSize(horizontal: false, vertical: true)
                .padding(.horizontal, 4)

            // 描述
            if !description.isEmpty {
                Text(description)
                    .font(.caption)
                    .foregroundColor(Color.archivedText)
                    .multilineTextAlignment(.center)
                    .lineLimit(1)
                    .padding(.bottom, 2)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.primaryBg)
        )
        .shadow(color: Color.primaryText.opacity(0.05), radius: 1.5, x: 0, y: 1)
        .fixedSize(horizontal: false, vertical: true)
        .frame(width: cardWidth)
        // 监听系统主题变化通知
        .onReceive(NotificationCenter.default.publisher(for: TraitChangeObserver.traitChangeNotification)) { _ in
            themeManager.updateThemeColorsOnly()
        }
    }
}

struct HindsightView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            HindsightView()
        }
    }
}