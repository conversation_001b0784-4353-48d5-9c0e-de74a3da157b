import Foundation

// MARK: - API Response Parsing Extensions
extension HindsightData {
    /// 从API响应字典创建HindsightData对象
    static func fromAPIResponse(_ response: [String: Any]) -> HindsightData? {
        // 提取基本统计数据 - 使用更灵活的方式获取totalBrews
        let totalBrews: Int? = {
            if let value = response["total_brews"] as? Int {
                return value
            }
            if let value = response["total_records"] as? Int {
                return value
            }
            // 不再阻止创建对象，而是返回nil值
            return nil
        }()

        // 单独提取 totalRecords 字段
        let totalRecords = response["total_records"] as? Int

        #if DEBUG
        // 打印主要字段的值，帮助调试
        print("从API响应解析 - 原始数据:")
        print("total_brews: \(response["total_brews"] ?? "nil")")
        print("total_records: \(response["total_records"] ?? "nil")")
        print("total_dose: \(response["total_dose"] ?? "nil")")
        print("tasted_beans_count: \(response["tasted_beans_count"] ?? "nil")")
        print("解析后的totalBrews: \(String(describing: totalBrews))")
        #endif

        let activeDays = response["active_days"] as? Int
        let totalDays = response["total_days"] as? Int
        let averageBrewsPerDay = response["average_brews_per_day"] as? Double ?? 0.0
        let averageRating = response["average_rating"] as? Double ?? 0.0
        let activeRate = response["active_rate"] as? Double
        
        // 提取时间范围
        let timeRangeString = response["time_range"] as? String
        let timeRange: TimeRange? = {
            if let rangeString = timeRangeString {
                return TimeRange(rawValue: rangeString)
            }
            return nil
        }()

        // 提取设备统计数据
        var equipmentStats: [EquipmentStatistics] = []
        if let equipmentStatsArray = response["equipment_stats"] as? [[String: Any]] {
            for stat in equipmentStatsArray {
                if let name = stat["name"] as? String,
                   let count = stat["count"] as? Int,
                   let percentage = stat["percentage"] as? Double {
                    equipmentStats.append(EquipmentStatistics(
                        name: name,
                        count: count,
                        percentage: percentage
                    ))
                }
            }
        }

        // 提取咖啡豆统计数据
        var beanStats: [BeanStatistics] = []
        if let beanStatsArray = response["bean_stats"] as? [[String: Any]] {
            for stat in beanStatsArray {
                if let name = stat["name"] as? String,
                   let count = stat["count"] as? Int,
                   let percentage = stat["percentage"] as? Double {
                    beanStats.append(BeanStatistics(
                        name: name,
                        count: count,
                        percentage: percentage
                    ))
                }
            }
        }

        // 提取月度冲煮数据
        var monthlyBrews: [MonthlyBrewsData] = []
        if let monthlyBrewsArray = response["monthly_brews"] as? [[String: Any]] {
            for brew in monthlyBrewsArray {
                if let month = brew["month"] as? String,
                   let count = brew["count"] as? Int {
                    monthlyBrews.append(MonthlyBrewsData(
                        month: month,
                        count: count
                    ))
                }
            }
        }

        // 提取评分分布数据
        var ratingDistribution: [RatingDistributionData] = []
        if let ratingDistributionArray = response["rating_distribution"] as? [[String: Any]] {
            for rating in ratingDistributionArray {
                if let ratingValue = rating["rating"] as? Int,
                   let count = rating["count"] as? Int {
                    ratingDistribution.append(RatingDistributionData(
                        rating: ratingValue,
                        count: count
                    ))
                }
            }
        }

        // 咖啡豆消耗力字段
        let totalDose = response["total_dose"] as? Double
        let avgDose = response["avg_dose"] as? Double
        let days250gLasts = response["days_250g_lasts"] as? Double
        let tastedBeansCount = response["tasted_beans_count"] as? Int
        let totalBrewingCost = response["total_brewing_cost"] as? Double
        let avgCostPerCup = response["avg_cost_per_cup"] as? Double
        let brewingCostRecordsCount = response["brewing_cost_records_count"] as? Int

        // 冲煮习惯字段
        let mostUsedMethod = response["most_used_method"] as? String
        let mostUsedBrewer = response["most_used_brewer"] as? String
        let mostUsedBrewerBrand = response["most_used_brewer_brand"] as? String
        let mostUsedGrinder = response["most_used_grinder"] as? String
        let mostUsedGrinderBrand = response["most_used_grinder_brand"] as? String

        // 饮用习惯字段
        let peakPeriod = response["peak_period"] as? String
        let peakWeekday = response["peak_weekday"] as? String
        let peakTime = response["peak_time"] as? String
        let brewsPerActiveDay = response["brews_per_active_day"] as? Double

        // 消费习惯字段
        let mostRepurchasedBean = response["most_repurchased_bean"] as? String
        let mostRepurchasedBeanRoaster = response["most_repurchased_bean_roaster"] as? String
        let mostVisitedRoaster = response["most_visited_roaster"] as? String
        let mostVisitedRoasterAmount = response["most_visited_roaster_amount"] as? Double
        let totalBeanCosts = response["total_bean_costs"] as? Double
        let equipmentCosts = response["equipment_costs"] as? Double

        // 口味偏好字段
        let favoriteBeanName = response["favorite_bean_name"] as? String
        let favoriteBeanRoaster = response["favorite_bean_roaster"] as? String
        let favoriteBeanRating = response["favorite_bean_rating"] as? Double
        let favoriteBeanCount = response["favorite_bean_count"] as? Int
        let mostUsedBeanName = response["most_used_bean_name"] as? String
        let mostUsedBeanRoaster = response["most_used_bean_roaster"] as? String
        let mostUsedBeanCount = response["most_used_bean_count"] as? Int
        let mostUsedBeanRating = response["most_used_bean_rating"] as? Double
        let mostCommonOrigin = response["most_common_origin"] as? String
        let mostCommonVariety = response["most_common_variety"] as? String

        // 提取风味标签数组
        let mostCommonFlavors = response["most_common_flavors"] as? [String]

        let favoriteRecipeName = response["favorite_recipe_name"] as? String
        let favoriteRecipeRating = response["favorite_recipe_rating"] as? Double
        let favoriteRecipeCount = response["favorite_recipe_count"] as? Int
        let mostUsedRecipeName = response["most_used_recipe_name"] as? String
        let mostUsedRecipeCount = response["most_used_recipe_count"] as? Int
        let mostUsedRecipeRating = response["most_used_recipe_rating"] as? Double

        // 其他统计字段
        let maxStreak = response["max_streak"] as? Int
        let totalAllTimeRecords = response["total_all_time_records"] as? Int
        let daysSinceRegistration = response["days_since_registration"] as? Int

        // 创建并返回HindsightData对象
        return HindsightData(
            totalBrews: totalBrews,
            totalRecords: totalRecords,  // 添加 totalRecords 参数
            activeDays: activeDays,
            totalDays: totalDays,
            averageBrewsPerDay: averageBrewsPerDay,
            averageRating: averageRating,
            equipmentStats: equipmentStats,
            beanStats: beanStats,
            monthlyBrews: monthlyBrews,
            ratingDistribution: ratingDistribution,
            activeRate: activeRate,
            timeRange: timeRange,

            // 咖啡豆消耗力字段
            totalDose: totalDose,
            avgDose: avgDose,
            days250gLasts: days250gLasts,
            tastedBeansCount: tastedBeansCount,
            totalBrewingCost: totalBrewingCost,
            avgCostPerCup: avgCostPerCup,
            brewingCostRecordsCount: brewingCostRecordsCount,

            // 冲煮习惯字段
            mostUsedMethod: mostUsedMethod,
            mostUsedBrewer: mostUsedBrewer,
            mostUsedBrewerBrand: mostUsedBrewerBrand,
            mostUsedGrinder: mostUsedGrinder,
            mostUsedGrinderBrand: mostUsedGrinderBrand,

            // 饮用习惯字段
            peakPeriod: peakPeriod,
            peakWeekday: peakWeekday,
            peakTime: peakTime,
            brewsPerActiveDay: brewsPerActiveDay,

            // 消费习惯字段
            mostRepurchasedBean: mostRepurchasedBean,
            mostRepurchasedBeanRoaster: mostRepurchasedBeanRoaster,
            mostVisitedRoaster: mostVisitedRoaster,
            mostVisitedRoasterAmount: mostVisitedRoasterAmount,
            totalBeanCosts: totalBeanCosts,
            equipmentCosts: equipmentCosts,

            // 口味偏好字段
            favoriteBeanName: favoriteBeanName,
            favoriteBeanRoaster: favoriteBeanRoaster,
            favoriteBeanRating: favoriteBeanRating,
            favoriteBeanCount: favoriteBeanCount,
            mostUsedBeanName: mostUsedBeanName,
            mostUsedBeanRoaster: mostUsedBeanRoaster,
            mostUsedBeanCount: mostUsedBeanCount,
            mostUsedBeanRating: mostUsedBeanRating,
            mostCommonOrigin: mostCommonOrigin,
            mostCommonVariety: mostCommonVariety,
            mostCommonFlavors: mostCommonFlavors,
            favoriteRecipeName: favoriteRecipeName,
            favoriteRecipeRating: favoriteRecipeRating,
            favoriteRecipeCount: favoriteRecipeCount,
            mostUsedRecipeName: mostUsedRecipeName,
            mostUsedRecipeCount: mostUsedRecipeCount,
            mostUsedRecipeRating: mostUsedRecipeRating,

            // 其他统计字段
            maxStreak: maxStreak,
            totalAllTimeRecords: totalAllTimeRecords,
            daysSinceRegistration: daysSinceRegistration
        )
    }
}
