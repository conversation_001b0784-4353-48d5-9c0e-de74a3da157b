import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

struct LoginView: View {
    @EnvironmentObject var authService: AuthService
    @Environment(\.dismiss) private var dismiss
    @State private var username: String = ""
    @State private var password: String = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showTryAgainButton = false
    @State private var showRegisterView = false
    @State private var showForgotPasswordView = false

    var onLoginSuccess: (() -> Void)?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                Form {
                    Section {
                        TextField("用户名", text: $username)
                            .disableAutocorrection(true)
                            .autocapitalization(.none)
                        
                        SecureField("密码", text: $password)
                    }
                    
                    if let errorMessage = errorMessage {
                        Section {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.red)
                                    
                                    Text("登录失败")
                                        .foregroundColor(.red)
                                        .font(.headline)
                                }
                                
                                Text(errorMessage)
                                    .foregroundColor(.red)
                                    .fixedSize(horizontal: false, vertical: true)
                                
                                if showTryAgainButton {
                                    Button(action: login) {
                                        Text("重试")
                                            .frame(maxWidth: .infinity, alignment: .center)
                                    }
                                    .padding(.top, 8)
                                }
                            }
                            .padding(.vertical, 4)
                        }
                    }
                    
                    Section {
                        Button(action: login) {
                            HStack {
                                if isLoading {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .frame(width: 16, height: 16)
                                } else {
                                    Image(systemName: "lock.open")
                                }
                                Text("登录")
                            }
                            .frame(maxWidth: .infinity, alignment: .center)
                        }
                        .disabled(isLoading || username.isEmpty || password.isEmpty)
                    }

                    // 注册和找回密码
                    Section {
                        VStack(spacing: 16) {
                            Button(action: {
                                showRegisterView = true
                            }) {
                                HStack {
                                    Image(systemName: "person.badge.plus")
                                    Text("还没有账号？立即注册")
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(8)
                            }
                            .buttonStyle(PlainButtonStyle())

                            Button(action: {
                                showForgotPasswordView = true
                            }) {
                                HStack {
                                    Image(systemName: "key.horizontal")
                                        .foregroundColor(.orange)
                                    Text("忘记密码？")
                                        .foregroundColor(.orange)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(Color.orange.opacity(0.1))
                                .cornerRadius(8)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        .padding(.vertical, 8)
                    }
                }
                .listStyle(InsetGroupedListStyle())
                .scrollContentBackground(.hidden)
                .disabled(isLoading)
                .navigationTitle("登录")
                .navigationBarTitleDisplayMode(.inline)
            }
            .background(Color(UIColor.systemGroupedBackground))
        }
        .sheet(isPresented: $showRegisterView) {
            RegisterView(onRegisterSuccess: onLoginSuccess)
                .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $showForgotPasswordView) {
            ForgotPasswordView()
                .presentationDragIndicator(.visible)
        }
        .overlay {
            if isLoading {
                ProgressView("正在登录...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.3))
            }
        }
    }
    
    private func login() {
        isLoading = true
        errorMessage = nil
        showTryAgainButton = false
        
        Task {
            do {
                try await authService.login(username: username, password: password)
                await MainActor.run {
                    isLoading = false
                    onLoginSuccess?()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "\(error.localizedDescription)"
                    showTryAgainButton = true
                }
            }
        }
    }
} 
