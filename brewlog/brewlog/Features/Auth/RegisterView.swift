import SwiftUI

struct RegisterView: View {
    @Environment(\.dismiss) private var dismiss

    @State private var username: String = ""
    @State private var email: String = ""
    @State private var firstName: String = ""
    @State private var password: String = ""
    @State private var confirmPassword: String = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var successMessage: String?
    @State private var showPasswordGenerator = false
    @State private var showUsernameGenerator = false
    @State private var showPasswordVisibility = false
    @State private var generatedPasswords: [String] = []
    @State private var generatedUsernames: [String] = []
    @State private var showOnboarding = false
    @State private var needsEmailVerification = false
    @State private var usernameCheckMessage: String?
    @State private var isCheckingUsername = false

    var onRegisterSuccess: (() -> Void)?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                Form {
                    // 基本信息
                    Section {
                        // 用户名输入
                        HStack {
                            TextField("用户名", text: $username)
                                .disableAutocorrection(true)
                                .autocapitalization(.none)
                                .onChange(of: username) { _ in
                                    clearError()
                                    checkUsernameAvailability()
                                }

                            if isCheckingUsername {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .frame(width: 16, height: 16)
                            }

                            Button(action: {
                                showUsernameGenerator.toggle()
                            }) {
                                Image(systemName: "wand.and.rays")
                            }
                        }

                        // 用户名检测结果
                        if let usernameCheckMessage = usernameCheckMessage {
                            HStack {
                                Image(systemName: usernameCheckMessage.contains("可用") ? "checkmark.circle.fill" : "xmark.circle.fill")
                                    .foregroundColor(usernameCheckMessage.contains("可用") ? .green : .red)

                                Text(usernameCheckMessage)
                                    .font(.caption)
                                    .foregroundColor(usernameCheckMessage.contains("可用") ? .green : .red)
                            }
                        }

                        // 用户名生成器
                        if showUsernameGenerator {
                            usernameGeneratorView
                        }

                        TextField("邮箱", text: $email)
                            .disableAutocorrection(true)
                            .autocapitalization(.none)
                            .keyboardType(.emailAddress)
                            .onChange(of: email) { _ in
                                clearError()
                            }

                        TextField("昵称 (选填)", text: $firstName)
                            .onChange(of: firstName) { _ in
                                clearError()
                            }
                    } header: {
                        Text("基本信息")
                    } footer: {
                        Text("用户名至少5个字符，只能包含字母和数字，必须以字母开头")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    .listRowBackground(Color(UIColor.secondarySystemBackground))
                    
                    // 密码设置
                    Section {
                        // 密码控制栏
                        HStack {
                            Text("密码可见性")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Spacer()

                            Button(action: {
                                showPasswordVisibility.toggle()
                            }) {
                                HStack {
                                    Image(systemName: showPasswordVisibility ? "eye.slash" : "eye")
                                    Text(showPasswordVisibility ? "隐藏" : "显示")
                                }
                                .font(.caption)
                                .foregroundColor(.gray)
                            }

                            Button(action: {
                                showPasswordGenerator.toggle()
                            }) {
                                HStack {
                                    Image(systemName: "key.fill")
                                    Text("生成密码")
                                }
                                .font(.caption)
                            }
                        }
                        .padding(.vertical, 4)

                        // 密码输入
                        if showPasswordVisibility {
                            TextField("密码", text: $password)
                                .onChange(of: password) { _ in
                                    clearError()
                                }
                        } else {
                            SecureField("密码", text: $password)
                                .onChange(of: password) { _ in
                                    clearError()
                                }
                        }

                        // 确认密码输入
                        if showPasswordVisibility {
                            TextField("确认密码", text: $confirmPassword)
                                .onChange(of: confirmPassword) { _ in
                                    clearError()
                                }
                        } else {
                            SecureField("确认密码", text: $confirmPassword)
                                .onChange(of: confirmPassword) { _ in
                                    clearError()
                                }
                        }

                        // 密码生成器
                        if showPasswordGenerator {
                            passwordGeneratorView
                        }
                    } header: {
                        Text("密码设置")
                    } footer: {
                        Text("密码至少8位，需包含字母和数字")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    .listRowBackground(Color(UIColor.secondarySystemBackground))
                    
                    // 成功信息
                    if let successMessage = successMessage {
                        Section {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)

                                    Text("注册成功")
                                        .foregroundColor(.green)
                                        .font(.headline)
                                }

                                Text(successMessage)
                                    .foregroundColor(.primary)
                                    .fixedSize(horizontal: false, vertical: true)

                                if needsEmailVerification {
                                    Text("请检查您的邮箱并点击验证链接以激活账户。")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                        .fixedSize(horizontal: false, vertical: true)
                                }
                            }
                            .padding(.vertical, 4)
                        }
                        .listRowBackground(Color(UIColor.secondarySystemBackground))
                    }

                    // 错误信息
                    if let errorMessage = errorMessage {
                        Section {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.red)
                                    
                                    Text("注册失败")
                                        .foregroundColor(.red)
                                        .font(.headline)
                                }
                                
                                Text(errorMessage)
                                    .foregroundColor(.red)
                                    .fixedSize(horizontal: false, vertical: true)
                            }
                            .padding(.vertical, 4)
                        }
                        .listRowBackground(Color(UIColor.secondarySystemBackground))
                    }
                    
                    // 注册按钮
                    Section {
                        Button(action: register) {
                            HStack {
                                if isLoading {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .frame(width: 16, height: 16)
                                } else {
                                    Image(systemName: "person.badge.plus")
                                }
                                Text("注册")
                            }
                            .frame(maxWidth: .infinity, alignment: .center)
                        }
                        .disabled(isLoading || !isFormValid || successMessage != nil)
                    }
                    .listRowBackground(Color(UIColor.secondarySystemBackground))

                    // 开始使用按钮（注册成功后显示）
                    if successMessage != nil {
                        Section {
                            Button(action: {
                                showOnboarding = true
                            }) {
                                HStack {
                                    Image(systemName: "arrow.right.circle.fill")
                                        .foregroundColor(.green)
                                    Text("开始使用")
                                        .foregroundColor(.green)
                                }
                                .frame(maxWidth: .infinity, alignment: .center)
                            }
                        }
                        .listRowBackground(Color(UIColor.secondarySystemBackground))
                    }
                }
                .listStyle(InsetGroupedListStyle())
                .scrollContentBackground(.hidden)
                .disabled(isLoading)
                .navigationTitle("注册")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("取消") {
                            dismiss()
                        }
                    }
                }
            }
            .background(Color(UIColor.systemGroupedBackground))
        }
        .presentationDragIndicator(.visible)
        .overlay {
            if isLoading {
                ProgressView("处理中...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.3))
            }
        }
        .sheet(isPresented: $showOnboarding) {
            // OnboardingView() // 暂时注释，等待OnboardingView可用
            Text("欢迎使用！新手引导即将推出")
                .padding()
                .presentationDragIndicator(.visible)
        }
    }
    
    // MARK: - 计算属性
    
    private var isFormValid: Bool {
        !username.isEmpty &&
        username.count >= 5 &&
        isValidUsername(username) &&
        !email.isEmpty &&
        !password.isEmpty &&
        !confirmPassword.isEmpty &&
        password == confirmPassword &&
        isValidEmail(email) &&
        password.count >= 8
    }
    
    // MARK: - 子视图

    private var usernameGeneratorView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("推荐用户名")
                    .font(.caption)
                    .foregroundColor(.primary)

                Spacer()

                Button("生成新用户名") {
                    generateNewUsernames()
                }
                .font(.caption)
            }

            ForEach(Array(generatedUsernames.enumerated()), id: \.offset) { index, generatedUsername in
                usernameOptionView(username: generatedUsername, index: index)
            }

            Text("建议使用咖啡相关的用户名，如：v60berry、ethwash、brew23")
                .font(.caption)
                .foregroundColor(.gray)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.vertical, 8)
        .onAppear {
            if generatedUsernames.isEmpty {
                generateNewUsernames()
            }
        }
    }

    private var passwordGeneratorView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("推荐密码")
                    .font(.caption)
                    .foregroundColor(.primary)

                Spacer()

                Button("生成新密码") {
                    generateNewPasswords()
                }
                .font(.caption)
            }

            ForEach(Array(generatedPasswords.enumerated()), id: \.offset) { index, generatedPassword in
                passwordOptionView(password: generatedPassword, index: index)
            }

            Text("⚠️ 请妥善保管密码，即使忘记密码也可通过邮箱找回")
                .font(.caption)
                .foregroundColor(.orange)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.vertical, 8)
        .onAppear {
            if generatedPasswords.isEmpty {
                generateNewPasswords()
            }
        }
    }
    
    private func usernameOptionView(username: String, index: Int) -> some View {
        HStack {
            Button(action: {
                selectUsername(username)
            }) {
                HStack {
                    Text(username)
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(.primary)

                    Spacer()
                }
            }
            .buttonStyle(PlainButtonStyle())

            Button(action: {
                copyToClipboard(username)
            }) {
                Image(systemName: "doc.on.doc")
            }
        }
        .padding(.vertical, 4)
    }

    private func passwordOptionView(password: String, index: Int) -> some View {
        HStack {
            Button(action: {
                selectPassword(password)
            }) {
                HStack {
                    Text(password)
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(.primary)

                    Spacer()
                }
            }
            .buttonStyle(PlainButtonStyle())

            Button(action: {
                copyPasswordToClipboard(password)
            }) {
                Image(systemName: "doc.on.doc")
            }
        }
        .padding(.vertical, 4)
    }
    
    // MARK: - 方法
    
    private func register() {
        guard isFormValid else {
            errorMessage = "请检查输入信息"
            return
        }

        isLoading = true
        errorMessage = nil

        // 模拟注册过程
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            isLoading = false
            successMessage = "注册成功！欢迎加入咖啡爱好者社区"
            needsEmailVerification = true

            // 注册成功后不立即关闭，等待用户点击"开始使用"
        }

        // 实际的API调用（暂时注释）
        /*
        Task {
            do {
                // 调用APIService的注册方法
                let response = try await APIService.shared.register(
                    username: username,
                    email: email,
                    password: password,
                    firstName: firstName.isEmpty ? nil : firstName
                )

                await MainActor.run {
                    isLoading = false
                    successMessage = "注册成功！欢迎加入咖啡爱好者社区"
                    needsEmailVerification = true

                    // 注册成功后不立即关闭，等待用户点击"开始使用"
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "注册失败: \(error.localizedDescription)"
                }
            }
        }
        */
    }
    
    private func clearError() {
        errorMessage = nil
        successMessage = nil
    }

    private func checkUsernameAvailability() {
        guard !username.isEmpty && username.count >= 5 && isValidUsername(username) else {
            usernameCheckMessage = nil
            return
        }

        isCheckingUsername = true
        usernameCheckMessage = nil

        // 模拟API调用检测用户名重复
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            isCheckingUsername = false

            // 模拟检测结果（实际应该调用API）
            let unavailableUsernames = ["admin", "test", "user", "coffee", "brew", "admin123", "test123"]

            if unavailableUsernames.contains(username.lowercased()) {
                usernameCheckMessage = "用户名已被占用"
            } else {
                usernameCheckMessage = "用户名可用"
            }
        }
    }

    private func generateNewUsernames() {
        // 简化的咖啡相关用户名生成
        let coffeeTerms = ["brew", "roast", "grind", "pour", "drip", "shot", "crema", "bloom", "v60", "aero", "chemex", "kalita", "berry", "citrus", "choco", "caramel", "floral", "nutty", "fruity", "sweet"]
        let numbers = ["01", "02", "03", "07", "09", "12", "17", "19", "23", "27", "42", "88", "99", "2024", "2025"]

        var usernames: [String] = []

        for _ in 0..<3 {
            let term1 = coffeeTerms.randomElement()!
            let term2 = coffeeTerms.randomElement()!
            let number = numbers.randomElement()!

            // 生成不同的组合模式
            let patterns = [
                term1 + term2,
                term1 + number,
                term2 + number,
                term1 + term2 + number
            ]

            if let username = patterns.randomElement(), username.count >= 5 {
                usernames.append(username)
            }
        }

        generatedUsernames = usernames
    }

    private func generateNewPasswords() {
        // 复杂密码生成
        generatedPasswords = generateComplexPasswords(count: 3)
    }

    private func generateComplexPasswords(count: Int) -> [String] {
        var passwords: [String] = []

        let uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        let numbers = "0123456789"
        let symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"

        let coffeeWords = ["Coffee", "Brew", "Espresso", "Latte", "Mocha", "Cappuccino", "Americano", "Macchiato", "Cortado", "Flat", "Pour", "Drip", "French", "Aero", "Chemex", "V60", "Kalita", "Hario", "Bean", "Roast", "Grind", "Shot", "Crema", "Bloom", "Extract"]

        for _ in 0..<count {
            var password = ""

            // 随机选择一个咖啡词汇作为基础
            let baseWord = coffeeWords.randomElement()!
            password += baseWord

            // 添加随机数字 (2-3位)
            let numberCount = Int.random(in: 2...3)
            for _ in 0..<numberCount {
                password += String(numbers.randomElement()!)
            }

            // 添加随机符号 (1-2个)
            let symbolCount = Int.random(in: 1...2)
            for _ in 0..<symbolCount {
                password += String(symbols.randomElement()!)
            }

            // 随机插入大写字母
            if Bool.random() {
                let randomIndex = password.index(password.startIndex, offsetBy: Int.random(in: 1..<password.count))
                let randomUppercase = String(uppercase.randomElement()!)
                password.insert(contentsOf: randomUppercase, at: randomIndex)
            }

            // 确保密码长度在8-16之间
            if password.count >= 8 && password.count <= 16 {
                passwords.append(password)
            }
        }

        // 如果生成的密码不够，用备用密码填充
        while passwords.count < count {
            let backupPasswords = [
                "Coffee123!@",
                "Brew2024#$",
                "Espresso789&",
                "Latte456*+",
                "Mocha321!?"
            ]
            if let backup = backupPasswords.randomElement() {
                passwords.append(backup)
            }
        }

        return Array(passwords.prefix(count))
    }

    private func selectUsername(_ username: String) {
        self.username = username
    }

    private func selectPassword(_ password: String) {
        self.password = password
        self.confirmPassword = password
    }

    private func copyToClipboard(_ text: String) {
        #if canImport(UIKit)
        UIPasteboard.general.string = text
        #endif
    }

    private func copyPasswordToClipboard(_ password: String) {
        #if canImport(UIKit)
        UIPasteboard.general.string = password
        #endif
        // 这里可以添加复制成功的提示
        print("密码已复制到剪贴板: \(password)")
    }

    private func isValidUsername(_ username: String) -> Bool {
        // 检查长度（至少5个字符）
        guard username.count >= 5 else { return false }

        // 检查字符（只允许字母和数字）
        let allowedCharacters = CharacterSet.alphanumerics
        let usernameCharacterSet = CharacterSet(charactersIn: username)
        guard allowedCharacters.isSuperset(of: usernameCharacterSet) else { return false }

        // 检查是否以字母开头
        guard let firstChar = username.first, firstChar.isLetter else { return false }

        return true
    }

    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}


