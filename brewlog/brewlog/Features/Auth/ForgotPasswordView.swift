import SwiftUI

struct ForgotPasswordView: View {
    @Environment(\.dismiss) private var dismiss
    
    @State private var email: String = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var successMessage: String?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                Form {
                    // 说明信息
                    Section {
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Image(systemName: "envelope.circle.fill")
                                    .foregroundColor(.blue)
                                    .font(.title2)
                                
                                Text("找回密码")
                                    .font(.headline)
                                    .foregroundColor(.primary)
                            }
                            
                            Text("请输入您的注册邮箱，我们将向您发送密码重置链接。")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .padding(.vertical, 8)
                    }
                    .listRowBackground(Color(UIColor.secondarySystemBackground))
                    
                    // 邮箱输入
                    Section {
                        TextField("邮箱地址", text: $email)
                            .disableAutocorrection(true)
                            .autocapitalization(.none)
                            .keyboardType(.emailAddress)
                            .onChange(of: email) { _ in
                                clearMessages()
                            }
                    } header: {
                        Text("邮箱地址")
                    } footer: {
                        Text("请输入您注册时使用的邮箱地址")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    .listRowBackground(Color(UIColor.secondarySystemBackground))
                    
                    // 成功信息
                    if let successMessage = successMessage {
                        Section {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                    
                                    Text("发送成功")
                                        .foregroundColor(.green)
                                        .font(.headline)
                                }
                                
                                Text(successMessage)
                                    .foregroundColor(.primary)
                                    .fixedSize(horizontal: false, vertical: true)
                                
                                Text("请检查您的邮箱（包括垃圾邮件文件夹），点击邮件中的链接重置密码。")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                    .fixedSize(horizontal: false, vertical: true)
                            }
                            .padding(.vertical, 4)
                        }
                        .listRowBackground(Color(UIColor.secondarySystemBackground))
                    }
                    
                    // 错误信息
                    if let errorMessage = errorMessage {
                        Section {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.red)
                                    
                                    Text("发送失败")
                                        .foregroundColor(.red)
                                        .font(.headline)
                                }
                                
                                Text(errorMessage)
                                    .foregroundColor(.red)
                                    .fixedSize(horizontal: false, vertical: true)
                                
                                Button(action: sendResetEmail) {
                                    Text("重试")
                                        .frame(maxWidth: .infinity, alignment: .center)
                                }
                                .padding(.top, 8)
                            }
                            .padding(.vertical, 4)
                        }
                        .listRowBackground(Color(UIColor.secondarySystemBackground))
                    }
                    
                    // 发送按钮
                    Section {
                        Button(action: sendResetEmail) {
                            HStack {
                                if isLoading {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .frame(width: 16, height: 16)
                                } else {
                                    Image(systemName: "paperplane.fill")
                                }
                                Text("发送重置邮件")
                            }
                            .frame(maxWidth: .infinity, alignment: .center)
                        }
                        .disabled(isLoading || !isValidEmail || successMessage != nil)
                    }
                    .listRowBackground(Color(UIColor.secondarySystemBackground))
                    
                    // 返回登录
                    if successMessage != nil {
                        Section {
                            Button(action: {
                                dismiss()
                            }) {
                                HStack {
                                    Image(systemName: "arrow.left.circle")
                                    Text("返回登录")
                                }
                                .frame(maxWidth: .infinity, alignment: .center)
                            }
                        }
                        .listRowBackground(Color(UIColor.secondarySystemBackground))
                    }
                }
                .listStyle(InsetGroupedListStyle())
                .scrollContentBackground(.hidden)
                .disabled(isLoading)
                .navigationTitle("找回密码")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("取消") {
                            dismiss()
                        }
                    }
                }
            }
            .background(Color(UIColor.systemGroupedBackground))
        }
        .presentationDragIndicator(.visible)
        .overlay {
            if isLoading {
                ProgressView("处理中...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.3))
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var isValidEmail: Bool {
        !email.isEmpty && isValidEmailFormat(email)
    }
    
    // MARK: - 方法
    
    private func sendResetEmail() {
        guard isValidEmail else {
            errorMessage = "请输入有效的邮箱地址"
            return
        }

        isLoading = true
        clearMessages()

        Task {
            do {
                // 调用APIService的密码重置方法
                let response = try await APIService.shared.resetPassword(email: email)

                await MainActor.run {
                    isLoading = false
                    successMessage = response.message
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "发送失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }
    
    private func isValidEmailFormat(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

#Preview {
    ForgotPasswordView()
}
