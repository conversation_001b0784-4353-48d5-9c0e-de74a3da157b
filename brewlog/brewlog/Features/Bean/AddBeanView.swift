import SwiftUI
import UIKit
// 如果需要，取消注释下面的导入语句
// import Foundation

// 表单草稿结构体，需要遵循Codable协议以便JSON序列化
struct BeanFormDraft: Codable {
    // 基本信息
    var name: String = ""
    var type: String = "SINGLE"
    var roaster: String = ""
    var notes: String = ""
    var isDecaf: Bool = false
    var flavorTags: [String] = []
    
    // 包装信息
    var barcode: String = ""
    var bagWeight: Double?
    var bagRemain: Double?
    var purchasePrice: Double?
    var createdAt: Date = Date()
    var roastDateEnabled: Bool = false
    var roastDate: Date = Date()
    var restPeriodType: String = "SINGLE"
    var restPeriodMin: Int?
    var restPeriodMax: Int?
    
    // 详细属性
    var origin: String = ""
    var region: String = ""
    var finca: String = ""
    var variety: String = ""
    var process: String = ""
    var roastLevel: Int = 4
    var altitudeType: String = "SINGLE"
    var altitudeSingle: Int?
    var altitudeMin: Int?
    var altitudeMax: Int?
    
    // 拼配豆组件 - 由于复杂性，单独处理
    // blendComponents不能直接包含在这里，因为MutableBlendComponent不是Codable
    
    // 拼配组件JSON - 单独存储拼配组件数据
    var blendComponentsJSON: String?
}

struct AddBeanView: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel = BrewLogViewModel()
    
    // 添加焦点状态管理
    @FocusState private var focusedField: String?
    
    // Tab选择状态
    @State private var selectedTab: Int = 0 // 0: 基本信息, 1: 包装信息, 2: 详细属性
    
    // 基本信息
    @State private var name: String = ""
    @State private var type: String = "SINGLE"
    @State private var roaster: String = ""
    @State private var notes: String = ""
    @State private var isDecaf: Bool = false
    @State private var flavorTags: [String] = []
    
    // 包装信息
    @State private var barcode: String = ""
    @State private var bagWeight: Double?
    @State private var bagRemain: Double?
    @State private var purchasePrice: Double?
    @State private var createdAt: Date = Date()
    @State private var roastDateEnabled: Bool = false
    @State private var roastDate: Date = Date()
    @State private var restPeriodType: String = "SINGLE"
    @State private var restPeriodMin: Int?
    @State private var restPeriodMax: Int?
    
    // 详细属性
    @State private var origin: String = ""
    @State private var region: String = ""
    @State private var finca: String = ""
    @State private var variety: String = ""
    @State private var process: String = ""
    @State private var roastLevel: Int = 4
    @State private var altitudeType: String = "SINGLE"
    @State private var altitudeSingle: Int?
    @State private var altitudeMin: Int?
    @State private var altitudeMax: Int?
    
    // 拼配豆组件
    @State private var blendComponents: [MutableBlendComponent] = [MutableBlendComponent(from: BlendComponent.defaultComponent())]
    
    // UI状态控制
    @State private var errorMessage: String?
    @State private var isLoading = false
    @State private var showingFlavorTagsPicker = false
    @State private var hasDraft: Bool = false
    @State private var showingValidationAlert = false
    @State private var validationErrors: [String] = []
    @State private var draftNoticeShown = false
    
    // 计算属性
    private var shouldShowDetails: Bool {
        return type != "SKIP"
    }
    
    private var isValid: Bool {
        !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !roaster.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // 验证表单数据
    private func validateForm() -> Bool {
        validationErrors = []
        
        // 基本必填字段验证
        if name.isEmpty {
            validationErrors.append("咖啡豆名称不能为空")
        } else if name.count > 50 {
            validationErrors.append("咖啡豆名称不能超过50个字符")
        }
        
        if roaster.isEmpty {
            validationErrors.append("豆商不能为空")
        } else if roaster.count > 50 {
            validationErrors.append("豆商名称不能超过50个字符")
        }
        
        // 包装规格和库存余量验证
        if let weight = bagWeight, weight < 0 {
            validationErrors.append("包装规格不能为负数")
        }
        
        if let remain = bagRemain, remain < 0 {
            validationErrors.append("库存余量不能为负数")
        }
        
        // 价格验证
        if let price = purchasePrice, price < 0 {
            validationErrors.append("购买价格不能为负数")
        }
        
        // 养豆期验证
        if roastDateEnabled {
            if let minRest = restPeriodMin {
                if minRest < 1 || minRest > 60 {
                    validationErrors.append("养豆期必须在1-60天范围内")
                }
            }
            
            if restPeriodType == "RANGE" {
                if let maxRest = restPeriodMax {
                    if maxRest < 1 || maxRest > 60 {
                        validationErrors.append("最长养豆期必须在1-60天范围内")
                    }
                    
                    if let minRest = restPeriodMin, maxRest < minRest {
                        validationErrors.append("最长养豆期不能小于最短养豆期")
                    }
                }
            }
        }
        
        // 海拔验证
        if type != "SKIP" {
            if altitudeType == "SINGLE" {
                if let altitude = altitudeSingle, (altitude < 0 || altitude > 9999) {
                    validationErrors.append("海拔必须在0-9999米范围内")
                }
            } else {
                if let minAlt = altitudeMin, (minAlt < 0 || minAlt > 9999) {
                    validationErrors.append("最低海拔必须在0-9999米范围内")
                }
                
                if let maxAlt = altitudeMax, (maxAlt < 0 || maxAlt > 9999) {
                    validationErrors.append("最高海拔必须在0-9999米范围内")
                }
                
                if let minAlt = altitudeMin, let maxAlt = altitudeMax, maxAlt < minAlt {
                    validationErrors.append("最高海拔不能小于最低海拔")
                }
            }
        }
        
        // 拼配豆验证
        if type == "BLEND" {
            // 检查总比例是否接近100%
            let totalRatio = calculateTotalRatio()
            if abs(totalRatio - 100) > 0.02 {
                validationErrors.append("拼配豆比例总和必须等于100%")
            }
            
            // 检查每个组件的海拔设置
            for (index, component) in blendComponents.enumerated() {
                if component.altitudeType == "SINGLE" {
                    if let altitude = component.altitudeSingle, (altitude < 0 || altitude > 9999) {
                        validationErrors.append("拼配豆#\(index + 1)的海拔必须在0-9999米范围内")
                    }
                } else {
                    if let minAlt = component.altitudeMin, (minAlt < 0 || minAlt > 9999) {
                        validationErrors.append("拼配豆#\(index + 1)的最低海拔必须在0-9999米范围内")
                    }
                    
                    if let maxAlt = component.altitudeMax, (maxAlt < 0 || maxAlt > 9999) {
                        validationErrors.append("拼配豆#\(index + 1)的最高海拔必须在0-9999米范围内")
                    }
                    
                    if let minAlt = component.altitudeMin, let maxAlt = component.altitudeMax, maxAlt < minAlt {
                        validationErrors.append("拼配豆#\(index + 1)的最高海拔不能小于最低海拔")
                    }
                }
            }
        }
        
        if !validationErrors.isEmpty {
            showingValidationAlert = true
            return false
        }
        
        return true
    }
    
    // 草稿保存和加载函数
    private func saveDraft() {
        // 创建一个可变副本
        var draftCopy = BeanFormDraft(
            name: name,
            type: type,
            roaster: roaster,
            notes: notes,
            isDecaf: isDecaf,
            flavorTags: flavorTags,
            barcode: barcode,
            bagWeight: bagWeight,
            bagRemain: bagRemain,
            purchasePrice: purchasePrice,
            createdAt: createdAt,
            roastDateEnabled: roastDateEnabled,
            roastDate: roastDate,
            restPeriodType: restPeriodType,
            restPeriodMin: restPeriodMin,
            restPeriodMax: restPeriodMax,
            origin: origin,
            region: region,
            finca: finca,
            variety: variety,
            process: process,
            roastLevel: roastLevel,
            altitudeType: altitudeType,
            altitudeSingle: altitudeSingle,
            altitudeMin: altitudeMin,
            altitudeMax: altitudeMax
        )
        
        // 单独处理拼配组件，转换为JSON
        if type == "BLEND" && !blendComponents.isEmpty {
            let encoder = JSONEncoder()
            if let blendData = try? encoder.encode(blendComponents.map { $0.toStorable() }) {
                draftCopy.blendComponentsJSON = String(data: blendData, encoding: .utf8)
            }
        }
        
        // 将草稿序列化为JSON并保存到UserDefaults
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(draftCopy)
            UserDefaults.standard.set(data, forKey: "BeanFormDraft")
            print("✅ 草稿已保存")
        } catch {
            print("❌ 保存草稿失败: \(error.localizedDescription)")
        }
    }
    
    private func loadDraft() {
        guard let data = UserDefaults.standard.data(forKey: "BeanFormDraft") else {
            print("ℹ️ 没有找到草稿数据")
            return
        }
        
        do {
            let decoder = JSONDecoder()
            let draft = try decoder.decode(BeanFormDraft.self, from: data)
            
            // 检查草稿是否只包含默认值
            if isEmptyDraft(draft) {
                // 如果草稿只包含默认值，则清除草稿并返回
                print("ℹ️ 草稿只包含默认值，不恢复")
                UserDefaults.standard.removeObject(forKey: "BeanFormDraft")
                return
            }
            
            // 恢复所有状态
            self.name = draft.name
            self.type = draft.type
            self.roaster = draft.roaster
            self.notes = draft.notes
            self.isDecaf = draft.isDecaf
            self.flavorTags = draft.flavorTags
            self.barcode = draft.barcode
            self.bagWeight = draft.bagWeight
            self.bagRemain = draft.bagRemain
            self.purchasePrice = draft.purchasePrice
            self.createdAt = draft.createdAt
            self.roastDateEnabled = draft.roastDateEnabled
            self.roastDate = draft.roastDate
            self.restPeriodType = draft.restPeriodType
            self.restPeriodMin = draft.restPeriodMin
            self.restPeriodMax = draft.restPeriodMax
            self.origin = draft.origin
            self.region = draft.region
            self.finca = draft.finca
            self.variety = draft.variety
            self.process = draft.process
            self.roastLevel = draft.roastLevel
            self.altitudeType = draft.altitudeType
            self.altitudeSingle = draft.altitudeSingle
            self.altitudeMin = draft.altitudeMin
            self.altitudeMax = draft.altitudeMax
            
            // 恢复拼配组件
            if type == "BLEND", let blendJSON = draft.blendComponentsJSON, 
               let jsonData = blendJSON.data(using: .utf8) {
                let decoder = JSONDecoder()
                if let storables = try? decoder.decode([StorableBlendComponent].self, from: jsonData) {
                    self.blendComponents = storables.map { $0.toMutableComponent() }
                }
            }
            
            hasDraft = true
            print("✅ 草稿已加载")
        } catch {
            print("❌ 加载草稿失败: \(error.localizedDescription)")
        }
    }
    
    // 检查草稿是否只包含默认值
    private func isEmptyDraft(_ draft: BeanFormDraft) -> Bool {
        // 检查必填字段
        if !draft.name.isEmpty || !draft.roaster.isEmpty {
            return false
        }
        
        // 检查数组不为空
        if !draft.flavorTags.isEmpty {
            return false
        }
        
        // 检查默认值以外的字符串字段
        if draft.type != "SINGLE" ||
           !draft.notes.isEmpty ||
           !draft.barcode.isEmpty ||
           !draft.origin.isEmpty ||
           !draft.region.isEmpty ||
           !draft.finca.isEmpty ||
           !draft.variety.isEmpty ||
           !draft.process.isEmpty {
            return false
        }
        
        // 检查布尔字段
        if draft.isDecaf || draft.roastDateEnabled {
            return false
        }
        
        // 检查数值字段是否设置了值
        if draft.bagWeight != nil ||
           draft.bagRemain != nil ||
           draft.purchasePrice != nil ||
           draft.restPeriodMin != nil ||
           draft.restPeriodMax != nil ||
           draft.altitudeSingle != nil ||
           draft.altitudeMin != nil ||
           draft.altitudeMax != nil {
            return false
        }
        
        // 检查其他默认值
        if draft.roastLevel != 4 ||
           draft.altitudeType != "SINGLE" ||
           draft.restPeriodType != "SINGLE" {
            return false
        }
        
        // 检查拼配组件
        if draft.blendComponentsJSON != nil {
            return false
        }
        
        // 所有检查通过，草稿只包含默认值
        return true
    }
    
    private func clearDraft() {
        // 清除UserDefaults中的草稿
        UserDefaults.standard.removeObject(forKey: "BeanFormDraft")
        hasDraft = false
        
        // 重置所有表单字段为默认值
        // 基本信息
        name = ""
        type = "SINGLE"
        roaster = ""
        notes = ""
        isDecaf = false
        flavorTags = []
        
        // 包装信息
        barcode = ""
        bagWeight = nil
        bagRemain = nil
        purchasePrice = nil
        createdAt = Date()
        roastDateEnabled = false
        roastDate = Date()
        restPeriodType = "SINGLE"
        restPeriodMin = nil
        restPeriodMax = nil
        
        // 详细属性
        origin = ""
        region = ""
        finca = ""
        variety = ""
        process = ""
        roastLevel = 4
        altitudeType = "SINGLE"
        altitudeSingle = nil
        altitudeMin = nil
        altitudeMax = nil
        
        // 拼配豆组件重置为默认状态
        blendComponents = [MutableBlendComponent(from: BlendComponent.defaultComponent())]
        
        // UI状态重置
        selectedTab = 0
        
        print("🗑️ 草稿已清除并重置表单")
    }
    
    // 隐藏键盘方法
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    var body: some View {
        ZStack(alignment: .top) {
            // 固定的顶部导航栏
            topNavigationBar
                .background(Color.primaryBg)
                .zIndex(1)
            
            ScrollViewReader { proxy in
                ScrollView {
                    VStack(spacing: 8) {
                        // 添加一个占位符，高度等于导航栏高度
                        Color.clear
                            .frame(height: 52)
                        
                        // 如果有草稿，显示提示信息（只在视图首次加载时显示）
                        if hasDraft && !draftNoticeShown {
                            HStack(spacing: 8) {
                                Image(systemName: "doc.append")
                                    .foregroundColor(.green)
                                Text("已从上次编辑恢复草稿")
                                    .font(.footnote)
                                    .foregroundColor(.primaryText)
                                Spacer()
                                Button(action: {
                                    clearDraft()
                                    draftNoticeShown = true
                                }) {
                                    Text("清除草稿")
                                        .font(.footnote)
                                        .foregroundColor(.error)
                                }
                            }
                            .padding(10)
                            .background(Color.navbarBg)
                            .cornerRadius(6)
                            .padding(.horizontal, 12)
                            .padding(.bottom, 4)
                            .transition(.opacity)
                            .id("draftNotice-\(draftNoticeShown)")
                            .onAppear {
                                // 5秒后自动隐藏提示
                                DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                                    withAnimation {
                                        draftNoticeShown = true
                                    }
                                }
                            }
                        }
                        
                        // 主要内容区域
                        VStack(spacing: 16) {
                            // 顶部Tab切换
                            tabSelectionView
                            
                            // 内容区域
                            tabContentView
                        }
                        .padding(.horizontal, 12)
                    }
                }
                .background(
                    Color.primaryBg
                )
                .contentShape(Rectangle())
                .onTapGesture {
                    hideKeyboard()
                }
                .onAppear {
                    scrollProxy = proxy
                }
            }
        }
        .alert("错误", isPresented: Binding(
            get: { errorMessage != nil },
            set: { if !$0 { errorMessage = nil } }
        )) {
            Text(errorMessage ?? "")
            Button("确定", role: .cancel) {}
        }
        .alert("表单验证错误", isPresented: $showingValidationAlert) {
            Button("确定", role: .cancel) {
                showingValidationAlert = false
            }
        } message: {
            VStack(alignment: .leading, spacing: 4) {
                ForEach(validationErrors, id: \.self) { error in
                    Text("• " + error)
                }
            }
        }
        .overlay(
            Group {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.4)

                        BlinkingLoader(
                            color: .primaryBg,
                            width: 12,
                            height: 18,
                            duration: 1.5,
                            text: "保存中..."
                        )
                        .padding()
                        .background(Color.secondaryBg.opacity(0.9))
                        .cornerRadius(10)
                        .shadow(radius: 5)
                    }
                }
            }
        )
        .toolbar {
            ToolbarItemGroup(placement: .keyboard) {
                // 风味标签组件已经有自己的工具栏设置
                if focusedField != nil && !focusedField!.contains("flavorTags") {
                    // 显示常用的包装规格数字选项
                    if focusedField == "包装规格 (g)_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("100g") { bagWeight = 100.0; focusedField = nil }
                                Button("200g") { bagWeight = 200.0; focusedField = nil }
                                Button("250g") { bagWeight = 250.0; focusedField = nil }
                                Button("454g") { bagWeight = 454.0; focusedField = nil }
                                Button("500g") { bagWeight = 500.0; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为库存余量也添加常用选项
                    else if focusedField == "库存余量 (g)_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                if let weight = bagWeight {
                                    Button("\(Int(weight))g") { bagRemain = weight; focusedField = nil }
                                }
                                Button("一半") { 
                                    if let weight = bagWeight {
                                        bagRemain = weight / 2
                                    }
                                    focusedField = nil 
                                }
                                Button("1/4") { 
                                    if let weight = bagWeight {
                                        bagRemain = weight / 4
                                    }
                                    focusedField = nil 
                                }
                                Button("0g") { bagRemain = 0; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为价格添加常用选项
                    else if focusedField == "购买价格 (元)_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("30元") { purchasePrice = 30.0; focusedField = nil }
                                Button("40元") { purchasePrice = 40.0; focusedField = nil }
                                Button("50元") { purchasePrice = 50.0; focusedField = nil }
                                Button("60元") { purchasePrice = 60.0; focusedField = nil }
                                Button("99元") { purchasePrice = 99.0; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为养豆期添加常用选项
                    else if focusedField == "养豆天数_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("5天") { restPeriodMin = 5; focusedField = nil }
                                Button("10天") { restPeriodMin = 10; focusedField = nil }
                                Button("14天") { restPeriodMin = 14; focusedField = nil }
                                Button("25天") { restPeriodMin = 25; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    else if focusedField == "最短养豆期_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("5天") { restPeriodMin = 5; focusedField = nil }
                                Button("10天") { restPeriodMin = 10; focusedField = nil }
                                Button("14天") { restPeriodMin = 14; focusedField = nil }
                                Button("25天") { restPeriodMin = 25; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    else if focusedField == "最长养豆期_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("7天") { restPeriodMax = 7; focusedField = nil }
                                Button("14天") { restPeriodMax = 14; focusedField = nil }
                                Button("21天") { restPeriodMax = 21; focusedField = nil }
                                Button("30天") { restPeriodMax = 30; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为海拔信息添加常用选项
                    else if focusedField == "种植海拔(米)_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("1000m") { altitudeSingle = 1000; focusedField = nil }
                                Button("1600m") { altitudeSingle = 1600; focusedField = nil }
                                Button("1800m") { altitudeSingle = 1800; focusedField = nil }
                                Button("2300m") { altitudeSingle = 2300; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    else if focusedField == "最低海拔(米)_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("1000m") { altitudeMin = 1000; focusedField = nil }
                                Button("1500m") { altitudeMin = 1500; focusedField = nil }
                                Button("1600m") { altitudeMin = 1600; focusedField = nil }
                                Button("2100m") { altitudeMin = 2100; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    else if focusedField == "最高海拔(米)_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("1800m") { altitudeMax = 1800; focusedField = nil }
                                Button("2000m") { altitudeMax = 2000; focusedField = nil }
                                Button("2150m") { altitudeMax = 2150; focusedField = nil }
                                Button("2400m") { altitudeMax = 2400; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为拼配组件的海拔添加常用选项
                    else if let components = focusedField?.split(separator: "_").map(String.init),
                              components.count >= 3,
                              components[0] == "种植海拔(米)" {
                        if let index = Int(components[1]), index < blendComponents.count {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    Button("1000m") { blendComponents[index].altitudeSingle = 1000; focusedField = nil }
                                    Button("1600m") { blendComponents[index].altitudeSingle = 1600; focusedField = nil }
                                    Button("1800m") { blendComponents[index].altitudeSingle = 1800; focusedField = nil }
                                    Button("2300m") { blendComponents[index].altitudeSingle = 2300; focusedField = nil }
                                }
                            }
                            .foregroundColor(.functionText)
                        }
                    }
                    else if let components = focusedField?.split(separator: "_").map(String.init),
                              components.count >= 3,
                              components[0] == "最低海拔(米)" {
                        if let index = Int(components[1]), index < blendComponents.count {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    Button("1000m") { blendComponents[index].altitudeMin = 1000; focusedField = nil }
                                    Button("1500m") { blendComponents[index].altitudeMin = 1500; focusedField = nil }
                                    Button("1600m") { blendComponents[index].altitudeMin = 1600; focusedField = nil }
                                    Button("2100m") { blendComponents[index].altitudeMin = 2100; focusedField = nil }
                                }
                            }
                            .foregroundColor(.functionText)
                        }
                    }
                    else if let components = focusedField?.split(separator: "_").map(String.init),
                              components.count >= 3,
                              components[0] == "最高海拔(米)" {
                        if let index = Int(components[1]), index < blendComponents.count {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    Button("1800m") { blendComponents[index].altitudeMax = 1800; focusedField = nil }
                                    Button("2000m") { blendComponents[index].altitudeMax = 2000; focusedField = nil }
                                    Button("2150m") { blendComponents[index].altitudeMax = 2150; focusedField = nil }
                                    Button("2400m") { blendComponents[index].altitudeMax = 2400; focusedField = nil }
                                }
                            }
                            .foregroundColor(.functionText)
                        }
                    }
                    // 为处理法添加常用选项
                    else if focusedField == "处理法_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("水洗") { process = "水洗"; focusedField = nil }
                                Button("日晒") { process = "日晒"; focusedField = nil }
                                Button("蜜处理") { process = "蜜处理"; focusedField = nil }
                                Button("半水洗") { process = "半水洗"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为品种添加常用选项
                    else if focusedField == "品种_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("瑰夏") { variety = "瑰夏"; focusedField = nil }
                                Button("耶加雪菲") { variety = "耶加雪菲"; focusedField = nil }
                                Button("埃塞原生种") { variety = "埃塞原生种"; focusedField = nil }
                                Button("卡蒂姆") { variety = "卡蒂姆"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为产地添加常用选项
                    else if focusedField == "产地_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("中国") { origin = "中国"; focusedField = nil }
                                Button("肯尼亚") { origin = "肯尼亚"; focusedField = nil }
                                Button("哥伦比亚") { origin = "哥伦比亚"; focusedField = nil }
                                Button("埃塞俄比亚") { origin = "埃塞俄比亚"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为拼配组件中的品种添加常用选项
                    else if let components = focusedField?.split(separator: "_").map(String.init),
                             components.count >= 3,
                             components[0] == "品种",
                             let index = Int(components[1]),
                             index < blendComponents.count {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("瑰夏") { blendComponents[index].variety = "瑰夏"; focusedField = nil }
                                Button("耶加雪菲") { blendComponents[index].variety = "耶加雪菲"; focusedField = nil }
                                Button("埃塞原生种") { blendComponents[index].variety = "埃塞原生种"; focusedField = nil }
                                Button("卡蒂姆") { blendComponents[index].variety = "卡蒂姆"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为拼配组件中的产地添加常用选项
                    else if let components = focusedField?.split(separator: "_").map(String.init),
                             components.count >= 3,
                             components[0] == "产地",
                             let index = Int(components[1]),
                             index < blendComponents.count {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("中国") { blendComponents[index].origin = "中国"; focusedField = nil }
                                Button("肯尼亚") { blendComponents[index].origin = "肯尼亚"; focusedField = nil }
                                Button("哥伦比亚") { blendComponents[index].origin = "哥伦比亚"; focusedField = nil }
                                Button("埃塞俄比亚") { blendComponents[index].origin = "埃塞俄比亚"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为拼配组件中的处理法添加常用选项
                    else if let components = focusedField?.split(separator: "_").map(String.init),
                             components.count >= 3,
                             components[0] == "处理法",
                             let index = Int(components[1]),
                             index < blendComponents.count {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("水洗") { blendComponents[index].process = "水洗"; focusedField = nil }
                                Button("日晒") { blendComponents[index].process = "日晒"; focusedField = nil }
                                Button("蜜处理") { blendComponents[index].process = "蜜处理"; focusedField = nil }
                                Button("半水洗") { blendComponents[index].process = "半水洗"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    
                    Spacer()
                    Button(action: {
                        focusedField = nil
                    }) {
                        Text("完成")
                            .foregroundColor(.functionText)
                    }
                }
            }
        }
        .sheet(isPresented: $showingFlavorTagsPicker) {
            FlavorTagsInput(selectedTags: $flavorTags)
        }
        .onAppear(perform: loadDraft)
        .onDisappear(perform: saveDraft)
    }

    // 顶部导航栏
    private var topNavigationBar: some View {
        ZStack {
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(.linkText)
                }
                
                Spacer()
                
                Button(action: saveBean) {
                    Text("保存")
                        .fontWeight(.medium)
                        .foregroundColor(isValid && !isLoading ? .linkText : Color.gray.opacity(0.5))
                }
                .disabled(!isValid || isLoading)
            }
            
            Text("新增咖啡豆")
                .font(.headline)
                .foregroundColor(.primaryText)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
    }

    // 顶部Tab选择视图
    private var tabSelectionView: some View {
        HStack(spacing: 0) {
            tabButton(title: "基本信息", index: 0)
            tabButton(title: "包装信息(选填)", index: 1)
            tabButton(title: type == "SKIP" ? "详细属性(跳过)" : "详细属性", index: 2, isDisabled: !shouldShowDetails)
        }
        .padding(.bottom, 8)
    }
    
    // Tab按钮
    private func tabButton(title: String, index: Int, isDisabled: Bool = false) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                selectedTab = index
            }
        }) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(
                        isDisabled 
                        ? Color.gray.opacity(0.5) 
                        : (selectedTab == index ? Color.primary : Color.gray)
                    )
                    .padding(.vertical, 8)
                
                // 选中指示器
                Rectangle()
                    .frame(height: 2)
                    .foregroundColor(selectedTab == index ? .functionText : .clear)
                    .animation(.spring(response: 0.3, dampingFraction: 0.8), value: selectedTab)
            }
        }
        .frame(maxWidth: .infinity)
        .buttonStyle(PlainButtonStyle())
        .disabled(isDisabled)
    }
    
    // Tab内容视图
    private var tabContentView: some View {
        VStack(spacing: 16) {
            switch selectedTab {
            case 0:
                generalInfoSection()
            case 1:
                productInfoSection()
            case 2:
                if shouldShowDetails {
                    detailsSection()
                } else {
                    VStack {
                        Text("详细属性仅对单品和拼配有效")
                            .foregroundColor(.primaryText)
                            .padding(20)
                    }
                    .frame(maxWidth: .infinity)
                    .background(Color.secondaryBg.opacity(0.15))
                    .cornerRadius(8)
                }
            default:
                EmptyView()
            }
        }
        .padding(.bottom, 20)
    }

    // 基本信息区域
    private func generalInfoSection() -> some View {
        VStack(spacing: 20) {
            // 咖啡豆类型选择
            VStack(alignment: .leading, spacing: 10) {
                Text("咖啡豆类型")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                HStack(spacing: 12) {
                    typeRadioButton(title: "单品", value: "SINGLE")
                    typeRadioButton(title: "拼配", value: "BLEND")
                    typeRadioButton(title: "跳过", value: "SKIP")
                    Spacer()
                }
                .padding(.horizontal, 4)
            }
            .padding(.horizontal, 16)
            
            // 豆商
            formFieldSimple(title: "豆商", binding: $roaster, placeholder: "请输入豆商/烘焙商")
            
            // 咖啡豆名称
            formFieldSimple(title: "咖啡豆名称", binding: $name)
            
            // 低因咖啡开关
            toggleFieldSimple(title: "是否低因咖啡？", isOn: $isDecaf)

            // 风味标签
            VStack(alignment: .leading, spacing: 10) {
                HStack(alignment: .center, spacing: 4) {
                    Text("风味标签")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(选填，请填写咖啡豆包装袋上的风味描述)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                    
                    Spacer()
                }
                
                FlavorTagsInput(selectedTags: $flavorTags)
                    .hideKeyboardToolbar() // 使用新的修饰器隐藏工具栏
            }
            .id("flavorTagsSection")
            .padding(.horizontal, 16)
            .onChange(of: focusedField) { newValue in
                if newValue?.contains("flavorTags") == true {
                    withAnimation {
                        // 添加延迟以确保键盘已经显示
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            scrollProxy?.scrollTo("flavorTagsSection", anchor: .top)
                        }
                    }
                }
            }
            
            // 备注文本编辑器
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Text("备注")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                    
                    Spacer()
                    
                    // 字数统计
                    Text("\(notes.count)/500")
                        .font(.caption)
                        .foregroundColor(notes.count >= 500 ? .error : .primaryText)
                }
                
                ZStack(alignment: .topLeading) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                        .submitLabel(.done)
                        .focused($focusedField, equals: "备注_field")
                        .scrollContentBackground(.hidden)
                        .background(Color.clear)
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.secondaryBg)
                        )
                        .onChange(of: notes) { newValue in
                            if newValue.count > 500 {
                                notes = String(newValue.prefix(500))
                            }
                        }
                    
                    if notes.isEmpty {
                        Text("请输入备注信息")
                            .foregroundColor(.gray)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 12)
                            .background(Color.clear)
                            .allowsHitTesting(false)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 简化版表单字段
    private func formFieldSimple(title: String, binding: Binding<String>, placeholder: String? = nil, optional: Bool = false) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                if optional {
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
            }
            
            TextField(placeholder ?? "请输入\(title)", text: binding)
                .focused($focusedField, equals: "\(title)_field")
                .submitLabel(.done)
                .font(.system(size: 17))
                .padding(.vertical, 14)
                .padding(.horizontal, 16)
                .background(Color.secondaryBg)
                .cornerRadius(12)
        }
        .padding(.horizontal, 16)
    }
    
    // 简化版开关字段
    private func toggleFieldSimple(title: String, isOn: Binding<Bool>) -> some View {
        HStack {
            Text(title)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.primaryText)
            
            Spacer()
            
            Toggle("", isOn: isOn)
                .labelsHidden()
                .tint(.functionText)
        }
        .padding(.horizontal, 16)
    }

    // 包装信息区域
    private func productInfoSection() -> some View {
        VStack(spacing: 20) {
            // 包装规格和库存余量
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("包装规格 (g)")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    numberFieldSimple(value: $bagWeight, placeholder: "0.00", fieldIdentifier: "包装规格 (g)")
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("库存余量 (g)")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    numberFieldSimple(value: $bagRemain, placeholder: "0.00", fieldIdentifier: "库存余量 (g)")
                }
            }
            .padding(.horizontal, 16)
            
            // 购买时间
            VStack(alignment: .leading, spacing: 8) {
                Text("购买时间")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                // 使用系统的直接可见DatePicker但自定义样式
                DatePicker(
                    "",
                    selection: $createdAt,
                    displayedComponents: [.date, .hourAndMinute]
                )
                .datePickerStyle(CompactDatePickerStyle())
                .labelsHidden()
                .accentColor(.functionText)
                .cornerRadius(12)
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 16)
            
            // 购买价格
            VStack(alignment: .leading, spacing: 8) {
                Text("购买价格 (元)")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                numberFieldSimple(value: $purchasePrice, placeholder: "0.00", fieldIdentifier: "购买价格 (元)")
            }
            .padding(.horizontal, 16)
            
            // 烘焙日期开关和相关字段
            VStack(alignment: .leading, spacing: 10) {
                // 是否知道烘焙日期开关
                toggleFieldSimple(title: "是否知道烘焙日期？", isOn: $roastDateEnabled)
                
                if roastDateEnabled {
                    // 烘焙日期选择器
                    VStack(alignment: .leading, spacing: 8) {
                        Text("烘焙日期")
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(.primaryText)
                        
                        DatePicker("", selection: $roastDate, displayedComponents: .date)
                            .datePickerStyle(CompactDatePickerStyle())
                            .labelsHidden()
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .padding(.horizontal, 16)
                    
                    // 养豆期设置
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("养豆期")
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.primaryText)
                            
                            Text("(选填，最多60天)")
                                .font(.caption)
                                .foregroundColor(.noteText)
                        }
                        
                        // 单一值/范围值切换
                        HStack(spacing: 8) {
                            Text("按单一值")
                                .font(.system(size: 14))
                            
                            Toggle("", isOn: Binding(
                                get: { restPeriodType == "RANGE" },
                                set: { restPeriodType = $0 ? "RANGE" : "SINGLE" }
                            ))
                            .labelsHidden()
                            .toggleStyle(SwitchToggleStyle(tint: .functionText))
                            
                            Text("按范围值")
                                .font(.system(size: 14))
                        }
                        .padding(.vertical, 6)
                        
                        // 养豆期输入
                        if restPeriodType == "SINGLE" {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("养豆天数")
                                    .font(.system(size: 16, weight: .regular))
                                    .foregroundColor(.primaryText)
                                
                                numberFieldSimple(value: $restPeriodMin, placeholder: "0", fieldIdentifier: "养豆天数")
                            }
                        } else {
                            HStack(spacing: 16) {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("最短养豆期")
                                        .font(.system(size: 16, weight: .regular))
                                        .foregroundColor(.primaryText)
                                    
                                    numberFieldSimple(value: $restPeriodMin, placeholder: "0", fieldIdentifier: "最短养豆期")
                                }
                                
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("最长养豆期")
                                        .font(.system(size: 16, weight: .regular))
                                        .foregroundColor(.primaryText)
                                    
                                    numberFieldSimple(value: $restPeriodMax, placeholder: "0", fieldIdentifier: "最长养豆期")
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                }
            }
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 简化版数字输入字段
    private func numberFieldSimple<T: Numeric & LosslessStringConvertible>(
        value: Binding<T?>,
        placeholder: String = "0",
        fieldIdentifier: String
    ) -> some View {
        let stringValue = value.wrappedValue != nil ? String(describing: value.wrappedValue!) : ""
        
        return ZStack(alignment: .leading) {
            if stringValue.isEmpty {
                Text(placeholder)
                    .foregroundColor(.gray)
                    .padding(.leading, 16)
            }
            
            TextField("", text: Binding(
                get: { stringValue },
                set: { newValue in
                    if let parsed = T(newValue) {
                        value.wrappedValue = parsed
                    } else if newValue.isEmpty {
                        value.wrappedValue = nil
                    }
                }
            ))
            .keyboardType(.decimalPad)
            .disableAutocorrection(true)
            .autocapitalization(.none)
            .font(.system(size: 17))
            .padding(.vertical, 14)
            .padding(.horizontal, 16)
            .focused($focusedField, equals: fieldIdentifier + "_field")
        }
        .background(Color.secondaryBg)
        .cornerRadius(12)
    }
    
    // Int类型专用的numberFieldSimple版本
    private func numberFieldSimpleForInt(
        value: Binding<Int?>,
        placeholder: String = "0",
        fieldIdentifier: String,
        useBlendStyle: Bool = false
    ) -> some View {
        let stringValue = value.wrappedValue != nil ? String(describing: value.wrappedValue!) : ""
        
        return ZStack(alignment: .leading) {
            if stringValue.isEmpty {
                Text(placeholder)
                    .foregroundColor(.gray)
                    .padding(.leading, 16)
            }
            
            TextField("", text: Binding(
                get: { stringValue },
                set: { newValue in
                    if let parsed = Int(newValue) {
                        value.wrappedValue = parsed
                    } else if newValue.isEmpty {
                        value.wrappedValue = nil
                    }
                }
            ))
            .keyboardType(.numberPad)
            .disableAutocorrection(true)
            .autocapitalization(.none)
            .font(.system(size: 17))
            .padding(.vertical, 14)
            .padding(.horizontal, 16)
            .focused($focusedField, equals: fieldIdentifier + "_field")
        }
        .background(useBlendStyle ? Color.focusBg : Color.secondaryBg)
        .cornerRadius(12)
    }

    // 类型单选按钮组件
    private func typeRadioButton(title: String, value: String) -> some View {
        Button(action: {
            type = value
            
            // 根据类型调整页面显示
            if selectedTab == 2 && value == "SKIP" {
                // 如果当前在详细属性tab并选择了"跳过"，则自动跳转到基本信息tab
                selectedTab = 0
            }
        }) {
            HStack(spacing: 8) {
                ZStack {
                    Circle()
                        .stroke(type == value ? Color.functionText : Color.gray, lineWidth: 1.5)
                        .frame(width: 20, height: 20)
                    
                    if type == value {
                        Circle()
                            .fill(Color.functionText)
                            .frame(width: 12, height: 12)
                    }
                }
                
                Text(title)
                    .font(.system(size: 15))
                    .foregroundColor(type == value ? .primary : .primaryText)
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 14)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(type == value ? Color.functionText.opacity(0.1) : Color.clear)
            )
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 数字输入字段组件
    private func numberField<T: Numeric & LosslessStringConvertible>(
        title: String,
        value: Binding<T?>,
        placeholder: String = "",
        format: NumberFormatter.Style = .decimal,
        range: ClosedRange<Int>? = nil
    ) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primaryText)
            
            HStack {
                if let valueUnwrapped = value.wrappedValue {
                    let stringValue = String(describing: valueUnwrapped)
                    TextField(placeholder, text: Binding(
                        get: { stringValue },
                        set: { newValue in
                            if let newValue = T(newValue) {
                                value.wrappedValue = newValue
                            } else if newValue.isEmpty {
                                value.wrappedValue = nil
                            }
                        }
                    ))
                    .keyboardType(.decimalPad)
                    .focused($focusedField, equals: "\(title)_field")
                    .submitLabel(.done)
                    .onTapGesture {} // 空的onTapGesture阻止文本字段失去焦点
                } else {
                    TextField(placeholder, text: Binding(
                        get: { "" },
                        set: { newValue in
                            if let newValue = T(newValue) {
                                value.wrappedValue = newValue
                            }
                        }
                    ))
                    .keyboardType(.decimalPad)
                    .focused($focusedField, equals: "\(title)_field")
                    .submitLabel(.done)
                    .onTapGesture {} // 空的onTapGesture阻止文本字段失去焦点
                }
            }
            .padding()
            .background(Color.primaryBg)
            .cornerRadius(8)
        }
    }

    // 切换开关组件
    private func toggleField(title: String, isOn: Binding<Bool>) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primaryText)
            
            Spacer()
            
            Toggle("", isOn: isOn)
                .labelsHidden()
        }
        .padding(.horizontal, 8)
    }

    // 详细属性区域
    private func detailsSection() -> some View {
        VStack(spacing: 12) {
            if type == "BLEND" {
                // 拼配豆组件
                blendComponentsSection()
            } else {
                // 单品咖啡详细信息
                singleBeanDetailsSection()
            }
        }
    }

    // 单品咖啡详细信息部分
    private func singleBeanDetailsSection() -> some View {
        VStack(spacing: 20) {
            // 烘焙程度滑块
            VStack(alignment: .leading, spacing: 10) {
                Text("烘焙程度")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                VStack(spacing: 12) {
                    sliderWithTap(value: Binding(
                        get: { Double(roastLevel) },
                        set: { roastLevel = Int($0) }
                    ), in: 1...7, step: 1)
                    
                    HStack {
                        Text("极浅")
                            .font(.caption)
                        Spacer()
                        Text("浅")
                            .font(.caption)
                        Spacer()
                        Text("浅中")
                            .font(.caption)
                        Spacer()
                        Text("中")
                            .font(.caption)
                        Spacer()
                        Text("中深")
                            .font(.caption)
                        Spacer()
                        Text("深")
                            .font(.caption)
                        Spacer()
                        Text("极深")
                            .font(.caption)
                    }
                }
                .padding(.vertical, 4)
            }
            .padding(.horizontal, 16)
            
            // 产地、产区
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 4) {
                        Text("产地")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                        
                        Text("(选填)")
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                    TextField("请输入原产国", text: $origin)
                        .focused($focusedField, equals: "产地_field")
                        .submitLabel(.done)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 4) {
                        Text("产区")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                        
                        Text("(选填)")
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                    TextField("请输入产区", text: $region)
                        .focused($focusedField, equals: "产区_field")
                        .submitLabel(.done)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                }
            }
            .padding(.horizontal, 16)
            
            // 庄园、品种
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 4) {
                        Text("庄园/处理站")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                        
                        Text("(选填)")
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                    TextField("请输入庄园", text: $finca)
                        .focused($focusedField, equals: "庄园_field")
                        .submitLabel(.done)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 4) {
                        Text("品种")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                        
                        Text("(选填)")
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                    TextField("请输入咖啡豆种", text: $variety)
                        .focused($focusedField, equals: "品种_field")
                        .submitLabel(.done)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                }
            }
            .padding(.horizontal, 16)
            
            // 处理法
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("处理法")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
                TextField("请输入处理法", text: $process)
                    .focused($focusedField, equals: "处理法_field")
                    .submitLabel(.done)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
            }
            .padding(.horizontal, 16)
            
            // 海拔信息
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Text("海拔信息")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
                // 单一值/范围值切换
                HStack(spacing: 8) {
                    Text("按单一值")
                        .font(.system(size: 14))
                    
                    Toggle("", isOn: Binding(
                        get: { altitudeType == "RANGE" },
                        set: { altitudeType = $0 ? "RANGE" : "SINGLE" }
                    ))
                    .labelsHidden()
                    .toggleStyle(SwitchToggleStyle(tint: .functionText))
                    
                    Text("按范围值")
                        .font(.system(size: 14))
                }
                .padding(.vertical, 6)
                
                // 海拔输入
                if altitudeType == "SINGLE" {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("种植海拔(米)")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                        
                        numberFieldSimpleForInt(value: $altitudeSingle, placeholder: "0", fieldIdentifier: "种植海拔(米)", useBlendStyle: false)
                    }
                } else {
                    HStack(spacing: 16) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("最低海拔(米)")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                            
                            numberFieldSimpleForInt(value: $altitudeMin, placeholder: "0", fieldIdentifier: "最低海拔(米)", useBlendStyle: false)
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("最高海拔(米)")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                            
                            numberFieldSimpleForInt(value: $altitudeMax, placeholder: "0", fieldIdentifier: "最高海拔(米)", useBlendStyle: false)
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 字段组包装器，提供轻微的视觉分组
    private func fieldsetGroup<Content: View>(@ViewBuilder content: () -> Content) -> some View {
        content()
            .padding(.vertical, 10)
            .padding(.horizontal, 2)
            .background(Color.secondaryBg.opacity(0.15))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.gray.opacity(0.1), lineWidth: 1)
            )
    }

    // 拼配豆组件部分
    private func blendComponentsSection() -> some View {
        VStack(spacing: 20) {
            HStack {
                Text("拼配豆信息")
                    .font(.headline)
                
                Spacer()
                
                // 如果拼配组件不超过5个，显示添加按钮
                if blendComponents.count < 5 {
                    Button(action: {
                        withAnimation {
                            // 折叠所有现有组件
                            for i in 0..<blendComponents.count {
                                blendComponents[i].isExpanded = false
                            }
                            
                            // 创建新组件并直接设置为展开状态
                            var newComponent = MutableBlendComponent(from: BlendComponent.defaultComponent())
                            newComponent.isExpanded = true
                            blendComponents.append(newComponent)
                            recalculateBlendRatios()
                        }
                    }) {
                        HStack {
                            Image(systemName: "plus.circle")
                            Text("追加拼配 (\(blendComponents.count)/5)")
                        }
                        .foregroundColor(.functionText)
                    }
                }
            }
            .padding(.horizontal, 16)
            
            // 逐个显示拼配组件
            ForEach(0..<blendComponents.count, id: \.self) { index in
                blendComponentView(index: index)
            }
            
            // 如果有多个组件，显示总比例
            if blendComponents.count > 1 {
                HStack {
                    Spacer()
                    
                    let ratio = calculateTotalRatio()
                    let isInteger = ratio.truncatingRemainder(dividingBy: 1) == 0
                    Text("总比例：\(isInteger ? String(format: "%.0f", ratio) : String(format: "%.2f", ratio))%")
                        .foregroundColor(abs(ratio - 100) > 0.02 ? .error : .primaryText)
                        .font(.subheadline)
                    
                    if abs(ratio - 100) > 0.02 {
                        Text("(必须等于100%，允许0.01的误差)")
                            .font(.caption)
                            .foregroundColor(.error)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .cornerRadius(8)
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
            }
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 单个拼配组件视图
    private func blendComponentView(index: Int) -> some View {
        VStack(alignment: .leading, spacing: 0) {
            // 组件标题栏
            Button(action: {
                withAnimation {
                    blendComponents[index].isExpanded.toggle()
                }
            }) {
                HStack {
                    Text("拼配豆 #\(index + 1)")
                        .font(.headline)
                    
                    Spacer()
                    
                    // 如果组件数大于1，显示删除按钮
                    if blendComponents.count > 1 {
                        Button(action: {
                            withAnimation {
                                blendComponents.remove(at: index)
                                recalculateBlendRatios()
                            }
                        }) {
                            Image(systemName: "trash")
                                .foregroundColor(.error)
                                .padding(.trailing, 8)
                        }
                        .buttonStyle(.plain)
                    }
                    
                    // 展开/折叠按钮
                    Image(systemName: blendComponents[index].isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.primaryText)
                }
                .padding(.vertical, 12)
                .padding(.horizontal, 12)
                .contentShape(Rectangle())
            }
            .buttonStyle(PlainButtonStyle())
            
            // 可折叠的内容区域
            if blendComponents[index].isExpanded {
                Divider()
                
                VStack(spacing: 16) {
                    // 如果有多个组件，显示比例设置
                    if blendComponents.count > 1 {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("拼配比例(%)")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                            Text("(如果不清楚可以不修改，保持均摊比例)")
                                .font(.caption)
                                .foregroundColor(.noteText)
                            TextField("0", value: $blendComponents[index].blendRatio, formatter: NumberFormatter())
                                .keyboardType(.decimalPad)
                                .focused($focusedField, equals: "拼配比例_\(index)_field")
                                .padding(12)
                                .background(Color.focusBg)
                                .cornerRadius(8)
                                .onChange(of: blendComponents[index].blendRatio) { _ in
                                    // 当用户手动修改某个组件的比例时，更新总比例显示
                                    // 不自动调整其他比例，让用户自己处理
                                    _ = calculateTotalRatio()
                                }
                        }
                        .padding(.horizontal, 16)
                        .padding(.top, 16)
                    }
                    
                    // 烘焙程度滑块
                    VStack(alignment: .leading, spacing: 10) {
                        Text("烘焙程度")
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(.primaryText)
                        
                        VStack(spacing: 12) {
                            sliderWithTap(value: Binding(
                                get: { Double(blendComponents[index].roastLevel) },
                                set: { blendComponents[index].roastLevel = Int($0) }
                            ), in: 1...7, step: 1)
                            
                            HStack {
                                Text("极浅")
                                    .font(.caption)
                                Spacer()
                                Text("浅")
                                    .font(.caption)
                                Spacer()
                                Text("浅中")
                                    .font(.caption)
                                Spacer()
                                Text("中")
                                    .font(.caption)
                                Spacer()
                                Text("中深")
                                    .font(.caption)
                                Spacer()
                                Text("深")
                                    .font(.caption)
                                Spacer()
                                Text("极深")
                                    .font(.caption)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 16)
                    
                    // 其他详细属性
                    VStack(spacing: 16) {
                        HStack(spacing: 16) {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(spacing: 4) {
                                    Text("产地")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    Text("(选填)")
                                        .font(.caption)
                                        .foregroundColor(.noteText)
                                }
                                TextField("请输入原产国", text: Binding(
                                    get: { blendComponents[index].origin ?? "" },
                                    set: { blendComponents[index].origin = $0 }
                                ))
                                .focused($focusedField, equals: "产地_\(index)_field")
                                .submitLabel(.done)
                                .padding(.vertical, 12)
                                .padding(.horizontal, 12)
                                .background(Color.focusBg)
                                .cornerRadius(8)
                            }
                            
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(spacing: 4) {
                                    Text("产区")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    Text("(选填)")
                                        .font(.caption)
                                        .foregroundColor(.noteText)
                                }
                                TextField("请输入产区", text: Binding(
                                    get: { blendComponents[index].region ?? "" },
                                    set: { blendComponents[index].region = $0 }
                                ))
                                .focused($focusedField, equals: "产区_\(index)_field")
                                .submitLabel(.done)
                                .padding(.vertical, 12)
                                .padding(.horizontal, 12)
                                .background(Color.focusBg)
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal, 16)
                        
                        HStack(spacing: 16) {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(spacing: 4) {
                                    Text("庄园/处理站")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    Text("(选填)")
                                        .font(.caption)
                                        .foregroundColor(.noteText)
                                }
                                
                                TextField("请输入庄园", text: Binding(
                                    get: { blendComponents[index].finca ?? "" },
                                    set: { blendComponents[index].finca = $0 }
                                ))
                                .focused($focusedField, equals: "庄园_\(index)_field")
                                .submitLabel(.done)
                                .padding(.vertical, 12)
                                .padding(.horizontal, 12)
                                .background(Color.focusBg)
                                .cornerRadius(8)
                            }
                            
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(spacing: 4) {
                                    Text("品种")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    Text("(选填)")
                                        .font(.caption)
                                        .foregroundColor(.noteText)
                                }
                                TextField("请输入咖啡豆种", text: Binding(
                                    get: { blendComponents[index].variety ?? "" },
                                    set: { blendComponents[index].variety = $0 }
                                ))
                                .focused($focusedField, equals: "品种_\(index)_field")
                                .submitLabel(.done)
                                .padding(.vertical, 12)
                                .padding(.horizontal, 12)
                                .background(Color.focusBg)
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal, 16)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            HStack(spacing: 4) {
                                Text("处理法")
                                    .font(.subheadline)
                                    .foregroundColor(.primaryText)
                                
                                Text("(选填)")
                                    .font(.caption)
                                    .foregroundColor(.noteText)
                            }
                            TextField("请输入处理法", text: Binding(
                                get: { blendComponents[index].process ?? "" },
                                set: { blendComponents[index].process = $0 }
                            ))
                            .focused($focusedField, equals: "处理法_\(index)_field")
                            .submitLabel(.done)
                            .padding(.vertical, 12)
                            .padding(.horizontal, 12)
                            .background(Color.focusBg)
                            .cornerRadius(8)
                        }
                        .padding(.horizontal, 16)
                        
                        // 海拔信息
                        VStack(alignment: .leading, spacing: 10) {
                            HStack(spacing: 4) {
                                Text("海拔信息")
                                    .font(.subheadline)
                                    .foregroundColor(.primaryText)
                                
                                Text("(选填)")
                                    .font(.caption)
                                    .foregroundColor(.noteText)
                            }
                            // 单一值/范围值切换
                            HStack(spacing: 8) {
                                Text("按单一值")
                                    .font(.system(size: 14))
                                    
                                    Toggle("", isOn: Binding(
                                        get: { 
                                            // 添加安全检查，确保索引有效
                                            guard index < blendComponents.count else { return false }
                                            return blendComponents[index].altitudeType == "RANGE" 
                                        },
                                        set: { 
                                            // 添加安全检查，确保索引有效
                                            guard index < blendComponents.count else { return }
                                            blendComponents[index].altitudeType = $0 ? "RANGE" : "SINGLE"
                                        }
                                    ))
                                    .labelsHidden()
                                    .toggleStyle(SwitchToggleStyle(tint: .functionText))
                                    
                                Text("按范围值")
                                    .font(.system(size: 14))
                            }
                            .padding(.vertical, 6)
                            
                            // 海拔输入
                            if index < blendComponents.count && blendComponents[index].altitudeType == "SINGLE" {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("种植海拔(米)")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    numberFieldSimpleForInt(
                                        value: Binding(
                                            get: { 
                                                guard index < blendComponents.count else { return nil }
                                                return blendComponents[index].altitudeSingle 
                                            },
                                            set: { 
                                                guard index < blendComponents.count else { return }
                                                blendComponents[index].altitudeSingle = $0 
                                            }
                                        ),
                                        placeholder: "0",
                                        fieldIdentifier: "种植海拔(米)_\(index)",
                                        useBlendStyle: true
                                    )
                                }
                            } else if index < blendComponents.count {
                                HStack(spacing: 16) {
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("最低海拔(米)")
                                            .font(.subheadline)
                                            .foregroundColor(.primaryText)
                                        
                                        numberFieldSimpleForInt(
                                            value: Binding(
                                                get: { 
                                                    guard index < blendComponents.count else { return nil }
                                                    return blendComponents[index].altitudeMin 
                                                },
                                                set: { 
                                                    guard index < blendComponents.count else { return }
                                                    blendComponents[index].altitudeMin = $0 
                                                }
                                            ),
                                            placeholder: "0",
                                            fieldIdentifier: "最低海拔(米)_\(index)",
                                            useBlendStyle: true
                                        )
                                    }
                                    
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("最高海拔(米)")
                                            .font(.subheadline)
                                            .foregroundColor(.primaryText)
                                        
                                        numberFieldSimpleForInt(
                                            value: Binding(
                                                get: { 
                                                    guard index < blendComponents.count else { return nil }
                                                    return blendComponents[index].altitudeMax 
                                                },
                                                set: { 
                                                    guard index < blendComponents.count else { return }
                                                    blendComponents[index].altitudeMax = $0 
                                                }
                                            ),
                                            placeholder: "0",
                                            fieldIdentifier: "最高海拔(米)_\(index)",
                                            useBlendStyle: true
                                        )
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.bottom, 16)
                    }
                }
            }
        }
        .background(Color.secondaryBg)
        .cornerRadius(8)
        .padding(.horizontal, 16)
    }

    // 自定义TextField解决数字键盘自动关闭问题
    private func customNumberField<T: Numeric & LosslessStringConvertible>(
        title: String,
        value: Binding<T?>,
        placeholder: String = "0"
    ) -> some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primaryText)
            
            HStack {
                let stringValue = value.wrappedValue != nil ? String(describing: value.wrappedValue!) : ""
                ZStack(alignment: .leading) {
                    if stringValue.isEmpty {
                        Text(placeholder)
                            .foregroundColor(.gray)
                            .padding(.leading, 8)
                    }
                    
                    TextField("", text: Binding(
                        get: { stringValue },
                        set: { newValue in
                            if let parsed = T(newValue) {
                                value.wrappedValue = parsed
                            } else if newValue.isEmpty {
                                value.wrappedValue = nil
                            }
                        }
                    ))
                    .keyboardType(.decimalPad)
                    .disableAutocorrection(true)
                    .autocapitalization(.none)
                    .focused($focusedField, equals: "\(title)_field")
                    .padding(.leading, 8)
                }
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 2)
            .background(Color.primaryBg.opacity(0.7))
            .cornerRadius(6)
            .overlay(
                RoundedRectangle(cornerRadius: 6)
                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
            )
            .contentShape(Rectangle())
            .onTapGesture {
                focusedField = "\(title)_field"
            }
        }
    }

    // 计算总比例
    private func calculateTotalRatio() -> Double {
        let total = blendComponents.reduce(0) { $0 + Double($1.blendRatio) }
        return (total * 100).rounded() / 100 // 保留两位小数
    }

    // 重新计算拼配比例
    private func recalculateBlendRatios() {
        // 如果只有一个组件，设置为100%
        if blendComponents.count == 1 {
            blendComponents[0].blendRatio = 100
            return
        }
        
        // 均分比例
        let equalRatio = 100.0 / Double(blendComponents.count)
        for i in 0..<blendComponents.count {
            blendComponents[i].blendRatio = equalRatio
        }
    }

    // 保存咖啡豆
    private func saveBean() {
        // 首先进行表单验证
        guard validateForm() else { return }
        guard isValid else { return }
        
        isLoading = true
        errorMessage = nil
        
        // 如果用户输入了bagWeight但未输入bagRemain，则自动设置bagRemain为bagWeight
        if let weight = bagWeight, bagRemain == nil {
            bagRemain = weight
        }
        
        // 处理养豆期范围值只填写了一个的情况
        var finalRestPeriodType = restPeriodType
        var finalRestPeriodMin = restPeriodMin
        var finalRestPeriodMax = restPeriodMax
        
        if roastDateEnabled && restPeriodType == "RANGE" {
            if (restPeriodMin != nil && restPeriodMax == nil) || (restPeriodMin != nil && restPeriodMax == 0) {
                // 只有最小值或最长值为0，转为单一值
                finalRestPeriodType = "SINGLE"
                finalRestPeriodMin = restPeriodMin  // 保持最小值作为单一值
                finalRestPeriodMax = nil
            } else if (restPeriodMin == nil && restPeriodMax != nil) || (restPeriodMin == 0 && restPeriodMax != nil && restPeriodMax! > 0) {
                // 只有最大值或最短值为0，转为单一值
                finalRestPeriodType = "SINGLE"
                finalRestPeriodMin = restPeriodMax  // 使用最大值作为单一值
                finalRestPeriodMax = nil
            }
        }
        
        // 处理海拔范围值只填写了一个的情况
        var finalAltitudeType = altitudeType
        var finalAltitudeSingle = altitudeSingle
        var finalAltitudeMin = altitudeMin
        var finalAltitudeMax = altitudeMax
        
        if type != "SKIP" && altitudeType == "RANGE" {
            if (altitudeMin != nil && altitudeMax == nil) || (altitudeMin != nil && altitudeMax == 0) {
                // 只有最小值或最高值为0，转为单一值
                finalAltitudeType = "SINGLE"
                finalAltitudeSingle = altitudeMin
                finalAltitudeMin = nil
                finalAltitudeMax = nil
            } else if (altitudeMin == nil && altitudeMax != nil) || (altitudeMin == 0 && altitudeMax != nil && altitudeMax! > 0) {
                // 只有最大值或最低值为0，转为单一值
                finalAltitudeType = "SINGLE"
                finalAltitudeSingle = altitudeMax
                finalAltitudeMin = nil
                finalAltitudeMax = nil
            }
        }
        
        // 创建新的咖啡豆
        Task {
            do {
                let roastDateToSave = roastDateEnabled ? roastDate : nil
                let restPeriodMinToSave = roastDateEnabled ? finalRestPeriodMin : nil
                let restPeriodMaxToSave = roastDateEnabled && finalRestPeriodType == "RANGE" ? finalRestPeriodMax : nil
                
                try await viewModel.createBean(
                    name: name,
                    type: type,
                    roaster: roaster,
                    origin: type == "SKIP" ? nil : origin,
                    region: type == "SKIP" ? nil : region,
                    finca: type == "SKIP" ? nil : finca,
                    variety: type == "SKIP" ? nil : variety,
                    process: type == "SKIP" ? nil : process,
                    roastLevel: roastLevel,
                    notes: notes.isEmpty ? nil : notes,
                    barcode: barcode.isEmpty ? nil : barcode,
                    bagWeight: bagWeight,
                    bagRemain: bagRemain,
                    purchasePrice: purchasePrice,
                    isDecaf: isDecaf,
                    createdAt: createdAt,
                    roastDate: roastDateToSave,
                    isArchived: false, // 新增的咖啡豆默认不归档
                    isFavorite: false, // 新增的咖啡豆默认不是首选
                    altitudeType: type == "SKIP" ? "SINGLE" : finalAltitudeType,
                    altitudeSingle: type == "SKIP" || finalAltitudeType != "SINGLE" ? nil : finalAltitudeSingle,
                    altitudeMin: type == "SKIP" || finalAltitudeType != "RANGE" ? nil : finalAltitudeMin,
                    altitudeMax: type == "SKIP" || finalAltitudeType != "RANGE" ? nil : finalAltitudeMax,
                    restPeriodMin: restPeriodMinToSave,
                    restPeriodMax: restPeriodMaxToSave,
                    flavorTags: flavorTags.isEmpty ? nil : flavorTags,
                    blendComponents: type == "BLEND" ? processBlendComponents() : nil
                )
                
                // 在关闭表单前刷新咖啡豆列表，确保列表立即更新
                if let beanListViewModel = brewlog.SharedViewModels.shared.beanListViewModel {
                    await beanListViewModel.fetchCoffeeBeans(forceRefresh: true)
                    print("✅ 成功刷新咖啡豆列表")
                } else {
                    print("⚠️ 未找到BeanListViewModel实例，无法刷新列表")
                }
                
                // 保存成功后清除草稿
                clearDraft()
                
                presentationMode.wrappedValue.dismiss()
            } catch let error as NSError {
                // 处理唯一性冲突错误
                if error.domain == "brewlog.APIError" && error.code == 2 {
                    if let responseData = error.userInfo["responseData"] as? [String: Any],
                       let errorMessage = responseData["error"] as? String {
                        self.errorMessage = errorMessage
                    } else {
                        self.errorMessage = "可能存在相同的豆商+咖啡豆组合，请更改名称后重试"
                    }
                } else {
                    errorMessage = error.localizedDescription
                }
            } catch {
                errorMessage = error.localizedDescription
            }
            
            isLoading = false
        }
    }
    
    // 处理拼配组件中的海拔信息
    private func processBlendComponents() -> [BlendComponent] {
        return blendComponents.map { component in
            var mutableComponent = component
            
            // 处理拼配组件中的海拔范围值只填写了一个的情况
            if mutableComponent.altitudeType == "RANGE" {
                if (mutableComponent.altitudeMin != nil && mutableComponent.altitudeMax == nil) || 
                   (mutableComponent.altitudeMin != nil && mutableComponent.altitudeMax == 0) {
                    // 只有最小值或最高值为0，转为单一值
                    mutableComponent.altitudeType = "SINGLE"
                    mutableComponent.altitudeSingle = mutableComponent.altitudeMin
                    mutableComponent.altitudeMin = nil
                    mutableComponent.altitudeMax = nil
                } else if (mutableComponent.altitudeMin == nil && mutableComponent.altitudeMax != nil) || 
                          (mutableComponent.altitudeMin == 0 && mutableComponent.altitudeMax != nil && mutableComponent.altitudeMax! > 0) {
                    // 只有最大值或最低值为0，转为单一值
                    mutableComponent.altitudeType = "SINGLE"
                    mutableComponent.altitudeSingle = mutableComponent.altitudeMax
                    mutableComponent.altitudeMin = nil
                    mutableComponent.altitudeMax = nil
                }
            }
            
            return mutableComponent.toBlendComponent()
        }
    }

    // 自定义可点击定位的滑块
    private func sliderWithTap(value: Binding<Double>, in range: ClosedRange<Double>, step: Double = 1.0) -> some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 背景轨道
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 4)
                    .cornerRadius(2)
                
                // 已填充部分
                Rectangle()
                    .fill(Color.functionText)
                    .frame(width: CGFloat((value.wrappedValue - range.lowerBound) / (range.upperBound - range.lowerBound)) * geometry.size.width, height: 4)
                    .cornerRadius(2)
                
                // 滑块按钮
                Circle()
                    .fill(Color.functionText)
                    .frame(width: 24, height: 24)
                    .shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)
                    .offset(x: CGFloat((value.wrappedValue - range.lowerBound) / (range.upperBound - range.lowerBound)) * (geometry.size.width - 24))
                
                // 透明覆盖层用于点击
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle())
                    .frame(height: 40)
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { gesture in
                                let ratio = min(max(0, gesture.location.x / geometry.size.width), 1)
                                let newValue = range.lowerBound + (range.upperBound - range.lowerBound) * Double(ratio)
                                // 根据step约束值
                                let steppedValue = round(newValue / step) * step
                                value.wrappedValue = min(max(range.lowerBound, steppedValue), range.upperBound)
                            }
                    )
            }
        }
        .frame(height: 40)
    }

    #if DEBUG
    struct AddBeanView_Previews: PreviewProvider {
        static var previews: some View {
            NavigationView {
                AddBeanView()
            }
        }
    }
    #endif

    // 添加ScrollViewProxy用于滚动控制
    @State private var scrollProxy: ScrollViewProxy?
}

// 构建默认的混合组件
extension BlendComponent {
    static func defaultComponent() -> BlendComponent {
        // 创建一个可变的 BlendComponent
        let component = BlendComponent(
            id: 1,
            coffeeBeanId: 0,
            origin: "",
            region: "",
            finca: "",
            variety: "",
            process: "",
            roastLevel: 4,
            roastLevelDisplay: "中烘",
            blendRatio: 100,
            order: 0,
            altitudeType: "SINGLE",
            altitudeSingle: nil,
            altitudeMin: nil,
            altitudeMax: nil
        )
        
        // 如果需要修改属性，可以在这里
        
        return component
    }
}

// ViewModel扩展添加createBean方法以支持标签和拼配组件
extension BrewLogViewModel {
    func createBean(
        name: String,
        type: String,
        roaster: String,
        origin: String?,
        region: String?,
        finca: String?,
        variety: String?,
        process: String?,
        roastLevel: Int,
        notes: String?,
        barcode: String?,
        bagWeight: Double?,
        bagRemain: Double?,
        purchasePrice: Double?,
        isDecaf: Bool,
        createdAt: Date,
        roastDate: Date?,
        isArchived: Bool,
        isFavorite: Bool,
        altitudeType: String,
        altitudeSingle: Int?,
        altitudeMin: Int?,
        altitudeMax: Int?,
        restPeriodMin: Int?,
        restPeriodMax: Int?,
        flavorTags: [String]? = nil,
        blendComponents: [BlendComponent]? = nil
    ) async throws {
        // 传递所有参数给原始的createBean方法，包括flavorTags和blendComponents
        try await self.createBean(
            name: name,
            type: type,
            roaster: roaster,
            origin: origin ?? "",
            region: region ?? "",
            finca: finca ?? "",
            variety: variety ?? "",
            process: process ?? "",
            roastLevel: roastLevel,
            notes: notes ?? "",
            barcode: barcode ?? "",
            bagWeight: bagWeight,
            bagRemain: bagRemain,
            purchasePrice: purchasePrice,
            isDecaf: isDecaf,
            createdAt: createdAt,
            roastDate: roastDate,
            isArchived: isArchived,
            isFavorite: isFavorite,
            altitudeType: altitudeType,
            altitudeSingle: altitudeSingle,
            altitudeMin: altitudeMin,
            altitudeMax: altitudeMax,
            restPeriodMin: restPeriodMin,
            restPeriodMax: restPeriodMax,
            flavorTags: flavorTags,
            blendComponents: blendComponents
        )
    }
}

// 修正 BlendComponent 结构中 roastLevel 属性问题
struct MutableBlendComponent {
    var id: Int
    var coffeeBeanId: Int
    var origin: String?
    var region: String?
    var finca: String?
    var variety: String?
    var process: String?
    var roastLevel: Int
    var roastLevelDisplay: String?
    var blendRatio: Double
    var order: Int
    var altitudeType: String
    var altitudeSingle: Int?
    var altitudeMin: Int?
    var altitudeMax: Int?
    var isExpanded: Bool = true // 添加展开/折叠状态属性
    
    // 从 BlendComponent 创建 MutableBlendComponent
    init(from component: BlendComponent) {
        self.id = component.id
        self.coffeeBeanId = component.coffeeBeanId
        self.origin = component.origin
        self.region = component.region
        self.finca = component.finca
        self.variety = component.variety
        self.process = component.process
        self.roastLevel = component.roastLevel
        self.roastLevelDisplay = component.roastLevelDisplay
        self.blendRatio = component.blendRatio
        self.order = component.order
        self.altitudeType = component.altitudeType
        self.altitudeSingle = component.altitudeSingle
        self.altitudeMin = component.altitudeMin
        self.altitudeMax = component.altitudeMax
        self.isExpanded = true
    }
    
    // 转换回 BlendComponent
    func toBlendComponent() -> BlendComponent {
        let component = BlendComponent(
            id: id,
            coffeeBeanId: coffeeBeanId,
            origin: origin,
            region: region,
            finca: finca,
            variety: variety,
            process: process,
            roastLevel: roastLevel,
            roastLevelDisplay: roastLevelDisplay,
            blendRatio: blendRatio,
            order: order,
            altitudeType: altitudeType,
            altitudeSingle: altitudeSingle,
            altitudeMin: altitudeMin,
            altitudeMax: altitudeMax
        )
        return component
    }
    
    // 转换为可存储格式
    func toStorable() -> StorableBlendComponent {
        return StorableBlendComponent(
            id: id,
            coffeeBeanId: coffeeBeanId,
            origin: origin ?? "",
            region: region ?? "",
            finca: finca ?? "",
            variety: variety ?? "",
            process: process ?? "",
            roastLevel: roastLevel,
            roastLevelDisplay: roastLevelDisplay ?? "",
            blendRatio: blendRatio,
            order: order,
            altitudeType: altitudeType,
            altitudeSingle: altitudeSingle,
            altitudeMin: altitudeMin,
            altitudeMax: altitudeMax,
            isExpanded: isExpanded
        )
    }
}

// 可序列化的混合组件结构体，用于保存到UserDefaults
struct StorableBlendComponent: Codable {
    var id: Int
    var coffeeBeanId: Int
    var origin: String
    var region: String
    var finca: String
    var variety: String
    var process: String
    var roastLevel: Int
    var roastLevelDisplay: String
    var blendRatio: Double
    var order: Int
    var altitudeType: String
    var altitudeSingle: Int?
    var altitudeMin: Int?
    var altitudeMax: Int?
    var isExpanded: Bool = true // 添加展开/折叠状态属性
    
    // 转换回可变混合组件
    func toMutableComponent() -> MutableBlendComponent {
        var component = MutableBlendComponent(from: BlendComponent(
            id: id,
            coffeeBeanId: coffeeBeanId,
            origin: origin,
            region: region,
            finca: finca,
            variety: variety,
            process: process,
            roastLevel: roastLevel,
            roastLevelDisplay: roastLevelDisplay,
            blendRatio: blendRatio,
            order: order,
            altitudeType: altitudeType,
            altitudeSingle: altitudeSingle,
            altitudeMin: altitudeMin,
            altitudeMax: altitudeMax
        ))
        component.isExpanded = isExpanded
        return component
    }
} 

// 内存辅助工具类，用于比较绑定对象
class MemoryHelpers {
    // 获取对象的内存地址字符串
    static func addressString<T>(of object: T) -> String {
        return String(format: "%p", ObjectIdentifier(object as AnyObject).hashValue)
    }
}

