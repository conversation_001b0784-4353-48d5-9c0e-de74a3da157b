import SwiftUI

// 添加一个ObservableObject来管理BeanDetailView的状态
class BeanDetailViewState: ObservableObject {
    @Published var refreshedBean: CoffeeBean?
    @Published var isRefreshing: Bool = false
    @Published var refreshError: Error?
    
    // 简化截图相关状态
    @Published var isGeneratingImage: Bool = false
}

struct BeanDetailView: View {
    let bean: CoffeeBean
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authService: AuthService
    @StateObject private var viewModel = BeanDetailViewModel()
    @State private var showingEditView = false
    @State private var showingOccurrenceView = false
    @Environment(\.displayScale) var displayScale
    @Environment(\.colorScheme) var colorScheme
    @ObservedObject private var themeManager = ThemeManager.shared
    
    // 使用ObservableObject来管理状态
    @StateObject private var viewState = BeanDetailViewState()
    
    // 添加一个ID标识，确保视图重建时使用新的实例
    let viewID = UUID()
    
    var body: some View {
        ScrollView {
            // 使用refreshedBean如果有的话，否则使用传入的bean
            beanDetailContent
                .overlay(
                    Group {
                        if viewState.isRefreshing {
                            BlinkingLoader(
                                color: .primaryText,
                                width: 12,
                                height: 18,
                                duration: 1.5,
                                text: "刷新中..."
                            )
                            .frame(width: 120, height: 50)
                            .background(Color.primaryBg.opacity(0.8))
                            .cornerRadius(10)
                        }
                    }
                )
        }
        .background(Color.primaryBg)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .principal) {
                Text("咖啡豆详情")
                    .font(.headline)
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                HStack {
                    Button(action: {
                        // 长截图功能
                        Task {
                            await generateAndShareImage()
                        }
                    }) {
                        if viewState.isGeneratingImage {
                            BlinkingLoader(
                                color: .primaryText,
                                width: 8,
                                height: 12,
                                duration: 1.0,
                                showText: false
                            )
                        } else {
                            Image("screenshot.symbols")
                        }
                    }
                    .disabled(viewState.isGeneratingImage)
                    
                    Button(action: {
                        showingEditView = true
                    }) {
                        Image("edit.symbols")
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditView) {
            NavigationView {
                EditBeanView(bean: viewState.refreshedBean ?? bean)
            }
        }
        .overlay {
            ZStack {
                if viewModel.showingFlavorAccuracyInfo {
                    FlavorAccuracyInfoSheet(isPresented: $viewModel.showingFlavorAccuracyInfo)
                }
            }
        }
        .id(viewID) // 确保视图使用唯一ID，避免复用
        .onAppear {
            themeManager.updateThemeColorsOnly()
            
            // 清除之前可能保存的其他咖啡豆数据
            clearPreviousBeanData()
            
            // 重置状态，确保每次打开都是新的状态
            viewState.refreshedBean = nil
            // 确保截图相关状态也被重置
            viewState.isGeneratingImage = false
            
            // 强制从服务器获取最新数据
            refreshBeanData()
            
            print("🔄 BeanDetailView出现 - 咖啡豆ID: \(bean.id), 名称: \(bean.name)")
        }
        .onDisappear {
            // 在视图消失时保存最新数据
            saveCurrentBean()
            // 确保截图相关状态被重置
            viewState.isGeneratingImage = false
            print("👋 BeanDetailView消失 - 咖啡豆ID: \(bean.id), 名称: \(bean.name)")
        }
        .onReceive(NotificationCenter.default.publisher(for: TraitChangeObserver.traitChangeNotification)) { _ in
            themeManager.updateThemeColorsOnly()
        }
        // 监听BeanListNeedsRefresh通知，但不再返回列表页面，而是更新当前数据
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("BeanListNeedsRefresh"))) { _ in
            print("⚡️ 在BeanDetailView中收到BeanListNeedsRefresh通知，刷新当前咖啡豆详情")
            // 刷新当前咖啡豆数据而不是返回列表
            refreshBeanData()
        }
        // 添加对BeanDetailNeedsRefresh通知的监听
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("BeanDetailNeedsRefresh"))) { notification in
            if let updatedBean = notification.object as? CoffeeBean, updatedBean.id == (viewState.refreshedBean ?? bean).id {
                print("⚡️ 在BeanDetailView中收到BeanDetailNeedsRefresh通知，刷新当前咖啡豆详情")
                // 直接使用通知中的咖啡豆数据更新视图，无需再次请求
                Task {
                    await MainActor.run {
                        self.viewState.refreshedBean = updatedBean
                        print("✅ 咖啡豆数据刷新成功：\(updatedBean.name)")
                        
                        // 使用ObservableObject触发视图刷新
                        self.viewState.objectWillChange.send()
                    }
                }
            }
        }
        .navigationDestination(isPresented: $showingOccurrenceView) {
            BeanOccurrenceView(bean: viewState.refreshedBean ?? bean)
        }
    }
    
    // 获取当前显示的咖啡豆数据
    private var currentBean: CoffeeBean {
        viewState.refreshedBean ?? bean
    }
    
    // 清除可能存在的其他咖啡豆数据缓存
    private func clearPreviousBeanData() {
        // 遍历所有可能的saved_bean_前缀键，但跳过当前咖啡豆的键
        let userDefaults = UserDefaults.standard
        let beanKeyPrefix = "saved_bean_"
        
        // 获取所有以beanKeyPrefix开头的键
        let allKeys = userDefaults.dictionaryRepresentation().keys
        let beanKeys = allKeys.filter { $0.hasPrefix(beanKeyPrefix) }
        
        // 当前咖啡豆的键
        let currentBeanKey = "saved_bean_\(bean.id)"
        
        // 删除所有其他咖啡豆的键
        for key in beanKeys {
            if key != currentBeanKey {
                userDefaults.removeObject(forKey: key)
                print("🗑️ 已清除其他咖啡豆缓存: \(key)")
            }
        }
    }
    
    // 刷新咖啡豆数据的方法
    private func refreshBeanData() {
        Task {
            await MainActor.run {
                viewState.isRefreshing = true
                viewState.refreshError = nil
            }
            
            do {
                // 检查是否有来自 BeanOccurrenceViewModel 的数据
                if let viewModel = try? await checkForUpdatedBean() {
                    await MainActor.run {
                        if let updatedBean = viewModel.updatedBean {
                            self.viewState.refreshedBean = updatedBean
                            print("✅ 使用视图模型中的咖啡豆数据刷新成功：\(updatedBean.name)")
                            
                            // 使用ObservableObject触发视图刷新
                            self.viewState.objectWillChange.send()
                        }
                        viewState.isRefreshing = false
                    }
                    return
                }
                
                // 备选方案：如果没有视图模型数据，尝试从 API 获取
                let updatedBean: CoffeeBean? = try await getBeanDetail(beanId: bean.id)
                
                await MainActor.run {
                    if let updatedBean = updatedBean {
                        self.viewState.refreshedBean = updatedBean
                        print("✅ 咖啡豆数据刷新成功：\(updatedBean.name)")
                        
                        // 使用ObservableObject触发视图刷新
                        self.viewState.objectWillChange.send()
                    }
                    viewState.isRefreshing = false
                }
            } catch {
                await MainActor.run {
                    print("❌ 刷新咖啡豆数据失败：\(error.localizedDescription)")
                    viewState.refreshError = error
                    viewState.isRefreshing = false
                }
            }
        }
    }
    
    // 检查UserDefaults中是否有保存的数据
    private func checkForSavedBean() {
        // 尝试从UserDefaults获取保存的咖啡豆数据
        if let savedBeanData = UserDefaults.standard.data(forKey: "saved_bean_\(bean.id)") {
            let decoder = JSONDecoder()
            do {
                let savedBean = try decoder.decode(CoffeeBean.self, from: savedBeanData)
                
                // 比较occurrences数量，如果保存的数据更新，则使用保存的数据
                if let savedOccurrences = savedBean.occurrences, 
                   let currentOccurrences = (viewState.refreshedBean ?? bean).occurrences,
                   savedOccurrences.count != currentOccurrences.count {
                    
                    print("📝 从UserDefaults加载到更新的咖啡豆数据，ID: \(savedBean.id)，名称: \(savedBean.name)")
                    print("🔄 回购记录数: 当前 \(currentOccurrences.count) -> 保存的 \(savedOccurrences.count)")
                    
                    // 更新refreshedBean
                    self.viewState.refreshedBean = savedBean
                } else {
                    print("📝 UserDefaults中存在咖啡豆数据，但回购记录数量相同或当前数据更新")
                }
            } catch {
                print("❌ 解码UserDefaults中的咖啡豆数据失败: \(error.localizedDescription)")
            }
        } else {
            print("📝 UserDefaults中没有找到此咖啡豆的保存数据")
        }
    }
    
    // 保存当前咖啡豆数据到UserDefaults
    private func saveCurrentBean() {
        // 只保存有refreshedBean的情况，避免覆盖未更新的数据
        if let beanToSave = viewState.refreshedBean {
            let encoder = JSONEncoder()
            do {
                let beanData = try encoder.encode(beanToSave)
                UserDefaults.standard.set(beanData, forKey: "saved_bean_\(beanToSave.id)")
                print("📝 已将咖啡豆数据保存到UserDefaults，ID: \(beanToSave.id)，名称: \(beanToSave.name)")
            } catch {
                print("❌ 编码咖啡豆数据失败: \(error.localizedDescription)")
            }
        } else {
            print("📝 没有需要保存的更新咖啡豆数据")
        }
    }
    
    // 添加检查是否有视图模型更新的方法
    private func checkForUpdatedBean() async throws -> BeanOccurrenceViewModel? {
        // 在主视图层次结构中查找 BeanOccurrenceViewModel
        // 这是一个简单示范，实际应用中可能需要更复杂的访问方式
        return nil
    }
    
    // 获取咖啡豆详情的方法
    private func getBeanDetail(beanId: Int) async throws -> CoffeeBean? {
        // 使用APIService获取最新的咖啡豆数据
        // 这里假设APIService有一个通过ID获取咖啡豆详情的方法
        // 如果没有，可以先尝试从BeanListViewModel获取，或者创建一个专门的方法
        
        // 方式1: 直接通过API获取（如果有相应的API端点）
        do {
            let endpoint = "/ios/api/beans/\(beanId)/"
            var request = URLRequest(url: URL(string: "\(APIService.shared.getBaseURL())\(endpoint)")!)
            request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            // 添加缓存控制，确保每次获取最新数据
            request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
            request.cachePolicy = .reloadIgnoringLocalCacheData
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 {
                // 尝试打印响应数据以进行调试
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("📥 BeanDetailView.getBeanDetail响应数据: \(jsonString)")
                }
                
                let decoder = JSONDecoder()
                // 设置日期解码策略
                decoder.dateDecodingStrategy = .custom({ decoder in
                    let container = try decoder.singleValueContainer()
                    if let dateString = try? container.decode(String.self) {
                        let formatter = DateFormatter()
                        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
                        if let date = formatter.date(from: dateString) {
                            return date
                        }
                        
                        // 尝试更多格式
                        let formats = ["yyyy-MM-dd'T'HH:mm:ssZ", "yyyy-MM-dd"]
                        for format in formats {
                            formatter.dateFormat = format
                            if let date = formatter.date(from: dateString) {
                                return date
                            }
                        }
                        return Date()
                    } else if let timestamp = try? container.decode(Double.self) {
                        return Date(timeIntervalSince1970: timestamp)
                    }
                    return Date()
                })
                
                // 尝试两种可能的响应结构
                do {
                    // 首先尝试直接解码为CoffeeBean
                    return try decoder.decode(CoffeeBean.self, from: data)
                } catch {
                    print("❌ 直接解码为CoffeeBean失败，尝试解码为SingleBeanResponse: \(error)")
                    
                    // 如果失败，尝试解码为嵌套结构
                    struct SingleBeanResponse: Decodable {
                        let bean: CoffeeBean
                    }
                    
                    let beanResponse = try decoder.decode(SingleBeanResponse.self, from: data)
                    return beanResponse.bean
                }
            } else {
                print("❌ 获取咖啡豆详情失败，HTTP状态码: \((response as? HTTPURLResponse)?.statusCode ?? -1)")
                if let errorData = String(data: data, encoding: .utf8) {
                    print("❌ 错误响应: \(errorData)")
                }
                throw APIError.serverError((response as? HTTPURLResponse)?.statusCode ?? -1, "获取咖啡豆详情失败")
            }
        } catch {
            print("❌ API获取咖啡豆详情失败：\(error.localizedDescription)")
            throw error
        }
    }
    
    // 豆子详情内容
    var beanDetailContent: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 卡片视图
                ZStack(alignment: .leading) {
                    // 卡片主体 - 居中展示
                    ZStack(alignment: .topLeading) {
                        // 外边框
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.primaryBg)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.functionText, lineWidth: 2)
                            )
                        
                        VStack(spacing: 0) {
                            // 上层：风味部分 (只有在有风味标签时显示)
                            if let tasteNotes = currentBean.tasteNotes, !tasteNotes.isEmpty {
                                VStack(alignment: .trailing, spacing: 8) {
                                    Text("风味")
                                        .font(.footnote)
                                        .foregroundColor(.error)
                                        .frame(maxWidth: .infinity, alignment: .trailing)
                                        .padding(.top, 12)
                                        .padding(.trailing, 16)
                                    
                                    // 风味标签 (每行2个)
                                    VStack(alignment: .center, spacing: 6) {
                                        ForEach(0..<(tasteNotes.count + 1) / 2, id: \.self) { row in
                                            HStack(spacing: 4) {
                                                let firstIndex = row * 2
                                                let secondIndex = firstIndex + 1
                                                
                                                // 检查是否是最后一行且是奇数个标签
                                                let isLastOddRow = tasteNotes.count % 2 != 0 && firstIndex == tasteNotes.count - 1
                                                
                                                if isLastOddRow {
                                                    // 奇数个风味标签时，最后一个标签居中显示且不带顿号
                                                    Spacer()
                                                    Text(tasteNotes[firstIndex])
                                                        .frame(maxWidth: .infinity, alignment: .center)
                                                    Spacer()
                                                } else {
                                                    // 常规的双标签行
                                                    if firstIndex < tasteNotes.count {
                                                        Text(tasteNotes[firstIndex])
                                                            .frame(maxWidth: .infinity, alignment: .trailing)
                                                    }
                                                    
                                                    Text("、")
                                                        .foregroundColor(.gray)
                                                    
                                                    if secondIndex < tasteNotes.count {
                                                        Text(tasteNotes[secondIndex])
                                                            .frame(maxWidth: .infinity, alignment: .leading)
                                                    } else {
                                                        Spacer()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    .font(.callout)
                                    .padding(.horizontal, 16)
                                    .padding(.bottom, 32)
                                }
                                
                                CustomDivider(color: .functionText)
                            }
                            
                            // 中层：豆子基本信息
                            VStack(alignment: .center, spacing: 8) {
                                // 豆子名称
                                ZStack(alignment: .topTrailing) {
                                    Text(currentBean.name)
                                        .font(.title3)
                                        .fontWeight(.bold)
                                        .multilineTextAlignment(.center)
                                        .padding(.top, 32)
                                        .padding(.horizontal, 16)
                                    
                                    // 无咖啡因角标
                                    if currentBean.isDecaf {
                                        Image("decaf.symbols")
                                            .offset(x: 8, y: 30) // 调整位置到右上角
                                            .foregroundColor(.linkText)
                                    }
                                }
                                .frame(maxWidth: .infinity)
                                
                                // 豆商名称
                                Text(currentBean.roaster)
                                    .font(.headline)
                                    .fontWeight(.regular)
                                    .foregroundColor(.gray)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 14)
                                
                                // 烘焙度 (当 bean.typeDisplay != "跳过" 时需隐藏)
                                if currentBean.typeDisplay != "跳过" {
                                    // 判断是否为拼配咖啡 且 有blendComponents
                                    if currentBean.type == "BLEND" && currentBean.blendComponents != nil && !currentBean.blendComponents!.isEmpty {
                                        // 获取所有组件的烘焙度
                                        let roastLevels = currentBean.blendComponents!.map { $0.roastLevel }
                                        
                                        // 判断所有烘焙度是否相同
                                        let allSameLevel = roastLevels.allSatisfy { $0 == roastLevels.first }
                                        
                                        if allSameLevel {
                                            // 如果所有烘焙度相同，只显示一个不带序号的指示器
                                            RoastLevelIndicator(roastLevel: roastLevels.first ?? currentBean.roastLevel)
                                                .padding(.top, 4)
                                                .padding(.bottom, 32)
                                                .padding(.horizontal, 16)
                                        } else {
                                            // 如果烘焙度不同，为每个组件分别显示指示器
                                            MultiRoastLevelIndicator(components: currentBean.blendComponents!)
                                                .padding(.top, 4)
                                                .padding(.bottom, 32)
                                                .padding(.horizontal, 16)
                                        }
                                    } else {
                                        // 非拼配咖啡，显示单一烘焙度
                                        RoastLevelIndicator(roastLevel: currentBean.roastLevel)
                                            .padding(.top, 4)
                                            .padding(.bottom, 32)
                                            .padding(.horizontal, 16)
                                    }
                                }
                            }
                            
                            // 下层：详细信息 (当 bean.typeDisplay != "跳过" 时需隐藏)
                            if currentBean.typeDisplay != "跳过" {
                                CustomDivider(color: .functionText)
                                
                                // 使用FlexibleAttributesContainer替代单个属性部分的垂直排列
                                VStack(spacing: 16) {
                                    // 创建属性列表
                                    let attributes: [(String, String?, [(Int, String)]?)] = [
                                        ("类型", currentBean.typeDisplay, nil),
                                        ("产地", currentBean.origin, viewModel.getComponentAttribute(bean: currentBean, keyPath: \BlendComponent.origin)),
                                        ("产区", currentBean.region, viewModel.getComponentAttribute(bean: currentBean, keyPath: \BlendComponent.region)),
                                        ("庄园", currentBean.finca, viewModel.getComponentAttribute(bean: currentBean, keyPath: \BlendComponent.finca)),
                                        ("品种", currentBean.variety, viewModel.getComponentAttribute(bean: currentBean, keyPath: \BlendComponent.variety)),
                                        ("处理法", currentBean.process, viewModel.getComponentAttribute(bean: currentBean, keyPath: \BlendComponent.process)),
                                        // 修改海拔信息逻辑，确保BLEND类型时不显示SINGLE类型的海拔信息
                                        currentBean.type == "BLEND" ?
                                            ("海拔", nil, viewModel.getComponentsAltitude(bean: currentBean)) :
                                        currentBean.altitudeType == "SINGLE" && currentBean.altitudeSingle != nil && currentBean.altitudeSingle != 0 ?
                                            ("海拔", "\(currentBean.altitudeSingle ?? 0)米", nil) :
                                        currentBean.altitudeType == "RANGE" && currentBean.altitudeMin != nil && currentBean.altitudeMax != nil && 
                                        (currentBean.altitudeMin != 0 || currentBean.altitudeMax != 0) ?
                                            ("海拔", "\(currentBean.altitudeMin ?? 0)-\(currentBean.altitudeMax ?? 0)米", nil) :
                                            ("", nil, nil)
                                    ]
                                    
                                    // 过滤掉空值，只保留有内容的属性
                                    let validAttributes = attributes.filter { title, value, components in
                                        return !title.isEmpty && ((value != nil && !value!.isEmpty) || (components != nil && !components!.isEmpty))
                                    }
                                    
                                    // 使用灵活布局展示属性
                                    FlexibleAttributesContainer(attributes: validAttributes)
                                    
                                    // 移除单独的海拔信息部分
                                }
                                .padding(16)
                                .frame(maxWidth: .infinity, alignment: .center)
                            } else {
                                Spacer(minLength: 16)
                            }
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .padding(.horizontal, 30)
                    
                    // 豆子状态指示条
                    BeanStatusBar(bean: currentBean)
                        .frame(width: 6)
                        .padding(.top, 10)
                        .padding(.leading, 16)
                }
                .padding(.top, 8)

                // 附加信息
                SectionDivider(title: "附加信息")
                    .padding(.horizontal, 32)
                VStack(alignment: .leading, spacing: 24) {
                    if currentBean.roastDate != nil {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("烘焙日期")
                                .font(.callout)
                                .fontWeight(.medium)
                                .foregroundColor(.primaryText.opacity(0.4))
                            if currentBean.restPeriodMin != nil && currentBean.restPeriodMax != nil {
                                if currentBean.restPeriodMax == 0 {
                                    // 当最大养豆期为0时，只显示最小养豆期
                                    Text("\(currentBean.roastDate!.formattedDate())（养豆期: \(currentBean.restPeriodMin!)天）")
                                        .font(.body)
                                        .foregroundColor(.primaryText)
                                } else {
                                    // 最大养豆期不为0时，显示范围
                                    Text("\(currentBean.roastDate!.formattedDate())（养豆期: \(currentBean.restPeriodMin!)-\(currentBean.restPeriodMax!)天）")
                                        .font(.body)
                                        .foregroundColor(.primaryText)
                                }
                            } else if currentBean.restPeriodMin != nil {
                                Text("\(currentBean.roastDate!.formattedDate())（养豆期: \(currentBean.restPeriodMin!)天）")
                                    .font(.body)
                                    .foregroundColor(.primaryText)
                            } else {
                                Text(currentBean.roastDate!.formattedDate())
                                    .font(.body)
                                    .foregroundColor(.primaryText)
                            }
                        }
                    }
                    VStack(alignment: .leading, spacing: 4) {
                        Text("购买日期")
                            .font(.callout)
                            .fontWeight(.medium)
                            .foregroundColor(.primaryText.opacity(0.4))
                        Text(currentBean.createdAt.formattedDate())
                            .font(.body)
                            .foregroundColor(.primaryText)
                    }
                    if let price = currentBean.purchasePrice, price > 0 {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("价格")
                                .font(.callout)
                                .fontWeight(.medium)
                                .foregroundColor(.primaryText.opacity(0.4))
                            
                            if let weight = currentBean.bagWeight, weight > 0 {
                                // 计算单价并四舍五入到两位小数
                                let unitPrice = (price / weight)
                                Text("\(NumberFormatters.formatWithPrecision(price, precision: 2))元（\(NumberFormatters.formatWithPrecision(unitPrice, precision: 2))元/克）")
                                    .font(.body)
                                    .foregroundColor(.primaryText)
                            } else {
                                Text(NumberFormatters.formatWithPrecision(price, precision: 2)+"元")
                                    .font(.body)
                                    .foregroundColor(.primaryText)
                            }
                        }
                    }
                    if let bagWeight = currentBean.bagWeight, bagWeight > 0 {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("包装规格")
                                .font(.callout)
                                .fontWeight(.medium)
                                .foregroundColor(.primaryText.opacity(0.4))
                            Text("\(NumberFormatters.formatWithPrecision(bagWeight, precision: 2))g")
                                .font(.body)
                                .foregroundColor(.primaryText)
                        }
                    }
                    if let bagRemain = currentBean.bagRemain {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("库存余量")
                                .font(.callout)
                                .fontWeight(.medium)
                                .foregroundColor(.primaryText.opacity(0.4))
                            if let remainingUses = currentBean.remainingUses {
                                if remainingUses > 0 {
                                    Text("\(NumberFormatters.formatWithPrecision(bagRemain, precision: 2))g（大约还可以用\(remainingUses)次）")
                                        .font(.body)
                                        .foregroundColor(.primaryText)
                                } else {
                                    Text("\(NumberFormatters.formatWithPrecision(bagRemain, precision: 2))g（不足以下次使用）")
                                        .font(.body)
                                        .foregroundColor(.primaryText)
                                }
                            } else {
                                Text("\(NumberFormatters.formatWithPrecision(bagRemain, precision: 2))g")
                                    .font(.body)
                                    .foregroundColor(.primaryText)
                            }
                        }
                    }
                    
                    // 添加回购次数显示
                    if let occurrences = currentBean.occurrences, occurrences.count > 0 {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("回购次数")
                                .font(.callout)
                                .fontWeight(.medium)
                                .foregroundColor(.primaryText.opacity(0.4))
                            
                            if occurrences.count > 1, let avgRepurchaseInterval = currentBean.avgRepurchaseInterval {
                                Text("\(occurrences.count)次（平均间隔\(NumberFormatters.formatTimeInterval(avgRepurchaseInterval))）")
                                    .font(.body)
                                    .foregroundColor(.primaryText)
                            } else {
                                Text("\(occurrences.count)次")
                                    .font(.body)
                                    .foregroundColor(.primaryText)
                            }
                        }
                    }
                    
                    // 回购记录按钮 - 只在有回购记录时显示
                    if let occurrences = currentBean.occurrences, occurrences.count > 0 {
                        NavigationLink(destination: BeanOccurrenceView(bean: currentBean)) {
                            HStack {
                                Image(systemName: "list.bullet")
                                Text("查看回购历史")
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                            }
                            .padding(16)
                            .background(Color.secondaryBg)
                            .cornerRadius(10)
                        }
                    }
                    
                    if !(currentBean.notes?.isEmpty ?? true) {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack(spacing: 8) {
                                Image("note.symbols")
                                    .font(.callout)
                                    .foregroundColor(.functionText)
                                
                                Text("备注")
                                    .font(.callout)
                                    .fontWeight(.medium)
                                    .foregroundColor(.functionText)
                            }
                            
                            Text(currentBean.notes ?? "")
                                .font(.callout)
                                .foregroundColor(.primaryText)
                                .fixedSize(horizontal: false, vertical: true)
                                .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .padding(16)
                        .frame(maxWidth: .infinity)
                        .background(Color.navbarBg)
                        .cornerRadius(8)
                    }
                }
                .padding(.horizontal, 32)

                // 提要信息
                SectionDivider(title: "提要")
                    .padding(.horizontal, 32)
                
                // 居中容器
                VStack(alignment: .center) {
                    // 使用FlowLayout替代HStack，实现自动换行
                    FlowLayout(spacing: 12) {
                        // 已养豆天数
                        if let roastDate = currentBean.roastDate {
                            // 计算烘焙日期至今的天数
                            let daysSince = Calendar.current.dateComponents([.day], from: roastDate, to: Date()).day ?? 0
                            
                            // 只显示60天以内的养豆期
                            if daysSince <= 60 {
                                VStack(spacing: 2) {
                                    Text("已养豆")
                                        .font(.footnote)
                                        .foregroundColor(.detailText)
                                    
                                    Text("\(daysSince)")
                                        .font(.title)
                                        .fontWeight(.bold)
                                        .foregroundColor(.primaryText)
                                    
                                    Text("天")
                                        .font(.caption)
                                        .foregroundColor(.detailText)
                                }
                                .padding(.horizontal, 24)
                                .padding(.vertical, 12)
                                .background(Color.secondaryBg)
                                .cornerRadius(8)
                            }
                        }
                        
                        // 使用次数
                        VStack(spacing: 2) {
                            Text("使用次数")
                                .font(.footnote)
                                .foregroundColor(.detailText)
                            
                            Text("\(currentBean.usageCount ?? 0)")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.primaryText)
                            
                            Text("次")
                                .font(.caption)
                                .foregroundColor(.detailText)
                        }
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(Color.secondaryBg)
                        .cornerRadius(8)

                        // 最近使用天数
                        if let daysLastUse = currentBean.daysSinceLastUse {
                            VStack(spacing: 2) {
                                Text("最近使用")
                                    .font(.footnote)
                                    .foregroundColor(.detailText)
                                
                                // 根据天数显示不同的文本
                                if daysLastUse == 0 {
                                    Text("今天")
                                        .font(.title)
                                        .fontWeight(.bold)
                                        .foregroundColor(.primaryText)
                                    
                                    // 添加空白占位符，确保高度一致
                                    Text(" ")
                                        .font(.caption)
                                        .foregroundColor(.clear)
                                } else if daysLastUse == 1 {
                                    Text("昨天")
                                        .font(.title)
                                        .fontWeight(.bold)
                                        .foregroundColor(.primaryText)
                                    
                                    // 添加空白占位符，确保高度一致
                                    Text(" ")
                                        .font(.caption)
                                        .foregroundColor(.clear)
                                } else {
                                    Text("\(daysLastUse)")
                                        .font(.title)
                                        .fontWeight(.bold)
                                        .foregroundColor(.primaryText)
                                    
                                    Text("天前")
                                        .font(.caption)
                                        .foregroundColor(.detailText)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.secondaryBg)
                            .cornerRadius(8)
                        } else {
                            // 咖啡豆还未使用过的显示状态
                            VStack(spacing: 2) {
                                Text("最近使用")
                                    .font(.footnote)
                                    .foregroundColor(.detailText)
                                
                                Text("-")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.primaryText)
                                
                                Text("尚未使用")
                                    .font(.caption)
                                    .foregroundColor(.detailText)
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.secondaryBg)
                            .cornerRadius(8)
                        }
                        
                        // 平均得分
                        if let avgRating = currentBean.avgRating {
                            VStack(spacing: 2) {
                                Text("平均得分")
                                    .font(.footnote)
                                    .foregroundColor(.detailText)
                                
                                Text("\(NumberFormatters.formatWithPrecision(avgRating, precision: 2))")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.primaryText)
                                
                                Text("分")
                                    .font(.caption)
                                    .foregroundColor(.detailText)
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.secondaryBg)
                            .cornerRadius(8)
                        }

                        // 常用冲煮方式
                        if let equipment = currentBean.mostUsedEquipment, let brewMethodDisplay = equipment.brewMethodDisplay {
                            VStack(spacing: 2) {
                            Text("最常用于")
                                .font(.footnote)
                                .foregroundColor(.detailText)
                            Text(brewMethodDisplay)
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.primaryText)
                            Text("冲煮方法")
                                .font(.caption)
                                .foregroundColor(.detailText)
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.secondaryBg)
                            .cornerRadius(8)
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
                .padding(.horizontal, 32)
                .padding(.bottom, 8)
                
                // 添加品鉴维度展示
                let hasDimensions = currentBean.dimensionsAvg != nil && !currentBean.dimensionsAvg!.isEmpty
                let hasFlavorTags = currentBean.uniqueFlavorTags != nil && !currentBean.uniqueFlavorTags!.isEmpty
                
                if hasFlavorTags || hasDimensions {
                    SectionDivider(title: "我的品鉴笔记")
                        .padding(.horizontal, 32)
                        .padding(.top, 8)
                    
                    VStack(spacing: 16) {
                        // 如果有风味标签，则显示风味标签
                        if let flavorTags = currentBean.uniqueFlavorTags, !flavorTags.isEmpty {
                            VStack(spacing: 8) {
                                FlowLayout(spacing: 8) {
                                    ForEach(flavorTags, id: \.id) { tag in
                                        Text(tag.name)
                                            .font(.subheadline)
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 6)
                                            .background(
                                                // 检查标签是否存在于tasteNotes中
                                                currentBean.tasteNotes?.contains(tag.name) == true 
                                                ? Color.error.opacity(0.1) 
                                                : Color.primaryAccent.opacity(0.1)
                                            )
                                            .cornerRadius(15)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 15)
                                                    .stroke(
                                                        // 检查标签是否存在于tasteNotes中
                                                        currentBean.tasteNotes?.contains(tag.name) == true
                                                        ? Color.error.opacity(0.3)
                                                        : Color.primaryAccent.opacity(0.3),
                                                        lineWidth: 1
                                                    )
                                            )
                                            .foregroundColor(
                                                // 检查标签是否存在于tasteNotes中
                                                currentBean.tasteNotes?.contains(tag.name) == true
                                                ? .error
                                                : .primaryText
                                            )
                                    }
                                    
                                    // 添加风味准确度标签
                                    if let flavorAccuracy = currentBean.flavorAccuracy {
                                        HStack(spacing: 4) {
                                            Image("overlap.symbols")
                                            Text("\(flavorAccuracy)%")
                                        }
                                        .font(.subheadline)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color.primaryText.opacity(0.1))
                                        .cornerRadius(15)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 15)
                                                .stroke(Color.primaryText.opacity(0.3), lineWidth: 1)
                                        )
                                        .onTapGesture {
                                            viewModel.showingFlavorAccuracyInfo = true
                                        }
                                    }
                                }
                                .padding(.horizontal, 32)
                            }
                        }
                        
                        // 显示雷达图风格的品鉴维度
                        if let dimensions = currentBean.dimensionsAvg, !dimensions.isEmpty {
                            if dimensions["avg_aroma"] != nil || 
                               dimensions["avg_acidity"] != nil || 
                               dimensions["avg_sweetness"] != nil || 
                               dimensions["avg_body"] != nil || 
                               dimensions["avg_aftertaste"] != nil {
                                
                                RecordFlavorDimensionChart(
                                    aroma: Int(dimensions["avg_aroma"] ?? 0),
                                    acidity: Int(dimensions["avg_acidity"] ?? 0),
                                    sweetness: Int(dimensions["avg_sweetness"] ?? 0),
                                    aftertaste: Int(dimensions["avg_aftertaste"] ?? 0),
                                    bodyValue: Int(dimensions["avg_body"] ?? 0)
                                )
                                .frame(height: 240)
                                .padding(.horizontal, 16)
                            }

                            // 品鉴笔记次数统计
                            if let tastingCount = currentBean.tastingCount, tastingCount > 1 {
                                HStack(alignment: .top, spacing: 8) {
                                    Image(systemName:"info.circle")
                                    Text("基于\(tastingCount)条品鉴记录汇总后计算平均值。")
                                }
                                .font(.footnote)
                                .foregroundColor(Color.noteText)
                                .padding(.bottom, 20)
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }
            }
            .foregroundColor(.primaryText)
        }
        .padding(.vertical, 16)
    }
    
    // 获取维度名称
    func getDimensionName(_ key: String) -> String {
        switch key {
        case "aroma": return "香气"
        case "acidity": return "酸度"
        case "sweetness": return "甜度"
        case "body": return "口感"
        case "aftertaste": return "余韵"
        default: return key
        }
    }
    
    // 雷达图实现
    struct RadarChart: View {
        var keys: [String]
        var values: [Double]
        var strokeColor: Color = .functionText
        let maxValue: Double // 最大值，咖啡评分通常为5分
        @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听
        
        // 基于当前主题的颜色
        private var backgroundColor: Color {
            Color.functionText.opacity(0.2)
        }
        private var borderColor: Color {
            Color.functionText.opacity(0.8)
        }
        private var pointColor: Color {
            Color.primaryAccent
        }
        private var gridColor: Color {
            Color.primaryText.opacity(0.1)
        }
        private var labelColor: Color {
            Color.primaryText
        }

        var body: some View {
            GeometryReader { geometry in
                self.makeRadarChart(geometry: geometry)
            }
            .background(Color.primaryBg)
        }

        private func makeRadarChart(geometry: GeometryProxy) -> some View {
            let center = CGPoint(x: geometry.size.width / 2, y: geometry.size.height / 2)
            let radius = min(geometry.size.width, geometry.size.height) / 2 * 0.8
            let angleAdjustment = -CGFloat.pi / 2 // 从上方开始
            let memoryLevels = 6 // 网格线数量，包括中心和边界

            return ZStack {
                // 绘制背景网格圆环
                ForEach(0..<memoryLevels, id: \.self) { level in
                    Circle()
                        .stroke(gridColor, lineWidth: 1)
                        .frame(width: radius * 2 * CGFloat(level) / CGFloat(memoryLevels - 1), height: radius * 2 * CGFloat(level) / CGFloat(memoryLevels - 1))
                        .position(center)
                }
                
                // 数据点连线并填充
                Path { path in
                    for (index, value) in values.enumerated() {
                        let angle = 2 * .pi / CGFloat(values.count) * CGFloat(index) + angleAdjustment
                        let pointRadius = radius * (value / maxValue) // 按最大值5进行归一化
                        let point = CGPoint(x: center.x + cos(angle) * pointRadius, y: center.y + sin(angle) * pointRadius)

                        if index == 0 {
                            path.move(to: point)
                        } else {
                            path.addLine(to: point)
                        }
                    }
                    path.closeSubpath()
                }
                .fill(backgroundColor)
                .overlay(
                    Path { path in
                        for (index, value) in values.enumerated() {
                            let angle = 2 * .pi / CGFloat(values.count) * CGFloat(index) + angleAdjustment
                            let pointRadius = radius * (value / maxValue)
                            let point = CGPoint(x: center.x + cos(angle) * pointRadius, y: center.y + sin(angle) * pointRadius)

                            if index == 0 {
                                path.move(to: point)
                            } else {
                                path.addLine(to: point)
                            }
                        }
                        path.closeSubpath()
                    }
                    .stroke(borderColor, lineWidth: 2)
                )
                
                // 添加数据点
                ForEach(Array(values.enumerated()), id: \.offset) { index, value in
                    let angle = 2 * .pi / CGFloat(values.count) * CGFloat(index) + angleAdjustment
                    let pointRadius = radius * (value / maxValue)
                    let point = CGPoint(x: center.x + cos(angle) * pointRadius, y: center.y + sin(angle) * pointRadius)
                    
                    Circle()
                        .fill(pointColor)
                        .frame(width: 8, height: 8)
                        .overlay(
                            Circle()
                                .stroke(Color.primaryBg, lineWidth: 1)
                                .frame(width: 8, height: 8)
                        )
                        .position(point)
                }
                
                // 添加五个文本标签 - 准确定位在各自轴线延伸最外端
                ForEach(0..<keys.count, id: \.self) { index in
                    let angle = 2 * .pi / CGFloat(keys.count) * CGFloat(index) + angleAdjustment
                    let labelDistance = radius + 25 // 增加标签与雷达图边缘的距离
                    let x = center.x + cos(angle) * labelDistance
                    let y = center.y + sin(angle) * labelDistance
                    
                    Text(keys[index])
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(labelColor)
                        .position(x: x, y: y)
                }
                
                // 在每层圆环上添加刻度值，放在最上层确保覆盖其他元素
                ForEach(1..<memoryLevels, id: \.self) { level in
                    Text("\(level)")
                        .font(.system(size: 10))
                        .foregroundColor(.detailText)
                        .position(x: center.x, y: center.y - radius * CGFloat(level) / CGFloat(memoryLevels - 1))
                }
            }
        }
    }
    
    // 添加FlowLayout组件
    struct FlowLayout: Layout {
        let spacing: CGFloat
        
        init(spacing: CGFloat = 8) {
            self.spacing = spacing
        }
        
        func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
            let rows = computeRows(proposal: proposal, subviews: subviews)
            var height: CGFloat = 0
            var width: CGFloat = 0
            
            for (i, row) in rows.enumerated() {
                let rowSize = rowSize(for: row)
                height += rowSize.height
                if i < rows.count - 1 {
                    height += spacing
                }
                width = max(width, rowSize.width)
            }
            
            return CGSize(width: width, height: height)
        }
        
        func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
            let rows = computeRows(proposal: proposal, subviews: subviews)
            var y = bounds.minY
            
            for row in rows {
                var x = bounds.minX
                let rowSize = rowSize(for: row)
                
                for view in row {
                    let viewSize = view.sizeThatFits(.unspecified)
                    view.place(
                        at: CGPoint(x: x, y: y + (rowSize.height - viewSize.height) / 2),
                        proposal: ProposedViewSize(width: viewSize.width, height: viewSize.height)
                    )
                    x += viewSize.width + spacing
                }
                
                y += rowSize.height + spacing
            }
        }
        
        private func computeRows(proposal: ProposedViewSize, subviews: Subviews) -> [[LayoutSubview]] {
            var rows: [[LayoutSubview]] = [[]]
            var currentRow = 0
            var remainingWidth = proposal.width ?? .infinity
            
            for view in subviews {
                let viewSize = view.sizeThatFits(.unspecified)
                
                if viewSize.width > remainingWidth {
                    currentRow += 1
                    rows.append([])
                    remainingWidth = proposal.width ?? .infinity
                }
                
                rows[currentRow].append(view)
                remainingWidth -= viewSize.width + spacing
            }
            
            return rows
        }
        
        private func rowSize(for row: [LayoutSubview]) -> CGSize {
            var width: CGFloat = 0
            var height: CGFloat = 0
            
            for (index, view) in row.enumerated() {
                let size = view.sizeThatFits(.unspecified)
                width += size.width
                if index < row.count - 1 {
                    width += spacing
                }
                height = max(height, size.height)
            }
            
            return CGSize(width: width, height: height)
        }
    }
    
    // 品鉴维度雷达图
    struct RecordFlavorDimensionChart: View {
        let aroma: Int
        let acidity: Int
        let sweetness: Int
        let aftertaste: Int
        let bodyValue: Int  // 将body改为bodyValue以避免命名冲突
        @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听
        
        private var keys: [String] {
            ["香气", "酸质", "甜度", "余韵", "醇厚"]
        }
        
        private var values: [Double] {
            [Double(aroma), Double(acidity), Double(sweetness), Double(aftertaste), Double(bodyValue)]
        }
        
        var body: some View {
            RadarChart(keys: keys, values: values, strokeColor: .functionText, maxValue: 5.0)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding()
        }
    }
    
    // 用于长截图的包装视图
    var screenshotWrapperView: some View {
        ZStack {
            // 外层背景色
            Color(red: 0xAB/255.0, green: 0xB8/255.0, blue: 0xC3/255.0)
                .edgesIgnoringSafeArea(.all)
            
            // 通过嵌套ZStack确保阴影只应用于最外层
            ZStack {
                // 内容卡片背景 - 用于应用圆角、边框和阴影
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.primaryBg)
                    .shadow(color: Color.black.opacity(0.15), radius: 15, x: 0, y: 12)
                
                // 内容区域 - 不应用阴影
                VStack(spacing: 0) {
                    // 去掉顶部导航标题
                    
                    // 使用专为截图优化的内容视图
                    shareDetailContent
                    
                    // 底部分享水印 - 直接作为卡片内容的一部分
                    HStack {
                        Spacer()
                Text("via 咖啡札记")
                    .font(.caption)
                    .foregroundColor(.detailText)
                            .padding(.trailing, 16)
                    }
                    .padding(.vertical, 8)
                }
            }
                    .padding(.horizontal, 18)
            .padding(.vertical, 20)
        }
    }
    
    // 专为截图优化的内容视图，使用纯SwiftUI组件
    var shareDetailContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 卡片视图
            ZStack(alignment: .leading) {
                // 卡片主体 - 居中展示
                ZStack(alignment: .topLeading) {
                    // 外边框 - 恢复原始的边框样式
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.primaryBg)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.functionText, lineWidth: 2)
                        )
                    
                    VStack(spacing: 0) {
                        // 上层：风味部分 (只有在有风味标签时显示)
                        if let tasteNotes = currentBean.tasteNotes, !tasteNotes.isEmpty {
                            VStack(alignment: .trailing, spacing: 8) {
                                Text("风味")
                                    .font(.footnote)
                                    .foregroundColor(.error)
                                    .frame(maxWidth: .infinity, alignment: .trailing)
                                    .padding(.top, 12)
                                    .padding(.trailing, 16)
                                
                                // 风味标签 (每行2个)
                                VStack(alignment: .center, spacing: 6) {
                                    ForEach(0..<(tasteNotes.count + 1) / 2, id: \.self) { row in
                                        HStack(spacing: 4) {
                                            let firstIndex = row * 2
                                            let secondIndex = firstIndex + 1
                                            
                                            // 检查是否是最后一行且是奇数个标签
                                            let isLastOddRow = tasteNotes.count % 2 != 0 && firstIndex == tasteNotes.count - 1
                                            
                                            if isLastOddRow {
                                                // 奇数个风味标签时，最后一个标签居中显示且不带顿号
                                                Spacer()
                                                Text(tasteNotes[firstIndex])
                                                    .frame(maxWidth: .infinity, alignment: .center)
                                                Spacer()
                                            } else {
                                                // 常规的双标签行
                                                if firstIndex < tasteNotes.count {
                                                    Text(tasteNotes[firstIndex])
                                                        .frame(maxWidth: .infinity, alignment: .trailing)
                                                }
                                                
                                                Text("、")
                                                    .foregroundColor(.gray)
                                                
                                                if secondIndex < tasteNotes.count {
                                                    Text(tasteNotes[secondIndex])
                                                        .frame(maxWidth: .infinity, alignment: .leading)
                                                } else {
                                                    Spacer()
                                                }
                                            }
                                        }
                                    }
                                }
                                .font(.callout)
                                .padding(.horizontal, 16)
                                .padding(.bottom, 32)
                            }
                            
                            CustomDivider(color: .functionText)
                        }
                        
                        // 中层：豆子基本信息
                        VStack(alignment: .center, spacing: 8) {
                            // 豆子名称
                            ZStack(alignment: .topTrailing) {
                                Text(currentBean.name)
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .multilineTextAlignment(.center)
                                    .padding(.top, 32)
                                    .padding(.horizontal, 16)
                                
                                // 低因角标
                                if currentBean.isDecaf {
                                    Image("decaf.symbols")
                                        .offset(x: 8, y: 30) // 调整位置到右上角
                                        .foregroundColor(.linkText)
                                }
                            }
                            .frame(maxWidth: .infinity)
                            
                            // 豆商名称
                            Text(currentBean.roaster)
                                .font(.headline)
                                .fontWeight(.regular)
                                .foregroundColor(.gray)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 14)
                            
                            // 烘焙度 (当 bean.typeDisplay != "跳过" 时需隐藏)
                            if currentBean.typeDisplay != "跳过" {
                                // 判断是否为拼配咖啡 且 有blendComponents
                                if currentBean.type == "BLEND" && currentBean.blendComponents != nil && !currentBean.blendComponents!.isEmpty {
                                    // 获取所有组件的烘焙度
                                    let roastLevels = currentBean.blendComponents!.map { $0.roastLevel }
                                    
                                    // 判断所有烘焙度是否相同
                                    let allSameLevel = roastLevels.allSatisfy { $0 == roastLevels.first }
                                    
                                    if allSameLevel {
                                        // 如果所有烘焙度相同，只显示一个不带序号的指示器
                                        RoastLevelIndicator(roastLevel: roastLevels.first ?? currentBean.roastLevel)
                                            .padding(.top, 4)
                                            .padding(.bottom, 32)
                                            .padding(.horizontal, 16)
                                    } else {
                                        // 如果烘焙度不同，为每个组件分别显示指示器
                                        MultiRoastLevelIndicator(components: currentBean.blendComponents!)
                                            .padding(.top, 4)
                                            .padding(.bottom, 32)
                                            .padding(.horizontal, 16)
                                    }
                                } else {
                                    // 非拼配咖啡，显示单一烘焙度
                                    RoastLevelIndicator(roastLevel: currentBean.roastLevel)
                                        .padding(.top, 4)
                                        .padding(.bottom, 32)
                                        .padding(.horizontal, 16)
                                }
                            }
                        }
                        
                        // 下层：详细信息 (当 bean.typeDisplay != "跳过" 时需隐藏)
                        if currentBean.typeDisplay != "跳过" {
                            CustomDivider(color: .functionText)
                            
                            // 使用FlexibleAttributesContainer替代单个属性部分的垂直排列
                            VStack(spacing: 16) {
                                // 创建属性列表
                                let attributes: [(String, String?, [(Int, String)]?)] = [
                                    ("类型", currentBean.typeDisplay, nil),
                                    ("产地", currentBean.origin, viewModel.getComponentAttribute(bean: currentBean, keyPath: \BlendComponent.origin)),
                                    ("产区", currentBean.region, viewModel.getComponentAttribute(bean: currentBean, keyPath: \BlendComponent.region)),
                                    ("庄园", currentBean.finca, viewModel.getComponentAttribute(bean: currentBean, keyPath: \BlendComponent.finca)),
                                    ("品种", currentBean.variety, viewModel.getComponentAttribute(bean: currentBean, keyPath: \BlendComponent.variety)),
                                    ("处理法", currentBean.process, viewModel.getComponentAttribute(bean: currentBean, keyPath: \BlendComponent.process)),
                                    // 修改海拔信息逻辑，确保BLEND类型时不显示SINGLE类型的海拔信息
                                    currentBean.type == "BLEND" ?
                                        ("海拔", nil, viewModel.getComponentsAltitude(bean: currentBean)) :
                                    currentBean.altitudeType == "SINGLE" && currentBean.altitudeSingle != nil && currentBean.altitudeSingle != 0 ?
                                        ("海拔", "\(currentBean.altitudeSingle ?? 0)米", nil) :
                                    currentBean.altitudeType == "RANGE" && currentBean.altitudeMin != nil && currentBean.altitudeMax != nil && 
                                    (currentBean.altitudeMin != 0 || currentBean.altitudeMax != 0) ?
                                        ("海拔", "\(currentBean.altitudeMin ?? 0)-\(currentBean.altitudeMax ?? 0)米", nil) :
                                        ("", nil, nil)
                                ]
                                
                                // 过滤掉空值，只保留有内容的属性
                                let validAttributes = attributes.filter { title, value, components in
                                    return !title.isEmpty && ((value != nil && !value!.isEmpty) || (components != nil && !components!.isEmpty))
                                }
                                
                                // 使用灵活布局展示属性
                                FlexibleAttributesContainer(attributes: validAttributes)
                            }
                            .padding(16)
                            .frame(maxWidth: .infinity, alignment: .center)
                        } else {
                            Spacer(minLength: 16)
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
                .padding(.top, 16)
                .padding(.horizontal, 8)
                
                // 豆子状态指示条
                BeanStatusBar(bean: currentBean)
                    .frame(width: 6)
                    .padding(.top, 26)
                    .padding(.leading, -4)
            }
            .padding(.top, 8)

            // 移除附加信息和提要部分
            
            // 添加品鉴维度展示
            let hasDimensions = currentBean.dimensionsAvg != nil && !currentBean.dimensionsAvg!.isEmpty
            let hasFlavorTags = currentBean.uniqueFlavorTags != nil && !currentBean.uniqueFlavorTags!.isEmpty
            
            if hasFlavorTags || hasDimensions {
                SectionDivider(title: "我的品鉴笔记")
                    .padding(.horizontal, 32)
                    .padding(.top, 8)
                
                VStack(spacing: 16) {
                    // 如果有风味标签，则显示风味标签
                    if let flavorTags = currentBean.uniqueFlavorTags, !flavorTags.isEmpty {
                        VStack(spacing: 8) {
                            FlowLayout(spacing: 8) {
                                ForEach(flavorTags, id: \.id) { tag in
                                    Text(tag.name)
                                        .font(.subheadline)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(
                                            // 检查标签是否存在于tasteNotes中
                                            currentBean.tasteNotes?.contains(tag.name) == true 
                                            ? Color.error.opacity(0.1) 
                                            : Color.primaryAccent.opacity(0.1)
                                        )
                                        .cornerRadius(15)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 15)
                                                .stroke(
                                                    // 检查标签是否存在于tasteNotes中
                                                    currentBean.tasteNotes?.contains(tag.name) == true
                                                    ? Color.error.opacity(0.3)
                                                    : Color.primaryAccent.opacity(0.3),
                                                    lineWidth: 1
                                                )
                                        )
                                        .foregroundColor(
                                            // 检查标签是否存在于tasteNotes中
                                            currentBean.tasteNotes?.contains(tag.name) == true
                                            ? .error
                                            : .primaryText
                                        )
                                }
                                
                                // 添加风味准确度标签
                                if let flavorAccuracy = currentBean.flavorAccuracy {
                                    HStack(spacing: 4) {
                                        Image("overlap.symbols")
                                        Text("\(flavorAccuracy)%")
                                    }
                                    .font(.subheadline)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(Color.primaryText.opacity(0.1))
                                    .cornerRadius(15)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 15)
                                            .stroke(Color.primaryText.opacity(0.3), lineWidth: 1)
                                    )
                                }
                            }
                            .padding(.horizontal, 32)
                        }
                    }
                    
                    // 显示雷达图风格的品鉴维度
                    if let dimensions = currentBean.dimensionsAvg, !dimensions.isEmpty {
                        if dimensions["avg_aroma"] != nil || 
                           dimensions["avg_acidity"] != nil || 
                           dimensions["avg_sweetness"] != nil || 
                           dimensions["avg_body"] != nil || 
                           dimensions["avg_aftertaste"] != nil {
                            
                            RecordFlavorDimensionChart(
                                aroma: Int(dimensions["avg_aroma"] ?? 0),
                                acidity: Int(dimensions["avg_acidity"] ?? 0),
                                sweetness: Int(dimensions["avg_sweetness"] ?? 0),
                                aftertaste: Int(dimensions["avg_aftertaste"] ?? 0),
                                bodyValue: Int(dimensions["avg_body"] ?? 0)
                            )
                            .frame(height: 240)
                            .padding(.horizontal, 16)
                        }

                        // 品鉴笔记次数统计
                        if let tastingCount = currentBean.tastingCount, tastingCount > 1 {
                            HStack(alignment: .top, spacing: 8) {
                                Image(systemName:"info.circle")
                                Text("基于\(tastingCount)条品鉴记录汇总后计算平均值。")
                            }
                            .font(.footnote)
                            .foregroundColor(Color.noteText)
                            .padding(.bottom, 20)
                        }
                    }
                }
                .padding(.vertical, 8)
            }
        }
        .padding(.horizontal)
        .padding(.bottom, 16)
    }
    
    @MainActor private func generateAndShareImage() async {
        // 确保不在生成图片过程中重复触发
        guard !viewState.isGeneratingImage else { 
            print("⚠️ 已在生成图片，跳过")
            return 
        }
        
        // 标记正在生成图片，显示加载指示器
        viewState.isGeneratingImage = true
        
        // 使用UIKit屏幕尺寸来设置宽度
        let screenWidth = UIScreen.main.bounds.width
        
        // 确保主题颜色最新
        themeManager.updateThemeColorsOnly()
        
        // 延时一小段时间确保视图已加载完毕
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        
        do {
            // 首先计算内容的实际高度
            let sizingContent = screenshotWrapperView
                .frame(width: screenWidth)
                .fixedSize(horizontal: true, vertical: true) // 让高度适应内容
                
            // 使用GeometryReader测量内容实际高度（放在后台队列中执行以不阻塞UI）
            let contentSize = await withCheckedContinuation { (continuation: CheckedContinuation<CGSize, Never>) in
                DispatchQueue.main.async {
                    let controller = UIHostingController(rootView: sizingContent)
                    controller.view.layoutIfNeeded()
                    
                    // 获取实际内容尺寸并添加一些缓冲区
                    var size = controller.sizeThatFits(in: CGSize(width: screenWidth, height: UIView.layoutFittingCompressedSize.height))
                    
                    // 确保内容大小是有效的
                    if size.height <= 0 {
                        size.height = UIScreen.main.bounds.height
                    }
                    
                    continuation.resume(returning: size)
                }
            }
            
            // 创建一个渲染器并设置内容
            let renderer = ImageRenderer(
                content: screenshotWrapperView
                    .frame(width: screenWidth, height: contentSize.height)
                    .environment(\.colorScheme, colorScheme) // 确保渲染时使用当前的颜色方案
            )
            
            // 设置正确的显示比例
            renderer.scale = displayScale
            
            // 确保生成图像的上下文是不透明的
            renderer.proposedSize = ProposedViewSize(width: screenWidth, height: contentSize.height)
            
            if let uiImage = renderer.uiImage {
                // 保存为PNG格式而非JPEG
                let finalImage: UIImage
                if let pngData = uiImage.pngData(),
                   let pngImage = UIImage(data: pngData) {
                    finalImage = pngImage
                } else {
                    finalImage = uiImage
                }
                
                // 等待一小段时间确保图片已经准备好
                try? await Task.sleep(nanoseconds: 200_000_000) // 0.2秒
                
                // 使用UIActivityViewController分享图片
                let activityVC = UIActivityViewController(
                    activityItems: [finalImage],
                    applicationActivities: nil
                )
                
                // 在iPad上，设置弹出位置
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let rootViewController = windowScene.windows.first?.rootViewController {
                    
                    if UIDevice.current.userInterfaceIdiom == .pad {
                        activityVC.popoverPresentationController?.sourceView = rootViewController.view
                        activityVC.popoverPresentationController?.sourceRect = CGRect(
                            x: UIScreen.main.bounds.width / 2,
                            y: UIScreen.main.bounds.height / 2,
                            width: 0,
                            height: 0
                        )
                    }
                    
                    // 呈现分享控制器
                    await MainActor.run {
                        print("📸 图片生成成功，准备显示系统分享表单")
                        rootViewController.present(activityVC, animated: true) {
                            // 分享控制器显示后，重置生成状态
                            self.viewState.isGeneratingImage = false
                        }
                    }
                } else {
                    // 如果无法获取rootViewController，则只重置状态
                    await MainActor.run {
                        self.viewState.isGeneratingImage = false
                        print("⚠️ 无法获取rootViewController来显示分享表单")
                    }
                }
            } else {
                await MainActor.run {
                    viewState.isGeneratingImage = false
                    print("⚠️ 渲染器未能生成图片")
                    
                    // 显示错误提示
                    showErrorToast("生成图片失败")
                }
            }
        } catch {
            print("❌ 生成图片时发生错误: \(error.localizedDescription)")
            await MainActor.run {
                viewState.isGeneratingImage = false
                showErrorToast("生成图片失败: \(error.localizedDescription)")
            }
        }
    }
    
    // 显示错误提示
    private func showErrorToast(_ message: String) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first
        else { return }
        
        let toastView = UIView()
        toastView.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.9)
        toastView.layer.cornerRadius = 10
        toastView.layer.shadowColor = UIColor.black.cgColor
        toastView.layer.shadowOpacity = 0.2
        toastView.layer.shadowOffset = CGSize(width: 0, height: 2)
        toastView.layer.shadowRadius = 4
        
        let label = UILabel()
        label.text = message
        label.textColor = UIColor.label
        label.textAlignment = .center
        label.numberOfLines = 0
        
        toastView.addSubview(label)
        window.addSubview(toastView)
        
        label.translatesAutoresizingMaskIntoConstraints = false
        toastView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            label.leadingAnchor.constraint(equalTo: toastView.leadingAnchor, constant: 16),
            label.trailingAnchor.constraint(equalTo: toastView.trailingAnchor, constant: -16),
            label.topAnchor.constraint(equalTo: toastView.topAnchor, constant: 12),
            label.bottomAnchor.constraint(equalTo: toastView.bottomAnchor, constant: -12),
            
            toastView.centerXAnchor.constraint(equalTo: window.centerXAnchor),
            toastView.bottomAnchor.constraint(equalTo: window.safeAreaLayoutGuide.bottomAnchor, constant: -30),
            toastView.widthAnchor.constraint(lessThanOrEqualTo: window.widthAnchor, constant: -40)
        ])
        
        // 动画显示
        toastView.alpha = 0
        UIView.animate(withDuration: 0.3) {
            toastView.alpha = 1
        }
        
        // 2秒后消失
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            UIView.animate(withDuration: 0.3, animations: {
                toastView.alpha = 0
            }) { _ in
                toastView.removeFromSuperview()
            }
        }
    }
}

// 豆子状态指示条
struct BeanStatusBar: View {
    let bean: CoffeeBean
    
    var body: some View {
        // 垂直排列多个状态指示条
        VStack(spacing: 2) {  // 减少间距，使指示条更紧凑
            // 归档状态
            if bean.isArchived {
                statusIndicator(color: .navbarBg)
            } else {
                // 显示基础状态 (baseStatus而不是status，因为status会将inUse+bestFlavor合并为bestFlavor)
                let baseStatus = bean.baseStatus
                if baseStatus == .resting {
                    statusIndicator(color: .noteText)
                } else if baseStatus == .outOfStock {
                    statusIndicator(color: .error)
                } else if baseStatus == .inUse {
                    statusIndicator(color: .green)
                }
                
                // 单独判断最佳赏味期，与基础状态分开显示
                if bean.isInBestFlavorPeriod {
                    statusIndicator(color: .linkText)
                }
            }
            
            // 首选状态始终单独判断
            if bean.isFavorite {
                statusIndicator(color: .yellow)
            }
            
            // 添加一个占位Spacer，让指示条始终保持在上方
            Spacer()
        }
        // 移除minHeight约束，让高度自动适应卡片
    }
    
    // 单个状态指示条
    private func statusIndicator(color: Color) -> some View {
        Rectangle()
            .fill(color)
            .cornerRadius(3)
            .frame(height: 26)  // 调整高度为18
            // 不需要垂直内边距，由VStack的spacing控制
    }
    
    // 获取状态对应的颜色
    private func getStatusColor(_ status: BeanStatus) -> Color {
        switch status {
        case .resting:
            return .noteText
        case .inUse:
            return .green
        case .outOfStock:
            return .error
        case .bestFlavor:
            return .linkText
        }
    }
}

// 烘焙度指示器
struct RoastLevelIndicator: View {
    let roastLevel: Int
    
    // 定义烘焙度名称映射
    private let roastLevelNames = [
        1: "极浅烘",
        2: "浅烘",
        3: "中浅烘",
        4: "中烘",
        5: "中深烘",
        6: "深烘",
        7: "极深烘"
    ]
    
    var body: some View {
        TooltipContainer(
            tooltipItems: [
                TooltipItem(title: "\(roastLevelNames[roastLevel] ?? "未知")")
            ],
            direction: .top,
            dismissAfter: 2.5,
            bgColor: .primaryText,
            textColor: .primaryBg
        ) {
            HStack(spacing: 5) {
                ForEach(1...7, id: \.self) { level in
                    Image("defaultAvatar.symbols")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: level == roastLevel ? 22 : 14, height: level == roastLevel ? 22 : 14)
                        .foregroundColor(level == roastLevel ? .functionText : .noteText)
                }
            }
        }
    }
}

// 多组件烘焙度指示器 - 显示每个组件的烘焙度
struct MultiRoastLevelIndicator: View {
    let components: [BlendComponent]
    
    // 定义烘焙度名称映射
    private let roastLevelNames = [
        1: "极浅烘",
        2: "浅烘",
        3: "中浅烘",
        4: "中烘",
        5: "中深烘",
        6: "深烘",
        7: "极深烘"
    ]
    
    var body: some View {
        VStack(spacing: 8) {
            ForEach(Array(components.enumerated()), id: \.element.id) { index, component in
                HStack {
                    // 显示组件序号标签
                    Text("#\(index + 1)")
                        .font(.footnote)
                        .foregroundColor(.gray)
                        .frame(width: 25, alignment: .leading)
                    
                    // 烘焙度指示器
                    TooltipContainer(
                        tooltipItems: [
                            TooltipItem(title: "\(roastLevelNames[component.roastLevel] ?? "未知")")
                        ],
                        direction: .top,
                        dismissAfter: 2.5,
                        bgColor: .primaryText,
                        textColor: .primaryBg
                    ) {
                        HStack(spacing: 5) {
                            ForEach(1...7, id: \.self) { level in
                                Image("defaultAvatar.symbols")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: level == component.roastLevel ? 22 : 14, height: level == component.roastLevel ? 22 : 14)
                                    .foregroundColor(level == component.roastLevel ? .functionText : .noteText)
                            }
                        }
                    }
                }
            }
        }
    }
}

// 豆子特性区域
struct BeanAttributeSection: View {
    let title: String
    let value: String?
    let components: [(Int, String)]?
    
    var body: some View {
        if (value != nil && !value!.isEmpty) || (components != nil && !components!.isEmpty) {
            VStack(alignment: .center, spacing: 4) {
                Text(title)
                    .font(.footnote)
                    .foregroundColor(.archivedText)
                    .frame(maxWidth: .infinity, alignment: .center)
                
                if let value = value, !value.isEmpty {
                    Text(value)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primaryText)
                        .frame(maxWidth: .infinity, alignment: .center)
                }
                
                if let components = components {
                    ForEach(components, id: \.0) { index, value in
                        // 当总组件只有1个时不显示序号
                        if components.count == 1 {
                            Text(value)
                                .font(.body)
                                .fontWeight(.medium)
                                .foregroundColor(.primaryText)
                                .frame(maxWidth: .infinity, alignment: .center)
                        } else {
                            Text("#\(index+1) \(value)")
                                .font(.body)
                                .fontWeight(.medium)
                                .foregroundColor(.primaryText)
                                .frame(maxWidth: .infinity, alignment: .center)
                        }
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .center)
            .padding(16)
        }
    }
}

// 灵活布局属性容器
struct FlexibleAttributesContainer: View {
    let attributes: [(String, String?, [(Int, String)]?)]
    
    // 缓存结构
    struct Cache {
        struct Row {
            let items: [BeanAttributeItem]
            let size: CGSize
        }
        
        let rows: [Row]
        let height: CGFloat
    }
    
    // 表示单个属性项
    struct BeanAttributeItem: Identifiable {
        let id = UUID()
        let title: String
        let value: String?
        let components: [(Int, String)]?
        var size: CGSize = .zero
    }
    
    var body: some View {
        // 使用自定义Layout
        FlexableStack(spacing: 12, alignment: .center) {
            ForEach(attributeItems) { item in
                BeanAttributeSection(
                    title: item.title,
                    value: item.value,
                    components: item.components
                )
                .fixedSize()  // 确保每个项按其固有尺寸布局
            }
        }
    }
    
    // 将属性转换为BeanAttributeItem数组
    private var attributeItems: [BeanAttributeItem] {
        attributes.map { title, value, components in
            BeanAttributeItem(title: title, value: value, components: components)
        }
    }
}

// 实现灵活布局
struct FlexableStack: Layout {
    var spacing: CGFloat = 10
    var alignment: HorizontalAlignment = .leading
    
    // 缓存结构，根据文章实现
    struct Cache {
        let rows: [Row]
        let height: CGFloat
        
        struct Row {
            let viewsSizes: [CGSize]
            let size: CGSize
        }
    }
    
    func makeCache(subviews: Subviews) -> Cache {
        return .init(rows: [], height: 0)
    }
    
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout Cache) -> CGSize {
        let maxWidth = proposal.width ?? 0
        cache = calculateRows(maxWidth, proposal: proposal, subviews: subviews)
        return .init(width: maxWidth, height: cache.height)
    }
    
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout Cache) {
        var origin = bounds.origin
        var subviews = subviews
        
        for row in cache.rows {
            // 根据对齐方式计算行起始位置
            origin.x = getRowXOrigin(bounds: bounds, rowWidth: row.size.width)
            
            for size in row.viewsSizes {
                guard let view = subviews.popFirst() else { return }
                view.place(at: origin, proposal: .init(size))
                origin.x += size.width + spacing
            }
            
            // 移动到下一行
            origin.y += row.size.height + spacing
        }
    }
    
    // 计算单行信息
    private func calculateRow(_ maxWidth: CGFloat, proposal: ProposedViewSize, subviews: inout Subviews) -> Cache.Row? {
        var viewSizes: [CGSize] = []
        var rowHeight: CGFloat = 0
        var origin = CGRect.zero.origin
        let hasSpace: (CGSize) -> Bool = { (origin.x + $0.width + spacing) <= maxWidth }
        
        // 保持迭代直到行填满
        while true {
            guard let view = subviews.first else {
                let rowSize = CGSize(width: origin.x - spacing, height: rowHeight)
                return viewSizes.isEmpty ? nil : .init(viewsSizes: viewSizes, size: rowSize)
            }
            
            let size = view.sizeThatFits(proposal)
            if !hasSpace(size) {
                let rowSize = CGSize(width: origin.x - spacing, height: rowHeight)
                return viewSizes.isEmpty ? nil : .init(viewsSizes: viewSizes, size: rowSize)
            }
            
            _ = subviews.popFirst()
            viewSizes.append(size)
            rowHeight = max(rowHeight, size.height)
            origin.x += (size.width + spacing)
        }
    }
    
    // 计算所有行
    private func calculateRows(_ maxWidth: CGFloat, proposal: ProposedViewSize, subviews: Subviews) -> Cache {
        var rows: [Cache.Row] = []
        var height: CGFloat = 0
        var subviews = subviews
        
        while !subviews.isEmpty {
            guard let row = calculateRow(maxWidth, proposal: proposal, subviews: &subviews) else { break }
            rows.append(row)
            height += row.size.height + spacing
        }
        
        // 去掉最后一行的间距
        if !rows.isEmpty {
            height -= spacing
        }
        
        return .init(rows: rows, height: height)
    }
    
    // 根据对齐方式计算行起始X坐标
    private func getRowXOrigin(bounds: CGRect, rowWidth: CGFloat) -> CGFloat {
        switch alignment {
        case .center:
            return (bounds.minX + bounds.maxX - rowWidth) / 2
        case .trailing:
            return bounds.maxX - rowWidth
        default:
            return bounds.minX
        }
    }
}

// ViewModel
class BeanDetailViewModel: ObservableObject {
    @Published var showDeleteAlert = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showingFlavorAccuracyInfo = false
    
    // 辅助函数，执行网络请求并可以抛出错误
    private func performRequest(request: URLRequest) async throws {
        // 示例实现，实际应该使用URLSession或其他网络库
        throw NSError(domain: "未实现网络请求", code: -1, userInfo: nil)
    }
    
    // 获取拼配组件的特定属性值
    func getComponentAttribute(bean: CoffeeBean, keyPath: KeyPath<BlendComponent, String?>) -> [(Int, String)]? {
        guard bean.type == "BLEND", let components = bean.blendComponents else { return nil }
        
        return components.enumerated()
            .compactMap { index, component in
                if let value = component[keyPath: keyPath], !value.isEmpty {
                    return (index, value)
                }
                return nil
            }
    }
    
    // 获取拼配组件的海拔信息
    func getComponentsAltitude(bean: CoffeeBean) -> [(Int, String)]? {
        guard bean.type == "BLEND", let components = bean.blendComponents else { return nil }
        
        return components.enumerated()
            .compactMap { index, component in
                if component.altitudeType == "SINGLE" && component.altitudeSingle != nil && component.altitudeSingle != 0 {
                    return (index, "\(component.altitudeSingle ?? 0)米")
                } else if component.altitudeType == "RANGE" && component.altitudeMin != nil && component.altitudeMax != nil && 
                          (component.altitudeMin != 0 || component.altitudeMax != 0) {
                    return (index, "\(component.altitudeMin ?? 0)-\(component.altitudeMax ?? 0)米")
                }
                return nil
            }
    }
}

// CoffeeBean预览扩展
extension CoffeeBean {
    static var previewForDetailView: CoffeeBean {
        // 使用没有解码器参数的模拟数据初始化
        let bean = CoffeeBean(
            id: 1,
            name: "耶加雪菲G1",
            type: "SINGLE",
            typeDisplay: "单品",
            roaster: "隔壁烘焙",
            roastLevel: 3,
            roastLevelDisplay: "中浅烘",
            origin: "埃塞俄比亚",
            region: "西达摩",
            finca: "花蝴蝶庄园",
            variety: "埃塞原生种",
            process: "水洗",
            barcode: nil,
            notes: nil,
            bagWeight: 200.0,
            bagRemain: 150.0,
            purchasePrice: 120.0,
            roastDate: Date(),
            createdAt: Date(),
            deletedAt: nil,
            isFavorite: true,
            isArchived: false,
            isDeleted: false,
            isDecaf: false,
            altitudeType: "RANGE",
            altitudeSingle: nil,
            altitudeMin: 1800,
            altitudeMax: 2100,
            restPeriodMin: 7,
            restPeriodMax: 14,
            restPeriodProgress: 5,
            stockStatus: "IN_USE",
            avgRating: 4.5,
            tasteNotes: ["草莓", "蜂蜜", "柠檬", "水蜜桃"],
            blendComponents: nil,
            occurrences: nil,
            usageCount: 10,
            lastUsed: Date().addingTimeInterval(-86400 * 3), // 3天前
            daysSinceLastUse: 3,
            mostUsedEquipment: nil,
            remainingUses: 15,
            occurrencesCount: 2,
            avgRepurchaseInterval: 30.5,
            dimensionsAvg: ["aroma": 4.2, "acidity": 4.0, "sweetness": 3.8, "body": 3.5, "aftertaste": 4.0],
            tastingCount: 8,
            uniqueFlavorTags: nil,
            flavorAccuracy: 85
        )
        
        return bean
    }
}

// BlendComponent预览扩展
extension BlendComponent {
    static var previewForDetailView: BlendComponent {
        // 使用没有解码器参数的初始化方法
        return BlendComponent(
            id: 1,
            coffeeBeanId: 1,
            origin: "埃塞俄比亚",
            region: "西达摩",
            finca: "花蝴蝶庄园",
            variety: "埃塞原生种",
            process: "水洗",
            roastLevel: 3,
            roastLevelDisplay: "中浅烘",
            blendRatio: 50,
            order: 0,
            altitudeType: "RANGE",
            altitudeSingle: nil,
            altitudeMin: 1800,
            altitudeMax: 2100
        )
    }
}

// 预览
struct BeanDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            BeanDetailView(bean: .previewForDetailView)
        }
    }
}

// 添加自定义分隔线组件到文件底部
// 自定义分隔线
struct CustomDivider: View {
    let color: Color
    
    var body: some View {
        color
            .frame(height: 2)
            .edgesIgnoringSafeArea(.horizontal)
    }
}

// 添加风味准确度信息弹框
struct FlavorAccuracyInfoSheet: View {
    @Binding var isPresented: Bool
    @GestureState private var dragOffset = CGSize.zero
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 底部弹框
                VStack(spacing: 0) {
                    // 顶部灰条和关闭按钮
                    VStack(spacing: 0) {
                        // 灰色拖动条
                        Capsule()
                            .fill(Color.noteText.opacity(0.3))
                            .frame(width: 36, height: 5)
                            .padding(.top, 8)
                            .padding(.bottom, 12)
                        
                        // 标题栏
                        HStack {
                            Text("风味准确度")
                                .font(.headline)
                                .foregroundColor(.primaryText)
                            
                            Spacer()
                            
                            // 关闭按钮
                            Button(action: {
                                isPresented = false
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.archivedText)
                                    .font(.system(size: 24))
                            }
                        }
                        .padding(.bottom, 16)
                    }
                    .padding(.horizontal)
                    
                    // 内容区域
                    Text("风味准确度是指冲煮出的咖啡风味与咖啡豆预期风味特征的匹配程度。百分比数值越高，代表冲煮过程能更精准地呈现咖啡豆的独特风味特性，或表明烘焙商对咖啡豆的风味描述与真实表现的契合度更高。")
                        .font(.subheadline)
                        .foregroundColor(.primaryText.opacity(0.8))
                        .fixedSize(horizontal: false, vertical: true)
                        .lineSpacing(4)
                        .padding(.horizontal)
                        .padding(.bottom, 24)
                }
                .frame(maxWidth: .infinity)
                .background(Color.primaryBg)
                .cornerRadius(16)
                .shadow(color: Color.primaryText.opacity(0.2), radius: 10, x: 0, y: -5)
                .offset(y: max(0, dragOffset.height))
                .gesture(
                    DragGesture()
                        .updating($dragOffset) { value, state, _ in
                            if value.translation.height > 0 {
                                state = value.translation
                            }
                        }
                        .onEnded { value in
                            if value.translation.height > 50 {
                                isPresented = false
                            }
                        }
                )
                .frame(maxHeight: .infinity, alignment: .bottom)
                // 添加底部安全区域和水平间距
                .padding(.bottom, geometry.safeAreaInsets.bottom + 12)
                .padding(.horizontal, 12)
            }
            .edgesIgnoringSafeArea(.bottom)
        }
        .transition(.move(edge: .bottom))
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPresented)
    } 
}