import SwiftUI
import SwiftData

/// 咖啡豆回购记录视图
/// 用于显示特定咖啡豆的回购历史记录，并提供添加新回购记录的功能
struct BeanOccurrenceView: View {
    // 当前咖啡豆的ID
    let beanId: Int
    
    // 当前视图模型
    @StateObject private var viewModel = BeanOccurrenceViewModel()
    
    // 环境状态
    @Environment(\.presentationMode) var presentationMode
    @State private var alertMessage = ""
    @State private var showAlert = false
    @State private var beanName = ""
    @State private var isLoading = false
    @State private var occurrences: [BeanOccurrence] = []
    @State private var currentBean: CoffeeBean?
    
    // 批量编辑模式
    @State private var isEditMode = false
    @State private var selectedOccurrenceIds: Set<Int> = []
    @State private var showBatchDeleteConfirmation = false
    
    // 控制是否直接显示添加表单
    @State private var showAddFormDirectly: Bool
    
    // 默认初始化方法 - 接受beanId参数
    init(beanId: Int, showAddFormDirectly: Bool = false) {
        self.beanId = beanId
        self._showAddFormDirectly = State(initialValue: showAddFormDirectly)
    }
    
    // 兼容BeanDetailView - 接受CoffeeBean参数
    init(bean: CoffeeBean, showAddFormDirectly: Bool = false) {
        self.beanId = bean.id
        self._currentBean = State(initialValue: bean)
        self._showAddFormDirectly = State(initialValue: showAddFormDirectly)
    }
    
    var body: some View {
        ZStack {
            // 背景颜色
            Color.secondaryBg.ignoresSafeArea()
            
            // 主内容区域
            ZStack {
                if !isLoading {
                    if !occurrences.isEmpty || (currentBean != nil && hasInitialBeanInfo) {
                        timelineListView
                    } else {
                        emptyStateView
                    }
                } else {
                    loadingView
                }
                
                // 错误消息覆盖
                if viewModel.errorMessage != nil {
                    errorView
                }
            }
        }
        .navigationTitle(beanName.isEmpty ? "咖啡豆回购记录" : "\(beanName)的回购记录")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarTrailing) {
                if !occurrences.isEmpty && !isLoading {
                    Button(action: {
                        if isEditMode {
                            // 如果有选中的记录，显示删除确认
                            if !selectedOccurrenceIds.isEmpty {
                                showBatchDeleteConfirmation = true
                            } else {
                                // 否则直接退出编辑模式
                                isEditMode = false
                            }
                        } else {
                            // 进入编辑模式
                            isEditMode = true
                            selectedOccurrenceIds.removeAll()
                        }
                    }) {
                        Text(isEditMode && !selectedOccurrenceIds.isEmpty ? "删除" : "选择")
                    }
                }
            }
        }
        .sheet(isPresented: $viewModel.showAddSheet) {
            addOccurrenceSheet
        }
        .sheet(isPresented: $viewModel.showEditSheet) {
            editOccurrenceSheet
        }
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text("提示"),
                message: Text(alertMessage),
                dismissButton: .default(Text("确定"))
            )
        }
        .alert("删除记录", isPresented: $showBatchDeleteConfirmation) {
            Button("取消", role: .cancel) {
                // 点击取消时退出编辑模式
                isEditMode = false
                selectedOccurrenceIds.removeAll()
            }
            Button("删除", role: .destructive) {
                deleteBatchOccurrences()
            }
        } message: {
            Text("确定要删除选中的\(selectedOccurrenceIds.count)条回购记录吗？此操作无法撤销。")
        }
        .task {
            await loadBeanDetails()
            
            // 如果需要直接显示添加表单，在加载完数据后显示
            if showAddFormDirectly {
                // 确保数据加载完成且bean存在
                if currentBean != nil {
                    // 延迟一下以确保UI渲染完成
                    try? await Task.sleep(nanoseconds: 300_000_000) // 300毫秒
                    viewModel.showAddSheet = true
                }
            }
        }
        .onAppear {
            // 注册通知 - 监听咖啡豆详情更新通知
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("BeanDetailNeedsRefresh"),
                object: nil,
                queue: .main
            ) { [self] notification in
                if let updatedBean = notification.object as? CoffeeBean, updatedBean.id == self.beanId {
                    print("📣 收到咖啡豆详情更新通知，正在处理...")
                    
                    // 获取通知中的额外信息
                    let userInfo = notification.userInfo
                    let updatedTime = userInfo?["updatedAt"] as? TimeInterval ?? Date().timeIntervalSince1970
                    let updateTimeString = Date(timeIntervalSince1970: updatedTime).formatted()
                    print("📅 数据更新时间: \(updateTimeString)")
                    
                    // 如果包含特定记录ID，输出详细日志
                    if let occurrenceId = userInfo?["occurrenceId"] as? Int {
                        print("🔍 更新的回购记录ID: \(occurrenceId)")
                    }
                    
                    // 如果正在显示表单，则不刷新以避免冲突
                    if viewModel.showEditSheet || viewModel.showAddSheet {
                        print("⚠️ 正在显示表单，暂不刷新数据")
                        return
                    }
                    
                    // 显示加载状态
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isLoading = true
                    }
                    
                    // 强制在主线程上同步更新UI
                    DispatchQueue.main.async {
                        // 1. 更新当前bean引用
                        self.currentBean = updatedBean
                        self.beanName = updatedBean.name
                        
                        // 2. 更新ViewModel的状态
                        self.viewModel.updatedBean = updatedBean
                        
                        // 3. 清空并重新填充回购记录列表
                        self.occurrences = []
                        
                        // 4. 延迟一帧后再设置新数据，确保UI能察觉到变化
                        DispatchQueue.main.async {
                            if let occurrencesList = updatedBean.occurrences {
                                let sortedOccurrences = occurrencesList.sorted(by: { $0.createdAt > $1.createdAt })
                                self.occurrences = sortedOccurrences
                                self.viewModel.occurrences = occurrencesList
                                print("📦 强制刷新UI，更新\(sortedOccurrences.count)条回购记录")
                            }
                            
                            // 短暂延迟后隐藏加载状态
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                withAnimation {
                                    self.isLoading = false
                                }
                            }
                        }
                    }
                }
            }
        }
        .onDisappear {
            // 移除通知观察者
            NotificationCenter.default.removeObserver(
                self,
                name: NSNotification.Name("BeanDetailNeedsRefresh"),
                object: nil
            )
        }
    }
    
    // 检查是否有初始包装信息
    private var hasInitialBeanInfo: Bool {
        guard let bean = currentBean else { return false }
        return bean.initialBagWeight != nil || 
               bean.initialBagRemain != nil || 
               bean.initialPurchasePrice != nil ||
               bean.initialRoastDate != nil
    }
    
    // 时间线风格的回购记录列表
    private var timelineListView: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 显示所有回购记录，从新到旧排序
                ForEach(occurrences.indices, id: \.self) { index in
                    let occurrence = occurrences[index]
                    let nextIndex = index + 1
                    let hasNext = nextIndex < occurrences.count
                    
                    VStack(spacing: 0) {
                        OccurrenceRecordView(
                            occurrence: occurrence,
                            beanId: beanId,
                            isEditMode: isEditMode,
                            isSelected: selectedOccurrenceIds.contains(occurrence.id),
                            onSelect: {
                                toggleSelection(occurrenceId: occurrence.id)
                            },
                            onTap: {
                                if !isEditMode {
                                    viewModel.selectedOccurrence = occurrence
                                    viewModel.showEditSheet = true
                                }
                            }
                        )
                        
                        // 如果有下一条记录，添加时间线连接
                        if hasNext {
                            // 计算时间间隔 - 注意这里需要反转时间计算顺序
                            let dateInterval = occurrences[index].createdAt.timeIntervalSince(occurrences[nextIndex].createdAt)
                            TimelineConnector(dateInterval: abs(dateInterval))
                        }
                        // 如果是最后一条记录且有初始记录，添加连接到初始记录的时间线
                        else if index == occurrences.count - 1 && currentBean != nil && hasInitialBeanInfo {
                            // 计算时间间隔 - 最后一条回购记录的时间与初始记录的创建时间，反转计算顺序
                            if let bean = currentBean {
                                let initialDate = bean.initialCreatedAt ?? bean.createdAt
                                let dateInterval = occurrence.createdAt.timeIntervalSince(initialDate)
                                if dateInterval > 0 {
                                    TimelineConnector(dateInterval: dateInterval)
                                }
                            }
                        }
                    }
                }
                
                // 最后添加初始记录（最早的记录）
                if let bean = currentBean, hasInitialBeanInfo {
                    InitialBeanRecordView(bean: bean)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .background(Color.secondaryBg)
    }
    
    // 初始咖啡豆记录视图
    private struct InitialBeanRecordView: View {
        let bean: CoffeeBean
        
        // 格式化日期
        private func formatDate(_ date: Date) -> String {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy年MM月dd日"
            return formatter.string(from: date)
        }
        
        // 计算并格式化烘焙日期与购买日期的差距
        private func formatRoastDateDiff() -> String? {
            guard let roastDate = bean.initialRoastDate else {
                return nil
            }
            
            // 计算天数差异（购买日期 - 烘焙日期）
            let calendar = Calendar.current
            let purchaseDate = bean.initialCreatedAt ?? bean.createdAt
            
            // 使用calendar计算两个日期之间的天数差异，忽略时分秒
            let purchaseDay = calendar.startOfDay(for: purchaseDate)
            let roastDay = calendar.startOfDay(for: roastDate)
            
            if let days = calendar.dateComponents([.day], from: roastDay, to: purchaseDay).day {
                if days == 0 {
                    return "(当天烘焙)"
                } else if days > 0 {
                    return "(\(days)天前烘焙)"
                } else {
                    return "(\(abs(days))天后烘焙)"
                }
            }
            
            return nil
        }
        
        // 格式化小数，保留小数点后有效数字(结尾的0不显示)
        private func formatDecimal(_ value: Double) -> String {
            // 检查小数部分是否为0
            if value.truncatingRemainder(dividingBy: 1) == 0 {
                return String(format: "%.0f", value) // 整数格式
            } else {
                // 使用NumberFormatter来去除小数末尾的0
                let formatter = NumberFormatter()
                formatter.minimumFractionDigits = 0 // 小数最少位数
                formatter.maximumFractionDigits = 2 // 小数最多位数
                formatter.numberStyle = .decimal
                formatter.decimalSeparator = "." // 确保小数点使用.而不是,
                formatter.groupingSeparator = "" // 不使用千位分隔符
                
                if let formattedString = formatter.string(from: NSNumber(value: value)) {
                    return formattedString
                } else {
                    return String(format: "%.2f", value) // 回退到固定两位小数格式
                }
            }
        }
        
        var body: some View {
            VStack(alignment: .leading, spacing: 12) {
                // 第一行：初次购买信息
                Text("初次购买")
                    .font(.headline)
                    .foregroundColor(.primaryText.opacity(0.5))
                
                // 第二行：购买时间
                HStack(spacing: 4) {
                    Text(formatDate(bean.initialCreatedAt ?? bean.createdAt))
                        .font(.subheadline)
                        .foregroundColor(.primaryText.opacity(0.7))
                    
                    // 显示烘焙日期差距
                    if let roastDateDiff = formatRoastDateDiff() {
                        Text(roastDateDiff)
                            .font(.caption)
                            .foregroundColor(.detailText)
                    }
                }
                
                // 第三行：价格与包装规格
                HStack {
                    // 左侧：价格
                    if let price = bean.initialPurchasePrice {
                        Text("¥\(formatDecimal(price))")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                    }
                    
                    Spacer()
                    
                    // 右侧：包装规格
                    if let weight = bean.initialBagWeight {
                        Text("\(formatDecimal(weight))g")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                    }
                }
            }
            .padding(12)
            .background(Color.secondaryBg)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(style: StrokeStyle(lineWidth: 1, dash: [5]))
                    .foregroundColor(Color.archivedText.opacity(0.5))
            )
            .padding(.bottom, 12)
        }
    }
    
    // 回购记录视图
    private struct OccurrenceRecordView: View {
        let occurrence: BeanOccurrence
        let beanId: Int
        let isEditMode: Bool
        let isSelected: Bool
        let onSelect: () -> Void
        let onTap: () -> Void
        
        // 查找上一条记录价格(如果有)
        @State private var previousPrice: Double? = nil
        @State private var priceChangeRatio: Double? = nil
        @State private var purchaseIndex: Int? = nil
        
        // 烘焙日期相关
        @State private var roastDateText: String? = nil
        
        // 格式化日期
        private func formatDate(_ date: Date) -> String {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy年MM月dd日"
            return formatter.string(from: date)
        }
        
        // 计算并格式化烘焙日期与购买日期的差距
        private func formatRoastDateDiff() -> String? {
            guard let roastDate = occurrence.roastDate else {
                return nil
            }
            
            // 计算天数差异（购买日期 - 烘焙日期）
            let calendar = Calendar.current
            let purchaseDate = occurrence.createdAt
            
            // 使用calendar计算两个日期之间的天数差异，忽略时分秒
            let purchaseDay = calendar.startOfDay(for: purchaseDate)
            let roastDay = calendar.startOfDay(for: roastDate)
            
            if let days = calendar.dateComponents([.day], from: roastDay, to: purchaseDay).day {
                if days == 0 {
                    return "(当天烘焙)"
                } else if days > 0 {
                    return "(\(days)天前烘焙)"
                } else {
                    return "(\(abs(days))天后烘焙)"
                }
            }
            
            return nil
        }
        
        // 格式化小数，保留小数点后有效数字(结尾的0不显示)
        private func formatDecimal(_ value: Double) -> String {
            // 检查小数部分是否为0
            if value.truncatingRemainder(dividingBy: 1) == 0 {
                return String(format: "%.0f", value) // 整数格式
            } else {
                // 使用NumberFormatter来去除小数末尾的0
                let formatter = NumberFormatter()
                formatter.minimumFractionDigits = 0 // 小数最少位数
                formatter.maximumFractionDigits = 2 // 小数最多位数
                formatter.numberStyle = .decimal
                formatter.decimalSeparator = "." // 确保小数点使用.而不是,
                formatter.groupingSeparator = "" // 不使用千位分隔符
                
                if let formattedString = formatter.string(from: NSNumber(value: value)) {
                    return formattedString
                } else {
                    return String(format: "%.2f", value) // 回退到固定两位小数格式
                }
            }
        }
        
        // 获取价格变动百分比
        private func getPriceChangeText() -> (String, Color, Bool) {
            guard let prevPrice = previousPrice,
                  let currPrice = occurrence.purchasePrice,
                  let ratio = priceChangeRatio else {
                return ("", .clear, false)
            }
            
            let change = currPrice - prevPrice
            
            // 如果价格变动为0，不显示变动信息
            if change == 0 || abs(ratio) < 0.0001 {
                return ("", .clear, false)
            }
            
            let isIncrease = change > 0
            let changeAbs = abs(change)
            
            // 格式化百分比(保留整数)
            let percentText = String(format: "%.0f%%", abs(ratio * 100))
            // 使用formatDecimal格式化价格变化值
            let changeText = "\(isIncrease ? "+" : "-")\(formatDecimal(changeAbs))(\(isIncrease ? "↗︎" : "↘︎")\(percentText))"
            
            // 上涨红色背景，下跌绿色背景
            let bgColor = isIncrease ? Color.red.opacity(0.2) : Color.green.opacity(0.2)
            
            return (changeText, bgColor, true)
        }
        
        var body: some View {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    // 第一行：第n次回购
                    if let index = purchaseIndex {
                        Text("第\(index)次回购")
                            .font(.headline)
                            .foregroundColor(.primaryText.opacity(0.5))
                    } else {
                        Text("回购记录")
                            .font(.headline)
                            .foregroundColor(.primaryText.opacity(0.5))
                    }
                    
                    Spacer()
                    
                    // 选择框区域 - 无论编辑模式与否都保持相同尺寸
                    ZStack {
                        // 非编辑模式下显示透明占位
                        if !isEditMode {
                            Image(systemName: "circle")
                                .foregroundColor(.clear)
                                .font(.system(size: 20))
                        } else {
                            // 编辑模式下显示选择框
                            Button(action: onSelect) {
                                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                                    .foregroundColor(isSelected ? .functionText : .gray)
                                    .font(.system(size: 20))
                            }
                        }
                    }
                    .frame(width: 28, height: 28) // 固定大小
                }
                
                // 第二行：购买时间
                HStack(spacing: 4) {
                    Text(formatDate(occurrence.createdAt))
                        .font(.subheadline)
                        .foregroundColor(.primaryText.opacity(0.7))
                    
                    // 显示烘焙日期差距
                    if let roastDateDiff = formatRoastDateDiff() {
                        Text(roastDateDiff)
                            .font(.caption)
                            .foregroundColor(.detailText)
                    }
                }
                
                // 第三行：价格与包装规格
                HStack {
                    // 左侧：价格和价格变动
                    if let price = occurrence.purchasePrice {
                        HStack(spacing: 4) {
                            Text("¥\(formatDecimal(price))")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                            
                            // 显示价格变动
                            let (changeText, bgColor, hasChange) = getPriceChangeText()
                            if hasChange {
                                Text(changeText)
                                    .font(.caption)
                                    .padding(.horizontal, 4)
                                    .padding(.vertical, 2)
                                    .background(bgColor)
                                    .cornerRadius(4)
                                    .foregroundColor(.primaryText)
                            }
                        }
                    }
                    
                    Spacer()
                    
                    // 右侧：包装规格
                    if let weight = occurrence.bagWeight {
                        Text("\(formatDecimal(weight))g")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                    }
                }
            }
            .padding(12)
            .background(Color.primaryBg)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.functionText : Color.clear, lineWidth: isSelected ? 2 : 0)
            )
            .onTapGesture {
                if isEditMode {
                    onSelect()
                } else {
                    onTap()
                }
            }
            .onAppear {
                // 查找上一条记录以及当前是第几次回购
                // 使用传入的咖啡豆ID
                Task {
                    do {
                        // 使用传入的咖啡豆ID
                        let viewModel = BeanOccurrenceViewModel()
                        let bean = try await viewModel.loadBean(beanId: beanId)
                        
                        guard let occurrences = bean.occurrences else { return }
                        
                        // 按时间排序
                        let sortedOccurrences = occurrences.sorted(by: { $0.createdAt > $1.createdAt })
                        
                        // 找到当前记录的索引
                        if let index = sortedOccurrences.firstIndex(where: { $0.id == occurrence.id }) {
                            // 计算是第几次回购(从新到旧排序，所以需要反转计算)
                            DispatchQueue.main.async {
                                self.purchaseIndex = sortedOccurrences.count - index
                                
                                // 计算烘焙日期差距文本
                                self.roastDateText = formatRoastDateDiff()
                            }
                            
                            // 如果不是最后一次回购，获取上一次回购的价格
                            let nextIndex = index + 1
                            if nextIndex < sortedOccurrences.count {
                                let previousOccurrence = sortedOccurrences[nextIndex]
                                
                                // 如果两个记录都有价格，计算变化
                                if let prevPrice = previousOccurrence.purchasePrice,
                                   let currPrice = occurrence.purchasePrice {
                                    
                                    DispatchQueue.main.async {
                                        self.previousPrice = prevPrice
                                        
                                        // 计算变化率
                                        if prevPrice > 0 {
                                            let changeRatio = (currPrice - prevPrice) / prevPrice
                                            self.priceChangeRatio = changeRatio
                                        }
                                    }
                                }
                            } else if index == sortedOccurrences.count - 1 {
                                // 最后一次回购与初始购买比较
                                if let initialPrice = bean.initialPurchasePrice,
                                   let currPrice = occurrence.purchasePrice {
                                    
                                    DispatchQueue.main.async {
                                        self.previousPrice = initialPrice
                                        
                                        // 计算变化率
                                        if initialPrice > 0 {
                                            let changeRatio = (currPrice - initialPrice) / initialPrice
                                            self.priceChangeRatio = changeRatio
                                        }
                                    }
                                }
                            }
                        }
                    } catch {
                        print("获取回购记录索引失败: \(error.localizedDescription)")
                    }
                }
            }
        }
    }
    
    // 时间线连接器，显示间隔时间
    private struct TimelineConnector: View {
        let dateInterval: TimeInterval
        
        var body: some View {
            VStack(spacing: 4) {
                // 上部连接线
                Rectangle()
                    .fill(Color.archivedText.opacity(0.5))
                    .frame(width: 2, height: 20)
                
                // 时间间隔标签
                Text(formatWithPrecision(dateInterval))
                    .font(.caption)
                    .foregroundColor(.detailText)
                    .padding(.horizontal, 12)
                    .cornerRadius(12)
                
                // 下部连接线
                Rectangle()
                    .fill(Color.archivedText.opacity(0.5))
                    .frame(width: 2, height: 20)
            }
            .padding(.vertical, 4)
            .frame(maxWidth: .infinity, alignment: .center)
        }
        
        // 复用NumberFormatters中的时间格式化函数
        private func formatWithPrecision(_ interval: TimeInterval) -> String {
            return NumberFormatters.formatTimeInterval(interval)
        }
    }
    
    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "cart.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            Text("暂无回购记录")
                .font(.title2)
            Text("可在咖啡豆列表页\n长按预览卡片选择「添加回购记录」")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // 加载中视图
    private var loadingView: some View {
        BlinkingLoader(color: .primaryText)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // 错误视图
    private var errorView: some View {
        VStack {
            Text(viewModel.errorMessage ?? "出现错误")
                .foregroundColor(.red)
                .padding()
            Button("重试") {
                Task {
                    await loadBeanDetails()
                }
            }
            .buttonStyle(.bordered)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
    }
    
    // 添加回购记录表单
    private var addOccurrenceSheet: some View {
        Group {
            if let bean = currentBean {
                BeanOccurrenceForm(viewModel: viewModel, bean: bean)
                    .onDisappear {
                        if viewModel.updatedBean != nil {
                            // 如果表单提交成功，直接使用ViewModel中的数据更新UI
                            if let updatedBean = viewModel.updatedBean {
                                // 先显示加载状态
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    isLoading = true
                                }
                                
                                // 强制更新UI - 先更新bean数据
                                DispatchQueue.main.async {
                                    self.currentBean = updatedBean
                                    self.beanName = updatedBean.name
                                    
                                    // 清空当前列表，强制触发UI刷新
                                    self.occurrences = []
                                    
                                    // 延迟一帧再设置新数据
                                    DispatchQueue.main.async {
                                        // 更新回购记录，确保按日期降序排序（从新到旧）
                                        if let occurrencesList = updatedBean.occurrences {
                                            let sortedOccurrences = occurrencesList.sorted(by: { $0.createdAt > $1.createdAt })
                                            self.occurrences = sortedOccurrences
                                            print("📦 新增表单后强制刷新UI，记录数量: \(sortedOccurrences.count)")
                                        }
                                        
                                        // 短暂延迟后隐藏加载状态
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                            withAnimation {
                                                self.isLoading = false
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
            } else {
                // 如果没有bean对象，显示错误
                Text("无法加载咖啡豆信息")
                    .padding()
                    .onDisappear {}
            }
        }
    }
    
    // 编辑回购记录表单
    private var editOccurrenceSheet: some View {
        Group {
            if let bean = currentBean, let occurrence = viewModel.selectedOccurrence {
                BeanOccurrenceForm(viewModel: viewModel, bean: bean, occurrence: occurrence)
                    .onDisappear {
                        if viewModel.error == nil && viewModel.updatedBean != nil {
                            // 表单关闭后更新界面，使用已有数据不再发起网络请求
                            if let updatedBean = viewModel.updatedBean {
                                // 1. 先显示加载状态，让用户感知到变化
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    isLoading = true
                                }
                                
                                // 2. 强制更新UI - 先更新bean数据
                                DispatchQueue.main.async {
                                    self.currentBean = updatedBean
                                    self.beanName = updatedBean.name
                                    
                                    // 清空当前列表，强制触发UI刷新
                                    self.occurrences = []
                                    
                                    // 延迟一帧再设置新数据
                                    DispatchQueue.main.async {
                                        // 更新回购记录，确保按日期降序排序（从新到旧）
                                        if let occurrencesList = updatedBean.occurrences {
                                            let sortedOccurrences = occurrencesList.sorted(by: { $0.createdAt > $1.createdAt })
                                            self.occurrences = sortedOccurrences
                                            print("📦 编辑表单后强制刷新UI，记录数量: \(sortedOccurrences.count)")
                                        }
                                        
                                        // 短暂延迟后隐藏加载状态
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                            withAnimation {
                                                self.isLoading = false
                                            }
                                        }
                                    }
                                }
                            }
                        } else if let error = viewModel.error {
                            // 显示错误消息
                            alertMessage = "更新回购记录失败：\(error.localizedDescription)"
                            showAlert = true
                        }
                    }
            } else {
                // 如果没有bean对象或选中的occurrence，显示错误
                Text("无法加载回购记录信息")
                    .padding()
                    .onDisappear {}
            }
        }
    }
    
    // 切换记录选择状态
    private func toggleSelection(occurrenceId: Int) {
        if selectedOccurrenceIds.contains(occurrenceId) {
            selectedOccurrenceIds.remove(occurrenceId)
        } else {
            selectedOccurrenceIds.insert(occurrenceId)
        }
    }
    
    // 批量删除选中的记录
    private func deleteBatchOccurrences() {
        Task {
            isLoading = true
            
            var hasError = false
            var deletedCount = 0
            var lastUpdatedBean: CoffeeBean? = nil
            
            // 逐个删除选中的记录
            for occurrenceId in selectedOccurrenceIds {
                do {
                    try await viewModel.deleteOccurrence(occurrenceId: occurrenceId)
                    deletedCount += 1
                    // 保存最后一次更新的bean
                    if let bean = viewModel.updatedBean {
                        lastUpdatedBean = bean
                    }
                } catch {
                    hasError = true
                    print("删除记录 \(occurrenceId) 失败: \(error.localizedDescription)")
                }
            }
            
            // 强制更新本地UI数据，不再通过loadBeanDetails重新请求
            if let updatedBean = lastUpdatedBean {
                await MainActor.run {
                    // 先显示加载状态
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isLoading = true
                    }
                    
                    // 强制刷新UI - 先清空当前列表
                    self.occurrences = []
                    self.currentBean = updatedBean
                    
                    // 延迟一帧再设置新数据
                    DispatchQueue.main.async {
                        // 如果有剩余记录，更新列表
                        if let occurrencesList = updatedBean.occurrences, !occurrencesList.isEmpty {
                            let sortedOccurrences = occurrencesList.sorted(by: { $0.createdAt > $1.createdAt })
                            self.occurrences = sortedOccurrences
                            print("📦 删除后强制刷新UI，剩余记录数量: \(sortedOccurrences.count)")
                        } else {
                            // 确保列表保持为空
                            self.occurrences = []
                            print("📦 删除后所有记录已清空")
                        }
                        
                        // 延迟后退出编辑模式
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            // 退出编辑模式
                            isEditMode = false
                            selectedOccurrenceIds.removeAll()
                            
                            // 最后关闭加载状态
                            withAnimation {
                                isLoading = false
                            }
                            
                            // 只有在发生错误时才显示提示
                            if hasError {
                                if deletedCount > 0 {
                                    alertMessage = "成功删除\(deletedCount)条记录，但部分记录删除失败"
                                } else {
                                    alertMessage = "删除失败，请稍后重试"
                                }
                                showAlert = true
                            }
                            
                            // 如果删除后没有回购记录了，自动返回上一页
                            if self.occurrences.isEmpty {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                    presentationMode.wrappedValue.dismiss()
                                }
                            }
                        }
                    }
                }
            } else {
                // 如果没有获取到更新的bean，回退到网络请求
                await loadBeanDetails()
                
                // 退出编辑模式
                isEditMode = false
                selectedOccurrenceIds.removeAll()
                
                // 只有在发生错误时才显示提示
                if hasError {
                    if deletedCount > 0 {
                        alertMessage = "成功删除\(deletedCount)条记录，但部分记录删除失败"
                    } else {
                        alertMessage = "删除失败，请稍后重试"
                    }
                    showAlert = true
                }
                
                // 如果删除后没有回购记录了，自动返回上一页
                if occurrences.isEmpty {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                isLoading = false
            }
        }
    }
    
    /// 加载咖啡豆详情和回购记录
    private func loadBeanDetails() async {
        // 提前检查是否已有缓存数据，如果有则立即显示
        if let currentBean = currentBean {
            await MainActor.run {
                beanName = currentBean.name
                if let occurrencesList = currentBean.occurrences, !occurrencesList.isEmpty {
                    occurrences = occurrencesList.sorted(by: { $0.createdAt > $1.createdAt })
                }
            }
        }
        
        await MainActor.run {
            isLoading = true
        }
        
        do {
            // 构建安全的API端点路径
            let endpoint = "/ios/api/beans/\(beanId)/"
            
            // 添加日志
            print("🔄 正在请求API: \(endpoint) (使用APIRequest结构体)")
            
            // 使用本地缓存机制和清除缓存功能
            APIService.shared.clearCache(for: endpoint)
            
            // 创建API请求对象
            let request = APIRequest(
                endpoint: endpoint,
                method: .get,
                parameters: ["t": "\(Int(Date().timeIntervalSince1970))"],
                forceRefresh: true
            )
            
            // 使用perform方法执行请求
            let bean: CoffeeBean = try await APIService.shared.perform(request)
            
            // 在主线程更新UI
            await MainActor.run {
                // 强制刷新UI - 先清空现有数据
                self.occurrences = []
                
                // 更新bean和名称
                self.currentBean = bean
                self.beanName = bean.name
                
                // 延迟一帧后再更新列表数据，确保UI能感知到变化
                DispatchQueue.main.async {
                    // 更新回购记录，确保按日期降序排序（从新到旧）
                    if let occurrencesList = bean.occurrences, !occurrencesList.isEmpty {
                        let sortedOccurrences = occurrencesList.sorted(by: { $0.createdAt > $1.createdAt })
                        self.occurrences = sortedOccurrences
                        print("📦 成功加载\(sortedOccurrences.count)条回购记录")
                        
                        // 输出第一条记录信息用于调试
                        if let firstOccurrence = sortedOccurrences.first {
                            print("🔍 第一条回购记录: ID=\(firstOccurrence.id), 时间=\(firstOccurrence.createdAt), 价格=\(String(describing: firstOccurrence.purchasePrice))")
                        }
                    } else {
                        print("📭 没有找到回购记录")
                    }
                }
                
                viewModel.errorMessage = nil
                
                // 将获取的数据同步到ViewModel
                viewModel.updatedBean = bean
                if let occurrencesList = bean.occurrences {
                    viewModel.occurrences = occurrencesList
                }
                
                // 保存更新后的咖啡豆数据到UserDefaults
                do {
                    let encoder = JSONEncoder()
                    let beanData = try encoder.encode(bean)
                    UserDefaults.standard.set(beanData, forKey: "saved_bean_\(bean.id)")
                    print("📝 加载后已将最新咖啡豆数据保存到UserDefaults，ID: \(bean.id)")
                } catch {
                    print("❌ 编码咖啡豆数据失败: \(error.localizedDescription)")
                }
            }
        } catch let apiError as APIError {
            // 处理API错误
            await MainActor.run {
                viewModel.errorMessage = "加载失败: \(apiError.message)"
                print("❌ API错误：\(apiError.message)")
                
                // 显示更详细的错误信息
                if case .invalidURL = apiError {
                    print("❌ URL无效错误，请检查APIService中的baseURL和endpoint组合")
                } else if case .networkError(let error) = apiError {
                    print("❌ 网络错误：\(error.localizedDescription)")
                }
            }
        } catch {
            // 处理其他错误
            await MainActor.run {
                viewModel.errorMessage = "无法加载咖啡豆数据：\(error.localizedDescription)"
                print("❌ 未分类错误：\(error)")
            }
        }
        
        await MainActor.run {
            isLoading = false
        }
    }
}

// MARK: - PreviewProvider
/*
struct BeanOccurrenceView_Previews: PreviewProvider {
    static var previews: some View {
        BeanOccurrenceView(beanId: 1)
    }
}
*/ 