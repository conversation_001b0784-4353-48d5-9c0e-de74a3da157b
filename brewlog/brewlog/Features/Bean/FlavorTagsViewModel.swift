import SwiftUI

// 风味标签ViewModel
class FlavorTagsViewModel: ObservableObject {
    struct TagCategory {
        let category: String
        let tags: [String]
    }
    
    struct FlavorTag: Codable, Identifiable {
        let id: Int
        let name: String
        let usage_count: Int
    }
    
    @Published var popularTags: [String] = []
    @Published var tagCategories: [TagCategory] = []
    @Published var filteredTags: [String] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private var allTags: Set<String> = []
    private var serverTags: [FlavorTag] = []
    
    func loadTags() {
        isLoading = true
        errorMessage = nil
        
        guard let url = URL(string: "\(APIService.shared.getBaseURL())/ios/api/flavor-tags/") else {
            self.errorMessage = "无效的URL"
            self.isLoading = false
            return
        }
        
        Task {
            do {
                let tags: [FlavorTag] = try await APIService.shared.get("/ios/api/flavor-tags/")
                
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.serverTags = tags
                    
                    // 提取常用标签（使用次数最多的8个）
                    let sortedTags = tags.sorted { $0.usage_count > $1.usage_count }
                    self.popularTags = sortedTags.prefix(8).map { $0.name }
                    
                    // 所有标签用于搜索
                    self.allTags = Set(tags.map { $0.name })
                    self.filteredTags = Array(self.allTags)
                    
                    // 根据预设分类获取标签
                    self.loadPresetCategories()
                }
            } catch {
                DispatchQueue.main.async {
                    self.errorMessage = "加载标签失败: \(error.localizedDescription)"
                    
                    // 加载失败时使用本地预设数据
                    self.loadPresetData()
                }
            }
        }
    }
    
    private func loadPresetData() {
        // 使用预设数据作为备用
        popularTags = ["巧克力", "坚果", "浆果", "柑橘", "焦糖", "花香", "水果", "蜂蜜"]
        tagCategories = [
            TagCategory(category: "水果味", tags: ["苹果", "柠檬", "橙子", "草莓", "蓝莓", "黑莓", "樱桃", "葡萄", "桃子", "梨", "菠萝", "香蕉", "芒果", "番石榴", "黄杏", "青梅"]),
            TagCategory(category: "甜味", tags: ["焦糖", "蜂蜜", "红糖", "巧克力", "太妃糖", "黑糖", "白糖", "枫糖浆", "蔗糖"]),
            TagCategory(category: "坚果味", tags: ["杏仁", "榛子", "开心果", "花生", "核桃", "腰果", "松子", "板栗"]),
            TagCategory(category: "香料", tags: ["肉桂", "丁香", "八角", "黑胡椒", "白胡椒", "小豆蔻", "肉豆蔻", "香草"]),
            TagCategory(category: "花香", tags: ["茉莉", "玫瑰", "薰衣草", "橙花", "紫罗兰", "菊花", "洋甘菊", "桂花"])
        ]
        
        // 收集所有预设标签
        allTags = Set(popularTags)
        for category in tagCategories {
            allTags.formUnion(category.tags)
        }
        
        filteredTags = Array(allTags)
    }
    
    private func loadPresetCategories() {
        // 使用预设分类，但与从服务器加载的标签交叉筛选
        let categories = [
            "水果味": ["苹果", "柠檬", "橙子", "草莓", "蓝莓", "黑莓", "樱桃", "葡萄", "桃子", "梨", "菠萝", "香蕉", "芒果", "番石榴", "黄杏", "青梅"],
            "甜味": ["焦糖", "蜂蜜", "红糖", "巧克力", "太妃糖", "黑糖", "白糖", "枫糖浆", "蔗糖"],
            "坚果味": ["杏仁", "榛子", "开心果", "花生", "核桃", "腰果", "松子", "板栗"],
            "香料": ["肉桂", "丁香", "八角", "黑胡椒", "白胡椒", "小豆蔻", "肉豆蔻", "香草"],
            "花香": ["茉莉", "玫瑰", "薰衣草", "橙花", "紫罗兰", "菊花", "洋甘菊", "桂花"]
        ]
        
        var newCategories: [TagCategory] = []
        
        for (category, tags) in categories {
            // 只保留与服务器标签交集的标签
            let filteredTags = tags.filter { self.allTags.contains($0) }
            if !filteredTags.isEmpty {
                newCategories.append(TagCategory(category: category, tags: filteredTags))
            }
        }
        
        // 如果没有任何匹配标签，使用服务器标签
        if newCategories.isEmpty {
            let userTags = Array(self.allTags).sorted()
            newCategories.append(TagCategory(category: "我的标签", tags: userTags))
        }
        
        self.tagCategories = newCategories
    }
    
    func filterTags(_ query: String) {
        if query.isEmpty {
            filteredTags = Array(allTags)
        } else {
            filteredTags = Array(allTags).filter { $0.localizedCaseInsensitiveContains(query) }
        }
    }
    
    func tagExists(_ tag: String) -> Bool {
        return allTags.contains(tag)
    }
    
    func createTag(_ tagName: String, completion: @escaping (Result<FlavorTag, Error>) -> Void) {
        guard !tagName.isEmpty else {
            completion(.failure(NSError(domain: "FlavorTagsViewModel", code: 400, userInfo: [NSLocalizedDescriptionKey: "标签名称不能为空"])))
            return
        }
        
        let parameters: [String: Any] = ["name": tagName]
        
        Task {
            do {
                let tag: FlavorTag = try await APIService.shared.post("/ios/api/flavor-tags/create/", body: parameters)
                
                DispatchQueue.main.async {
                    // 添加到本地的标签集合中
                    self.serverTags.append(tag)
                    self.allTags.insert(tag.name)
                    self.filteredTags = Array(self.allTags)
                    
                    // 更新分类
                    self.loadPresetCategories()
                    
                    completion(.success(tag))
                }
            } catch {
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
    }
} 