import SwiftUI

struct BeanCard: View {
    let bean: CoffeeBean
    var onDelete: () -> Void
    var onEdit: () -> Void
    var onToggleFavorite: () -> Void
    var onArchive: () -> Void
    var onView: () -> Void
    
    @State private var showingContextMenu = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 标题栏（豆商名+状态图标）
            HStack {
                Text(bean.roaster)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 状态图标区
                HStack(spacing: 4) {
                    if bean.isFavorite {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                    }
                    
                    // 基础状态图标
                    switch bean.baseStatus {
                    case .resting:
                        Image("resting.symbols")
                            .foregroundColor(.gray)
                    case .outOfStock:
                        Image("outtaStock.symbols")
                            .foregroundColor(.red)
                    case .inUse:
                        Image("brewing.symbols")
                            .foregroundColor(.green)
                    case .bestFlavor:
                        // 这个分支实际上不会被执行，因为baseStatus中没有bestFlavor
                        // 但为了完整性保留
                        Image("brewing.symbols")
                            .foregroundColor(.green)
                    }
                    
                    // 单独判断是否在最佳赏味期
                    if bean.isInBestFlavorPeriod && bean.baseStatus == .inUse {
                        Image("flavor.symbols")
                            .foregroundColor(.blue)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.top, 12)
            
            // 豆子名称和评分
            HStack(alignment: .firstTextBaseline) {
                Text(bean.name)
                    .font(.headline)
                    .lineLimit(1)
                
                Spacer()
                
                // 评分星级
                if let rating = bean.restPeriodProgress {
                    HStack(spacing: 2) {
                        ForEach(0..<min(Int(rating/2), 5), id: \.self) { _ in
                            Image(systemName: "heart.fill")
                                .foregroundColor(.red)
                                .font(.caption)
                        }
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.top, 4)
            
            // 标签区域
            HStack {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 6) {
                        // 单品/拼配标签
                        if bean.type != "SKIP" {
                            if bean.type == "SINGLE" {
                                BeanTag(text: "单品", iconName: "square.on.square")
                            } else {
                                BeanTag(text: "拼配", iconName: "square.grid.2x2")
                            }
                        }
                        
                        // 低因咖啡标签
                        if bean.isDecaf {
                            BeanTag(text: "低因", iconName: "bolt.slash")
                        }
                        
                        // 笔记标签
                        if bean.notes != nil && !bean.notes!.isEmpty {
                            BeanTag(text: "笔记", iconName: "note.text")
                        }
                        
                        // 价格标签
                        if let price = bean.purchasePrice {
                            BeanTag(text: "¥\(Int(price))", iconName: nil)
                        }
                        
                        // 处理法标签
                        if let process = bean.process {
                            BeanTag(text: process, iconName: nil)
                        }
                        
                        // 豆种标签
                        if let variety = bean.variety {
                            BeanTag(text: variety, iconName: nil)
                        }
                    }
                }
                
                Spacer()
                
                // 双层进度环
                BeanProgressRings(
                    bagRemain: bean.bagRemain,
                    bagWeight: bean.bagWeight,
                    daysSinceRoast: bean.roastDate.flatMap { date -> Int? in 
                        Calendar.current.dateComponents([.day], from: date, to: Date()).day
                    },
                    restPeriodMin: bean.restPeriodMin,
                    restPeriodMax: bean.restPeriodMax
                )
            }
            .padding(12)
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.primary.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// 咖啡豆标签组件
struct BeanTag: View {
    let text: String
    let iconName: String?
    
    var body: some View {
        HStack(spacing: 2) {
            if let icon = iconName {
                Image(systemName: icon)
                    .font(.caption2)
            }
            
            Text(text)
                .font(.caption2)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(Color.secondary.opacity(0.15))
        .cornerRadius(4)
    }
}

#if DEBUG
struct BeanCard_Previews: PreviewProvider {
    static var previews: some View {
        BeanCard(
            bean: CoffeeBean.preview,
            onDelete: {},
            onEdit: {},
            onToggleFavorite: {},
            onArchive: {},
            onView: {}
        )
        .previewLayout(.sizeThatFits)
        .padding()
    }
}
#endif 