import Foundation

// 定义编辑咖啡豆特定的错误类型
enum EditBeanAPIError: Error {
    case invalidURL
    case invalidResponse
    case networkError(Error)
    case decodingError(Error)
    case encodingError(Error)
    case serverError(Int, String?)
    case unauthorized
    case unknown
    case validationError(String, [String: [String]]?)
    
    var userFriendlyMessage: String {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .invalidResponse:
            return "服务器返回了无效的响应"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .encodingError(let error):
            return "数据编码错误: \(error.localizedDescription)"
        case .serverError(let code, let message):
            return message ?? "服务器错误 (代码: \(code))"
        case .unauthorized:
            return "未授权，请重新登录"
        case .validationError(let message, _):
            return "数据验证错误: \(message)"
        case .unknown:
            return "发生了未知错误"
        }
    }
}

// 控制咖啡豆编辑表单的ViewModel
class EditBeanViewModel: ObservableObject {
    @Published var errorMessage: String?
    @Published var isLoading = false
    
    // 更新咖啡豆
    func updateBean(
        id: Int,
        name: String,
        type: String,
        roaster: String,
        origin: String,
        region: String,
        finca: String,
        variety: String,
        process: String,
        roastLevel: Int,
        notes: String,
        barcode: String,
        bagWeight: Double?,
        bagRemain: Double?,
        purchasePrice: Double?,
        isDecaf: Bool,
        createdAt: Date,
        roastDate: String?,
        isArchived: Bool,
        isFavorite: Bool,
        altitudeType: String,
        altitudeSingle: Int?,
        altitudeMin: Int?,
        altitudeMax: Int?,
        restPeriodMin: Int?,
        restPeriodMax: Int?
    ) async throws {
        // 构建请求体
        var requestBody: [String: Any] = [
            "name": name,
            "type": type,
            "roaster": roaster,
            "origin": origin,
            "region": region,
            "finca": finca,
            "variety": variety,
            "process": process,
            "roast_level": roastLevel,
            "notes": notes,
            "barcode": barcode,
            "is_decaf": isDecaf,
            "is_archived": isArchived,
            "is_favorite": isFavorite,
            "altitude_type": altitudeType
        ]
        
        // 添加可选字段
        if let bagWeight = bagWeight {
            requestBody["bag_weight"] = bagWeight
        }
        
        if let bagRemain = bagRemain {
            requestBody["bag_remain"] = bagRemain
        }
        
        if let purchasePrice = purchasePrice {
            requestBody["purchase_price"] = purchasePrice
        }
        
        // 格式化创建日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        requestBody["created_at"] = dateFormatter.string(from: createdAt)
        
        // 添加烘焙日期
        if let roastDate = roastDate {
            requestBody["roast_date"] = roastDate
        }
        
        // 添加海拔信息
        if altitudeType == "SINGLE" {
            if let altitudeSingle = altitudeSingle {
                requestBody["altitude_single"] = altitudeSingle
            }
        } else if altitudeType == "RANGE" {
            if let altitudeMin = altitudeMin {
                requestBody["altitude_min"] = altitudeMin
            }
            if let altitudeMax = altitudeMax {
                requestBody["altitude_max"] = altitudeMax
            }
        }
        
        // 添加养豆期信息
        if let restPeriodMin = restPeriodMin {
            requestBody["rest_period_min"] = restPeriodMin
        }
        if let restPeriodMax = restPeriodMax {
            requestBody["rest_period_max"] = restPeriodMax
        }
        
        // 创建请求
        let endpoint = "/ios/api/beans/\(id)/"
        guard let url = URL(string: "\(APIService.shared.getBaseURL())\(endpoint)") else {
            throw EditBeanAPIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
        
        // 序列化请求体
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            print("❌ 请求数据序列化失败: \(error)")
            throw EditBeanAPIError.encodingError(error)
        }
        
        // 发送请求
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw EditBeanAPIError.invalidResponse
            }
            
            // 检查响应状态码
            if httpResponse.statusCode == 200 || httpResponse.statusCode == 201 {
                // 解析响应
                do {
                    let _ = try JSONSerialization.jsonObject(with: data) as? [String: Any]
                    // 成功更新
                    return
                } catch {
                    print("❌ 响应解析失败: \(error)")
                    throw EditBeanAPIError.decodingError(error)
                }
            } else {
                // 处理错误响应
                var errorMessage: String?
                if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    errorMessage = errorData["detail"] as? String
                }
                
                if httpResponse.statusCode == 400 {
                    // 尝试解析验证错误
                    if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        print("📦 验证错误: \(errorData)")
                        
                        // 构建NSError以包含响应数据
                        let userInfo: [String: Any] = ["responseData": errorData]
                        throw NSError(domain: "brewlog.APIError", code: 2, userInfo: userInfo)
                    }
                }
                
                throw EditBeanAPIError.serverError(httpResponse.statusCode, errorMessage)
            }
        } catch let error as EditBeanAPIError {
            throw error
        } catch {
            print("❌ 网络请求失败: \(error)")
            throw EditBeanAPIError.networkError(error)
        }
    }
    
    // 通过ID获取咖啡豆
    func getBeanById(_ id: Int) async -> CoffeeBean? {
        do {
            let endpoint = "/ios/api/beans/\(id)/"
            var request = URLRequest(url: URL(string: "\(APIService.shared.getBaseURL())\(endpoint)")!)
            request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            // 添加缓存控制
            request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
            request.cachePolicy = .reloadIgnoringLocalCacheData
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 {
                // 尝试打印响应数据以进行调试
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("📥 getBeanById响应数据: \(jsonString)")
                    
                    // 尝试解析JSON为字典以进行详细检查
                    if let jsonObject = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                        print("📥 响应中关键字段检查:")
                        print("- taste_notes: \(jsonObject["taste_notes"] != nil ? "存在" : "不存在")")
                        if let tasteTags = jsonObject["taste_notes"] as? [String] {
                            print("  值: \(tasteTags.joined(separator: ", "))")
                        }
                        
                        print("- blend_components: \(jsonObject["blend_components"] != nil ? "存在" : "不存在")")
                        if let components = jsonObject["blend_components"] as? [[String: Any]] {
                            print("  数量: \(components.count)")
                        }
                        
                        print("- bag_weight: \(jsonObject["bag_weight"] != nil ? "存在，值: \(jsonObject["bag_weight"]!)" : "不存在")")
                        print("- purchase_price: \(jsonObject["purchase_price"] != nil ? "存在，值: \(jsonObject["purchase_price"]!)" : "不存在")")
                    }
                }
                
                let decoder = JSONDecoder()
                // 设置日期解码策略，以匹配服务器返回的日期格式
                decoder.dateDecodingStrategy = .custom({ decoder in
                    let container = try decoder.singleValueContainer()
                    if let dateString = try? container.decode(String.self) {
                        let formatter = DateFormatter()
                        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
                        if let date = formatter.date(from: dateString) {
                            return date
                        }
                        
                        // 尝试更多格式
                        let formats = ["yyyy-MM-dd'T'HH:mm:ssZ", "yyyy-MM-dd"]
                        for format in formats {
                            formatter.dateFormat = format
                            if let date = formatter.date(from: dateString) {
                                return date
                            }
                        }
                        return Date()
                    } else if let timestamp = try? container.decode(Double.self) {
                        return Date(timeIntervalSince1970: timestamp)
                    }
                    return Date()
                })
                
                // 尝试两种可能的响应结构
                do {
                    // 首先尝试直接解码为CoffeeBean
                    let bean = try decoder.decode(CoffeeBean.self, from: data)
                    return bean
                } catch {
                    print("❌ 直接解码为CoffeeBean失败，尝试解码为SingleBeanResponse: \(error)")
                    
                    // 如果失败，尝试解码为嵌套结构
                    struct SingleBeanResponse: Decodable {
                        let bean: CoffeeBean
                    }
                    
                    let beanResponse = try decoder.decode(SingleBeanResponse.self, from: data)
                    return beanResponse.bean
                }
            } else {
                print("❌ 获取咖啡豆失败，HTTP状态码: \((response as? HTTPURLResponse)?.statusCode ?? -1)")
                if let errorData = String(data: data, encoding: .utf8) {
                    print("❌ 错误响应: \(errorData)")
                }
            }
        } catch {
            print("❌ 获取咖啡豆(ID: \(id))失败: \(error)")
        }
        return nil
    }
}

// 转换日期为字符串
func formatDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateFormat = "yyyy-MM-dd"
    return formatter.string(from: date)
} 