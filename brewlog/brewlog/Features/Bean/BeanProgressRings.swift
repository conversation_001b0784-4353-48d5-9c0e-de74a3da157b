import SwiftUI
import Foundation

// 私有进度环组件
private struct CircularProgressView: View {
    var progress: Double
    var strokeWidth: CGFloat
    var color: Color

    var body: some View {
        ZStack {
            // 移除了带透明度的背景圆环
            Circle()
                .trim(from: 0, to: CGFloat(min(progress, 1.0)))
                .stroke(style: StrokeStyle(
                    lineWidth: strokeWidth,
                    lineCap: .round,
                    lineJoin: .round
                ))
                .foregroundColor(color)
                .rotationEffect(.degrees(-90))
        }
    }
}

struct BeanProgressRings: View {
    // 剩余克数
    var bagRemain: Double?
    // 包装规格
    var bagWeight: Double?
    // 养豆天数
    var daysSinceRoast: Int?
    // 最短养豆期（天）
    var restPeriodMin: Int?
    // 最长养豆期（天）
    var restPeriodMax: Int?

    // 添加主题管理器以响应主题变化
    @Environment(\.colorScheme) private var colorScheme

    // 养豆期最大天数（固定值）
    private let maxRestingDays = 60
    // 库存警戒线（克）
    private let lowStockThreshold = 15.0

    // 计算库存百分比 (0-1)
    private var stockPercentage: Double {
        guard let remain = bagRemain, let weight = bagWeight, weight > 0 else { return 0 }
        return remain / weight
    }

    // 判断是否显示库存环
    private var shouldShowStockRing: Bool {
        // 检查包装规格是否有效(非nil且大于0)
        guard let weight = bagWeight else { return false }
        return weight > 0
    }

    // 获取库存环颜色
    private var stockColor: Color {
        guard let remain = bagRemain else { return .green }
        return remain < lowStockThreshold ? .red : .green
    }

    // 计算养豆进度 (0-1)，天数越少进度越大
    private var restingProgress: Double {
        guard let days = daysSinceRoast, days >= 0 else { return 0 }
        // 将天数转换为剩余进度百分比
        return Double(maxRestingDays - min(days, maxRestingDays)) / Double(maxRestingDays)
    }

    // 判断是否在最佳赏味期
    private var isInPeakPeriod: Bool {
        guard let days = daysSinceRoast,
              days >= 0,
              let minDays = restPeriodMin else {
            return false
        }

        // 如果有最大养豆期，检查是否在范围内
        if let maxDays = restPeriodMax {
            return days >= minDays && days <= maxDays
        }

        // 如果只有最小养豆期，检查是否正好是那一天
        return days == minDays
    }

    // 获取养豆期颜色
    private var restingColor: Color {
        guard let days = daysSinceRoast, days >= 0 else { return .gray }
        return isInPeakPeriod ? .blue : .orange
    }

    // 判断是否显示养豆相关UI
    private var shouldShowRestingUI: Bool {
        guard let days = daysSinceRoast else { return false }
        return days >= 0
    }

    // 格式化数字，移除多余的小数位零
    private func formatNumber(_ number: Double) -> String {
        let format = "%.0f"
        let formattedValue = String(format: format, number)
        return formattedValue
    }

    var body: some View {
        ZStack {
            // 外环(库存) - 仅当有包装规格时显示
            if shouldShowStockRing {
                CircularProgressView(
                    progress: stockPercentage,
                    strokeWidth: 4,
                    color: stockColor
                )
            }

            // 内环(养豆期) - 仅当有烘焙日期时显示
            if shouldShowRestingUI {
                CircularProgressView(
                    progress: restingProgress,
                    strokeWidth: 4,
                    color: restingColor
                )
                .padding(5)  // 减小内边距以让双环更贴合
            }

            // 中心文字
            VStack(spacing: 2) {
                // 库存重量 - 仅当有包装规格时显示
                if shouldShowStockRing, let remain = bagRemain {
                    Text("\(formatNumber(remain))g")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(remain < lowStockThreshold ? .red : (colorScheme == .dark ? Color.white : Color.black))
                }

                // 养豆天数 - 仅当有烘焙日期时显示
                if shouldShowRestingUI, let days = daysSinceRoast {
                    Text(days >= maxRestingDays ? "60+天" : "\(days)天")
                        .font(.system(size: 10))
                        .foregroundColor(restingColor)
                }
            }
        }
        .frame(width: 56, height: 56)

    }
}

#if DEBUG
struct BeanProgressRings_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 未设置烘焙日期和包装规格
            BeanProgressRings(
                bagRemain: 200,
                bagWeight: nil,
                daysSinceRoast: nil,
                restPeriodMin: 14,
                restPeriodMax: 21
            )

            // 未设置包装规格，有烘焙日期
            BeanProgressRings(
                bagRemain: 200,
                bagWeight: nil,
                daysSinceRoast: 7,
                restPeriodMin: 14,
                restPeriodMax: 21
            )

            // 有包装规格，无烘焙日期
            BeanProgressRings(
                bagRemain: 160,
                bagWeight: 200,
                daysSinceRoast: nil,
                restPeriodMin: 14,
                restPeriodMax: 21
            )

            // 普通状态（库存充足）
            BeanProgressRings(
                bagRemain: 160,
                bagWeight: 200,
                daysSinceRoast: 7,
                restPeriodMin: 14,
                restPeriodMax: 21
            )

            // 库存不足15g
            BeanProgressRings(
                bagRemain: 12,
                bagWeight: 200,
                daysSinceRoast: 16,
                restPeriodMin: 14,
                restPeriodMax: 21
            )

            // 最佳赏味期
            BeanProgressRings(
                bagRemain: 100,
                bagWeight: 200,
                daysSinceRoast: 16,
                restPeriodMin: 14,
                restPeriodMax: 21
            )

            // 超过最佳赏味期
            BeanProgressRings(
                bagRemain: 40,
                bagWeight: 200,
                daysSinceRoast: 25,
                restPeriodMin: 14,
                restPeriodMax: 21
            )
        }
    }
}
#endif