import SwiftUI

struct BeanOccurrenceForm: View {
    @ObservedObject var viewModel: BeanOccurrenceViewModel
    var bean: CoffeeBean
    var occurrence: BeanOccurrence?
    var isEditing: Bool
    @Environment(\.presentationMode) var presentationMode
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""
    @State private var useRoastDate = false
    @State private var useRestPeriod = false
    @State private var useRestPeriodRange = false
    @State private var showingValidationAlert = false
    @State private var validationErrors: [String] = []
    @FocusState private var focusedField: String?
    
    init(viewModel: BeanOccurrenceViewModel, bean: CoffeeBean, occurrence: BeanOccurrence? = nil) {
        self.viewModel = viewModel
        self.bean = bean
        self.occurrence = occurrence
        self.isEditing = occurrence != nil
        
        // 初始化状态
        let roastDateExists = occurrence?.roastDate != nil
        let restPeriodMinExists = occurrence?.restPeriodMin != nil
        let restPeriodMaxExists = occurrence?.restPeriodMax != nil
        
        // 使用_useRoastDate来初始化State属性
        _useRoastDate = State(initialValue: roastDateExists)
        _useRestPeriod = State(initialValue: restPeriodMinExists)
        _useRestPeriodRange = State(initialValue: restPeriodMaxExists)
    }
    
    var body: some View {
        ZStack(alignment: .top) {
            // 固定的顶部导航栏
            topNavigationBar
                .background(Color.primaryBg)
                .zIndex(1)
            
            ScrollView {
                VStack(spacing: 8) {
                    // 添加一个占位符，高度等于导航栏高度
                    Color.clear
                        .frame(height: 52)
                    
                    // 表单内容区域
                    VStack(spacing: 20) {
                        // 添加说明文本
                        HStack(spacing: 8) {
                            Image(systemName: "info.circle")
                                .foregroundColor(.noteText)
                            Text("同款咖啡豆的再次补货，不会打断冲煮记录的用豆信息统计。仅更新包装信息，能追踪回购历史。")
                                .font(.footnote)
                                .foregroundColor(.primaryText)
                        }
                        .padding(10)
                        .background(Color.navbarBg)
                        .cornerRadius(6)
                        .padding(.horizontal, 12)
                        .padding(.bottom, 4)

                        // 包装规格和库存余量
                        HStack(spacing: 16) {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("包装规格 (g)")
                                    .font(.system(size: 16, weight: .regular))
                                    .foregroundColor(.primaryText)
                                
                                numberFieldSimple(value: $viewModel.formData.bagWeight, placeholder: "0.00", fieldIdentifier: "包装规格 (g)")
                            }
                            
                            VStack(alignment: .leading, spacing: 8) {
                                Text("库存余量 (g)")
                                    .font(.system(size: 16, weight: .regular))
                                    .foregroundColor(.primaryText)
                                
                                numberFieldSimple(value: $viewModel.formData.bagRemain, placeholder: "0.00", fieldIdentifier: "库存余量 (g)")
                            }
                        }
                        .padding(.horizontal, 16)
                        
                        // 购买时间
                        VStack(alignment: .leading, spacing: 8) {
                            Text("购买时间")
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.primaryText)
                            
                            // 使用系统的直接可见DatePicker但自定义样式
                            DatePicker(
                                "",
                                selection: Binding(
                                    get: { viewModel.formData.createdAt ?? Date() },
                                    set: { viewModel.formData.createdAt = $0 }
                                ),
                                displayedComponents: [.date, .hourAndMinute]
                            )
                            .datePickerStyle(CompactDatePickerStyle())
                            .labelsHidden()
                            .accentColor(.functionText)
                            .cornerRadius(12)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .padding(.horizontal, 16)
                        
                        // 购买价格
                        VStack(alignment: .leading, spacing: 8) {
                            Text("购买价格 (元)")
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.primaryText)
                            
                            numberFieldSimple(value: $viewModel.formData.purchasePrice, placeholder: "0.00", fieldIdentifier: "购买价格 (元)")
                        }
                        .padding(.horizontal, 16)
                        
                        // 烘焙日期开关和相关字段
                        VStack(alignment: .leading, spacing: 10) {
                            // 是否知道烘焙日期开关
                            toggleFieldSimple(title: "是否知道烘焙日期？", isOn: $useRoastDate)
                            
                            if useRoastDate {
                                // 烘焙日期选择器
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("烘焙日期")
                                        .font(.system(size: 16, weight: .regular))
                                        .foregroundColor(.primaryText)
                                    
                                    DatePicker("", selection: Binding(
                                        get: { viewModel.formData.roastDate ?? Date() },
                                        set: { viewModel.formData.roastDate = $0 }
                                    ), displayedComponents: .date)
                                        .datePickerStyle(CompactDatePickerStyle())
                                        .labelsHidden()
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                }
                                
                                // 养豆期设置
                                VStack(alignment: .leading, spacing: 8) {
                                    HStack {
                                        Text("养豆期")
                                            .font(.system(size: 16, weight: .regular))
                                            .foregroundColor(.primaryText)
                                        
                                        Text("(选填，最多60天)")
                                            .font(.caption)
                                            .foregroundColor(.noteText)
                                    }
                                    
                                    // 单一值/范围值切换
                                    HStack(spacing: 8) {
                                        Text("按单一值")
                                            .font(.system(size: 14))
                                        
                                        Toggle("", isOn: $useRestPeriodRange)
                                        .labelsHidden()
                                        .toggleStyle(SwitchToggleStyle(tint: .functionText))
                                        
                                        Text("按范围值")
                                            .font(.system(size: 14))
                                    }
                                    .padding(.vertical, 6)
                                    
                                    // 养豆期输入
                                    if !useRestPeriodRange {
                                        VStack(alignment: .leading, spacing: 8) {
                                            Text("养豆天数")
                                                .font(.system(size: 16, weight: .regular))
                                                .foregroundColor(.primaryText)
                                            
                                            numberFieldSimple(value: $viewModel.formData.restPeriodMin, placeholder: "0", fieldIdentifier: "养豆天数")
                                        }
                                    } else {
                                        HStack(spacing: 16) {
                                            VStack(alignment: .leading, spacing: 8) {
                                                Text("最短养豆期")
                                                    .font(.system(size: 16, weight: .regular))
                                                    .foregroundColor(.primaryText)
                                                
                                                numberFieldSimple(value: $viewModel.formData.restPeriodMin, placeholder: "0", fieldIdentifier: "最短养豆期")
                                            }
                                            
                                            VStack(alignment: .leading, spacing: 8) {
                                                Text("最长养豆期")
                                                    .font(.system(size: 16, weight: .regular))
                                                    .foregroundColor(.primaryText)
                                                
                                                numberFieldSimple(value: $viewModel.formData.restPeriodMax, placeholder: "0", fieldIdentifier: "最长养豆期")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, 16)
                        .onChange(of: useRoastDate) { newValue in
                            if !newValue {
                                viewModel.formData.roastDate = nil
                                useRestPeriod = false
                                useRestPeriodRange = false
                                viewModel.formData.restPeriodMin = ""
                                viewModel.formData.restPeriodMax = ""
                            } else if viewModel.formData.roastDate == nil {
                                viewModel.formData.roastDate = Date()
                            }
                        }
                        .onChange(of: useRestPeriod) { newValue in
                            if !newValue {
                                viewModel.formData.restPeriodMin = ""
                                viewModel.formData.restPeriodMax = ""
                                useRestPeriodRange = false
                            }
                        }
                    }
                    .padding(.vertical, 20)
                    .background(Color.primaryBg)
                    .cornerRadius(10)
                }
                .padding(.horizontal, 16)
            }
            .background(Color.primaryBg)
            .onTapGesture {
                // 点击空白处关闭键盘
                UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
            }
        }
        .background(Color.primaryBg)
        .alert("错误", isPresented: $showingErrorAlert) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(errorMessage)
        }
        .alert("表单验证错误", isPresented: $showingValidationAlert) {
            Button("确定", role: .cancel) {
                showingValidationAlert = false
            }
        } message: {
            VStack(alignment: .leading, spacing: 4) {
                ForEach(validationErrors, id: \.self) { error in
                    Text("• " + error)
                }
            }
        }
        .overlay(
            Group {
                if viewModel.isLoading {
                    BlinkingLoader(text: "处理中...")
                        .padding()
                        .background(Color(.systemBackground))
                        .cornerRadius(8)
                        .shadow(radius: 2)
                }
            }
        )
        .onAppear {
            // 在视图出现时初始化数据
            if let occurrence = occurrence {
                initializeFormForEdit(occurrence: occurrence)
            } else {
                initializeFormForNewOccurrence(from: bean)
            }
        }
        .toolbar {
            ToolbarItemGroup(placement: .keyboard) {
                if focusedField != nil {
                    // 显示常用的包装规格数字选项
                    if focusedField == "包装规格 (g)_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("100g") { viewModel.formData.bagWeight = "100.0"; focusedField = nil }
                                Button("200g") { viewModel.formData.bagWeight = "200.0"; focusedField = nil }
                                Button("250g") { viewModel.formData.bagWeight = "250.0"; focusedField = nil }
                                Button("454g") { viewModel.formData.bagWeight = "454.0"; focusedField = nil }
                                Button("500g") { viewModel.formData.bagWeight = "500.0"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为库存余量也添加常用选项
                    else if focusedField == "库存余量 (g)_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                if let weightStr = viewModel.formData.bagWeight, let weight = Double(weightStr) {
                                    Button("\(Int(weight))g") { viewModel.formData.bagRemain = String(weight); focusedField = nil }
                                }
                                Button("一半") { 
                                    if let weightStr = viewModel.formData.bagWeight, let weight = Double(weightStr) {
                                        viewModel.formData.bagRemain = String(weight / 2)
                                    }
                                    focusedField = nil 
                                }
                                Button("1/4") { 
                                    if let weightStr = viewModel.formData.bagWeight, let weight = Double(weightStr) {
                                        viewModel.formData.bagRemain = String(weight / 4)
                                    }
                                    focusedField = nil 
                                }
                                Button("0g") { viewModel.formData.bagRemain = "0"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为价格添加常用选项
                    else if focusedField == "购买价格 (元)_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("30元") { viewModel.formData.purchasePrice = "30.0"; focusedField = nil }
                                Button("40元") { viewModel.formData.purchasePrice = "40.0"; focusedField = nil }
                                Button("50元") { viewModel.formData.purchasePrice = "50.0"; focusedField = nil }
                                Button("60元") { viewModel.formData.purchasePrice = "60.0"; focusedField = nil }
                                Button("99元") { viewModel.formData.purchasePrice = "99.0"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    // 为养豆期添加常用选项
                    else if focusedField == "养豆天数_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("5天") { viewModel.formData.restPeriodMin = "5"; focusedField = nil }
                                Button("10天") { viewModel.formData.restPeriodMin = "10"; focusedField = nil }
                                Button("14天") { viewModel.formData.restPeriodMin = "14"; focusedField = nil }
                                Button("25天") { viewModel.formData.restPeriodMin = "25"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    else if focusedField == "最短养豆期_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("5天") { viewModel.formData.restPeriodMin = "5"; focusedField = nil }
                                Button("10天") { viewModel.formData.restPeriodMin = "10"; focusedField = nil }
                                Button("14天") { viewModel.formData.restPeriodMin = "14"; focusedField = nil }
                                Button("25天") { viewModel.formData.restPeriodMin = "25"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    else if focusedField == "最长养豆期_field" {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                Button("7天") { viewModel.formData.restPeriodMax = "7"; focusedField = nil }
                                Button("14天") { viewModel.formData.restPeriodMax = "14"; focusedField = nil }
                                Button("21天") { viewModel.formData.restPeriodMax = "21"; focusedField = nil }
                                Button("30天") { viewModel.formData.restPeriodMax = "30"; focusedField = nil }
                            }
                        }
                        .foregroundColor(.functionText)
                    }
                    
                    Spacer()
                    Button(action: {
                        focusedField = nil
                    }) {
                        Text("完成")
                            .foregroundColor(.functionText)
                    }
                }
            }
        }
    }
    
    // 顶部导航栏
    private var topNavigationBar: some View {
        ZStack {
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(.linkText)
                }
                
                Spacer()
                
                Button(action: submitForm) {
                    Text("保存")
                        .fontWeight(.medium)
                        .foregroundColor(!viewModel.isLoading ? .linkText : Color.gray.opacity(0.5))
                }
                .disabled(viewModel.isLoading)
            }
            
            Text(isEditing ? "编辑回购记录" : "新增回购记录")
                .font(.headline)
                .foregroundColor(.primaryText)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
    }
    
    // MARK: - 初始化表单数据
    private func initializeFormForEdit(occurrence: BeanOccurrence) {
        // 包装信息
        viewModel.formData.bagWeight = occurrence.bagWeight.map { String(format: "%.1f", $0) } ?? ""
        viewModel.formData.bagRemain = occurrence.bagRemain.map { String(format: "%.1f", $0) } ?? ""
        viewModel.formData.purchasePrice = occurrence.purchasePrice.map { String(format: "%.2f", $0) } ?? ""
        
        // 日期信息
        viewModel.formData.createdAt = occurrence.createdAt
        viewModel.formData.roastDate = occurrence.roastDate
        useRoastDate = occurrence.roastDate != nil
        
        // 养豆期
        viewModel.formData.restPeriodMin = occurrence.restPeriodMin.map { String($0) } ?? ""
        viewModel.formData.restPeriodMax = occurrence.restPeriodMax.map { String($0) } ?? ""
        useRestPeriod = occurrence.restPeriodMin != nil
        useRestPeriodRange = occurrence.restPeriodMax != nil
    }
    
    private func initializeFormForNewOccurrence(from bean: CoffeeBean) {
        // 如果有现有的咖啡豆信息，自动复制它们
        viewModel.formData.createdAt = Date()
        
        // 检查是否有包装信息可以复制
        if let bagWeight = bean.bagWeight {
            viewModel.formData.bagWeight = String(format: "%.1f", bagWeight)
        } else {
            viewModel.formData.bagWeight = ""
        }
        
        // 库存余量默认与包装规格相同（全新包装）
        if let bagWeight = bean.bagWeight {
            viewModel.formData.bagRemain = String(format: "%.1f", bagWeight)
        } else {
            viewModel.formData.bagRemain = ""
        }
        
        // 购买价格
        if let purchasePrice = bean.purchasePrice {
            viewModel.formData.purchasePrice = String(format: "%.2f", purchasePrice)
        } else {
            viewModel.formData.purchasePrice = ""
        }
        
        // 烘焙日期 - 默认设为当天（避免未来日期）
        viewModel.formData.roastDate = Date() 
        useRoastDate = true  // 默认启用烘焙日期
        
        // 养豆期
        if let restPeriodMin = bean.restPeriodMin {
            viewModel.formData.restPeriodMin = String(restPeriodMin)
            useRestPeriod = true
        } else {
            viewModel.formData.restPeriodMin = ""
            useRestPeriod = false
        }
        
        if let restPeriodMax = bean.restPeriodMax {
            viewModel.formData.restPeriodMax = String(restPeriodMax)
            useRestPeriodRange = true
        } else {
            viewModel.formData.restPeriodMax = ""
            useRestPeriodRange = false
        }
    }
    
    // MARK: - 辅助组件
    // 简化版数字输入字段 - 修改为接受可选字符串类型
    private func numberFieldSimple(
        value: Binding<String?>,
        placeholder: String = "0",
        fieldIdentifier: String
    ) -> some View {
        ZStack(alignment: .leading) {
            if value.wrappedValue?.isEmpty ?? true {
                Text(placeholder)
                    .foregroundColor(.gray)
                    .padding(.leading, 16)
            }
            
            TextField("", text: Binding(
                get: { value.wrappedValue ?? "" },
                set: { value.wrappedValue = $0 }
            ))
                .keyboardType(.decimalPad)
                .disableAutocorrection(true)
                .autocapitalization(.none)
                .font(.system(size: 17))
                .padding(.vertical, 14)
                .padding(.horizontal, 16)
                .focused($focusedField, equals: fieldIdentifier + "_field")
        }
        .background(Color.secondaryBg)
        .cornerRadius(12)
    }
    
    // 简化版开关字段
    private func toggleFieldSimple(title: String, isOn: Binding<Bool>) -> some View {
        HStack {
            Text(title)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.primaryText)
            
            Spacer()
            
            Toggle("", isOn: isOn)
                .labelsHidden()
                .tint(.functionText)
        }
    }
    
    // MARK: - 表单验证
    private func validateForm() -> Bool {
        validationErrors = []
        
        // 包装规格和库存余量验证
        if let weightStr = viewModel.formData.bagWeight, !weightStr.isEmpty {
            if let weight = Double(weightStr) {
                if weight < 0 {
                    validationErrors.append("包装规格不能为负数")
                }
            } else {
                validationErrors.append("包装规格必须是有效数字")
            }
        }
        
        if let remainStr = viewModel.formData.bagRemain, !remainStr.isEmpty {
            if let remain = Double(remainStr) {
                if remain < 0 {
                    validationErrors.append("库存余量不能为负数")
                }
                
                // 检查是否大于包装规格
                if let weightStr = viewModel.formData.bagWeight, 
                   let weight = Double(weightStr),
                   remain > weight {
                    validationErrors.append("库存余量不能大于包装规格")
                }
            } else {
                validationErrors.append("库存余量必须是有效数字")
            }
        }
        
        // 价格验证
        if let priceStr = viewModel.formData.purchasePrice, !priceStr.isEmpty {
            if let price = Double(priceStr) {
                if price < 0 {
                    validationErrors.append("购买价格不能为负数")
                }
            } else {
                validationErrors.append("购买价格必须是有效数字")
            }
        }
        
        // 日期验证 - 检查是否有未来日期
        let today = Calendar.current.startOfDay(for: Date())
        
        // 验证烘焙日期
        if let roastDate = viewModel.formData.roastDate {
            if Calendar.current.startOfDay(for: roastDate) > today {
                validationErrors.append("烘焙日期不能是未来日期")
            }
        }
        
        // 验证购买日期
        if let purchaseDate = viewModel.formData.createdAt {
            if Calendar.current.startOfDay(for: purchaseDate) > today {
                validationErrors.append("购买日期不能是未来日期")
            }
        }
        
        // 养豆期验证
        if useRoastDate {
            if let minRestStr = viewModel.formData.restPeriodMin, !minRestStr.isEmpty {
                if let minRest = Int(minRestStr) {
                    if minRest < 1 || minRest > 60 {
                        validationErrors.append("养豆期必须在1-60天范围内")
                    }
                } else {
                    validationErrors.append("养豆期必须是有效数字")
                }
            }
            
            if useRestPeriodRange {
                if let maxRestStr = viewModel.formData.restPeriodMax, !maxRestStr.isEmpty {
                    if let maxRest = Int(maxRestStr) {
                        if maxRest < 1 || maxRest > 60 {
                            validationErrors.append("最长养豆期必须在1-60天范围内")
                        }
                    } else {
                        validationErrors.append("最长养豆期必须是有效数字")
                    }
                    
                    // 验证最小值和最大值的关系
                    if let minRestStr = viewModel.formData.restPeriodMin, !minRestStr.isEmpty,
                       let maxRestStr = viewModel.formData.restPeriodMax, !maxRestStr.isEmpty,
                       let minRest = Int(minRestStr), let maxRest = Int(maxRestStr), 
                       maxRest < minRest {
                        validationErrors.append("最长养豆期不能小于最短养豆期")
                    }
                }
            }
        }
        
        if !validationErrors.isEmpty {
            showingValidationAlert = true
            return false
        }
        
        return true
    }
    
    // MARK: - 操作方法
    private func submitForm() {
        // 表单验证
        guard validateForm() else { return }
        
        // 处理养豆期范围值只填写了一个的情况
        if useRoastDate && useRestPeriod {
            if useRestPeriodRange {
                if let minRestStr = viewModel.formData.restPeriodMin, !minRestStr.isEmpty,
                   (viewModel.formData.restPeriodMax == nil || viewModel.formData.restPeriodMax!.isEmpty) {
                    // 只有最小值，复制到最大值
                    viewModel.formData.restPeriodMax = viewModel.formData.restPeriodMin
                } else if (viewModel.formData.restPeriodMin == nil || viewModel.formData.restPeriodMin!.isEmpty),
                          let maxRestStr = viewModel.formData.restPeriodMax, !maxRestStr.isEmpty {
                    // 只有最大值，复制到最小值
                    viewModel.formData.restPeriodMin = viewModel.formData.restPeriodMax
                }
            }
        }
        
        // 如果用户输入了bagWeight但未输入bagRemain，则自动设置bagRemain为bagWeight
        if let weightStr = viewModel.formData.bagWeight, !weightStr.isEmpty,
           (viewModel.formData.bagRemain == nil || viewModel.formData.bagRemain!.isEmpty) {
            viewModel.formData.bagRemain = weightStr
        }
        
        // 使用ViewModel中的方法进行二次验证
        guard viewModel.isFormValid() else {
            validationErrors.append("表单数据无效，请检查所有输入。")
            showingValidationAlert = true
            return
        }
        
        // 提交表单前先清除错误状态
        viewModel.error = nil
        
        Task {
            if isEditing {
                // 编辑现有记录
                do {
                    try await viewModel.updateOccurrence()
                } catch {
                    await MainActor.run {
                        viewModel.error = error
                    }
                }
            } else {
                // 创建新记录
                await viewModel.createOccurrence(for: bean)
            }
            
            // 检查操作是否成功
            await MainActor.run {
                if viewModel.error == nil {
                    dismissAndNotify()
                } else {
                    handleSubmitError()
                }
            }
        }
    }
    
    private func dismissAndNotify() {
        // 先获取更新后的Bean数据和编辑的记录ID，以便在关闭表单后马上使用
        let updatedBeanCopy = viewModel.updatedBean
        let editedOccurrenceId = isEditing ? viewModel.selectedOccurrence?.id : nil
        
        // 将已更新的occurrenceId存储到UserDefaults，以便其他视图可以检查
        if let occurrenceId = editedOccurrenceId {
            UserDefaults.standard.set(occurrenceId, forKey: "last_updated_occurrence_id")
            UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: "last_updated_occurrence_time")
            print("⏱️ 已记录最近更新的回购记录ID: \(occurrenceId)")
        }
        
        // 然后关闭表单
        presentationMode.wrappedValue.dismiss()
        
        // 使用延迟确保通知在表单关闭后处理
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            if let updatedBean = updatedBeanCopy {
                print("📣 表单提交后，发送数据更新通知")
                
                // 创建额外的用户信息字典，包含更多详细信息
                var userInfo: [AnyHashable: Any] = [
                    "updatedAt": Date().timeIntervalSince1970,
                    "beanId": updatedBean.id
                ]
                
                // 如果是编辑现有记录，添加记录ID
                if let occurrenceId = editedOccurrenceId {
                    userInfo["occurrenceId"] = occurrenceId
                }
                
                // 只发送一个通知，让所有需要更新的视图都监听这一个通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("BeanDetailNeedsRefresh"), 
                    object: updatedBean,
                    userInfo: userInfo
                )
            }
        }
    }
    
    private func handleSubmitError() {
        if let apiError = viewModel.error as? APIError {
            errorMessage = apiError.userFriendlyMessage
        } else {
            errorMessage = viewModel.error?.localizedDescription ?? "操作失败，请稍后重试"
        }
        showingErrorAlert = true
    }
} 