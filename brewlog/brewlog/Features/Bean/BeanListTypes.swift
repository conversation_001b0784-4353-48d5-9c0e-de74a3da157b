import Foundation

// 咖啡豆排序类型
enum SortType: String, CaseIterable, Identifiable {
    case time = "按时间"
    case rating = "按评分"
    case roaster = "按豆商"
    
    var id: String { rawValue }
}

// 咖啡豆排序选项
enum BeanSortOption: Identifiable, Equatable {
    case time(ascending: Bool)
    case rating(ascending: Bool)
    case roaster(ascending: Bool)
    
    var id: String { rawValue }
    
    // 构造基于排序类型的实例（默认降序）
    init(type: SortType) {
        switch type {
        case .time: self = .time(ascending: false)
        case .rating: self = .rating(ascending: false)
        case .roaster: self = .roaster(ascending: false)
        }
    }
    
    // 切换升序/降序
    func toggleDirection() -> BeanSortOption {
        switch self {
        case .time(let ascending): return .time(ascending: !ascending)
        case .rating(let ascending): return .rating(ascending: !ascending)
        case .roaster(let ascending): return .roaster(ascending: !ascending)
        }
    }
    
    // 获取排序类型
    var sortType: SortType {
        switch self {
        case .time: return .time
        case .rating: return .rating
        case .roaster: return .roaster
        }
    }
    
    // 是否为升序
    var isAscending: Bool {
        switch self {
        case .time(let ascending), .rating(let ascending), .roaster(let ascending):
            return ascending
        }
    }
    
    // 排序键
    var sortKey: String {
        switch self {
        case .time: return "time"
        case .rating: return "rating"
        case .roaster: return "roaster"
        }
    }
    
    // 显示名称
    var rawValue: String {
        let direction = isAscending ? "↑" : "↓"
        switch self {
        case .time: return "\(SortType.time.rawValue)\(direction)"
        case .rating: return "\(SortType.rating.rawValue)\(direction)"
        case .roaster: return "\(SortType.roaster.rawValue)\(direction)"
        }
    }
    
    // 默认排序选项
    static let defaultOption: BeanSortOption = .time(ascending: false)
    
    // 获取所有排序选项
    static var allCases: [BeanSortOption] {
        return SortType.allCases.map { BeanSortOption(type: $0) }
    }
    
    // Equatable协议实现
    static func == (lhs: BeanSortOption, rhs: BeanSortOption) -> Bool {
        switch (lhs, rhs) {
        case (.time(let a1), .time(let a2)): return a1 == a2
        case (.rating(let a1), .rating(let a2)): return a1 == a2
        case (.roaster(let a1), .roaster(let a2)): return a1 == a2
        default: return false
        }
    }
}

// 咖啡豆筛选器
struct BeanListFilter {
    var roastLevel: String?
    var process: String?
    var roaster: String?
    var variety: String?
    var ratingRange: ClosedRange<Double>?
    var sortBy: BeanSortOption = .defaultOption
    var statusFilter: BeanStatus?
    
    // 检查是否有任何筛选条件
    var hasFilters: Bool {
        return roastLevel != nil || 
               process != nil || 
               roaster != nil || 
               variety != nil || 
               ratingRange != nil || 
               sortBy != .defaultOption ||
               statusFilter != nil
    }
    
    // 检查是否有高级筛选条件（不包括状态筛选）
    var hasAdvancedFilters: Bool {
        return roastLevel != nil || 
               process != nil || 
               roaster != nil || 
               variety != nil || 
               ratingRange != nil || 
               sortBy != .defaultOption
    }
    
    // 重置所有筛选条件
    mutating func reset() {
        roastLevel = nil
        process = nil
        roaster = nil
        variety = nil
        ratingRange = nil
        sortBy = .defaultOption
        statusFilter = nil
    }
}

// 咖啡豆状态
enum BeanStatus: String, CaseIterable, Identifiable {
    case resting = "养豆中"
    case inUse = "使用中"
    case outOfStock = "已用完"
    case bestFlavor = "最佳赏味期"
    
    var id: String { rawValue }
}

// 咖啡豆统计
struct BeanStats {
    var resting: Int = 0
    var outOfStock: Int = 0
    var inUse: Int = 0
}

// 咖啡豆分组
struct BeanGroup: Identifiable {
    var id: String { name }
    var name: String
    var beans: [CoffeeBean]
    var order: Int = 0  // 用于控制分组顺序的属性，默认为0
}

// 养豆进度计算扩展
extension CoffeeBean {
    // 计算库存百分比
    var stockPercentage: Double {
        guard let weight = bagWeight, weight > 0, let remain = bagRemain else { return 0 }
        return min(max(remain / weight, 0), 1)
    }
    
    // 计算养豆进度百分比
    var restingProgress: Double {
        guard let roastDate = roastDate, let minDays = restPeriodMin, minDays > 0 else { return 1.0 }
        
        let days = Calendar.current.dateComponents([.day], from: roastDate, to: Date()).day ?? 0
        
        // 如果有范围设置
        if let maxDays = restPeriodMax, maxDays > minDays {
            // 在最小天数之前
            if days < minDays {
                return Double(days) / Double(minDays)
            }
            // 在最小和最大天数之间(最佳期间)
            else if days <= maxDays {
                return 1.0
            }
            // 超过最大天数
            else {
                return 1.0 + (Double(days - maxDays) / Double(maxDays)) // 大于1表示过期
            }
        } else {
            // 只有最小天数设置
            return min(Double(days) / Double(minDays), 1.0)
        }
    }
    
    // 获取自烘焙日期以来的天数
    var daysSinceRoast: Int? {
        guard let roastDate = roastDate else { return nil }
        return Calendar.current.dateComponents([.day], from: roastDate, to: Date()).day
    }
    
    // 判断是否处于最佳赏味期
    var isInBestFlavorPeriod: Bool {
        guard let roastDate = roastDate, 
              let minRestDays = restPeriodMin, 
              let maxRestDays = restPeriodMax else { 
            return false 
        }
        
        let daysSinceRoast = Calendar.current.dateComponents([.day], from: roastDate, to: Date()).day ?? 0
        
        // 如果已经过了最小养豆天数，并且没有超过最大养豆天数，那么就是在最佳赏味期
        return daysSinceRoast >= minRestDays && daysSinceRoast <= maxRestDays
    }
    
    // 基础状态判断（不考虑最佳赏味期）
    var baseStatus: BeanStatus {
        // 首先检查库存是否用完
        if let remain = bagRemain, remain <= 0 {
            return .outOfStock
        }
        
        // 检查是否在养豆期
        if let roastDate = roastDate, let minRestDays = restPeriodMin {
            let days = Calendar.current.dateComponents([.day], from: roastDate, to: Date()).day ?? 0
            if days < minRestDays {
                return .resting
            }
        }
        
        // 否则为使用中
        return .inUse
    }
    
    // 判断豆子当前的状态（筛选器使用）
    var status: BeanStatus {
        let base = baseStatus
        
        // 仅当基础状态为使用中，且处于最佳赏味期时，返回bestFlavor
        if base == .inUse && isInBestFlavorPeriod {
            return .bestFlavor
        }
        
        return base
    }
    
    // 判断豆子是否属于某个状态组（统计和过滤使用）
    func belongsToStatus(_ statusToCheck: BeanStatus) -> Bool {
        // 如果豆子已归档，则不属于任何活跃状态（resting、inUse、outOfStock、bestFlavor）
        if isArchived {
            return false
        }
        
        if statusToCheck == .bestFlavor {
            return baseStatus == .inUse && isInBestFlavorPeriod
        } else if statusToCheck == .inUse {
            // inUse包括普通使用中和最佳赏味期
            return baseStatus == .inUse
        } else {
            return baseStatus == statusToCheck
        }
    }
} 