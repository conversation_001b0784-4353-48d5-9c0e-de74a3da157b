import SwiftUI
import UIKit
// 如果需要，取消注释下面的导入语句
// import Foundation

struct EditBeanView: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel = EditBeanViewModel()
    
    let bean: CoffeeBean
    
    // 添加焦点状态管理
    @FocusState private var focusedField: String?
    
    // Tab选择状态
    @State private var selectedTab: Int = 0 // 0: 基本信息, 1: 包装信息, 2: 详细属性
    
    // 基本信息
    @State private var name: String = ""
    @State private var type: String = "SINGLE"
    @State private var roaster: String = ""
    @State private var notes: String = ""
    @State private var isDecaf: Bool = false
    @State private var flavorTags: [String] = []
    
    // 包装信息
    @State private var barcode: String = ""
    @State private var bagWeight: Double?
    @State private var bagRemain: Double?
    @State private var purchasePrice: Double?
    @State private var createdAt: Date = Date()
    @State private var roastDateEnabled: Bool = false
    @State private var roastDate: Date = Date()
    @State private var restPeriodType: String = "SINGLE"
    @State private var restPeriodMin: Int?
    @State private var restPeriodMax: Int?
    
    // 详细属性
    @State private var origin: String = ""
    @State private var region: String = ""
    @State private var finca: String = ""
    @State private var variety: String = ""
    @State private var process: String = ""
    @State private var roastLevel: Int = 4
    @State private var altitudeType: String = "SINGLE"
    @State private var altitudeSingle: Int?
    @State private var altitudeMin: Int?
    @State private var altitudeMax: Int?
    
    // 拼配豆组件
    @State private var blendComponents: [MutableBlendComponent] = []
    
    // UI状态控制
    @State private var errorMessage: String?
    @State private var isLoading = false
    @State private var showingFlavorTagsPicker = false
    @State private var showingValidationAlert = false
    @State private var validationErrors: [String] = []
    @State private var isArchived: Bool = false
    @State private var isFavorite: Bool = false
    
    // 初始化方法
    init(bean: CoffeeBean) {
        self.bean = bean
    }
    
    // 计算属性
    private var shouldShowDetails: Bool {
        return type != "SKIP"
    }
    
    private var isValid: Bool {
        !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !roaster.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // 检查表单是否已被修改
    private var isFormModified: Bool {
        // 基本信息
        if name != bean.name ||
           type != bean.type ||
           roaster != bean.roaster ||
           notes != (bean.notes ?? "") ||
           isDecaf != bean.isDecaf ||
           !areArraysEqual(flavorTags, bean.tasteNotes ?? []) {
            return true
        }
        
        // 包装信息
        if barcode != (bean.barcode ?? "") ||
           bagWeight != bean.bagWeight ||
           bagRemain != bean.bagRemain ||
           purchasePrice != bean.purchasePrice ||
           createdAt != bean.createdAt {
            return true
        }
        
        // 烘焙日期相关
        let hasRoastDate = bean.roastDate != nil
        if roastDateEnabled != hasRoastDate {
            return true
        }
        
        if roastDateEnabled && bean.roastDate != nil && 
           roastDate != bean.roastDate! {
            return true
        }
        
        // 养豆期设置
        if roastDateEnabled {
            // 如果原始数据同时有最小和最大值，但当前不是range模式，则已修改
            if bean.restPeriodMin != nil && bean.restPeriodMax != nil && restPeriodType != "RANGE" {
                return true
            }
            
            // 如果当前是range模式，但原始数据不同时拥有最小和最大值，则已修改
            if restPeriodType == "RANGE" && (bean.restPeriodMin == nil || bean.restPeriodMax == nil) {
                return true
            }
            
            // 检查具体的值是否发生变化
            if restPeriodMin != bean.restPeriodMin || 
               (restPeriodType == "RANGE" && restPeriodMax != bean.restPeriodMax) {
                return true
            }
        }
        
        // 详细属性（只有非SKIP类型才检查）
        if type != "SKIP" {
            if origin != (bean.origin ?? "") ||
               region != (bean.region ?? "") ||
               finca != (bean.finca ?? "") ||
               variety != (bean.variety ?? "") ||
               process != (bean.process ?? "") ||
               roastLevel != bean.roastLevel {
                return true
            }
            
            // 海拔设置
            if altitudeType != bean.altitudeType {
                return true
            }
            
            if altitudeType == "SINGLE" && altitudeSingle != bean.altitudeSingle {
                return true
            }
            
            if altitudeType == "RANGE" && (altitudeMin != bean.altitudeMin || altitudeMax != bean.altitudeMax) {
                return true
            }
            
            // 拼配组件 (复杂对象比较，简单判断数量是否有变化)
            if type == "BLEND" {
                let originalCount = bean.blendComponents?.count ?? 0
                if blendComponents.count != originalCount {
                    return true
                }
                
                // 简单比较各个主要属性
                if originalCount > 0 && bean.blendComponents != nil {
                    for (index, component) in blendComponents.enumerated() {
                        if index >= bean.blendComponents!.count {
                            return true
                        }
                        
                        let original = bean.blendComponents![index]
                        if component.blendRatio != original.blendRatio ||
                           component.roastLevel != original.roastLevel ||
                           component.origin != original.origin ||
                           component.region != original.region ||
                           component.process != original.process ||
                           component.finca != original.finca ||
                           component.variety != original.variety ||
                           component.altitudeType != original.altitudeType ||
                           component.altitudeSingle != original.altitudeSingle ||
                           component.altitudeMin != original.altitudeMin ||
                           component.altitudeMax != original.altitudeMax {
                            return true
                        }
                    }
                }
            }
        }
        
        return false
    }
    
    // 辅助函数比较两个数组是否相等
    private func areArraysEqual<T: Equatable>(_ first: [T], _ second: [T]) -> Bool {
        if first.count != second.count {
            return false
        }
        
        for (index, element) in first.enumerated() {
            if element != second[index] {
                return false
            }
        }
        
        return true
    }
    
    var body: some View {
        ZStack(alignment: .top) {
            // 固定的顶部导航栏
            topNavigationBar
                .background(Color.primaryBg)
                .zIndex(1)
            
            ScrollViewReader { proxy in
                ScrollView {
                    VStack(spacing: 8) {
                        // 添加一个占位符，高度等于导航栏高度
                        Color.clear
                            .frame(height: 52)
                        
                        // 主要内容区域
                        VStack(spacing: 16) {
                            // 顶部Tab切换
                            tabSelectionView
                            
                            // 内容区域
                            tabContentView
                        }
                        .padding(.horizontal, 12)
                    }
                }
                .background(
                    Color.primaryBg
                )
                .contentShape(Rectangle())
                .onTapGesture {
                    hideKeyboard()
                }
                .onAppear {
                    scrollProxy = proxy
                    loadBeanData()
                }
            }
        }
        .alert("错误", isPresented: Binding(
            get: { errorMessage != nil },
            set: { if !$0 { errorMessage = nil } }
        )) {
            Text(errorMessage ?? "")
            Button("确定", role: .cancel) {}
        }
        .alert("表单验证错误", isPresented: $showingValidationAlert) {
            Button("确定", role: .cancel) {
                showingValidationAlert = false
            }
        } message: {
            VStack(alignment: .leading, spacing: 4) {
                ForEach(validationErrors, id: \.self) { error in
                    Text("• " + error)
                }
            }
        }
        .overlay(
            Group {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.4)

                        BlinkingLoader(
                            color: .primaryBg,
                            width: 12,
                            height: 18,
                            duration: 1.5,
                            text: "保存中..."
                        )
                        .padding()
                        .background(Color.secondaryBg.opacity(0.9))
                        .cornerRadius(10)
                        .shadow(radius: 5)
                    }
                }
            }
        )
        .toolbar {
            ToolbarItemGroup(placement: .keyboard) {
                // 工具栏内容将在后续添加
                Spacer()
                Button(action: {
                    focusedField = nil
                }) {
                    Text("完成")
                        .foregroundColor(.functionText)
                }
            }
        }
        .sheet(isPresented: $showingFlavorTagsPicker) {
            FlavorTagsInput(selectedTags: $flavorTags)
        }
    }
    
    // 加载咖啡豆数据
    private func loadBeanData() {
        // 基本信息
        name = bean.name
        type = bean.type
        roaster = bean.roaster
        notes = bean.notes ?? ""
        isDecaf = bean.isDecaf
        flavorTags = bean.tasteNotes ?? []
        
        // 包装信息
        barcode = bean.barcode ?? ""  // 仍然加载条形码，只是不显示在界面上
        bagWeight = bean.bagWeight
        bagRemain = bean.bagRemain
        purchasePrice = bean.purchasePrice
        createdAt = bean.createdAt
        
        if let beanRoastDate = bean.roastDate {
            roastDateEnabled = true
            roastDate = beanRoastDate
        } else {
            roastDateEnabled = false
            roastDate = Date()
        }
        
        // 养豆期设置
        if let min = bean.restPeriodMin, let max = bean.restPeriodMax {
            // 两个值都有，使用范围模式
            restPeriodType = "RANGE"
            restPeriodMin = min
            restPeriodMax = max
        } else if let min = bean.restPeriodMin {
            // 只有最小值，使用单一模式
            restPeriodType = "SINGLE"
            restPeriodMin = min
            restPeriodMax = nil
        } else if let max = bean.restPeriodMax {
            // 只有最大值，使用单一模式且值设为最大值
            restPeriodType = "SINGLE"
            restPeriodMin = max  // 将最大值作为单一值
            restPeriodMax = nil
        } else {
            // 两个值都没有
            restPeriodType = "SINGLE"
            restPeriodMin = nil
            restPeriodMax = nil
        }
        
        // 详细属性
        origin = bean.origin ?? ""
        region = bean.region ?? ""
        finca = bean.finca ?? ""
        variety = bean.variety ?? ""
        process = bean.process ?? ""
        roastLevel = bean.roastLevel
        
        // 海拔设置
        altitudeType = bean.altitudeType
        altitudeSingle = bean.altitudeSingle
        altitudeMin = bean.altitudeMin
        altitudeMax = bean.altitudeMax
        
        // 其他属性 - 这些属性不在UI上显示，但在保存时需要
        isArchived = bean.isArchived
        isFavorite = bean.isFavorite
        
        // 拼配组件
        if type == "BLEND", let components = bean.blendComponents {
            blendComponents = components.map { MutableBlendComponent(from: $0) }
        } else {
            // 默认添加一个组件
            blendComponents = [MutableBlendComponent(from: BlendComponent.defaultComponent())]
        }
    }
    
    // 隐藏键盘方法
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    // 添加ScrollViewProxy用于滚动控制
    @State private var scrollProxy: ScrollViewProxy?
    
    // 顶部导航栏
    private var topNavigationBar: some View {
        ZStack {
                HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(.linkText)
                }
                
                    Spacer()
                
                Button(action: saveBean) {
                    Text("保存")
                        .fontWeight(.medium)
                        .foregroundColor(isValid && isFormModified && !isLoading ? .linkText : Color.gray.opacity(0.5))
                }
                .disabled(!isValid || !isFormModified || isLoading)
            }
            
            Text("编辑咖啡豆")
                .font(.headline)
                .foregroundColor(.primaryText)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
    }
    
    // 顶部Tab选择视图
    private var tabSelectionView: some View {
        HStack(spacing: 0) {
            tabButton(title: "基本信息", index: 0)
            tabButton(title: "包装信息(选填)", index: 1)
            tabButton(title: type == "SKIP" ? "详细属性(跳过)" : "详细属性", index: 2, isDisabled: !shouldShowDetails)
        }
        .padding(.bottom, 8)
    }
    
    // Tab按钮
    private func tabButton(title: String, index: Int, isDisabled: Bool = false) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                selectedTab = index
            }
        }) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(
                        isDisabled 
                        ? Color.gray.opacity(0.5) 
                        : (selectedTab == index ? Color.primary : Color.gray)
                    )
                    .padding(.vertical, 8)
                
                // 选中指示器
                Rectangle()
                    .frame(height: 2)
                    .foregroundColor(selectedTab == index ? .functionText : .clear)
                    .animation(.spring(response: 0.3, dampingFraction: 0.8), value: selectedTab)
            }
        }
        .frame(maxWidth: .infinity)
        .buttonStyle(PlainButtonStyle())
        .disabled(isDisabled)
    }

    // Tab内容视图
    private var tabContentView: some View {
        VStack(spacing: 16) {
            switch selectedTab {
            case 0:
                generalInfoSection()
            case 1:
                productInfoSection()
            case 2:
                if shouldShowDetails {
                    detailsSection()
                } else {
                    VStack {
                        Text("详细属性仅对单品和拼配有效")
                            .foregroundColor(.primaryText)
                            .padding(20)
                    }
                    .frame(maxWidth: .infinity)
                    .background(Color.secondaryBg.opacity(0.15))
                    .cornerRadius(8)
                }
            default:
                EmptyView()
            }
        }
        .padding(.bottom, 20)
    }
    
    // 验证表单数据
    private func validateForm() -> Bool {
        validationErrors = []
        
        // 基本必填字段验证
        if name.isEmpty {
            validationErrors.append("咖啡豆名称不能为空")
        } else if name.count > 50 {
            validationErrors.append("咖啡豆名称不能超过50个字符")
        }
        
        if roaster.isEmpty {
            validationErrors.append("豆商不能为空")
        } else if roaster.count > 50 {
            validationErrors.append("豆商名称不能超过50个字符")
        }
        
        // 包装规格和库存余量验证
        if let weight = bagWeight, weight < 0 {
            validationErrors.append("包装规格不能为负数")
        }
        
        if let remain = bagRemain, remain < 0 {
            validationErrors.append("库存余量不能为负数")
        }
        
        // 价格验证
        if let price = purchasePrice, price < 0 {
            validationErrors.append("购买价格不能为负数")
        }
        
        // 养豆期验证
        if roastDateEnabled {
            if let minRest = restPeriodMin {
                if minRest < 1 || minRest > 60 {
                    validationErrors.append("养豆期必须在1-60天范围内")
                }
            }
            
            if restPeriodType == "RANGE" {
                if let maxRest = restPeriodMax {
                    if maxRest < 1 || maxRest > 60 {
                        validationErrors.append("最长养豆期必须在1-60天范围内")
                    }
                    
                    if let minRest = restPeriodMin, maxRest < minRest {
                        validationErrors.append("最长养豆期不能小于最短养豆期")
                    }
                }
            }
        }
        
        // 海拔验证
        if type != "SKIP" {
                if altitudeType == "SINGLE" {
                if let altitude = altitudeSingle, (altitude < 0 || altitude > 9999) {
                    validationErrors.append("海拔必须在0-9999米范围内")
                    }
                } else {
                if let minAlt = altitudeMin, (minAlt < 0 || minAlt > 9999) {
                    validationErrors.append("最低海拔必须在0-9999米范围内")
                }
                
                if let maxAlt = altitudeMax, (maxAlt < 0 || maxAlt > 9999) {
                    validationErrors.append("最高海拔必须在0-9999米范围内")
                }
                
                if let minAlt = altitudeMin, let maxAlt = altitudeMax, maxAlt < minAlt {
                    validationErrors.append("最高海拔不能小于最低海拔")
                }
            }
        }
        
        // 拼配豆验证
        if type == "BLEND" {
            // 检查总比例是否接近100%
            let totalRatio = calculateTotalRatio()
            if abs(totalRatio - 100) > 0.02 {
                validationErrors.append("拼配豆比例总和必须等于100%")
            }
            
            // 检查每个组件的海拔设置
            for (index, component) in blendComponents.enumerated() {
                if component.altitudeType == "SINGLE" {
                    if let altitude = component.altitudeSingle, (altitude < 0 || altitude > 9999) {
                        validationErrors.append("拼配豆#\(index + 1)的海拔必须在0-9999米范围内")
                    }
                } else {
                    if let minAlt = component.altitudeMin, (minAlt < 0 || minAlt > 9999) {
                        validationErrors.append("拼配豆#\(index + 1)的最低海拔必须在0-9999米范围内")
                    }
                    
                    if let maxAlt = component.altitudeMax, (maxAlt < 0 || maxAlt > 9999) {
                        validationErrors.append("拼配豆#\(index + 1)的最高海拔必须在0-9999米范围内")
                    }
                    
                    if let minAlt = component.altitudeMin, let maxAlt = component.altitudeMax, maxAlt < minAlt {
                        validationErrors.append("拼配豆#\(index + 1)的最高海拔不能小于最低海拔")
                    }
                }
            }
        }
        
        if !validationErrors.isEmpty {
            showingValidationAlert = true
            return false
        }
        
        return true
    }
    
    // 计算总比例
    private func calculateTotalRatio() -> Double {
        let total = blendComponents.reduce(0) { $0 + Double($1.blendRatio) }
        return (total * 100).rounded() / 100 // 保留两位小数
    }

    // 保存咖啡豆
    private func saveBean() {
        // 首先进行表单验证
        guard validateForm() else { return }
        guard isValid else { return }
        guard isFormModified else { return }
        
        isLoading = true
        errorMessage = nil
        
        // 如果用户输入了bagWeight但未输入bagRemain，则自动设置bagRemain为bagWeight
        if let weight = bagWeight, bagRemain == nil {
            bagRemain = weight
        }
        
        // 处理养豆期范围值只填写了一个的情况
        var finalRestPeriodType = restPeriodType
        var finalRestPeriodMin = restPeriodMin
        var finalRestPeriodMax = restPeriodMax
        
        if roastDateEnabled && restPeriodType == "RANGE" {
            if (restPeriodMin != nil && restPeriodMax == nil) || (restPeriodMin != nil && restPeriodMax == 0) {
                // 只有最小值或最长值为0，转为单一值
                finalRestPeriodType = "SINGLE"
                finalRestPeriodMin = restPeriodMin  // 保持最小值作为单一值
                finalRestPeriodMax = nil
            } else if (restPeriodMin == nil && restPeriodMax != nil) || (restPeriodMin == 0 && restPeriodMax != nil && restPeriodMax! > 0) {
                // 只有最大值或最短值为0，转为单一值
                finalRestPeriodType = "SINGLE"
                finalRestPeriodMin = restPeriodMax  // 使用最大值作为单一值
                finalRestPeriodMax = nil
            }
        }
        
        // 处理海拔范围值只填写了一个的情况
        var finalAltitudeType = altitudeType
        var finalAltitudeSingle = altitudeSingle
        var finalAltitudeMin = altitudeMin
        var finalAltitudeMax = altitudeMax
        
        if type != "SKIP" && altitudeType == "RANGE" {
            if (altitudeMin != nil && altitudeMax == nil) || (altitudeMin != nil && altitudeMax == 0) {
                // 只有最小值或最高值为0，转为单一值
                finalAltitudeType = "SINGLE"
                finalAltitudeSingle = altitudeMin
                finalAltitudeMin = nil
                finalAltitudeMax = nil
            } else if (altitudeMin == nil && altitudeMax != nil) || (altitudeMin == 0 && altitudeMax != nil && altitudeMax! > 0) {
                // 只有最大值或最低值为0，转为单一值
                finalAltitudeType = "SINGLE"
                finalAltitudeSingle = altitudeMax
                finalAltitudeMin = nil
                finalAltitudeMax = nil
            }
        }
        
        // 定义一个函数用于刷新BeanListView
        func refreshBeanList() async {
            if let beanListViewModel = brewlog.SharedViewModels.shared.beanListViewModel {
                await beanListViewModel.fetchCoffeeBeans(forceRefresh: true)
                print("✅ 成功刷新咖啡豆列表")
                
                // 确保主线程更新UI
                await MainActor.run {
                    // 发送通知，通知BeanListView刷新
                    NotificationCenter.default.post(name: NSNotification.Name("BeanListNeedsRefresh"), object: nil)
                }
            } else {
                print("⚠️ 未找到BeanListViewModel实例，无法刷新列表")
            }
        }
        
        Task {
            do {
                // 删除未使用的变量
                let restPeriodMinToSave = roastDateEnabled ? finalRestPeriodMin : nil
                let restPeriodMaxToSave = roastDateEnabled && finalRestPeriodType == "RANGE" ? finalRestPeriodMax : nil
                
                // 将日期转换为YYYY-MM-DD格式字符串，而不是时间戳
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                let roastDateString = roastDateEnabled ? dateFormatter.string(from: roastDate) : nil
                
                // 构建请求体
                var requestBody: [String: Any] = [
                    "name": name,
                    "type": type,
                    "roaster": roaster,
                    "origin": type == "SKIP" ? "" : origin,
                    "region": type == "SKIP" ? "" : region,
                    "finca": type == "SKIP" ? "" : finca,
                    "variety": type == "SKIP" ? "" : variety,
                    "process": type == "SKIP" ? "" : process,
                    "roast_level": roastLevel,
                    "notes": notes.isEmpty ? "" : notes,
                    "barcode": barcode.isEmpty ? "" : barcode,
                    "is_decaf": isDecaf,
                    "is_archived": isArchived,
                    "is_favorite": isFavorite,
                    "altitude_type": type == "SKIP" ? "SINGLE" : finalAltitudeType,
                    "taste_notes": flavorTags  // 添加风味标签字段
                ]

                // 添加可选字段
                if let bagWeight = bagWeight {
                    requestBody["bag_weight"] = bagWeight
                }

                if let bagRemain = bagRemain {
                    requestBody["bag_remain"] = bagRemain
                }

                if let purchasePrice = purchasePrice {
                    requestBody["purchase_price"] = purchasePrice
                }
                
                // 添加拼配组件字段
                if type == "BLEND" {
                    let processedComponents = processBlendComponents()
                    let componentsData = processedComponents.map { component -> [String: Any] in
                        var data: [String: Any] = [
                            "blend_ratio": component.blendRatio,
                            "roast_level": component.roastLevel,
                            "origin": component.origin ?? "",
                            "region": component.region ?? "",
                            "process": component.process ?? "",
                            "finca": component.finca ?? "",
                            "variety": component.variety ?? "",
                            "altitude_type": component.altitudeType
                        ]
                        
                        if component.altitudeType == "SINGLE" {
                            if let altitudeSingle = component.altitudeSingle {
                                data["altitude_single"] = altitudeSingle
                            }
                        } else if component.altitudeType == "RANGE" {
                            if let altitudeMin = component.altitudeMin {
                                data["altitude_min"] = altitudeMin
                            }
                            if let altitudeMax = component.altitudeMax {
                                data["altitude_max"] = altitudeMax
                            }
                        }
                        
                        return data
                    }
                    requestBody["blend_components"] = componentsData
                }

                // 将创建日期改为时间戳格式而不是字符串格式
                requestBody["created_at"] = Int(createdAt.timeIntervalSince1970)

                // 添加烘焙日期
                if let roastDate = roastDateString {
                    requestBody["roast_date"] = roastDate
                }
                
                // 添加海拔信息
                if type != "SKIP" {
                    if finalAltitudeType == "SINGLE" {
                        if let altitudeSingle = finalAltitudeSingle {
                            requestBody["altitude_single"] = altitudeSingle
                        }
                    } else if finalAltitudeType == "RANGE" {
                        if let altitudeMin = finalAltitudeMin {
                            requestBody["altitude_min"] = altitudeMin
                        }
                        if let altitudeMax = finalAltitudeMax {
                            requestBody["altitude_max"] = altitudeMax
                        }
                    }
                }
                
                // 添加养豆期信息
                if let restPeriodMin = restPeriodMinToSave {
                    requestBody["rest_period_min"] = restPeriodMin
                }
                if let restPeriodMax = restPeriodMaxToSave {
                    requestBody["rest_period_max"] = restPeriodMax
                }
                
                // 创建请求
                let endpoint = "/ios/api/beans/\(bean.id)/"
                guard let url = URL(string: "\(APIService.shared.getBaseURL())\(endpoint)") else {
                    throw EditBeanAPIError.invalidURL
                }
                
                var request = URLRequest(url: url)
                request.httpMethod = "PUT"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
                
                // 序列化请求体并打印请求信息用于调试
                do {
                    let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
                    request.httpBody = jsonData
                    
                    // 打印请求信息用于调试
                    print("📤 发送请求到 \(endpoint)")
                    if let jsonString = String(data: jsonData, encoding: .utf8) {
                        print("📦 请求体: \(jsonString)")
                    }
                    
                    // 单独检查关键字段
                    print("📦 taste_notes字段: \(requestBody["taste_notes"] != nil ? "存在" : "不存在")")
                    if let tasteTags = requestBody["taste_notes"] as? [String] {
                        print("📦 taste_notes内容: \(tasteTags.joined(separator: ", "))")
                    }
                    
                    if type == "BLEND" {
                        print("📦 blend_components字段: \(requestBody["blend_components"] != nil ? "存在" : "不存在")")
                        if let components = requestBody["blend_components"] as? [[String: Any]] {
                            print("📦 blend_components数量: \(components.count)")
                            for (i, component) in components.enumerated() {
                                print("📦 组件#\(i+1): \(component["blend_ratio"] ?? "未知")%, 产地: \(component["origin"] ?? "未知")")
                            }
                        }
                    }
                } catch {
                    print("❌ 请求数据序列化失败: \(error)")
                    throw EditBeanAPIError.encodingError(error)
                }
                
                // 发送请求
                let (data, response) = try await URLSession.shared.data(for: request)
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    throw EditBeanAPIError.invalidResponse
                }
                
                // 检查响应状态码
                if httpResponse.statusCode == 200 || httpResponse.statusCode == 201 {
                    // 解析响应
                    do {
                        if let jsonString = String(data: data, encoding: .utf8) {
                            print("📥 响应数据: \(jsonString)")
                        }
                        
                        let responseDict = try JSONSerialization.jsonObject(with: data) as? [String: Any]
                        print("✅ 更新成功，响应: \(responseDict ?? [:])")
                        
                        // 获取更新后的咖啡豆以发送通知
                        if let updatedBean = await viewModel.getBeanById(bean.id) {
                            print("✅ 成功获取更新后的咖啡豆数据: \(updatedBean.name)")
                            // 打印额外的调试信息
                            print("📊 风味标签状态: \(updatedBean.tasteNotes != nil ? "有数据，数量 \(updatedBean.tasteNotes!.count)" : "无数据")")
                            if let tags = updatedBean.tasteNotes, !tags.isEmpty {
                                print("📊 风味标签内容: \(tags.joined(separator: ", "))")
                            }
                            
                            print("📊 拼配组件状态: \(updatedBean.blendComponents != nil ? "有数据，数量 \(updatedBean.blendComponents!.count)" : "无数据")")
                            if let components = updatedBean.blendComponents, !components.isEmpty {
                                for (i, component) in components.enumerated() {
                                    print("📊 拼配组件#\(i+1): 比例 \(component.blendRatio)%, 烘焙度 \(component.roastLevel), 产地 \(component.origin ?? "无")")
                                }
                            }
                            
                            // 确保主线程更新UI
                            await MainActor.run {
                                // 发送通知，通知BeanDetailView刷新
                                NotificationCenter.default.post(name: NSNotification.Name("BeanDetailNeedsRefresh"), object: updatedBean)
                                
                                // 发送通知，通知BeanListView刷新
                                NotificationCenter.default.post(name: NSNotification.Name("BeanListNeedsRefresh"), object: nil)
                                
                                // 保存更新后的咖啡豆数据到UserDefaults，确保其他视图可以访问
                                do {
                                    let encoder = JSONEncoder()
                                    let beanData = try encoder.encode(updatedBean)
                                    UserDefaults.standard.set(beanData, forKey: "saved_bean_\(updatedBean.id)")
                                    print("📝 编辑后已将咖啡豆数据保存到UserDefaults，ID: \(updatedBean.id)，名称: \(updatedBean.name)")
                                } catch {
                                    print("❌ 编码咖啡豆数据失败: \(error.localizedDescription)")
                                }
                                
                                // 成功后直接关闭表单返回到详情页
                                presentationMode.wrappedValue.dismiss()
                            }
                        } else {
                            // 如果无法获取更新后的咖啡豆，仍然发送列表刷新通知
                            await refreshBeanList()
                            
                            // 成功后关闭表单
                            await MainActor.run {
                                presentationMode.wrappedValue.dismiss()
                            }
                        }
                    } catch {
                        print("❌ 响应解析失败: \(error)")
                        throw EditBeanAPIError.decodingError(error)
                    }
                } else {
                    // 处理错误响应
                    var errorMessage: String?
                    if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        errorMessage = errorData["detail"] as? String ?? errorData["error"] as? String
                        print("📦 验证错误: \(errorData)")
                        
                        // 构建NSError以包含响应数据
                        let userInfo: [String: Any] = ["responseData": errorData]
                        throw NSError(domain: "brewlog.APIError", code: 2, userInfo: userInfo)
                    }
                    
                    throw EditBeanAPIError.serverError(httpResponse.statusCode, errorMessage)
                }
                
            } catch let error as NSError {
                print("❌ 保存咖啡豆失败: \(error)")
                
                // 处理唯一性冲突错误
                if error.domain == "brewlog.APIError" && error.code == 2 {
                    print("🔍 错误域: \(error.domain), 错误码: \(error.code)")
                    print("🔍 错误信息: \(error.localizedDescription)")
                    print("🔍 用户信息: \(error.userInfo)")
                    
                    if let responseData = error.userInfo["responseData"] as? [String: Any],
                       let errorMessage = responseData["error"] as? String {
                        self.errorMessage = errorMessage
                    } else {
                        self.errorMessage = "可能存在相同的豆商+咖啡豆组合，请更改名称后重试"
                    }
                } 
                // 处理API解析错误
                else if error.domain == "brewlog.APIError" && error.code == 3 {
                    print("🔍 错误域: \(error.domain), 错误码: \(error.code)")
                    print("🔍 错误信息: \(error.localizedDescription)")
                    print("🔍 用户信息: \(error.userInfo)")
                    
                    if let responseData = error.userInfo["responseData"] as? [String: Any] {
                        print("📦 解析失败的响应数据: \(responseData)")
                        
                        // 尝试从嵌套结构中提取数据
                        if let nestedData = responseData["data"] as? [String: Any],
                           let beanId = nestedData["id"] as? Int {
                            print("✅ 服务器实际返回了有效数据，ID: \(beanId)")
                            self.errorMessage = "操作可能已成功，但解析响应时出错。请刷新列表确认。"
                            
                            // 尝试手动刷新列表
                            Task {
                                await refreshBeanList()
                                
                                // 操作实际上可能已成功，关闭表单
                                await MainActor.run {
                                    self.presentationMode.wrappedValue.dismiss()
                                }
                            }
                            return
                        }
                        
                        self.errorMessage = "数据解析错误，但操作可能已成功。请刷新后检查。"
                    } else {
                        self.errorMessage = "数据格式错误，请联系开发者"
                    }
                } else {
                    // 打印更多错误信息以便调试
                    print("🔍 错误域: \(error.domain), 错误码: \(error.code)")
                    print("🔍 错误信息: \(error.localizedDescription)")
                    print("🔍 用户信息: \(error.userInfo)")
                    
                    errorMessage = error.localizedDescription
                }
            } catch let apiError as APIError {
                print("❌ API错误: \(apiError)")
                
                // 根据API错误类型提供更友好的提示
                switch apiError {
                case .decodingError(let decodingError):
                    print("📦 解码错误: \(decodingError)")
                    self.errorMessage = "服务器响应格式异常，但操作可能已成功。请刷新页面查看结果。"
                    
                    // 尝试手动刷新列表
                    Task {
                        await refreshBeanList()
                        
                        // 假设操作已成功，关闭表单
                        await MainActor.run {
                            self.presentationMode.wrappedValue.dismiss()
                        }
                    }
                case .serverError(let code, let message):
                    // 修改 message 类型判断，不使用 nil 合并运算符
                    self.errorMessage = message.isEmpty ? "服务器错误 (代码: \(code))" : message
                case .unauthorized:
                    self.errorMessage = "登录已过期，请重新登录"
                default:
                    self.errorMessage = apiError.userFriendlyMessage
                }
            } catch {
                print("❌ 未知错误: \(error)")
                print("❌ 错误类型: \(Swift.type(of: error))")
                print("❌ 错误描述: \(error.localizedDescription)")
                
                errorMessage = "保存失败: \(error.localizedDescription)"
            }
            
            isLoading = false
        }
    }
    
    // 处理拼配组件中的海拔信息
    private func processBlendComponents() -> [BlendComponent] {
        return blendComponents.map { component in
            var mutableComponent = component
            
            // 处理拼配组件中的海拔范围值只填写了一个的情况
            if mutableComponent.altitudeType == "RANGE" {
                if (mutableComponent.altitudeMin != nil && mutableComponent.altitudeMax == nil) || 
                   (mutableComponent.altitudeMin != nil && mutableComponent.altitudeMax == 0) {
                    // 只有最小值或最高值为0，转为单一值
                    mutableComponent.altitudeType = "SINGLE"
                    mutableComponent.altitudeSingle = mutableComponent.altitudeMin
                    mutableComponent.altitudeMin = nil
                    mutableComponent.altitudeMax = nil
                } else if (mutableComponent.altitudeMin == nil && mutableComponent.altitudeMax != nil) || 
                          (mutableComponent.altitudeMin == 0 && mutableComponent.altitudeMax != nil && mutableComponent.altitudeMax! > 0) {
                    // 只有最大值或最低值为0，转为单一值
                    mutableComponent.altitudeType = "SINGLE"
                    mutableComponent.altitudeSingle = mutableComponent.altitudeMax
                    mutableComponent.altitudeMin = nil
                    mutableComponent.altitudeMax = nil
                }
            }
            
            return mutableComponent.toBlendComponent()
        }
    }
    
    // 基本信息区域
    private func generalInfoSection() -> some View {
        VStack(spacing: 20) {
            // 咖啡豆类型选择
            VStack(alignment: .leading, spacing: 10) {
                Text("咖啡豆类型")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                HStack(spacing: 12) {
                    typeRadioButton(title: "单品", value: "SINGLE")
                    typeRadioButton(title: "拼配", value: "BLEND")
                    typeRadioButton(title: "跳过", value: "SKIP")
                    Spacer()
                }
                .padding(.horizontal, 4)
            }
            .padding(.horizontal, 16)
            
            // 豆商
            formFieldSimple(title: "豆商", binding: $roaster, placeholder: "请输入豆商/烘焙商")
            
            // 咖啡豆名称
            formFieldSimple(title: "咖啡豆名称", binding: $name)
            
            // 低因咖啡开关
            toggleFieldSimple(title: "是否低因咖啡？", isOn: $isDecaf)

            // 风味标签
            VStack(alignment: .leading, spacing: 10) {
                HStack(alignment: .center, spacing: 4) {
                    Text("风味标签")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(选填，请填写咖啡豆包装袋上的风味描述)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                    
                    Spacer()
                }
                
                FlavorTagsInput(selectedTags: $flavorTags)
                    .hideKeyboardToolbar() // 使用新的修饰器隐藏工具栏
            }
            .id("flavorTagsSection")
            .padding(.horizontal, 16)
            .onChange(of: focusedField) { newValue in
                if newValue?.contains("flavorTags") == true {
                    withAnimation {
                        // 添加延迟以确保键盘已经显示
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            scrollProxy?.scrollTo("flavorTagsSection", anchor: .top)
                        }
                    }
                }
            }
            
            // 备注文本编辑器
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Text("备注")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                    
                    Spacer()
                    
                    // 字数统计
                    Text("\(notes.count)/500")
                        .font(.caption)
                        .foregroundColor(notes.count >= 500 ? .error : .primaryText)
                }
                
                ZStack(alignment: .topLeading) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                        .submitLabel(.done)
                        .focused($focusedField, equals: "备注_field")
                        .scrollContentBackground(.hidden)
                        .background(Color.clear)
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.secondaryBg)
                        )
                        .onChange(of: notes) { newValue in
                            if newValue.count > 500 {
                                notes = String(newValue.prefix(500))
                            }
                        }
                    
                    if notes.isEmpty {
                        Text("请输入备注信息")
                            .foregroundColor(.gray)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 12)
                            .background(Color.clear)
                            .allowsHitTesting(false)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 简化版表单字段
    private func formFieldSimple(title: String, binding: Binding<String>, placeholder: String? = nil, optional: Bool = false) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                if optional {
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
            }
            
            TextField(placeholder ?? "请输入\(title)", text: binding)
                .focused($focusedField, equals: "\(title)_field")
                .submitLabel(.done)
                .font(.system(size: 17))
                .padding(.vertical, 14)
                .padding(.horizontal, 16)
                .background(Color.secondaryBg)
                .cornerRadius(12)
        }
        .padding(.horizontal, 16)
    }
    
    // 简化版开关字段
    private func toggleFieldSimple(title: String, isOn: Binding<Bool>) -> some View {
        HStack {
            Text(title)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.primaryText)
            
            Spacer()
            
            Toggle("", isOn: isOn)
                .labelsHidden()
                .tint(.functionText)
        }
        .padding(.horizontal, 16)
    }
    
    // 类型单选按钮组件
    private func typeRadioButton(title: String, value: String) -> some View {
        Button(action: {
            type = value
            
            // 根据类型调整页面显示
            if selectedTab == 2 && value == "SKIP" {
                // 如果当前在详细属性tab并选择了"跳过"，则自动跳转到基本信息tab
                selectedTab = 0
            }
        }) {
            HStack(spacing: 8) {
                ZStack {
                    Circle()
                        .stroke(type == value ? Color.functionText : Color.gray, lineWidth: 1.5)
                        .frame(width: 20, height: 20)
                    
                    if type == value {
                        Circle()
                            .fill(Color.functionText)
                            .frame(width: 12, height: 12)
                    }
                }
                
                Text(title)
                    .font(.system(size: 15))
                    .foregroundColor(type == value ? .primary : .primaryText)
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 14)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(type == value ? Color.functionText.opacity(0.1) : Color.clear)
            )
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // 包装信息区域
    private func productInfoSection() -> some View {
        VStack(spacing: 20) {
            // 包装规格和库存余量
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("包装规格 (g)")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    numberFieldSimple(value: $bagWeight, placeholder: "0.00", fieldIdentifier: "包装规格 (g)")
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("库存余量 (g)")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    numberFieldSimple(value: $bagRemain, placeholder: "0.00", fieldIdentifier: "库存余量 (g)")
                }
            }
            .padding(.horizontal, 16)
            
            // 添加说明文字
            Text("📌 此处主要用于手动调整余量。如果您新购买了一包同款咖啡豆，建议在咖啡豆列表卡片长按菜单点击「回购」，这样您的消费记录将更加准确。")
                .font(.caption)
                .foregroundColor(.noteText)
                .padding(.horizontal, 16)
                .padding(.bottom, 8)
            
            // 购买时间
            VStack(alignment: .leading, spacing: 8) {
                Text("购买时间")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                // 使用系统的直接可见DatePicker但自定义样式
                DatePicker(
                    "",
                    selection: $createdAt,
                    displayedComponents: [.date, .hourAndMinute]
                )
                .datePickerStyle(CompactDatePickerStyle())
                .labelsHidden()
                .accentColor(.functionText)
                .cornerRadius(12)
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 16)
            
            // 购买价格
            VStack(alignment: .leading, spacing: 8) {
                Text("购买价格 (元)")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                numberFieldSimple(value: $purchasePrice, placeholder: "0.00", fieldIdentifier: "购买价格 (元)")
            }
            .padding(.horizontal, 16)
            
            // 烘焙日期开关和相关字段
            VStack(alignment: .leading, spacing: 10) {
                // 是否知道烘焙日期开关
                toggleFieldSimple(title: "是否知道烘焙日期？", isOn: $roastDateEnabled)
                
                if roastDateEnabled {
                    // 烘焙日期选择器
                    VStack(alignment: .leading, spacing: 8) {
                        Text("烘焙日期")
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(.primaryText)
                        
                        DatePicker("", selection: $roastDate, displayedComponents: .date)
                            .datePickerStyle(CompactDatePickerStyle())
                            .labelsHidden()
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .padding(.horizontal, 16)
                    
                    // 养豆期设置
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("养豆期")
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.primaryText)
                            
                            Text("(选填，最多60天)")
                                .font(.caption)
                                .foregroundColor(.noteText)
                        }
                        
                        // 单一值/范围值切换
                        HStack(spacing: 8) {
                            Text("按单一值")
                                .font(.system(size: 14))
                            
                            Toggle("", isOn: Binding(
                                get: { restPeriodType == "RANGE" },
                                set: { restPeriodType = $0 ? "RANGE" : "SINGLE" }
                            ))
                            .labelsHidden()
                            .toggleStyle(SwitchToggleStyle(tint: .functionText))
                            
                            Text("按范围值")
                                .font(.system(size: 14))
                        }
                        .padding(.vertical, 6)
                        
                        // 养豆期输入
                        if restPeriodType == "SINGLE" {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("养豆天数")
                                    .font(.system(size: 16, weight: .regular))
                                    .foregroundColor(.primaryText)
                                
                                numberFieldSimpleForInt(value: $restPeriodMin, placeholder: "0", fieldIdentifier: "养豆天数")
                            }
                        } else {
                            HStack(spacing: 16) {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("最短养豆期")
                                        .font(.system(size: 16, weight: .regular))
                                        .foregroundColor(.primaryText)
                                    
                                    numberFieldSimpleForInt(value: $restPeriodMin, placeholder: "0", fieldIdentifier: "最短养豆期")
                                }
                                
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("最长养豆期")
                                        .font(.system(size: 16, weight: .regular))
                                        .foregroundColor(.primaryText)
                                    
                                    numberFieldSimpleForInt(value: $restPeriodMax, placeholder: "0", fieldIdentifier: "最长养豆期")
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                }
            }
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 简化版数字输入字段
    private func numberFieldSimple<T: Numeric & LosslessStringConvertible>(
        value: Binding<T?>,
        placeholder: String = "0",
        fieldIdentifier: String
    ) -> some View {
        let stringValue = value.wrappedValue != nil ? String(describing: value.wrappedValue!) : ""
        
        return ZStack(alignment: .leading) {
            if stringValue.isEmpty {
                Text(placeholder)
                    .foregroundColor(.gray)
                    .padding(.leading, 16)
            }
            
            TextField("", text: Binding(
                get: { stringValue },
                set: { newValue in
                    if let parsed = T(newValue) {
                        value.wrappedValue = parsed
                    } else if newValue.isEmpty {
                        value.wrappedValue = nil
                    }
                }
            ))
            .keyboardType(.decimalPad)
            .disableAutocorrection(true)
            .autocapitalization(.none)
            .font(.system(size: 17))
            .padding(.vertical, 14)
            .padding(.horizontal, 16)
            .focused($focusedField, equals: fieldIdentifier + "_field")
        }
        .background(Color.secondaryBg)
        .cornerRadius(12)
    }
    
    // Int类型专用的numberFieldSimple版本
    private func numberFieldSimpleForInt(
        value: Binding<Int?>,
        placeholder: String = "0",
        fieldIdentifier: String,
        useBlendStyle: Bool = false
    ) -> some View {
        let stringValue = value.wrappedValue != nil ? String(describing: value.wrappedValue!) : ""
        
        return ZStack(alignment: .leading) {
            if stringValue.isEmpty {
                Text(placeholder)
                    .foregroundColor(.gray)
                    .padding(.leading, 16)
            }
            
            TextField("", text: Binding(
                get: { stringValue },
                set: { newValue in
                    if let parsed = Int(newValue) {
                        value.wrappedValue = parsed
                    } else if newValue.isEmpty {
                        value.wrappedValue = nil
                    }
                }
            ))
            .keyboardType(.numberPad)
            .disableAutocorrection(true)
            .autocapitalization(.none)
            .font(.system(size: 17))
            .padding(.vertical, 14)
            .padding(.horizontal, 16)
            .focused($focusedField, equals: fieldIdentifier + "_field")
        }
        .background(useBlendStyle ? Color.focusBg : Color.secondaryBg)
        .cornerRadius(12)
    }
    
    // 详细属性区域
    private func detailsSection() -> some View {
        VStack(spacing: 12) {
            if type == "BLEND" {
                // 拼配豆组件
                blendComponentsSection()
            } else {
                // 单品咖啡详细信息
                singleBeanDetailsSection()
            }
        }
    }

    // 单品咖啡详细信息部分
    private func singleBeanDetailsSection() -> some View {
        VStack(spacing: 20) {
            // 烘焙程度滑块
            VStack(alignment: .leading, spacing: 10) {
                Text("烘焙程度")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)
                
                VStack(spacing: 12) {
                    sliderWithTap(value: Binding(
                        get: { Double(roastLevel) },
                        set: { roastLevel = Int($0) }
                    ), in: 1...7, step: 1)
                    
                    HStack {
                        Text("极浅")
                            .font(.caption)
                        Spacer()
                        Text("浅")
                            .font(.caption)
                        Spacer()
                        Text("浅中")
                            .font(.caption)
                        Spacer()
                        Text("中")
                            .font(.caption)
                        Spacer()
                        Text("中深")
                            .font(.caption)
                        Spacer()
                        Text("深")
                            .font(.caption)
                        Spacer()
                        Text("极深")
                            .font(.caption)
                    }
                }
                .padding(.vertical, 4)
            }
            .padding(.horizontal, 16)
            
            // 产地、产区
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 4) {
                        Text("产地")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                        
                        Text("(选填)")
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                    TextField("请输入原产国", text: $origin)
                        .focused($focusedField, equals: "产地_field")
                        .submitLabel(.done)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 4) {
                        Text("产区")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                        
                        Text("(选填)")
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                    TextField("请输入产区", text: $region)
                        .focused($focusedField, equals: "产区_field")
                        .submitLabel(.done)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                }
            }
            .padding(.horizontal, 16)
            
            // 庄园、品种
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 4) {
                        Text("庄园/处理站")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                        
                        Text("(选填)")
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                    TextField("请输入庄园", text: $finca)
                        .focused($focusedField, equals: "庄园_field")
                        .submitLabel(.done)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 4) {
                        Text("品种")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                        
                        Text("(选填)")
                            .font(.caption)
                            .foregroundColor(.noteText)
                    }
                    TextField("请输入咖啡豆种", text: $variety)
                        .focused($focusedField, equals: "品种_field")
                        .submitLabel(.done)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                }
            }
            .padding(.horizontal, 16)
            
            // 处理法
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("处理法")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
                TextField("请输入处理法", text: $process)
                    .focused($focusedField, equals: "处理法_field")
                    .submitLabel(.done)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
            }
            .padding(.horizontal, 16)
            
            // 海拔信息
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Text("海拔信息")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
                // 单一值/范围值切换
                HStack(spacing: 8) {
                    Text("按单一值")
                        .font(.system(size: 14))
                    
                    Toggle("", isOn: Binding(
                        get: { altitudeType == "RANGE" },
                        set: { altitudeType = $0 ? "RANGE" : "SINGLE" }
                    ))
                    .labelsHidden()
                    .toggleStyle(SwitchToggleStyle(tint: .functionText))
                    
                    Text("按范围值")
                        .font(.system(size: 14))
                }
                .padding(.vertical, 6)
                
                // 海拔输入
                if altitudeType == "SINGLE" {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("种植海拔(米)")
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                        
                        numberFieldSimpleForInt(value: $altitudeSingle, placeholder: "0", fieldIdentifier: "种植海拔(米)", useBlendStyle: false)
                    }
                } else {
                    HStack(spacing: 16) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("最低海拔(米)")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                            
                            numberFieldSimpleForInt(value: $altitudeMin, placeholder: "0", fieldIdentifier: "最低海拔(米)", useBlendStyle: false)
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("最高海拔(米)")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                            
                            numberFieldSimpleForInt(value: $altitudeMax, placeholder: "0", fieldIdentifier: "最高海拔(米)", useBlendStyle: false)
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 拼配豆组件部分
    private func blendComponentsSection() -> some View {
        VStack(spacing: 20) {
            HStack {
                Text("拼配豆信息")
                    .font(.headline)
                
                Spacer()
                
                // 如果拼配组件不超过5个，显示添加按钮
                if blendComponents.count < 5 {
                    Button(action: {
                        withAnimation {
                            // 折叠所有现有组件
                            for i in 0..<blendComponents.count {
                                blendComponents[i].isExpanded = false
                            }
                            
                            // 创建新组件并直接设置为展开状态
                            var newComponent = MutableBlendComponent(from: BlendComponent.defaultComponent())
                            newComponent.isExpanded = true
                            blendComponents.append(newComponent)
                            recalculateBlendRatios()
                        }
                    }) {
                        HStack {
                            Image(systemName: "plus.circle")
                            Text("追加拼配 (\(blendComponents.count)/5)")
                        }
                        .foregroundColor(.functionText)
                    }
                }
            }
            .padding(.horizontal, 16)
            
            // 逐个显示拼配组件
            ForEach(0..<blendComponents.count, id: \.self) { index in
                blendComponentView(index: index)
            }
            
            // 如果有多个组件，显示总比例
            if blendComponents.count > 1 {
                HStack {
                    Spacer()
                    
                    let ratio = calculateTotalRatio()
                    let isInteger = ratio.truncatingRemainder(dividingBy: 1) == 0
                    Text("总比例：\(isInteger ? String(format: "%.0f", ratio) : String(format: "%.2f", ratio))%")
                        .foregroundColor(abs(ratio - 100) > 0.02 ? .error : .primaryText)
                        .font(.subheadline)
                    
                    if abs(ratio - 100) > 0.02 {
                        Text("(必须等于100%，允许0.01的误差)")
                            .font(.caption)
                            .foregroundColor(.error)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .cornerRadius(8)
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
            }
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 单个拼配组件视图
    private func blendComponentView(index: Int) -> some View {
        VStack(alignment: .leading, spacing: 0) {
            // 组件标题栏
            Button(action: {
                withAnimation {
                    blendComponents[index].isExpanded.toggle()
                }
            }) {
                HStack {
                    Text("拼配豆 #\(index + 1)")
                        .font(.headline)
                    
                    Spacer()
                    
                    // 如果组件数大于1，显示删除按钮
                    if blendComponents.count > 1 {
                        Button(action: {
                            withAnimation {
                                blendComponents.remove(at: index)
                                recalculateBlendRatios()
                            }
                        }) {
                            Image(systemName: "trash")
                                .foregroundColor(.error)
                                .padding(.trailing, 8)
                        }
                        .buttonStyle(.plain)
                    }
                    
                    // 展开/折叠按钮
                    Image(systemName: blendComponents[index].isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.primaryText)
                }
                .padding(.vertical, 12)
                .padding(.horizontal, 12)
                .contentShape(Rectangle())
            }
            .buttonStyle(PlainButtonStyle())
            
            // 可折叠的内容区域
            if blendComponents[index].isExpanded {
                Divider()
                
                VStack(spacing: 16) {
                    // 如果有多个组件，显示比例设置
                    if blendComponents.count > 1 {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("拼配比例(%)")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                            Text("(如果不清楚可以不修改，保持均摊比例)")
                                .font(.caption)
                                .foregroundColor(.noteText)
                            TextField("0", value: $blendComponents[index].blendRatio, formatter: NumberFormatter())
                                .keyboardType(.decimalPad)
                                .focused($focusedField, equals: "拼配比例_\(index)_field")
                                .padding(12)
                                .background(Color.focusBg)
                                .cornerRadius(8)
                                .onChange(of: blendComponents[index].blendRatio) { _ in
                                    // 当用户手动修改某个组件的比例时，更新总比例显示
                                    // 不自动调整其他比例，让用户自己处理
                                    _ = calculateTotalRatio()
                                }
                        }
                        .padding(.horizontal, 16)
                        .padding(.top, 16)
                    }
                    
                    // 烘焙程度滑块
                    VStack(alignment: .leading, spacing: 10) {
                        Text("烘焙程度")
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(.primaryText)
                        
                        VStack(spacing: 12) {
                            sliderWithTap(value: Binding(
                                get: { Double(blendComponents[index].roastLevel) },
                                set: { blendComponents[index].roastLevel = Int($0) }
                            ), in: 1...7, step: 1)
                            
                            HStack {
                                Text("极浅")
                                    .font(.caption)
                                Spacer()
                                Text("浅")
                                    .font(.caption)
                                Spacer()
                                Text("浅中")
                                    .font(.caption)
                                Spacer()
                                Text("中")
                                    .font(.caption)
                                Spacer()
                                Text("中深")
                                    .font(.caption)
                                Spacer()
                                Text("深")
                                    .font(.caption)
                                Spacer()
                                Text("极深")
                                    .font(.caption)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 16)
                    
                    // 其他详细属性
                    VStack(spacing: 16) {
                        HStack(spacing: 16) {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(spacing: 4) {
                                    Text("产地")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    Text("(选填)")
                                        .font(.caption)
                                        .foregroundColor(.noteText)
                                }
                                TextField("请输入原产国", text: Binding(
                                    get: { blendComponents[index].origin ?? "" },
                                    set: { blendComponents[index].origin = $0 }
                                ))
                                .focused($focusedField, equals: "产地_\(index)_field")
                                .submitLabel(.done)
                                .padding(.vertical, 12)
                                .padding(.horizontal, 12)
                                .background(Color.focusBg)
                                .cornerRadius(8)
                            }
                            
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(spacing: 4) {
                                    Text("产区")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    Text("(选填)")
                                        .font(.caption)
                                        .foregroundColor(.noteText)
                                }
                                
                                TextField("请输入产区", text: Binding(
                                    get: { blendComponents[index].region ?? "" },
                                    set: { blendComponents[index].region = $0 }
                                ))
                                .focused($focusedField, equals: "产区_\(index)_field")
                                .submitLabel(.done)
                                .padding(.vertical, 12)
                                .padding(.horizontal, 12)
                                .background(Color.focusBg)
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal, 16)
                        
                        HStack(spacing: 16) {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(spacing: 4) {
                                    Text("庄园/处理站")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    Text("(选填)")
                                        .font(.caption)
                                        .foregroundColor(.noteText)
                                }
                                
                                TextField("请输入庄园", text: Binding(
                                    get: { blendComponents[index].finca ?? "" },
                                    set: { blendComponents[index].finca = $0 }
                                ))
                                .focused($focusedField, equals: "庄园_\(index)_field")
                                .submitLabel(.done)
                                .padding(.vertical, 12)
                                .padding(.horizontal, 12)
                                .background(Color.focusBg)
                                .cornerRadius(8)
                            }
                            
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(spacing: 4) {
                                    Text("品种")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    Text("(选填)")
                                        .font(.caption)
                                        .foregroundColor(.noteText)
                                }
                                TextField("请输入咖啡豆种", text: Binding(
                                    get: { blendComponents[index].variety ?? "" },
                                    set: { blendComponents[index].variety = $0 }
                                ))
                                .focused($focusedField, equals: "品种_\(index)_field")
                                .submitLabel(.done)
                                .padding(.vertical, 12)
                                .padding(.horizontal, 12)
                                .background(Color.focusBg)
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal, 16)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            HStack(spacing: 4) {
                                Text("处理法")
                                    .font(.subheadline)
                                    .foregroundColor(.primaryText)
                                
                                Text("(选填)")
                                    .font(.caption)
                                    .foregroundColor(.noteText)
                            }
                            TextField("请输入处理法", text: Binding(
                                get: { blendComponents[index].process ?? "" },
                                set: { blendComponents[index].process = $0 }
                            ))
                            .focused($focusedField, equals: "处理法_\(index)_field")
                            .submitLabel(.done)
                            .padding(.vertical, 12)
                            .padding(.horizontal, 12)
                            .background(Color.focusBg)
                            .cornerRadius(8)
                        }
                        .padding(.horizontal, 16)
                    }
                    
                    // 海拔信息
                    VStack(alignment: .leading, spacing: 10) {
                        HStack(spacing: 4) {
                            Text("海拔信息")
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                            
                            Text("(选填)")
                                .font(.caption)
                                .foregroundColor(.noteText)
                        }
                        // 单一值/范围值切换
                        HStack(spacing: 8) {
                            Text("按单一值")
                                .font(.system(size: 14))
                            
                            Toggle("", isOn: Binding(
                                get: { 
                                    // 添加安全检查，确保索引有效
                                    guard index < blendComponents.count else { return false }
                                    return blendComponents[index].altitudeType == "RANGE" 
                                },
                                set: { 
                                    // 添加安全检查，确保索引有效
                                    guard index < blendComponents.count else { return }
                                    blendComponents[index].altitudeType = $0 ? "RANGE" : "SINGLE"
                                }
                            ))
                            .labelsHidden()
                            .toggleStyle(SwitchToggleStyle(tint: .functionText))
                            
                            Text("按范围值")
                                .font(.system(size: 14))
                        }
                        .padding(.vertical, 6)
                        
                        // 海拔输入
                        if index < blendComponents.count && blendComponents[index].altitudeType == "SINGLE" {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("种植海拔(米)")
                                    .font(.subheadline)
                                    .foregroundColor(.primaryText)
                                
                                numberFieldSimpleForInt(
                                    value: Binding(
                                        get: { 
                                            guard index < blendComponents.count else { return nil }
                                            return blendComponents[index].altitudeSingle 
                                        },
                                        set: { 
                                            guard index < blendComponents.count else { return }
                                            blendComponents[index].altitudeSingle = $0 
                                        }
                                    ),
                                    placeholder: "0",
                                    fieldIdentifier: "种植海拔(米)_\(index)",
                                    useBlendStyle: true
                                )
                            }
                        } else if index < blendComponents.count {
                            HStack(spacing: 16) {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("最低海拔(米)")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    numberFieldSimpleForInt(
                                        value: Binding(
                                            get: { 
                                                guard index < blendComponents.count else { return nil }
                                                return blendComponents[index].altitudeMin 
                                            },
                                            set: { 
                                                guard index < blendComponents.count else { return }
                                                blendComponents[index].altitudeMin = $0 
                                            }
                                        ),
                                        placeholder: "0",
                                        fieldIdentifier: "最低海拔(米)_\(index)",
                                        useBlendStyle: true
                                    )
                                }
                                
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("最高海拔(米)")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText)
                                    
                                    numberFieldSimpleForInt(
                                        value: Binding(
                                            get: { 
                                                guard index < blendComponents.count else { return nil }
                                                return blendComponents[index].altitudeMax 
                                            },
                                            set: { 
                                                guard index < blendComponents.count else { return }
                                                blendComponents[index].altitudeMax = $0 
                                            }
                                        ),
                                        placeholder: "0",
                                        fieldIdentifier: "最高海拔(米)_\(index)",
                                        useBlendStyle: true
                                    )
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 16)
                }
            }
        }
        .background(Color.secondaryBg)
        .cornerRadius(8)
        .padding(.horizontal, 16)
    }
    
    // 自定义可点击定位的滑块
    private func sliderWithTap(value: Binding<Double>, in range: ClosedRange<Double>, step: Double = 1.0) -> some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 背景轨道
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 4)
                    .cornerRadius(2)
                
                // 已填充部分
                Rectangle()
                    .fill(Color.functionText)
                    .frame(width: CGFloat((value.wrappedValue - range.lowerBound) / (range.upperBound - range.lowerBound)) * geometry.size.width, height: 4)
                    .cornerRadius(2)
                
                // 滑块按钮
                Circle()
                    .fill(Color.functionText)
                    .frame(width: 24, height: 24)
                    .shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)
                    .offset(x: CGFloat((value.wrappedValue - range.lowerBound) / (range.upperBound - range.lowerBound)) * (geometry.size.width - 24))
                
                // 透明覆盖层用于点击
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle())
                    .frame(height: 40)
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { gesture in
                                let ratio = min(max(0, gesture.location.x / geometry.size.width), 1)
                                let newValue = range.lowerBound + (range.upperBound - range.lowerBound) * Double(ratio)
                                // 根据step约束值
                                let steppedValue = round(newValue / step) * step
                                value.wrappedValue = min(max(range.lowerBound, steppedValue), range.upperBound)
                            }
                    )
            }
        }
        .frame(height: 40)
    }
    
    // 重新计算拼配比例
    private func recalculateBlendRatios() {
        // 如果只有一个组件，设置为100%
        if blendComponents.count == 1 {
            blendComponents[0].blendRatio = 100
            return
        }
        
        // 均分比例
        let equalRatio = 100.0 / Double(blendComponents.count)
        for i in 0..<blendComponents.count {
            blendComponents[i].blendRatio = equalRatio
        }
    }
}

#if DEBUG
struct EditBeanView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            EditBeanView(bean: CoffeeBean(
                id: 1, 
                name: "测试咖啡豆", 
                type: "SINGLE", 
                typeDisplay: "单品",
                roaster: "测试豆商", 
                roastLevel: 4,
                roastLevelDisplay: "中烘",
                origin: "埃塞俄比亚", 
                region: "耶加雪菲", 
                finca: "测试庄园", 
                variety: "日晒瑰夏", 
                process: "水洗", 
                barcode: "12345678", 
                notes: "测试备注", 
                bagWeight: 200.0, 
                bagRemain: 150.0, 
                purchasePrice: 99.0, 
                roastDate: Date(),
                createdAt: Date(), 
                deletedAt: nil,
                isFavorite: true, 
                isArchived: false,
                isDeleted: false,
                isDecaf: false, 
                altitudeType: "SINGLE", 
                altitudeSingle: 1800, 
                altitudeMin: nil, 
                altitudeMax: nil, 
                restPeriodMin: 14, 
                restPeriodMax: nil,
                restPeriodProgress: nil,
                stockStatus: "IN_STOCK",
                avgRating: nil,
                tasteNotes: ["花香", "巧克力", "果酸"],
                blendComponents: nil,
                occurrences: nil,
                usageCount: nil,
                lastUsed: nil,
                daysSinceLastUse: nil,
                mostUsedEquipment: nil,
                remainingUses: nil,
                occurrencesCount: nil,
                avgRepurchaseInterval: nil,
                dimensionsAvg: nil,
                tastingCount: nil,
                uniqueFlavorTags: nil,
                flavorAccuracy: nil
            ))
        }
    }
}
#endif 
