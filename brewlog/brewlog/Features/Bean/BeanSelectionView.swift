import SwiftUI

struct BeanSelectionView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = BrewLogViewModel()
    @Binding var selectedBean: CoffeeBean?
    @State private var searchText = ""
    
    var filteredBeans: [CoffeeBean] {
        if searchText.isEmpty {
            return viewModel.coffeeBeans
        } else {
            return viewModel.coffeeBeans.filter { bean in
                bean.name.localizedCaseInsensitiveContains(searchText) ||
                bean.roaster.localizedCaseInsensitiveContains(searchText) ||
                (bean.origin ?? "").localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var body: some View {
        List(filteredBeans) { bean in
            VStack(alignment: .leading, spacing: 4) {
                Text(bean.name)
                    .font(.headline)
                Text(bean.roaster)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .contentShape(Rectangle())
            .onTapGesture {
                selectedBean = bean
                dismiss()
            }
        }
        .navigationTitle("选择咖啡豆")
        .searchable(text: $searchText, prompt: "搜索咖啡豆")
        .task {
            await viewModel.fetchCoffeeBeans()
        }
    }
}

#if DEBUG
struct BeanSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            BeanSelectionView(selectedBean: .constant(nil))
        }
    }
}
#endif 