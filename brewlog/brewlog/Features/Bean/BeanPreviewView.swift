import SwiftUI

// 咖啡豆详情预览视图 - 最简化实现
struct BeanQuickPreviewView: View {
    // 接收 viewModel 但不使用 ObservedObject
    let viewModel: BeanListViewModel
    
    // 传入的 bean ID
    let beanId: Int
    
    // 初始 bean 数据，仅用于首次渲染
    let initialBean: CoffeeBean
    
    // 本地state绑定
    @State private var favoriteState: Bool
    // 添加刷新触发器
    @State private var forceRefresh: UUID = UUID()
    // 添加状态更新锁，避免重入
    @State private var isUpdating: Bool = false
    
    // 初始化方法，直接使用传入的 bean
    init(viewModel: BeanListViewModel, bean: CoffeeBean) {
        self.viewModel = viewModel
        self.beanId = bean.id
        self.initialBean = bean
        self._favoriteState = State(initialValue: bean.isFavorite)
    }
    
    // 计算属性：检查是否有任何"更多信息行"数据需要显示
    private func hasAdditionalInfo(_ bean: CoffeeBean) -> Bool {
        // 如果任何一个条件满足，则返回true
        return (bean.typeDisplay != "跳过") ||
               bean.isDecaf ||
               !(bean.notes?.isEmpty ?? true) ||
               (bean.purchasePrice != nil && bean.purchasePrice! > 0) ||
               (bean.roastLevelEnum != nil && bean.typeDisplay != "跳过") ||
               (bean.tasteNotes != nil && !bean.tasteNotes!.isEmpty)
    }
    
    var body: some View {
        // 尝试获取最新的 bean 数据，如果找不到则使用初始值
        let bean = viewModel.coffeeBeans.first(where: { $0.id == beanId }) ?? initialBean
        
        VStack(alignment: .leading, spacing: 12) {
            // 豆商
            HStack {
                Text(bean.roaster)
                    .font(.system(size: 16, weight: .light, design: .default))
                    .foregroundColor(.primaryText)
                    .lineLimit(1)
                
                Spacer()
                
                // 显示本地状态变量
                Text(favoriteState ? "★" : "☆")
                    .font(.caption)
                    .opacity(0) // 在UI中不可见，仅作为依赖触发更新
                
                // 库存信息 - 非归档状态才显示
                if !bean.isArchived, let bagRemain = bean.bagRemain, let bagWeight = bean.bagWeight, bagWeight > 0 {
                    Text("库存：\(NumberFormatters.formatWithPrecision(bagRemain, precision: 2))g/\(NumberFormatters.formatWithPrecision(bagWeight, precision: 2))g")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // 咖啡豆名称
            HStack {
                Text(bean.name)
                    .font(.headline)
                    .lineLimit(1)
                
                Spacer()
                
                // 养豆信息 - 非归档状态才显示
                if !bean.isArchived, let daysSinceRoast = bean.daysSinceRoast, daysSinceRoast >= 0 {
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("已养豆：\(daysSinceRoast)天")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        if let restPeriodMin = bean.restPeriodMin, let restPeriodMax = bean.restPeriodMax {
                            // 有最小和最大养豆期
                            if restPeriodMax == 0 {
                                // 当最大养豆期为0时，只显示最小养豆期
                                Text("养豆期：\(restPeriodMin)天")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            } else {
                                // 最大养豆期不为0时，显示范围
                                Text("养豆期：\(restPeriodMin)-\(restPeriodMax)天")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        } else if let restPeriodMin = bean.restPeriodMin {
                            // 只有最小养豆期
                            Text("养豆期：\(restPeriodMin)天")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            
            // 状态行
            HStack(spacing: 12) {
                // 如果在最佳赏味期，同时显示最佳赏味期和使用中两个状态
                if bean.isInBestFlavorPeriod && bean.baseStatus == .inUse {
                    HStack(spacing: 8) {
                        BeanStatusLabel(status: .inUse)
                        BeanStatusLabel(status: .bestFlavor)
                    }
                } else {
                    BeanStatusLabel(status: bean.status)
                }
                
                // 首选标记 - 使用本地状态变量
                if favoriteState {
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption)
                        
                        Text("首选")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if bean.isArchived {
                    Label("已归档", systemImage: "archivebox")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 只有当有额外信息需要显示时才添加分隔线
            if hasAdditionalInfo(bean) {
                Divider()
                
                // 更多信息行（一行一个信息）
                // 类型
                if bean.typeDisplay != "跳过" {
                    HStack(spacing: 4) {
                        // 根据typeDisplay类型显示不同图标
                        Image(bean.typeDisplay == "单品" ? "single.symbols" : "blend.symbols")
                            .font(.callout)
                            .foregroundColor(.linkText)
                            
                        Text(bean.typeDisplay)
                            .font(.callout)
                            .foregroundColor(.primaryText)
                    }
                }
                // 是否低因咖啡
                if bean.isDecaf {
                    HStack(spacing: 4) {
                        Image("decaf.symbols")
                            .font(.callout)
                            .foregroundColor(.linkText)
                            
                        Text("低因咖啡")
                            .font(.callout)
                            .foregroundColor(.primaryText)
                    }
                }
                // 备注
                if !(bean.notes?.isEmpty ?? true) {
                    HStack(spacing: 4) {
                        Image("note.symbols")
                            .font(.callout)
                            .foregroundColor(.linkText)
                            
                        Text(bean.notes ?? "")
                            .font(.callout)
                            .foregroundColor(.primaryText)
                            .lineLimit(2)
                            .truncationMode(.tail)
                    }
                }
                // 价格
                if let price = bean.purchasePrice, price > 0 {
                    HStack(spacing: 4) {
                        Image("price.symbols")
                            .font(.callout)
                            .foregroundColor(.linkText)
                            
                        Text(NumberFormatters.formatWithPrecision(price, precision: 2)+"元")
                            .font(.callout)
                            .foregroundColor(.primaryText)
                    }
                }
                // 烘焙度
                if let roastLevel = bean.roastLevelEnum, bean.typeDisplay != "跳过" {
                    HStack(spacing: 4) {
                        Image("roastLevel.symbols")
                            .font(.callout)
                            .foregroundColor(.linkText)
                        
                        Text(roastLevel.rawValue)
                            .font(.callout)
                            .foregroundColor(.primaryText)
                    }
                }
                // 风味标签
                if let tasteNotes = bean.tasteNotes, !tasteNotes.isEmpty {
                    HStack(spacing: 4) {
                        Image("flavor.symbols")
                            .font(.body)
                            .foregroundColor(.linkText)
                        
                        Text(tasteNotes.joined(separator: "、"))
                            .font(.callout)
                            .foregroundColor(.primaryText)
                    }
                }
            }
        }
        .padding()
        .frame(width: 320)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        // 强制使用ID刷新整个视图
        .id("bean-preview-\(beanId)-\(favoriteState)-\(forceRefresh)")
        // 在视图显示时立即检查最新状态
        .onAppear {
            #if DEBUG
            print("🟢 BeanQuickPreviewView 出现: ID=\(beanId), 当前状态=\(favoriteState)")
            #endif
            
            // 立即获取最新状态
            updateFavoriteState()
        }
        // 使用 SwiftUI 原生方式接收通知
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("BeanStatusUpdated"))) { notification in
            handleStatusNotification(notification)
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("GlobalBeanUpdated"))) { _ in
            handleGlobalNotification()
        }
    }
    
    // 处理特定状态通知
    private func handleStatusNotification(_ notification: Notification) {
        // 如果正在更新，忽略此通知以防止重入
        guard !isUpdating,
              let data = notification.userInfo as? [String: Any],
              let updatedId = data["id"] as? Int,
              let newFavoriteState = data["isFavorite"] as? Bool,
              updatedId == beanId else {
            return
        }
        
        #if DEBUG
        print("🔔 BeanQuickPreviewView 收到状态通知: ID=\(beanId), 新状态=\(newFavoriteState), 旧状态=\(favoriteState)")
        #endif
        
        // 防止重入
        isUpdating = true
        
        // 检查状态是否有变化
        if favoriteState != newFavoriteState {
            #if DEBUG
            print("✨ BeanQuickPreviewView 状态将更新: ID=\(beanId), 新状态=\(newFavoriteState)")
            #endif
            
            DispatchQueue.main.async {
                favoriteState = newFavoriteState
                // 强制刷新
                forceRefresh = UUID()
                
                // 重置锁
                isUpdating = false
            }
        } else {
            isUpdating = false
        }
    }
    
    // 处理全局通知
    private func handleGlobalNotification() {
        // 如果正在更新，忽略此通知以防止重入
        guard !isUpdating else { return }
        
        #if DEBUG
        print("🌎 BeanQuickPreviewView 收到全局通知: ID=\(beanId)")
        #endif
        
        // 标记为正在更新
        isUpdating = true
        
        // 使用相同的 ID 直接调用更新方法
        DispatchQueue.main.async {
            updateFavoriteState()
            
            // 重置锁
            isUpdating = false
        }
    }
    
    // 更新首选状态
    private func updateFavoriteState() {
        // 如果正在更新，忽略此请求以防止重入
        guard !isUpdating else { return }
        
        // 标记为正在更新
        isUpdating = true
        
        let currentId = beanId // 捕获当前ID
        
        // 确保在视图上下文中获取最新数据
        if let updatedBean = viewModel.coffeeBeans.first(where: { $0.id == currentId }) {
            if favoriteState != updatedBean.isFavorite {
                #if DEBUG
                print("🔄 BeanQuickPreviewView 状态更新: ID=\(currentId), 旧状态=\(favoriteState), 新状态=\(updatedBean.isFavorite)")
                #endif
                
                // 在主线程更新状态以确保UI响应
                DispatchQueue.main.async {
                    favoriteState = updatedBean.isFavorite
                    // 强制刷新
                    forceRefresh = UUID()
                    
                    // 重置锁
                    isUpdating = false
                }
            } else {
                #if DEBUG
                print("🟰 BeanQuickPreviewView 状态相同: ID=\(currentId), 状态=\(favoriteState)")
                #endif
                isUpdating = false
            }
        } else {
            #if DEBUG
            print("❓ BeanQuickPreviewView 未找到Bean: ID=\(currentId)")
            #endif
            isUpdating = false
        }
    }
}

// 咖啡豆状态标签组件
struct BeanStatusLabel: View {
    let status: BeanStatus
    
    var body: some View {
        HStack(spacing: 4) {
            Image(status == .resting ? "resting.symbols" : 
                  status == .inUse ? "brewing.symbols" : 
                  status == .bestFlavor ? "flavor.symbols" : "outtaStock.symbols")
                .foregroundColor(statusColor)
                .font(.caption)
            
            Text(statusText)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var statusColor: Color {
        switch status {
        case .resting:
            return .gray
        case .inUse:
            return .green
        case .outOfStock:
            return .red
        case .bestFlavor:
            return .blue
        }
    }
    
    private var statusText: String {
        switch status {
        case .resting:
            return "养豆中"
        case .inUse:
            return "使用中"
        case .outOfStock:
            return "已用完"
        case .bestFlavor:
            return "最佳赏味期"
        }
    }
} 