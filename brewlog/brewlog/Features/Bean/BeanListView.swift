import SwiftUI
import UIKit

// 定义RoastLevel枚举
enum RoastLevel: String, Codable, CaseIterable, Identifiable {
    case extremeLight = "极浅烘"
    case light = "浅烘"
    case mediumLight = "浅中烘"
    case medium = "中烘"
    case mediumDark = "中深烘"
    case dark = "深烘"
    case extremeDark = "极深烘"
    
    var id: String { self.rawValue }
    
    static var defaultValue: Self {
        return .medium
    }
}

// 修复RoastLevel类型转换问题
extension CoffeeBean {
    var roastLevelEnum: RoastLevel? {
        // 直接使用非可选类型roastLevel
        let roastLevelValue = self.roastLevel // self.roastLevel是非可选类型Int

        switch roastLevelValue {
        case 1: return .extremeLight
        case 2: return .light
        case 3: return .mediumLight
        case 4: return .medium
        case 5: return .mediumDark
        case 6: return .dark
        case 7: return .extremeDark
        case 0: return nil // 如果值为0（默认值或无效值），返回nil
        default: return .medium
        }
    }
}

// 添加ScrollOffsetPreferenceKey定义
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// 为GeometryReader创建一个辅助修饰符来获取滚动位置
extension View {
    func trackScrollOffset(coordinateSpace: CoordinateSpace = .global) -> some View {
        self.background(
            GeometryReader { geo in
                Color.clear
                    .preference(key: ScrollOffsetPreferenceKey.self, value: geo.frame(in: coordinateSpace).minY)
            }
        )
    }
    
    // 添加自定义圆角修饰符，使用不同名称避免冲突
    func customCornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCornerShape(radius: radius, corners: corners))
    }
}

// 自定义圆角形状
struct RoundedCornerShape: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners
    
    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// 咖啡豆统计框组件
struct StatsBox: View {
    let title: String
    let value: String
    var subtitle: String? = nil
    var isSelected: Bool = false
    var action: (() -> Void)? = nil
    
    var body: some View {
        Button(action: {
            action?()
        }) {
            VStack(spacing: 2) {
                Text(title)
                    .font(.footnote)
                    .foregroundColor(isSelected ? .accentColor : .detailText)
                
                Text(value)
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(isSelected ? .accentColor : .primaryText)
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.archivedText)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 8)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct BeanListView: View {
    @StateObject private var viewModel = BeanListViewModel()
    @EnvironmentObject private var themeManager: ThemeManager
    @EnvironmentObject private var appState: AppState  // 添加对AppState的引用
    @State private var searchText = ""
    @State private var showingAddSheet = false
    @State private var showingFilterSheet = false
    @State private var selectedBean: CoffeeBean?
    @State private var selectedBeanID: Int? = nil  // 用于控制导航的状态变量
    @State private var scrollOffset: CGFloat = 0
    @State private var resetSwipeStates: Bool = false // 用于重置所有轻扫状态
    @State private var showArchivedBeans: Bool = false // 控制已归档区域展开状态
    
    // 删除相关的状态变量
    @State private var beanToDelete: CoffeeBean? = nil
    @State private var showDeleteAlert = false
    @State private var isDeleting = false
    @State private var deleteError: String? = nil
    @State private var showDeleteErrorAlert = false
    
    // 修改相关状态变量
    @State private var beanToEdit: CoffeeBean? = nil
    
    // 添加回购记录相关状态变量
    @State private var beanToAddOccurrence: CoffeeBean? = nil
    
    // 添加导航到详情页的状态
    @State private var navigateToBeanDetail = false
    
    // 添加显示筛选页的模型
    @State private var filterToShow: BeanListFilter?
    
    // 添加显示添加页的标志
    @State private var shouldShowAddBean = false
    
    // 访问手势设置
    let gestureSettings = GestureSettings.shared
    
    var filteredBeans: [CoffeeBean] {
        if searchText.isEmpty {
            return viewModel.coffeeBeans
        } else {
            // 完全重构实现方式，避免使用任何可能导致Predicate冲突的集合方法
            var result: [CoffeeBean] = []
            let searchQuery = searchText.lowercased()
            
            for bean in viewModel.coffeeBeans {
                // 检查各个字段是否包含搜索文本
                let nameMatch = bean.name.lowercased().contains(searchQuery)
                let roasterMatch = bean.roaster.lowercased().contains(searchQuery)
                let originMatch = (bean.origin ?? "").lowercased().contains(searchQuery)
                let regionMatch = (bean.region ?? "").lowercased().contains(searchQuery)
                let fincaMatch = (bean.finca ?? "").lowercased().contains(searchQuery)
                let varietyMatch = (bean.variety ?? "").lowercased().contains(searchQuery)
                let processMatch = (bean.process ?? "").lowercased().contains(searchQuery)
                let notesMatch = (bean.notes ?? "").lowercased().contains(searchQuery)
                let typeDisplayMatch = bean.typeDisplay.lowercased().contains(searchQuery)
                let roastLevelDisplayMatch = bean.roastLevelDisplay.lowercased().contains(searchQuery)
                
                // 检查烘焙度等级
                let roastLevelMatch = bean.roastLevelEnum?.rawValue.lowercased().contains(searchQuery) ?? false
                
                // 如果任何字段匹配，则添加到结果集
                if nameMatch || roasterMatch || originMatch || regionMatch || 
                   fincaMatch || varietyMatch || processMatch || 
                   notesMatch || typeDisplayMatch || roastLevelDisplayMatch || roastLevelMatch {
                    result.append(bean)
                }
            }
            
            return result
        }
    }
    
    var body: some View {
        mainView
            .navigationTitle("我的咖啡豆")
            .navigationBarTitleDisplayMode(.large)
            .task {
                // 先加载咖啡豆数据
                await viewModel.fetchCoffeeBeans()
                
                // 然后检查是否需要导航到特定咖啡豆详情
                if let beanId = appState.selectedBeanId {
                    // 查找对应的咖啡豆
                    if let bean = viewModel.coffeeBeans.first(where: { $0.id == beanId }) {
                        print("通过URL导航到咖啡豆详情，ID: \(beanId)")
                        
                        // 延迟一下导航，避免刚加载页面就跳转造成的闪烁问题
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            // 导航到详情页
                            viewModel.viewBeanDetails(bean: bean)
                            
                            // 清除selectedBeanId，避免重复导航
                            appState.selectedBeanId = nil
                        }
                    } else {
                        print("未找到ID为\(beanId)的咖啡豆")
                        // 清除无效的selectedBeanId
                        appState.selectedBeanId = nil
                    }
                }
            }
            // 监听selectedBeanId的变化
            .onChange(of: appState.selectedBeanId) { newBeanId in
                if let beanId = newBeanId {
                    if let bean = viewModel.coffeeBeans.first(where: { $0.id == beanId }) {
                        print("监听到selectedBeanId变化，导航到咖啡豆详情，ID: \(beanId)")
                        
                        // 导航到详情页
                        viewModel.viewBeanDetails(bean: bean)
                        
                        // 清除selectedBeanId，避免重复导航
                        DispatchQueue.main.async {
                            appState.selectedBeanId = nil
                        }
                    } else {
                        print("未找到ID为\(beanId)的咖啡豆，尝试重新加载数据")
                        // 尝试重新加载数据，然后再次查找
                        Task {
                            await viewModel.fetchCoffeeBeans()
                            
                            if let bean = viewModel.coffeeBeans.first(where: { $0.id == beanId }) {
                                viewModel.viewBeanDetails(bean: bean)
                            }
                            
                            // 无论是否找到，都清除selectedBeanId
                            DispatchQueue.main.async {
                                appState.selectedBeanId = nil
                            }
                        }
                    }
                }
            }
            // 监听BeanListNeedsRefresh通知，用于在修改咖啡豆后刷新列表
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("BeanListNeedsRefresh"))) { _ in
                print("⚡️ 收到BeanListNeedsRefresh通知，刷新咖啡豆列表")
                
                // 确保在主线程执行UI更新
                Task { @MainActor in
                    // 先清除缓存
                    URLCache.shared.removeAllCachedResponses()
                    
                    // 短暂延迟确保缓存已清除
                    try? await Task.sleep(nanoseconds: 100_000_000) // 100毫秒
                    
                    // 强制刷新数据
                    await viewModel.fetchCoffeeBeans(forceRefresh: true)
                    
                    // 再次延迟确保数据已更新
                    try? await Task.sleep(nanoseconds: 100_000_000) // 100毫秒
                    
                    // 触发视图完全重新加载
                    viewModel.objectWillChange.send()
                    
                    print("♻️ 数据刷新完成并触发视图重载")
                }
            }
            // 添加对ShowAddOccurrenceForm通知的监听
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ShowAddOccurrenceForm"))) { notification in
                print("⚡️ 收到ShowAddOccurrenceForm通知，打开回购记录表单")
                
                if let bean = notification.object as? CoffeeBean {
                    // 设置要添加回购记录的咖啡豆
                    beanToAddOccurrence = bean
                }
            }
            // 接收回购记录表单提交后的通知，刷新咖啡豆数据
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("BeanDetailNeedsRefresh"))) { notification in
                print("⚡️ 收到BeanDetailNeedsRefresh通知，更新咖啡豆列表中的相应条目")
                
                // 获取通知中的咖啡豆数据
                if let updatedBean = notification.object as? CoffeeBean {
                    Task { @MainActor in
                        // 使用ViewModel的updateBean方法更新单个咖啡豆数据
                        viewModel.updateBean(updatedBean)
                        print("✅ 使用新方法更新了ID为\(updatedBean.id)的咖啡豆数据")
                        
                        // 如果是回购记录表单关闭，清除引用
                        if beanToAddOccurrence?.id == updatedBean.id {
                            beanToAddOccurrence = nil
                        }
                    }
                } else {
                    print("❌ BeanDetailNeedsRefresh通知中没有有效的咖啡豆数据")
                    
                    // 尝试完整刷新
                    Task { @MainActor in
                        await viewModel.fetchCoffeeBeans(forceRefresh: true)
                    }
                }
            }
    }
    
    // 主视图组件
    private var mainView: some View {
        ZStack {
            // 核心内容
            mainContentView
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        toolbarButtons
                    }
                }
                .confirmationDialog(
                    "确认删除",
                    isPresented: $showDeleteAlert,
                    titleVisibility: .visible
                ) {
                    Button("取消", role: .cancel) {
                        beanToDelete = nil
                    }
                    
                    Button("删除", role: .destructive) {
                        if let bean = beanToDelete {
                            deleteBean(bean)
                        }
                    }
                    .disabled(isDeleting)
                } message: {
                    if beanToDelete != nil {
                        if isDeleting {
                            Text("正在删除...")
                        } else {
                            Text("确定要删除这个咖啡豆吗？此操作不可撤销。删除后，相关冲煮记录不会受影响。")
                        }
                    }
                }
                .alert(
                    "删除失败",
                    isPresented: $showDeleteErrorAlert,
                    actions: {
                        Button("确定", role: .cancel) {}
                    },
                    message: {
                        Text(deleteError ?? "未知错误")
                    }
                )
                .navigationDestination(for: CoffeeBean.self) { bean in
                    // 为每个导航目标创建唯一的BeanDetailView实例
                    BeanDetailView(bean: bean)
                        .id(bean.id)
                }
            
            // 使用Background导航链接，确保状态清晰
            NavigationLink(
                destination: Group {
                    if let bean = viewModel.selectedBean {
                        BeanDetailView(bean: bean)
                            .id(bean.id) // 使用咖啡豆ID作为视图ID，确保视图是唯一的
                    } else {
                        EmptyView()
                    }
                },
                isActive: $viewModel.shouldNavigateToDetail,
                label: { EmptyView() }
            )
            .opacity(0) // 使链接不可见但可用
        }
        // 编辑咖啡豆表单
        .sheet(item: $beanToEdit) { bean in
            NavigationView {
                EditBeanView(bean: bean)
                    .id(UUID()) // 使用随机ID确保每次都是新实例
                    .onAppear {
                        print("EditBeanView已显示，加载的咖啡豆ID: \(bean.id), 名称: \(bean.name)")
                    }
            }
        }
        // 添加回购记录表单
        .sheet(item: $beanToAddOccurrence) { bean in
            NavigationView {
                BeanOccurrenceForm(viewModel: BeanOccurrenceViewModel(), bean: bean)
            }
        }
        // 添加咖啡豆表单
        .sheet(isPresented: $showingAddSheet) {
            NavigationView {
                AddBeanView()
            }
        }
        // 筛选弹窗
        .sheet(isPresented: $showingFilterSheet) {
            BeanListFilterPopover(
                viewModel: viewModel,
                isPresented: $showingFilterSheet
            )
        }
    }
    
    // 工具栏按钮
    private var toolbarButtons: some View {
        HStack(spacing: 12) {
            // 筛选按钮
            Button {
                showingFilterSheet = true
            } label: {
                ZStack {
                    Image(systemName: "line.3.horizontal.decrease")
                        .foregroundColor(Color.functionText)
                        .font(.system(size: 15))
                        .frame(width: 30, height: 30)
                        .background(Circle().fill(Color.navbarBg))
                    
                    // 如果有高级过滤条件（不包括状态筛选），才显示指示器
                    if viewModel.filter.hasAdvancedFilters {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                            .offset(x: 8, y: -8)
                    }
                }
            }
            
            // 添加按钮
            Button {
                showingAddSheet = true
            } label: {
                Image(systemName: "plus")
                    .foregroundColor(Color.functionText)
                    .font(.system(size: 16))
                    .frame(width: 30, height: 30)
                    .background(Circle().fill(Color.navbarBg))
            }
        }
    }
    
    // 主要内容视图
    private var mainContentView: some View {
        Group {
            if viewModel.coffeeBeans.isEmpty {
                if viewModel.isLoadingBeans {
                    // 添加VStack使loading指示器居中显示
                    VStack {
                        Spacer()
                        
                        // 使用BlinkingLoader替换ProgressView
                        BlinkingLoader(
                            color: .secondaryText,
                            width: 12,
                            height: 18,
                            duration: 1.5,
                            text: "正在加载咖啡豆..."
                        )
                        
                        Spacer()
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.secondaryBg)
                } else if let _ = viewModel.error {
                    errorStateView
                } else {
                    emptyStateView
                }
            } else {
                // 有数据时总是显示列表，不管加载状态如何
                beanListView
            }
        }
    }
    
    // 错误状态视图
    private var errorStateView: some View {
        VStack {
            Spacer()
            
            VStack(spacing: 20) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 50))
                    .foregroundColor(.error)
                
                Text("未能读取数据")
                    .font(.headline)
                
                if let apiError = viewModel.error as? APIError {
                    Text(apiError.userFriendlyMessage)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                } else if let error = viewModel.error {
                    Text(error.localizedDescription)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                } else {
                    Text("因为数据缺失")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                
                VStack(spacing: 12) {
                    Button(action: {
                        Task {
                            await viewModel.fetchCoffeeBeans(forceRefresh: true)
                        }
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("刷新")
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(Color.blue)
                        .foregroundColor(.primaryBg)
                        .cornerRadius(8)
                    }
                    
                    Button(action: {
                        Task {
                            // 清除现有缓存并强制刷新
                            URLCache.shared.removeAllCachedResponses()
                            try? await Task.sleep(nanoseconds: 500_000_000) // 等待500毫秒
                            await viewModel.fetchCoffeeBeans(forceRefresh: true)
                        }
                    }) {
                        HStack {
                            Image(systemName: "clear")
                            Text("清除缓存并刷新")
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(Color.secondaryBg)
                        .foregroundColor(.primaryText)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray, lineWidth: 1)
                        )
                    }
                }
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.secondaryBg)
    }
    
    // 空状态视图
    private var emptyStateView: some View {
        // 添加外层VStack并使用Spacer实现垂直居中
        VStack {
            Spacer()
            
            // 保持原有的内容布局
            VStack(spacing: 20) {
                Image(systemName: "cup.and.saucer")
                    .font(.system(size: 50))
                    .foregroundColor(Color.secondaryText)
                Text("暂无符合条件的咖啡豆")
                    .font(.title2)
                    .foregroundColor(Color.primaryText)
                Text("点击右上角 + 添加新的咖啡豆吧")
                    .font(.subheadline)
                    .foregroundColor(Color.detailText)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 20)
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.secondaryBg)
    }
    
    // 咖啡豆列表视图
    private var beanListView: some View {
        ZStack(alignment: .top) {
            // 添加一个全屏的背景色视图
            Color.secondaryBg
                .edgesIgnoringSafeArea(.all)
            
            ScrollViewReader { scrollProxy in
                beansList
                    .searchable(text: $searchText, prompt: "搜索咖啡豆")
                    .onChange(of: searchText) { newValue in
                        // 当搜索文本变化时，处理搜索行为
                        if !newValue.isEmpty {
                            // 可以在这里添加搜索记录或其他逻辑
                            resetSwipeStates = true
                        }
                    }
            }
        }
    }
    
    // 咖啡豆列表
    private var beansList: some View {
        List {
            // 统计信息部分 - 仅在非搜索状态显示
            if searchText.isEmpty {
                VStack(spacing: 0) {
                    // 统计卡片
                    HStack(spacing: 8) {
                        // 养豆中
                        StatsBox(
                            title: "养豆中",
                            value: "\(viewModel.stats.resting)",
                            subtitle: "款",
                            isSelected: viewModel.filter.statusFilter == .resting,
                            action: {
                                if viewModel.filter.statusFilter == .resting {
                                    viewModel.filterByStatus(nil)
                                } else {
                                    viewModel.filterByStatus(.resting)
                                }
                            }
                        )
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(viewModel.filter.statusFilter == .resting ? Color.primaryBg.opacity(0.7) : Color.primaryBg)
                        )
                        
                        // 已用完
                        StatsBox(
                            title: "已用完",
                            value: "\(viewModel.stats.outOfStock)",
                            subtitle: "款 (未归档)",
                            isSelected: viewModel.filter.statusFilter == .outOfStock,
                            action: {
                                if viewModel.filter.statusFilter == .outOfStock {
                                    viewModel.filterByStatus(nil)
                                } else {
                                    viewModel.filterByStatus(.outOfStock)
                                }
                            }
                        )
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(viewModel.filter.statusFilter == .outOfStock ? Color.primaryBg.opacity(0.7) : Color.primaryBg)
                        )
                        
                        // 使用中
                        StatsBox(
                            title: "使用中",
                            value: "\(viewModel.stats.inUse)",
                            subtitle: "款 (未用完)",
                            isSelected: viewModel.filter.statusFilter == .inUse,
                            action: {
                                if viewModel.filter.statusFilter == .inUse {
                                    viewModel.filterByStatus(nil)
                                } else {
                                    viewModel.filterByStatus(.inUse)
                                }
                            }
                        )
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(viewModel.filter.statusFilter == .inUse ? Color.primaryBg.opacity(0.7) : Color.primaryBg)
                        )
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.secondaryBg)
                    )
                    .padding(.horizontal, 8)
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets(top: 8, leading: 12, bottom: 0, trailing: 12))
                
                // 分组显示标题和数量
                HStack {
                    Text(getStatusFilterText())
                        .font(.caption)
                        .foregroundColor(Color.noteText)
                        .padding(.vertical, 4)
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets(top: 2, leading: 16, bottom: 4, trailing: 16))
                .id("headerRow")
                .trackScrollOffset()  // 使用更新后的修饰符跟踪滚动位置
            }
            
            // 检查是否有搜索文本，使用filteredBeans而不是viewModel.activeBeans
            if !searchText.isEmpty {
                // 搜索结果区域
                Section(header: 
                    HStack {
                        Text("搜索结果")
                            .font(.footnote)
                            .foregroundColor(.noteText)
                            .padding(.vertical, 4)
                            
                        Text("(\(filteredBeans.count)款)")
                            .foregroundColor(.noteText)
                            .font(.footnote)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 12)
                    .listRowInsets(EdgeInsets())
                    .background(Color.secondaryBg)
                ) {
                    if filteredBeans.isEmpty {
                        HStack {
                            Spacer()
                            VStack(spacing: 12) {
                                Image(systemName: "magnifyingglass")
                                    .font(.system(size: 30))
                                    .foregroundColor(.secondary)
                                
                                Text("没有找到符合条件的咖啡豆")
                                    .font(.callout)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                            }
                            .padding(.vertical, 30)
                            Spacer()
                        }
                        .listRowBackground(Color.secondaryBg)
                    } else {
                        ForEach(filteredBeans) { bean in
                            beanRowView(for: bean)
                                .id(bean.id)
                        }
                    }
                }
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets())
                .listRowBackground(Color.secondaryBg)
            } else {
                // 活跃咖啡豆区域 - 仅在未搜索时显示
                if !viewModel.activeBeans.isEmpty {
                    ForEach(viewModel.activeBeans) { group in
                        Section(header: 
                            HStack {
                                Text(group.name)
                                    .font(.footnote)
                                    .foregroundColor(.secondaryText)
                                    .padding(.vertical, 4)
                                Spacer()
                            }
                            .padding(.horizontal, 12)
                            .listRowInsets(EdgeInsets())
                            .background(Color.secondaryBg)
                        ) {
                            ForEach(group.beans) { bean in
                                beanRowView(for: bean)
                                    .id(bean.id)
                            }
                        }
                        .listRowSeparator(.hidden)
                        .listRowInsets(EdgeInsets())
                        .listRowBackground(Color.secondaryBg)
                    }
                }
                
                // 已归档咖啡豆区域 - 重构为与活跃咖啡豆相同的结构
                if !viewModel.archivedBeans.isEmpty {
                    // 添加分隔线增强视觉分隔
                    Section {
                        Rectangle()
                            .fill(Color.navbarBg)
                            .frame(height: 8)
                    }
                    .listRowBackground(Color.secondaryBg)
                    .listRowSeparator(.hidden)
                    .listRowInsets(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0))
                    
                    // 归档标题区域
                    Section {
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showArchivedBeans.toggle()
                            }
                        }) {
                            HStack {
                                Spacer()

                                Image(systemName: showArchivedBeans ? "arrowtriangle.down.fill" : "arrowtriangle.right.fill")
                                    .foregroundColor(.detailText)
                                    .font(.system(size: 13))

                                Text("已归档咖啡豆")
                                    .font(.system(size: 15, weight: .semibold))
                                    .foregroundColor(.primaryText)

                                Text("(\(viewModel.archivedBeans.flatMap { $0.beans }.count)款)")
                                    .foregroundColor(.secondaryText)
                                    .font(.footnote)

                                Spacer()
                            }
                            .contentShape(Rectangle())
                            .padding(.horizontal, 12)
                            .padding(.bottom, 8)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    .listRowBackground(Color.secondaryBg)
                    .listRowSeparator(.hidden)
                    .listRowInsets(EdgeInsets(top: 8, leading: 12, bottom: 0, trailing: 12))
                    
                    // 已归档内容区域 - 仅在展开状态显示
                    if showArchivedBeans {
                        ForEach(viewModel.archivedBeans) { group in
                            Section(header: 
                                HStack {
                                    Text(group.name)
                                        .font(.footnote)
                                        .foregroundColor(.secondaryText)
                                        .padding(.vertical, 4)
                                    Spacer()
                                }
                                .padding(.horizontal, 12)
                                .listRowInsets(EdgeInsets())
                                .background(Color.secondaryBg)
                            ) {
                                ForEach(group.beans) { bean in
                                    beanRowView(for: bean)
                                        .id(bean.id)
                                }
                            }
                            .listRowSeparator(.hidden)
                            .listRowInsets(EdgeInsets())
                            .listRowBackground(Color.secondaryBg)
                            .transition(.opacity)
                        }
                    }
                }
            }
            
            // 底部加载状态
            beanListFooterView
        }
        .id("beanList")
        .background(Color.secondaryBg)
        .scrollContentBackground(.hidden) // 关键: 隐藏默认的滚动内容背景
        .listStyle(PlainListStyle())
        .simultaneousGesture(
            // 监听列表的点击手势，点击空白区域时重置所有轻扫状态
            TapGesture().onEnded { _ in
                resetSwipeStates = true
            }
        )
        // 添加对滚动时的监听，确保滚动时不会出现跳跃
        .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
            if abs(scrollOffset - value) > 5 {
                // 当滚动距离超过阈值时，如果已归档展开，则收起
                if showArchivedBeans && abs(scrollOffset - value) > 50 {
                    withAnimation(.easeInOut(duration: 0.25)) {
                        showArchivedBeans = false
                    }
                }
                scrollOffset = value
                resetSwipeStates = true
            }
        }
    }
    
    // 列表底部视图
    private var beanListFooterView: some View {
        Group {
            if !viewModel.coffeeBeans.isEmpty {
                HStack {
                    Spacer()
                    if viewModel.isLoadingBeans && !viewModel.coffeeBeans.isEmpty {
                        // 使用BlinkingLoader替换ProgressView
                        BlinkingLoader(
                            color: .secondaryText,
                            width: 12,
                            height: 15,
                            duration: 1.5,
                            text: "加载更多..."
                        )
                        .padding(.vertical, 10)
                    } else if viewModel.coffeeBeans.count > 0 {
                        Text("— 到底了 —")
                            .font(.footnote)
                            .foregroundColor(Color.noteText.opacity(0.6))
                            .padding(.vertical, 10)
                    }
                    Spacer()
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets(top: 4, leading: 12, bottom: 4, trailing: 12))
            }
        }
    }
    
    // 获取状态过滤文本
    private func getStatusFilterText() -> String {
        if !searchText.isEmpty {
            let searchResultCount = filteredBeans.count
            return "共找到 \(searchResultCount) 款符合搜索条件的咖啡豆"
        }
        
        let activeCount = viewModel.activeBeans.flatMap { $0.beans }.count
        let archivedCount = viewModel.archivedBeans.flatMap { $0.beans }.count
        
        // 基础文本部分
        var baseText = "共 \(activeCount) 款"
        
        // 如果有状态筛选，添加对应状态文本
        if let status = viewModel.filter.statusFilter {
            let statusText: String
            switch status {
            case .resting:
                statusText = "养豆中"
            case .inUse:
                statusText = "使用中"
            case .outOfStock:
                statusText = "已用完"
            case .bestFlavor:
                statusText = "最佳赏味期"
            }
            
            baseText += "「\(statusText)」"
        } else if viewModel.filter.hasAdvancedFilters {
            // 有高级筛选但没有状态筛选
            baseText += "符合条件的"
        }
        
        // 添加归档信息
        baseText += "咖啡豆"
        if archivedCount > 0 {
            baseText += " · 已归档 \(archivedCount) 款"
        }
        
        return baseText
    }
    
    // 单行咖啡豆视图
    private func beanRowView(for bean: CoffeeBean) -> some View {
        // 使用SwipeItem来实现自定义左右轻扫菜单
        ZStack {
            // 获取左右轻扫设置
            let leftAction = gestureSettings.getAction(for: .coffeeBean, direction: .left)
            let rightAction = gestureSettings.getAction(for: .coffeeBean, direction: .right)
            
            // 根据咖啡豆是否已设为首选，动态调整显示的操作文本和颜色
            let isFavorited = bean.isFavorite
            
            // 使用SwipeItem替换原生swipeActions
            SwipeItem(content: {
                ZStack {
                    CardWithPressEffect { isPressed in
                        beanCardContent(bean, isPressed: isPressed)
                    } onTap: {
                        // 导航到详情页
                        showBeanDetail(bean)
                    }
                    .contentShape(Rectangle())
                }
            }, left: {
                // 左侧轻扫内容 (向右轻扫)
                VStack(spacing: 4) {
                    // 为首选操作动态更改图标
                    if rightAction == .favorite && isFavorited {
                        Image(systemName: "star.slash")
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                            .symbolRenderingMode(.monochrome)
                    } else if rightAction == .edit || rightAction == .restock {
                        // 使用自定义图标
                        Image(rightAction.icon)
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                    } else {
                        Image(systemName: rightAction.icon)
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                            .symbolRenderingMode(.monochrome)
                    }
                    
                    // 判断是否是首选操作且已设为首选
                    if rightAction == .favorite && isFavorited {
                        Text("取消首选")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if rightAction == .archive && bean.isArchived {
                        Text("取消归档")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else {
                        Text(rightAction.rawValue)
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .contentShape(Rectangle())
                .onTapGesture {
                    // 触觉反馈
                    let generator = UIImpactFeedbackGenerator(style: .medium)
                    generator.impactOccurred()
                    
                    // 根据设置的操作执行相应的动作
                    if rightAction == .edit {
                        // 打开修改表单
                        navigateToEditForm(bean)
                    } else if rightAction == .delete {
                        // 调用删除确认方法
                        showDeleteConfirmation(for: bean)
                    } else if rightAction == .favorite {
                        // 处理首选操作
                        toggleFavorite(bean)
                    } else if rightAction == .archive {
                        // 处理归档操作
                        toggleArchive(bean)
                    } else if rightAction == .restock {
                        // 处理回购操作
                        showBeanOccurrenceForm(for: bean)
                    }
                    
                    // 点击后自动关闭轻扫菜单
                    resetSwipeStates = true
                }
            }, right: { deleteAction in
                // 右侧轻扫内容 (向左轻扫)
                VStack(spacing: 4) {
                    // 为首选操作动态更改图标
                    if leftAction == .favorite && isFavorited {
                        Image(systemName: "star.slash")
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                            .symbolRenderingMode(.monochrome)
                    } else if leftAction == .edit || leftAction == .restock {
                        // 使用自定义图标
                        Image(leftAction.icon)
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                    } else {
                        Image(systemName: leftAction.icon)
                            .foregroundColor(.primaryBg)
                            .font(.system(size: 24))
                            .symbolRenderingMode(.monochrome)
                    }
                    
                    // 根据轻扫状态和操作类型显示不同文本
                    if deleteAction && leftAction == .delete {
                        Text("删除")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if deleteAction && leftAction == .edit {
                        Text("修改")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if deleteAction && leftAction == .favorite {
                        // 根据是否已首选显示不同文本
                        Text(isFavorited ? "取消首选" : "设为首选")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if deleteAction && leftAction == .archive {
                        // 根据是否已归档显示不同文本
                        Text(bean.isArchived ? "取消归档" : "归档")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if deleteAction && leftAction == .restock {
                        Text("回购")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if leftAction == .favorite && isFavorited {
                        Text("取消首选")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else if leftAction == .archive && bean.isArchived {
                        Text("取消归档")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    } else {
                        Text(leftAction.rawValue)
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.primaryBg)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .contentShape(Rectangle())
                .onTapGesture {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.warning)
                    
                    // 根据设置的操作执行相应的动作
                    if leftAction == .delete {
                        // 调用删除确认方法
                        showDeleteConfirmation(for: bean)
                    } else if leftAction == .edit {
                        // 打开修改表单
                        navigateToEditForm(bean)
                    } else if leftAction == .favorite {
                        // 处理首选操作
                        toggleFavorite(bean)
                    } else if leftAction == .archive {
                        // 处理归档操作
                        toggleArchive(bean)
                    } else if leftAction == .restock {
                        // 处理回购操作
                        showBeanOccurrenceForm(for: bean)
                    }
                    
                    // 点击后自动关闭轻扫菜单
                    resetSwipeStates = true
                }
            }, onDelete: {
                // 向左轻扫完成动作
                if leftAction == .delete {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.warning)
                    
                    // 调用删除确认方法
                    showDeleteConfirmation(for: bean)
                } else if leftAction == .edit {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                    
                    // 调用修改表单
                    navigateToEditForm(bean)
                } else if leftAction == .favorite {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                    
                    // 处理首选操作
                    toggleFavorite(bean)
                } else if leftAction == .archive {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                    
                    // 处理归档操作
                    toggleArchive(bean)
                } else if leftAction == .restock {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                    
                    // 处理回购操作
                    showBeanOccurrenceForm(for: bean)
                }
            }, onEdit: {
                // 向右轻扫完成动作
                if rightAction == .edit {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                    
                    // 调用修改表单
                    navigateToEditForm(bean)
                } else if rightAction == .delete {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.warning)
                    
                    // 调用删除确认方法
                    showDeleteConfirmation(for: bean)
                } else if rightAction == .favorite {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                    
                    // 处理首选操作
                    toggleFavorite(bean)
                } else if rightAction == .archive {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                    
                    // 处理归档操作
                    toggleArchive(bean)
                } else if rightAction == .restock {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                    
                    // 处理回购操作
                    showBeanOccurrenceForm(for: bean)
                }
            }, resetTrigger: $resetSwipeStates,
               leftActionType: leftAction,
               rightActionType: rightAction,
               isLeftCompareSelected: false,
               isRightCompareSelected: false)
        }
        .frame(height: 136) // 确保整个容器高度固定为136
        .padding(.horizontal, 2)
        .contextMenu {
            // 点击查看详情菜单项
            Button(action: {
                showBeanDetail(bean)
            }) {
                Label("查看详情", systemImage: "eye")
            }
            
            // 分享选项
            Button {
                shareBean(bean)
            } label: {
                Label("分享文字版", systemImage: "square.and.arrow.up")
            }
                
            Divider()
            
            // 首选选项
            if bean.isFavorite {
                Button {
                    toggleFavorite(bean)
                } label: {
                    Label("取消首选", systemImage: "star.slash")
                }
            } else {
                Button {
                    toggleFavorite(bean)
                } label: {
                    Label("设为首选", systemImage: "star")
                }
            }
            
            // 归档选项
            if bean.isArchived {
                Button {
                    toggleArchive(bean)
                } label: {
                    Label("取消归档", systemImage: "archivebox.circle")
                }
            } else {
                Button {
                    toggleArchive(bean)
                } label: {
                    Label("归档", systemImage: "archivebox")
                }
            }
                
            Divider()
            
            // 添加回购记录选项
            Button {
                // 发送通知打开回购记录表单
                showBeanOccurrenceForm(for: bean)
            } label: {
                Label {
                    Text("回购")
                } icon: {
                    Image("beanOccurrence.symbols")
                }
            }
            
            // 修改选项
            Button {
                navigateToEditForm(bean)
            } label: {
                Label {
                    Text("修改")
                } icon: {
                    Image("edit.symbols")
                }
            }
            
            // 删除选项
            Button(role: .destructive) {
                // 显示确认对话框
                showDeleteConfirmation(for: bean)
            } label: {
                Label("删除", systemImage: "trash")
            }
        } preview: {
            BeanQuickPreviewView(viewModel: viewModel, bean: bean)
        }
        .listRowBackground(Color.secondaryBg)
        .listRowSeparator(.hidden)
        .listRowInsets(EdgeInsets(top: 8, leading: 12, bottom: 4, trailing: 12))  // 修改上下内边距为4像素
        .id("bean_row_\(bean.id)") // 为每行添加唯一ID
    }
    
    // 咖啡豆卡片内容
    private func beanCardContent(_ bean: CoffeeBean, isPressed: Bool) -> some View {
        VStack(alignment: .leading, spacing: 8) {  // 增加垂直间距
            // 第一行：豆商和状态图标
            HStack {
                Text(bean.roaster)
                    .font(.system(size: 16, weight: .light, design: .default))
                    .foregroundColor(.primaryText)
                    .lineLimit(1)
                
                Spacer()
                
                // 状态图标 - 重构为先判断是否归档，未归档才显示基础状态
                if !bean.isArchived {
                    if bean.baseStatus == .resting {
                        Image("resting.symbols")
                            .foregroundColor(.noteText)
                            .font(.subheadline)
                    } else if bean.baseStatus == .outOfStock {
                        Image("outtaStock.symbols")
                            .foregroundColor(.error)
                            .font(.subheadline)
                    } else if bean.baseStatus == .inUse {
                        Image("brewing.symbols")
                            .foregroundColor(.green)
                            .font(.subheadline)
                    }
                    
                    // 如果处于最佳赏味期，额外显示一个flavor图标
                    if bean.isInBestFlavorPeriod && bean.baseStatus == .inUse {
                        Image("flavor.symbols")
                            .foregroundColor(.linkText)
                            .font(.body)
                    }
                }
                
                if bean.isFavorite {
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .font(.subheadline)
                }
            }
            
            // 第二行：咖啡豆名称和平均评分
            HStack {
                Text(bean.name)
                    .font(.headline)
                    .lineLimit(1)
                
                if let avgRating = bean.avgRating {
                    // 直接调用函数获取心形图标
                    getHeartsByRating(avgRating)
                        .font(.caption)
                }
            }
            
            // 第三行
            HStack(alignment: .top) {  // 改为顶部对齐，保持图标和标签在上方对齐
                // 左侧信息区域
                VStack(alignment: .leading, spacing: 8) {  // 使用VStack允许内容换行
                    // 第一行：图标和烘焙度
                    HStack(spacing: 8) {
                        // 咖啡豆类型
                        if bean.typeDisplay != "跳过" {
                            Image(bean.typeDisplay == "单品" ? "single.symbols" : "blend.symbols")
                                .font(.callout)
                                .foregroundColor(.linkText)
                        }
                        // 是否低因咖啡
                        if bean.isDecaf {
                            Image("decaf.symbols")
                                .font(.callout)
                                .foregroundColor(.linkText)
                        }
                        // 备注
                        if !(bean.notes?.isEmpty ?? true) {
                            Image("note.symbols")
                                .foregroundColor(.linkText)
                                .font(.callout)
                        }
                        // 价格
                        if let price = bean.purchasePrice, price > 0 {
                            Image("price.symbols")
                                .foregroundColor(.linkText)
                                .font(.callout)
                        }
                        // 烘焙度显示逻辑
                        if bean.type == "SINGLE" {
                            // 单品咖啡：直接显示烘焙度
                            if let roastLevel = bean.roastLevelEnum {
                                Text(roastLevel.rawValue)
                                    .font(.caption)
                                    .foregroundColor(.primaryText)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.secondaryBg)
                                    .clipShape(Capsule())
                                    .overlay(
                                        Capsule()
                                            .stroke(Color.detailText.opacity(0.3), lineWidth: 0.5)
                                    )
                            }
                        } else if bean.type == "BLEND" {
                            // 拼配咖啡：检查blend_components里组件烘焙度是否一致
                            if let components = bean.blendComponents, !components.isEmpty {
                                // 找出最高和最低烘焙度
                                let roastLevels = components.map { $0.roastLevel }
                                
                                if let minLevel = roastLevels.min(), 
                                   let maxLevel = roastLevels.max() {
                                   
                                    // 所有组件烘焙度相同时才显示
                                    if minLevel == maxLevel {
                                        // 从组件中获取烘焙度显示文本
                                        if let firstComponent = components.first,
                                           let displayText = firstComponent.roastLevelDisplay {
                                            Text(displayText)
                                                .font(.caption)
                                                .foregroundColor(.primaryText)
                                                .padding(.horizontal, 6)
                                                .padding(.vertical, 2)
                                                .background(Color.secondaryBg)
                                                .clipShape(Capsule())
                                                .overlay(
                                                    Capsule()
                                                        .stroke(Color.detailText.opacity(0.3), lineWidth: 0.5)
                                                )
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // 第二行：风味标签
                    if let tasteNotes = bean.tasteNotes, !tasteNotes.isEmpty {
                        HStack(spacing: 6) {
                            // 最多显示4个标签
                            let maxTags = 4
                            let displayCount = min(tasteNotes.count, maxTags)
                            let hasMoreTags = tasteNotes.count > maxTags
                            
                            // 显示标签
                            ForEach(0..<displayCount, id: \.self) { index in
                                flavorTagView(text: tasteNotes[index])
                            }
                            
                            // 如果有更多标签，显示"+N"
                            if hasMoreTags {
                                flavorTagView(text: "+\(tasteNotes.count - maxTags)")
                            }
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)  // 占据左侧所有可用空间
                
                Spacer(minLength: 8)
                
                // 右侧进度环 - 如果咖啡豆已归档，则不显示进度环
                if !bean.isArchived {
                    BeanProgressRings(
                        bagRemain: bean.bagRemain,
                        bagWeight: bean.bagWeight,
                        daysSinceRoast: bean.roastDate.flatMap { date -> Int? in 
                            Calendar.current.dateComponents([.day], from: date, to: Date()).day
                        },
                        restPeriodMin: bean.restPeriodMin,
                        restPeriodMax: bean.restPeriodMax
                    )
                } else {
                    // 对已归档咖啡豆显示归档图标
                    Image(systemName: "archivebox.fill")
                        .foregroundColor(.navbarBg)
                        .font(.system(size: 20))
                        .frame(width: 56, height: 56)
                }
            }
        }
        .padding(.vertical, 12)  // 将垂直内边距从14减少到12
        .padding(.horizontal, 14)
        .background(
            RoundedRectangle(cornerRadius: 12)  // 修改圆角半径，确保与轻扫按钮一致
                .fill(isPressed ? Color.focusBg : Color.primaryBg)
                .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
        )
        .frame(height: 136)  // 给卡片内容设置固定高度
        .id("bean_card_\(bean.id)") // 添加唯一ID，避免卡片内容复用
    }
    
    // 提取出风味标签视图组件
    private func flavorTagView(text: String) -> some View {
        Text(text)
            .font(.caption)
            .foregroundColor(.primaryText)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.navbarBg)
            .clipShape(Capsule())
            .overlay(
                Capsule()
                    .stroke(Color.detailText.opacity(0.3), lineWidth: 0.5)
            )
    }
    
    // 获取状态颜色
    private func getStatusColor(_ status: BeanStatus) -> Color {
        switch status {
        case .resting:
            return .gray
        case .inUse:
            return .green
        case .outOfStock:
            return .error
        case .bestFlavor:
            return .blue
        }
    }
    
    // 显示删除确认弹窗
    private func showDeleteConfirmation(for bean: CoffeeBean) {
        beanToDelete = bean
        showDeleteAlert = true
    }
    
    // 导航到修改表单
    private func navigateToEditForm(_ bean: CoffeeBean) {
        // 先将beanToEdit设置为nil，确保和当前bean不同，强制触发SwiftUI的状态更新
        self.beanToEdit = nil
        
        // 使用Task确保状态更新被处理
        Task { @MainActor in
            // 等待一小段时间确保nil状态被处理
            try? await Task.sleep(for: .milliseconds(50))
            
            // 设置新的bean来触发sheet显示
            self.beanToEdit = bean
            
            // 打印日志以便调试
            print("navigateToEditForm设置beanToEdit：ID \(bean.id), 名称 \(bean.name)")
        }
    }
    
    // 删除咖啡豆
    private func deleteBean(_ bean: CoffeeBean) {
        isDeleting = true
        
        // 显示删除中的状态反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred(intensity: 0.7)
        
        Task {
            do {
                // 检查用户是否已登录
                guard APIService.shared.isLoggedIn else {
                    await MainActor.run {
                        isDeleting = false
                        deleteError = "您的登录已失效，请重新登录后再试"
                        showDeleteErrorAlert = true
                        
                        // 添加错误反馈
                        let generator = UINotificationFeedbackGenerator()
                        generator.notificationOccurred(.error)
                    }
                    return
                }
                
                // 删除咖啡豆
                try await viewModel.deleteBean(bean)
                
                await MainActor.run {
                    isDeleting = false
                    
                    // 添加成功删除的振动反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                    
                    // 已被删除，清除引用
                    beanToDelete = nil
                    
                    // 重置所有轻扫状态，提供更好的用户体验
                    resetSwipeStates = true
                }
            } catch let apiError as APIError where apiError == APIError.unauthorized {
                await MainActor.run {
                    isDeleting = false
                    deleteError = "您的登录已失效，请重新登录后再试"
                    showDeleteErrorAlert = true
                    
                    // 添加错误反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.error)
                }
            } catch {
                await MainActor.run {
                    isDeleting = false
                    
                    // 设置错误并显示错误提示弹窗
                    if let apiError = error as? APIError {
                        deleteError = apiError.userFriendlyMessage
                    } else {
                        deleteError = error.localizedDescription
                    }
                    showDeleteErrorAlert = true
                    
                    // 添加错误反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.error)
                }
            }
        }
    }
    
    // 切换首选状态
    private func toggleFavorite(_ bean: CoffeeBean) {
        Task {
            // 触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()
            
            await viewModel.toggleFavorite(bean)
        }
    }
    
    // 切换归档状态
    private func toggleArchive(_ bean: CoffeeBean) {
        Task {
            // 触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()
            
            if bean.isArchived {
                await viewModel.unarchiveBean(bean)
            } else {
                await viewModel.archiveBean(bean)
            }
        }
    }
    
    // 格式化日期方法 - 处理可选类型Date
    private func formatDate(_ date: Date?) -> String {
        guard let validDate = date else { return "-" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: validDate)
    }
    
    // 根据评分获取对应的爱心图标
    private func getHeartsByRating(_ rating: Double) -> some View {
        // 先计算心形数量，将switch移到ViewBuilder外部
        let heartCount: Int
        
        switch rating {
        case _ where rating > 8: heartCount = 5   // 8分以上显示5颗心
        case _ where rating > 6: heartCount = 4   // 6-8分显示4颗心
        case _ where rating > 4: heartCount = 3   // 4-6分显示3颗心
        case _ where rating > 2: heartCount = 2   // 2-4分显示2颗心
        case _ where rating >= 0: heartCount = 1  // 0-2分显示1颗心
        default: heartCount = 0
        }
        
        // 使用计算好的heartCount返回视图
        return HStack(spacing: 2) {
            ForEach(0..<heartCount, id: \.self) { _ in
                Image(systemName: "heart.fill")
                    .foregroundColor(.red)
            }
        }
    }
    
    // 分享咖啡豆信息
    private func shareBean(_ bean: CoffeeBean) {
        // 构建分享文本，按照AddBeanView表单字段顺序
        var shareText = "🫘 咖啡豆信息 (ID: \(bean.id))\n\n"

        // 1. 基本信息
        shareText += "名称: \(bean.name)\n"
        shareText += "类型: \(bean.typeDisplay)\n"
        shareText += "豆商: \(bean.roaster)\n"

        if let notes = bean.notes, !notes.isEmpty {
            shareText += "备注: \(notes)\n"
        }

        if bean.isDecaf {
            shareText += "低咖啡因: 是\n"
        }

        if let tasteNotes = bean.tasteNotes, !tasteNotes.isEmpty {
            let tasteNotesText = tasteNotes.joined(separator: "、")
            shareText += "风味标签: \(tasteNotesText)\n"
        }
        shareText += "\n"

        // 2. 包装信息
        var hasPackagingInfo = false

        if let bagWeight = bean.bagWeight {
            shareText += "包装规格: \(String(format: "%.0f", bagWeight))g\n"
            hasPackagingInfo = true
        }

        if let bagRemain = bean.bagRemain {
            shareText += "库存余量: \(String(format: "%.1f", bagRemain))g\n"
            hasPackagingInfo = true
        }

        if let purchasePrice = bean.purchasePrice {
            shareText += "购买价格: ¥\(String(format: "%.2f", purchasePrice))\n"
            hasPackagingInfo = true
        }

        // 购买时间（使用createdAt）
        shareText += "购买时间: \(DateFormatter.shareFormatter.string(from: bean.createdAt))\n"
        hasPackagingInfo = true

        if let roastDate = bean.roastDate {
            shareText += "烘焙日期: \(DateFormatter.dateOnlyFormatter.string(from: roastDate))\n"
            hasPackagingInfo = true
        }

        // 养豆期
        if let restMin = bean.restPeriodMin {
            if let restMax = bean.restPeriodMax, restMax != restMin {
                shareText += "养豆期: \(restMin)-\(restMax)天\n"
            } else {
                shareText += "养豆期: \(restMin)天\n"
            }
            hasPackagingInfo = true
        }

        if hasPackagingInfo {
            shareText += "\n"
        }

        // 3. 详细属性
        var hasDetailInfo = false

        if let origin = bean.origin, !origin.isEmpty {
            shareText += "产地: \(origin)\n"
            hasDetailInfo = true
        }

        if let region = bean.region, !region.isEmpty {
            shareText += "产区: \(region)\n"
            hasDetailInfo = true
        }

        if let finca = bean.finca, !finca.isEmpty {
            shareText += "庄园/处理站: \(finca)\n"
            hasDetailInfo = true
        }

        if let variety = bean.variety, !variety.isEmpty {
            shareText += "品种: \(variety)\n"
            hasDetailInfo = true
        }

        if let process = bean.process, !process.isEmpty {
            shareText += "处理法: \(process)\n"
            hasDetailInfo = true
        }

        shareText += "烘焙度: \(bean.roastLevelDisplay)\n"
        hasDetailInfo = true

        // 海拔信息
        if bean.altitudeType == "SINGLE" {
            if let altitude = bean.altitudeSingle {
                shareText += "海拔: \(altitude)m\n"
                hasDetailInfo = true
            }
        } else if bean.altitudeType == "RANGE" {
            if let minAlt = bean.altitudeMin, let maxAlt = bean.altitudeMax {
                shareText += "海拔: \(minAlt)-\(maxAlt)m\n"
                hasDetailInfo = true
            }
        }

        // 如果有评分信息
        if let avgRating = bean.avgRating {
            shareText += "\n平均评分: \(String(format: "%.1f", avgRating))/10\n"
        }
        
        // 创建分享活动视图控制器
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
        
        // 获取当前的UIWindow
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true, completion: nil)
        }
    }
    
    // 修改显示详情页的封装方法
    private func showBeanDetail(_ bean: CoffeeBean) {
        print("📱 导航到咖啡豆详情：ID \(bean.id), 名称: \(bean.name)")
        
        // 先清除所有可能的导航状态
        viewModel.shouldNavigateToDetail = false
        viewModel.selectedBean = nil
        
        // 使用Task确保状态更新被处理
        Task { @MainActor in
            // 等待短暂延时确保状态被更新
            try? await Task.sleep(for: .milliseconds(50))
            
            // 设置选中的咖啡豆，触发导航
            viewModel.selectedBean = bean
            viewModel.shouldNavigateToDetail = true
            
            // 立即重置所有轻扫状态，确保平滑过渡
            resetSwipeStates = true
        }
    }
    
    // 添加显示回购表单的辅助方法
    private func showBeanOccurrenceForm(for bean: CoffeeBean) {
        // 发送通知打开回购记录表单
        NotificationCenter.default.post(
            name: NSNotification.Name("ShowAddOccurrenceForm"),
            object: bean
        )
    }
}

// 咖啡豆状态标签
struct StatusLabel: View {
    let status: BeanStatus
    
    var body: some View {
        HStack(spacing: 4) {
            Image(status == .resting ? "resting.symbols" : 
                  status == .inUse ? "brewing.symbols" : 
                  status == .bestFlavor ? "flavor.symbols" : "outtaStock.symbols")
                .foregroundColor(statusColor)
                .font(.caption)
            
            Text(statusText)
                    .font(.caption)
                    .foregroundColor(.secondary)
        }
    }
    
    private var statusColor: Color {
        switch status {
        case .resting:
            return .gray
        case .inUse:
            return .green
        case .outOfStock:
            return .error
        case .bestFlavor:
            return .blue
        }
    }
    
    private var statusText: String {
        switch status {
        case .resting:
            return "养豆中"
        case .inUse:
            return "使用中"
        case .outOfStock:
            return "已用完"
        case .bestFlavor:
            return "最佳赏味期"
        }
    }
}

#if DEBUG
struct BeanListView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            BeanListView()
                .environmentObject(ThemeManager.shared)
        }
    }
}
#endif 
