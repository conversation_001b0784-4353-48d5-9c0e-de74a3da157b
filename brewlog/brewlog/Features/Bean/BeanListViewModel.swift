import Foundation
import SwiftUI
import Combine
import UIKit

@MainActor
class BeanListViewModel: ObservableObject {
    private let apiService: APIService
    // 添加通知服务引用
    private let beanNotificationService = BeanNotificationService.shared
    
    // MARK: - Published Properties
    @Published var coffeeBeans: [CoffeeBean] = []
    @Published var filter = BeanListFilter()
    @Published var stats = BeanStats()
    @Published var activeBeans: [BeanGroup] = []
    @Published var archivedBeans: [BeanGroup] = []
    @Published var isLoadingBeans = false
    @Published var showingFilters = false
    @Published var error: Error?
    @Published var selectedBean: CoffeeBean?
    @Published var shouldNavigateToDetail = false
    
    // 可用的筛选选项
    @Published var availableRoastLevels: [(String, String)] = []
    @Published var availableProcesses: [(String, String)] = []
    @Published var availableRoasters: [String] = []
    @Published var availableVarieties: [String] = []
    
    // 分组类型
    enum GroupType {
        case month
        case rating
        case roaster
    }
    @Published var groupType: GroupType = .month
    
    @Published var beans: [CoffeeBean] = []
    private var lastUpdateTimestamp: Int = 0
    
    init(apiService: APIService = APIService.shared) {
        self.apiService = apiService
        
        // 创建默认筛选值
        createDefaultFilters()
        
        // 将自己注册到共享视图模型中
        SharedViewModels.shared.beanListViewModel = self
        
        // 初始加载数据
        Task {
            await fetchCoffeeBeans()
            await fetchFilterOptions()
            
            // 加载完数据后，检查并调度咖啡豆通知
            self.checkAndScheduleBeanNotifications()
        }
        
        // 添加通知监听，用于检查Bean状态
        setupNotifications()
    }
    
    // 创建默认筛选选项
    private func createDefaultFilters() {
        // 烘焙度
        availableRoastLevels = [
            ("1", "极浅烘"),
            ("2", "浅烘"),
            ("3", "中浅烘"),
            ("4", "中烘"),
            ("5", "中深烘"),
            ("6", "深烘"),
            ("7", "极深烘")
        ]
        
        // 处理法
        availableProcesses = [
            ("WASHED", "水洗"),
            ("NATURAL", "日晒"),
            ("HONEY", "蜜处理"),
            ("ANAEROBIC", "厌氧"),
            ("CARBONIC", "碳浸"),
            ("MIXED", "混合处理")
        ]
    }
    
    // 获取咖啡豆数据
    func fetchCoffeeBeans(forceRefresh: Bool = false) async {
        if isLoadingBeans && !forceRefresh {
            return // 避免重复加载
        }
        
        // 设置加载状态
        await MainActor.run {
            isLoadingBeans = true
            error = nil // 清除之前的错误
        }
        
        // 构建URL - 修正API路径
        var urlComponents = URLComponents(string: "\(APIService.shared.getBaseURL())/ios/api/beans/")!
        var queryItems = [URLQueryItem(name: "page_size", value: "20")]
        
        // 添加强制刷新参数
        if forceRefresh {
            queryItems.append(URLQueryItem(name: "force_refresh", value: "true"))
            // 添加随机参数以避免缓存
            queryItems.append(URLQueryItem(name: "cache_buster", value: "\(Date().timeIntervalSince1970)"))
        }
        
        urlComponents.queryItems = queryItems
        
        // 创建请求
        var request = URLRequest(url: urlComponents.url!)
        request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
        
        // 关键修改: 增强缓存控制
        if forceRefresh {
            // 完全禁用缓存
            request.cachePolicy = URLRequest.CachePolicy.reloadIgnoringLocalAndRemoteCacheData
            request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
            request.addValue("no-store", forHTTPHeaderField: "Cache-Control")
            request.addValue("must-revalidate", forHTTPHeaderField: "Cache-Control")
            request.addValue("no-cache", forHTTPHeaderField: "Pragma")
            
            // 强制远程服务端也刷新
            request.addValue("0", forHTTPHeaderField: "If-Modified-Since")
            request.addValue("\"0\"", forHTTPHeaderField: "If-None-Match")
            
            // 清除URLSession缓存
            URLCache.shared.removeCachedResponse(for: request)
        }
        
        #if DEBUG
        print("🔍 开始获取咖啡豆数据: \(request.url?.absoluteString ?? "")")
        if forceRefresh {
            print("🔄 执行强制刷新，禁用缓存")
        }
        #endif
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            if let httpResponse = response as? HTTPURLResponse {
                #if DEBUG
                print("📥 咖啡豆API响应状态码: \(httpResponse.statusCode)")
                #endif
                
                if httpResponse.statusCode == 200 {
                    // 检查X-Updated-At头
                    if let updatedAtHeader = httpResponse.value(forHTTPHeaderField: "X-Updated-At"),
                       let timestamp = Int(updatedAtHeader) {
                        UserDefaults.standard.set(timestamp, forKey: "beans_last_update")
                        self.lastUpdateTimestamp = timestamp
                        #if DEBUG
                        print("📅 从X-Updated-At头更新时间戳: \(timestamp)")
                        #endif
                    }
                    
                    // 注释: 修复JSON解析错误
                    // 这里我们对API返回的数据进行更灵活的处理，同时支持两种可能的格式:
                    // 1. 分页格式: {"count": 10, "results": [...], "next": ...}
                    // 2. 数组格式: [...]
                    // 通过先检查JSON是否包含"results"字段，我们可以确定使用哪种解析方式
                    do {
                        let decoder = JSONDecoder()
                        
                        // 设置日期解码策略，增强兼容性
                        decoder.dateDecodingStrategy = .custom({ decoder in
                            let container = try decoder.singleValueContainer()
                            if let dateString = try? container.decode(String.self) {
                                let formatter = DateFormatter()
                                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
                                if let date = formatter.date(from: dateString) {
                                    return date
                                }
                                
                                // 尝试更多格式
                                let formats = ["yyyy-MM-dd'T'HH:mm:ssZ", "yyyy-MM-dd"]
                                for format in formats {
                                    formatter.dateFormat = format
                                    if let date = formatter.date(from: dateString) {
                                        return date
                                    }
                                }
                                return Date()
                            } else if let timestamp = try? container.decode(Double.self) {
                                return Date(timeIntervalSince1970: timestamp)
                            }
                            return Date()
                        })
                        
                        // 先尝试解析为带分页的响应格式
                        if let jsonObject = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                           jsonObject["results"] != nil {
                            // 使用分页格式API响应
                            let apiResponse = try decoder.decode(APIResponse<CoffeeBean>.self, from: data)
                            let beans = apiResponse.results
                            
                            #if DEBUG
                            print("✅ 成功解析咖啡豆数据（分页格式），共\(beans.count)条记录")
                            #endif
                            
                            await MainActor.run {
                                self.beans = beans
                                self.coffeeBeans = beans
                                self.processBeanData(beans)
                                self.isLoadingBeans = false
                                
                                // 如果没有从头信息获取到时间戳，则使用当前时间
                                if self.lastUpdateTimestamp == 0 {
                                    let timestamp = Int(Date().timeIntervalSince1970)
                                    UserDefaults.standard.set(timestamp, forKey: "beans_last_update")
                                    self.lastUpdateTimestamp = timestamp
                                }
                                
                                // 清理过期的通知记录
                                self.cleanupNotificationRecords()
                                
                                // 在数据更新后检查并调度通知
                                self.checkAndScheduleBeanNotifications()
                            }
                        } else {
                            // 尝试直接解析为数组格式
                            let beans = try decoder.decode([CoffeeBean].self, from: data)
                            
                            #if DEBUG
                            print("✅ 成功解析咖啡豆数据（数组格式），共\(beans.count)条记录")
                            #endif
                            
                            await MainActor.run {
                                self.beans = beans
                                self.coffeeBeans = beans
                                self.processBeanData(beans)
                                self.isLoadingBeans = false
                                
                                // 如果没有从头信息获取到时间戳，则使用当前时间
                                if self.lastUpdateTimestamp == 0 {
                                    let timestamp = Int(Date().timeIntervalSince1970)
                                    UserDefaults.standard.set(timestamp, forKey: "beans_last_update")
                                    self.lastUpdateTimestamp = timestamp
                                }
                                
                                // 清理过期的通知记录
                                self.cleanupNotificationRecords()
                                
                                // 在数据更新后检查并调度通知
                                self.checkAndScheduleBeanNotifications()
                            }
                        }
                    } catch {
                        await MainActor.run {
                            let decodingError = APIError.decodingError(error)
                            self.error = decodingError
                            self.isLoadingBeans = false
                            
                            #if DEBUG
                            print("❌ 咖啡豆数据解析失败: \(error)")
                            print("📝 错误详情: \(error)")
                            
                            // 尝试打印服务器返回的原始JSON数据，帮助调试
                            if let jsonString = String(data: data, encoding: .utf8) {
                                print("📄 服务器返回的JSON数据:")
                                print(jsonString)
                            }
                            #endif
                        }
                    }
                } else {
                    // 处理非200状态码
                    await MainActor.run {
                        var message: String? = nil
                        if let jsonData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                           let errorMessage = jsonData["detail"] as? String {
                            message = errorMessage
                        }
                        
                        let serverError = APIError.serverError(httpResponse.statusCode, message ?? "未知服务器错误")
                        self.error = serverError
                        self.isLoadingBeans = false
                        
                        #if DEBUG
                        print("❌ 服务器错误: \(httpResponse.statusCode)")
                        if let message = message {
                            print("📝 错误信息: \(message)")
                        }
                        #endif
                    }
                }
            } else {
                await MainActor.run {
                    self.error = APIError.invalidResponse
                    self.isLoadingBeans = false
                    
                    #if DEBUG
                    print("❌ 无效的响应")
                    #endif
                }
            }
        } catch {
            await MainActor.run {
                self.error = APIError.networkError(error)
                self.isLoadingBeans = false
                
                #if DEBUG
                print("❌ 网络错误: \(error)")
                #endif
            }
        }
    }
    
    // 获取筛选选项数据
    func fetchFilterOptions() async {
        // 如果没有咖啡豆数据，先尝试加载
        if coffeeBeans.isEmpty {
            // 如果未在加载中，尝试加载
            if !isLoadingBeans {
                await fetchCoffeeBeans()
            }
            
            // 如果加载后仍然没有数据或有错误，则退出，但在开发环境输出更多信息
            if coffeeBeans.isEmpty {
                #if DEBUG
                print("⚠️ 无法获取筛选选项：没有可用的咖啡豆数据")
                if let error = error {
                    print("📋 错误详情: \(error)")
                    if let apiError = error as? APIError {
                        print("📋 用户友好错误信息: \(apiError.userFriendlyMessage)")
                    }
                }
                #endif
                return
            }
        }
        
        #if DEBUG
        let totalCount = coffeeBeans.count
        let activeCount = coffeeBeans.filter { !$0.isArchived }.count
        let archivedCount = coffeeBeans.filter { $0.isArchived }.count
        print("📊 从已加载的咖啡豆数据中提取筛选选项，共\(totalCount)项 (活跃: \(activeCount), 归档: \(archivedCount))")
        #endif
        
        // 从所有咖啡豆数据中提取唯一的豆商和品种，确保包括已归档的数据
        // 使用 compactMap 和空字符串检查可以避免nil值和空字符串
        let roasters = Set(coffeeBeans.map { $0.roaster }).sorted()
        
        // 提取所有品种，包括已归档的咖啡豆
        var allVarieties = Set(coffeeBeans.compactMap { $0.variety }.filter { !$0.isEmpty })
        
        // 提取所有处理法，包括已归档的咖啡豆
        var allProcesses = Set(coffeeBeans.compactMap { $0.process }.filter { !$0.isEmpty })
        
        // 从拼配组件中收集额外的处理法和品种
        for bean in coffeeBeans {
            if let components = bean.blendComponents {
                // 收集拼配组件中的处理法
                let componentProcesses = components.compactMap { $0.process }.filter { !$0.isEmpty }
                allProcesses.formUnion(componentProcesses)
                
                // 收集拼配组件中的品种
                let componentVarieties = components.compactMap { $0.variety }.filter { !$0.isEmpty }
                allVarieties.formUnion(componentVarieties)
            }
        }
        
        // 排序处理
        let varieties = Array(allVarieties).sorted()
        
        // 更新处理法选项，将代码值映射为显示名称
        let processOptions: [(String, String)] = allProcesses.map { processCode in
            let displayName = getProcessDisplayName(processCode)
            return (processCode, displayName)
        }.sorted { $0.1 < $1.1 } // 按显示名称排序
        
        #if DEBUG
        // 检查归档和非归档数据的处理法种类
        let activeProcesses = Set(coffeeBeans.filter { !$0.isArchived }.compactMap { $0.process }.filter { !$0.isEmpty })
        let archivedProcesses = Set(coffeeBeans.filter { $0.isArchived }.compactMap { $0.process }.filter { !$0.isEmpty })
        
        // 检查拼配组件中的处理法
        var blendComponentProcesses = Set<String>()
        for bean in coffeeBeans {
            if let components = bean.blendComponents {
                let componentProcesses = components.compactMap { $0.process }.filter { !$0.isEmpty }
                blendComponentProcesses.formUnion(componentProcesses)
            }
        }
        
        print("📊 处理法统计:")
        print("   - 活跃咖啡豆处理法: \(activeProcesses.sorted().joined(separator: ", "))")
        print("   - 归档咖啡豆处理法: \(archivedProcesses.sorted().joined(separator: ", "))")
        print("   - 拼配组件处理法: \(blendComponentProcesses.sorted().joined(separator: ", "))")
        
        // 检查归档和非归档数据的品种种类
        let activeVarieties = Set(coffeeBeans.filter { !$0.isArchived }.compactMap { $0.variety }.filter { !$0.isEmpty })
        let archivedVarieties = Set(coffeeBeans.filter { $0.isArchived }.compactMap { $0.variety }.filter { !$0.isEmpty })
        
        // 检查拼配组件中的品种
        var blendComponentVarieties = Set<String>()
        for bean in coffeeBeans {
            if let components = bean.blendComponents {
                let componentVarieties = components.compactMap { $0.variety }.filter { !$0.isEmpty }
                blendComponentVarieties.formUnion(componentVarieties)
            }
        }
        
        print("📊 品种统计:")
        print("   - 活跃咖啡豆品种: \(activeVarieties.sorted().joined(separator: ", "))")
        print("   - 归档咖啡豆品种: \(archivedVarieties.sorted().joined(separator: ", "))")
        print("   - 拼配组件品种: \(blendComponentVarieties.sorted().joined(separator: ", "))")
        #endif
        
        await MainActor.run {
            self.availableRoasters = Array(roasters)
            self.availableVarieties = varieties
            self.availableProcesses = processOptions
        }
        
        #if DEBUG
        print("✅ 成功提取筛选选项: \(roasters.count) 个豆商, \(varieties.count) 个品种, \(processOptions.count) 种处理法")
        #endif
    }
    
    // 获取处理法的显示名称
    private func getProcessDisplayName(_ processCode: String) -> String {
        // 根据处理法代码返回显示名称
        let processMapping: [String: String] = [
            "WASHED": "水洗",
            "NATURAL": "日晒",
            "HONEY": "蜜处理",
            "ANAEROBIC": "厌氧",
            "CARBONIC": "碳浸",
            "MIXED": "混合处理"
        ]
        
        return processMapping[processCode.uppercased()] ?? processCode
    }
    
    // 处理获取到的咖啡豆数据
    private func processBeanData(_ beans: [CoffeeBean]) {
        // 更新统计数据
        updateStats(beans)
        
        // 保存原始数据（如果是第一次加载）
        if self.coffeeBeans.isEmpty {
            self.coffeeBeans = beans
        }
        
        // 先获取筛选后的豆子
        let filteredBeans = applyFilters(beans)
        
        // 分开活跃和归档的豆子
        let active = filteredBeans.filter { !$0.isArchived }
        let archived = filteredBeans.filter { $0.isArchived }
        
        // 应用分组
        switch groupType {
        case .month:
            #if DEBUG
            print("📊 按月份分组，排序方式: \(filter.sortBy.rawValue)")
            #endif
            self.activeBeans = groupBeansByMonth(active)
            self.archivedBeans = groupBeansByMonth(archived)
        case .rating:
            #if DEBUG
            print("📊 按评分分组，排序方式: \(filter.sortBy.rawValue)")
            #endif
            self.activeBeans = groupBeansByRating(active)
            self.archivedBeans = groupBeansByRating(archived)
        case .roaster:
            #if DEBUG
            print("📊 按豆商分组，排序方式: \(filter.sortBy.rawValue)")
            #endif
            self.activeBeans = groupBeansByRoaster(active)
            self.archivedBeans = groupBeansByRoaster(archived)
        }
    }
    
    // 应用筛选条件
    private func applyFilters(_ beans: [CoffeeBean]) -> [CoffeeBean] {
        var result = beans
        
        #if DEBUG
        print("🔍 开始应用筛选条件，原始数据：\(beans.count)项")
        #endif
        
        // 筛选烘焙度
        if let roastLevel = filter.roastLevel, !roastLevel.isEmpty {
            if let level = Int(roastLevel) {
                result = result.filter { $0.roastLevel == level }
                #if DEBUG
                print("🔍 应用烘焙度筛选 (\(roastLevel))，结果：\(result.count)项")
                #endif
            }
        }
        
        // 筛选处理法
        if let process = filter.process, !process.isEmpty {
            #if DEBUG
            print("🔍 开始应用处理法筛选：\(process)")
            #endif
            
            result = result.filter { bean in
                // 检查主咖啡豆的处理法
                if bean.process == process {
                    #if DEBUG
                    print("   ✅ 咖啡豆ID:\(bean.id) - 主处理法匹配：\(process)")
                    #endif
                    return true
                }
                
                // 检查拼配组件中的处理法
                if let components = bean.blendComponents {
                    // 如果任何组件的处理法匹配，返回true
                    let hasMatchingProcess = components.contains { $0.process == process }
                    #if DEBUG
                    if hasMatchingProcess {
                        let matchingComponents = components.filter { $0.process == process }
                        print("   ✅ 咖啡豆ID:\(bean.id) - 拼配组件处理法匹配：找到\(matchingComponents.count)个组件")
                    } else if !components.isEmpty {
                        print("   ❌ 咖啡豆ID:\(bean.id) - 拼配组件处理法不匹配：检查了\(components.count)个组件")
                    }
                    #endif
                    return hasMatchingProcess
                }
                
                #if DEBUG
                print("   ❌ 咖啡豆ID:\(bean.id) - 处理法不匹配")
                #endif
                return false
            }
            
            #if DEBUG
            print("🔍 应用处理法筛选后结果：\(result.count)项")
            #endif
        }
        
        // 筛选豆商
        if let roaster = filter.roaster, !roaster.isEmpty {
            result = result.filter { $0.roaster == roaster }
            #if DEBUG
            print("🔍 应用豆商筛选 (\(roaster))，结果：\(result.count)项")
            #endif
        }
        
        // 筛选豆种
        if let variety = filter.variety, !variety.isEmpty {
            #if DEBUG
            print("🔍 开始应用品种筛选：\(variety)")
            #endif
            
            result = result.filter { bean in
                // 检查主咖啡豆的品种
                if bean.variety == variety {
                    #if DEBUG
                    print("   ✅ 咖啡豆ID:\(bean.id) - 主品种匹配：\(variety)")
                    #endif
                    return true
                }
                
                // 检查拼配组件中的品种
                if let components = bean.blendComponents {
                    // 如果任何组件的品种匹配，返回true
                    let hasMatchingVariety = components.contains { $0.variety == variety }
                    #if DEBUG
                    if hasMatchingVariety {
                        let matchingComponents = components.filter { $0.variety == variety }
                        print("   ✅ 咖啡豆ID:\(bean.id) - 拼配组件品种匹配：找到\(matchingComponents.count)个组件")
                    } else if !components.isEmpty {
                        print("   ❌ 咖啡豆ID:\(bean.id) - 拼配组件品种不匹配：检查了\(components.count)个组件")
                    }
                    #endif
                    return hasMatchingVariety
                }
                
                #if DEBUG
                print("   ❌ 咖啡豆ID:\(bean.id) - 品种不匹配")
                #endif
                return false
            }
            
            #if DEBUG
            print("🔍 应用品种筛选后结果：\(result.count)项")
            #endif
        }
        
        // 筛选评分范围
        if let ratingRange = filter.ratingRange {
            #if DEBUG
            print("📊 开始评分筛选, 范围: \(ratingRange)")
            #endif
            
            result = result.filter {
                // 优先使用avgRating，如果存在
                if let avgRating = $0.avgRating {
                    let matches = ratingRange.contains(avgRating)
                    #if DEBUG
                    print("   豆子ID:\($0.id) avgRating:\(avgRating) matches:\(matches)")
                    #endif
                    return matches
                }
                // 如果没有avgRating，尝试使用restPeriodProgress
                else if let rating = $0.restPeriodProgress {
                    let matches = ratingRange.contains(Double(rating))
                    #if DEBUG
                    print("   豆子ID:\($0.id) restPeriodProgress:\(rating) matches:\(matches)")
                    #endif
                    return matches
                }
                #if DEBUG
                print("   豆子ID:\($0.id) 无评分数据")
                #endif
                return false
            }
            
            #if DEBUG
            print("📊 评分筛选后数量: \(result.count)")
            #endif
        }
        
        // 筛选状态
        if let status = filter.statusFilter {
            result = result.filter { $0.belongsToStatus(status) }
            #if DEBUG
            print("🔍 应用状态筛选 (\(status))，结果：\(result.count)项")
            #endif
        }
        
        #if DEBUG
        print("✅ 筛选完成，最终结果：\(result.count)项")
        #endif
        
        return result
    }
    
    // 按照排序选项对豆子进行排序
    private func sortBeans(_ beans: [CoffeeBean], by sortOption: BeanSortOption) -> [CoffeeBean] {
        // 组内排序现在由分组函数处理，这个函数主要用于确保原始数据的顺序
        return sortBeansWithinGroup(beans, by: sortOption)
    }
    
    // 更新统计数据
    private func updateStats(_ beans: [CoffeeBean]) {
        let activeBeans = beans.filter { !$0.isArchived && !$0.isDeleted }
        
        // 使用belongsToStatus方法统计，确保inUse包含bestFlavor状态的咖啡豆
        stats.resting = activeBeans.filter { $0.belongsToStatus(.resting) }.count
        stats.outOfStock = activeBeans.filter { $0.belongsToStatus(.outOfStock) }.count
        stats.inUse = activeBeans.filter { $0.belongsToStatus(.inUse) }.count
    }
    
    // 按月份分组咖啡豆
    private func groupBeansByMonth(_ beans: [CoffeeBean]) -> [BeanGroup] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy年MM月"
        
        // 首先创建一个月份排序映射，用于辅助排序
        let allMonths = Set(beans.map { dateFormatter.string(from: $0.createdAt) })
        let sortedMonths = allMonths.sorted(by: >)  // 默认降序
        let monthOrderMap = Dictionary(uniqueKeysWithValues: zip(sortedMonths, (0..<sortedMonths.count)))
        
        let grouped = Dictionary(grouping: beans) { bean in
            dateFormatter.string(from: bean.createdAt)
        }
        
        // 首先按月份分组，然后在每个月份内部按照指定的排序选项排序
        let result = grouped.map { month, groupedBeans in
            // 在月份内部根据选择的排序方式进行排序
            let sortedBeans = sortBeansWithinGroup(groupedBeans, by: filter.sortBy)
            let orderValue = monthOrderMap[month] ?? 0
            return BeanGroup(name: month, beans: sortedBeans, order: orderValue)
        }
        
        // 根据排序选项决定分组顺序
        switch filter.sortBy {
        case .time:
            // 时间排序时，按月份升序或降序排列
            if filter.sortBy.isAscending {
                return result.sorted { $0.name < $1.name }  // 升序："2022年01月" < "2022年02月"
            } else {
                return result.sorted { $0.name > $1.name }  // 降序："2022年02月" > "2022年01月"
            }
        case .rating:
            // 评分排序时，先计算每个月份的平均评分，再排序
            return result.sorted { group1, group2 in
                // 计算两个组的平均评分
                let avgRating1 = calculateAverageRating(for: group1.beans)
                let avgRating2 = calculateAverageRating(for: group2.beans)
                
                // 根据升序/降序决定排序
                if filter.sortBy.isAscending {
                    return avgRating1 < avgRating2
                } else {
                    return avgRating1 > avgRating2
                }
            }
        case .roaster:
            // 豆商排序时，使用字母顺序排列月份内最多的豆商
            return result.sorted { group1, group2 in
                // 找出每个组中出现最多的豆商
                let mostCommonRoaster1 = findMostCommonRoaster(in: group1.beans) ?? ""
                let mostCommonRoaster2 = findMostCommonRoaster(in: group2.beans) ?? ""
                
                // 根据升序/降序决定排序
                if filter.sortBy.isAscending {
                    return mostCommonRoaster1 < mostCommonRoaster2
                } else {
                    return mostCommonRoaster1 > mostCommonRoaster2
                }
            }
        }
    }
    
    // 计算一组咖啡豆的平均评分
    private func calculateAverageRating(for beans: [CoffeeBean]) -> Double {
        let validRatings = beans.compactMap { bean -> Double? in
            if let avgRating = bean.avgRating {
                return avgRating
            } else if let progress = bean.restPeriodProgress {
                return Double(progress)
            }
            return nil
        }
        
        guard !validRatings.isEmpty else {
            return 0.0
        }
        
        return validRatings.reduce(0, +) / Double(validRatings.count)
    }
    
    // 找出一组咖啡豆中出现最多的豆商
    private func findMostCommonRoaster(in beans: [CoffeeBean]) -> String? {
        let roasterCounts = beans.reduce(into: [String: Int]()) { counts, bean in
            counts[bean.roaster, default: 0] += 1
        }
        
        return roasterCounts.max(by: { $0.value < $1.value })?.key
    }
    
    // 按排序选项对组内豆子进行排序
    private func sortBeansWithinGroup(_ beans: [CoffeeBean], by sortOption: BeanSortOption) -> [CoffeeBean] {
        switch sortOption {
        case .time(let ascending):
            return beans.sorted { bean1, bean2 in
                if ascending {
                    return bean1.createdAt < bean2.createdAt
                } else {
                    return bean1.createdAt > bean2.createdAt
                }
            }
        case .rating(let ascending):
            return beans.sorted { bean1, bean2 in
                let rating1 = bean1.avgRating ?? Double(bean1.restPeriodProgress ?? 0)
                let rating2 = bean2.avgRating ?? Double(bean2.restPeriodProgress ?? 0)
                
                if ascending {
                    return rating1 < rating2
                } else {
                    return rating1 > rating2
                }
            }
        case .roaster(let ascending):
            return beans.sorted { bean1, bean2 in
                if ascending {
                    return bean1.roaster < bean2.roaster
                } else {
                    return bean1.roaster > bean2.roaster
                }
            }
        }
    }
    
    // 按评分分组咖啡豆
    private func groupBeansByRating(_ beans: [CoffeeBean]) -> [BeanGroup] {
        let groups = [
            (name: "♥️♥️♥️♥️♥️ (8.1-10分)", range: 8.0...10.0, order: 5),
            (name: "♥️♥️♥️♥️ (6.1-8分)", range: 6.0...8.0, order: 4),
            (name: "♥️♥️♥️ (4.1-6分)", range: 4.0...6.0, order: 3),
            (name: "♥️♥️ (2.1-4分)", range: 2.0...4.0, order: 2),
            (name: "♥️ (0-2分)", range: 0.0...2.0, order: 1),
            (name: "未评分", range: -1.0...(-1.0), order: 0)  // 特殊范围用于未评分豆子
        ]
        
        var result: [BeanGroup] = []
        
        #if DEBUG
        print("📊 按评分分组，共 \(beans.count) 个咖啡豆")
        #endif
        
        for group in groups {
            if group.name == "未评分" {
                // 筛选出没有评分的豆子
                let noRatingBeans = beans.filter { bean in
                    return bean.avgRating == nil && bean.restPeriodProgress == nil
                }
                
                if !noRatingBeans.isEmpty {
                    #if DEBUG
                    print("📊 '未评分'组: \(noRatingBeans.count) 个咖啡豆")
                    #endif
                    
                    // 未评分组内部也需要排序，但使用创建时间或其他非评分字段
                    var sortedBeans: [CoffeeBean]
                    switch filter.sortBy {
                    case .rating:
                        // 当主排序是评分时，未评分组内使用时间排序
                        sortedBeans = noRatingBeans.sorted { bean1, bean2 in
                            if filter.sortBy.isAscending {
                                return bean1.createdAt < bean2.createdAt
                            } else {
                                return bean1.createdAt > bean2.createdAt
                            }
                        }
                    default:
                        // 其他情况使用选定的排序
                        sortedBeans = sortBeansWithinGroup(noRatingBeans, by: filter.sortBy)
                    }
                    result.append(BeanGroup(name: group.name, beans: sortedBeans, order: group.order))
                }
            } else {
                let filteredBeans = beans.filter { bean in
                    // 优先使用avgRating，如果存在
                    if let avgRating = bean.avgRating {
                        return group.range.contains(avgRating)
                    }
                    // 如果没有avgRating，尝试使用restPeriodProgress
                    else if let rating = bean.restPeriodProgress {
                        return group.range.contains(Double(rating))
                    }
                    return false
                }
                
                if !filteredBeans.isEmpty {
                    #if DEBUG
                    print("📊 '\(group.name)'组: \(filteredBeans.count) 个咖啡豆")
                    #endif
                    
                    // 在评分分组内部再次按评分排序
                    let sortedBeans: [CoffeeBean]
                    if filter.sortBy.sortKey == "rating" {
                        // 评分排序
                        sortedBeans = filteredBeans.sorted { bean1, bean2 in
                            let rating1 = bean1.avgRating ?? Double(bean1.restPeriodProgress ?? 0)
                            let rating2 = bean2.avgRating ?? Double(bean2.restPeriodProgress ?? 0)
                            
                            if filter.sortBy.isAscending {
                                return rating1 < rating2
                            } else {
                                return rating1 > rating2
                            }
                        }
                    } else {
                        // 其他排序方式
                        sortedBeans = sortBeansWithinGroup(filteredBeans, by: filter.sortBy)
                    }
                    
                    result.append(BeanGroup(name: group.name, beans: sortedBeans, order: group.order))
                }
            }
        }
        
        // 对分组进行排序
        let sortedResult: [BeanGroup]
        if filter.sortBy.sortKey == "rating" {
            if filter.sortBy.isAscending {
                // 评分升序：顺序为 未评分->1星->2星->...->5星
                sortedResult = result.sorted { $0.order < $1.order }
                #if DEBUG
                print("📊 评分升序分组顺序: \(sortedResult.map { $0.name }.joined(separator: ", "))")
                #endif
            } else {
                // 评分降序：顺序为 5星->4星->...->1星->未评分
                sortedResult = result.sorted { $0.order > $1.order }
                #if DEBUG
                print("📊 评分降序分组顺序: \(sortedResult.map { $0.name }.joined(separator: ", "))")
                #endif
            }
        } else {
            // 非评分排序时，保持固定顺序（评分降序）
            sortedResult = result.sorted { $0.order > $1.order }
            #if DEBUG
            print("📊 非评分排序分组顺序: \(sortedResult.map { $0.name }.joined(separator: ", "))")
            #endif
        }
        
        return sortedResult
    }
    
    // 应用筛选
    func applyFilter() {
        #if DEBUG
        print("🔄 应用筛选条件 - 排序方式: \(filter.sortBy.rawValue)")
        #endif
        
        // 根据排序方式自动切换分组类型
        if filter.sortBy.sortKey == "rating" {
            #if DEBUG
            print("🔄 检测到评分排序，自动切换到评分分组")
            #endif
            groupType = .rating
        } else if filter.sortBy.sortKey == "time" {
            #if DEBUG
            print("🔄 检测到时间排序，自动切换到月份分组")
            #endif
            groupType = .month
        } else if filter.sortBy.sortKey == "roaster" {
            #if DEBUG
            print("🔄 检测到豆商排序，自动切换到豆商分组")
            #endif
            groupType = .roaster
        }
        
        // 使用原始数据应用筛选，而不是使用已筛选过的数据
        let filteredBeans = applyFilters(coffeeBeans)
        
        // 分开活跃和归档的豆子
        let active = filteredBeans.filter { !$0.isArchived }
        let archived = filteredBeans.filter { $0.isArchived }
        
        // 分组
        switch groupType {
        case .month:
            #if DEBUG
            print("📊 按月份分组，排序方式: \(filter.sortBy.rawValue)")
            #endif
            self.activeBeans = groupBeansByMonth(active)
            self.archivedBeans = groupBeansByMonth(archived)
        case .rating:
            #if DEBUG
            print("📊 按评分分组，排序方式: \(filter.sortBy.rawValue)")
            #endif
            self.activeBeans = groupBeansByRating(active)
            self.archivedBeans = groupBeansByRating(archived)
        case .roaster:
            #if DEBUG
            print("📊 按豆商分组，排序方式: \(filter.sortBy.rawValue)")
            #endif
            self.activeBeans = groupBeansByRoaster(active)
            self.archivedBeans = groupBeansByRoaster(archived)
        }
        
        #if DEBUG
        if !activeBeans.isEmpty {
            print("📋 活跃分组顺序: \(activeBeans.map { $0.name }.joined(separator: ", "))")
        }
        #endif
    }
    
    // 重置筛选
    func resetFilter() {
        filter.reset()
        // 使用原始数据应用筛选
        applyFilter()
    }
    
    // 切换状态筛选
    func filterByStatus(_ status: BeanStatus?) {
        filter.statusFilter = status
        applyFilter()
    }
    
    // MARK: - 咖啡豆操作
    
    // 设置/取消设置首选
    func toggleFavorite(_ bean: CoffeeBean) async {
        #if DEBUG
        print("📱 toggleFavorite 开始: ID=\(bean.id), 当前状态=\(bean.isFavorite)")
        #endif
        
        // 检查是否是要设置已归档的咖啡豆为首选
        if !bean.isFavorite && bean.isArchived {
            // 需要先取消归档
            #if DEBUG
            print("🔄 检测到已归档咖啡豆，需要先取消归档: ID=\(bean.id)")
            #endif
            
            // 先取消归档
            await unarchiveBean(bean)
            
            // 获取最新的咖啡豆数据
            let updatedBean: CoffeeBean
            if let index = coffeeBeans.firstIndex(where: { $0.id == bean.id }) {
                updatedBean = coffeeBeans[index]
            } else {
                // 如果找不到最新数据，直接使用原始咖啡豆数据
                updatedBean = bean
                #if DEBUG
                print("⚠️ 取消归档后无法找到更新的咖啡豆数据: ID=\(bean.id)")
                #endif
            }
            
            #if DEBUG
            print("✅ 成功取消归档，继续设置首选: ID=\(bean.id)")
            #endif
        }
        
        // 先更新本地缓存中的对象状态
        await MainActor.run {
            // 在本地数据集中找到对象的索引
            if let index = self.coffeeBeans.firstIndex(where: { $0.id == bean.id }) {
                // 创建一个新的对象副本，因为CoffeeBean是不可变的结构体
                let updatedBean = CoffeeBean(
                    id: bean.id,
                    name: bean.name,
                    type: bean.type,
                    typeDisplay: bean.typeDisplay,
                    roaster: bean.roaster,
                    roastLevel: bean.roastLevel,
                    roastLevelDisplay: bean.roastLevelDisplay,
                    origin: bean.origin,
                    region: bean.region,
                    finca: bean.finca,
                    variety: bean.variety,
                    process: bean.process,
                    barcode: bean.barcode,
                    notes: bean.notes,
                    bagWeight: bean.bagWeight,
                    bagRemain: bean.bagRemain,
                    purchasePrice: bean.purchasePrice,
                    roastDate: bean.roastDate,
                    createdAt: bean.createdAt,
                    deletedAt: bean.deletedAt,
                    isFavorite: !bean.isFavorite, // 切换收藏状态
                    isArchived: self.coffeeBeans[index].isArchived, // 使用最新的归档状态
                    isDeleted: bean.isDeleted,
                    isDecaf: bean.isDecaf,
                    altitudeType: bean.altitudeType,
                    altitudeSingle: bean.altitudeSingle,
                    altitudeMin: bean.altitudeMin,
                    altitudeMax: bean.altitudeMax,
                    restPeriodMin: bean.restPeriodMin,
                    restPeriodMax: bean.restPeriodMax,
                    restPeriodProgress: bean.restPeriodProgress,
                    stockStatus: bean.stockStatus,
                    avgRating: bean.avgRating,
                    tasteNotes: bean.tasteNotes,
                    blendComponents: bean.blendComponents,
                    occurrences: bean.occurrences,
                    usageCount: bean.usageCount,
                    lastUsed: bean.lastUsed,
                    daysSinceLastUse: bean.daysSinceLastUse,
                    mostUsedEquipment: bean.mostUsedEquipment,
                    remainingUses: bean.remainingUses,
                    occurrencesCount: bean.occurrencesCount,
                    avgRepurchaseInterval: bean.avgRepurchaseInterval,
                    dimensionsAvg: bean.dimensionsAvg,
                    tastingCount: bean.tastingCount,
                    uniqueFlavorTags: bean.uniqueFlavorTags,
                    flavorAccuracy: bean.flavorAccuracy
                )
                
                // 用新的对象替换原对象
                self.coffeeBeans[index] = updatedBean
                let newState = updatedBean.isFavorite
                
                #if DEBUG
                print("🔄 本地缓存已更新: ID=\(bean.id), 新状态=\(newState)")
                #endif
                
                // 重新处理数据以更新分组和列表
                self.processBeanData(self.coffeeBeans)
                
                // 强制在主线程发送通知
                DispatchQueue.main.async {
                    // 发送特定的通知，包含id和新状态
                    NotificationCenter.default.post(
                        name: NSNotification.Name("BeanStatusUpdated"),
                        object: nil,
                        userInfo: [
                            "id": bean.id,
                            "isFavorite": newState
                        ]
                    )
                    
                    // 再发一次全局类型的通知
                    NotificationCenter.default.post(
                        name: NSNotification.Name("GlobalBeanUpdated"),
                        object: nil
                    )
                }
            }
        }
        
        do {
            let endpoint = "/ios/api/beans/\(bean.id)/toggle_favorite/"
            
            // 定义一个新的响应类型，包含更新后的咖啡豆列表
            struct ToggleFavoriteResponse: Decodable {
                let success: Bool
                let message: String
                let is_favorite: Bool
                let updated_beans: [CoffeeBean]?
                let updated_at: Int?
            }
            
            // 发送请求并获取响应
            var request = URLRequest(url: URL(string: "\(APIService.shared.getBaseURL())\(endpoint)")!)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            // 检查响应头中的X-Updated-At
            if let httpResponse = response as? HTTPURLResponse {
                if let updatedAtHeader = httpResponse.value(forHTTPHeaderField: "X-Updated-At"),
                   let timestamp = Int(updatedAtHeader) {
                    UserDefaults.standard.set(timestamp, forKey: "beans_last_update")
                    self.lastUpdateTimestamp = timestamp
                    #if DEBUG
                    print("📅 从X-Updated-At头更新时间戳: \(timestamp)")
                    #endif
                }
            }
            
            let decoder = JSONDecoder()
            let apiResponse = try decoder.decode(ToggleFavoriteResponse.self, from: data)
            
            #if DEBUG
            print("🌐 服务器响应: ID=\(bean.id), 服务器状态=\(apiResponse.is_favorite)")
            #endif
            
            // 如果响应中包含updated_at但头信息中没有，也更新时间戳
            if let timestamp = apiResponse.updated_at {
                UserDefaults.standard.set(timestamp, forKey: "beans_last_update")
                self.lastUpdateTimestamp = timestamp
                #if DEBUG
                print("📅 从响应体更新时间戳: \(timestamp)")
                #endif
            }
            
            // 如果服务器返回了更新后的咖啡豆列表，直接使用
            if let updatedBeans = apiResponse.updated_beans, !updatedBeans.isEmpty {
                // 直接处理返回的bean数据
                processBeanData(updatedBeans)
                #if DEBUG
                print("✅ 成功更新本地数据，使用服务器返回的\(updatedBeans.count)个咖啡豆数据")
                #endif
                
                // 再次发送通知，使用服务器返回的实际状态
                await MainActor.run {
                    // 强制在主线程发送通知
                    DispatchQueue.main.async {
                        NotificationCenter.default.post(
                            name: NSNotification.Name("BeanStatusUpdated"),
                            object: nil,
                            userInfo: [
                                "id": bean.id,
                                "isFavorite": apiResponse.is_favorite
                            ]
                        )
                        
                        // 再发一次全局通知
                        NotificationCenter.default.post(
                            name: NSNotification.Name("GlobalBeanUpdated"),
                            object: nil
                        )
                    }
                }
            } else {
                // 如果没有返回更新数据，则重新获取
                #if DEBUG
                print("⚠️ 服务器未返回更新后的咖啡豆数据，重新获取全部数据")
                #endif
                await fetchCoffeeBeans()
                
                // 再次发送通知，使用实际状态
                await MainActor.run {
                    if let updatedBean = self.coffeeBeans.first(where: { $0.id == bean.id }) {
                        #if DEBUG
                        print("🔄 重新获取后状态: ID=\(bean.id), 状态=\(updatedBean.isFavorite)")
                        #endif
                        
                        // 强制在主线程发送通知
                        DispatchQueue.main.async {
                            NotificationCenter.default.post(
                                name: NSNotification.Name("BeanStatusUpdated"),
                                object: nil,
                                userInfo: [
                                    "id": bean.id,
                                    "isFavorite": updatedBean.isFavorite
                                ]
                            )
                            
                            // 再发一次全局通知
                            NotificationCenter.default.post(
                                name: NSNotification.Name("GlobalBeanUpdated"),
                                object: nil
                            )
                        }
                    }
                }
            }
        } catch {
            self.error = error
            #if DEBUG
            print("❌ 切换首选状态失败: \(error)")
            #endif
            
            // A. 失败时仍然尝试更新数据
            await fetchCoffeeBeans()
            
            // B. 失败时发送恢复通知，恢复原状态
            await MainActor.run {
                if let updatedBean = self.coffeeBeans.first(where: { $0.id == bean.id }) {
                    // 强制在主线程发送通知
                    DispatchQueue.main.async {
                        NotificationCenter.default.post(
                            name: NSNotification.Name("BeanStatusUpdated"),
                            object: nil,
                            userInfo: [
                                "id": bean.id,
                                "isFavorite": updatedBean.isFavorite
                            ]
                        )
                        
                        // 再发一次全局通知
                        NotificationCenter.default.post(
                            name: NSNotification.Name("GlobalBeanUpdated"),
                            object: nil
                        )
                    }
                }
            }
        }
    }
    
    // 归档咖啡豆
    func archiveBean(_ bean: CoffeeBean) async {
        do {
            // 首先检查是否需要取消收藏
            if bean.isFavorite {
                print("🔄 检测到咖啡豆是首选状态，需要先取消首选: ID=\(bean.id)")
                do {
                    // 调用切换首选的API，这会取消首选状态
                    await toggleFavorite(bean)
                    print("✅ 成功取消首选状态: ID=\(bean.id), 名称=\(bean.name)")
                } catch {
                    print("⚠️ 取消首选状态失败: \(error.localizedDescription)")
                    // 继续处理，即使取消首选失败，仍然执行归档
                }
            }
            
            let endpoint = "/ios/api/beans/\(bean.id)/archive/"
            
            // 创建请求
            var request = URLRequest(url: URL(string: "\(APIService.shared.getBaseURL())\(endpoint)")!)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            // 强制刷新
            request.cachePolicy = URLRequest.CachePolicy.reloadIgnoringLocalCacheData
            request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
            request.addValue("no-cache", forHTTPHeaderField: "Pragma")
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            // 检查X-Updated-At头
            if let httpResponse = response as? HTTPURLResponse {
                if let updatedAtHeader = httpResponse.value(forHTTPHeaderField: "X-Updated-At"),
                   let timestamp = Int(updatedAtHeader) {
                    UserDefaults.standard.set(timestamp, forKey: "beans_last_update")
                    self.lastUpdateTimestamp = timestamp
                    #if DEBUG
                    print("📅 从X-Updated-At头更新时间戳: \(timestamp)")
                    #endif
                }
            }
            
            // 解析响应
            let decoder = JSONDecoder()
            let archiveResponse = try decoder.decode(ArchiveBeanResponse.self, from: data)
            
            // 关键部分: 更新时间戳并刷新数据
            if archiveResponse.success {
                // 如果响应中包含updated_at但头信息中没有，也更新时间戳
                if let timestamp = archiveResponse.updated_at {
                    UserDefaults.standard.set(timestamp, forKey: "beans_last_update")
                    self.lastUpdateTimestamp = timestamp
                    #if DEBUG
                    print("📅 从响应体更新时间戳: \(timestamp)")
                    #endif
                }
                
                // 立即刷新数据
                await fetchCoffeeBeans(forceRefresh: true)
                
                // 触发UI更新
                await MainActor.run {
                    self.objectWillChange.send()
                }
            }
        } catch {
            print("归档失败：\(error)")
        }
    }
    
    // 取消归档咖啡豆
    func unarchiveBean(_ bean: CoffeeBean) async {
        do {
            // 首先检查是否需要取消收藏
            if bean.isFavorite {
                print("🔄 检测到咖啡豆是首选状态，需要先取消首选: ID=\(bean.id)")
                do {
                    // 调用切换首选的API，这会取消首选状态
                    await toggleFavorite(bean)
                    print("✅ 成功取消首选状态: ID=\(bean.id), 名称=\(bean.name)")
                } catch {
                    print("⚠️ 取消首选状态失败: \(error.localizedDescription)")
                    // 继续处理，即使取消首选失败，仍然执行取消归档
                }
            }
            
            let endpoint = "/ios/api/beans/\(bean.id)/unarchive/"
            
            // 创建请求
            var request = URLRequest(url: URL(string: "\(APIService.shared.getBaseURL())\(endpoint)")!)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            // 强制刷新
            request.cachePolicy = URLRequest.CachePolicy.reloadIgnoringLocalCacheData
            request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
            request.addValue("no-cache", forHTTPHeaderField: "Pragma")
            
            let (_, response) = try await URLSession.shared.data(for: request)
            
            // 检查X-Updated-At头
            if let httpResponse = response as? HTTPURLResponse {
                if let updatedAtHeader = httpResponse.value(forHTTPHeaderField: "X-Updated-At"),
                   let timestamp = Int(updatedAtHeader) {
                    UserDefaults.standard.set(timestamp, forKey: "beans_last_update")
                    self.lastUpdateTimestamp = timestamp
                    #if DEBUG
                    print("📅 从X-Updated-At头更新时间戳: \(timestamp)")
                    #endif
                }
            }
            
            // 更新本地数据
            await fetchCoffeeBeans()
        } catch {
            self.error = error
            print("取消归档咖啡豆失败: \(error)")
        }
    }
    
    // 删除咖啡豆
    func deleteBean(_ bean: CoffeeBean) async throws {
        do {
            let endpoint = "/ios/api/beans/\(bean.id)/"
            
            // 创建请求
            var request = URLRequest(url: URL(string: "\(APIService.shared.getBaseURL())\(endpoint)")!)
            request.httpMethod = "DELETE"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            // 强制刷新
            request.cachePolicy = URLRequest.CachePolicy.reloadIgnoringLocalCacheData
            request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
            request.addValue("no-cache", forHTTPHeaderField: "Pragma")
            
            let (_, response) = try await URLSession.shared.data(for: request)
            
            // 检查响应状态
            if let httpResponse = response as? HTTPURLResponse, 
               httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                // 删除成功
                
                // 检查X-Updated-At头
                if let updatedAtHeader = httpResponse.value(forHTTPHeaderField: "X-Updated-At"),
                   let timestamp = Int(updatedAtHeader) {
                    UserDefaults.standard.set(timestamp, forKey: "beans_last_update")
                    self.lastUpdateTimestamp = timestamp
                    #if DEBUG
                    print("📅 从X-Updated-At头更新时间戳: \(timestamp)")
                    #endif
                }
                
                // 更新本地数据
                await fetchCoffeeBeans()
            } else {
                throw URLError(.badServerResponse)
            }
        } catch {
            self.error = error
            print("删除咖啡豆失败: \(error)")
            throw error
        }
    }
    
    // 设置通知监听
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(checkBeanStatus),
            name: Notification.Name("CheckBeanStatus"),
            object: nil
        )
    }
    
    // 检查Bean状态的响应方法
    @objc private func checkBeanStatus(_ notification: Notification) {
        if let userInfo = notification.userInfo,
           let _ = userInfo["beanId"] as? Int {
            // 如果数据尚未加载，先加载数据
            if coffeeBeans.isEmpty && !isLoadingBeans {
                Task {
                    await fetchCoffeeBeans()
                    // 数据加载完毕后发送通知
                    NotificationCenter.default.post(
                        name: Notification.Name("BeanFavoriteStatusChanged"),
                        object: nil
                    )
                }
            }
        }
    }
    
    // 按豆商分组咖啡豆
    private func groupBeansByRoaster(_ beans: [CoffeeBean]) -> [BeanGroup] {
        let grouped = Dictionary(grouping: beans) { $0.roaster }
        
        // 按照豆商名排序
        var result = grouped.map { roaster, beans in
            // 在每个豆商内部，使用指定的排序选项
            let sortedBeans = sortBeansWithinGroup(beans, by: filter.sortBy)
            return BeanGroup(name: roaster, beans: sortedBeans)
        }
        
        // 根据排序选项决定如何排序豆商分组
        switch filter.sortBy {
        case .roaster:
            // 当按照豆商排序时，分组本身按照豆商名升序/降序排列
            if filter.sortBy.isAscending {
                result.sort { $0.name < $1.name }
            } else {
                result.sort { $0.name > $1.name }
            }
        case .rating:
            // 当按照评分排序时，分组按照平均评分排序
            result.sort { group1, group2 in
                let avgRating1 = calculateAverageRating(for: group1.beans)
                let avgRating2 = calculateAverageRating(for: group2.beans)
                
                if filter.sortBy.isAscending {
                    return avgRating1 < avgRating2
                } else {
                    return avgRating1 > avgRating2
                }
            }
        case .time:
            // 当按照时间排序时，分组按照最新豆子时间排序
            result.sort { group1, group2 in
                let latestBean1 = group1.beans.max(by: { $0.createdAt < $1.createdAt })
                let latestBean2 = group2.beans.max(by: { $0.createdAt < $1.createdAt })
                
                guard let date1 = latestBean1?.createdAt, let date2 = latestBean2?.createdAt else {
                    return false
                }
                
                if filter.sortBy.isAscending {
                    return date1 < date2
                } else {
                    return date1 > date2
                }
            }
        }
        
        return result
    }
    
    // MARK: - 新增方法
    // 处理导航到详情页面
    func viewBeanDetails(bean: CoffeeBean) {
        // 先清除之前的状态
        selectedBean = nil
        shouldNavigateToDetail = false
        
        // 清除可能存在的该豆子的UserDefaults缓存
        clearBeanCache(beanId: bean.id)
        
        // 使用Task确保状态更新被处理
        Task { @MainActor in
            // 等待一小段时间确保状态被清理
            try? await Task.sleep(nanoseconds: 50_000_000) // 50毫秒
            
            // 设置新的状态并触发导航
            print("🔍 导航到咖啡豆详情：ID=\(bean.id), 名称=\(bean.name)")
            selectedBean = bean
            shouldNavigateToDetail = true
        }
    }
    
    // 清除特定咖啡豆的缓存
    private func clearBeanCache(beanId: Int) {
        let cacheKey = "saved_bean_\(beanId)"
        if UserDefaults.standard.object(forKey: cacheKey) != nil {
            UserDefaults.standard.removeObject(forKey: cacheKey)
            print("🗑️ 已清除咖啡豆ID=\(beanId)的缓存")
        }
    }
    
    // 重置导航状态
    func resetNavigation() {
        selectedBean = nil
        shouldNavigateToDetail = false
    }
    
    // 新增方法：检查并调度咖啡豆通知
    private func checkAndScheduleBeanNotifications() {
        // 仅当当前有数据，且通知已启用时才调度通知
        if !coffeeBeans.isEmpty {
            let settings = beanNotificationService.getNotificationSettings()
            if settings.enabled {
                // 调用通知服务来安排通知
                Task {
                    beanNotificationService.scheduleBeanNotifications()
                }
            }
        }
    }
    
    // 更新bean信息时的通知处理
    func handleBeanUpdated(_ bean: CoffeeBean) {
        // 当单个豆子被更新时，更新通知
        Task {
            // 获取通知设置
            let settings = beanNotificationService.getNotificationSettings()
            if settings.enabled {
                // 如果启用了通知，为更新的bean重新安排通知
                beanNotificationService.scheduleNotificationForBean(bean, daysBefore: settings.daysBefore)
            }
        }
    }
    
    // 在删除bean时的通知处理
    func handleBeanDeleted(_ beanId: Int) {
        // 当豆子被删除时，移除相关通知
        beanNotificationService.removeBeanNotification(beanId: beanId)
    }
    
    // 当咖啡豆状态变化时（例如库存更新或使用情况变化）
    func handleBeanStatusChanged(_ beanId: Int) {
        // 找到对应的bean
        if let bean = coffeeBeans.first(where: { $0.id == beanId }) {
            // 更新通知
            handleBeanUpdated(bean)
        }
    }
    
    // 新增方法：清理过期的通知记录
    private func cleanupNotificationRecords() {
        // 获取当前所有咖啡豆ID
        let currentBeanIds = coffeeBeans.map { $0.id }
        
        // 使用通知服务清理过期记录
        beanNotificationService.cleanupNotificationRecords(currentBeanIds: currentBeanIds)
    }
    
    // 添加一个方法，允许用户手动触发特定咖啡豆的通知（可用于通知设置测试或手动重置）
    func forceNotificationForBean(_ bean: CoffeeBean) {
        // 重置该豆子的通知状态
        beanNotificationService.resetBeanNotificationStatus(beanId: bean.id)
        
        // 获取通知设置
        let settings = beanNotificationService.getNotificationSettings()
        
        // 立即触发通知
        Task {
            if settings.enabled {
                // 由于checkAndScheduleNotificationForBean是私有方法，我们使用scheduleBeanNotifications方法
                // 在重置了通知状态后，下一次scheduleBeanNotifications会为此豆子生成新通知
                beanNotificationService.scheduleBeanNotifications()
            } else {
                // 如果通知未启用，提示用户
                print("通知功能未启用，无法发送通知")
            }
        }
    }
    
    // 更新单个咖啡豆数据
    @MainActor
    func updateBean(_ updatedBean: CoffeeBean) {
        #if DEBUG
        print("🔄 更新咖啡豆数据：ID=\(updatedBean.id), 名称=\(updatedBean.name)")
        #endif
        
        // 查找并更新现有数据
        if let index = coffeeBeans.firstIndex(where: { $0.id == updatedBean.id }) {
            coffeeBeans[index] = updatedBean
            #if DEBUG
            print("✅ 成功更新咖啡豆数据：ID=\(updatedBean.id)")
            #endif
        } else {
            // 如果不存在则添加
            coffeeBeans.append(updatedBean)
            #if DEBUG
            print("➕ 添加新咖啡豆数据：ID=\(updatedBean.id)")
            #endif
        }
        
        // 重新处理分组和筛选
        processBeanData(coffeeBeans)
        
        // 如果当前选中的咖啡豆就是更新的豆子，也更新选中状态
        if let selectedBean = selectedBean, selectedBean.id == updatedBean.id {
            self.selectedBean = updatedBean
        }
        
        // 如果有缓存，也更新缓存
        updateBeanInUserDefaults(updatedBean)
    }
    
    // 更新UserDefaults中缓存的豆子数据
    private func updateBeanInUserDefaults(_ bean: CoffeeBean) {
        // 检查UserDefaults中是否有缓存的咖啡豆数据
        if UserDefaults.standard.data(forKey: "saved_bean_\(bean.id)") != nil {
            // 尝试用更新的数据替换
            let encoder = JSONEncoder()
            if let updatedData = try? encoder.encode(bean) {
                UserDefaults.standard.set(updatedData, forKey: "saved_bean_\(bean.id)")
                #if DEBUG
                print("📝 已更新UserDefaults中ID=\(bean.id)的咖啡豆缓存")
                #endif
            }
        }
    }
    
    // MARK: - 获取单个咖啡豆
    
    // 通过ID获取咖啡豆
    func getBeanById(_ id: Int) async -> CoffeeBean? {
        // 首先尝试从本地缓存查找
        if let bean = coffeeBeans.first(where: { $0.id == id }) {
            return bean
        }
        
        // 如果本地找不到，尝试从API获取
        do {
            let endpoint = "/ios/api/beans/\(id)/"
            var request = URLRequest(url: URL(string: "\(APIService.shared.getBaseURL())\(endpoint)")!)
            request.addValue("Bearer \(APIService.shared.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            // 添加缓存控制
            request.addValue("no-cache", forHTTPHeaderField: "Cache-Control")
            request.cachePolicy = .reloadIgnoringLocalCacheData
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 {
                // 定义单个咖啡豆响应格式
                struct SingleBeanResponse: Decodable {
                    let bean: CoffeeBean
                }
                
                let decoder = JSONDecoder()
                // 设置日期解码策略，以匹配服务器返回的日期格式
                decoder.dateDecodingStrategy = .custom({ decoder in
                    let container = try decoder.singleValueContainer()
                    if let dateString = try? container.decode(String.self) {
                        let formatter = DateFormatter()
                        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
                        if let date = formatter.date(from: dateString) {
                            return date
                        }
                        
                        // 尝试更多格式
                        let formats = ["yyyy-MM-dd'T'HH:mm:ssZ", "yyyy-MM-dd"]
                        for format in formats {
                            formatter.dateFormat = format
                            if let date = formatter.date(from: dateString) {
                                return date
                            }
                        }
                        return Date()
                    } else if let timestamp = try? container.decode(Double.self) {
                        return Date(timeIntervalSince1970: timestamp)
                    }
                    return Date()
                })
                
                let beanResponse = try decoder.decode(SingleBeanResponse.self, from: data)
                return beanResponse.bean
            }
        } catch {
            print("❌ 获取咖啡豆(ID: \(id))失败: \(error)")
        }
        
        return nil
    }
}

// CoffeeBean解析扩展
extension CoffeeBean {
    static func fromDictionary(_ dict: [String: Any]) throws -> CoffeeBean {
        guard let id = dict["id"] as? Int,
              let name = dict["name"] as? String,
              let type = dict["type"] as? String,
              let typeDisplay = dict["type_display"] as? String,
              let roaster = dict["roaster"] as? String,
              let roastLevel = dict["roast_level"] as? Int,
              let roastLevelDisplay = dict["roast_level_display"] as? String,
              let createdAtString = dict["created_at"] as? String,
              let isFavorite = dict["is_favorite"] as? Bool,
              let isArchived = dict["is_archived"] as? Bool,
              let isDeleted = dict["is_deleted"] as? Bool,
              let isDecaf = dict["is_decaf"] as? Bool,
              let altitudeType = dict["altitude_type"] as? String
        else {
            throw APIError.invalidResponse
        }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
        
        guard let createdAt = dateFormatter.date(from: createdAtString) else {
            throw APIError.invalidResponse
        }
        
        // 可选字段
        let origin = dict["origin"] as? String
        let region = dict["region"] as? String
        let finca = dict["finca"] as? String
        let variety = dict["variety"] as? String
        let process = dict["process"] as? String
        let barcode = dict["barcode"] as? String
        let notes = dict["notes"] as? String
        
        // 重要：API可能返回"weight"字段或"bag_weight"字段
        var bagWeight: Double?
        
        #if DEBUG
        print("🔍 解析weight/bag_weight字段")
        if let weightValue = dict["weight"] {
            print("   - API返回'weight'字段: \(weightValue)")
        }
        if let bagWeightValue = dict["bag_weight"] {
            print("   - API返回'bag_weight'字段: \(bagWeightValue)")
        }
        #endif
        
        // 优先尝试直接获取weight字段的值
        if let weightDouble = dict["weight"] as? Double {
            bagWeight = weightDouble
            #if DEBUG
            print("   ✅ 使用weight字段值(\(weightDouble))设置bagWeight")
            #endif
        } 
        // 如果无法从weight获取，则尝试从bag_weight获取
        else if let bagWeightValue = dict["bag_weight"] as? Double {
            bagWeight = bagWeightValue
            #if DEBUG
            print("   ✅ 使用bag_weight字段值(\(bagWeightValue))设置bagWeight")
            #endif
        }
        
        #if DEBUG
        if let value = bagWeight {
            print("   🎯 最终bagWeight值: \(value)g")
        } else {
            print("   ❌ 未能解析出bagWeight值，将设为nil")
        }
        #endif
        
        let bagRemain = dict["bag_remain"] as? Double
        let purchasePrice = dict["price"] as? Double
        
        #if DEBUG
        print("📦 CoffeeBean解析 id: \(id), name: \(name)")
        if let weight = bagWeight {
            print("   bagWeight: \(weight)")
        } else {
            print("   bagWeight: nil")
        }
        
        if let remain = bagRemain {
            print("   bagRemain: \(remain)")
        } else {
            print("   bagRemain: nil")
        }
        #endif
        
        var roastDate: Date?
        if let roastDateValue = dict["roast_date"] {
            #if DEBUG
            print("   检测到roast_date字段: \(roastDateValue)")
            #endif
            
            if let roastDateString = roastDateValue as? String {
                // 处理字符串格式
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd"
                roastDate = formatter.date(from: roastDateString)
                #if DEBUG
                print("   尝试从字符串格式解析: \(roastDateString)")
                #endif
            } else if let roastDateTimestamp = roastDateValue as? TimeInterval {
                // 处理时间戳格式（秒）
                roastDate = Date(timeIntervalSince1970: roastDateTimestamp)
                #if DEBUG
                print("   尝试从时间戳解析: \(roastDateTimestamp)")
                #endif
            } else if let roastDateNumber = roastDateValue as? NSNumber {
                // 处理NSNumber格式
                roastDate = Date(timeIntervalSince1970: roastDateNumber.doubleValue)
                #if DEBUG
                print("   尝试从NSNumber解析: \(roastDateNumber.doubleValue)")
                #endif
            } else if let roastDateDouble = roastDateValue as? Double {
                // 处理Double格式的时间戳
                roastDate = Date(timeIntervalSince1970: roastDateDouble)
                #if DEBUG
                print("   尝试从Double解析: \(roastDateDouble)")
                #endif
            } else {
                #if DEBUG
                print("   无法识别的roast_date类型")
                #endif
            }
            
            #if DEBUG
            if let date = roastDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                print("   转换后roastDate: \(formatter.string(from: date))")
            } else {
                print("   ⚠️ roastDate转换失败")
            }
            #endif
        }
        
        var deletedAt: Date?
        if let deletedAtString = dict["deleted_at"] as? String {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
            deletedAt = formatter.date(from: deletedAtString)
        }
        
        let altitudeSingle = dict["altitude_single"] as? Int
        let altitudeMin = dict["altitude_min"] as? Int
        let altitudeMax = dict["altitude_max"] as? Int
        
        let restPeriodMin = dict["rest_period_min"] as? Int
        let restPeriodMax = dict["rest_period_max"] as? Int
        let restPeriodProgress = dict["rest_period_progress"] as? Int
        
        let stockStatus = dict["stock_status"] as? String ?? ""
        
        // 处理blend_components字段 - 添加这部分
        var blendComponents: [BlendComponent]? = nil
        if let componentsArray = dict["blend_components"] as? [[String: Any]] {
            #if DEBUG
            print("   检测到blend_components字段，包含\(componentsArray.count)个组件")
            #endif
            
            // 解析拼配组件数组
            var parsedComponents: [BlendComponent] = []
            
            for (index, componentDict) in componentsArray.enumerated() {
                let componentId = componentDict["id"] as? Int ?? 0
                let beanId = componentDict["coffee_bean_id"] as? Int ?? 0
                let origin = componentDict["origin"] as? String
                let region = componentDict["region"] as? String
                let finca = componentDict["finca"] as? String
                let variety = componentDict["variety"] as? String
                let process = componentDict["process"] as? String
                let roastLevel = componentDict["roast_level"] as? Int ?? 0
                let roastLevelDisplay = componentDict["roast_level_display"] as? String
                let blendRatio = componentDict["blend_ratio"] as? Double ?? 0.0
                let order = componentDict["order"] as? Int ?? index + 1
                
                // 处理海拔信息
                let altitudeType = componentDict["altitude_type"] as? String ?? "UNKNOWN"
                let altitudeSingle = componentDict["altitude_single"] as? Int
                let altitudeMin = componentDict["altitude_min"] as? Int
                let altitudeMax = componentDict["altitude_max"] as? Int
                
                // 创建BlendComponent对象
                let component = BlendComponent(
                    id: componentId,
                    coffeeBeanId: beanId,
                    origin: origin,
                    region: region,
                    finca: finca,
                    variety: variety,
                    process: process,
                    roastLevel: roastLevel,
                    roastLevelDisplay: roastLevelDisplay,
                    blendRatio: blendRatio,
                    order: order,
                    altitudeType: altitudeType,
                    altitudeSingle: altitudeSingle,
                    altitudeMin: altitudeMin,
                    altitudeMax: altitudeMax
                )
                
                parsedComponents.append(component)
                
                #if DEBUG
                print("   成功解析拼配组件 #\(index+1): id=\(componentId), origin=\(origin ?? "无"), process=\(process ?? "无"), variety=\(variety ?? "无"), ratio=\(blendRatio)")
                #endif
            }
            
            if !parsedComponents.isEmpty {
                blendComponents = parsedComponents
            }
        }
        
        return CoffeeBean(
            id: id,
            name: name,
            type: type,
            typeDisplay: typeDisplay,
            roaster: roaster,
            roastLevel: roastLevel,
            roastLevelDisplay: roastLevelDisplay,
            origin: origin,
            region: region,
            finca: finca,
            variety: variety,
            process: process,
            barcode: barcode,
            notes: notes,
            bagWeight: bagWeight,
            bagRemain: bagRemain,
            purchasePrice: purchasePrice,
            roastDate: roastDate,
            createdAt: createdAt,
            deletedAt: deletedAt,
            isFavorite: isFavorite,
            isArchived: isArchived,
            isDeleted: isDeleted,
            isDecaf: isDecaf,
            altitudeType: altitudeType,
            altitudeSingle: altitudeSingle,
            altitudeMin: altitudeMin,
            altitudeMax: altitudeMax,
            restPeriodMin: restPeriodMin,
            restPeriodMax: restPeriodMax,
            restPeriodProgress: restPeriodProgress,
            stockStatus: stockStatus,
            avgRating: dict["avg_rating"] as? Double,
            tasteNotes: dict["taste_notes"] as? [String],
            blendComponents: blendComponents,
            occurrences: nil,  // 这里暂不解析occurrences，如需使用可另行添加
            usageCount: dict["usage_count"] as? Int,
            lastUsed: (dict["last_used"] as? Double).flatMap { Date(timeIntervalSince1970: $0) },
            daysSinceLastUse: dict["days_since_last_use"] as? Int,
            mostUsedEquipment: nil, // 需要单独解析EquipmentSummary对象
            remainingUses: dict["remaining_uses"] as? Int,
            occurrencesCount: dict["occurrences_count"] as? Int,
            avgRepurchaseInterval: dict["avg_repurchase_interval"] as? Double,
            dimensionsAvg: dict["dimensions_avg"] as? [String: Double],
            tastingCount: dict["tasting_count"] as? Int,
            uniqueFlavorTags: nil, // 需要单独解析FlavorTag对象
            flavorAccuracy: dict["flavor_accuracy"] as? Int
        )
    }
} 