import SwiftUI
import UIKit

struct BeanListFilterPopover: View {
    @ObservedObject var viewModel: BeanListViewModel
    @Binding var isPresented: Bool
    @State private var isApplying: Bool = false
    @State private var tempRoastLevel: String = ""
    @State private var tempProcess: String = ""
    @State private var tempRoaster: String = ""
    @State private var tempVariety: String = ""
    @State private var tempRatingRange: String = ""
    @State private var tempSortBy: BeanSortOption = .defaultOption
    
    // 判断是否所有筛选项都是默认值
    private var isAllDefaultFilters: Bool {
        return tempRoastLevel.isEmpty &&
               tempProcess.isEmpty &&
               tempRoaster.isEmpty &&
               tempVariety.isEmpty &&
               tempRatingRange.isEmpty &&
               tempSortBy == .defaultOption
    }
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("条件")) {
                    // 烘焙度筛选
                    HStack {
                        Text("烘焙度")
                        Spacer()
                        Menu {
                            Button("全部") {
                                tempRoastLevel = ""
                            }
                            ForEach(viewModel.availableRoastLevels, id: \.0) { level, name in
                                Button(action: {
                                    tempRoastLevel = level
                                }) {
                                    HStack {
                                        Text(name)
                                        if tempRoastLevel == level {
                                            Image(systemName: "checkmark")
                                        }
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Text(selectedRoastLevelName)
                                    .foregroundColor(selectedRoastLevelName == "全部" ? Color.primaryText : Color.linkText)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(selectedRoastLevelName == "全部" ? Color.primaryText : Color.linkText)
                            }
                        }
                    }
                    
                    // 处理法筛选
                    HStack {
                        Text("处理法")
                        Spacer()
                        Menu {
                            Button("全部") {
                                tempProcess = ""
                            }
                            ForEach(viewModel.availableProcesses, id: \.0) { process, name in
                                Button(action: {
                                    tempProcess = process
                                }) {
                                    HStack {
                                        Text(name)
                                        if tempProcess == process {
                                            Image(systemName: "checkmark")
                                        }
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Text(selectedProcessName)
                                    .foregroundColor(selectedProcessName == "全部" ? Color.primaryText : Color.linkText)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(selectedProcessName == "全部" ? Color.primaryText : Color.linkText)
                            }
                        }
                    }
                    
                    // 豆商筛选
                    HStack {
                        Text("豆商")
                        Spacer()
                        Menu {
                            Button("全部") {
                                tempRoaster = ""
                            }
                            ForEach(viewModel.availableRoasters, id: \.self) { roaster in
                                Button(action: {
                                    tempRoaster = roaster
                                }) {
                                    HStack {
                                        Text(roaster)
                                        if tempRoaster == roaster {
                                            Image(systemName: "checkmark")
                                        }
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Text(tempRoaster.isEmpty ? "全部" : tempRoaster)
                                    .foregroundColor(tempRoaster.isEmpty ? Color.primaryText : Color.linkText)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(tempRoaster.isEmpty ? Color.primaryText : Color.linkText)
                            }
                        }
                    }
                    
                    // 豆种筛选
                    HStack {
                        Text("豆种")
                        Spacer()
                        Menu {
                            Button("全部") {
                                tempVariety = ""
                            }
                            ForEach(viewModel.availableVarieties, id: \.self) { variety in
                                Button(action: {
                                    tempVariety = variety
                                }) {
                                    HStack {
                                        Text(variety)
                                        if tempVariety == variety {
                                            Image(systemName: "checkmark")
                                        }
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Text(tempVariety.isEmpty ? "全部" : tempVariety)
                                    .foregroundColor(tempVariety.isEmpty ? Color.primaryText : Color.linkText)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(tempVariety.isEmpty ? Color.primaryText : Color.linkText)
                            }
                        }
                    }
                    
                    // 评分范围筛选
                    HStack {
                        Text("评分")
                        Spacer()
                        Menu {
                            Button("全部") {
                                tempRatingRange = ""
                            }
                            
                            Button("♥️♥️♥️♥️♥️ (8.1-10分)") {
                                tempRatingRange = "8-10"
                            }
                            
                            Button("♥️♥️♥️♥️ (6.1-8分)") {
                                tempRatingRange = "6-8"
                            }
                            
                            Button("♥️♥️♥️ (4.1-6分)") {
                                tempRatingRange = "4-6"
                            }
                            
                            Button("♥️♥️ (2.1-4分)") {
                                tempRatingRange = "2-4"
                            }
                            
                            Button("♥️ (0-2分)") {
                                tempRatingRange = "0-2"
                            }
                        } label: {
                            HStack {
                                Text(tempRatingRange.isEmpty ? "全部" : getRatingName(for: tempRatingRange))
                                    .foregroundColor(tempRatingRange.isEmpty ? Color.primaryText : Color.linkText)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(tempRatingRange.isEmpty ? Color.primaryText : Color.linkText)
                            }
                        }
                    }
                }
                .listRowBackground(Color.primaryBg)
                    // 排序选项
                    Section(header: Text("排序")) {
                        HStack {
                            Text("排序方式")
                            Spacer()
                            Menu {
                                ForEach(SortType.allCases) { sortType in
                                    Button(action: {
                                        // 如果已经是相同类型的排序，则切换升序/降序
                                        if tempSortBy.sortType == sortType {
                                            tempSortBy = tempSortBy.toggleDirection()
                                        } else {
                                            // 否则使用该类型的默认排序（降序）
                                            tempSortBy = BeanSortOption(type: sortType)
                                        }
                                        
                                        // 添加触觉反馈
                                        let generator = UIImpactFeedbackGenerator(style: .light)
                                        generator.impactOccurred()
                                        
                                        // 预览排序效果，不关闭弹窗
                                        previewSortChange(tempSortBy)
                                    }) {
                                        HStack {
                                            Text(tempSortBy.sortType == sortType ? tempSortBy.rawValue : sortType.rawValue)
                                            if tempSortBy.sortType == sortType {
                                                Image(systemName: "checkmark")
                                            }
                                        }
                                    }
                                }
                            } label: {
                                HStack {
                                    Text(tempSortBy.rawValue)
                                        .foregroundColor(Color.linkText)
                                    Image(systemName: "chevron.down")
                                        .font(.caption)
                                        .foregroundColor(Color.linkText)
                                }
                            }
                        }
                    }
                    .listRowBackground(Color.primaryBg)
            }
            .listStyle(InsetGroupedListStyle())
            .scrollContentBackground(.hidden)
            .onAppear {
                initializeFilters()
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("重置") {
                        resetFilters()
                    }
                    .foregroundColor(isAllDefaultFilters ? Color.gray : Color.linkText)
                    .font(.system(size: 17, weight: .regular))
                    .disabled(isAllDefaultFilters)
                    .animation(.easeInOut(duration: 0.2), value: isAllDefaultFilters)
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("筛选") {
                        applyFilters()
                    }
                    .disabled(isApplying)
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(isApplying ? .gray : Color.linkText)
                }
            }
            .background(Color.secondaryBg)
            .environment(\.locale, Locale(identifier: "zh_CN"))
        }
        .presentationDetents([.height(480), .large])
        .presentationDragIndicator(.visible)
        .interactiveDismissDisabled(isApplying)
        .background(Color.secondaryBg)
        .environment(\.locale, Locale(identifier: "zh_CN"))
    }
    
    // 已选择的烘焙度名称
    private var selectedRoastLevelName: String {
        if tempRoastLevel.isEmpty {
            return "全部"
        } else {
            if let levelTuple = viewModel.availableRoastLevels.first(where: { $0.0 == tempRoastLevel }) {
                return levelTuple.1
            } else {
                return "全部"
            }
        }
    }
    
    // 已选择的处理法名称
    private var selectedProcessName: String {
        if tempProcess.isEmpty {
            return "全部"
        } else {
            if let processTuple = viewModel.availableProcesses.first(where: { $0.0 == tempProcess }) {
                return processTuple.1
            } else {
                return "全部"
            }
        }
    }
    
    // 获取评分范围对应的名称
    private func getRatingName(for ratingRange: String) -> String {
        switch ratingRange {
        case "8-10": return "♥️♥️♥️♥️♥️ (8.1-10分)"
        case "6-8": return "♥️♥️♥️♥️ (6.1-8分)"
        case "4-6": return "♥️♥️♥️ (4.1-6分)"
        case "2-4": return "♥️♥️ (2.1-4分)"
        case "0-2": return "♥️ (0-2分)"
        default: return "全部"
        }
    }
    
    // 初始化筛选值
    private func initializeFilters() {
        tempRoastLevel = viewModel.filter.roastLevel ?? ""
        tempProcess = viewModel.filter.process ?? ""
        tempRoaster = viewModel.filter.roaster ?? ""
        tempVariety = viewModel.filter.variety ?? ""
        tempSortBy = viewModel.filter.sortBy
        
        // 设置评分范围
        if let range = viewModel.filter.ratingRange {
            print("🔄 初始化评分筛选值，当前范围: \(range)")
            
            // 检查上下限，将ClosedRange转化为字符串
            if range.lowerBound >= 8.0 || range.upperBound == Double.infinity {
                tempRatingRange = "8-10"
                print("   设置为五星(8-10)")
            } else if range.lowerBound >= 6.0 && range.upperBound <= 8.0 {
                tempRatingRange = "6-8"
                print("   设置为四星(6-8)")
            } else if range.lowerBound >= 4.0 && range.upperBound <= 6.0 {
                tempRatingRange = "4-6"
                print("   设置为三星(4-6)")
            } else if range.lowerBound >= 2.0 && range.upperBound <= 4.0 {
                tempRatingRange = "2-4"
                print("   设置为两星(2-4)")
            } else if range.lowerBound >= 0.0 && range.upperBound <= 2.0 {
                tempRatingRange = "0-2"
                print("   设置为一星(0-2)")
            } else {
                tempRatingRange = ""
                print("   无法匹配评分范围，设置为全部")
            }
        } else {
            tempRatingRange = ""
            print("🔄 没有评分筛选值，设置为全部")
        }
        
        // 在初始化完成后强制触发UI更新，确保按钮状态正确
        _ = isAllDefaultFilters
    }
    
    // 重置所有筛选条件
    private func resetFilters() {
        // 添加轻微的触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()
        
        // 清除所有筛选条件
        withAnimation(.easeInOut(duration: 0.2)) {
            tempRoastLevel = ""
            tempProcess = ""
            tempRoaster = ""
            tempVariety = ""
            tempRatingRange = ""
            tempSortBy = .defaultOption
        }
    }
    
    // 应用筛选条件
    private func applyFilters() {
        guard !isApplying else { return }
        
        // 设置正在应用状态，防止多次点击
        isApplying = true
        
        // 更新筛选条件
        viewModel.filter.roastLevel = tempRoastLevel.isEmpty ? nil : tempRoastLevel
        viewModel.filter.process = tempProcess.isEmpty ? nil : tempProcess
        viewModel.filter.roaster = tempRoaster.isEmpty ? nil : tempRoaster
        viewModel.filter.variety = tempVariety.isEmpty ? nil : tempVariety
        viewModel.filter.sortBy = tempSortBy
        
        // 设置评分范围
        switch tempRatingRange {
        case "8-10":
            viewModel.filter.ratingRange = 8.0...Double.infinity  // rating >= 8
            print("⭐️ 设置评分筛选: 8-10, 范围: \(String(describing: viewModel.filter.ratingRange))")
        case "6-8":
            viewModel.filter.ratingRange = 6.0...8.0  // 6 <= rating <= 8
            print("⭐️ 设置评分筛选: 6-8, 范围: \(String(describing: viewModel.filter.ratingRange))")
        case "4-6":
            viewModel.filter.ratingRange = 4.0...6.0  // 4 <= rating <= 6
            print("⭐️ 设置评分筛选: 4-6, 范围: \(String(describing: viewModel.filter.ratingRange))")
        case "2-4":
            viewModel.filter.ratingRange = 2.0...4.0  // 2 <= rating <= 4
            print("⭐️ 设置评分筛选: 2-4, 范围: \(String(describing: viewModel.filter.ratingRange))")
        case "0-2":
            viewModel.filter.ratingRange = 0.0...2.0  // 0 <= rating <= 2
            print("⭐️ 设置评分筛选: 0-2, 范围: \(String(describing: viewModel.filter.ratingRange))")
        default:
            viewModel.filter.ratingRange = nil
            print("⭐️ 清除评分筛选")
        }
        
        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()
        
        // 使用与系统一致的动画参数平滑关闭视图
        withAnimation(.spring(response: 0.35, dampingFraction: 1, blendDuration: 0)) {
            isPresented = false
        }
        
        // 在关闭动画完成后应用筛选条件
        Task {
            try? await Task.sleep(nanoseconds: 350_000_000) // 等待350毫秒，让关闭动画完成
            print("🔍 开始应用筛选条件")
            viewModel.applyFilter()
            print("✅ 筛选条件已应用")
            isApplying = false
        }
    }
    
    // 添加预览排序效果的函数
    private func previewSortChange(_ option: BeanSortOption) {
        // 临时应用排序选项，但不保存其他筛选条件的更改
        let previousOption = viewModel.filter.sortBy
        viewModel.filter.sortBy = option
        
        // 添加额外的振动反馈，表示正在预览效果
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
        
        // 更新UI显示
        viewModel.applyFilter()
        
        print("🔍 预览排序变更: \(previousOption.rawValue) -> \(option.rawValue)")
    }
}

#if DEBUG
struct BeanListFilterPopover_Previews: PreviewProvider {
    static var previews: some View {
        BeanListFilterPopover(
            viewModel: BeanListViewModel(),
            isPresented: .constant(true)
        )
    }
}
#endif