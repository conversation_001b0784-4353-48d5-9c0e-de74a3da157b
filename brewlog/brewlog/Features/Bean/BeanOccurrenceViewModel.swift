import Foundation
import Combine

// MARK: - 响应数据结构
struct OccurrenceResponse: Codable {
    let success: Bool
    let message: String
    let bean: CoffeeBean
    let occurrence: BeanOccurrence?
    let updatedAt: Int?
    
    enum CodingKeys: String, CodingKey {
        case success
        case message
        case bean
        case occurrence
        case updatedAt = "updated_at"
    }
}

// MARK: - 表单数据结构
struct OccurrenceFormData {
    var bagWeight: String?
    var bagRemain: String?
    var purchasePrice: String?
    var roastDate: Date?
    var createdAt: Date?
    var restPeriodMin: String?
    var restPeriodMax: String?
}

// MARK: - APIService扩展 - 添加缓存控制功能
extension APIService {
    func clearCache(for endpoint: String) {
        // 清除特定请求的缓存
        if let url = URL(string: APIService.environment.baseURL + endpoint) {
            let config = URLSessionConfiguration.default
            config.urlCache?.removeCachedResponse(for: URLRequest(url: url))
            
            // 输出日志
            print("🧹 清除API缓存: \(url.absoluteString)")
        }
    }
}

class BeanOccurrenceViewModel: ObservableObject {
    private let apiService = APIService.shared
    private var cancellables = Set<AnyCancellable>()
    
    @Published var isLoading = false
    @Published var error: Error?
    @Published var occurrences: [BeanOccurrence] = []
    @Published var selectedBean: CoffeeBean?
    @Published var updatedBean: CoffeeBean?
    
    // UI状态
    @Published var showAddSheet = false
    @Published var showEditSheet = false
    @Published var deletePrompt = false
    @Published var selectedOccurrence: BeanOccurrence?
    @Published var errorMessage: String?
    
    // 表单数据
    @Published var formData = OccurrenceFormData()
    
    // API请求数据
    var createData: [String: Any]?
    var updateData: [String: Any]?
    
    // 创建新回购记录
    func createOccurrence(for bean: CoffeeBean) async {
        // 首先检查表单是否有效
        if !isFormValid() {
            await MainActor.run {
                self.error = APIError.validationError("表单数据无效，请检查所有输入。", [:])
                self.isLoading = false
            }
            return
        }
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            // 检查咖啡豆是否已归档，如果是，则先取消归档
            if bean.isArchived {
                // 调用取消归档API
                print("🔄 检测到咖啡豆已归档，先执行取消归档操作")
                do {
                    let unarchiveEndpoint = "/ios/api/beans/\(bean.id)/unarchive/"
                    let _: APIEmptyResponse = try await apiService.post(unarchiveEndpoint, body: [:])
                    print("✅ 成功取消归档咖啡豆: ID=\(bean.id), 名称=\(bean.name)")
                } catch {
                    print("❌ 取消归档失败: \(error.localizedDescription)")
                    // 继续处理，即使取消归档失败，仍然创建回购记录
                    // 服务端也会自动处理这种情况
                }
            }
            
            // 转换表单字段，处理可能的空值
            let bagWeightValue = Double(formData.bagWeight?.replacingOccurrences(of: ",", with: ".") ?? "0")
            let bagRemainValue = Double(formData.bagRemain?.replacingOccurrences(of: ",", with: ".") ?? "0")
            let purchasePriceValue = Double(formData.purchasePrice?.replacingOccurrences(of: ",", with: ".") ?? "0")
            let restPeriodMinValue = formData.restPeriodMin.flatMap { Int($0) }
            let restPeriodMaxValue = formData.restPeriodMax.flatMap { Int($0) }
            
            // 获取并验证日期值
            let threadSafeRoastDate = await MainActor.run { formData.roastDate }
            let threadSafePurchaseDate = await MainActor.run { formData.createdAt ?? Date() }
            
            // 确保日期不为今天以后（未来日期）
            let today = Calendar.current.startOfDay(for: Date())
            
            // 验证烘焙日期不能在未来
            if let roastDate = threadSafeRoastDate, 
               Calendar.current.startOfDay(for: roastDate) > today {
                await MainActor.run {
                    self.error = APIError.validationError("烘焙日期不能是未来日期", ["roast_date": ["烘焙日期不能是未来日期"]])
                    self.isLoading = false
                }
                return
            }
            
            // 验证购买日期不能在未来
            if Calendar.current.startOfDay(for: threadSafePurchaseDate) > today {
                await MainActor.run {
                    self.error = APIError.validationError("购买日期不能是未来日期", ["created_at": ["购买日期不能是未来日期"]])
                    self.isLoading = false
                }
                return
            }
            
            // 调用服务端API创建回购记录 - 使用my/views.py中的add_bean_occurrence端点
            let response: OccurrenceResponse = try await apiService.post(
                "/ios/api/beans/\(bean.id)/occurrence/",
                body: [
                    "id": 0, // 添加id字段，默认为0，系统会自动生成
                    "bag_weight": bagWeightValue as Any,
                    "bag_remain": bagRemainValue as Any,
                    "purchase_price": purchasePriceValue as Any,
                    "roast_date": threadSafeRoastDate != nil ? formatDate(threadSafeRoastDate!) : NSNull(),
                    "created_at": Int(threadSafePurchaseDate.timeIntervalSince1970), // 使用时间戳代替字符串日期
                    "rest_period_min": restPeriodMinValue ?? NSNull(),
                    "rest_period_max": restPeriodMaxValue ?? NSNull()
                ]
            )
            
            // 更新本地数据
            await MainActor.run {
                // 获取更新后的数据
                self.updatedBean = response.bean
                
                // 如果bean.occurrences包含回购记录，更新本地列表
                if let freshOccurrences = response.bean.occurrences {
                    self.occurrences = freshOccurrences
                }
                
                self.isLoading = false
                self.resetForm()
                
                // 添加日志
                print("✅ 成功创建回购记录，更新本地缓存")
                print("📦 创建后回购记录数量: \(self.occurrences.count)")
                
                // 保存更新后的咖啡豆数据到UserDefaults，确保其他视图可以访问
                do {
                    let encoder = JSONEncoder()
                    let beanData = try encoder.encode(response.bean)
                    UserDefaults.standard.set(beanData, forKey: "saved_bean_\(response.bean.id)")
                    print("📝 回购操作后已将咖啡豆数据保存到UserDefaults，ID: \(response.bean.id)，名称: \(response.bean.name)")
                } catch {
                    print("❌ 编码咖啡豆数据失败: \(error.localizedDescription)")
                }
                
                // 只发送一次通知，并在主线程处理
                print("📣 发送回购记录更新通知")
                NotificationCenter.default.post(
                    name: NSNotification.Name("BeanDetailNeedsRefresh"),
                    object: response.bean,
                    userInfo: ["updatedAt": Date().timeIntervalSince1970, "beanId": response.bean.id]
                )
            }
        } catch let apiError as APIError {
            await MainActor.run {
                self.error = apiError
                // 获取错误详情
                if let apiErrorDetails = apiError.details {
                    print("创建回购记录API错误: \(apiError.message), 详情: \(apiErrorDetails)")
                } else {
                    print("创建回购记录API错误: \(apiError.message)")
                }
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.error = error
                print("创建回购记录未知错误: \(error)")
                self.isLoading = false
            }
        }
    }
    
    // 更新回购记录
    func updateOccurrence() async throws {
        // 首先检查表单是否有效
        if !isFormValid() {
            throw APIError.validationError("表单数据无效，请检查所有输入。", [:])
        }
        
        // 准备更新回购记录的数据
        prepareUpdateData()
        
        // 获取要更新的记录ID
        guard let occurrenceId = selectedOccurrence?.id, let requestBody = updateData else {
            throw NSError(domain: "BeanOccurrenceError", code: 1002, userInfo: [NSLocalizedDescriptionKey: "无效的回购记录更新"])
        }
        
        // 确保更新的ID字段是正确的
        var finalBody = requestBody
        finalBody["id"] = occurrenceId
        
        do {
            // 调用API更新回购记录，使用正确的API端点和PUT方法
            let endpoint = "/ios/api/beans/occurrence/\(occurrenceId)/"
            let response: OccurrenceResponse = try await apiService.put(endpoint, body: finalBody)
            
            // 在主线程上更新UI状态
            await MainActor.run {
                // 更新本地数据
                self.updatedBean = response.bean
                
                // 如果bean.occurrences包含回购记录，更新本地列表
                if let freshOccurrences = response.bean.occurrences {
                    self.occurrences = freshOccurrences
                    print("✅ 更新本地回购记录列表，新数量: \(freshOccurrences.count)")
                }
                
                // 重置表单数据
                resetForm()
                
                // 保存更新后的咖啡豆数据到UserDefaults
                do {
                    let encoder = JSONEncoder()
                    let beanData = try encoder.encode(response.bean)
                    UserDefaults.standard.set(beanData, forKey: "saved_bean_\(response.bean.id)")
                    print("📝 更新回购记录后已将咖啡豆数据保存到UserDefaults，ID: \(response.bean.id)")
                } catch {
                    print("❌ 编码咖啡豆数据失败: \(error.localizedDescription)")
                }
                
                // 只发送一次通知，并在主线程处理
                print("📣 发送回购记录更新通知")
                NotificationCenter.default.post(
                    name: NSNotification.Name("BeanDetailNeedsRefresh"),
                    object: response.bean,
                    userInfo: ["updatedAt": Date().timeIntervalSince1970, "beanId": response.bean.id]
                )
            }
        } catch let apiError as APIError {
            print("❌ 更新回购记录API错误: \(apiError.message)")
            throw apiError
        } catch {
            print("❌ 更新回购记录失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    // 删除回购记录
    func deleteOccurrence(occurrenceId: Int) async throws {
        do {
            // 调用API删除回购记录，使用正确的API端点和DELETE方法
            let endpoint = "/ios/api/beans/occurrence/\(occurrenceId)/delete/"
            let response: OccurrenceResponse = try await apiService.delete(endpoint)
            
            // 在主线程上更新UI状态
            await MainActor.run {
                // 更新本地数据
                self.updatedBean = response.bean
                
                // 更新回购记录列表 - 由于删除了记录，可能为空
                if let freshOccurrences = response.bean.occurrences {
                    self.occurrences = freshOccurrences
                } else {
                    self.occurrences = []
                }
                
                // 添加日志确认响应
                print("✅ 删除回购记录成功，响应: \(response.message)")
                
                // 保存更新后的咖啡豆数据到UserDefaults
                do {
                    let encoder = JSONEncoder()
                    let beanData = try encoder.encode(response.bean)
                    UserDefaults.standard.set(beanData, forKey: "saved_bean_\(response.bean.id)")
                    print("📝 删除回购记录后已将咖啡豆数据保存到UserDefaults，ID: \(response.bean.id)")
                } catch {
                    print("❌ 编码咖啡豆数据失败: \(error.localizedDescription)")
                }
                
                // 发送一个通知通知其他视图更新
                NotificationCenter.default.post(
                    name: NSNotification.Name("BeanDetailNeedsRefresh"), 
                    object: response.bean,
                    userInfo: ["updatedAt": Date().timeIntervalSince1970, "beanId": response.bean.id, "operation": "delete"]
                )
            }
        } catch {
            print("❌ 删除回购记录失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    // 加载特定咖啡豆的所有回购记录
    func loadOccurrences(for bean: CoffeeBean) {
        Task { @MainActor in
            self.isLoading = true
            self.selectedBean = bean
            
            // 优先使用updatedBean（如果有的话），因为它包含最新的回购记录数据
            if let updatedBean = self.updatedBean, updatedBean.id == bean.id {
                // 使用最新更新的bean数据
                if let occurrences = updatedBean.occurrences {
                    self.occurrences = occurrences
                    print("✅ 使用updatedBean中的回购记录，数量: \(occurrences.count)")
                } else {
                    self.occurrences = []
                    print("🔄 updatedBean中没有回购记录")
                }
            } else {
                // 使用传入的bean
                if let occurrences = bean.occurrences {
                    self.occurrences = occurrences
                    print("✅ 使用传入bean中的回购记录，数量: \(occurrences.count)")
                } else {
                    self.occurrences = []
                    print("🔄 传入bean中没有回购记录")
                }
                
                // 如果没有使用updatedBean，尝试主动获取最新数据
                if bean.occurrencesCount ?? 0 > 0 {
                    do {
                        // 使用my/views.py中的view_bean端点获取咖啡豆详情
                        let updatedBean: CoffeeBean = try await apiService.get("/ios/api/beans/\(bean.id)/")
                        
                        // 更新updatedBean
                        self.updatedBean = updatedBean
                        
                        // 更新回购记录
                        if let freshOccurrences = updatedBean.occurrences {
                            self.occurrences = freshOccurrences
                            print("✅ 已获取最新回购记录，数量: \(freshOccurrences.count)")
                        }
                    } catch {
                        print("❌ 获取最新回购记录失败: \(error.localizedDescription)")
                    }
                }
            }
            
            self.isLoading = false
        }
    }
    
    // 格式化日期为服务器API所需的格式
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
    
    // 初始化表单数据，用于创建新的回购记录
    func initializeAddForm() {
        // 重置表单数据
        resetForm()
        
        // 设置默认值 - 当前日期
        formData.createdAt = Date()
    }
    
    // 初始化表单数据，用于编辑现有回购记录
    func initializeEditForm(occurrence: BeanOccurrence) {
        // 重置表单数据
        resetForm()
        
        // 设置选中的回购记录
        selectedOccurrence = occurrence
        
        // 填充表单数据
        if let bagWeight = occurrence.bagWeight {
            formData.bagWeight = String(format: "%.1f", bagWeight)
        }
        
        if let bagRemain = occurrence.bagRemain {
            formData.bagRemain = String(format: "%.1f", bagRemain)
        }
        
        if let purchasePrice = occurrence.purchasePrice {
            formData.purchasePrice = String(format: "%.2f", purchasePrice)
        }
        
        // 日期处理
        formData.roastDate = occurrence.roastDate
        formData.createdAt = occurrence.createdAt
        
        // 养豆期
        if let restMin = occurrence.restPeriodMin {
            formData.restPeriodMin = String(restMin)
        }
        
        if let restMax = occurrence.restPeriodMax {
            formData.restPeriodMax = String(restMax)
        }
    }
    
    // 重置表单数据
    func resetForm() {
        formData = OccurrenceFormData()
        createData = nil
        updateData = nil
        selectedOccurrence = nil
        errorMessage = nil
    }
    
    // 计算回购周期的文本表示
    func formatRepurchaseInterval(timeGap: TimeInterval?) -> String {
        guard let timeGap = timeGap else { return "- -" }
        
        let days = Int(timeGap / (24 * 60 * 60))
        if days < 30 {
            return "\(days) 天"
        } else if days < 365 {
            let months = days / 30
            return "\(months) 个月"
        } else {
            let years = Double(days) / 365.0
            return String(format: "%.1f 年", years)
        }
    }
    
    /// 获取咖啡豆详情的方法
    func getBeanDetail(beanId: Int) async throws -> CoffeeBean {
        do {
            // 使用my/views.py中的view_bean端点获取咖啡豆详情
            let response: SingleBeanResponse = try await apiService.get("/ios/api/beans/\(beanId)/")
            return response.bean
        } catch {
            print("❌ 获取咖啡豆详情失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 加载咖啡豆详情
    func loadBean(beanId: Int) async throws -> CoffeeBean {
        // 使用正确的API端点获取咖啡豆详情
        do {
            // 服务器端API路径为/iosapp/views.py中的bean_detail接口
            let bean: CoffeeBean = try await apiService.get("/ios/api/beans/\(beanId)/")
            return bean
        } catch {
            print("加载咖啡豆详情失败: \(error)")
            throw error
        }
    }
    
    /// 创建回购记录
    func createOccurrence(bean: CoffeeBean) async throws {
        // 调用现有的实现方法
        await createOccurrence(for: bean)
    }
    
    // MARK: - 辅助方法
    
    /// 检查表单是否有效
    func isFormValid() -> Bool {
        // 包装规格必须有效
        guard let bagWeightStr = formData.bagWeight,
              !bagWeightStr.isEmpty,
              let bagWeight = Double(bagWeightStr.replacingOccurrences(of: ",", with: ".")),
              bagWeight > 0 else {
            return false
        }
        
        // 剩余重量必须有效
        guard let bagRemainStr = formData.bagRemain,
              !bagRemainStr.isEmpty,
              let bagRemain = Double(bagRemainStr.replacingOccurrences(of: ",", with: ".")),
              bagRemain >= 0 else {
            return false
        }
        
        // 剩余重量不能大于包装规格
        if bagRemain > bagWeight {
            return false
        }
        
        // 购买日期必须存在
        guard formData.createdAt != nil else {
            return false
        }
        
        // 如果设置了烘焙日期，验证养豆期
        if let roastDate = formData.roastDate {
            // 烘焙日期不能是未来日期
            if Calendar.current.startOfDay(for: roastDate) > Calendar.current.startOfDay(for: Date()) {
                return false
            }
            
            // 验证养豆期(如果设置)
            if let minRestStr = formData.restPeriodMin, !minRestStr.isEmpty {
                if let minRest = Int(minRestStr) {
                    if minRest < 1 || minRest > 60 {
                        return false
                    }
                } else {
                    return false
                }
            }
            
            if let maxRestStr = formData.restPeriodMax, !maxRestStr.isEmpty {
                if let maxRest = Int(maxRestStr) {
                    if maxRest < 1 || maxRest > 60 {
                        return false
                    }
                    
                    // 如果有最小值，检查最大值是否小于最小值
                    if let minRestStr = formData.restPeriodMin, 
                       let minRest = Int(minRestStr),
                       maxRest < minRest {
                        return false
                    }
                } else {
                    return false
                }
            }
        }
        
        return true
    }
    
    /// 准备创建数据
    func prepareCreateData() {
        // 转换表单字段，处理可能的空值
        let bagWeightValue = Double(formData.bagWeight?.replacingOccurrences(of: ",", with: ".") ?? "0")
        let bagRemainValue = Double(formData.bagRemain?.replacingOccurrences(of: ",", with: ".") ?? "0")
        let purchasePriceValue = Double(formData.purchasePrice?.replacingOccurrences(of: ",", with: ".") ?? "0")
        let restPeriodMinValue = formData.restPeriodMin.flatMap { Int($0) }
        let restPeriodMaxValue = formData.restPeriodMax.flatMap { Int($0) }
        
        var requestBody: [String: Any] = [
            "id": 0  // 添加默认id字段，值为0
        ]
        
        // 添加必填字段
        if let bagWeight = bagWeightValue {
            requestBody["bag_weight"] = bagWeight
        }
        
        if let bagRemain = bagRemainValue {
            requestBody["bag_remain"] = bagRemain
        }
        
        // 添加可选字段
        if let purchasePrice = purchasePriceValue {
            requestBody["purchase_price"] = purchasePrice
        }
        
        if let roastDate = formData.roastDate {
            requestBody["roast_date"] = formatDateForAPI(roastDate)
        }
        
        if let createdAt = formData.createdAt {
            // 使用时间戳格式代替日期字符串
            requestBody["created_at"] = Int(createdAt.timeIntervalSince1970)
        }
        
        if let restMin = restPeriodMinValue {
            requestBody["rest_period_min"] = restMin
        }
        
        if let restMax = restPeriodMaxValue {
            requestBody["rest_period_max"] = restMax
        }
        
        createData = requestBody
    }
    
    /// 准备更新数据
    func prepareUpdateData() {
        // 与创建数据相同的处理逻辑
        prepareCreateData()
        updateData = createData
    }
    
    /// 格式化日期为API需要的格式
    private func formatDateForAPI(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
} 