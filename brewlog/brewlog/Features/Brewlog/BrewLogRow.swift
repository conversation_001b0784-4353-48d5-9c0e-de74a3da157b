import SwiftUI
import Foundation

struct BrewLogRow: View {
    let record: BrewingRecord
    var isPressed: Bool = false  // 添加按压状态参数，默认为false
    var showExtraInfoIcons: Bool = true  // 添加控制额外信息图标显示的参数，默认为true
    var showBorderRadius: Bool = true  // 添加控制边框圆角显示的参数，默认为true
    
    // 添加可选的环境对象和主题颜色
    @EnvironmentObject private var themeManager: ThemeManager
    var themeColors: ThemeColors?
    
    // 添加状态更新ID，用于强制视图刷新
    @State private var updateID = UUID()
    
    // 计算属性：获取当前使用的主题颜色
    private var currentColors: ThemeColors {
        // 如果传入了themeColors，优先使用
        if let colors = themeColors {
            return colors
        }
        
        // 使用环境对象中的主题颜色
        return themeManager.currentThemeColors
    }
    
    // 计算属性：检查是否有额外信息图标需要显示
    private var hasExtraInfoToShow: Bool {
        // 如果不显示额外信息图标，直接返回false
        if !showExtraInfoIcons {
            return false
        }
        
        // 检查是否有可显示的额外信息
        let hasNotes = !record.notes.isEmpty
        let hasSteps = !record.steps.isEmpty
        let hasFlavorTags = !record.flavorTags.isEmpty
        let hasFlavorRadar = record.aroma > 0 || record.acidity > 0 || record.sweetness > 0 || record.body > 0 || record.aftertaste > 0
        
        // 环境数据
        let hasWaterQuality = record.waterQuality != nil && !record.waterQuality!.isEmpty
        let hasRoomTemperature = record.roomTemperature != nil && record.roomTemperature! > 0
        let hasRoomHumidity = record.roomHumidity != nil && record.roomHumidity! > 0
        let hasEnvironmentData = hasWaterQuality || hasRoomTemperature || hasRoomHumidity
        
        return hasNotes || hasSteps || hasFlavorTags || hasFlavorRadar || hasEnvironmentData
    }
    
    // 计算属性：根据是否显示额外信息图标来确定卡片高度
    private var cardHeight: CGFloat {
        return hasExtraInfoToShow && showExtraInfoIcons ? 180 : 160
    }
    
    var body: some View {
        ZStack(alignment: .bottomTrailing) {
            VStack(alignment: .leading, spacing: 10) {
                // 第一行：评分和配方名称
                HStack {
                    HStack(spacing: 4) {
                        Text(record.ratingDisplay)
                            .font(.headline)
                            .foregroundColor(currentColors.primaryTextColor)
                    }
                    
                    if let recipeName = record.recipeName {
                        Text(recipeName)
                            .font(.system(size: 18, weight: .light, design: .default))
                            .foregroundColor(currentColors.primaryTextColor)
                    }
                    
                    Spacer()
                }
                
                // 第二行：咖啡豆信息和创建时间
                HStack {
                    Text(record.coffeeBean.name)
                        .font(.headline)
                        .foregroundColor(currentColors.primaryTextColor)
                    
                    Spacer()
                    
                    Text(formatTimeAgo(record.createdAt))
                        .font(.caption2)
                        .foregroundColor(currentColors.detailTextColor)
                }
                
                // 第三行：设备名称和研磨信息（改为GitHub Badge风格）
                HStack(spacing: 10) {
                    // GitHub Badge 风格的设备名称和冲煮方法
                    BadgeView(
                        leftText: record.brewingEquipment.name,
                        rightText: record.brewingEquipment.brewMethodDisplay ?? "",
                        leftBgColor: currentColors.functionTextColor.opacity(0.2),
                        rightBgColor: currentColors.functionTextColor.opacity(0.1),
                        leftTextColor: currentColors.functionTextColor,
                        rightTextColor: currentColors.detailTextColor
                    )
                    
                    // 研磨设备和粗细信息的GitHub Badge
                    if let grindingEquipment = record.grindingEquipment {
                        BadgeView(
                            leftText: grindingEquipment.name,
                            rightText: record.grindSize,
                            leftBgColor: currentColors.functionTextColor.opacity(0.2),
                            rightBgColor: currentColors.functionTextColor.opacity(0.1),
                            leftTextColor: currentColors.functionTextColor,
                            rightTextColor: currentColors.detailTextColor
                        )
                    }
                }
                
                // 第四行：萃取时间、粉水比和水温
                HStack(spacing: 8) {
                    ParameterBadgeView(
                        icon: Image("clock.symbols"),
                        value: NumberFormatters.formatBrewingTime(Double(record.brewingTime)),
                        color: currentColors.primaryTextColor
                    )
                    
                    ParameterBadgeView(
                        icon: Image("ratio.symbols"),
                        value: NumberFormatters.formatBrewRatio(record.yieldWeight / record.doseWeight),
                        color: currentColors.primaryTextColor
                    )
                    
                    if record.waterTemperature > 0 {
                        ParameterBadgeView(
                            icon: Image("temperature.symbols"),
                            value: NumberFormatters.formatTemperature(record.waterTemperature),
                            color: currentColors.primaryTextColor
                        )
                    }
                    
                    Spacer()
                }
                
                // 第五行：额外信息图标（如果有且需要显示）
                if hasExtraInfoToShow {
                    HStack(spacing: 16) {
                        // 笔记
                        if !record.notes.isEmpty {
                            Image("note.symbols")
                                .foregroundColor(currentColors.linkTextColor)
                                .font(.callout)
                        }
                        
                        // 步骤
                        if !record.steps.isEmpty {
                            Image("steps.symbols")
                                .foregroundColor(currentColors.linkTextColor)
                                .font(.callout)
                        }
                        
                        // 风味标签
                        if !record.flavorTags.isEmpty {
                            Image("flavor.symbols")
                                .foregroundColor(currentColors.linkTextColor)
                                .font(.callout)
                        }
                        
                        // 风味雷达图
                        if record.aroma > 0 || record.acidity > 0 || record.sweetness > 0 || record.body > 0 || record.aftertaste > 0 {
                            Image("radar.symbols")
                                .foregroundColor(currentColors.linkTextColor)
                                .font(.callout)
                        }
                        
                        // 环境数据
                        let hasWaterQuality = record.waterQuality != nil && !record.waterQuality!.isEmpty
                        let hasRoomTemperature = record.roomTemperature != nil && record.roomTemperature! > 0
                        let hasRoomHumidity = record.roomHumidity != nil && record.roomHumidity! > 0
                        
                        if hasWaterQuality || hasRoomTemperature || hasRoomHumidity {
                            Image("environment.symbols")
                                .foregroundColor(currentColors.linkTextColor)
                                .font(.callout)
                        }
                        
                        Spacer()
                    }
                }
            }
            .padding(.vertical, 16)
            .padding(.horizontal, 14)
            
            // 右下角显示咖啡粉重量
            Text(NumberFormatters.formatWeight(record.doseWeight))
                .font(.system(size: 24, weight: .bold, design: .rounded))
                .foregroundColor(currentColors.detailTextColor.opacity(0.2))
                .padding(.trailing, 8)
                .padding(.bottom, 4)
        }
        // 使用计算属性动态设置卡片高度
        .frame(height: cardHeight)
        // 根据showBorderRadius参数使用不同的形状
        .modifier(CardStyle(
            isPressed: isPressed, 
            showBorderRadius: showBorderRadius,
            primaryBgColor: currentColors.primaryBgColor,
            focusBgColor: currentColors.focusBgColor,
            primaryTextColor: currentColors.primaryTextColor
        ))
        // 监听主题变化通知，强制视图刷新
        .id(updateID)
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ThemeDidChange"))) { _ in
            updateID = UUID() // 强制视图刷新
        }
    }
    
    // 卡片样式修饰器
    private struct CardStyle: ViewModifier {
        let isPressed: Bool
        let showBorderRadius: Bool
        let primaryBgColor: Color
        let focusBgColor: Color
        let primaryTextColor: Color
        
        func body(content: Content) -> some View {
            if showBorderRadius {
                content
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(isPressed ? focusBgColor : primaryBgColor)
                            // 移除可能导致闪烁的覆盖层
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(primaryTextColor.opacity(0.05), lineWidth: 1)
                    )
            } else {
                content
                    .background(
                        Rectangle()
                            .fill(isPressed ? focusBgColor : primaryBgColor)
                            // 移除可能导致闪烁的覆盖层
                    )
                    .clipShape(Rectangle())
                    .overlay(
                        Rectangle()
                            .stroke(primaryTextColor.opacity(0.05), lineWidth: 1)
                    )
            }
        }
    }
    
    // 处理不同类型的时间输入
    private func formatTimeAgoFromAny(_ timeValue: Any) -> String {
        if let date = timeValue as? Date {
            return formatTimeAgo(date)
        } else if let timestamp = timeValue as? TimeInterval {
            return formatTimeAgo(Date(timeIntervalSince1970: timestamp))
        } else if let timestampInt = timeValue as? Int {
            return formatTimeAgo(Date(timeIntervalSince1970: TimeInterval(timestampInt)))
        } else if let timestampDouble = timeValue as? Double {
            return formatTimeAgo(Date(timeIntervalSince1970: timestampDouble))
        } else if let timestampString = timeValue as? String, let timestampDouble = Double(timestampString) {
            return formatTimeAgo(Date(timeIntervalSince1970: timestampDouble))
        }
        return "未知时间"
    }
    
    private func formatTimeAgo(_ date: Date) -> String {
        let now = Date()
        let calendar = Calendar.current
        
        // 日期比较时只考虑年月日，忽略时分秒
        let dateOnly = calendar.startOfDay(for: date)
        let nowOnly = calendar.startOfDay(for: now)
        
        // 先计算天数差异（基于日期部分）
        let dayComponents = calendar.dateComponents([.day], from: dateOnly, to: nowOnly)
        let days = dayComponents.day ?? 0
        
        // 若是同一天，则使用完整时间差计算小时、分钟、秒
        if days == 0 {
            let components = calendar.dateComponents([.hour, .minute, .second], from: date, to: now)
            let hours = components.hour ?? 0
            let minutes = components.minute ?? 0
            let seconds = components.second ?? 0
            
            // 判断时间先后顺序
            if date > now { // 未来时间
                if hours > 0 {
                    return "\(hours)小时后"
                } else if minutes > 0 {
                    return "\(minutes)分钟后"
                } else if seconds > 0 {
                    return "\(seconds)秒后"
                } else {
                    return "刚刚"
                }
            } else { // 过去时间
                if hours > 0 {
                    return "\(hours)小时前"
                } else if minutes > 0 {
                    return "\(minutes)分钟前"
                } else if seconds > 0 {
                    return "\(seconds)秒前"
                } else {
                    return "刚刚"
                }
            }
        } else { // 不同天
            // 判断时间先后顺序
            if dateOnly > nowOnly { // 未来日期
                if days < 7 {
                    return "\(days)天后"
                } else {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd HH:mm"
                    return formatter.string(from: date)
                }
            } else { // 过去日期
                if days < 7 {
                    return "\(days)天前"
                } else {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd HH:mm"
                    return formatter.string(from: date)
                }
            }
        }
    }
}

// GitHub Badge 风格的视图组件
private struct BadgeView: View {
    let leftText: String
    let rightText: String
    let leftBgColor: Color
    let rightBgColor: Color
    let leftTextColor: Color
    let rightTextColor: Color
    
    var body: some View {
        HStack(spacing: 0) {
            // 左侧部分
            Text(leftText)
                .font(.caption)
                .padding(.vertical, 4)
                .padding(.horizontal, 6)
                .background(leftBgColor)
                .foregroundColor(leftTextColor)
                .clipShape(
                    // 左侧部分只有左边是圆角
                    .rect(
                        topLeadingRadius: 4,
                        bottomLeadingRadius: 4,
                        bottomTrailingRadius: 0,
                        topTrailingRadius: 0
                    )
                )
            
            // 右侧部分
            Text(rightText)
                .font(.caption)
                .padding(.vertical, 4)
                .padding(.horizontal, 6)
                .background(rightBgColor)
                .foregroundColor(rightTextColor)
                .clipShape(
                    // 右侧部分只有右边是圆角
                    .rect(
                        topLeadingRadius: 0,
                        bottomLeadingRadius: 0,
                        bottomTrailingRadius: 4,
                        topTrailingRadius: 4
                    )
                )
        }
        .overlay(
            // 添加极细边框增强视觉效果
            RoundedRectangle(cornerRadius: 4)
                .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
        )
    }
}

private struct ParameterView: View {
    let icon: Image
    let value: String
    let secondaryColor: Color
    let primaryColor: Color
    
    var body: some View {
        HStack(spacing: 4) {
            icon
                .font(.caption)
                .foregroundColor(secondaryColor)
            
            Text(value)
                .font(.subheadline)
                .foregroundColor(primaryColor)
        }
    }
}

private struct ParameterBadgeView: View {
    let icon: Image
    let value: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 4) {
            icon
                .font(.caption)
            Text(value)
                .font(.caption)
        }
        .foregroundColor(color)
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Color.navbarBg
        )
        .clipShape(Capsule())
    }
}

#if DEBUG
struct BrewLogRow_Previews: PreviewProvider {
    static var previews: some View {
        BrewLogRow(record: BrewingRecord.preview)
            .environmentObject(ThemeManager.shared)
            .previewLayout(.sizeThatFits)
            .padding()
    }
}
#endif
