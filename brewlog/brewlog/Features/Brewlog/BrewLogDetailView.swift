import SwiftUI
import AudioToolbox // 添加这一行以支持系统音效
import Photos

// 创建一个ObservableObject来管理记录状态
class BrewLogDetailViewModel: ObservableObject {
    @Published var record: BrewingRecord

    init(record: BrewingRecord) {
        self.record = record
    }

    func updateRecord(_ newRecord: BrewingRecord) {
        record = newRecord
    }
}

struct BrewLogDetailView: View {
    let initialRecord: BrewingRecord
    @StateObject private var recordViewModel: BrewLogDetailViewModel
    @State private var refreshTrigger = false // 添加刷新触发器
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.displayScale) var displayScale
    @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听
    @EnvironmentObject var authService: AuthService
    @State private var showingDeleteAlert = false
    @State private var showingEditView = false
    @State private var showingStepGuide = false
    @State private var currentStep = 0
    @State private var isGeneratingImage = false
    @State private var showingOverlapInfo = false  // 添加状态变量
    @StateObject private var viewModel = BrewLogViewModel()
    // 添加主题管理器的观察
    @ObservedObject private var themeManager = ThemeManager.shared

    // 初始化方法
    init(record: BrewingRecord) {
        self.initialRecord = record
        self._recordViewModel = StateObject(wrappedValue: BrewLogDetailViewModel(record: record))
    }

    // 在外部创建滚动视图内容以便于截图
    var detailContent: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 顶部标题区域 - 用户名称和日期在左侧，温湿度印章在右侧
            VStack(alignment: .leading, spacing: 0) {
                Text(AttributedString(getUserNameAndDateWithTempHumidity()))
                    .font(.subheadline)
                    .lineLimit(nil)
                    .fixedSize(horizontal: false, vertical: true)
                    .padding(.bottom, 8)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // 冲煮信息标签
                FlowLayout(alignment: .leading, spacing: 8) {
                    BrewMethodBadge(method: recordViewModel.record.brewingEquipment.brewMethod ?? "其他")

                    Badge {
                        Image("clock.symbols")
                        Text(NumberFormatters.formatBrewingTime(Double(recordViewModel.record.brewingTime)))
                    }
                    .modifier(CustomBadgeBackground(color: Color.navbarBg))

                    Badge {
                        Image("ratio.symbols")
                        Text(NumberFormatters.formatBrewRatio(recordViewModel.record.yieldWeight / recordViewModel.record.doseWeight))
                    }
                    .modifier(CustomBadgeBackground(color: Color.navbarBg))
                }
                .padding(.top, 4)
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // 评价与备注
            HStack(alignment: .bottom, spacing: 4) {
                ZStack {
                    Text(recordViewModel.record.ratingDisplay)
                        .font(.title2)
                }

                BubbleView(notesEmpty: recordViewModel.record.notes.isEmpty) {
                    if !recordViewModel.record.notes.isEmpty {
                        Text(recordViewModel.record.notes)
                            .fixedSize(horizontal: false, vertical: true)
                    } else {
                        Text(getRatingText(recordViewModel.record.ratingLevel))
                            .foregroundColor(Color.primaryText)
                    }
                }
            }
            .padding(.bottom, 4)

            // 关键参数卡片
            HStack(spacing: 12) {
                KeyParamCard(icon: "grinder", title: recordViewModel.record.grindSize == "-" ? "-" : "#\(recordViewModel.record.grindSize)", subtitle: recordViewModel.record.waterTemperature > 0 ? " " : nil)

                KeyParamCard(icon: "bean", title: NumberFormatters.formatWeight(recordViewModel.record.doseWeight), subtitle: recordViewModel.record.waterTemperature > 0 ? " " : nil)

                KeyParamCard(icon: "droplet", title: NumberFormatters.formatWeight(recordViewModel.record.yieldWeight), subtitle: recordViewModel.record.waterTemperature > 0 ? NumberFormatters.formatTemperature(recordViewModel.record.waterTemperature) + "°C" : nil)
            }
            .padding(.vertical, 8)

            // 详细信息列表
            VStack(alignment: .leading, spacing: 10) {
                DetailItem(label: "用豆", value: getBeanDetailText())

                if let waterQuality = recordViewModel.record.waterQuality, !waterQuality.isEmpty {
                    DetailItem(label: "用水", value: waterQuality)
                }

                DetailItem(label: "器具", value: getEquipmentDetailText())

                // 显示小工具信息
                if let gadgets = recordViewModel.record.gadgets, !gadgets.isEmpty {
                    VStack(alignment: .leading, spacing: 6) {
                        HStack(alignment: .top, spacing: 12) {
                            Text("小物")
                                .font(.system(size: 15))
                                .frame(width: 40, alignment: .leading)
                                .foregroundColor(.primaryText.opacity(0.6))

                            VStack(alignment: .leading, spacing: 6) {
                                ForEach(gadgets, id: \.id) { gadget in
                                    Text(getGadgetName(gadget))
                                        .font(.system(size: 15))
                                }
                            }
                        }
                    }
                }
            }
            .font(.system(size: 15))
            .padding(.vertical, 8)

            // 冲煮步骤
            if !recordViewModel.record.steps.isEmpty {
                SectionDivider(title: "详细步骤")

                // 步骤列表
                VStack(spacing: 16) {
                    ForEach(Array(recordViewModel.record.steps.enumerated()), id: \.offset) { index, step in
                        HStack(alignment: .top, spacing: 12) {
                            Text("\(index + 1)")
                                .font(.system(size: 32, weight: .light))
                                .foregroundColor(.primaryText.opacity(0.3))
                                .frame(width: 30)

                            VStack(alignment: .leading, spacing: 8) {
                                // 使用AdaptiveHighlightedText显示高亮效果
                                AdaptiveHighlightedText(
                                    step.text,
                                    font: .subheadline,
                                    foregroundColor: Color.primaryText,
                                    textAlignment: .leading
                                )
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .lineSpacing(2)
                                .lineLimit(nil)
                                .multilineTextAlignment(.leading)
                                .fixedSize(horizontal: false, vertical: true)

                                if let timer = step.timer, !timer.isEmpty {
                                    Text("⏱ \(timer)")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText.opacity(0.5))
                                }
                            }
                        }
                    }
                }

                // 步骤指引按钮
                HStack {
                    Spacer()
                    Button(action: { showingStepGuide = true }) {
                        HStack {
                            Image(systemName: "arrow.triangle.turn.up.right.diamond")
                            Text("步骤指引")
                        }
                        .font(.subheadline)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .foregroundColor(.blue)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.blue, lineWidth: 1)
                        )
                    }
                    Spacer()
                }
                .padding(.top, 8)
            }

            // 品鉴笔记
            if !recordViewModel.record.flavorTags.isEmpty || recordViewModel.record.aroma > 0 || recordViewModel.record.acidity > 0 || recordViewModel.record.sweetness > 0 || recordViewModel.record.body > 0 || recordViewModel.record.aftertaste > 0 {
                SectionDivider(title: "品鉴笔记")

                VStack(spacing: 16) {
                    if !recordViewModel.record.flavorTags.isEmpty {
                        RecordFlavorTagsView(
                            tags: recordViewModel.record.flavorTags,
                            overlapPercentage: recordViewModel.record.tagOverlap,
                            showingOverlapInfo: $showingOverlapInfo
                        )
                    }

                    if recordViewModel.record.aroma > 0 || recordViewModel.record.acidity > 0 || recordViewModel.record.sweetness > 0 || recordViewModel.record.body > 0 || recordViewModel.record.aftertaste > 0 {
                        RecordFlavorDimensionChart(
                            aroma: recordViewModel.record.aroma,
                            acidity: recordViewModel.record.acidity,
                            sweetness: recordViewModel.record.sweetness,
                            aftertaste: recordViewModel.record.aftertaste,
                            bodyValue: recordViewModel.record.body
                        )
                        .frame(height: 240)
                    }
                }
                .padding(.vertical, 8)
            }
        }
        .padding(.horizontal)
        .padding(.bottom, 24)
        .background(Color.primaryBg)
        .foregroundColor(.primaryText)
    }

    // 专用于生成截图的内容，包含水印
    var shareContent: some View {
        ZStack {
            // 外层背景色
            Color(red: 0xAB/255.0, green: 0xB8/255.0, blue: 0xC3/255.0)
                .edgesIgnoringSafeArea(.all)

            // 通过嵌套ZStack确保阴影只应用于最外层
            ZStack {
                // 内容卡片背景 - 用于应用圆角、边框和阴影
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.primaryBg)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
                    )
                    .shadow(color: Color.black.opacity(0.15), radius: 15, x: 0, y: 12)

                // 内容区域 - 不应用阴影
                VStack(spacing: 0) {
                    // 添加导航标题
                    Text(recordViewModel.record.recipeName == nil || recordViewModel.record.recipeName!.isEmpty ? "无名配方" : recordViewModel.record.recipeName!)
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.primaryText)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal, 16)
                        .padding(.top, 16)
                        .padding(.bottom, 8)

                    // 使用shareDetailContent代替detailContent来避免UIViewRepresentable的渲染问题
                    shareDetailContent

                    // 底部分享水印 - 直接作为卡片内容的一部分
                    HStack {
                        Spacer()
                        Text("via 咖啡札记")
                            .font(.caption)
                            .foregroundColor(.detailText)
                            .padding(.trailing, 16)
                    }
                    .padding(.vertical, 8)
                }
                .padding(.bottom, 0) // 移除底部内边距避免与水印重叠
            }
            .padding(.horizontal, 18) // 长截图上下间距稍短
            .padding(.vertical, 20)
        }
        .cornerRadius(0) // 确保外层容器没有圆角
    }

    // 专为截图优化的内容视图，使用纯SwiftUI组件，不使用UIViewRepresentable
    var shareDetailContent: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 顶部标题区域 - 用户名称和日期在左侧，温湿度印章在右侧
            VStack(alignment: .leading, spacing: 0) {
                Text(AttributedString(getUserNameAndDateWithTempHumidity()))
                    .font(.subheadline)
                    .lineLimit(nil)
                    .fixedSize(horizontal: false, vertical: true)
                    .padding(.bottom, 8)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // 冲煮信息标签
                FlowLayout(alignment: .leading, spacing: 8) {
                    BrewMethodBadge(method: recordViewModel.record.brewingEquipment.brewMethod ?? "其他")

                    Badge {
                        Image("clock.symbols")
                        Text(NumberFormatters.formatBrewingTime(Double(recordViewModel.record.brewingTime)))
                    }
                    .modifier(CustomBadgeBackground(color: Color.navbarBg))

                    Badge {
                        Image("ratio.symbols")
                        Text(NumberFormatters.formatBrewRatio(recordViewModel.record.yieldWeight / recordViewModel.record.doseWeight))
                    }
                    .modifier(CustomBadgeBackground(color: Color.navbarBg))
                }
                .padding(.top, 4)
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // 评价与备注
            HStack(alignment: .bottom, spacing: 4) {
                ZStack {
                    Text(recordViewModel.record.ratingDisplay)
                        .font(.title2)
                }

                BubbleView(notesEmpty: recordViewModel.record.notes.isEmpty) {
                    if !recordViewModel.record.notes.isEmpty {
                        Text(recordViewModel.record.notes)
                            .fixedSize(horizontal: false, vertical: true)
                    } else {
                        Text(getRatingText(recordViewModel.record.ratingLevel))
                            .foregroundColor(Color.primaryText)
                    }
                }
            }
            .padding(.bottom, 4)

            // 关键参数卡片
            HStack(spacing: 12) {
                KeyParamCard(icon: "grinder", title: recordViewModel.record.grindSize == "-" ? "-" : "#\(recordViewModel.record.grindSize)", subtitle: recordViewModel.record.waterTemperature > 0 ? " " : nil)

                KeyParamCard(icon: "bean", title: NumberFormatters.formatWeight(recordViewModel.record.doseWeight), subtitle: recordViewModel.record.waterTemperature > 0 ? " " : nil)

                KeyParamCard(icon: "droplet", title: NumberFormatters.formatWeight(recordViewModel.record.yieldWeight), subtitle: recordViewModel.record.waterTemperature > 0 ? NumberFormatters.formatTemperature(recordViewModel.record.waterTemperature) + "°C" : nil)
            }
            .padding(.vertical, 8)

            // 详细信息列表
            VStack(alignment: .leading, spacing: 10) {
                DetailItem(label: "用豆", value: getBeanDetailText())

                if let waterQuality = recordViewModel.record.waterQuality, !waterQuality.isEmpty {
                    DetailItem(label: "用水", value: waterQuality)
                }

                DetailItem(label: "器具", value: getEquipmentDetailText())

                // 显示小工具信息
                if let gadgets = recordViewModel.record.gadgets, !gadgets.isEmpty {
                    VStack(alignment: .leading, spacing: 6) {
                        HStack(alignment: .top, spacing: 12) {
                            Text("小物")
                                .font(.system(size: 15))
                                .frame(width: 40, alignment: .leading)
                                .foregroundColor(.primaryText.opacity(0.6))

                            VStack(alignment: .leading, spacing: 6) {
                                ForEach(gadgets, id: \.id) { gadget in
                                    Text(getGadgetName(gadget))
                                        .font(.system(size: 15))
                                }
                            }
                        }
                    }
                }
            }
            .font(.system(size: 15))
            .padding(.vertical, 8)

            // 冲煮步骤
            if !recordViewModel.record.steps.isEmpty {
                SectionDivider(title: "详细步骤")

                // 步骤列表 - 在截图中使用纯Text替代AdaptiveHighlightedText
                VStack(spacing: 16) {
                    ForEach(Array(recordViewModel.record.steps.enumerated()), id: \.offset) { index, step in
                        HStack(alignment: .top, spacing: 12) {
                            Text("\(index + 1)")
                                .font(.system(size: 32, weight: .light))
                                .foregroundColor(.primaryText.opacity(0.3))
                                .frame(width: 30)

                            VStack(alignment: .leading, spacing: 8) {
                                // 使用纯Text而不是AdaptiveHighlightedText（否则会加载失败）
                                Text(step.text)
                                    .font(.subheadline)
                                    .foregroundColor(Color.primaryText)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .lineSpacing(2) // 增加行间距
                                    .fixedSize(horizontal: false, vertical: true)

                                if let timer = step.timer, !timer.isEmpty {
                                    Text("⏱ \(timer)")
                                        .font(.subheadline)
                                        .foregroundColor(.primaryText.opacity(0.5))
                                }
                            }
                        }
                    }
                }
            }

            // 品鉴笔记
            if !recordViewModel.record.flavorTags.isEmpty || recordViewModel.record.aroma > 0 || recordViewModel.record.acidity > 0 || recordViewModel.record.sweetness > 0 || recordViewModel.record.body > 0 || recordViewModel.record.aftertaste > 0 {
                SectionDivider(title: "品鉴笔记")

                VStack(spacing: 16) {
                    if !recordViewModel.record.flavorTags.isEmpty {
                        RecordFlavorTagsView(
                            tags: recordViewModel.record.flavorTags,
                            overlapPercentage: recordViewModel.record.tagOverlap,
                            showingOverlapInfo: $showingOverlapInfo
                        )
                    }

                    if recordViewModel.record.aroma > 0 || recordViewModel.record.acidity > 0 || recordViewModel.record.sweetness > 0 || recordViewModel.record.body > 0 || recordViewModel.record.aftertaste > 0 {
                        RecordFlavorDimensionChart(
                            aroma: recordViewModel.record.aroma,
                            acidity: recordViewModel.record.acidity,
                            sweetness: recordViewModel.record.sweetness,
                            aftertaste: recordViewModel.record.aftertaste,
                            bodyValue: recordViewModel.record.body
                        )
                        .frame(height: 240)
                    }
                }
                .padding(.vertical, 8)
            }
        }
        .padding(.horizontal)
        .padding(.bottom, 24)
        .background(Color.primaryBg)
        .foregroundColor(.primaryText)
    }

    var body: some View {
        ScrollView {
            detailContent
        }
        .id(refreshTrigger) // 添加ID依赖，当refreshTrigger变化时强制重新渲染
        .background(Color.primaryBg)
        .navigationTitle(recordViewModel.record.recipeName == nil || recordViewModel.record.recipeName!.isEmpty ? "无名配方" : recordViewModel.record.recipeName!)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                HStack {
                    Button(action: {
                        // 分享功能，生成长截图
                        Task {
                            await generateImage()
                        }
                    }) {
                        if isGeneratingImage {
                            BlinkingLoader(
                                color: .primaryText,
                                width: 8,
                                height: 12,
                                duration: 1.0,
                                showText: false
                            )
                        } else {
                            Image("screenshot.symbols")
                        }
                    }
                    .disabled(isGeneratingImage)

                    Button(action: {
                        showingEditView = true
                    }) {
                        Image("edit.symbols")
                    }
                }
            }
        }
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                title: Text("删除记录"),
                message: Text("确定要删除这条记录吗？此操作不可恢复。"),
                primaryButton: .destructive(Text("删除")) {
                    Task {
                        do {
                            try await viewModel.deleteRecord(recordViewModel.record)
                            presentationMode.wrappedValue.dismiss()
                        } catch {
                            print("删除记录失败: \(error)")
                        }
                    }
                },
                secondaryButton: .cancel(Text("取消"))
            )
        }
        .sheet(isPresented: $showingEditView) {
            NavigationView {
                EditBrewLogView(record: recordViewModel.record)
            }
        }
        .fullScreenCover(isPresented: $showingStepGuide) {
            StepGuideView(steps: recordViewModel.record.steps, currentStep: $currentStep)
        }
        .overlay(
            ZStack {
                if showingOverlapInfo {
                    OverlapInfoSheet(isPresented: $showingOverlapInfo)
                }
            }
        )
        // 添加系统主题改变监听
        .onAppear {
            // 进入视图时强制更新主题颜色
            themeManager.updateThemeColorsOnly()
        }
        // 监听系统主题变化通知
        .onReceive(NotificationCenter.default.publisher(for: TraitChangeObserver.traitChangeNotification)) { _ in
            themeManager.updateThemeColorsOnly()
        }
        // 监听记录更新通知
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("BrewLogRecordUpdated"))) { notification in
            // 首先尝试从userInfo中获取数据
            if let userInfo = notification.userInfo,
               let recordData = userInfo["recordData"] as? Data,
               let recordId = userInfo["recordId"] as? Int {

                if recordId == recordViewModel.record.id {
                    do {
                        let updatedRecord = try JSONDecoder().decode(BrewingRecord.self, from: recordData)

                        // 在主线程更新
                        DispatchQueue.main.async {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                recordViewModel.updateRecord(updatedRecord)
                            }
                        }
                    } catch {
                        print("解码记录失败: \(error)")
                    }
                }
            }
            // 备用方案：尝试从object获取数据
            else if let updatedRecord = notification.object as? BrewingRecord {
                if updatedRecord.id == recordViewModel.record.id {
                    DispatchQueue.main.async {
                        recordViewModel.updateRecord(updatedRecord)
                    }
                }
            }
        }
    }

    @MainActor private func generateImage() async {
        // 确保不在生成图片过程中重复触发
        guard !isGeneratingImage else {
            print("⚠️ 已在生成图片，跳过")
            return
        }

        // 标记正在生成图片，显示加载指示器
        isGeneratingImage = true

        do {
            // 使用UIKit屏幕尺寸来设置宽度
            let screenWidth = UIScreen.main.bounds.width

            // 确保主题颜色最新
            themeManager.updateThemeColorsOnly()

            // 延时一小段时间确保视图已加载完毕
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

            // 首先计算内容的实际高度
            let sizingContent = shareContent
                .frame(width: screenWidth)
                .fixedSize(horizontal: true, vertical: true) // 让高度适应内容

            // 使用GeometryReader测量内容实际高度（放在后台队列中执行以不阻塞UI）
            let contentSize = await withCheckedContinuation { (continuation: CheckedContinuation<CGSize, Never>) in
                DispatchQueue.main.async {
                    let controller = UIHostingController(rootView: sizingContent)
                    controller.view.layoutIfNeeded()

                    // 获取实际内容尺寸并添加一些缓冲区
                    var size = controller.sizeThatFits(in: CGSize(width: screenWidth, height: UIView.layoutFittingCompressedSize.height))

                    // 确保内容大小是有效的
                    if size.height <= 0 {
                        size.height = UIScreen.main.bounds.height
                    }

                    continuation.resume(returning: size)
                }
            }

            // 创建一个渲染器并设置内容
            let renderer = ImageRenderer(
                content: shareContent
                    .frame(width: screenWidth, height: contentSize.height)
                    .environment(\.colorScheme, colorScheme) // 确保渲染时使用当前的颜色方案
            )

            // 设置正确的显示比例
            renderer.scale = displayScale

            // 确保生成图像的上下文是不透明的
            renderer.proposedSize = ProposedViewSize(width: screenWidth, height: contentSize.height)

            if let uiImage = renderer.uiImage {
                // 保存为PNG格式而非JPEG
                let finalImage: UIImage
                if let pngData = uiImage.pngData(),
                   let pngImage = UIImage(data: pngData) {
                    finalImage = pngImage
                } else {
                    finalImage = uiImage
                }

                // 等待一小段时间确保图片已经准备好
                try await Task.sleep(nanoseconds: 200_000_000) // 0.2秒

                // 使用UIActivityViewController分享图片
                let activityVC = UIActivityViewController(
                    activityItems: [finalImage],
                    applicationActivities: nil
                )

                // 在iPad上，设置弹出位置
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let rootViewController = windowScene.windows.first?.rootViewController {

                    if UIDevice.current.userInterfaceIdiom == .pad {
                        activityVC.popoverPresentationController?.sourceView = rootViewController.view
                        activityVC.popoverPresentationController?.sourceRect = CGRect(
                            x: UIScreen.main.bounds.width / 2,
                            y: UIScreen.main.bounds.height / 2,
                            width: 0,
                            height: 0
                        )
                    }

                    // 呈现分享控制器
                    await MainActor.run {
                        print("📸 图片生成成功，准备显示系统分享表单")
                        rootViewController.present(activityVC, animated: true) {
                            // 分享控制器显示后，重置生成状态
                            self.isGeneratingImage = false
                        }
                    }
                } else {
                    // 如果无法获取rootViewController，则只重置状态
                    await MainActor.run {
                        self.isGeneratingImage = false
                        print("⚠️ 无法获取rootViewController来显示分享表单")
                    }
                }
            } else {
                await MainActor.run {
                    isGeneratingImage = false
                    print("⚠️ 渲染器未能生成图片")

                    // 显示错误提示
                    showErrorToast("生成图片失败")
                }
            }
        } catch {
            print("❌ 生成图片时发生错误: \(error.localizedDescription)")
            await MainActor.run {
                isGeneratingImage = false
                showErrorToast("生成图片失败: \(error.localizedDescription)")
            }
        }
    }

    // 获取用户名
    private func getUserName() -> String {
        if let firstName = authService.currentUser?.firstName, !firstName.isEmpty {
            return firstName
        }
        return authService.currentUser?.username ?? "用户"
    }

    // 获取配方名称，确保为空时返回"无名配方"
    private func getRecipeName() -> String {
        return recordViewModel.record.recipeName == nil || recordViewModel.record.recipeName!.isEmpty ? "无名配方" : recordViewModel.record.recipeName!
    }

    // 显示错误提示
    private func showErrorToast(_ message: String) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first
        else { return }

        let toastView = UIView()
        toastView.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.9)
        toastView.layer.cornerRadius = 10
        toastView.layer.shadowColor = UIColor.black.cgColor
        toastView.layer.shadowOpacity = 0.2
        toastView.layer.shadowOffset = CGSize(width: 0, height: 2)
        toastView.layer.shadowRadius = 4

        let label = UILabel()
        label.text = message
        label.textColor = UIColor.label
        label.textAlignment = .center
        label.numberOfLines = 0

        toastView.addSubview(label)
        window.addSubview(toastView)

        label.translatesAutoresizingMaskIntoConstraints = false
        toastView.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            label.leadingAnchor.constraint(equalTo: toastView.leadingAnchor, constant: 16),
            label.trailingAnchor.constraint(equalTo: toastView.trailingAnchor, constant: -16),
            label.topAnchor.constraint(equalTo: toastView.topAnchor, constant: 12),
            label.bottomAnchor.constraint(equalTo: toastView.bottomAnchor, constant: -12),

            toastView.centerXAnchor.constraint(equalTo: window.centerXAnchor),
            toastView.bottomAnchor.constraint(equalTo: window.safeAreaLayoutGuide.bottomAnchor, constant: -30),
            toastView.widthAnchor.constraint(lessThanOrEqualTo: window.widthAnchor, constant: -40)
        ])

        // 动画显示
        toastView.alpha = 0
        UIView.animate(withDuration: 0.3) {
            toastView.alpha = 1
        }

        // 2秒后消失
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            UIView.animate(withDuration: 0.3, animations: {
                toastView.alpha = 0
            }) { _ in
                toastView.removeFromSuperview()
            }
        }
    }

    // 获取用户名
    private func getUserNameText() -> String {
        return getUserName()
    }

    // 获取日期文本
    private func getDateText() -> String {
        return " 记于 \(formattedDate(recordViewModel.record.createdAt))"
    }

    private func getBeanDetailText() -> String {
        let bean = recordViewModel.record.coffeeBean
        var text = "\(bean.roaster) \(bean.name)"

        // CoffeeBean没有isDeleted字段，所以我们直接跳过这个判断

        // 调整处理roastLevel字段
        text += "，\(bean.roastLevelDisplay)"

        if let process = bean.process {
            text += process
        }

        // 处理可能为nil的origin字段
        if let origin = bean.origin, !origin.isEmpty {
            text += "（\(origin)）"
        }

        return text
    }

    private func getRoastLevelText(_ level: String) -> String {
        return level // 直接返回字符串类型的烘焙等级
    }

    private func getEquipmentDetailText() -> String {
        let brew = recordViewModel.record.brewingEquipment
        let grind = recordViewModel.record.grindingEquipment

        var text = ""

        // 冲煮器材
        if let brand = brew.brand, !brand.isEmpty {
            if brew.name.contains(brand) {
                text += brew.name
            } else {
                text += "\(brand) \(brew.name)"
            }
        } else {
            text += brew.name
        }

        if brew.isDeleted {
            text += " [已删除]"
        }

        text += " + "

        // 研磨器材，注意处理可能为nil的情况
        if let grindEquip = grind {
            if let brand = grindEquip.brand, !brand.isEmpty {
                if grindEquip.name.contains(brand) {
                    text += grindEquip.name
                } else {
                    text += "\(brand) \(grindEquip.name)"
                }
            } else {
                text += grindEquip.name
            }

            if grindEquip.isDeleted {
                text += " [已删除]"
            }
        } else {
            text += "未指定研磨器具"
        }

        return text
    }

    private func getGadgetName(_ gadget: Equipment) -> String {
        var name = ""
        if let brand = gadget.brand, !brand.isEmpty {
            if gadget.name.contains(brand) {
                name = gadget.name
            } else {
                name = "\(brand) \(gadget.name)"
            }
        } else {
            name = gadget.name
        }

        if gadget.isDeleted {
            name += " [已删除]"
        }

        return name
    }

    private func getGadgetsDetailText() -> String {
        // 此函数保留但不再使用
        var gadgetText = ""

        // 处理单个小工具
        if let gadgets = recordViewModel.record.gadgets, !gadgets.isEmpty {
            gadgetText = gadgets.map { gadget in
                getGadgetName(gadget)
            }.joined(separator: "、")
        } else {
            gadgetText = "无"
        }

        return gadgetText
    }

    private func getRatingText(_ rating: Int) -> String {
        switch rating {
        case 1: return "浪费了"
        case 2: return "差劲"
        case 3: return "不满意"
        case 4: return "失望"
        case 5: return "普通"
        case 6: return "及格"
        case 7: return "不错"
        case 8: return "很好！"
        case 9: return "出色！"
        case 10: return "完美！"
        default: return "未评分"
        }
    }

    private func formatDuration(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%d:%02d", minutes, remainingSeconds)
    }

    private func formattedDate(_ date: Date) -> String {
        // 确定时间段名称
        let hour = Calendar.current.component(.hour, from: date)
        let amPm: String
        var displayHour = hour

        if 0 <= hour && hour < 2 {
            amPm = "午夜"
        } else if 2 <= hour && hour < 4 {
            amPm = "凌晨"
        } else if 4 <= hour && hour < 6 {
            amPm = "黎明"
        } else if 6 <= hour && hour < 8 {
            amPm = "早晨"
        } else if 8 <= hour && hour < 11 {
            amPm = "上午"
        } else if 11 <= hour && hour < 13 {
            amPm = "中午"
            if hour > 12 {
                displayHour -= 12
            }
        } else if 13 <= hour && hour < 17 {
            amPm = "下午"
            displayHour -= 12
        } else if hour == 17 {
            amPm = "傍晚"
            displayHour -= 12
        } else if 18 <= hour && hour < 23 {
            amPm = "晚上"
            displayHour -= 12
        } else {  // 23点
            amPm = "深夜"
            displayHour -= 12
        }

        // 获取星期几
        let weekday = Calendar.current.component(.weekday, from: date)
        let weekdays = ["日","一","二","三","四","五","六"]
        let weekdayString = weekdays[weekday-1] // 因为weekday从1开始（周日）

        let minute = Calendar.current.component(.minute, from: date)
        let day = Calendar.current.component(.day, from: date)
        let month = Calendar.current.component(.month, from: date)
        let year = Calendar.current.component(.year, from: date)

        // 获取当前年份，以决定是否显示年份
        let currentYear = Calendar.current.component(.year, from: Date())

        var dateFormat = "\(month)月\(day)日 周\(weekdayString) \(amPm)\(displayHour):\(String(format: "%02d", minute))"
        if year != currentYear {
            dateFormat = "\(year)年\(dateFormat)"
        }

        return dateFormat
    }

    // 添加一个新方法来生成包含用户名、日期和温湿度的AttributedString
    private func getUserNameAndDateWithTempHumidity() -> NSAttributedString {
        let result = NSMutableAttributedString()

        // 用户名
        let nameAttributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor(Color.primaryText)
        ]
        let nameString = NSAttributedString(string: getUserName(), attributes: nameAttributes)
        result.append(nameString)

        // 日期
        let dateAttributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor(Color.primaryText.opacity(0.5))
        ]
        let dateString = NSAttributedString(string: " 记于 \(formattedDate(recordViewModel.record.createdAt))", attributes: dateAttributes)
        result.append(dateString)

        // 温湿度
        if let roomTemp = recordViewModel.record.roomTemperature, let roomHumidity = recordViewModel.record.roomHumidity,
            roomTemp > 0, roomHumidity > 0 {

            let dotAttributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: UIColor(Color.primaryText.opacity(0.5))
            ]
            result.append(NSAttributedString(string: " · ", attributes: dotAttributes))

            let tempAttributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: UIColor(Color.error.opacity(0.8))
            ]
            result.append(NSAttributedString(string: "\(NumberFormatters.formatTemperature(roomTemp))ºc", attributes: tempAttributes))

            result.append(NSAttributedString(string: " · ", attributes: dotAttributes))

            let humidityAttributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: UIColor(Color.linkText.opacity(0.8))
            ]
            result.append(NSAttributedString(string: "\(roomHumidity)%rh", attributes: humidityAttributes))
        } else if let roomTemp = recordViewModel.record.roomTemperature, roomTemp > 0 {
            let dotAttributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: UIColor(Color.primaryText.opacity(0.5))
            ]
            result.append(NSAttributedString(string: " · ", attributes: dotAttributes))

            let tempAttributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: UIColor(Color.error.opacity(0.8))
            ]
            result.append(NSAttributedString(string: "\(NumberFormatters.formatTemperature(roomTemp))ºc", attributes: tempAttributes))
        } else if let roomHumidity = recordViewModel.record.roomHumidity, roomHumidity > 0 {
            let dotAttributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: UIColor(Color.primaryText.opacity(0.5))
            ]
            result.append(NSAttributedString(string: " · ", attributes: dotAttributes))

            let humidityAttributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: UIColor(Color.linkText.opacity(0.8))
            ]
            result.append(NSAttributedString(string: "\(roomHumidity)%rh", attributes: humidityAttributes))
        }

        return result
    }
}

// MARK: - 辅助视图组件

struct Badge<Content: View>: View {
    let content: Content
    @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        HStack(spacing: 4) {
            content
        }
        .font(.subheadline)
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.secondaryBg)
        .cornerRadius(20)
    }
}

struct BrewMethodBadge: View {
    let method: String
    @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听

    var body: some View {
        Badge {
            BrewMethodIcon(method: method)
            Text(getMethodDisplayText(method: method))
        }
        .modifier(CustomBadgeBackground(color: Color.navbarBg))
    }

    /// 将冲煮方式代码转换为显示名称
    func getMethodDisplayText(method: String) -> String {
        // 转换为大写以便统一处理
        let uppercaseMethod = method.uppercased()

        switch uppercaseMethod {
        case "ESPRESSO":
            return "意式"
        case "POUR_OVER":
            return "手冲"
        case "AEROPRESS":
            return "爱乐压"
        case "COLD_BREW":
            return "冷萃"
        case "MOKA_POT":
            return "摩卡壶"
        case "FRENCH_PRESS":
            return "法压壶"
        case "AUTO_DRIP":
            return "自动滴滤"
        default:
            return method
        }
    }

    @ViewBuilder
    func BrewMethodIcon(method: String) -> some View {
        // 转换为大写以便统一处理
        let uppercaseMethod = method.uppercased()

        switch uppercaseMethod {
        case "ESPRESSO":
            Image("espresso.symbols")
        case "POUR_OVER":
            Image("pourover.symbols")
        case "AEROPRESS":
            Image("aeropresso.symbols")
        case "COLD_BREW":
            Image("coldbrew.symbols")
        case "MOKA_POT":
            Image("mokapot.symbols")
        case "FRENCH_PRESS":
            Image("frenchpress.symbols")
        case "AUTO_DRIP":
            Image("autodrip.symbols")
        default:
            Image(systemName: "cup.and.saucer")
        }
    }
}

// 添加自定义修饰器来替换Badge内部的背景色
struct CustomBadgeBackground: ViewModifier {
    let color: Color
    @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听

    func body(content: Content) -> some View {
        content
            .background(color)
            .cornerRadius(20)  // 确保圆角半径与Badge一致
    }
}

struct BubbleView<Content: View>: View {
    let content: Content
    @Environment(\.colorScheme) var colorScheme // 添加环境变量以检测当前颜色模式
    var notesEmpty: Bool = false // 添加标志位，表示notes是否为空

    init(notesEmpty: Bool = false, @ViewBuilder content: () -> Content) {
        self.content = content()
        self.notesEmpty = notesEmpty
    }

    var body: some View {
        HStack(alignment: .bottom) {
            content
                .padding(12)
                .background(
                    notesEmpty ? Color.secondaryText.opacity(0.5) : Color.focusBg.opacity(0.5)
                )
                .clipShape(BubbleShape(direction: .left))
            Spacer()
        }
        .padding(.top, 12)
        .padding(.bottom, 2)  // 减少底部内边距使气泡更靠下
        .padding(.leading, 0)  // 移除左侧内边距
        .padding(.trailing, 40)
    }
}

struct BubbleShape: Shape {
    enum Direction {
        case left
        case right
    }

    let direction: Direction

    func path(in rect: CGRect) -> Path {
        return (direction == .left) ? getLeftBubblePath(in: rect) : getRightBubblePath(in: rect)
    }

    private func getLeftBubblePath(in rect: CGRect) -> Path {
        let width = rect.width
        let height = rect.height
        let path = Path { p in
            p.move(to: CGPoint(x: 25, y: height))
            p.addLine(to: CGPoint(x: width - 20, y: height))
            p.addCurve(to: CGPoint(x: width, y: height - 20),
                       control1: CGPoint(x: width - 8, y: height),
                       control2: CGPoint(x: width, y: height - 8))
            p.addLine(to: CGPoint(x: width, y: 20))
            p.addCurve(to: CGPoint(x: width - 20, y: 0),
                       control1: CGPoint(x: width, y: 8),
                       control2: CGPoint(x: width - 8, y: 0))
            p.addLine(to: CGPoint(x: 21, y: 0))
            p.addCurve(to: CGPoint(x: 4, y: 20),
                       control1: CGPoint(x: 12, y: 0),
                       control2: CGPoint(x: 4, y: 8))
            p.addLine(to: CGPoint(x: 4, y: height - 11))
            p.addCurve(to: CGPoint(x: 0, y: height),
                       control1: CGPoint(x: 4, y: height - 1),
                       control2: CGPoint(x: 0, y: height))
            p.addLine(to: CGPoint(x: -0.05, y: height - 0.01))
            p.addCurve(to: CGPoint(x: 11.0, y: height - 4.0),
                       control1: CGPoint(x: 4.0, y: height + 0.5),
                       control2: CGPoint(x: 8, y: height - 1))
            p.addCurve(to: CGPoint(x: 25, y: height),
                       control1: CGPoint(x: 16, y: height),
                       control2: CGPoint(x: 20, y: height))
        }
        return path
    }

    private func getRightBubblePath(in rect: CGRect) -> Path {
        let width = rect.width
        let height = rect.height
        let path = Path { p in
            p.move(to: CGPoint(x: 25, y: height))
            p.addLine(to: CGPoint(x:  20, y: height))
            p.addCurve(to: CGPoint(x: 0, y: height - 20),
                       control1: CGPoint(x: 8, y: height),
                       control2: CGPoint(x: 0, y: height - 8))
            p.addLine(to: CGPoint(x: 0, y: 20))
            p.addCurve(to: CGPoint(x: 20, y: 0),
                       control1: CGPoint(x: 0, y: 8),
                       control2: CGPoint(x: 8, y: 0))
            p.addLine(to: CGPoint(x: width - 21, y: 0))
            p.addCurve(to: CGPoint(x: width - 4, y: 20),
                       control1: CGPoint(x: width - 12, y: 0),
                       control2: CGPoint(x: width - 4, y: 8))
            p.addLine(to: CGPoint(x: width - 4, y: height - 11))
            p.addCurve(to: CGPoint(x: width, y: height),
                       control1: CGPoint(x: width - 4, y: height - 1),
                       control2: CGPoint(x: width, y: height))
            p.addLine(to: CGPoint(x: width + 0.05, y: height - 0.01))
            p.addCurve(to: CGPoint(x: width - 11, y: height - 4),
                       control1: CGPoint(x: width - 4, y: height + 0.5),
                       control2: CGPoint(x: width - 8, y: height - 1))
            p.addCurve(to: CGPoint(x: width - 25, y: height),
                       control1: CGPoint(x: width - 16, y: height),
                       control2: CGPoint(x: width - 20, y: height))
        }
        return path
    }
}

struct KeyParamCard: View {
    let icon: String
    let title: String
    var subtitle: String? = nil
    @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听

    var body: some View {
        VStack(spacing: 4) {
            Image(icon)
                .resizable()
                .scaledToFit()
                .frame(width: 48, height: 48)
                .padding(.bottom, 4)

            Text(title)
                .font(.headline)
                .foregroundColor(.primaryText) // 添加文本颜色

            if let subtitle = subtitle {
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.primaryText)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(Color.navbarBg.opacity(0.5))
        .cornerRadius(12)
    }
}

struct DetailItem: View {
    let label: String
    let value: String
    @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text(label)
                .frame(width: 40, alignment: .leading)
                .foregroundColor(.primaryText.opacity(0.6))

            Text(value)
                .foregroundColor(.primaryText) // 添加文本颜色
                .fixedSize(horizontal: false, vertical: true)
        }
    }
}

struct RecordFlavorTagsView: View {
    let tags: [FlavorTag]
    let overlapPercentage: Int?
    @Binding var showingOverlapInfo: Bool  // 改为 Binding

    var body: some View {
        VStack(spacing: 8) {
            FlowLayout(spacing: 8) {
                ForEach(tags) { tag in
                    Text(tag.name)
                        .font(.subheadline)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.primaryAccent.opacity(0.1))
                        .cornerRadius(15)
                        .overlay(
                            RoundedRectangle(cornerRadius: 15)
                                .stroke(Color.primaryAccent.opacity(0.3), lineWidth: 1)
                        )
                }

                if let overlap = overlapPercentage {
                    HStack(spacing: 4) {
                        Image("overlap.symbols")
                        Text("\(overlap)%")
                    }
                    .font(.subheadline)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.primaryText.opacity(0.1))
                    .cornerRadius(15)
                    .overlay(
                        RoundedRectangle(cornerRadius: 15)
                            .stroke(Color.primaryText.opacity(0.3), lineWidth: 1)
                    )
                    .onTapGesture {
                        showingOverlapInfo = true
                    }
                }
            }
        }
    }
}

// 添加一个新的弹框视图组件
struct OverlapInfoSheet: View {
    @Binding var isPresented: Bool
    @GestureState private var dragOffset = CGSize.zero

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 底部弹框
                VStack(spacing: 0) {
                    // 顶部灰条和关闭按钮
                    VStack(spacing: 0) {
                        // 灰色拖动条
                        Capsule()
                            .fill(Color.noteText.opacity(0.3))
                            .frame(width: 36, height: 5)
                            .padding(.top, 8)
                            .padding(.bottom, 12)

                        // 标题栏
                        HStack {
                            Text("风味重叠度")
                                .font(.headline)
                                .foregroundColor(.primaryText)

                            Spacer()

                            // 关闭按钮
                            Button(action: {
                                isPresented = false
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.archivedText)
                                    .font(.system(size: 24))
                            }
                        }
                        .padding(.bottom, 16)
                    }
                    .padding(.horizontal)

                    // 内容区域
                    Text("与咖啡豆风味标签的重叠度，帮助您了解是否与预期风味相符")
                        .font(.subheadline)
                        .foregroundColor(.primaryText.opacity(0.8))
                        .fixedSize(horizontal: false, vertical: true)
                        .lineSpacing(4)
                        .padding(.horizontal)
                        .padding(.bottom, 24)
                }
                .frame(maxWidth: .infinity)
                .background(Color.primaryBg)
                .cornerRadius(16)
                .shadow(color: Color.primaryText.opacity(0.2), radius: 10, x: 0, y: -5)
                .offset(y: max(0, dragOffset.height))
                .gesture(
                    DragGesture()
                        .updating($dragOffset) { value, state, _ in
                            if value.translation.height > 0 {
                                state = value.translation
                            }
                        }
                        .onEnded { value in
                            if value.translation.height > 50 {
                                isPresented = false
                            }
                        }
                )
                .frame(maxHeight: .infinity, alignment: .bottom)
                // 添加底部安全区域和水平间距
                .padding(.bottom, geometry.safeAreaInsets.bottom + 12)
                .padding(.horizontal, 12)
            }
            .edgesIgnoringSafeArea(.bottom)
        }
        .transition(.move(edge: .bottom))
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPresented)
    }
}

// 添加圆角辅助扩展
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

// 自定义圆角形状
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

struct RecordFlavorDimensionChart: View {
    let aroma: Int
    let acidity: Int
    let sweetness: Int
    let aftertaste: Int
    let bodyValue: Int  // 将body改为bodyValue以避免命名冲突
    @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听

    private var keys: [String] {
        ["香气", "酸质", "甜度", "余韵", "醇厚"]
    }

    private var values: [Double] {
        [Double(aroma), Double(acidity), Double(sweetness), Double(aftertaste), Double(bodyValue)]
    }

    var body: some View {
        RadarChart(keys: keys, values: values, strokeColor: .functionText, maxValue: 5.0)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding()
    }
}

// 雷达图实现
struct RadarChart: View {
    var keys: [String]
    var values: [Double]
    var strokeColor: Color = .functionText
    let maxValue: Double // 最大值，咖啡评分通常为5分
    @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听

    // 基于当前主题的颜色
    private var backgroundColor: Color {
        Color.functionText.opacity(0.2)
    }
    private var borderColor: Color {
        Color.functionText.opacity(0.8)
    }
    private var pointColor: Color {
        Color.primaryAccent
    }
    private var gridColor: Color {
        Color.primaryText.opacity(0.1)
    }
    private var labelColor: Color {
        Color.primaryText
    }

    var body: some View {
        GeometryReader { geometry in
            self.makeRadarChart(geometry: geometry)
        }
        .background(Color.primaryBg)
    }

    private func makeRadarChart(geometry: GeometryProxy) -> some View {
        let center = CGPoint(x: geometry.size.width / 2, y: geometry.size.height / 2)
        let radius = min(geometry.size.width, geometry.size.height) / 2 * 0.8
        let angleAdjustment = -CGFloat.pi / 2 // 从上方开始
        let memoryLevels = 6 // 网格线数量，包括中心和边界

        return ZStack {
            // 绘制背景网格圆环
            ForEach(0..<memoryLevels, id: \.self) { level in
                Circle()
                    .stroke(gridColor, lineWidth: 1)
                    .frame(width: radius * 2 * CGFloat(level) / CGFloat(memoryLevels - 1), height: radius * 2 * CGFloat(level) / CGFloat(memoryLevels - 1))
                    .position(center)
            }

            // 数据点连线并填充
            Path { path in
                for (index, value) in values.enumerated() {
                    let angle = 2 * .pi / CGFloat(values.count) * CGFloat(index) + angleAdjustment
                    let pointRadius = radius * (value / maxValue) // 按最大值5进行归一化
                    let point = CGPoint(x: center.x + cos(angle) * pointRadius, y: center.y + sin(angle) * pointRadius)

                    if index == 0 {
                        path.move(to: point)
                    } else {
                        path.addLine(to: point)
                    }
                }
                path.closeSubpath()
            }
            .fill(backgroundColor)
            .overlay(
                Path { path in
                    for (index, value) in values.enumerated() {
                        let angle = 2 * .pi / CGFloat(values.count) * CGFloat(index) + angleAdjustment
                        let pointRadius = radius * (value / maxValue)
                        let point = CGPoint(x: center.x + cos(angle) * pointRadius, y: center.y + sin(angle) * pointRadius)

                        if index == 0 {
                            path.move(to: point)
                        } else {
                            path.addLine(to: point)
                        }
                    }
                    path.closeSubpath()
                }
                .stroke(borderColor, lineWidth: 2)
            )

            // 添加数据点
            ForEach(Array(values.enumerated()), id: \.offset) { index, value in
                let angle = 2 * .pi / CGFloat(values.count) * CGFloat(index) + angleAdjustment
                let pointRadius = radius * (value / maxValue)
                let point = CGPoint(x: center.x + cos(angle) * pointRadius, y: center.y + sin(angle) * pointRadius)

                Circle()
                    .fill(pointColor)
                    .frame(width: 8, height: 8)
                    .overlay(
                        Circle()
                            .stroke(Color.primaryBg, lineWidth: 1)
                            .frame(width: 8, height: 8)
                    )
                    .position(point)
            }

            // 添加五个文本标签 - 准确定位在各自轴线延伸最外端
            ForEach(0..<keys.count, id: \.self) { index in
                let angle = 2 * .pi / CGFloat(keys.count) * CGFloat(index) + angleAdjustment
                let labelDistance = radius + 25 // 增加标签与雷达图边缘的距离
                let x = center.x + cos(angle) * labelDistance
                let y = center.y + sin(angle) * labelDistance

                Text(keys[index])
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(labelColor)
                    .position(x: x, y: y)
            }

            // 在每层圆环上添加刻度值，放在最上层确保覆盖其他元素
            ForEach(1..<memoryLevels, id: \.self) { level in
                Text("\(level)")
                    .font(.system(size: 10))
                    .foregroundColor(.detailText)
                    .position(x: center.x, y: center.y - radius * CGFloat(level) / CGFloat(memoryLevels - 1))
            }
        }
    }
}

struct StepGuideView: View {
    let steps: [BrewingStepItem]
    @Binding var currentStep: Int
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.colorScheme) var colorScheme
    @ObservedObject private var themeManager = ThemeManager.shared
    @State private var isCompleted = false
    @State private var showCompletionAlert = false // 添加完成提示状态
    @State private var shouldScrollToStep: Int? = nil // 用于触发滚动的状态
    @State private var autoScrollTimer: Timer? = nil // 自动滚动计时器
    @State private var autoScrollTarget: String? = nil // 自动滚动目标
    @State private var autoScrollAnchor: UnitPoint = .top // 自动滚动锚点
    
    var body: some View {
        VStack(spacing: 0) {
            navigationHeader
            mainContent
        }
        .background(Color.primaryBg)
        .foregroundColor(Color.primaryText)
        .onAppear {
            themeManager.updateThemeColorsOnly()
            // 重新进入指引时重置到第一步
            currentStep = 0
        }
        .onReceive(NotificationCenter.default.publisher(for: TraitChangeObserver.traitChangeNotification)) { _ in
            themeManager.updateThemeColorsOnly()
        }
        .onDisappear {
            // 清理自动滚动计时器
            stopAutoScroll()
        }
        .overlay(completionOverlay)
    }

    // 导航头部
    private var navigationHeader: some View {
        HStack {
            Text("步骤指引")
                .font(.headline)
            Spacer()
            Button("退出指引") {
                presentationMode.wrappedValue.dismiss()
            }
            .foregroundColor(Color.error)
        }
        .padding()
        .background(Color.primaryBg)
    }

    // 主要内容
    private var mainContent: some View {
        VStack(spacing: 20) {
            if !steps.isEmpty {
                currentStepCard
            }
            stepsListView
        }
        .padding(.vertical)
    }

    // 当前步骤卡片
    private var currentStepCard: some View {
        VStack(spacing: 16) {
            Text("步骤 \(currentStep + 1)")
                .font(.headline)
                .foregroundColor(.primaryBg.opacity(0.7))

            stepContentScrollView
            stepControlButtons
        }
        .padding()
        .background(Color.primaryText)
        .cornerRadius(12)
        .padding(.horizontal)
    }

    // 步骤内容滚动视图
    private var stepContentScrollView: some View {
        ScrollViewReader { proxy in
            ScrollView(.vertical, showsIndicators: true) {
                VStack {
                    Spacer(minLength: 20)
                        .id("content-top") // 顶部锚点

                    stepTextContent

                    Spacer(minLength: 20)
                        .id("content-bottom") // 底部锚点
                }
                .frame(minHeight: 200) // 固定最小高度，更紧凑
            }
            .frame(maxHeight: 200) // 固定最大高度，更紧凑
            .background(Color.clear)
            .onChange(of: currentStep) { _ in
                stopAutoScroll()
                // 重置到顶部
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    autoScrollTarget = "content-top"
                    autoScrollAnchor = .top
                    startAutoScrollIfNeeded()
                }
            }
            .onChange(of: autoScrollTarget) { target in
                if let target = target {
                    withAnimation(.easeInOut(duration: 0.8)) {
                        proxy.scrollTo(target, anchor: autoScrollAnchor)
                    }
                    // 清除滚动目标，避免重复触发
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        autoScrollTarget = nil
                    }
                }
            }
            .onAppear {
                startAutoScrollIfNeeded()
            }
        }
    }

    // 步骤文本内容
    private var stepTextContent: some View {
        AdaptiveHighlightedText(
            steps[currentStep].text,
            font: .title3,
            foregroundColor: Color.primaryText,
            nonHighlightedColor: Color.primaryBg,
            textAlignment: .center
        )
        .frame(maxWidth: .infinity, alignment: .center)
        .lineSpacing(3)
        .lineLimit(nil)
        .multilineTextAlignment(.center)
        .fixedSize(horizontal: false, vertical: true)
        .padding(.horizontal, 4) // 为滚动条留出空间
        .id("content-text") // 文本内容锚点
    }



    // 步骤控制按钮
    private var stepControlButtons: some View {
        HStack(spacing: 16) {
            previousStepButton
            timerView
            nextStepButton
        }
    }

    // 上一步按钮
    private var previousStepButton: some View {
        Group {
            if currentStep > 0 {
                Button(action: {
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()
                    goToPreviousStep(isAutomatic: false)
                }) {
                    Image(systemName: "backward.fill")
                        .font(.title2)
                        .foregroundColor(.primaryBg)
                }
                .frame(width: 44, height: 44)
            } else {
                Spacer()
                    .frame(width: 44, height: 44)
            }
        }
    }

    // 计时器视图
    private var timerView: some View {
        Group {
            if let timer = steps[currentStep].timer, !timer.isEmpty {
                StepGuideTimerView(
                    timerString: timer,
                    stepIndex: currentStep,
                    isLastStep: currentStep == steps.count - 1,
                    onTimerComplete: {
                        if currentStep < steps.count - 1 {
                            goToNextStep(isAutomatic: true)
                        } else {
                            isCompleted = true
                            showCompletionAlert = true
                            // 不再播放音效，因为StepGuideTimerView已经播放了

                            // 3秒后自动关闭提示
                            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                                showCompletionAlert = false
                            }
                        }
                    }
                )
                .frame(height: 44)
            }
        }
    }

    // 下一步按钮
    private var nextStepButton: some View {
        Group {
            if currentStep < steps.count - 1 {
                Button(action: {
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()
                    goToNextStep(isAutomatic: false)
                }) {
                    Image(systemName: "forward.fill")
                        .font(.title2)
                        .foregroundColor(.primaryBg)
                }
                .frame(width: 44, height: 44)
            } else {
                Spacer()
                    .frame(width: 44, height: 44)
            }
        }
    }

    // 步骤列表视图
    private var stepsListView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                VStack(alignment: .leading, spacing: 0) {
                    ForEach(Array(steps.enumerated()), id: \.offset) { index, step in
                        TimelineStepView(
                            step: step,
                            index: index,
                            currentStep: currentStep,
                            isLast: index == steps.count - 1,
                            onTap: {
                                if index != currentStep {
                                    playStepChangeSound()
                                    currentStep = index
                                }
                            }
                        )
                        .id(index) // 为每个步骤添加ID以支持滚动定位
                    }
                }
                .padding()
            }
            .onAppear {
                // 初次出现时滚动到当前步骤，使用更长的延迟确保视图完全加载
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        proxy.scrollTo(currentStep, anchor: .center)
                    }
                }
            }
            .onChange(of: currentStep) { newStep in
                // 当步骤改变时，自动滚动到当前步骤
                // 使用延迟确保UI更新完成
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        proxy.scrollTo(newStep, anchor: .center)
                    }
                }
            }
            .onChange(of: shouldScrollToStep) { targetStep in
                // 响应强制滚动请求
                if let step = targetStep {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        withAnimation(.easeInOut(duration: 0.5)) {
                            proxy.scrollTo(step, anchor: .center)
                        }
                        // 重置滚动触发器
                        shouldScrollToStep = nil
                    }
                }
            }
        }
    }

    // 完成提示覆盖层
    private var completionOverlay: some View {
        ZStack {
            if showCompletionAlert {
                VStack {
                    Text("🎉 所有步骤已完成！")
                        .font(.headline)
                        .padding()
                        .background(Color.focusBg)
                        .foregroundColor(Color.primaryText)
                        .cornerRadius(10)
                        .shadow(radius: 5)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black.opacity(0.3))
                .edgesIgnoringSafeArea(.all)
                .transition(.opacity)
                .animation(.easeInOut, value: showCompletionAlert)
                .onTapGesture {
                    // 点击任意位置关闭提示
                    showCompletionAlert = false
                }
            }
        }
    }
    
    private func goToNextStep(isAutomatic: Bool = false) {
        if currentStep < steps.count - 1 {
            if !isAutomatic {
                playStepChangeSound()
            }
            currentStep += 1
            // 触发强制滚动
            triggerScrollToStep(currentStep)
        }
    }

    private func goToPreviousStep(isAutomatic: Bool = false) {
        if currentStep > 0 {
            if !isAutomatic {
                playStepChangeSound()
            }
            currentStep -= 1
            // 触发强制滚动
            triggerScrollToStep(currentStep)
        }
    }

    // 触发滚动到指定步骤
    private func triggerScrollToStep(_ step: Int) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
            shouldScrollToStep = step
        }
    }

    // 开始自动滚动（如果需要）
    private func startAutoScrollIfNeeded() {
        guard currentStep < steps.count else { return }

        let currentStepItem = steps[currentStep]

        // 只有有计时器的步骤才启动自动滚动
        if let timerString = currentStepItem.timer, !timerString.isEmpty {
            let timerSeconds = parseTimerString(timerString)
            if timerSeconds > 0 {
                startTimerBasedAutoScroll(duration: TimeInterval(timerSeconds))
            }
        }
    }

    // 基于计时器的自动滚动
    private func startTimerBasedAutoScroll(duration: TimeInterval) {
        stopAutoScroll()

        let scrollInterval: TimeInterval = 1.0 // 每1秒更新一次
        let totalSteps = Int(duration / scrollInterval)
        var currentScrollStep = 0

        autoScrollTimer = Timer.scheduledTimer(withTimeInterval: scrollInterval, repeats: true) { timer in
            currentScrollStep += 1
            let progress = min(Double(currentScrollStep) / Double(totalSteps), 1.0)

            // 计算滚动位置
            DispatchQueue.main.async {
                if progress <= 0.1 {
                    // 开始阶段，保持在顶部
                    autoScrollTarget = "content-top"
                    autoScrollAnchor = .top
                } else if progress >= 0.9 {
                    // 结束阶段，滚动到底部
                    autoScrollTarget = "content-bottom"
                    autoScrollAnchor = .bottom
                } else {
                    // 中间阶段，根据进度平滑滚动
                    let adjustedProgress = (progress - 0.1) / 0.8 // 调整进度范围到0.1-0.9
                    autoScrollTarget = "content-text"
                    if adjustedProgress < 0.5 {
                        autoScrollAnchor = .center
                    } else {
                        autoScrollAnchor = .bottom
                    }
                }
            }

            if progress >= 1.0 {
                timer.invalidate()
            }
        }
    }



    // 停止自动滚动
    private func stopAutoScroll() {
        autoScrollTimer?.invalidate()
        autoScrollTimer = nil
    }

    // 解析计时器字符串，返回秒数
    private func parseTimerString(_ timerString: String) -> Int {
        let components = timerString.split(separator: ":")
        if components.count == 2, let minutes = Int(components[0]), let seconds = Int(components[1]) {
            return minutes * 60 + seconds
        } else if components.count == 1, let seconds = Int(components[0]) {
            return seconds
        }
        return 0
    }
    
    // 播放步骤切换音效
    private func playStepChangeSound() {
        AudioServicesPlaySystemSound(1104) // 使用系统提示音
    }
    
    // 播放完成音效
    private func playCompletionSound() {
        AudioServicesPlaySystemSound(1016) // 使用不同的系统提示音
    }
}

// 时间线步骤视图
struct TimelineStepView: View {
    let step: BrewingStepItem
    let index: Int
    let currentStep: Int
    let isLast: Bool
    let onTap: () -> Void
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 时间线部分
            VStack(spacing: 0) {
                // 步骤圆圈
                ZStack {
                    Circle()
                        .fill(index <= currentStep ? Color.functionText : Color.secondaryText.opacity(0.3))
                        .frame(width: 28, height: 28)

                    Text("\(index + 1)")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.primaryBg)
                }

                // 连接线（除了最后一个步骤）
                if !isLast {
                    Rectangle()
                        .fill(index < currentStep ? Color.functionText : Color.secondaryText.opacity(0.3))
                        .frame(width: 2)
                        .frame(minHeight: 40) // 使用minHeight而不是固定height
                }
            }
            .frame(maxHeight: .infinity) // 让时间线部分填满整个高度

            // 步骤内容 - 修改当前步骤和已播放步骤的文本颜色
            VStack(alignment: .leading, spacing: 4) {
                // 在列表中，当前步骤和已播放步骤使用更深的颜色
                if index <= currentStep {
                    AdaptiveHighlightedText(
                        step.text,
                        font: .subheadline,
                        foregroundColor: Color.functionText,  // 使用更明显的功能文本颜色
                        textAlignment: .leading
                    )
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .lineSpacing(2)
                    .lineLimit(nil)
                    .multilineTextAlignment(.leading)
                    .fixedSize(horizontal: false, vertical: true)
                } else {
                    // 未播放步骤保持原样
                    AdaptiveHighlightedText(
                        step.text,
                        font: .subheadline,
                        foregroundColor: Color.detailText,
                        textAlignment: .leading
                    )
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .lineSpacing(2)
                    .lineLimit(nil)
                    .multilineTextAlignment(.leading)
                    .fixedSize(horizontal: false, vertical: true)
                }

                if let timer = step.timer, !timer.isEmpty {
                    HStack {
                        Image(systemName: "timer")
                        Text(timer)
                    }
                    .font(.caption)
                    .foregroundColor(index <= currentStep ? Color.functionText : Color.detailText.opacity(0.7))
                }
            }
            .padding(.bottom, 16)
        }
        .contentShape(Rectangle())
        .onTapGesture(perform: onTap)
    }
}

// 增强版计时器视图
struct EnhancedStepTimerView: View {
    let timerString: String
    let onTimerComplete: () -> Void
    let autoStart: Bool

    @State private var isRunning = false
    @State private var isCompleted = false
    @State private var timeRemaining: Int = 0
    @State private var totalTime: Int = 0
    @State private var timer: Timer? = nil
    @State private var hasAppeared = false

    init(timerString: String, autoStart: Bool = false, onTimerComplete: @escaping () -> Void) {
        self.timerString = timerString
        self.autoStart = autoStart
        self.onTimerComplete = onTimerComplete
        let parsedTime = Self.parseTime(timerString)
        _timeRemaining = State(initialValue: parsedTime)
        _totalTime = State(initialValue: parsedTime)
    }
    
    var body: some View {
        VStack(spacing: 4) {
            // 时间显示
            Text(formatTime(timeRemaining))
                .monospacedDigit()
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(Color.primaryBg)
            
            // 进度条和控制按钮
            HStack(spacing: 12) {
                // 播放/暂停按钮
                Button(action: { isRunning ? pauseTimer() : startTimer() }) {
                    Image(systemName: isRunning ? "pause.circle.fill" : "play.circle.fill")
                        .font(.system(size: 22))
                        .foregroundColor(.primaryBg)
                }
                
                // 进度条
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // 背景条
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.primaryBg.opacity(0.3))
                            .frame(height: 8)
                        
                        // 进度条
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.primaryBg)
                            .frame(width: geometry.size.width * progress, height: 8)
                    }
                }
                .frame(height: 8)
                
                // 重置按钮
                Button(action: { resetTimer() }) {
                    Image(systemName: "arrow.counterclockwise.circle.fill")
                        .font(.system(size: 22))
                        .foregroundColor(.primaryBg)
                }
            }
        }
        .padding(.horizontal, 8)
        .onAppear {
            // 只在第一次出现且允许自动启动时启动计时器
            if !hasAppeared {
                hasAppeared = true
                if autoStart && totalTime <= 60 {
                    startTimer()
                }
            }
        }
        .onDisappear {
            // 离开视图时停止计时器
            pauseTimer()
        }
    }
    
    private var progress: CGFloat {
        if totalTime == 0 { return 0 }
        return CGFloat(totalTime - timeRemaining) / CGFloat(totalTime)
    }
    
    static func parseTime(_ timeString: String) -> Int {
        let components = timeString.split(separator: ":")
        if components.count == 2, let minutes = Int(components[0]), let seconds = Int(components[1]) {
            return minutes * 60 + seconds
        }
        return 0
    }
    
    private func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%d:%02d", minutes, remainingSeconds)
    }
    
    private func startTimer() {
        guard !isRunning && !isCompleted && timeRemaining > 0 else { return }
        
        isRunning = true
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            if timeRemaining > 0 {
                timeRemaining -= 1
                if timeRemaining == 0 {
                    completeTimer()
                }
            }
        }
    }
    
    private func pauseTimer() {
        timer?.invalidate()
        timer = nil
        isRunning = false
    }
    
    private func resetTimer() {
        pauseTimer()
        timeRemaining = totalTime
        isCompleted = false
    }
    
    private func completeTimer() {
        pauseTimer()
        isCompleted = true
        
        // 播放完成音效
        AudioServicesPlaySystemSound(1007) // 系统提示音
        
        // 调用完成回调
        onTimerComplete()
    }
}

// 步骤指引专用计时器视图
struct StepGuideTimerView: View {
    let timerString: String
    let stepIndex: Int
    let isLastStep: Bool
    let onTimerComplete: () -> Void

    @State private var isRunning = false
    @State private var isCompleted = false
    @State private var timeRemaining: Int = 0
    @State private var totalTime: Int = 0
    @State private var timer: Timer? = nil
    @State private var currentStepIndex: Int = -1

    init(timerString: String, stepIndex: Int, isLastStep: Bool, onTimerComplete: @escaping () -> Void) {
        self.timerString = timerString
        self.stepIndex = stepIndex
        self.isLastStep = isLastStep
        self.onTimerComplete = onTimerComplete
        let parsedTime = EnhancedStepTimerView.parseTime(timerString)
        _timeRemaining = State(initialValue: parsedTime)
        _totalTime = State(initialValue: parsedTime)
    }

    var body: some View {
        VStack(spacing: 4) {
            // 时间显示
            Text(formatTime(timeRemaining))
                .monospacedDigit()
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(Color.primaryBg)

            // 进度条和控制按钮
            HStack(spacing: 12) {
                // 播放/暂停按钮
                Button(action: {
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                    isRunning ? pauseTimer() : startTimer()
                }) {
                    Image(systemName: isRunning ? "pause.circle.fill" : "play.circle.fill")
                        .font(.system(size: 22))
                        .foregroundColor(.primaryBg)
                }

                // 进度条
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // 背景条
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.primaryBg.opacity(0.3))
                            .frame(height: 8)

                        // 进度条
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.primaryBg)
                            .frame(width: geometry.size.width * progress, height: 8)
                    }
                }
                .frame(height: 8)

                // 重置按钮
                Button(action: {
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                    resetTimer()
                }) {
                    Image(systemName: "arrow.counterclockwise.circle.fill")
                        .font(.system(size: 22))
                        .foregroundColor(.primaryBg)
                }
            }
        }
        .padding(.horizontal, 8)
        .onChange(of: stepIndex) { newStepIndex in
            // 当步骤改变时，重置计时器并根据条件自动启动
            if newStepIndex != currentStepIndex {
                currentStepIndex = newStepIndex
                resetTimer()

                // 自动启动计时器（60秒以内的计时器）
                if totalTime <= 60 {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        startTimer()
                    }
                }
            }
        }
        .onAppear {
            currentStepIndex = stepIndex
            // 首次出现时，如果是60秒以内的计时器则自动启动
            if totalTime <= 60 {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    startTimer()
                }
            }
        }
        .onDisappear {
            // 离开视图时停止计时器
            pauseTimer()
        }
    }

    private var progress: CGFloat {
        if totalTime == 0 { return 0 }
        return CGFloat(totalTime - timeRemaining) / CGFloat(totalTime)
    }

    private func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%d:%02d", minutes, remainingSeconds)
    }

    private func startTimer() {
        guard !isRunning && !isCompleted && timeRemaining > 0 else { return }

        isRunning = true
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            if timeRemaining > 0 {
                timeRemaining -= 1
                if timeRemaining == 0 {
                    completeTimer()
                }
            }
        }
    }

    private func pauseTimer() {
        timer?.invalidate()
        timer = nil
        isRunning = false
    }

    private func resetTimer() {
        pauseTimer()
        timeRemaining = totalTime
        isCompleted = false
    }

    private func completeTimer() {
        pauseTimer()
        isCompleted = true

        // 根据是否为最后一步播放不同音效
        if isLastStep {
            // 最后一步播放完成音效
            AudioServicesPlaySystemSound(1007) // 系统提示音
        } else {
            // 非最后步骤播放自动切换音效（与手动切换区分）
            AudioServicesPlaySystemSound(1003) // 自动切换音效，更轻柔
        }

        // 调用完成回调
        onTimerComplete()
    }
}

#if DEBUG
struct BrewLogDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            BrewLogDetailView(record: BrewingRecord.preview)
        }
        .environmentObject(AuthService.shared)
    }
}
#endif
