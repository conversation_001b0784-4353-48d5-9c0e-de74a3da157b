import SwiftUI
import UIKit

// 差异项数据模型
struct DiffItem: Identifiable {
    let id = UUID()
    let title: String
    let value1: String
    let value2: String
    let isDifferent: Bool
}

// 用于传递高度信息的PreferenceKey
struct HeightPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = max(value, nextValue())
    }
}

// 拆分视图组件 - 类似git-split-diffs的样式，支持折叠
struct SplitDiffView: View {
    let title: String
    let items: [DiffItem]
    let showDifferencesOnly: Bool
    
    @State private var isExpanded: Bool = true
    
    // 根据showDifferencesOnly过滤项目
    private var filteredItems: [DiffItem] {
        showDifferencesOnly ? items.filter { $0.isDifferent } : items
    }
    
    // 计算差异数量
    private var diffCount: Int {
        items.filter { $0.isDifferent }.count
    }
    
    // 判断是否有内容显示
    private var hasContent: Bool {
        !filteredItems.isEmpty
    }
    
    var body: some View {
        // 只有在有内容可显示时才渲染视图
        Group {
            if hasContent {
                VStack(alignment: .leading, spacing: 0) {
                    // 标题栏（可点击折叠）
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            isExpanded.toggle()
                        }
                    }) {
                        HStack {
                            Text(title)
                                .font(.headline)
                                .foregroundColor(.primaryText)
                            
                            // 显示差异数量
                            if diffCount > 0 {
                                Text("\(diffCount) 处差异")
                                    .font(.caption)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.primaryAccent)
                                    .cornerRadius(10)
                            }
                            
                            Spacer()
                            
                            // 折叠/展开图标
                            Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                                .foregroundColor(.functionText)
                                .font(.system(size: 14, weight: .medium))
                                .padding(4)
                                .background(Color.secondaryBg.opacity(0.5))
                                .clipShape(Circle())
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.secondaryBg)
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    // 差异项列表（可折叠）
                    if isExpanded {
                        VStack(spacing: 1) {
                            ForEach(filteredItems) { item in
                                VStack(spacing: 0) {
                                    // 项目标题
                                    HStack {
                                        Text(item.title)
                                            .font(.subheadline)
                                            .foregroundColor(.functionText)
                                            .padding(.horizontal, 16)
                                            .padding(.vertical, 8)
                                        
                                        Spacer()
                                        
                                        // 差异指示器
                                        if item.isDifferent {
                                            Image(systemName: "exclamationmark.triangle.fill")
                                                .foregroundColor(.primaryAccent)
                                                .font(.caption)
                                                .padding(.trailing, 16)
                                        }
                                    }
                                    .background(Color.secondaryBg.opacity(0.5))
                                    
                                    // 差异内容
                                    HStack(spacing: 0) {
                                        // 左侧值
                                        Text(item.value1)
                                            .font(.body)
                                            .padding(12)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .fixedSize(horizontal: false, vertical: true) // 允许垂直扩展以适应长文本
                                            .background(
                                                item.isDifferent ?
                                                Color.red.opacity(0.08) :
                                                Color.clear
                                            )
                                        
                                        // 分隔线
                                        Divider()
                                            .background(Color.primary.opacity(0.1))
                                        
                                        // 右侧值
                                        Text(item.value2)
                                            .font(.body)
                                            .padding(12)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .fixedSize(horizontal: false, vertical: true) // 允许垂直扩展以适应长文本
                                            .background(
                                                item.isDifferent ?
                                                Color.green.opacity(0.08) :
                                                Color.clear
                                            )
                                    }
                                }
                                .background(Color.primaryBg)
                                .cornerRadius(0)
                                .overlay(
                                    Rectangle()
                                        .frame(height: 1)
                                        .foregroundColor(Color.secondaryBg)
                                        .offset(y: 0.5),
                                    alignment: .bottom
                                )
                            }
                        }
                        .transition(.move(edge: .top).combined(with: .opacity))
                    }
                }
                .background(Color.primaryBg)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.03), radius: 2, x: 0, y: 1)
                .padding(.horizontal, 16)
            }
        }
    }
}

struct CompareView: View {
    let record1: BrewingRecord
    let record2: BrewingRecord
    
    // 状态变量：是否只显示差异项
    @State private var showDifferencesOnly: Bool = false
    
    // 状态变量：标题区域的最大高度
    @State private var titleMaxHeight: CGFloat = 0
    
    // 根据评分和时间确定哪个记录是正方（绿色）
    private var isRecord1Positive: Bool {
        if record1.ratingLevel != record2.ratingLevel {
            return record1.ratingLevel > record2.ratingLevel
        } else {
            return record1.createdAt > record2.createdAt
        }
    }
    
    // 获取正方记录
    private var positiveRecord: BrewingRecord {
        return isRecord1Positive ? record1 : record2
    }
    
    // 获取反方记录
    private var negativeRecord: BrewingRecord {
        return isRecord1Positive ? record2 : record1
    }

    @Environment(\.dismiss) private var dismiss
    @Environment(\.horizontalSizeClass) private var horizontalSizeClass
    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        ZStack(alignment: .top) {
            // 固定的顶部导航栏
            ZStack {
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Text("返回")
                            .foregroundColor(.linkText)
                    }
                    
                    Spacer()
                    
                    Button {
                        withAnimation {
                            showDifferencesOnly.toggle()
                        }
                    } label: {
                        Image(systemName: showDifferencesOnly ? "scope" : "dot.scope")
                            .foregroundColor(.functionText)
                            .font(.system(size: 16))
                    }
                    .help(showDifferencesOnly ? "显示所有内容" : "只看差异")
                }
                
                Text("对比记录")
                    .font(.headline)
                    .foregroundColor(.primaryText)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(Color.secondaryBg)
            .zIndex(1)
            
            ScrollView {
                VStack(spacing: 20) {
                    // 添加一个占位符，高度等于导航栏高度
                    Color.clear
                        .frame(height: 52)
                    
                    // 标题部分：显示两个记录的配方名称和时间
                    HStack(spacing: 0) {
                        // 使用GeometryReader获取整体宽度
                        GeometryReader { geometry in
                            HStack(spacing: 0) {
                                // 反方的标题和副标题
                                VStack(alignment: .leading, spacing: 4) {
                                    HStack(alignment: .top, spacing: 6) {
                                        Circle()
                                            .fill(Color.red.opacity(0.8))
                                            .frame(width: 10, height: 10)
                                            .padding(.top, 4) // 让圆点与文本顶部对齐
                                        
                                        Text(negativeRecord.recipeName ?? "无名配方")
                                            .font(.headline)
                                            .foregroundColor(.primaryText)
                                            .fixedSize(horizontal: false, vertical: true) // 允许文本换行
                                            .multilineTextAlignment(.leading)
                                    }
                                    
                                    Text(formatDate(negativeRecord.createdAt))
                                        .font(.caption)
                                        .foregroundColor(.detailText)
                                }
                                .padding(.vertical, 12)
                                .padding(.horizontal, 16)
                                .frame(width: geometry.size.width / 2, height: titleMaxHeight > 0 ? titleMaxHeight : nil, alignment: .leading)
                                .background(Color.secondaryBg)
                                .cornerRadius(12, corners: [.topLeft, .bottomLeft])
                                // 测量此视图的高度
                                .background(
                                    GeometryReader { geo in
                                        Color.clear
                                            .preference(key: HeightPreferenceKey.self, value: geo.size.height)
                                    }
                                )
                                
                                // 分隔线
                                Rectangle()
                                    .fill(Color.secondaryBg)
                                    .frame(width: 1)
                                
                                // 正方的标题和副标题
                                VStack(alignment: .leading, spacing: 4) {
                                    HStack(alignment: .top, spacing: 6) {
                                        Circle()
                                            .fill(Color.green.opacity(0.8))
                                            .frame(width: 10, height: 10)
                                            .padding(.top, 4) // 让圆点与文本顶部对齐
                                        
                                        Text(positiveRecord.recipeName ?? "无名配方")
                                            .font(.headline)
                                            .foregroundColor(.primaryText)
                                            .fixedSize(horizontal: false, vertical: true) // 允许文本换行
                                            .multilineTextAlignment(.leading)
                                    }
                                    
                                    Text(formatDate(positiveRecord.createdAt))
                                        .font(.caption)
                                        .foregroundColor(.detailText)
                                }
                                .padding(.vertical, 12)
                                .padding(.horizontal, 16)
                                .frame(width: geometry.size.width / 2, height: titleMaxHeight > 0 ? titleMaxHeight : nil, alignment: .leading)
                                .background(Color.secondaryBg)
                                .cornerRadius(12, corners: [.topRight, .bottomRight])
                                // 测量此视图的高度
                                .background(
                                    GeometryReader { geo in
                                        Color.clear
                                            .preference(key: HeightPreferenceKey.self, value: geo.size.height)
                                    }
                                )
                            }
                        }
                        // 使用PreferenceKey获取最大高度
                        .onPreferenceChange(HeightPreferenceKey.self) { height in
                            self.titleMaxHeight = max(self.titleMaxHeight, height)
                        }
                        .frame(height: titleMaxHeight > 0 ? titleMaxHeight : nil)
                        .animation(.easeInOut(duration: 0.2), value: titleMaxHeight)
                    }
                    .padding(.horizontal, 16)
                    .shadow(color: Color.black.opacity(0.03), radius: 2, x: 0, y: 1)
                    
                    // 1. 冲煮信息
                    let brewingInfoItems = getBrewingInfoItems()
                    SplitDiffView(
                        title: "冲煮信息",
                        items: brewingInfoItems,
                        showDifferencesOnly: showDifferencesOnly
                    )
                    
                    // 2. 用豆和器具
                    let beanAndEquipmentItems = getBeanAndEquipmentItems()
                    SplitDiffView(
                        title: "用豆和器具",
                        items: beanAndEquipmentItems,
                        showDifferencesOnly: showDifferencesOnly
                    )

                    // 3. 测量数据
                    let measurementItems = getMeasurementItems()
                    SplitDiffView(
                        title: "测量数据",
                        items: measurementItems,
                        showDifferencesOnly: showDifferencesOnly
                    )
                    
                    // 4. 环境数据
                    let environmentItems = getEnvironmentItems()
                    SplitDiffView(
                        title: "环境数据",
                        items: environmentItems,
                        showDifferencesOnly: showDifferencesOnly
                    )
                    
                    // 5. 配方步骤
                    if !negativeRecord.steps.isEmpty || !positiveRecord.steps.isEmpty {
                        let stepsText1 = formatSteps(negativeRecord.steps)
                        let stepsText2 = formatSteps(positiveRecord.steps)
                        
                        let isDifferent = stepsText1 != stepsText2
                        
                        // 只有在显示所有项或有差异时才显示
                        if !showDifferencesOnly || isDifferent {
                            SplitDiffView(
                                title: "配方步骤",
                                items: [
                                    DiffItem(
                                        title: "步骤详情",
                                        value1: stepsText1.isEmpty ? "无" : stepsText1,
                                        value2: stepsText2.isEmpty ? "无" : stepsText2,
                                        isDifferent: isDifferent
                                    )
                                ],
                                showDifferencesOnly: showDifferencesOnly
                            )
                        }
                    }
                    
                    // 6. 品鉴笔记
                    let tastingNoteItems = getTastingNoteItems()
                    SplitDiffView(
                        title: "品鉴笔记",
                        items: tastingNoteItems,
                        showDifferencesOnly: showDifferencesOnly
                    )
                }
                .padding(.bottom, 16)
            }
        }
        .background(Color.primaryBg)
        .navigationBarHidden(true)
    }
    
    // 获取冲煮信息项
    private func getBrewingInfoItems() -> [DiffItem] {
        var items: [DiffItem] = []
        
        // 综合评分
        if negativeRecord.ratingLevel > 0 || positiveRecord.ratingLevel > 0 {
            items.append(DiffItem(
                title: "综合评分",
                value1: "\(negativeRecord.ratingLevel)",
                value2: "\(positiveRecord.ratingLevel)",
                isDifferent: negativeRecord.ratingLevel != positiveRecord.ratingLevel
            ))
        }
        
        // 赛道（冲煮方法）
        items.append(DiffItem(
            title: "赛道",
            value1: getMethodDisplayText(method: negativeRecord.brewingEquipment.brewMethod ?? "其他"),
            value2: getMethodDisplayText(method: positiveRecord.brewingEquipment.brewMethod ?? "其他"),
            isDifferent: negativeRecord.brewingEquipment.brewMethod != positiveRecord.brewingEquipment.brewMethod
        ))
        
        // 备注
        if !negativeRecord.notes.isEmpty || !positiveRecord.notes.isEmpty {
            items.append(DiffItem(
                title: "备注",
                value1: negativeRecord.notes.isEmpty ? "无" : negativeRecord.notes,
                value2: positiveRecord.notes.isEmpty ? "无" : positiveRecord.notes,
                isDifferent: negativeRecord.notes != positiveRecord.notes
            ))
        }
        
        return items
    }
    
    /// 将冲煮方式代码转换为显示名称
    private func getMethodDisplayText(method: String) -> String {
        // 转换为大写以便统一处理
        let uppercaseMethod = method.uppercased()
        
        switch uppercaseMethod {
        case "ESPRESSO":
            return "意式"
        case "POUR_OVER":
            return "手冲"
        case "AEROPRESS":
            return "爱乐压"
        case "COLD_BREW":
            return "冷萃"
        case "MOKA_POT":
            return "摩卡壶"
        case "FRENCH_PRESS":
            return "法压壶"
        case "AUTO_DRIP":
            return "自动滴滤"
        default:
            return method
        }
    }
    
    // 获取用豆和器具项
    private func getBeanAndEquipmentItems() -> [DiffItem] {
        var items: [DiffItem] = []
        
        // 咖啡豆
        items.append(DiffItem(
            title: "咖啡豆",
            value1: "\(negativeRecord.coffeeBean.name) - \(negativeRecord.coffeeBean.roaster)",
            value2: "\(positiveRecord.coffeeBean.name) - \(positiveRecord.coffeeBean.roaster)",
            isDifferent: negativeRecord.coffeeBean.id != positiveRecord.coffeeBean.id
        ))
        
        // 冲煮器具
        items.append(DiffItem(
            title: "冲煮器具",
            value1: negativeRecord.brewingEquipment.brand != nil ? "\(negativeRecord.brewingEquipment.brand!) \(negativeRecord.brewingEquipment.name)" : negativeRecord.brewingEquipment.name,
            value2: positiveRecord.brewingEquipment.brand != nil ? "\(positiveRecord.brewingEquipment.brand!) \(positiveRecord.brewingEquipment.name)" : positiveRecord.brewingEquipment.name,
            isDifferent: negativeRecord.brewingEquipment.id != positiveRecord.brewingEquipment.id
        ))
        
        // 磨豆机
        if negativeRecord.grindingEquipment != nil || positiveRecord.grindingEquipment != nil {
            items.append(DiffItem(
                title: "磨豆机",
                value1: negativeRecord.grindingEquipment != nil ? 
                    (negativeRecord.grindingEquipment!.brand != nil ? 
                        "\(negativeRecord.grindingEquipment!.brand!) \(negativeRecord.grindingEquipment!.name)" : 
                        negativeRecord.grindingEquipment!.name) : 
                    "无",
                value2: positiveRecord.grindingEquipment != nil ? 
                    (positiveRecord.grindingEquipment!.brand != nil ? 
                        "\(positiveRecord.grindingEquipment!.brand!) \(positiveRecord.grindingEquipment!.name)" : 
                        positiveRecord.grindingEquipment!.name) : 
                    "无",
                isDifferent: negativeRecord.grindingEquipment?.id != positiveRecord.grindingEquipment?.id
            ))
        }
        
        // 研磨设置
        if !negativeRecord.grindSize.isEmpty || !positiveRecord.grindSize.isEmpty {
            items.append(DiffItem(
                title: "研磨设置",
                value1: negativeRecord.grindSize,
                value2: positiveRecord.grindSize,
                isDifferent: negativeRecord.grindSize != positiveRecord.grindSize
            ))
        }
        
        // 小工具组合
        if negativeRecord.gadgetKit != nil || positiveRecord.gadgetKit != nil {
            items.append(DiffItem(
                title: "小工具组合",
                value1: negativeRecord.gadgetKit?.name ?? "无",
                value2: positiveRecord.gadgetKit?.name ?? "无",
                isDifferent: negativeRecord.gadgetKit?.id != positiveRecord.gadgetKit?.id
            ))
        }
        
        // 小工具
        if (negativeRecord.gadgets != nil && !negativeRecord.gadgets!.isEmpty) || (positiveRecord.gadgets != nil && !positiveRecord.gadgets!.isEmpty) {
            let gadgetsText1 = formatGadgetsList(negativeRecord.gadgets)
            let gadgetsText2 = formatGadgetsList(positiveRecord.gadgets)
            
            items.append(DiffItem(
                title: "小工具",
                value1: gadgetsText1.isEmpty ? "无" : gadgetsText1,
                value2: gadgetsText2.isEmpty ? "无" : gadgetsText2,
                isDifferent: (negativeRecord.gadgets?.map { $0.id } ?? []) != (positiveRecord.gadgets?.map { $0.id } ?? [])
            ))
        }
        
        return items
    }
    
    // 格式化小工具列表为每行一个的形式
    private func formatGadgetsList(_ gadgets: [Equipment]?) -> String {
        guard let gadgets = gadgets, !gadgets.isEmpty else { return "无" }
        
        return gadgets.map { gadget in
            if let brand = gadget.brand, !brand.isEmpty {
                return "• \(brand) \(gadget.name)"
            } else {
                return "• \(gadget.name)"
            }
        }.joined(separator: "\n")
    }
    
    // 获取测量数据项
    private func getMeasurementItems() -> [DiffItem] {
        var items: [DiffItem] = []
        
        // 粉重
        if negativeRecord.doseWeight > 0 || positiveRecord.doseWeight > 0 {
            items.append(DiffItem(
                title: "粉重",
                value1: "\(negativeRecord.doseWeight)g",
                value2: "\(positiveRecord.doseWeight)g",
                isDifferent: negativeRecord.doseWeight != positiveRecord.doseWeight
            ))
        }
        
        // 液重
        if negativeRecord.yieldWeight > 0 || positiveRecord.yieldWeight > 0 {
            items.append(DiffItem(
                title: "液重",
                value1: "\(negativeRecord.yieldWeight)g",
                value2: "\(positiveRecord.yieldWeight)g",
                isDifferent: negativeRecord.yieldWeight != positiveRecord.yieldWeight
            ))
        }
        
        // 水温
        if negativeRecord.waterTemperature > 0 || positiveRecord.waterTemperature > 0 {
            items.append(DiffItem(
                title: "水温",
                value1: "\(negativeRecord.waterTemperature)℃",
                value2: "\(positiveRecord.waterTemperature)℃",
                isDifferent: negativeRecord.waterTemperature != positiveRecord.waterTemperature
            ))
        }
        
        // 萃取时间
        if negativeRecord.brewingTime > 0 || positiveRecord.brewingTime > 0 {
            items.append(DiffItem(
                title: "萃取时间",
                value1: formatDuration(negativeRecord.brewingTime),
                value2: formatDuration(positiveRecord.brewingTime),
                isDifferent: negativeRecord.brewingTime != positiveRecord.brewingTime
            ))
        }
        
        // 粉水比 - 只有当粉重和液重都有值时才显示
        if (negativeRecord.doseWeight > 0 && negativeRecord.yieldWeight > 0) || (positiveRecord.doseWeight > 0 && positiveRecord.yieldWeight > 0) {
            let ratio1 = negativeRecord.doseWeight > 0 ? negativeRecord.yieldWeight / negativeRecord.doseWeight : 0
            let ratio2 = positiveRecord.doseWeight > 0 ? positiveRecord.yieldWeight / positiveRecord.doseWeight : 0
            
            items.append(DiffItem(
                title: "粉水比 (允许0.1误差)",
                value1: negativeRecord.doseWeight > 0 ? String(format: "1:%.1f", ratio1) : "无",
                value2: positiveRecord.doseWeight > 0 ? String(format: "1:%.1f", ratio2) : "无",
                isDifferent: abs(ratio1 - ratio2) > 0.1
            ))
        }
        
        return items
    }
    
    // 获取环境数据项
    private func getEnvironmentItems() -> [DiffItem] {
        var items: [DiffItem] = []
        
        // 水质
        if (negativeRecord.waterQuality != nil && !negativeRecord.waterQuality!.isEmpty) || 
           (positiveRecord.waterQuality != nil && !positiveRecord.waterQuality!.isEmpty) {
            items.append(DiffItem(
                title: "水质",
                value1: (negativeRecord.waterQuality != nil && !negativeRecord.waterQuality!.isEmpty) ? negativeRecord.waterQuality! : "无",
                value2: (positiveRecord.waterQuality != nil && !positiveRecord.waterQuality!.isEmpty) ? positiveRecord.waterQuality! : "无",
                isDifferent: negativeRecord.waterQuality != positiveRecord.waterQuality
            ))
        }
        
        // 室温
        if (negativeRecord.roomTemperature != nil && negativeRecord.roomTemperature! > 0) || 
           (positiveRecord.roomTemperature != nil && positiveRecord.roomTemperature! > 0) {
            items.append(DiffItem(
                title: "室温",
                value1: negativeRecord.roomTemperature != nil && negativeRecord.roomTemperature! > 0 ? "\(negativeRecord.roomTemperature!)℃" : "无",
                value2: positiveRecord.roomTemperature != nil && positiveRecord.roomTemperature! > 0 ? "\(positiveRecord.roomTemperature!)℃" : "无",
                isDifferent: negativeRecord.roomTemperature != positiveRecord.roomTemperature
            ))
        }
        
        // 环境湿度
        if (negativeRecord.roomHumidity != nil && negativeRecord.roomHumidity! > 0) || 
           (positiveRecord.roomHumidity != nil && positiveRecord.roomHumidity! > 0) {
            items.append(DiffItem(
                title: "环境湿度",
                value1: negativeRecord.roomHumidity != nil && negativeRecord.roomHumidity! > 0 ? "\(negativeRecord.roomHumidity!)%" : "无",
                value2: positiveRecord.roomHumidity != nil && positiveRecord.roomHumidity! > 0 ? "\(positiveRecord.roomHumidity!)%" : "无",
                isDifferent: negativeRecord.roomHumidity != positiveRecord.roomHumidity
            ))
        }
        
        return items
    }
    
    // 获取品鉴笔记项
    private func getTastingNoteItems() -> [DiffItem] {
        var items: [DiffItem] = []
        
        // 风味标签
        let flavorTags1 = negativeRecord.flavorTags.map { $0.name }.joined(separator: ", ")
        let flavorTags2 = positiveRecord.flavorTags.map { $0.name }.joined(separator: ", ")
        if !flavorTags1.isEmpty || !flavorTags2.isEmpty {
            items.append(DiffItem(
                title: "风味标签",
                value1: flavorTags1.isEmpty ? "无" : flavorTags1,
                value2: flavorTags2.isEmpty ? "无" : flavorTags2,
                isDifferent: negativeRecord.flavorTags.map { $0.name } != positiveRecord.flavorTags.map { $0.name }
            ))
        }
        
        // 香气
        if negativeRecord.aroma > 0 || positiveRecord.aroma > 0 {
            items.append(DiffItem(
                title: "香气",
                value1: "\(negativeRecord.aroma)",
                value2: "\(positiveRecord.aroma)",
                isDifferent: negativeRecord.aroma != positiveRecord.aroma
            ))
        }
        
        // 酸度
        if negativeRecord.acidity > 0 || positiveRecord.acidity > 0 {
            items.append(DiffItem(
                title: "酸度",
                value1: "\(negativeRecord.acidity)",
                value2: "\(positiveRecord.acidity)",
                isDifferent: negativeRecord.acidity != positiveRecord.acidity
            ))
        }
        
        // 甜度
        if negativeRecord.sweetness > 0 || positiveRecord.sweetness > 0 {
            items.append(DiffItem(
                title: "甜度",
                value1: "\(negativeRecord.sweetness)",
                value2: "\(positiveRecord.sweetness)",
                isDifferent: negativeRecord.sweetness != positiveRecord.sweetness
            ))
        }
        
        // 醇度
        if negativeRecord.body > 0 || positiveRecord.body > 0 {
            items.append(DiffItem(
                title: "醇度",
                value1: "\(negativeRecord.body)",
                value2: "\(positiveRecord.body)",
                isDifferent: negativeRecord.body != positiveRecord.body
            ))
        }
        
        // 余韵
        if negativeRecord.aftertaste > 0 || positiveRecord.aftertaste > 0 {
            items.append(DiffItem(
                title: "余韵",
                value1: "\(negativeRecord.aftertaste)",
                value2: "\(positiveRecord.aftertaste)",
                isDifferent: negativeRecord.aftertaste != positiveRecord.aftertaste
            ))
        }
        
        return items
    }

    private func formatDuration(_ seconds: Int) -> String {
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        let remainingSeconds = seconds % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, remainingSeconds)
        } else {
            return String(format: "%d:%02d", minutes, remainingSeconds)
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
    
    private func formatSteps(_ steps: [BrewingStepItem]) -> String {
        return steps.map { step in
            let timerInfo = step.timer != nil ? " (\(step.timer!))" : ""
            return "\(step.order). \(step.text)\(timerInfo)"
        }.joined(separator: "\n")
    }
}

#if DEBUG
struct CompareView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            CompareView(record1: BrewingRecord.preview, record2: {
                let record = BrewingRecord.preview
                // 创建一个不同的记录用于预览

                // 修改一些属性以显示差异
                return BrewingRecord(
                    id: UUID().hashValue,
                    recipeName: "修改后的配方",
                    coffeeBean: CoffeeBean.preview,
                    brewingEquipment: Equipment.previewBrewing,
                    grindingEquipment: Equipment.previewGrinding,
                    grindSize: "25", // 不同的研磨度
                    doseWeight: 18.0, // 不同的粉量
                    yieldWeight: 300.0, // 不同的产出
                    waterTemperature: 90.0, // 不同的水温
                    brewingTime: 210, // 不同的时间
                    ratingLevel: 8, // 不同的评分
                    ratingDisplay: "⭐️⭐️⭐️⭐️",
                    aroma: 5, // 不同的香气
                    acidity: 4, // 不同的酸度
                    sweetness: 5, // 不同的甜度
                    body: 4, // 不同的醇度
                    aftertaste: 5, // 不同的余韵
                    waterQuality: "纯净水", // 不同的水质
                    roomTemperature: 22.0,
                    roomHumidity: 55,
                    steps: [
                        BrewingStepItem(text: "注水80g，搅拌均匀", timer: "0:00", order: 1),
                        BrewingStepItem(text: "30秒后注水至200g", timer: "0:30", order: 2),
                        BrewingStepItem(text: "1分钟后注水至300g", timer: "1:00", order: 3)
                    ],
                    flavorTags: [],
                    tagOverlap: nil,
                    notes: "这是一个修改后的记录，用于测试对比视图",
                    createdAt: Date(),
                    gadgets: nil,
                    gadgetKit: nil
                )
            }())
        }
    }
}
#endif