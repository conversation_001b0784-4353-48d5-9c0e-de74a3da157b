import SwiftUI

// 预览视图，用于上下文菜单的预览
struct BrewLogPreviewView: View {
    let record: BrewingRecord
    // 使用可选的环境对象和主题颜色
    @EnvironmentObject private var themeManager: ThemeManager
    var themeColors: ThemeColors?
    
    // 计算是否有额外信息
    private var hasExtraInfo: Bool {
        return !record.notes.isEmpty || 
               !record.steps.isEmpty || 
               !record.flavorTags.isEmpty || 
               record.aroma > 0 || 
               record.acidity > 0 || 
               record.sweetness > 0 || 
               record.body > 0 || 
               record.aftertaste > 0 || 
               (record.waterQuality != nil && !record.waterQuality!.isEmpty) ||
               (record.roomTemperature != nil && record.roomTemperature! > 0) ||
               (record.roomHumidity != nil && record.roomHumidity! > 0)
    }
    
    // 计算属性：获取当前使用的主题颜色
    private var currentColors: ThemeColors {
        // 如果传入了themeColors，优先使用
        if let colors = themeColors {
            return colors
        }
        
        // 使用环境对象中的主题颜色
        return themeManager.currentThemeColors
    }
    
    var body: some View {
        VStack(spacing: 8) {
            // 原始卡片内容，有额外信息时隐藏卡片中的图标行，并去掉边框圆角
            BrewLogRow(record: record, showExtraInfoIcons: !hasExtraInfo, showBorderRadius: false, themeColors: currentColors)
                .padding(.bottom, hasExtraInfo ? 0 : 0)
            
            // 如果有额外信息，显示纵向布局的额外信息条目
            if hasExtraInfo {
                BrewLogExtraInfoPreview(record: record, themeColors: themeColors)
                    .padding(.horizontal, 14)  // 和BrewLogRow保持一致的水平内边距
                    .padding(.vertical, 8)
                    .frame(maxWidth: .infinity, alignment: .leading)  // 添加这一行确保内容向左对齐
            }
        }
        .background(currentColors.primaryBgColor)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
        .frame(width: UIScreen.main.bounds.width - 40)  // 控制预览宽度
    }
}

// 额外信息预览 - 纵向布局
struct BrewLogExtraInfoPreview: View {
    let record: BrewingRecord
    var themeColors: ThemeColors? = nil
    
    // 计算当前使用的主题颜色
    private var currentColors: ThemeColors {
        // 如果传入了themeColors，优先使用
        if let colors = themeColors {
            return colors
        }
        
        // 否则使用默认的系统颜色
        return ThemeColors(
            primaryTextColor: Color.primary,
            detailTextColor: Color.secondary,
            archivedTextColor: Color.gray,
            functionTextColor: Color.blue,
            secondaryTextColor: Color.orange,
            linkTextColor: Color.blue,
            noteTextColor: Color.gray,
            errorTextColor: Color.red,
            primaryAccentColor: Color.orange,
            primaryBgColor: Color(UIColor.systemBackground),
            secondaryBgColor: Color(UIColor.secondarySystemBackground),
            navbarBgColor: Color(UIColor.systemGray6),
            focusBgColor: Color(UIColor.systemGray5)
        )
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            notesSection
            stepsSection
            flavorTagsSection
            flavorRatingSection
            environmentSection
        }
        .frame(maxWidth: .infinity, alignment: .leading)  // 确保VStack内容靠左对齐
    }
    
    @ViewBuilder
    private var notesSection: some View {
        if !record.notes.isEmpty {
            PreviewInfoRow(icon: Image("note.symbols"), content: record.notes, themeColors: currentColors)
        }
    }
    
    @ViewBuilder
    private var stepsSection: some View {
        if !record.steps.isEmpty {
            PreviewInfoRow(icon: Image("steps.symbols"), content: formatStepsCount(), themeColors: currentColors)
        }
    }
    
    // 格式化步骤数量内容
    private func formatStepsCount() -> String {
        return "\(record.steps.count)个步骤"
    }
    
    @ViewBuilder
    private var flavorTagsSection: some View {
        if !record.flavorTags.isEmpty {
            PreviewInfoRow(icon: Image("flavor.symbols"), content: formatFlavorTags(), themeColors: currentColors)
        }
    }
    
    // 格式化风味标签内容
    private func formatFlavorTags() -> String {
        return record.flavorTags.map { $0.name }.joined(separator: "、")
    }
    
    @ViewBuilder
    private var flavorRatingSection: some View {
        if record.aroma > 0 || record.acidity > 0 || record.sweetness > 0 || record.body > 0 || record.aftertaste > 0 {
            PreviewInfoRow(icon: Image("radar.symbols"), content: formatFlavorRatings(), themeColors: currentColors)
        }
    }
    
    // 格式化风味评分内容
    private func formatFlavorRatings() -> String {
        return "香气\(record.aroma)、酸度\(record.acidity)、甜度\(record.sweetness)、醇度\(record.body)、余韵\(record.aftertaste)"
    }
    
    @ViewBuilder
    private var environmentSection: some View {
        let hasWaterQuality = record.waterQuality != nil && !record.waterQuality!.isEmpty
        let hasRoomTemperature = record.roomTemperature != nil && record.roomTemperature! > 0
        let hasRoomHumidity = record.roomHumidity != nil && record.roomHumidity! > 0
        
        if hasWaterQuality || hasRoomTemperature || hasRoomHumidity {
            let environmentContent = buildEnvironmentContent(
                hasWaterQuality: hasWaterQuality,
                hasRoomTemperature: hasRoomTemperature,
                hasRoomHumidity: hasRoomHumidity
            )
            
            PreviewInfoRow(
                icon: Image("environment.symbols"),
                content: environmentContent,
                themeColors: currentColors
            )
        }
    }
    
    // 提取环境信息内容构建逻辑到单独的函数
    private func buildEnvironmentContent(
        hasWaterQuality: Bool,
        hasRoomTemperature: Bool,
        hasRoomHumidity: Bool
    ) -> String {
        var content = ""
        
        if hasWaterQuality {
            content += "水质:\(record.waterQuality!)，"
        }
        
        if hasRoomTemperature {
            let temp = Double(record.roomTemperature!)
            content += "室温:\(NumberFormatters.formatTemperature(temp))°C，"
        }
        
        if hasRoomHumidity {
            content += "湿度:\(record.roomHumidity!)%"
        }
        
        return content.trimmingCharacters(in: .whitespaces)
    }
}

// 简洁的预览信息行视图，避免和已有的InfoRow组件冲突
struct PreviewInfoRow: View {
    private let iconImage: Image?
    private let iconString: String?
    let content: String
    var themeColors: ThemeColors
    
    // 接受字符串类型的icon初始化方法
    init(icon: String, content: String, themeColors: ThemeColors) {
        self.iconString = icon
        self.iconImage = nil
        self.content = content
        self.themeColors = themeColors
    }
    
    // 新增：接受Image类型的icon初始化方法
    init(icon: Image, content: String, themeColors: ThemeColors) {
        self.iconImage = icon
        self.iconString = nil
        self.content = content
        self.themeColors = themeColors
    }
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            if let iconImage = iconImage {
                iconImage
                    .foregroundColor(themeColors.linkTextColor)
                    .frame(width: 16, height: 16)
            } else if let iconString = iconString {
                Image(systemName: iconMapping(for: iconString))
                    .foregroundColor(themeColors.linkTextColor)
                    .frame(width: 16, height: 16)
            }
            
            Text(content)
                .font(.subheadline)
                .foregroundColor(themeColors.primaryTextColor)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    private func iconMapping(for customIcon: String) -> String {
        switch customIcon {
        case "note.symbols":
            return "note.text"
        case "steps.symbols":
            return "list.number"
        case "flavor.symbols":
            return "tag"
        case "radar.symbols":
            return "chart.radar"
        case "environment.symbols":
            return "thermometer"
        default:
            return "circle"
        }
    }
}

// 风味评分项
struct FlavorItem: View {
    let name: String
    let value: Double
    var themeColors: ThemeColors
    
    // 添加一个接受Int的初始化方法
    init(name: String, value: Int, themeColors: ThemeColors) {
        self.name = name
        self.value = Double(value)
        self.themeColors = themeColors
    }
    
    // 保留原有的接受Double的初始化方法
    init(name: String, value: Double, themeColors: ThemeColors) {
        self.name = name
        self.value = value
        self.themeColors = themeColors
    }
    
    var body: some View {
        VStack(spacing: 2) {
            Text(name)
                .font(.caption)
                .foregroundColor(themeColors.noteTextColor)
            
            Text(String(format: "%.1f", value))
                .font(.caption)
                .foregroundColor(themeColors.detailTextColor)
        }
    }
}

#if DEBUG
struct BrewLogPreviewView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建默认的主题颜色用于预览
        let defaultColors = ThemeColors(
            primaryTextColor: Color.primary,
            detailTextColor: Color.secondary,
            archivedTextColor: Color.gray,
            functionTextColor: Color.blue,
            secondaryTextColor: Color.orange,
            linkTextColor: Color.blue,
            noteTextColor: Color.gray,
            errorTextColor: Color.red,
            primaryAccentColor: Color.orange,
            primaryBgColor: Color(UIColor.systemBackground),
            secondaryBgColor: Color(UIColor.secondarySystemBackground),
            navbarBgColor: Color(UIColor.systemGray6),
            focusBgColor: Color(UIColor.systemGray5)
        )
        
        BrewLogPreviewView(record: BrewingRecord.preview, themeColors: defaultColors)
            .previewLayout(.sizeThatFits)
            .padding()
    }
}
#endif 
