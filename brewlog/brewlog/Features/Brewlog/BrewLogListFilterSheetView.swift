import SwiftUI
import UIKit

// 筛选Sheet视图
struct BrewLogListFilterSheetView: View {
    @ObservedObject var viewModel: BrewLogViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var tempDateFrom: Date?
    @State private var tempDateTo: Date?
    @State private var tempSearchQuery: String = ""
    @State private var tempBrewMethod: String = ""
    @State private var tempCoffeeBean: String = ""
    @State private var tempRatingRange: String = ""
    @State private var isApplying: Bool = false
    @State private var dateRangeSelection: DateRangeSelection = .month
    
    // 判断是否所有筛选项都是默认值
    private var isAllDefaultFilters: Bool {
        // 检查是否为当月的日期范围（默认值）
        let isDefaultDateRange = dateRangeSelection == .month
        
        // 检查其他筛选条件是否为空
        return isDefaultDateRange &&
               tempSearchQuery.isEmpty &&
               tempBrewMethod.isEmpty &&
               tempCoffeeBean.isEmpty &&
               tempRatingRange.isEmpty
    }
    
    enum DateRangeSelection: String {
        case none = "none"       // 初始状态或未选中状态
        case month = "month"     // 当前月
        case year = "year"       // 当前年
        case previousMonth = "previousMonth" // 上个月
        case today = "today"     // 今天
        case custom = "custom"   // 自定义日期范围
    }
    
    var body: some View {
        NavigationView {
            List {
                dateRangeSection
                
                Section(header: Text("叠加条件")) {
                    // 搜索区域
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.primary)
                        ZStack(alignment: .leading) {
                            if tempSearchQuery.isEmpty {
                                Text("需包含的关键词")
                                    .foregroundColor(Color.archivedText)
                                    .padding(.leading, 4)
                            }
                            TextField("", text: $tempSearchQuery)
                                .foregroundColor(Color.primaryText)
                                .autocapitalization(.none)
                                .disableAutocorrection(true)
                                .padding(.leading, 4)
                        }
                        
                        if !tempSearchQuery.isEmpty {
                            Button(action: {
                                withAnimation {
                                    tempSearchQuery = ""
                                }
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(Color.detailText)
                                    .padding(.trailing, 8)
                            }
                        }
                    }
                    .padding(.vertical, 4)
                    
                    // 冲煮方式选择
                    Picker("冲煮方式", selection: $tempBrewMethod) {
                        Text("全部").tag("")
                        ForEach(viewModel.brewMethods) { method in
                            Text(method.name).tag(method.id)
                        }
                    }
                    .pickerStyle(.menu)
                    .tint(Color.linkText)
                    
                    // 咖啡豆选择
                    Picker("咖啡豆", selection: $tempCoffeeBean) {
                        Text("全部").tag("")
                        ForEach(viewModel.coffeeBeans) { bean in
                            Text(bean.name).tag("\(bean.id)")
                        }
                    }
                    .pickerStyle(.menu)
                    .tint(Color.linkText)
                    
                    // 评分选择
                    Picker("评分", selection: $tempRatingRange) {
                        Text("全部").tag("")
                        Text("♥️♥️♥️♥️♥️").tag("8.1-10")
                        Text("♥️♥️♥️♥️").tag("6.1-8")
                        Text("♥️♥️♥️").tag("4.1-6")
                        Text("♥️♥️").tag("2.1-4")
                        Text("♥️").tag("0-2")
                    }
                    .pickerStyle(.menu)
                    .tint(Color.linkText)
                }
                .listRowBackground(Color.primaryBg)
            }
            .listStyle(InsetGroupedListStyle())
            .scrollContentBackground(.hidden)
            .onAppear {
                initializeFilters()
                Task {
                    await viewModel.fetchFilterOptions()
                }
            }
            .navigationTitle("")  // 移除标题
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("重置") {
                        Task {
                            await resetFilters()
                        }
                    }
                    .foregroundColor(isAllDefaultFilters ? Color.gray : Color.linkText)
                    .font(.system(size: 17, weight: .regular))
                    .disabled(isAllDefaultFilters)
                    .animation(.easeInOut(duration: 0.2), value: isAllDefaultFilters)
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("筛选") {
                        applyFilters()
                    }
                    .disabled(isApplying)
                    .font(.system(size: 17, weight: .semibold))
                    .foregroundColor(isApplying ? .gray : Color.linkText)
                }
            }
            .background(Color.secondaryBg)
            .environment(\.locale, Locale(identifier: "zh_CN"))
        }
        .presentationDetents([.height(480), .large])
        .presentationDragIndicator(.visible)
        .interactiveDismissDisabled(isApplying)
        .background(Color.secondaryBg)
        .environment(\.locale, Locale(identifier: "zh_CN"))
    }
    
    // 日期范围部分
    private var dateRangeSection: some View {
        Section(header: Text("日期范围")) {
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Text("开始日期")
                        .foregroundColor(.secondary)
                        .frame(width: 70, alignment: .leading)
                    
                    ChineseDatePicker(date: $tempDateFrom, fallbackDate: Date()) {
                        withAnimation {
                            dateRangeSelection = .custom // 手动选择日期时设置为自定义
                            print("📅 已选择开始日期: \(tempDateFrom?.description ?? "nil"), 状态已更新为: \(dateRangeSelection)")
                        }
                    }
                    .padding(.vertical, 2)
                }
                
                HStack {
                    Text("结束日期")
                        .foregroundColor(.secondary)
                        .frame(width: 70, alignment: .leading)
                    
                    ChineseDatePicker(date: $tempDateTo, fallbackDate: Date()) {
                        withAnimation {
                            dateRangeSelection = .custom // 手动选择日期时设置为自定义
                            print("📅 已选择结束日期: \(tempDateTo?.description ?? "nil"), 状态已更新为: \(dateRangeSelection)")
                        }
                    }
                    .padding(.vertical, 2)
                }
            }
            .padding(.vertical, 2)
            
            dateQuickButtons
                .padding(.top, 4)
                .padding(.bottom, 2)
        }
        .listRowBackground(Color.primaryBg)
    }
    
    // 日期快速选择按钮
    private var dateQuickButtons: some View {
        HStack(spacing: 12) {
            Spacer()
            
            Button("前月") {
                setPreviousMonth()
            }
            .buttonStyle(.bordered)
            .tint(dateRangeSelection == .previousMonth ? Color.secondaryText : nil)
            .foregroundColor(dateRangeSelection == .previousMonth ? Color.functionText : Color.primaryText)
            
            Button("本月") {
                setCurrentMonth()
            }
            .buttonStyle(.bordered)
            .tint(dateRangeSelection == .month ? Color.secondaryText : nil)
            .foregroundColor(dateRangeSelection == .month ? Color.functionText : Color.primaryText)
            
            Button("今年") {
                setCurrentYear()
            }
            .buttonStyle(.bordered)
            .tint(dateRangeSelection == .year ? Color.secondaryText : nil)
            .foregroundColor(dateRangeSelection == .year ? Color.functionText : Color.primaryText)
            
            Button("今天") {
                setToday()
            }
            .buttonStyle(.bordered)
            .tint(dateRangeSelection == .today ? Color.secondaryText : nil)
            .foregroundColor(dateRangeSelection == .today ? Color.functionText : Color.primaryText)
            
            Spacer()
        }
    }
    
    // 设置上个月日期范围
    private func setPreviousMonth() {
        let calendar = Calendar.current
        let now = Date()
        if let previousMonth = calendar.date(byAdding: .month, value: -1, to: now),
           let firstDay = calendar.date(from: calendar.dateComponents([.year, .month], from: previousMonth)),
           let lastDay = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: firstDay) {
            withAnimation {
                tempDateFrom = firstDay
                tempDateTo = lastDay
                dateRangeSelection = .previousMonth
                print("📅 选择「前月」: \(firstDay) 到 \(lastDay), 状态: \(dateRangeSelection)")
            }
        }
    }
    
    // 设置本月日期范围
    private func setCurrentMonth() {
        let calendar = Calendar.current
        let now = Date()
        if let firstDay = calendar.date(from: calendar.dateComponents([.year, .month], from: now)),
           let lastDay = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: firstDay) {
            withAnimation {
                tempDateFrom = firstDay
                tempDateTo = lastDay
                dateRangeSelection = .month
                print("📅 选择「本月」: \(firstDay) 到 \(lastDay), 状态: \(dateRangeSelection)")
            }
        }
    }
    
    // 设置本年日期范围
    private func setCurrentYear() {
        let calendar = Calendar.current
        let now = Date()
        let year = calendar.component(.year, from: now)
        if let firstDay = calendar.date(from: DateComponents(year: year, month: 1, day: 1)),
           let lastDay = calendar.date(from: DateComponents(year: year, month: 12, day: 31)) {
            withAnimation {
                tempDateFrom = firstDay
                tempDateTo = lastDay
                dateRangeSelection = .year
                print("📅 选择「今年」: \(firstDay) 到 \(lastDay), 状态: \(dateRangeSelection)")
            }
        }
    }
    
    // 设置为今天日期范围
    private func setToday() {
        let today = Date()
        let calendar = Calendar.current
        withAnimation {
            // 设置为今天一整天的日期范围
            tempDateFrom = calendar.startOfDay(for: today)
            tempDateTo = calendar.date(bySettingHour: 23, minute: 59, second: 59, of: today) ?? today
            dateRangeSelection = .today
            print("📅 选择「今天」: \(tempDateFrom?.description ?? "nil") 到 \(tempDateTo?.description ?? "nil"), 状态: \(dateRangeSelection)")
        }
    }
    
    // 初始化筛选值
    private func initializeFilters() {
        // 加载现有筛选条件
        tempDateFrom = viewModel.filterDateFrom
        tempDateTo = viewModel.filterDateTo
        tempSearchQuery = viewModel.filterSearchQuery
        tempBrewMethod = viewModel.filterBrewMethod
        tempCoffeeBean = viewModel.filterCoffeeBean
        tempRatingRange = viewModel.filterRatingRange
        
        // 如果已有日期范围，则根据日期范围确定选择状态
        if tempDateFrom != nil && tempDateTo != nil {
            updateDateRangeSelection()
        } else {
            // 如果没有日期范围，默认设置为当前月份
            setCurrentMonth()
        }
        
        // 在初始化完成后强制触发UI更新，确保按钮状态正确
        _ = isAllDefaultFilters
    }
    
    // 更新日期范围选择状态
    private func updateDateRangeSelection() {
        let calendar = Calendar.current
        let now = Date()
        let currentYear = calendar.component(.year, from: now)
        
        // 如果没有设置日期范围，标记为自定义状态
        guard let from = tempDateFrom, let to = tempDateTo else {
            dateRangeSelection = .custom
            return
        }
        
        // 获取标准化的日期区间以进行精确比较
        let fromStart = calendar.startOfDay(for: from)
        let toEnd = calendar.date(bySettingHour: 23, minute: 59, second: 59, of: to) ?? to
        
        // 今天的日期范围
        let todayStart = calendar.startOfDay(for: now)
        let todayEnd = calendar.date(bySettingHour: 23, minute: 59, second: 59, of: now) ?? now
        
        // 本月的日期范围
        let monthFirstDay = calendar.date(from: calendar.dateComponents([.year, .month], from: now))!
        let monthLastDay = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: monthFirstDay)!
        
        // 上月的日期范围
        let previousMonth = calendar.date(byAdding: .month, value: -1, to: now)!
        let prevMonthFirstDay = calendar.date(from: calendar.dateComponents([.year, .month], from: previousMonth))!
        let prevMonthLastDay = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: prevMonthFirstDay)!
        
        // 今年的日期范围
        let yearFirstDay = calendar.date(from: DateComponents(year: currentYear, month: 1, day: 1))!
        let yearLastDay = calendar.date(from: DateComponents(year: currentYear, month: 12, day: 31))!
        
        // 检查日期是否是某个月的完整跨度
        let isFullMonthSpan = calendar.isDate(fromStart, inSameDayAs: 
                               calendar.date(from: calendar.dateComponents([.year, .month], from: fromStart))!) && 
                              calendar.isDate(toEnd, inSameDayAs:
                               calendar.date(byAdding: DateComponents(month: 1, day: -1), 
                                           to: calendar.date(from: calendar.dateComponents([.year, .month], from: fromStart))!)!)
        
        // 精确匹配日期范围
        if calendar.isDate(fromStart, inSameDayAs: todayStart) && calendar.isDate(toEnd, inSameDayAs: todayEnd) {
            dateRangeSelection = .today
        } else if calendar.isDate(fromStart, inSameDayAs: monthFirstDay) && calendar.isDate(toEnd, inSameDayAs: monthLastDay) {
            dateRangeSelection = .month
        } else if calendar.isDate(fromStart, inSameDayAs: prevMonthFirstDay) && calendar.isDate(toEnd, inSameDayAs: prevMonthLastDay) {
            dateRangeSelection = .previousMonth
        } else if calendar.isDate(fromStart, inSameDayAs: yearFirstDay) && calendar.isDate(toEnd, inSameDayAs: yearLastDay) {
            dateRangeSelection = .year
        } else if isFullMonthSpan {
            // 如果是完整月份范围但不是当前月或上月，显示为自定义但使用特殊样式标记
            dateRangeSelection = .custom
            
            // 可以考虑在这里设置其他视觉提示，比如高亮自定义月份按钮
            // 暂不修改UI，仅更新状态
        } else {
            dateRangeSelection = .custom
        }
        
        print("📅 日期范围状态: \(dateRangeSelection.rawValue), 从 \(fromStart) 到 \(toEnd)")
    }
    
    // 重置所有筛选条件
    private func resetFilters() async {
        // 添加轻微的触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()
        
        // 先清除所有筛选条件
        withAnimation(.easeInOut(duration: 0.2)) {
            tempSearchQuery = ""
            tempBrewMethod = ""
            tempCoffeeBean = ""
            tempRatingRange = ""
        }
        
        // 设置日期范围为当月
        setCurrentMonth()
        
        // 应用筛选条件 ViewModel 的清除方法
        Task {
            await viewModel.clearFilters()
        }
    }
    
    // 应用筛选条件
    private func applyFilters() {
        guard !isApplying else { return }
        
        // 设置正在应用状态，防止多次点击
        isApplying = true
        
        // 更新视图模型中的筛选条件
        viewModel.filterDateFrom = tempDateFrom
        viewModel.filterDateTo = tempDateTo
        viewModel.filterSearchQuery = tempSearchQuery
        viewModel.filterBrewMethod = tempBrewMethod
        viewModel.filterCoffeeBean = tempCoffeeBean
        viewModel.filterRatingRange = tempRatingRange
        
        // 保存当前的日期范围选择，供下次打开时使用
        UserDefaults.standard.set(dateRangeSelection.rawValue, forKey: "last_date_range_selection")
        
        // 添加触觉反馈
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()
        
        // 使用与系统一致的动画参数平滑关闭sheet
        withAnimation(.spring(response: 0.35, dampingFraction: 1, blendDuration: 0)) {
            dismiss()
        }
        
        // 在sheet关闭动画完成后应用筛选条件
        Task {
            try? await Task.sleep(nanoseconds: 350_000_000) // 等待350毫秒，让关闭动画完成
            await viewModel.applyFilters()
            isApplying = false
        }
    }
}

#if DEBUG
struct BrewLogListFilterSheetView_Previews: PreviewProvider {
    static var previews: some View {
        Text("BrewLogListFilterSheetView Preview")
    }
}
#endif 

// 中文日期选择器
struct ChineseDatePicker: View {
    @Binding var date: Date?
    let fallbackDate: Date
    var onChange: (() -> Void)? = nil
    
    // 使用UIDatePicker方式显示，彻底解决格式问题
    var body: some View {
        DatePickerWrapper(date: Binding(
            get: { self.date ?? fallbackDate },
            set: { 
                self.date = $0
                onChange?()
            }
        ))
        .frame(width: 160, height: 35, alignment: .leading)
        .id(date ?? fallbackDate) // 强制在日期变化时重新创建视图
    }
}

// UIDatePicker包装器，确保一致的中文格式
struct DatePickerWrapper: UIViewRepresentable {
    @Binding var date: Date
    
    func makeUIView(context: Context) -> UIDatePicker {
        let picker = UIDatePicker()
        picker.datePickerMode = .date
        
        // 直接设置样式，不需要版本判断（最低支持iOS 16.6）
        picker.preferredDatePickerStyle = .compact
        picker.tintColor = UIColor(Color.accentColor)
        
        // 设置区域和日期
        picker.locale = Locale(identifier: "zh_CN")
        picker.date = date
        
        // 添加事件监听
        picker.addTarget(context.coordinator, action: #selector(Coordinator.dateChanged(_:)), for: .valueChanged)
        
        // 调整外观
        picker.contentHorizontalAlignment = .left
        
        return picker
    }
    
    func updateUIView(_ uiView: UIDatePicker, context: Context) {
        if uiView.date != date {
            // 关闭动画更新日期
            UIView.performWithoutAnimation {
                uiView.setDate(date, animated: false)
            }
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject {
        var parent: DatePickerWrapper
        
        init(_ parent: DatePickerWrapper) {
            self.parent = parent
        }
        
        @objc func dateChanged(_ sender: UIDatePicker) {
            // 关闭动画更新绑定值
            UIView.performWithoutAnimation {
                parent.date = sender.date
            }
        }
    }
} 