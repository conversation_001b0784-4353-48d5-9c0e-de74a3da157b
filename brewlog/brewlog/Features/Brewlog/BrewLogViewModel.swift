import Foundation
import SwiftUI

@MainActor
class BrewLogViewModel: ObservableObject {
    private let apiService: APIService
    private let brewingService: BrewingService

    // 定时刷新计时器
    private var refreshTimer: Timer?

    // MARK: - Published Properties
    @Published var brewingRecords: [BrewingRecord] = []
    @Published var equipment: [Equipment] = []
    @Published var coffeeBeans: [CoffeeBean] = []
    @Published var isLoading = false
    @Published var isLoadingRecords = false
    @Published var isLoadingEquipment = false
    @Published var isLoadingBeans = false
    @Published var isRefreshing = false
    @Published var error: Error?
    @Published var isSaving = false
    @Published var lastRecord: BrewingRecord?
    @Published var draftRecord: BrewingRecord?
    @Published var editingRecord: BrewingRecord?
    @Published var templateRecord: BrewingRecord? = nil // 用于保存作为模板的记录
    @Published var heatmapData: [HeatmapData] = []
    @Published var isLoadingHeatmap = false
    @Published var hindsightData: HindsightData?
    @Published var isLoadingHindsight = false
    @Published var flavorTags: [FlavorTag] = []
    @Published var errorMessage: String?
    @Published var showingAddSheet = false

    // 对比功能相关属性
    @Published var recordsForCompare: [BrewingRecord] = [] // 存储被选择用于对比的记录
    @Published var showingCompareView = false // 控制是否显示对比界面

    // MARK: - Pagination Properties
    @Published var currentPage = 1
    @Published var hasMoreRecords = true

    // 统计数据
    @Published var streakDays = 0
    @Published var monthCount = 0
    @Published var yearCount = 0

    // 计算上次打卡距今多少天
    var lastStreakDays: Int {
        // 如果有连续打卡记录，就不需要显示上次打卡
        if streakDays > 0 {
            return 0
        }

        // 如果有冲煮记录，计算最新一条记录距离今天的天数
        if let latestRecord = brewingRecords.first {
            let calendar = Calendar.current
            let today = calendar.startOfDay(for: Date())
            let recordDate = calendar.startOfDay(for: latestRecord.createdAt)

            let components = calendar.dateComponents([.day], from: recordDate, to: today)
            return components.day ?? 0
        }

        return 0
    }

    // 筛选选项
    @Published var brewMethods: [BrewMethod] = []

    // 筛选条件
    @Published var filterDateFrom: Date?
    @Published var filterDateTo: Date?
    @Published var filterSearchQuery: String = ""
    @Published var filterBrewMethod: String = ""
    @Published var filterCoffeeBean: String = ""
    @Published var filterRatingRange: String = ""
    @Published var isFiltering = false
    @Published var showingFilterSheet = false
    @Published var totalPages = 1
    @Published var totalCount = 0

    // 检查当前日期范围是否为默认的当月范围
    var isDefaultDateRange: Bool {
        guard let from = filterDateFrom, let to = filterDateTo else {
            return false // 如果没有设置日期范围，则不是默认范围
        }

        let calendar = Calendar.current

        // 检查是否是完整的某个月范围（从月初到月末）
        let fromComponents = calendar.dateComponents([.year, .month, .day], from: from)
        let toComponents = calendar.dateComponents([.year, .month, .day], from: to)

        // 确认开始日期是当月第一天
        guard fromComponents.day == 1 else { return false }

        // 计算这个月的最后一天
        let fromMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: from))!
        let lastDayOfMonth = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: fromMonth)!
        let lastDayComponents = calendar.dateComponents([.year, .month, .day], from: lastDayOfMonth)

        // 确认结束日期是当月最后一天
        return toComponents.day == lastDayComponents.day &&
               toComponents.month == lastDayComponents.month &&
               toComponents.year == lastDayComponents.year
    }

    private let pageSize = 20

    var availableYears: [Int] {
        let currentYear = Calendar.current.component(.year, from: Date())
        return Array((currentYear-2)...currentYear).reversed()
    }

    // 日期格式化器
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }()

    // MARK: - 缓存管理
    private var lastUpdateTimestamp: Date? = nil
    private let cacheExpirationSeconds: TimeInterval = 30 // 缓存30秒过期

    private func isCacheValid() -> Bool {
        guard let timestamp = lastUpdateTimestamp else { return false }
        let now = Date()
        let elapsed = now.timeIntervalSince(timestamp)
        return elapsed < cacheExpirationSeconds
    }

    private func updateCacheTimestamp() {
        lastUpdateTimestamp = Date()
    }

    private func invalidateCache() {
        lastUpdateTimestamp = nil
    }

    // 添加一个属性来跟踪是否跳过默认筛选
    private let skipDefaultFilters: Bool

    // MARK: - Initialization
    init(apiService: APIService = .shared, brewingService: BrewingService = BrewingService(), skipDefaultFilters: Bool = false) {
        self.apiService = apiService
        self.brewingService = brewingService
        self.skipDefaultFilters = skipDefaultFilters

        // 设置默认筛选为当前月份，除非skipDefaultFilters为true
        if !skipDefaultFilters {
            setCurrentMonth()
            isFiltering = true
        }

        // 添加应用生命周期通知
        setupNotifications()

        Task {
            // 验证令牌有效性
            await apiService.checkTokenValidity()

            // 获取初始数据
            await fetchInitialData()
            loadDraft()
            loadLastRecord()
            // fetchStatistics会在fetchInitialData中的fetchBrewingRecords或fetchFilteredRecords中调用
            await fetchFilterOptions()

            // 设置定时刷新
            startRefreshTimer()
        }
    }

    deinit {
        // 在主线程上安全地停止定时器
        if let timer = refreshTimer {
            timer.invalidate()
            refreshTimer = nil
        }

        // 移除通知
        NotificationCenter.default.removeObserver(self)
    }

    // 设置通知监听
    private func setupNotifications() {
        // 监听应用进入前台的通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )

        // 监听冲煮提醒服务的 Hindsight 数据请求
        NotificationCenter.default.addObserver(
            forName: Notification.Name("RequestHindsightDataForBrewReminder"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task {
                await self?.fetchHindsightDataForBrewReminder()
            }
        }
    }

    // 处理应用进入前台通知
    @objc private func handleAppForeground() {
        Task {
            // 应用进入前台时刷新统计数据
            await fetchStatistics()
        }
    }

    // 启动定时刷新
    func startRefreshTimer() {
        // 先停止现有计时器
        stopRefreshTimer()

        // 设置新计时器，每5分钟刷新一次统计数据
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { [weak self] _ in
            Task { [weak self] in
                await self?.fetchStatistics()
            }
        }
    }

    // 停止定时刷新
    func stopRefreshTimer() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }

    // 手动刷新统计数据
    func refreshStatistics() async {
        await fetchStatistics()
    }

    // MARK: - Data Fetching
    func fetchInitialData() async {
        // 无论是否使用筛选条件，都使用 fetchFilteredRecords 端点
        // 在被调用时强制过期缓存
        invalidateCache()

        // 如果没有应用过滤条件，则会获取所有记录
        // 如果跳过默认筛选且filterDateFrom和filterDateTo都为nil，则不进行任何查询
        if skipDefaultFilters && filterDateFrom == nil && filterDateTo == nil {
            // 不执行任何查询，等待onAppear中设置的筛选条件
            return
        }

        await fetchFilteredRecords(forceRefresh: true)
        await fetchEquipments()
        await fetchCoffeeBeans()
    }

    // MARK: - Response Types
    struct BrewingRecordsResponse: Codable {
        let records: [BrewingRecord]
        let totalCount: Int
        let totalPages: Int
        let currentPage: Int

        enum CodingKeys: String, CodingKey {
            case records
            case totalCount = "total_count"
            case totalPages = "total_pages"
            case currentPage = "current_page"
        }

        // 自定义解码方法，增强错误处理
        init(from decoder: Decoder) throws {
            do {
                let container = try decoder.container(keyedBy: CodingKeys.self)

                // 尝试解码records字段
                do {
                    self.records = try container.decode([BrewingRecord].self, forKey: .records)
                } catch {
                    print("⚠️ 解码records字段失败: \(error)")

                    // 尝试从顶层解码数组
                    if let singleValueContainer = try? decoder.singleValueContainer(),
                       let records = try? singleValueContainer.decode([BrewingRecord].self) {
                        self.records = records
                    } else {
                        // 重新抛出原始错误
                        throw error
                    }
                }

                // 解码分页信息，使用默认值处理缺失字段
                self.totalCount = try container.decodeIfPresent(Int.self, forKey: .totalCount) ?? self.records.count
                self.totalPages = try container.decodeIfPresent(Int.self, forKey: .totalPages) ?? 1
                self.currentPage = try container.decodeIfPresent(Int.self, forKey: .currentPage) ?? 1
            } catch {
                print("❌ BrewingRecordsResponse解码失败: \(error)")
                throw error
            }
        }
    }

    // 已弃用：所有记录查询现在应统一使用 fetchFilteredRecords 方法
    // 这个方法保留仅作为参考，不应再被调用
    @available(*, deprecated, message: "Use fetchFilteredRecords instead")
    private func fetchBrewingRecordsFromPath(path: String) async -> [BrewingRecord]? {
        do {
            // 尝试解析为带分页的响应格式
            print("🔍 尝试解析为BrewingRecordsResponse格式")
            let response: BrewingRecordsResponse = try await apiService.get(path)
            print("✅ 成功解析为BrewingRecordsResponse格式，获取到\(response.records.count)条记录")

            // 更新分页信息
            self.totalPages = response.totalPages
            self.currentPage = response.currentPage
            self.totalCount = response.totalCount
            self.hasMoreRecords = response.currentPage < response.totalPages

            return response.records
        } catch let decodingError as APIError where decodingError.isDecodingError {
            // 如果解析为带分页的响应格式失败，尝试直接解析为数组
            print("⚠️ 解析为BrewingRecordsResponse失败: \(decodingError)")
            print("🔍 尝试直接解析为数组")

            do {
                let records: [BrewingRecord] = try await apiService.get(path)
                print("✅ 成功解析为数组，获取到\(records.count)条记录")
                return records
            } catch let arrayError as APIError where arrayError.isDecodingError {
                print("⚠️ 直接解析为数组失败: \(arrayError)")

                // 最后尝试解析为包含results字段的响应
                print("🔍 尝试解析为包含results字段的响应")
                do {
                    struct ResultsResponse: Codable {
                        let count: Int
                        let next: String?
                        let previous: String?
                        let results: [BrewingRecord]
                        let totalPages: Int?
                        let currentPage: Int?

                        enum CodingKeys: String, CodingKey {
                            case count
                            case next
                            case previous
                            case results
                            case totalPages = "total_pages"
                            case currentPage = "current_page"
                        }
                    }

                    let resultsResponse: ResultsResponse = try await apiService.get(path)
                    print("✅ 成功解析为包含results字段的响应，获取到\(resultsResponse.results.count)条记录")

                    // 如果响应中包含分页信息，则更新
                    if let totalPages = resultsResponse.totalPages {
                        self.totalPages = totalPages
                    }
                    if let currentPage = resultsResponse.currentPage {
                        self.currentPage = currentPage
                        self.hasMoreRecords = currentPage < (resultsResponse.totalPages ?? 1)
                    }
                    self.totalCount = resultsResponse.count

                    return resultsResponse.results
                } catch let resultsError {
                    print("❌ 所有解析方式都失败: \(resultsError)")

                    // 尝试一种特殊情况：如果服务器返回的是空数组[]
                    do {
                        // 使用URLSession.shared而不是apiService.session
                        let url = URL(string: apiService.getBaseURL() + path)!
                        let request = URLRequest(url: url)
                        let (data, _) = try await URLSession.shared.data(for: request)

                        if let jsonString = String(data: data, encoding: .utf8),
                           jsonString.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines) == "[]" {
                            print("🔍 检测到服务器返回空数组，返回空记录列表")
                            return []
                        }
                    } catch {
                        print("❌ 检查空数组失败: \(error)")
                    }

                    return nil
                }
            } catch let otherError {
                // 捕获其他所有类型的错误
                print("❌ 直接解析为数组时遇到非解码错误: \(otherError)")
                return nil
            }
        } catch let error {
            print("❌ fetchBrewingRecordsFromPath失败: \(error)")
            return nil
        }
    }

    @MainActor
    func fetchBrewingRecords() async {
        guard !isLoadingRecords else { return }
        isLoadingRecords = true

        // 修改：使用filtered-records端点替代原来的records端点
        // 不使用筛选条件，获取所有记录
        try? await fetchFilteredRecords()

        // 清除isFiltering状态
        isFiltering = false
        isLoadingRecords = false
    }

    // 按筛选条件获取冲煮记录
    func fetchFilteredRecords(loadMore: Bool = false, forceRefresh: Bool = false) async {
        // 检查缓存有效性，如果缓存有效且不需要强制刷新且没有加载更多且记录不为空，则直接返回
        if isCacheValid() && !forceRefresh && !loadMore && !brewingRecords.isEmpty {
            print("🔄 使用缓存的冲煮记录，不立即刷新 \(Date().timeIntervalSince(lastUpdateTimestamp!)) 秒")
            return
        }

        // 如果需要强制刷新，则重置分页状态和缓存
        if forceRefresh {
            await MainActor.run {
                currentPage = 1
                // 不要清空现有记录，以避免闪现空状态
                // brewingRecords = []
                invalidateCache()
            }
        }

        // 如果需要加载更多且已经没有更多记录，则直接返回
        if loadMore && (currentPage >= totalPages || !hasMoreRecords) {
            return
        }

        // 如果需要加载更多，先增加页码；否则重置页码
        if loadMore {
            currentPage += 1
        } else if !forceRefresh {
            // 只有在初始加载或重新筛选时才显示加载状态
            await MainActor.run {
                isLoadingRecords = true
            }
        }

        // 始终设置为筛选模式
        await MainActor.run {
            isFiltering = true
        }

        // 清除错误状态
        error = nil

        // 打印筛选条件用于调试
        print("🔍 筛选条件 - 日期: \(filterDateFrom?.description ?? "nil") 到 \(filterDateTo?.description ?? "nil")")
        print("🔍 筛选条件 - 搜索: \(filterSearchQuery)")
        print("🔍 筛选条件 - 冲煮方式: \(filterBrewMethod)")
        print("🔍 筛选条件 - 咖啡豆: \(filterCoffeeBean)")
        print("🔍 筛选条件 - 评分范围: \(filterRatingRange)")

        do {
            // 构建请求参数
            var parameters: [String: String] = [
                "page": String(currentPage),
                "page_size": String(pageSize),
                "force_refresh": "true"  // 添加强制刷新参数，确保不使用服务器缓存
            ]

            if let dateFrom = filterDateFrom {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd"
                parameters["date_from"] = formatter.string(from: dateFrom)
            }

            if let dateTo = filterDateTo {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd"
                parameters["date_to"] = formatter.string(from: dateTo)
            }

            if !filterSearchQuery.isEmpty {
                parameters["search_query"] = filterSearchQuery
            }

            if !filterBrewMethod.isEmpty {
                parameters["brew_method"] = filterBrewMethod
            }

            if !filterCoffeeBean.isEmpty {
                parameters["coffee_bean"] = filterCoffeeBean
            }

            if !filterRatingRange.isEmpty {
                parameters["rating_range"] = filterRatingRange
            }

            // 使用BrewingService获取筛选记录
            let result = try await brewingService.fetchFilteredRecords(
                dateFrom: filterDateFrom,
                dateTo: filterDateTo,
                searchQuery: filterSearchQuery.isEmpty ? nil : filterSearchQuery,
                brewMethod: filterBrewMethod.isEmpty ? nil : filterBrewMethod,
                coffeeBean: filterCoffeeBean.isEmpty ? nil : filterCoffeeBean,
                ratingRange: filterRatingRange.isEmpty ? nil : filterRatingRange,
                page: currentPage,
                pageSize: pageSize
            )

            await MainActor.run {
                // 更新元数据，无论是否加载更多
                self.totalCount = result.totalCount
                self.totalPages = result.totalPages
                self.hasMoreRecords = result.currentPage < result.totalPages

                // 如果是加载更多，追加记录
                if loadMore {
                    // 检查是否有重复记录，避免重复添加
                    let _ = Set(result.records.map { $0.id })
                    let existingRecordIds = Set(self.brewingRecords.map { $0.id })

                    // 只添加不存在的记录
                    let uniqueNewRecords = result.records.filter { !existingRecordIds.contains($0.id) }

                    // 使用一种不会触发整个数组替换的方式追加记录
                    if !uniqueNewRecords.isEmpty {
                        // 预先分配容量以提高性能
                        var updatedRecords = self.brewingRecords
                        updatedRecords.reserveCapacity(updatedRecords.count + uniqueNewRecords.count)
                        updatedRecords.append(contentsOf: uniqueNewRecords)
                        self.brewingRecords = updatedRecords

                        print("🔄 追加了 \(uniqueNewRecords.count) 条新记录，过滤掉了 \(result.records.count - uniqueNewRecords.count) 条重复记录")
                    } else {
                        print("🔄 没有发现新记录，跳过追加")
                    }
                } else {
                    // 缓存旧记录以供动画过渡
                    var oldRecords = self.brewingRecords

                    // 当结果不为空时，才替换本地记录
                    if !result.records.isEmpty {
                        self.brewingRecords = result.records
                    } else if !forceRefresh && !self.brewingRecords.isEmpty {
                        // 如果结果为空且不是强制刷新，保留现有记录，避免闪烁
                        print("⚠️ 服务器返回空结果，但保留现有记录以避免UI闪烁")
                    } else {
                        // 只有在强制刷新或当前列表为空时才清空
                        self.brewingRecords = []
                    }

                    // 检查是否添加了平滑过渡动画
                    if oldRecords.count > 0 && result.records.count > 0 {
                        // 记录ID变化以评估变化程度
                        let oldIds = Set(oldRecords.map { $0.id })
                        let newIds = Set(result.records.map { $0.id })
                        let commonIds = oldIds.intersection(newIds)
                        let percentUnchanged = Double(commonIds.count) / Double(max(oldIds.count, 1))

                        if percentUnchanged > 0.6 {
                            print("📊 记录列表中有 \(Int(percentUnchanged * 100))% 未变化，可以考虑添加平滑过渡")
                        }
                    }
                }

                print("🟢 成功获取记录，当前共有 \(self.brewingRecords.count) 条，总共 \(result.totalCount) 条，页码: \(result.currentPage)/\(result.totalPages)")

                // 完成加载
                self.isLoadingRecords = false

                // 如果当前是默认的本月筛选，且结果为空，尝试查找最后一个有记录的月份
                if !loadMore && isDefaultDateRange && result.records.isEmpty &&
                   filterSearchQuery.isEmpty && filterBrewMethod.isEmpty &&
                   filterCoffeeBean.isEmpty && filterRatingRange.isEmpty {
                    // 使用主线程异步执行，避免在当前状态更新过程中再次修改状态
                    DispatchQueue.main.async {
                        Task {
                            await self.findLastRecordMonth()
                        }
                    }
                }

                // 更新缓存时间戳
                updateCacheTimestamp()
            }

            // 更新统计数据，但仅当不是加载更多页时执行
            // 这样可以避免在分页加载时过度请求统计API
            if !loadMore {
                await fetchStatistics()
            }
        } catch let error as APIError {
            await MainActor.run {
                self.error = error
                print("🔴 获取筛选冲煮记录失败: \(error)")
                self.isLoadingRecords = false
            }
        } catch {
            await MainActor.run {
                self.error = APIError.unknown
                print("🔴 获取筛选冲煮记录时发生未知错误: \(error)")
                self.isLoadingRecords = false
            }
        }
    }

    // 加载更多记录
    func loadMoreRecords() async {
        // 保护措施：避免重复加载和无效加载
        guard !isLoadingRecords && hasMoreRecords && currentPage < totalPages else {
            print("⚠️ 跳过加载更多：已在加载=\(isLoadingRecords), 有更多=\(hasMoreRecords), 当前页=\(currentPage), 总页数=\(totalPages)")
            return
        }

        // 设置加载状态但不影响整体UI
        // 注意：这个状态更新必须和实际加载保持分离，以避免UI抖动
        await MainActor.run {
            isLoadingRecords = true
        }

        // 加载下一页
        await fetchFilteredRecords(loadMore: true)
    }

    // 应用筛选条件
    func applyFilters() async {
        // 重置分页状态
        await MainActor.run {
            currentPage = 1
            hasMoreRecords = true
            // 不要清空现有记录，避免闪现空白状态
            // brewingRecords = []

            // 更新筛选状态
            isFiltering = true

            // 使用缓存无效率
            invalidateCache()
        }

        // 获取新的筛选结果
        await fetchFilteredRecords(forceRefresh: true)
    }

    // 清除所有筛选条件
    func clearFilters() async {
        // 先更新UI状态
        await MainActor.run {
            filterDateFrom = nil
            filterDateTo = nil
            filterSearchQuery = ""
            filterBrewMethod = ""
            filterCoffeeBean = ""
            filterRatingRange = ""

            // 重置分页状态
            currentPage = 1
            brewingRecords = [] // 清空现有记录，避免旧数据闪现

            // 使用缓存无效率
            invalidateCache()
        }

        // 获取无筛选条件的所有记录
        await fetchFilteredRecords(forceRefresh: true)

        // 完成无筛选条件的记录获取后，更新isFiltering状态
        await MainActor.run {
            isFiltering = false
        }
    }

    // 设置日期范围
    func setCurrentMonth() {
        let calendar = Calendar.current
        let now = Date()

        if let firstDay = calendar.date(from: calendar.dateComponents([.year, .month], from: now)),
           let lastDay = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: firstDay) {
            filterDateFrom = firstDay
            filterDateTo = lastDay
        }
    }

    // 设置当前年度范围
    func setCurrentYear() {
        let calendar = Calendar.current
        let now = Date()
        let year = calendar.component(.year, from: now)

        if let firstDay = calendar.date(from: DateComponents(year: year, month: 1, day: 1)),
           let lastDay = calendar.date(from: DateComponents(year: year, month: 12, day: 31)) {
            filterDateFrom = firstDay
            filterDateTo = lastDay
        }
    }

    func refreshBrewingRecords() async {
        isRefreshing = true

        // 重置分页状态
        await MainActor.run {
            currentPage = 1
            hasMoreRecords = true
            // 不要清空现有记录，避免UI闪烁
            // brewingRecords = []
        }

        // 无论是否应用了筛选条件，都使用 fetchFilteredRecords 方法
        await fetchFilteredRecords(forceRefresh: true)

        // 刷新统计数据
        await fetchStatistics()

        await MainActor.run {
            isRefreshing = false
        }
    }

    @MainActor
    func fetchEquipments() async {
        guard !isLoadingEquipment else { return }
        isLoadingEquipment = true

        // 第一次尝试使用主路径
        await tryFetchEquipments(useFallbackPath: false)

        isLoadingEquipment = false
    }

    @MainActor
    private func tryFetchEquipments(useFallbackPath: Bool) async {
        do {
            let allEquipment: [Equipment]
            if useFallbackPath {
                // 使用备用路径
                print("🔄 尝试使用备用路径获取设备")
                do {
                    // 尝试直接解析为数组
                    allEquipment = try await apiService.get("/ios/api/equipment/")
                } catch let decodingError as APIError where decodingError.isDecodingError {
                    print("⚠️ 直接解析为数组失败: \(decodingError.message)")

                    // 尝试解析JSON数据
                    if let responseData = (decodingError as NSError).userInfo["data"] as? Data,
                       let jsonObj = try? JSONSerialization.jsonObject(with: responseData) {

                        print("🔍 分析JSON数据结构...")

                        if let dict = jsonObj as? [String: Any] {
                            // 检查是否存在常见的分页格式字段
                            let hasResults = dict["results"] != nil
                            let hasCount = dict["count"] != nil
                            let hasData = dict["data"] != nil

                            print("📊 JSON结构分析: 包含results=\(hasResults), count=\(hasCount), data=\(hasData)")

                            if hasResults {
                                // 尝试从results字段获取数据
                                struct ResultsResponse: Codable {
                                    let results: [Equipment]
                                }
                                let response: ResultsResponse = try await apiService.get("/ios/api/equipment/")
                                allEquipment = response.results
                                print("✅ 成功从results字段获取\(response.results.count)个设备")

                            } else if hasData {
                                // 尝试从data字段获取数据
                                struct DataResponse: Codable {
                                    let data: [Equipment]
                                }
                                let response: DataResponse = try await apiService.get("/ios/api/equipment/")
                                allEquipment = response.data
                                print("✅ 成功从data字段获取\(response.data.count)个设备")
                            } else {
                                // 找不到已知格式，尝试使用备用格式
                                print("⚠️ 未找到已知数据字段，尝试标准分页格式")
                                struct StandardResponse: Codable {
                                    let results: [Equipment]
                                }
                                let response: StandardResponse = try await apiService.get("/ios/api/equipment/")
                                allEquipment = response.results
                            }
                        } else {
                            // 不是字典，可能是其他格式，抛出错误
                            throw APIError.decodingError(decodingError)
                        }
                    } else {
                        // 无法获取原始JSON数据，尝试标准格式
                        print("🔍 尝试从响应字典中提取results字段")
                        struct EquipmentResponse: Codable {
                            let results: [Equipment]
                        }
                        let response: EquipmentResponse = try await apiService.get("/ios/api/equipment/")
                        allEquipment = response.results
                        print("✅ 成功获取\(response.results.count)个设备")
                    }
                }
            } else {
                // 使用主路径通过BrewingService
                print("🔍 使用BrewingService获取设备")
                allEquipment = try await brewingService.fetchEquipments()
                print("✅ 成功通过BrewingService获取\(allEquipment.count)个设备")
            }

            equipment = allEquipment

            // 添加详细调试日志
            print("📊 获取到设备总数: \(allEquipment.count)个")

            // 分析设备类型分布
            let typeCounts = Dictionary(grouping: allEquipment, by: { $0.type }).mapValues { $0.count }
            for (type, count) in typeCounts {
                print("📊 - \(type): \(count)个")
            }

            // 分析设备状态
            let archivedCount = allEquipment.filter { $0.isArchived }.count
            let deletedCount = allEquipment.filter { $0.isDeleted }.count
            let activeCount = allEquipment.filter { !$0.isArchived && !$0.isDeleted }.count

            print("📊 设备状态: 活跃=\(activeCount)个, 已归档=\(archivedCount)个, 已删除=\(deletedCount)个")

            // 分析可用设备数量
            let brewers = allEquipment.filter { $0.type == "BREWER" && !$0.isArchived && !$0.isDeleted }.count
            let grinders = allEquipment.filter { $0.type == "GRINDER" && !$0.isArchived && !$0.isDeleted }.count
            let gadgets = allEquipment.filter { $0.type == "GADGET" && !$0.isArchived && !$0.isDeleted }.count
            let gadgetKits = allEquipment.filter { $0.type == "GADGET_KIT" && !$0.isArchived && !$0.isDeleted }.count

            print("📊 可用设备: 冲煮器具=\(brewers)个, 磨豆机=\(grinders)个, 小工具=\(gadgets)个, 小工具组合=\(gadgetKits)个")

            error = nil

            // 如果成功获取了设备数据，缓存到本地
            if !allEquipment.isEmpty {
                saveEquipmentToCache(allEquipment)
            }
        } catch let apiError as APIError {
            self.error = apiError
            print("❌ 获取设备失败: \(apiError)")

            // 根据错误类型输出更详细的日志
            if case .serverError(let code, let message) = apiError {
                print("❌ 服务器错误 \(code): \(message)")

                // 如果是404错误且还没有尝试过备用路径，则尝试备用路径
                if code == 404 && !useFallbackPath {
                    print("🔄 API路径返回404，尝试使用备用路径")
                    await tryFetchEquipments(useFallbackPath: true)
                    return
                } else if code == 404 {
                    print("❌ 设备API路径可能不正确，所有尝试均失败")
                    errorMessage = "设备列表获取失败: API路径可能已变更"
                }
            } else if apiError.isDecodingError {
                print("🔴 解析设备数据失败: 服务器返回的格式与预期不符")
                print("⚠️ 尝试更新API客户端以适应新的响应格式")

                // 如果是解码错误且还没有尝试过备用路径，则尝试备用路径
                if !useFallbackPath {
                    print("🔄 尝试备用解析方法")
                    await tryFetchEquipments(useFallbackPath: true)
                    return
                }
            }

            // 如果获取失败且设备列表为空，尝试从缓存加载
            if equipment.isEmpty {
                tryLoadEquipmentFromCache()
            }
        } catch {
            self.error = error
            print("❌ 获取设备失败: \(error), 类型: \(type(of: error))")

            // 如果获取失败且设备列表为空，尝试从缓存加载
            if equipment.isEmpty {
                tryLoadEquipmentFromCache()
            }
        }
    }

    // 保存设备数据到缓存
    private func saveEquipmentToCache(_ equipments: [Equipment]) {
        do {
            let data = try JSONEncoder().encode(equipments)
            UserDefaults.standard.set(data, forKey: "cached_equipment")
            print("✅ 成功缓存 \(equipments.count) 个设备数据")
        } catch {
            print("❌ 缓存设备数据失败: \(error)")
        }
    }

    // 尝试从缓存加载设备数据
    private func tryLoadEquipmentFromCache() {
        if let cachedData = UserDefaults.standard.data(forKey: "cached_equipment") {
            do {
                let cachedEquipment = try JSONDecoder().decode([Equipment].self, from: cachedData)
                self.equipment = cachedEquipment
                print("📦 从缓存加载了 \(cachedEquipment.count) 个设备")
            } catch {
                print("❌ 从缓存加载设备失败: \(error)")
            }
        } else {
            print("⚠️ 无可用的设备缓存数据")
        }
    }

    @MainActor
    func fetchCoffeeBeans() async {
        guard !isLoadingBeans else { return }
        isLoadingBeans = true

        await tryFetchCoffeeBeans(useFallbackPath: false)

        isLoadingBeans = false
    }

    @MainActor
    private func tryFetchCoffeeBeans(useFallbackPath: Bool) async {
        do {
            let beans: [CoffeeBean]
            if useFallbackPath {
                // 使用备用路径
                print("🔄 尝试使用备用路径获取咖啡豆")
                beans = try await apiService.get("/ios/api/beans/")
            } else {
                // 使用主路径
                beans = try await brewingService.fetchCoffeeBeans()
            }

            // 过滤掉已归档和已删除的咖啡豆
            coffeeBeans = beans.filter { !$0.isArchived && !$0.isDeleted }
            error = nil

            print("🟢 成功获取 \(coffeeBeans.count) 个咖啡豆 (总数: \(beans.count))")

            // 缓存咖啡豆数据
            if !beans.isEmpty {
                saveBeansToCache(beans)
            }
        } catch let apiError as APIError {
            self.error = apiError
            print("🔴 获取咖啡豆失败: \(apiError)")

            // 针对不同错误类型给出更详细的信息
            if case .serverError(let code, let message) = apiError {
                print("❌ 咖啡豆服务器错误 \(code): \(message)")

                // 如果是404错误且还没有尝试过备用路径，则尝试备用路径
                if code == 404 && !useFallbackPath {
                    print("🔄 咖啡豆API路径返回404，尝试使用备用路径")
                    await tryFetchCoffeeBeans(useFallbackPath: true)
                    return
                } else if code == 404 {
                    print("❌ 咖啡豆API路径可能不正确，所有尝试均失败")
                    errorMessage = "咖啡豆列表获取失败: API路径可能已变更"
                }
            }

            // 如果获取失败且咖啡豆列表为空，尝试从缓存加载
            if coffeeBeans.isEmpty {
                tryLoadBeansFromCache()
            }
        } catch {
            self.error = error
            print("🔴 获取咖啡豆失败: \(error), 类型: \(type(of: error))")

            // 如果获取失败且咖啡豆列表为空，尝试从缓存加载
            if coffeeBeans.isEmpty {
                tryLoadBeansFromCache()
            }
        }
    }

    // 保存咖啡豆到缓存
    private func saveBeansToCache(_ beans: [CoffeeBean]) {
        do {
            let data = try JSONEncoder().encode(beans)
            UserDefaults.standard.set(data, forKey: "cached_coffee_beans")
            print("✅ 成功缓存 \(beans.count) 个咖啡豆")
        } catch {
            print("❌ 缓存咖啡豆失败: \(error)")
        }
    }

    // 尝试从缓存加载咖啡豆
    private func tryLoadBeansFromCache() {
        if let cachedData = UserDefaults.standard.data(forKey: "cached_coffee_beans") {
            do {
                let cachedBeans = try JSONDecoder().decode([CoffeeBean].self, from: cachedData)
                // 过滤掉已归档和已删除的咖啡豆
                self.coffeeBeans = cachedBeans.filter { !$0.isArchived && !$0.isDeleted }
                print("📦 从缓存加载了 \(coffeeBeans.count) 个咖啡豆 (总数: \(cachedBeans.count))")
            } catch {
                print("❌ 从缓存加载咖啡豆失败: \(error)")
            }
        } else {
            print("⚠️ 无可用的咖啡豆缓存数据")
        }
    }

    func fetchStatistics() async {
        do {
            let statistics: Statistics = try await apiService.get("/ios/api/brewlog/statistics/")
            streakDays = statistics.streakDays
            monthCount = statistics.monthCount
            yearCount = statistics.yearCount
            error = nil
        } catch {
            self.error = error
        }
    }

    func fetchFilterOptions() async {
        do {
            // 从API获取冲煮方式
            struct BrewMethodResponse: Codable {
                let id: String
                let value: String
                let name: String
            }

            let brewMethodsResponse: [BrewMethodResponse] = try await apiService.get("/ios/api/brewlog/brew-methods/")

            await MainActor.run {
                brewMethods = brewMethodsResponse.map { method in
                    // 这里使用value作为id，因为服务器返回的value是实际用于筛选的字段
                    BrewMethod(id: method.value, name: method.name)
                }
                print("🟢 成功获取 \(brewMethods.count) 种冲煮方式")
            }

            // 获取咖啡豆列表
            let response: APIResponse<CoffeeBean> = try await apiService.get("/ios/api/beans/")
            await MainActor.run {
                // 过滤掉已归档和已删除的咖啡豆
                coffeeBeans = response.results.filter { !$0.isArchived && !$0.isDeleted }
            }
        } catch {
            print("❌ 获取筛选选项失败: \(error)")
        }
    }

    // MARK: - Record Management
    func createRecord(_ record: BrewingRecord) async throws {
        isSaving = true
        defer { isSaving = false }

        do {
            try await brewingService.createRecord(record)
            await fetchFilteredRecords()
            await fetchStatistics()  // 更新统计数据，包括连续打卡天数
            clearDraft()
        } catch let error as APIError {
            throw error
        } catch {
            throw APIError.unknown
        }
    }

    func updateRecord(_ record: BrewingRecord) async throws -> BrewingRecord {
        isSaving = true
        defer { isSaving = false }

        do {
            let updatedRecord = try await brewingService.updateRecord(record)
            await fetchFilteredRecords()
            await fetchStatistics()  // 更新统计数据，包括连续打卡天数
            return updatedRecord
        } catch let error as APIError {
            throw error
        } catch {
            throw APIError.unknown
        }
    }

    func startEditing(_ record: BrewingRecord) {
        editingRecord = record
    }

    func cancelEditing() {
        editingRecord = nil
    }

    func saveDraft(_ record: BrewingRecord) {
        draftRecord = record
    }

    func loadDraft() {
        if let data = UserDefaults.standard.data(forKey: "brewlog_draft"),
           let record = try? JSONDecoder().decode(BrewingRecord.self, from: data) {
            draftRecord = record
        }
    }

    func clearDraft() {
        draftRecord = nil
        UserDefaults.standard.removeObject(forKey: "brewlog_draft")
    }

    func loadLastRecord() {
        if let data = UserDefaults.standard.data(forKey: "brewlog_last_record"),
           let record = try? JSONDecoder().decode(BrewingRecord.self, from: data) {
            lastRecord = record
        }
    }

    func saveLastRecord(_ record: BrewingRecord) {
        lastRecord = record
        if let data = try? JSONEncoder().encode(record) {
            UserDefaults.standard.set(data, forKey: "brewlog_last_record")
        }
    }

    @MainActor
    func deleteRecord(_ record: BrewingRecord) async throws {
        // 设置加载状态
        let wasLoading = isLoadingRecords
        if !wasLoading {
            isLoadingRecords = true
        }

        do {
            // 从后端删除记录
            try await brewingService.deleteRecord(record)

            // 从本地列表中移除记录
            brewingRecords.removeAll { $0.id == record.id }

            // 更新计数
            totalCount = max(0, totalCount - 1)

            // 立即获取最新的统计数据
            await fetchStatistics()

            // 如果当前记录是最后一条记录，可能需要更新lastRecord引用
            if lastRecord?.id == record.id {
                lastRecord = brewingRecords.first
            }

            // 恢复加载状态
            if !wasLoading {
                isLoadingRecords = false
            }
        } catch {
            // 恢复加载状态
            if !wasLoading {
                isLoadingRecords = false
            }
            throw error
        }
    }

    // MARK: - Bean Management
    @MainActor
    func deleteBean(_ bean: CoffeeBean) async throws {
        try await brewingService.deleteBean(bean)
        // 从本地列表中移除
        if let index = coffeeBeans.firstIndex(where: { $0.id == bean.id }) {
            coffeeBeans.remove(at: index)
        }
    }

    // MARK: - Heatmap
    func fetchHeatmapData(year: Int? = nil) async {
        isLoadingHeatmap = true
        defer { isLoadingHeatmap = false }

        do {
            let response: HeatmapResponse = try await apiService.get(
                "/ios/api/brewlog/heatmap/",
                parameters: ["year": "\(year ?? Calendar.current.component(.year, from: Date()))"]
            )

            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"

            heatmapData = response.data.map { (dateString, count) in
                let date = formatter.date(from: dateString) ?? Date()
                return HeatmapData(date: date, count: count)
            }.sorted { $0.date < $1.date }

            error = nil
        } catch {
            print("❌ Heatmap error: \(error)")
            self.error = error
        }
    }

    func getHeatmapDataForYear(_ year: Int) -> [HeatmapData] {
        return heatmapData.filter { Calendar.current.component(.year, from: $0.date) == year }
    }

    func getHeatmapYears() -> [Int] {
        let years = Set(heatmapData.map { Calendar.current.component(.year, from: $0.date) })
        return Array(years).sorted(by: >)
    }

    func getHeatmapValue(for date: Date) -> Int {
        if let data = heatmapData.first(where: { Calendar.current.isDate($0.date, inSameDayAs: date) }) {
            return data.count
        }
        return 0
    }

    // MARK: - Hindsight
    func fetchHindsightData(year: Int) async {
        isLoadingHindsight = true
        error = nil

        do {
            let response: HindsightResponse = try await apiService.get(
                "/ios/api/brewlog/hindsight/",
                parameters: ["year": "\(year)"]
            )

            hindsightData = response.toViewData
            error = nil

            // 更新冲煮提醒服务的数据
            if let hindsightData = hindsightData {
                BrewReminderService.shared.updateWithHindsightData(hindsightData)
            }
        } catch {
            self.error = error
            hindsightData = nil
        }

        isLoadingHindsight = false
    }

    // 为冲煮提醒服务获取 Hindsight 数据
    private func fetchHindsightDataForBrewReminder() async {
        // 按优先级尝试不同的时间范围：week > month > halfYear > year
        let timeRanges: [TimeRange] = [.week, .month, .halfYear, .year]

        for timeRange in timeRanges {
            do {
                let response: HindsightResponse = try await apiService.get(
                    "/ios/api/brewlog/hindsight/",
                    parameters: ["time_range": timeRange.rawValue]
                )

                // 如果这个时间范围有 peakTime 数据，就使用它
                if let peakTime = response.peakTime, !peakTime.isEmpty {
                    // 手动设置 timeRange 字段
                    var modifiedResponse = response
                    let hindsightData = modifiedResponse.toViewData

                    // 创建包含正确 timeRange 的 HindsightData
                    let correctedHindsightData = HindsightData(
                        totalBrews: hindsightData.totalBrews,
                        totalRecords: hindsightData.totalRecords,
                        activeDays: hindsightData.activeDays,
                        totalDays: hindsightData.totalDays,
                        averageBrewsPerDay: hindsightData.averageBrewsPerDay,
                        averageRating: hindsightData.averageRating,
                        equipmentStats: hindsightData.equipmentStats,
                        beanStats: hindsightData.beanStats,
                        monthlyBrews: hindsightData.monthlyBrews,
                        ratingDistribution: hindsightData.ratingDistribution,
                        activeRate: hindsightData.activeRate,
                        timeRange: timeRange, // 设置正确的 timeRange
                        totalDose: hindsightData.totalDose,
                        avgDose: hindsightData.avgDose,
                        days250gLasts: hindsightData.days250gLasts,
                        tastedBeansCount: hindsightData.tastedBeansCount,
                        totalBrewingCost: hindsightData.totalBrewingCost,
                        avgCostPerCup: hindsightData.avgCostPerCup,
                        brewingCostRecordsCount: hindsightData.brewingCostRecordsCount,
                        mostUsedMethod: hindsightData.mostUsedMethod,
                        mostUsedBrewer: hindsightData.mostUsedBrewer,
                        mostUsedBrewerBrand: hindsightData.mostUsedBrewerBrand,
                        mostUsedGrinder: hindsightData.mostUsedGrinder,
                        mostUsedGrinderBrand: hindsightData.mostUsedGrinderBrand,
                        peakPeriod: hindsightData.peakPeriod,
                        peakWeekday: hindsightData.peakWeekday,
                        peakTime: hindsightData.peakTime,
                        brewsPerActiveDay: hindsightData.brewsPerActiveDay,
                        mostRepurchasedBean: hindsightData.mostRepurchasedBean,
                        mostRepurchasedBeanRoaster: hindsightData.mostRepurchasedBeanRoaster,
                        mostVisitedRoaster: hindsightData.mostVisitedRoaster,
                        mostVisitedRoasterAmount: hindsightData.mostVisitedRoasterAmount,
                        totalBeanCosts: hindsightData.totalBeanCosts,
                        equipmentCosts: hindsightData.equipmentCosts,
                        favoriteBeanName: hindsightData.favoriteBeanName,
                        favoriteBeanRoaster: hindsightData.favoriteBeanRoaster,
                        favoriteBeanRating: hindsightData.favoriteBeanRating,
                        favoriteBeanCount: hindsightData.favoriteBeanCount,
                        mostUsedBeanName: hindsightData.mostUsedBeanName,
                        mostUsedBeanRoaster: hindsightData.mostUsedBeanRoaster,
                        mostUsedBeanCount: hindsightData.mostUsedBeanCount,
                        mostUsedBeanRating: hindsightData.mostUsedBeanRating,
                        mostCommonOrigin: hindsightData.mostCommonOrigin,
                        mostCommonVariety: hindsightData.mostCommonVariety,
                        mostCommonFlavors: hindsightData.mostCommonFlavors,
                        favoriteRecipeName: hindsightData.favoriteRecipeName,
                        favoriteRecipeRating: hindsightData.favoriteRecipeRating,
                        favoriteRecipeCount: hindsightData.favoriteRecipeCount,
                        mostUsedRecipeName: hindsightData.mostUsedRecipeName,
                        mostUsedRecipeCount: hindsightData.mostUsedRecipeCount,
                        mostUsedRecipeRating: hindsightData.mostUsedRecipeRating,
                        maxStreak: hindsightData.maxStreak,
                        totalAllTimeRecords: hindsightData.totalAllTimeRecords,
                        daysSinceRegistration: hindsightData.daysSinceRegistration
                    )

                    BrewReminderService.shared.updateWithHindsightData(correctedHindsightData)
                    return
                }
            } catch {
                // 继续尝试下一个时间范围
                continue
            }
        }
    }

    // MARK: - Equipment Helper Methods

    func getBrewingEquipments() -> [Equipment] {
        return equipment.filter { $0.type == "BREWER" && !$0.isArchived && !$0.isDeleted }
    }

    func getGrindingEquipments() -> [Equipment] {
        return equipment.filter { $0.type == "GRINDER" && !$0.isArchived && !$0.isDeleted }
    }

    func getGadgets() -> [Equipment] {
        return equipment.filter { $0.type == "GADGET" && !$0.isArchived && !$0.isDeleted }
    }

    func getGadgetKits() -> [Equipment] {
        return equipment.filter { $0.type == "GADGET_KIT" && !$0.isArchived && !$0.isDeleted }
    }

    // MARK: - Data Loading Methods

    func loadFlavorTags() async {
        await tryLoadFlavorTags(useFallbackPath: false)
    }

    private func tryLoadFlavorTags(useFallbackPath: Bool) async {
        do {
            let tags: [FlavorTag]
            if useFallbackPath {
                // 使用备用路径直接调用APIService
                print("🔄 尝试使用备用路径获取风味标签")
                tags = try await apiService.get("/ios/api/flavor-tags/")
            } else {
                // 使用主路径通过brewingService
                tags = try await brewingService.fetchFlavorTags()
            }

            flavorTags = tags
            print("🟢 成功加载 \(flavorTags.count) 个风味标签")

            // 缓存风味标签数据
            if !flavorTags.isEmpty {
                saveFlavorTagsToCache(flavorTags)
            }
        } catch let apiError as APIError {
            print("🔴 加载风味标签失败: \(apiError)")

            // 针对不同错误类型给出更详细的信息
            if case .serverError(let code, let message) = apiError {
                print("❌ 风味标签服务器错误 \(code): \(message)")

                // 如果是404错误且还没有尝试过备用路径，则尝试备用路径
                if code == 404 && !useFallbackPath {
                    print("🔄 风味标签API路径返回404，尝试使用备用路径")
                    await tryLoadFlavorTags(useFallbackPath: true)
                    return
                } else if code == 404 {
                    print("❌ 风味标签API路径可能不正确，所有尝试均失败")
                }
            }

            // 如果获取失败，尝试从缓存加载
            if flavorTags.isEmpty {
                tryLoadFlavorTagsFromCache()
            }
        } catch {
            print("🔴 加载风味标签失败: \(error), 类型: \(type(of: error))")

            // 如果获取失败，尝试从缓存加载
            if flavorTags.isEmpty {
                tryLoadFlavorTagsFromCache()
            }
        }
    }

    // 保存风味标签到缓存
    private func saveFlavorTagsToCache(_ tags: [FlavorTag]) {
        do {
            let data = try JSONEncoder().encode(tags)
            UserDefaults.standard.set(data, forKey: "cached_flavor_tags")
            print("✅ 成功缓存 \(tags.count) 个风味标签")
        } catch {
            print("❌ 缓存风味标签失败: \(error)")
        }
    }

    // 尝试从缓存加载风味标签
    private func tryLoadFlavorTagsFromCache() {
        if let cachedData = UserDefaults.standard.data(forKey: "cached_flavor_tags") {
            do {
                let cachedTags = try JSONDecoder().decode([FlavorTag].self, from: cachedData)
                self.flavorTags = cachedTags
                print("📦 从缓存加载了 \(cachedTags.count) 个风味标签")
            } catch {
                print("❌ 从缓存加载风味标签失败: \(error)")
            }
        } else {
            print("⚠️ 无可用的风味标签缓存数据")
        }
    }

    // MARK: - 对比功能
    // 判断记录是否已被选中进行对比
    func isSelectedForCompare(_ record: BrewingRecord) -> Bool {
        return recordsForCompare.contains(where: { $0.id == record.id })
    }

    // 切换记录的对比选中状态
    func toggleCompare(for record: BrewingRecord) {
        if isSelectedForCompare(record) {
            // 如果已经选中，则移除
            recordsForCompare.removeAll(where: { $0.id == record.id })
        } else {
            // 如果未选中且选中数量小于2，则添加
            if recordsForCompare.count < 2 {
                recordsForCompare.append(record)
            }
        }
    }

    // 添加记录到对比列表
    func addToCompare(_ record: BrewingRecord) -> Bool {
        // 如果已经在列表中，返回false表示未添加成功
        if isSelectedForCompare(record) {
            return false
        }

        // 如果已有2条记录，返回false表示未添加成功
        if recordsForCompare.count >= 2 {
            return false
        }

        // 添加到对比列表
        recordsForCompare.append(record)
        return true
    }

    // 从对比列表中移除记录
    func removeFromCompare(_ record: BrewingRecord) -> Bool {
        // 如果不在列表中，返回false表示未移除成功
        if !isSelectedForCompare(record) {
            return false
        }

        // 从列表中移除
        recordsForCompare.removeAll(where: { $0.id == record.id })
        return true
    }

    // 清空对比列表
    func clearCompare() {
        recordsForCompare.removeAll()
    }

    // 检查是否可以进行对比
    var canCompare: Bool {
        return recordsForCompare.count == 2
    }

    // 在满足条件时启动对比
    func startCompare() {
        if canCompare {
            showingCompareView = true
        }
    }

    // 添加一个单独获取特定ID冲煮记录的方法
    func fetchRecordById(_ id: Int) async -> BrewingRecord? {
        do {
            print("尝试获取ID为\(id)的冲煮记录")
            // 检查token有效性
            await apiService.checkTokenValidity()

            // 使用get方法获取特定ID的冲煮记录
            let endpoint = "/ios/api/brewlog/records/\(id)/"
            let record: BrewingRecord = try await apiService.get(endpoint)

            print("成功获取到ID为\(id)的冲煮记录")
            return record
        } catch {
            print("获取ID为\(id)的冲煮记录失败: \(error.localizedDescription)")
            return nil
        }
    }

    // 新增方法：查找最后一个有记录的月份
    func findLastRecordMonth() async {
        print("📅 当前月份没有记录，尝试查找最后一个有记录的月份")

        // 重置页码
        await MainActor.run {
            currentPage = 1
            hasMoreRecords = true
            isLoadingRecords = true
        }

        do {
            // 先尝试获取所有记录（不使用日期过滤），限制为1条，只为了找到最后一条记录
            let result = try await brewingService.fetchFilteredRecords(
                dateFrom: nil,
                dateTo: nil,
                page: 1,
                pageSize: 1
            )

            if let lastRecord = result.records.first {
                // 找到了最后一条记录，设置为该记录所在的月份
                let calendar = Calendar.current
                let recordDate = lastRecord.createdAt

                if let firstDayOfMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: recordDate)),
                   let lastDayOfMonth = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: firstDayOfMonth) {

                    await MainActor.run {
                        // 更新筛选日期范围
                        filterDateFrom = firstDayOfMonth
                        filterDateTo = lastDayOfMonth

                        // 更新统计数据 - 处理统计信息上的月/年切换
                        let now = Date()
                        let nowComponents = calendar.dateComponents([.year, .month], from: now)
                        let recordComponents = calendar.dateComponents([.year, .month], from: recordDate)

                        // 记录是否月份或年份发生了变化
                        if nowComponents.month != recordComponents.month || nowComponents.year != recordComponents.year {
                            print("📊 日期范围从当前月 \(nowComponents.year!)-\(nowComponents.month!) 变更为 \(recordComponents.year!)-\(recordComponents.month!)")
                        }

                        print("📅 已自动切换到最后记录所在月份: \(dateFormatter.string(from: firstDayOfMonth)) 至 \(dateFormatter.string(from: lastDayOfMonth))")
                    }

                    // 使用新的日期范围重新获取记录
                    await fetchFilteredRecords()

                    // 获取最新的统计数据
                    await fetchStatistics()
                } else {
                    // 日期计算出错，恢复加载状态
                    print("⚠️ 日期计算错误，无法确定记录所在月份")
                    await MainActor.run {
                        isLoadingRecords = false
                    }
                }
            } else {
                // 没有找到任何记录，保持当前月份筛选
                print("📅 未找到任何记录，保持当前月份筛选")
                await MainActor.run {
                    isLoadingRecords = false
                }
            }
        } catch let catchError {
            print("❌ 查找最后记录月份失败: \(catchError)")

            // 确保错误信息在UI上可见
            await MainActor.run {
                if let apiError = catchError as? APIError {
                    self.error = apiError
                } else {
                    self.error = APIError.unknown
                }
                isLoadingRecords = false
            }
        }
    }

    // 添加用户设置
    private var userSettings: UserDefaultsSettings {
        return UserDefaultsSettings.shared
    }

    // 添加排序方法
    private func sortRecords(by sortOrder: String) {
        switch sortOrder {
        case "createdAt_desc":
            brewingRecords.sort(by: { $0.createdAt > $1.createdAt })
        case "createdAt_asc":
            brewingRecords.sort(by: { $0.createdAt < $1.createdAt })
        case "rating_desc":
            brewingRecords.sort(by: { $0.ratingLevel > $1.ratingLevel })
        case "rating_asc":
            brewingRecords.sort(by: { $0.ratingLevel < $1.ratingLevel })
        default:
            brewingRecords.sort(by: { $0.createdAt > $1.createdAt })
        }
    }
}

#if DEBUG
extension BrewLogViewModel {
    static var preview: BrewLogViewModel {
        let viewModel = BrewLogViewModel()
        viewModel.brewingRecords = [
            BrewingRecord.preview,
            BrewingRecord.preview,
            BrewingRecord.preview
        ]
        return viewModel
    }
}
#endif

// MARK: - Date Formatter
extension DateFormatter {
    static let yyyyMMdd: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }()

    static let shareFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日 HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()

    static let dateOnlyFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}

// MARK: - Supporting Types
struct BrewMethod: Codable, Identifiable {
    let id: String
    let name: String
}

// MARK: - Statistics
struct Statistics: Codable {
    let streakDays: Int
    let monthCount: Int
    let yearCount: Int

    enum CodingKeys: String, CodingKey {
        case streakDays = "streak_days"
        case monthCount = "month_count"
        case yearCount = "year_count"
    }
}