import SwiftUI
import UIKit

struct EditBrewLogView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = BrewLogViewModel()
    @Environment(\.presentationMode) var presentationMode

    // 记录原始数据，用于后续提交
    let record: BrewingRecord

    // MARK: - Tab状态
    @State private var selectedTab: Int = 0 // 0: 冲煮信息, 1: 用豆和器具, 2: 测量数据, 3: 环境数据, 4: 配方步骤, 5: 品鉴笔记

    // MARK: - 基本信息状态
    @State private var recipeName = ""
    @State private var rating = 6
    @State private var notes = ""
    @State private var createdAt = Date()

    // MARK: - 用豆和器具状态
    @State private var selectedBean: CoffeeBean?
    @State private var selectedEquipment: Equipment?
    @State private var selectedGrindingEquipment: Equipment?
    @State private var grindSize = ""
    @State private var selectedGadgetKit: Equipment?
    @State private var selectedGadgets = Set<String>()

    // MARK: - 测量数据状态
    @State private var dose = ""
    @State private var yieldWeight = ""
    @State private var temperature = ""
    @State private var brewingHours = "00"
    @State private var brewingMinutes = "00"
    @State private var brewingSeconds = "00"
    @State private var isTimerRunning = false
    @State private var timerSeconds: TimeInterval = 0
    @State private var activeTimer: Timer?

    // MARK: - 环境数据状态
    @State private var waterQuality = ""
    @State private var roomTemperature = ""
    @State private var roomHumidity = ""

    // MARK: - 品鉴笔记状态
    @State private var aroma = 0
    @State private var acidity = 0
    @State private var sweetness = 0
    @State private var bodyRating = 0
    @State private var aftertaste = 0
    @State private var flavorTags: [String] = []

    // MARK: - 步骤状态
    @State private var steps: [BrewingStepItem] = []
    @State private var stepsEnabled = false
    @State private var editingStepIndex: Int? = nil
    @State private var newStepText = ""
    @State private var newStepMinutes = ""
    @State private var newStepSeconds = ""
    @State private var isEditingStepTimer = false
    @State private var stepTimerSeconds: TimeInterval = 0
    @State private var stepTimerActive = false
    @State private var stepTimerActiveIndex: Int? = nil
    @State private var lastDragEndTime: Date? = nil // 记录最后一次拖拽结束的时间

    // MARK: - UI状态
    @State private var errorMessage: String?
    @State private var showingValidationAlert = false
    @State private var validationMessage = ""
    @State private var isLoading = false
    @State private var stepsRefreshID = UUID() // 添加一个状态变量用于强制刷新步骤列表

    // MARK: - 焦点状态
    @FocusState private var focusedField: String?

    // MARK: - 添加ScrollViewProxy声明
    @State private var scrollProxy: ScrollViewProxy?

    // MARK: - 计算属性
    private var formattedTime: String {
        let minutes = Int(timerSeconds) / 60
        let seconds = Int(timerSeconds) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    private var fullBrewingTime: String {
        let time = "\(brewingHours):\(brewingMinutes):\(brewingSeconds)"
        return time
    }

    var body: some View {
        ZStack(alignment: .top) {
            // 固定的顶部导航栏
            topNavigationBar
                .background(Color.primaryBg)
                .zIndex(1)

            ScrollViewReader { proxy in
                ScrollView {
                    VStack(spacing: 8) {
                        // 添加一个占位符，高度等于导航栏高度
                        Color.clear
                            .frame(height: 52)

                        // 主要内容区域
                        VStack(spacing: 16) {
                            // 顶部Tab切换
                            tabSelectionView

                            // 内容区域 - 使用固定内容而不是TabView
                            VStack {
                                // 根据选中的标签显示对应内容
                                if selectedTab == 0 {
                                    brewingInfoTab
                                } else if selectedTab == 1 {
                                    beanAndEquipmentTab
                                } else if selectedTab == 2 {
                                    measurementTab
                                } else if selectedTab == 3 {
                                    environmentTab
                                } else if selectedTab == 4 {
                                    stepsTab
                                } else if selectedTab == 5 {
                                    flavorNotesTab
                                }
                            }
                        }
                        .padding(.horizontal, 12)
                        .padding(.bottom, 80) // 添加底部填充以确保内容完全显示
                    }
                }
                .background(Color.primaryBg)
                .onAppear {
                    scrollProxy = proxy

                    // 添加通知观察器
                    NotificationCenter.default.addObserver(
                        forName: Notification.Name("UpdateEditingStepIndex"),
                        object: nil,
                        queue: .main
                    ) { [self] notification in
                        if let userInfo = notification.userInfo,
                           let fromIndex = userInfo["fromIndex"] as? Int,
                           let toIndex = userInfo["toIndex"] as? Int {
                            // 更新编辑索引
                            if let editingIdx = editingStepIndex {
                                if editingIdx == fromIndex {
                                    editingStepIndex = toIndex
                                } else if editingIdx < fromIndex && editingIdx >= toIndex {
                                    editingStepIndex = editingIdx + 1
                                } else if editingIdx > fromIndex && editingIdx <= toIndex {
                                    editingStepIndex = editingIdx - 1
                                }
                            }
                        }
                    }
                }
                .onDisappear {
                    // 移除通知观察器
                    NotificationCenter.default.removeObserver(self)
                }
                .alert("验证失败", isPresented: $showingValidationAlert) {
                    Button("确定") { }
                } message: {
                    Text(validationMessage)
                }
                .sheet(isPresented: $isEditingStepTimer) {
                    // 空内容，因为我们已经将步骤编辑功能集成到了主视图中
                    EmptyView()
                }
            }
        }
        .task {
            await loadData()
            loadRecordData()
        }
    }

    // MARK: - View Components

    // 顶部导航栏
    private var topNavigationBar: some View {
        ZStack {
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(.linkText)
                }

                Spacer()

                Button("保存") {
                    Task {
                        await updateRecord()
                    }
                }
                .disabled(viewModel.isSaving || !isFormValid)
                .foregroundColor((viewModel.isSaving || !isFormValid) ? Color.gray.opacity(0.5) : .linkText)
            }

            Text("编辑冲煮记录")
                .font(.headline)
                .foregroundColor(.primaryText)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
    }

    // 顶部 Tab 选择器
    private var tabSelectionView: some View {
        HStack(spacing: 4) {
            tabButton(title: "冲煮信息", icon: "note.symbols", index: 0)
            tabButton(title: "用豆器具", icon: "equipment.symbols", index: 1)
            tabButton(title: "测量数据", icon: "ratio.symbols", index: 2)
            tabButton(title: "环境数据", icon: "environment.symbols", index: 3)
            tabButton(title: "配方步骤", icon: "steps.symbols", index: 4)
            tabButton(title: "品鉴笔记", icon: "radar.symbols", index: 5)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color.secondaryBg)
        .cornerRadius(10)
        .padding(.horizontal, 12)
        .padding(.bottom, 10)
    }

    // Tab按钮
    private func tabButton(title: String, icon: String, index: Int, isDisabled: Bool = false) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0.2)) {
                selectedTab = index
            }
        }) {
            ZStack(alignment: .center) {
                // 背景胶囊
                Capsule()
                    .fill(selectedTab == index ? Color.functionText : Color.clear)
                    .frame(height: 36)

                // 内容
                HStack(spacing: 6) {
                    // 图标 - 全部使用自定义图片
                    Image(icon)
                        .resizable()
                        .scaledToFit()
                        .frame(width: selectedTab == index ? 16 : 20, height: selectedTab == index ? 16 : 20)
                        .foregroundColor(selectedTab == index ? Color.primaryBg : Color.noteText)

                    // 文字 - 只在选中时显示，有展开动画
                    if selectedTab == index {
                        Text(title)
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(Color.primaryBg)
                            .lineLimit(1)
                            .transition(.asymmetric(
                                insertion: .move(edge: .leading).combined(with: .opacity),
                                removal: .move(edge: .trailing).combined(with: .opacity)
                            ))
                    }
                }
                .padding(.horizontal, selectedTab == index ? 12 : 0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: selectedTab)
            }
            .frame(height: 40)
            .frame(width: selectedTab == index ? nil : 40)
            // 确保选中时宽度能够自适应内容
            .fixedSize(horizontal: selectedTab == index, vertical: false)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isDisabled)
    }

    // MARK: - 加载数据

    // 加载表单所需基础数据
    private func loadData() async {
        await viewModel.fetchCoffeeBeans()
        await viewModel.fetchEquipments()
        await viewModel.loadFlavorTags()
    }

    // 加载记录数据填充表单
    private func loadRecordData() {
        // 基本信息
        recipeName = record.recipeName ?? ""
        rating = record.ratingLevel
        notes = record.notes
        createdAt = record.createdAt

        // 用豆和器具
        selectedBean = record.coffeeBean
        selectedEquipment = record.brewingEquipment
        selectedGrindingEquipment = record.grindingEquipment
        grindSize = record.grindSize
        selectedGadgetKit = record.gadgetKit

        if let gadgets = record.gadgets {
            selectedGadgets = Set(gadgets.map { String($0.id) })
        }

        // 测量数据
        dose = String(format: "%.1f", record.doseWeight)
        yieldWeight = String(format: "%.1f", record.yieldWeight)
        temperature = record.waterTemperature > 0 ? String(format: "%.1f", record.waterTemperature) : ""

        // 设置时间
        let totalSeconds = Int(record.brewingTime)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        brewingHours = String(format: "%02d", hours)
        brewingMinutes = String(format: "%02d", minutes)
        brewingSeconds = String(format: "%02d", seconds)

        // 环境数据
        waterQuality = record.waterQuality ?? ""
        roomTemperature = record.roomTemperature.map { String($0) } ?? ""
        roomHumidity = record.roomHumidity.map { String($0) } ?? ""

        // 品鉴笔记
        aroma = record.aroma
        acidity = record.acidity
        sweetness = record.sweetness
        bodyRating = record.body
        aftertaste = record.aftertaste
        flavorTags = record.flavorTags.map { $0.name }

        // 步骤
        steps = record.steps
        stepsEnabled = !record.steps.isEmpty
    }

    // MARK: - 实用工具方法

    // 一般表单项
    private func FormSection<Content: View>(_ title: String, note: String? = nil, @ViewBuilder content: () -> Content) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)

                if let note = note {
                    Text(note)
                        .font(.caption)
                        .foregroundColor(.noteText)
                }
            }
            content()
        }
    }

    // 根据评分获取表情
    private func ratingEmoji(for rating: Int) -> String {
        switch rating {
        case 1: return "😫"
        case 2: return "☹️"
        case 3: return "🙁"
        case 4: return "😕"
        case 5: return "😐"
        case 6: return "🙂"
        case 7: return "😌"
        case 8: return "😃"
        case 9: return "😄"
        case 10: return "😆"
        default: return "🙂"
        }
    }

    // 根据评分获取品鉴级别文字
    private func flavorLevelText(for rating: Int) -> String {
        switch rating {
        case 0: return "-"
        case 1: return "1"
        case 2: return "2"
        case 3: return "3"
        case 4: return "4"
        case 5: return "5"
        default: return "-"
        }
    }

    // 自定义可点击定位的滑块
    private func sliderWithTap(value: Binding<Double>, in range: ClosedRange<Double>, step: Double = 1.0) -> some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 背景轨道
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 4)
                    .cornerRadius(2)

                // 已填充部分
                Rectangle()
                    .fill(Color.functionText)
                    .frame(width: CGFloat((value.wrappedValue - range.lowerBound) / (range.upperBound - range.lowerBound)) * geometry.size.width, height: 4)
                    .cornerRadius(2)

                // 滑块按钮
                Circle()
                    .fill(Color.functionText)
                    .frame(width: 24, height: 24)
                    .shadow(color: Color.black.opacity(0.15), radius: 2, x: 0, y: 1)
                    .offset(x: CGFloat((value.wrappedValue - range.lowerBound) / (range.upperBound - range.lowerBound)) * (geometry.size.width - 24))

                // 透明覆盖层用于点击
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle())
                    .frame(height: 40)
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { gesture in
                                let ratio = min(max(0, gesture.location.x / geometry.size.width), 1)
                                let newValue = range.lowerBound + (range.upperBound - range.lowerBound) * Double(ratio)
                                // 根据step约束值
                                let steppedValue = round(newValue / step) * step
                                value.wrappedValue = min(max(range.lowerBound, steppedValue), range.upperBound)
                            }
                    )
            }
        }
        .frame(height: 40) // 只保留滑块自身的高度
    }

    // MARK: - 获取库存状态文本
    private func getStockStatusText(_ bean: CoffeeBean) -> String {
        guard let bagRemain = bean.bagRemain else {
            return ""
        }

        if bagRemain <= 0 {
            return "(已用完)"
        }

        if let bagWeight = bean.bagWeight, bagWeight > 0 {
            let remainText = NumberFormatters.formatWithPrecision(bagRemain, precision: 2)
            let weightText = NumberFormatters.formatWithPrecision(bagWeight, precision: 2)
            return "(剩余 \(remainText)/\(weightText)g)"
        }

        let remainText = NumberFormatters.formatWithPrecision(bagRemain, precision: 2)
        return "(剩余 \(remainText)g)"
    }

    // MARK: - 验证表单数据
    private var isFormValid: Bool {
        // 检查基本必填字段
        if selectedBean == nil || selectedEquipment == nil || selectedGrindingEquipment == nil {
            return false
        }

        // 检查粉重、液重必须有值
        if dose.isEmpty || yieldWeight.isEmpty {
            return false
        }

        // 检查时间格式
        if brewingHours.isEmpty || brewingMinutes.isEmpty || brewingSeconds.isEmpty {
            return false
        }

        // 检查数值是否有效
        if let doseValue = Double(dose), doseValue <= 0 {
            return false
        }

        if let yieldValue = Double(yieldWeight), yieldValue <= 0 {
            return false
        }

        // 检查时间格式是否有效
        if let hours = Int(brewingHours), let minutes = Int(brewingMinutes), let seconds = Int(brewingSeconds) {
            if hours < 0 || minutes < 0 || seconds < 0 || minutes >= 60 || seconds >= 60 {
                return false
            }
        } else {
            return false
        }

        // 检查温度格式（如果填写了温度）
        if !temperature.isEmpty {
            if let temp = Double(temperature), (temp <= 0 || temp >= 100) {
                return false
            }
        }

        return true
    }

    private func validateForm() -> Bool {
        var errors: [String] = []

        // 验证必填字段
        if selectedBean == nil {
            errors.append("请选择咖啡豆")
        }

        if selectedEquipment == nil {
            errors.append("请选择冲煮器具")
        }

        if selectedGrindingEquipment == nil {
            errors.append("请选择磨豆机")
        }

        // 验证数值字段
        if dose.isEmpty {
            errors.append("请输入粉重")
        } else if let doseValue = Double(dose), doseValue <= 0 {
            errors.append("粉重必须大于0")
        }

        if yieldWeight.isEmpty {
            errors.append("请输入液重")
        } else if let yieldValue = Double(yieldWeight), yieldValue <= 0 {
            errors.append("液重必须大于0")
        }

        if !temperature.isEmpty, let tempValue = Double(temperature), (tempValue <= 0 || tempValue >= 100) {
            errors.append("水温必须在0-100℃范围内")
        }

        // 验证时间格式
        if brewingHours.isEmpty || brewingMinutes.isEmpty || brewingSeconds.isEmpty {
            errors.append("请输入完整的萃取时间")
        } else {
            if let hours = Int(brewingHours), let minutes = Int(brewingMinutes), let seconds = Int(brewingSeconds) {
                if hours < 0 || minutes < 0 || seconds < 0 {
                    errors.append("时间不能为负数")
                }
                if minutes >= 60 || seconds >= 60 {
                    errors.append("分钟和秒数必须小于60")
                }
            } else {
                errors.append("时间格式不正确")
            }
        }

        // 显示错误消息
        if !errors.isEmpty {
            validationMessage = errors.joined(separator: "\n")
            showingValidationAlert = true
            return false
        }

        return true
    }

    // MARK: - 更新记录
    private func updateRecord() async {
        // 如果正在编辑步骤，先保存
        if let index = editingStepIndex, index >= 0 && index < steps.count {
            saveStep(at: index)
        }

        // 自动删除空步骤
        steps.removeAll { $0.text.isEmpty }

        // 如果全是空步骤，自动关闭记录详细步骤
        if steps.isEmpty {
            stepsEnabled = false
        }

        // 更新步骤顺序
        for (index, _) in steps.enumerated() {
            steps[index].order = index + 1
        }

        // 验证表单
        if !validateForm() {
            return
        }

        isLoading = true

        // 创建要更新的记录对象
        let updatedRecord = createUpdatedRecord()

        do {
            // 获取服务器返回的最新记录数据
            let serverUpdatedRecord = try await viewModel.updateRecord(updatedRecord)

            // 触发反馈
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(.success)

            // 发送通知更新配方册缓存
            NotificationCenter.default.post(name: NSNotification.Name("RecipeListNeedsRefresh"), object: nil)

            // 发送记录更新通知，让详情视图刷新 - 使用服务器返回的最新数据
            do {
                let recordData = try JSONEncoder().encode(serverUpdatedRecord)
                NotificationCenter.default.post(
                    name: NSNotification.Name("BrewLogRecordUpdated"),
                    object: nil,
                    userInfo: ["recordData": recordData, "recordId": serverUpdatedRecord.id]
                )
            } catch {
                NotificationCenter.default.post(name: NSNotification.Name("BrewLogRecordUpdated"), object: serverUpdatedRecord)
            }

            dismiss()
        } catch {
            errorMessage = "更新失败: \(error.localizedDescription)"
            showingValidationAlert = true
        }

        isLoading = false
    }

    // 创建更新后的记录对象
    private func createUpdatedRecord() -> BrewingRecord {
        // 转换时间为总秒数
        let hours = Int(brewingHours) ?? 0
        let minutes = Int(brewingMinutes) ?? 0
        let seconds = Int(brewingSeconds) ?? 0
        let totalSeconds = hours * 3600 + minutes * 60 + seconds

        // 将选择的小工具ID转换为设备对象
        let gadgets: [Equipment]? = selectedGadgets.isEmpty ? nil :
            viewModel.equipment.filter { equipment in
                selectedGadgets.contains(String(equipment.id))
            }

        // 将字符串风味标签转换为FlavorTag对象
        let flavorTagObjects = flavorTags.compactMap { tagName in
            viewModel.flavorTags.first { $0.name == tagName }
        }



        return BrewingRecord(
            id: record.id,
            recipeName: recipeName.isEmpty ? nil : recipeName,
            coffeeBean: selectedBean!,
            brewingEquipment: selectedEquipment!,
            grindingEquipment: selectedGrindingEquipment,
            grindSize: grindSize.isEmpty ? "-" : grindSize,
            doseWeight: Double(dose) ?? 0,
            yieldWeight: Double(yieldWeight) ?? 0,
            waterTemperature: Double(temperature) ?? 0,
            brewingTime: totalSeconds,
            ratingLevel: rating,
            ratingDisplay: ratingEmoji(for: rating),
            aroma: aroma,
            acidity: acidity,
            sweetness: sweetness,
            body: bodyRating,
            aftertaste: aftertaste,
            waterQuality: waterQuality.isEmpty ? nil : waterQuality,
            roomTemperature: roomTemperature.isEmpty ? nil : Double(roomTemperature),
            roomHumidity: roomHumidity.isEmpty ? nil : Int(roomHumidity),
            steps: steps.filter { !$0.text.isEmpty },
            flavorTags: flavorTagObjects,
            tagOverlap: record.tagOverlap,
            notes: notes,
            createdAt: createdAt,
            gadgets: gadgets,
            gadgetKit: selectedGadgetKit
        )
    }

    // MARK: - 基本信息标签页
    private var brewingInfoTab: some View {
        VStack(spacing: 20) {
            FormSection("配方名称", note: "(选填)") {
                TextField("为这个配方起个名字", text: $recipeName)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .focused($focusedField, equals: "recipeName")
            }
            .padding(.horizontal, 16)

            FormSection("记录时间") {
                DatePicker("", selection: $createdAt, displayedComponents: [.date, .hourAndMinute])
                    .datePickerStyle(CompactDatePickerStyle())
                    .labelsHidden()
                    .accentColor(.functionText)
                    .cornerRadius(12)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 16)

            FormSection("评价", note: "(\(rating)分)") {
                VStack(spacing: 12) {
                    ZStack(alignment: .top) {
                        sliderWithTap(value: Binding(
                            get: { Double(rating) },
                            set: { rating = Int($0) }
                        ), in: 1...10, step: 1)

                        // 在滑块下方单独放置表情，保证对齐
                        HStack(spacing: 8) {
                            ForEach(1...10, id: \.self) { index in
                                Text(ratingEmoji(for: index))
                                    .font(.body)
                                    .frame(maxWidth: .infinity)
                            }
                        }
                        .padding(.top, 37)
                    }
                    .frame(height: 60)
                }
                .padding(.vertical, 8)
            }
            .padding(.horizontal, 16)

            // 自定义备注区域
            VStack(alignment: .leading, spacing: 10) {
                // 标题行 - 将标题和字数统计放在同一行
                HStack {
                    Text("备注")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primaryText)

                    Text("(选填)")
                        .font(.caption)
                        .foregroundColor(.noteText)

                    Spacer()

                    // 字数统计
                    Text("\(notes.count)/500")
                        .font(.caption)
                        .foregroundColor(notes.count >= 500 ? .error : .primaryText)
                }

                // 文本编辑区域
                ZStack(alignment: .topLeading) {
                    TextEditor(text: $notes)
                        .font(.system(size: 17))
                        .scrollContentBackground(.hidden)
                        .background(Color.clear)
                        .frame(minHeight: 100)
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.secondaryBg)
                        )
                        .onChange(of: notes) { newValue in
                            if newValue.count > 500 {
                                notes = String(newValue.prefix(500))
                            }
                        }

                    if notes.isEmpty {
                        Text("请输入备注信息")
                            .foregroundColor(.gray)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 12)
                            .background(Color.clear)
                            .allowsHitTesting(false)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 用豆和器具标签页
    private var beanAndEquipmentTab: some View {
        VStack(spacing: 20) {
            // 咖啡豆选择
            FormSection("咖啡豆") {
                if viewModel.coffeeBeans.isEmpty {
                    Text("没有可用的咖啡豆，请先添加咖啡豆")
                        .foregroundColor(.secondary)
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .frame(maxWidth: .infinity, alignment: .leading)
                } else {
                    Picker("选择咖啡豆", selection: $selectedBean) {
                        Text("请选择").tag(Optional<CoffeeBean>.none)
                        ForEach(viewModel.coffeeBeans) { bean in
                            let favoriteText = bean.isFavorite ? " ⭐" : ""
                            let stockText = getStockStatusText(bean)
                            let displayText = "\(bean.roaster) \(bean.name)\(favoriteText) \(stockText)"
                            Text(displayText)
                                .tag(Optional(bean))
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .multilineTextAlignment(.leading)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .padding(.horizontal, 16)

            // 冲煮器具选择
            FormSection("冲煮器具") {
                let brewers = viewModel.getBrewingEquipments()
                if brewers.isEmpty {
                    Text("没有可用的冲煮器具，请先添加器具")
                        .foregroundColor(.secondary)
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .frame(maxWidth: .infinity, alignment: .leading)
                } else {
                    Picker("选择冲煮器具", selection: $selectedEquipment) {
                        Text("请选择").tag(Optional<Equipment>.none)
                        ForEach(brewers) { equipment in
                            let favoriteSymbol = equipment.isFavorite ? " ⭐" : ""
                            let brandText = equipment.brand ?? ""
                            let displayText = "\(brandText) \(equipment.name)\(favoriteSymbol)"
                            Text(displayText)
                                .tag(Optional(equipment))
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .multilineTextAlignment(.leading)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .padding(.horizontal, 16)

            // 磨豆机选择
            FormSection("磨豆机") {
                let grinders = viewModel.getGrindingEquipments()
                if grinders.isEmpty {
                    Text("没有可用的磨豆机，请先添加器具")
                        .foregroundColor(.secondary)
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .frame(maxWidth: .infinity, alignment: .leading)
                } else {
                    Picker("选择磨豆机", selection: $selectedGrindingEquipment) {
                        Text("请选择").tag(Optional<Equipment>.none)
                        ForEach(grinders) { equipment in
                            let favoriteSymbol = equipment.isFavorite ? " ⭐" : ""
                            let brandText = equipment.brand ?? ""
                            let displayText = "\(brandText) \(equipment.name)\(favoriteSymbol)"
                            Text(displayText)
                                .tag(Optional(equipment))
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .multilineTextAlignment(.leading)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .onChange(of: selectedGrindingEquipment) { newEquipment in
                        // 当选择新的磨豆机时，如果有预设研磨档位，自动填充
                        if let equipment = newEquipment, let preset = equipment.grindSizePreset, !preset.isEmpty {
                            grindSize = preset
                        }
                    }
                }
            }
            .padding(.horizontal, 16)

            // 研磨设置
            FormSection("研磨设置") {
                TextField("例如32挡或2.1.0刻度，不限格式", text: $grindSize)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .focused($focusedField, equals: "grindSize")
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 16)

            // 小工具组合
            FormSection("小工具组合", note: "(选填)") {
                let gadgetKits = viewModel.getGadgetKits()
                if gadgetKits.isEmpty {
                    Text("没有可用的小工具组合，请先添加")
                        .foregroundColor(.secondary)
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .frame(maxWidth: .infinity, alignment: .leading)
                } else {
                    Picker("选择小工具组合", selection: $selectedGadgetKit) {
                        Text("请选择").tag(Optional<Equipment>.none)
                        ForEach(gadgetKits) { kit in
                            let favoriteSymbol = kit.isFavorite ? " ⭐" : ""
                            let displayText = "\(kit.name)\(favoriteSymbol)"
                            Text(displayText)
                                .tag(Optional(kit))
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .multilineTextAlignment(.leading)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .onChange(of: selectedGadgetKit) { newKit in
                        if let kit = newKit {
                            // 清除当前选择的小工具
                            selectedGadgets.removeAll()

                            // 获取小工具组合中的小工具ID并自动勾选
                            let componentIds = kit.getGadgetComponentIds()
                            for id in componentIds {
                                selectedGadgets.insert(id)
                            }
                        } else {
                            // 当切换到"请选择"时，清除所有小工具的勾选状态
                            selectedGadgets.removeAll()
                        }
                    }
                }
            }
            .padding(.horizontal, 16)

            // 小工具选择
            FormSection("小工具", note: "(选填，可多选)") {
                let gadgets = viewModel.getGadgets()
                if gadgets.isEmpty {
                    Text("没有可用的小工具，请先添加")
                        .foregroundColor(.secondary)
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .frame(maxWidth: .infinity, alignment: .leading)
                } else {
                    VStack(spacing: 0) {
                        ForEach(gadgets) { gadget in
                            Button(action: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.6, blendDuration: 0.2)) {
                                    if selectedGadgets.contains(String(gadget.id)) {
                                        selectedGadgets.remove(String(gadget.id))
                                        // 当取消勾选小工具时，如果该小工具属于已选组合，则清除组合选择
                                        if let kit = selectedGadgetKit, kit.getGadgetComponentIds().contains(String(gadget.id)) {
                                            selectedGadgetKit = nil
                                        }
                                    } else {
                                        selectedGadgets.insert(String(gadget.id))
                                        // 只有在手动添加小工具（非通过组合自动选择）时，才清除小工具组合选择
                                        if selectedGadgetKit != nil {
                                            // 检查是否是来自组合的自动勾选
                                            let isPartOfCurrentKit = selectedGadgetKit?.getGadgetComponentIds().contains(String(gadget.id)) ?? false
                                            // 如果不是来自当前选中的组合，则清除组合选择
                                            if !isPartOfCurrentKit {
                                                selectedGadgetKit = nil
                                            }
                                        }
                                    }
                                }
                            }) {
                                HStack(spacing: 12) {
                                    // 圆形勾选框
                                    ZStack {
                                        Circle()
                                            .stroke(selectedGadgets.contains(String(gadget.id)) ? Color.functionText : Color.gray.opacity(0.5), lineWidth: 1.5)
                                            .frame(width: 22, height: 22)

                                        if selectedGadgets.contains(String(gadget.id)) {
                                            Circle()
                                                .fill(Color.functionText)
                                                .frame(width: 22, height: 22)

                                            Image(systemName: "checkmark")
                                                .font(.system(size: 12, weight: .bold))
                                                .foregroundColor(.white)
                                                .transition(.scale.combined(with: .opacity))
                                        }
                                    }
                                    .contentShape(Rectangle())

                                    // 文本内容，使用VStack在需要时能自动换行而不会产生过多右侧留白
                                    VStack(alignment: .leading, spacing: 2) {
                                        if let brand = gadget.brand, !brand.isEmpty {
                                            Text(brand)
                                                .foregroundColor(.noteText)
                                            + Text(" \(gadget.name)")
                                                .foregroundColor(.primaryText)
                                        } else {
                                            Text(gadget.name)
                                                .foregroundColor(.primaryText)
                                        }
                                    }
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .layoutPriority(1)
                                }
                                .padding(.vertical, 12)
                                .padding(.horizontal, 16)
                                .contentShape(Rectangle())
                                .background(
                                    selectedGadgets.contains(String(gadget.id))
                                    ? Color.secondaryBg.opacity(0.3)
                                    : Color.clear
                                )
                            }
                            .buttonStyle(PlainButtonStyle())

                            if gadget.id != gadgets.last?.id {
                                Divider()
                                    .padding(.horizontal, 16)
                            }
                        }
                    }
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 测量数据标签页
    private var measurementTab: some View {
        VStack(spacing: 20) {
            // 粉重
            FormSection("粉重(g)") {
                TextField("请输入咖啡粉重量", text: $dose)
                    .keyboardType(.decimalPad)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .focused($focusedField, equals: "dose")
            }
            .padding(.horizontal, 16)

            // 液重
            FormSection("液重(g)") {
                TextField("请输入咖啡液重量，或注水量", text: $yieldWeight)
                    .keyboardType(.decimalPad)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .focused($focusedField, equals: "yieldWeight")
            }
            .padding(.horizontal, 16)

            // 水温
            FormSection("水温(℃)", note: "(选填)") {
                TextField("请输入冲煮水温", text: $temperature)
                    .keyboardType(.decimalPad)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .focused($focusedField, equals: "temperature")
            }
            .padding(.horizontal, 16)

            // 萃取时间
            FormSection("萃取时间（时:分:秒）") {
                HStack {
                    // 时
                    TextField("时", text: $brewingHours)
                        .keyboardType(.numberPad)
                        .multilineTextAlignment(.center)
                        .frame(width: 60)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 8)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .focused($focusedField, equals: "brewingHours")

                    Text(":")
                        .font(.title3)
                        .foregroundColor(.primaryText)

                    // 分
                    TextField("分", text: $brewingMinutes)
                        .keyboardType(.numberPad)
                        .multilineTextAlignment(.center)
                        .frame(width: 60)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 8)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .focused($focusedField, equals: "brewingMinutes")

                    Text(":")
                        .font(.title3)
                        .foregroundColor(.primaryText)

                    // 秒
                    TextField("秒", text: $brewingSeconds)
                        .keyboardType(.numberPad)
                        .multilineTextAlignment(.center)
                        .frame(width: 60)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 8)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .focused($focusedField, equals: "brewingSeconds")

                    Spacer()
                }

                // 计时器按钮移到下一行
                Button {
                    if isTimerRunning {
                        stopTimer()
                        updateTimeFromTimer()
                    } else {
                        startTimer()
                    }
                } label: {
                    HStack {
                        Image(systemName: isTimerRunning ? "stop.circle.fill" : "play.circle.fill")
                        Text(isTimerRunning ? formattedTime : "自动计时记录")
                    }
                    .foregroundColor(isTimerRunning ? .red : .functionText)
                    .padding(.vertical, 10)
                    .padding(.horizontal, 16)
                    .background(isTimerRunning ? Color.red.opacity(0.1) : Color.functionText.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding(.top, 8)
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 环境数据标签页
    private var environmentTab: some View {
        VStack(spacing: 20) {
            FormSection("水质", note: "(选填)") {
                TextField("请输入水质或水源，格式不限", text: $waterQuality)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .focused($focusedField, equals: "waterQuality")
            }
            .padding(.horizontal, 16)

            FormSection("室温(℃)", note: "(选填)") {
                TextField("请输入当前室温", text: $roomTemperature)
                    .keyboardType(.decimalPad)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .focused($focusedField, equals: "roomTemperature")
            }
            .padding(.horizontal, 16)

            FormSection("环境湿度(%)", note: "(选填)") {
                TextField("请输入环境湿度值", text: $roomHumidity)
                    .keyboardType(.numberPad)
                    .font(.system(size: 17))
                    .padding(.vertical, 14)
                    .padding(.horizontal, 16)
                    .background(Color.secondaryBg)
                    .cornerRadius(12)
                    .focused($focusedField, equals: "roomHumidity")
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 品鉴笔记标签页
    private var flavorNotesTab: some View {
        VStack(spacing: 20) {
            // 风味标签
            VStack(alignment: .leading, spacing: 10) {
                HStack(alignment: .center, spacing: 4) {
                    Text("我尝到了哪些风味？")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)

                    Text("(选填，请填写实际品尝到的风味)")
                        .font(.caption)
                        .foregroundColor(.noteText)

                    Spacer()
                }
                FlavorTagsInput(selectedTags: $flavorTags)
                    .hideKeyboardToolbar() // 使用修饰器隐藏工具栏
            }
            .id("flavorTagsSection")
            .padding(.horizontal, 16)
            .onChange(of: focusedField) { newValue in
                if newValue?.contains("flavorTags") ?? false {
                    withAnimation {
                        // 添加延迟以确保键盘已经显示
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            scrollProxy?.scrollTo("flavorTagsSection", anchor: .top)
                        }
                    }
                }
            }

            // 香气
            FormSection("香气") {
                VStack(spacing: 8) {
                    flavorRatingSlider(value: $aroma)
                }
            }
            .padding(.horizontal, 16)

            // 酸质
            FormSection("酸质") {
                VStack(spacing: 8) {
                    flavorRatingSlider(value: $acidity)
                }
            }
            .padding(.horizontal, 16)

            // 醇厚
            FormSection("醇厚") {
                VStack(spacing: 8) {
                    flavorRatingSlider(value: $bodyRating)
                }
            }
            .padding(.horizontal, 16)

            // 甜度
            FormSection("甜度") {
                VStack(spacing: 8) {
                    flavorRatingSlider(value: $sweetness)
                }
            }
            .padding(.horizontal, 16)

            // 余韵
            FormSection("余韵") {
                VStack(spacing: 8) {
                    flavorRatingSlider(value: $aftertaste)
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 品鉴维度滑块 (原版，用于冲煮记录评价)
    private func flavorSlider(value: Binding<Int>) -> some View {
        ZStack(alignment: .top) {
            sliderWithTap(value: Binding(
                get: { Double(value.wrappedValue) },
                set: { value.wrappedValue = Int($0) }
            ), in: 1...10, step: 1)

            // 在滑块下方单独放置表情，保证对齐
            HStack(spacing: 0) {
                ForEach(1...10, id: \.self) { index in
                    Text(ratingEmoji(for: index))
                        .font(.caption2)
                        .frame(maxWidth: .infinity)
                }
            }
            .padding(.horizontal, 12)
            .padding(.top, 32)
        }
        .frame(height: 60)
    }

    // 新的品鉴评分滑块 (0-5分)
    private func flavorRatingSlider(value: Binding<Int>) -> some View {
        ZStack(alignment: .top) {
            sliderWithTap(value: Binding(
                get: { Double(value.wrappedValue) },
                set: { value.wrappedValue = Int($0) }
            ), in: 0...5, step: 1)

            // 在滑块下方放置数字和文本标签
            HStack(spacing: 28) {
                ForEach(0...5, id: \.self) { index in
                    Text(flavorLevelText(for: index))
                        .font(.body)
                        .frame(maxWidth: .infinity)
                }
            }
            .padding(.top, 32)
        }
        .frame(height: 60)
    }

    // 配方步骤标签页
    private var stepsTab: some View {
        VStack(spacing: 20) {
            // 标题和开关区域
            StepsHeaderView(stepsEnabled: $stepsEnabled, steps: $steps, addNewEmptyStep: addNewEmptyStep)

            // 当步骤超过2个时显示拖拽提示
            if stepsEnabled && steps.count >= 2 {
                HStack {
                    Text("(选填，长按可拖拽排序)")
                        .font(.caption)
                        .foregroundColor(.noteText)
                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.top, -12)
            }

            if stepsEnabled {
                VStack(spacing: 12) {
                    if steps.isEmpty {
                        Text("暂无步骤")
                            .foregroundColor(.secondary)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(Color.secondaryBg)
                            .cornerRadius(12)
                            .padding(.horizontal, 16)
                    } else {
                        // 步骤列表（支持拖拽排序）- 移除ScrollView，让步骤列表成为整体滚动的一部分
                        ZStack {
                            StepListView(
                                steps: $steps,
                                editingStepIndex: $editingStepIndex,
                                stepItemView: stepItemView,
                                refreshID: stepsRefreshID
                            )
                            .id(stepsRefreshID) // 使用stepsRefreshID作为视图的ID，确保在保存步骤后能正确刷新

                            // 如果正在编辑，添加一个底部空间以避免内容被添加按钮遮挡
                            if editingStepIndex != nil {
                                Spacer()
                                    .frame(height: 60)
                                    .frame(maxWidth: .infinity)
                            }
                        }
                    }

                    // 添加步骤按钮
                    // 如果是最后一个步骤且正在编辑，不显示添加按钮避免重叠
                    if editingStepIndex != steps.count - 1 {
                        AddStepButton(
                            onTap: {
                                // 如果有正在编辑的步骤，先保存
                                if let index = editingStepIndex, index >= 0 && index < steps.count {
                                    saveStep(at: index)
                                }
                                addNewEmptyStep()
                            }
                        )
                    }
                }
            }

            Spacer(minLength: 16)
        }
        .background(Color.primaryBg)
        .cornerRadius(10)
        .padding(.bottom, 16)
        .onTapGesture {
            // 点击整个标签页区域时，如果有正在编辑的步骤，保存它
            // 这里只会在点击步骤列表外的区域时触发
            if let index = editingStepIndex, index >= 0 && index < steps.count {
                saveStep(at: index)
            }
        }
        .id("stepsTab-\(stepsRefreshID)") // 为整个标签页添加ID，确保在保存步骤后能正确刷新
    }

    // 步骤标题和开关组件
    private struct StepsHeaderView: View {
        @Binding var stepsEnabled: Bool
        @Binding var steps: [BrewingStepItem]
        var addNewEmptyStep: () -> Void

        var body: some View {
            HStack {
                Text("记录详细步骤")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primaryText)

                Spacer()

                Toggle("", isOn: $stepsEnabled)
                    .labelsHidden()
                    .tint(.functionText)
                    .onChange(of: stepsEnabled) { enabled in
                        if enabled {
                            // 如果开启步骤且没有步骤，自动添加一个空步骤并进入编辑状态
                            if steps.isEmpty {
                                addNewEmptyStep()
                            }
                        }
                        // 移除关闭时清空步骤的代码
                    }
            }
            .padding(.horizontal, 16)
        }
    }

    // 步骤列表视图
    private struct StepListView: View {
        @Binding var steps: [BrewingStepItem]
        @Binding var editingStepIndex: Int?
        @State private var activeStep: BrewingStepItem?
        var stepItemView: (BrewingStepItem, Int) -> AnyView
        var refreshID: UUID // 添加刷新ID参数

        var body: some View {
            LazyVStack(spacing: 12) {
                ReorderableForEach(steps, active: $activeStep) { step in
                    let index = steps.firstIndex(of: step) ?? 0
                    stepItemView(step, index)
                        .padding(.horizontal, 16)
                        .id("\(refreshID)-\(step.uuid)-\(index)") // 使用刷新ID和步骤UUID组合作为视图ID
                } moveAction: { fromOffsets, toOffset in
                    steps.move(fromOffsets: fromOffsets, toOffset: toOffset)

                    // 更新步骤顺序
                    for (idx, _) in steps.enumerated() {
                        steps[idx].order = idx + 1
                    }

                    // 更新编辑索引
                    if let editingIdx = editingStepIndex {
                        if fromOffsets.contains(editingIdx) {
                            // 如果正在移动的是正在编辑的步骤
                            let newIndex = toOffset > editingIdx ? toOffset - 1 : toOffset
                            editingStepIndex = newIndex
                        } else if editingIdx >= fromOffsets.first! && editingIdx < toOffset {
                            // 如果编辑的步骤在移动范围内，向前移动
                            editingStepIndex = editingIdx - 1
                        } else if editingIdx >= toOffset && editingIdx < fromOffsets.first! {
                            // 如果编辑的步骤在移动范围内，向后移动
                            editingStepIndex = editingIdx + 1
                        }
                    }
                }
            }
            .reorderableForEachContainer(active: $activeStep)
        }
    }

    // 添加步骤按钮组件
    private struct AddStepButton: View {
        var onTap: () -> Void

        var body: some View {
            Button(action: onTap) {
                HStack {
                    Image(systemName: "plus.circle")
                    Text("添加步骤")
                }
                .foregroundColor(.functionText)
                .padding(.vertical, 10)
                .frame(maxWidth: .infinity)
                .background(Color.functionText.opacity(0.1))
                .cornerRadius(8)
            }
            .padding(.horizontal, 16)
            .padding(.top, 8)
        }
    }

    // 步骤项视图
    private func stepItemView(step: BrewingStepItem, index: Int) -> AnyView {
        AnyView(
            VStack {
                // 如果当前步骤处于编辑状态
                if editingStepIndex == index {
                    stepEditView(index: index)
                } else {
                    // 正常显示状态
                    normalStepView(step: step, index: index)
                }
            }
        )
    }

    // 正常显示状态的步骤视图
    private func normalStepView(step: BrewingStepItem, index: Int) -> some View {
        HStack(alignment: .top, spacing: 12) {
            // 步骤序号
            Text("\(index + 1)")
                .font(.headline)
                .frame(width: 24, alignment: .center)
                .foregroundColor(.primaryText)

            // 步骤内容
            VStack(alignment: .leading, spacing: 4) {
                Text(step.text.isEmpty ? "步骤 \(index + 1)" : step.text)
                    .foregroundColor(.primaryText)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .lineLimit(nil) // 允许显示多行
                    .fixedSize(horizontal: false, vertical: true) // 允许垂直方向自动扩展
                    .multilineTextAlignment(.leading) // 确保多行文本左对齐

                if let timer = step.timer {
                    HStack {
                        Image(systemName: "timer")
                        Text(timer)
                    }
                    .font(.caption)
                    .foregroundColor(.secondaryText)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // 操作按钮组
            HStack(spacing: 4) {
                // 编辑按钮
                Button(action: {
                    editStep(at: index)
                }) {
                    Image(systemName: "pencil")
                        .foregroundColor(.linkText)
                        .frame(width: 20, height: 20)
                        .padding(6)
                }

                // 删除按钮
                Button(action: {
                    deleteStep(at: index)
                }) {
                    Image(systemName: "trash")
                        .foregroundColor(.error)
                        .frame(width: 20, height: 20)
                        .padding(6)
                }
            }
            .padding(.top, 2) // 轻微下移整个按钮组以达到中线对齐
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .contentShape(Rectangle())
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.secondaryBg)
        )
        .cornerRadius(12)
        .onTapGesture {
            // 检查是否刚刚完成了拖拽操作
            if let lastDragTime = lastDragEndTime,
               Date().timeIntervalSince(lastDragTime) < 0.3 { // 300毫秒内不响应点击
                // 不执行任何操作，防止拖拽后立即触发编辑
                return
            }
            editStep(at: index)
        } // 添加点击手势，点击步骤卡片时进入编辑状态
    }

    // 步骤编辑视图
    private func stepEditView(index: Int) -> some View {
        VStack(spacing: 12) {
            // 步骤描述输入
            ZStack(alignment: .topLeading) {
                TextEditor(text: $newStepText)
                    .font(.system(size: 17))
                    .scrollContentBackground(.hidden)
                    .background(Color.clear)
                    .padding(8)
                    .frame(minHeight: 100)
                    .background(Color.secondaryBg.opacity(0.7))
                    .cornerRadius(12)
                    .focused($focusedField, equals: "stepText\(index)")
                    .submitLabel(.return) // 明确指定回车键行为为插入换行符
                    .onChange(of: newStepText) { value in
                        // 实时更新步骤文本，避免保存时丢失
                        if index >= 0 && index < steps.count {
                            // 创建一个全新的步骤数组
                            var newStepsArray = [BrewingStepItem]()

                            // 遍历步骤，为当前编辑的步骤创建新对象
                            for (i, step) in steps.enumerated() {
                                if i == index {
                                    let newStep = BrewingStepItem(
                                        uuid: step.uuid,
                                        text: value,
                                        timer: step.timer,
                                        order: step.order
                                    )
                                    newStepsArray.append(newStep)
                                } else {
                                    newStepsArray.append(step)
                                }
                            }

                            // 替换整个数组
                            steps = newStepsArray
                        }
                    }

                if newStepText.isEmpty {
                    Text("请输入步骤描述")
                        .font(.system(size: 17))
                        .foregroundColor(.gray)
                        .padding(.top, 16)
                        .padding(.leading, 16)
                        .allowsHitTesting(false)
                }
            }

            // 分割线
            Rectangle()
                .fill(Color.gray.opacity(0.2))
                .frame(height: 1)
                .padding(.horizontal, 4)
                .padding(.vertical, 4)

            // 计时器设置
            HStack {
                // 分钟输入
                TextField("分", text: $newStepMinutes)
                    .keyboardType(.numberPad)
                    .multilineTextAlignment(.center)
                    .frame(width: 60)
                    .font(.system(size: 17))
                    .padding(.vertical, 10)
                    .padding(.horizontal, 8)
                    .background(Color.secondaryBg.opacity(0.7))
                    .cornerRadius(8)
                    .focused($focusedField, equals: "stepMinutes\(index)")
                    .onChange(of: newStepMinutes) { value in
                        // 实时更新步骤时间，避免保存时丢失
                        if index >= 0 && index < steps.count {
                            // 创建timer字符串
                            let minutes = Int(value) ?? 0
                            let seconds = Int(newStepSeconds) ?? 0
                            let timerString = (minutes > 0 || seconds > 0) ?
                                String(format: "%d:%02d", minutes, seconds) : nil

                            // 创建一个全新的步骤数组
                            var newStepsArray = [BrewingStepItem]()

                            // 遍历步骤，为当前编辑的步骤创建新对象
                            for (i, step) in steps.enumerated() {
                                if i == index {
                                    let newStep = BrewingStepItem(
                                        uuid: step.uuid,
                                        text: step.text,
                                        timer: timerString,
                                        order: step.order
                                    )
                                    newStepsArray.append(newStep)
                                } else {
                                    newStepsArray.append(step)
                                }
                            }

                            // 替换整个数组
                            steps = newStepsArray
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture { } // 阻止点击事件冒泡

                Text(":")
                    .font(.title3)
                    .foregroundColor(.primaryText)

                // 秒数输入
                TextField("秒", text: $newStepSeconds)
                    .keyboardType(.numberPad)
                    .multilineTextAlignment(.center)
                    .frame(width: 60)
                    .font(.system(size: 17))
                    .padding(.vertical, 10)
                    .padding(.horizontal, 8)
                    .background(Color.secondaryBg.opacity(0.7))
                    .cornerRadius(8)
                    .focused($focusedField, equals: "stepSeconds\(index)")
                    .onChange(of: newStepSeconds) { value in
                        // 实时更新步骤时间，避免保存时丢失
                        if index >= 0 && index < steps.count {
                            // 创建timer字符串
                            let minutes = Int(newStepMinutes) ?? 0
                            let seconds = Int(value) ?? 0
                            let timerString = (minutes > 0 || seconds > 0) ?
                                String(format: "%d:%02d", minutes, seconds) : nil

                            // 创建一个全新的步骤数组
                            var newStepsArray = [BrewingStepItem]()

                            // 遍历步骤，为当前编辑的步骤创建新对象
                            for (i, step) in steps.enumerated() {
                                if i == index {
                                    let newStep = BrewingStepItem(
                                        uuid: step.uuid,
                                        text: step.text,
                                        timer: timerString,
                                        order: step.order
                                    )
                                    newStepsArray.append(newStep)
                                } else {
                                    newStepsArray.append(step)
                                }
                            }

                            // 替换整个数组
                            steps = newStepsArray
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture { } // 阻止点击事件冒泡

                Spacer()

                // 计时器按钮
                Button {
                    if stepTimerActive && stepTimerActiveIndex == index {
                        stopStepTimer()
                        updateStepTimeFromTimer()
                    } else {
                        startStepTimer(forIndex: index)
                    }
                } label: {
                    HStack {
                        Image(systemName: stepTimerActive && stepTimerActiveIndex == index ? "stop.circle.fill" : "play.circle.fill")
                        Text(stepTimerActive && stepTimerActiveIndex == index ? formatStepTime(stepTimerSeconds) : "计时")
                    }
                    .foregroundColor(stepTimerActive && stepTimerActiveIndex == index ? .red : .functionText)
                }
                .padding(.vertical, 6)
                .padding(.horizontal, 12)
                .background(
                    (stepTimerActive && stepTimerActiveIndex == index) ?
                        Color.red.opacity(0.1) : Color.functionText.opacity(0.1)
                )
                .cornerRadius(8)
                .contentShape(Rectangle())
                .onTapGesture {
                    if stepTimerActive && stepTimerActiveIndex == index {
                        stopStepTimer()
                        updateStepTimeFromTimer()
                    } else {
                        startStepTimer(forIndex: index)
                    }
                } // 阻止点击事件冒泡并处理点击
            }

            // 添加完成按钮
            Button(action: {
                if !newStepText.isEmpty {
                    saveStep(at: index)
                }
            }) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                    Text("完成")
                }
                .foregroundColor(.functionText)
                .padding(.vertical, 10)
                .frame(maxWidth: .infinity)
                .background(Color.functionText.opacity(0.1))
                .cornerRadius(8)
            }
            .disabled(newStepText.isEmpty)
            .opacity(newStepText.isEmpty ? 0.5 : 1)
        }
        .padding()
        .background(Color.secondaryBg)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.08), radius: 4, x: 0, y: 2) // 添加轻微阴影增强视觉区分
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.functionText.opacity(0.2), lineWidth: 1) // 添加轻微边框增强视觉区分
        )
        .contentShape(Rectangle())
        .onTapGesture { } // 阻止点击事件冒泡
        .zIndex(110) // 确保编辑视图始终在拖拽项之上
    }

    // MARK: - 步骤管理方法

    // 添加新的空步骤
    private func addNewEmptyStep() {
        // 取消当前可能正在进行的编辑
        if editingStepIndex != nil {
            cancelStepEdit()
        }

        // 停止可能正在运行的计时器
        if stepTimerActive {
            stopStepTimer()
        }

        // 计算新步骤的顺序
        let nextOrder = (steps.map { $0.order }.max() ?? 0) + 1
        let newIndex = steps.count

        // 创建一个唯一ID的新步骤
        let newStep = BrewingStepItem(text: "", timer: nil, order: nextOrder)

        // 添加新步骤
        withAnimation {
            steps.append(newStep)
        }

        // 准备编辑状态的变量
        newStepText = ""
        newStepMinutes = ""
        newStepSeconds = ""

        // 延迟一下再进入编辑状态，确保UI已经更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // 设置编辑状态
            editingStepIndex = newIndex

            // 聚焦到文本框
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                focusedField = "stepText\(newIndex)"
            }
        }
    }

    // 编辑步骤
    private func editStep(at index: Int) {
        // 如果已经在编辑其他步骤，先保存那个步骤
        if let currentEditingIndex = editingStepIndex, currentEditingIndex != index, currentEditingIndex >= 0 && currentEditingIndex < steps.count {
            saveStep(at: currentEditingIndex)
        }

        // 确保索引有效
        guard index >= 0 && index < steps.count else { return }

        let step = steps[index]

        // 检查步骤文本是否为预设的"步骤 x"格式，如果是，则在编辑时清空
        if step.text == "步骤 \(index + 1)" {
            newStepText = "" // 如果是默认文本，则清空让用户重新输入
        } else {
            newStepText = step.text
        }

        // 解析计时器时间
        if let timer = step.timer {
            let components = timer.split(separator: ":")
            if components.count == 2 {
                newStepMinutes = String(components[0])
                newStepSeconds = String(components[1])
            } else {
                newStepMinutes = "0"
                newStepSeconds = "0"
            }
        } else {
            newStepMinutes = "0"
            newStepSeconds = "0"
        }

        // 停止可能正在运行的计时器
        if stepTimerActive {
            stopStepTimer()
        }

        // 设置编辑状态
        editingStepIndex = index

        // 聚焦到文本框
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            focusedField = "stepText\(index)"
        }
    }

    // 保存步骤
    private func saveStep(at index: Int) {
        // 确保索引有效
        guard index >= 0 && index < steps.count else {
            cancelStepEdit()
            return
        }

        // 检查是否有计时器
        var timerString: String? = nil
        if !newStepMinutes.isEmpty || !newStepSeconds.isEmpty {
            let minutes = Int(newStepMinutes) ?? 0
            let seconds = Int(newStepSeconds) ?? 0
            if minutes > 0 || seconds > 0 {
                timerString = String(format: "%d:%02d", minutes, seconds)
            }
        }

        // 如果文本为空且没有计时器，删除这个步骤
        if newStepText.isEmpty && timerString == nil {
            // 复用cancelStepEdit的逻辑，它会检查并删除空步骤
            cancelStepEdit()
            return
        }

        // 确保文本不为空
        var finalText = newStepText
        if finalText.isEmpty {
            finalText = "步骤 \(index + 1)"
        }

        // 创建全新的数组，而不是修改现有元素
        var newStepsArray = [BrewingStepItem]()

        for (i, step) in steps.enumerated() {
            if i == index {
                // 创建一个完全新的步骤对象
                let newItem = BrewingStepItem(text: finalText, timer: timerString, order: step.order)
                newStepsArray.append(newItem)
            } else {
                // 保留其他步骤
                newStepsArray.append(step)
            }
        }

        // 替换整个数组
        steps = newStepsArray

        // 强制刷新步骤列表
        stepsRefreshID = UUID()

        // 清除编辑状态和焦点
        editingStepIndex = nil
        focusedField = nil

        // 停止计时器
        if stepTimerActive {
            stopStepTimer()
        }

        // 延迟一下刷新UI，确保数据已经更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.stepsRefreshID = UUID()
        }
    }

    // 取消步骤编辑
    private func cancelStepEdit() {
        // 标记是否是删除了最后一个步骤
        var wasLastStep = false

        // 如果是新添加的空步骤，则删除
        if let index = editingStepIndex, index >= 0 && index < steps.count {
            if steps[index].text.isEmpty {
                wasLastStep = steps.count == 1

                withAnimation {
                    steps.remove(at: index)
                    // 更新步骤顺序
                    for (idx, _) in steps.enumerated() {
                        steps[idx].order = idx + 1
                    }
                }
            }
        }

        // 清除编辑状态
        editingStepIndex = nil
        newStepText = ""
        newStepMinutes = ""
        newStepSeconds = ""

        // 停止计时器
        if stepTimerActive {
            stopStepTimer()
        }

        // 清除焦点
        focusedField = nil

        // 如果删除后没有步骤了，自动关闭步骤功能（不再自动添加新步骤）
        if steps.isEmpty {
            stepsEnabled = false
        }
        // 如果不是删除最后一个步骤，且删除后还有步骤功能开启但没有步骤，那么添加一个空步骤
        else if !wasLastStep && steps.isEmpty && stepsEnabled {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                addNewEmptyStep()
            }
        }
    }

    // 删除步骤的逻辑封装
    private func deleteStep(at index: Int) {
        // 确保索引在有效范围内
        guard index >= 0 && index < steps.count else { return }

        // 如果正在编辑这个步骤，先取消编辑
        if editingStepIndex == index {
            editingStepIndex = nil
            newStepText = ""
            newStepMinutes = ""
            newStepSeconds = ""
            focusedField = nil
        } else if let editingIdx = editingStepIndex, editingIdx > index {
            // 如果正在编辑的是后面的步骤，需要更新编辑索引
            editingStepIndex = editingIdx - 1
        }

        // 使用withAnimation来移除步骤
        withAnimation {
            steps.remove(at: index)

            // 更新步骤顺序
            for (idx, _) in steps.enumerated() {
                steps[idx].order = idx + 1
            }

            // 如果删除后没有步骤了，自动关闭步骤功能
            if steps.isEmpty {
                stepsEnabled = false
            }
        }
    }

    // MARK: - 计时器方法

    // 步骤计时器相关方法
    private func startStepTimer(forIndex index: Int) {
        stepTimerSeconds = 0
        stepTimerActive = true
        stepTimerActiveIndex = index
        activeTimer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            stepTimerSeconds += 1
        }
    }

    private func stopStepTimer() {
        activeTimer?.invalidate()
        activeTimer = nil
        stepTimerActive = false
    }

    private func updateStepTimeFromTimer() {
        let minutes = Int(stepTimerSeconds) / 60
        let seconds = Int(stepTimerSeconds) % 60

        newStepMinutes = String(minutes)
        newStepSeconds = String(format: "%02d", seconds)
    }

    private func formatStepTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    // 主计时器方法
    private func startTimer() {
        timerSeconds = 0
        isTimerRunning = true
        activeTimer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            timerSeconds += 1
        }
    }

    private func stopTimer() {
        activeTimer?.invalidate()
        activeTimer = nil
        isTimerRunning = false
    }

    // 更新时间输入框
    private func updateTimeFromTimer() {
        let hours = Int(timerSeconds) / 3600
        let minutes = (Int(timerSeconds) % 3600) / 60
        let seconds = Int(timerSeconds) % 60

        brewingHours = String(format: "%02d", hours)
        brewingMinutes = String(format: "%02d", minutes)
        brewingSeconds = String(format: "%02d", seconds)
    }
}

#if DEBUG
struct EditBrewLogView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            EditBrewLogView(record: BrewingRecord.preview)
        }
    }
}
#endif
