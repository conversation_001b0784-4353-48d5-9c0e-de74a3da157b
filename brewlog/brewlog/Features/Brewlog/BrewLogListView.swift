import SwiftUI
import UIKit
import Combine

// 删除多余的扩展，BrewingRecord已经在Models中实现了Identifiable
// extension BrewingRecord: Identifiable {}

// 添加SwipeItem组件
struct SwipeItem<Content: View, Left: View, Right: View>: View {
    var content: () -> Content
    var left: () -> Left
    var right: (Bool) -> Right  // 修改为接受deleteAction状态的闭包

    @State var hoffset: CGFloat = 0
    @State var anchor: CGFloat = 0
    @State var initialOffset: CGFloat = 0 // 用于记录拖动开始时的初始偏移
    @GestureState private var isDragging = false
    @GestureState private var isLongPressing = false // 新增：长按状态追踪
    @State private var isTapped = false // 新增：点击状态追踪

    let screenWidth = UIScreen.main.bounds.width
    var anchorWidth: CGFloat { screenWidth / 4 } // 减小删除按钮宽度为屏幕的1/4
    var swipeThreshold: CGFloat { screenWidth / 15 }
    var dragThreshold: CGFloat { screenWidth / 2 } // 触发删除动作的拖动阈值
    var buttonSpacing: CGFloat = 8.0 // 添加按钮与卡片之间的间距

    @State var rightPast = false
    @State var leftPast = false
    @State var deleteAction = false // 新增：标记是否触发删除动作
    @State var editAction = false // 新增：标记是否触发修改动作

    // 通过添加重置状态绑定，允许父视图控制重置
    @Binding var resetTrigger: Bool

    // 删除动作回调
    var onDelete: (() -> Void)?
    // 修改动作回调
    var onEdit: (() -> Void)?

    // 添加左右轻扫动作类型
    var leftActionType: SwipeAction = .delete
    var rightActionType: SwipeAction = .edit

    // 添加对比状态标志
    var isLeftCompareSelected: Bool = false
    var isRightCompareSelected: Bool = false

    init(@ViewBuilder content: @escaping () -> Content,
         @ViewBuilder left: @escaping () -> Left,
         @ViewBuilder right: @escaping (Bool) -> Right,  // 更新为接受deleteAction状态的闭包
         onDelete: (() -> Void)? = nil,
         onEdit: (() -> Void)? = nil,
         resetTrigger: Binding<Bool> = .constant(false),
         leftActionType: SwipeAction = .delete,
         rightActionType: SwipeAction = .edit,
         isLeftCompareSelected: Bool = false,
         isRightCompareSelected: Bool = false) {
        self.content = content
        self.left = left
        self.right = right
        self.onDelete = onDelete
        self.onEdit = onEdit
        self._resetTrigger = resetTrigger
        self.leftActionType = leftActionType
        self.rightActionType = rightActionType
        self.isLeftCompareSelected = isLeftCompareSelected
        self.isRightCompareSelected = isRightCompareSelected
    }

    // 重置轻扫状态的内部方法
    private func resetSwipeState() {
        withAnimation(.spring()) {
            anchor = 0
            hoffset = 0
            rightPast = false
            leftPast = false
            deleteAction = false
            editAction = false
            isTapped = false
        }
    }

    // 计算是否应该显示删除按钮
    private var shouldShowRightButtons: Bool {
        // 只有在没有长按和点击状态下，并且向左轻扫不为"无"时才显示按钮
        !isLongPressing && !isTapped && hoffset < 0 && leftActionType != .none
    }

    // 计算是否应该显示修改按钮
    private var shouldShowLeftButtons: Bool {
        // 只有在没有长按和点击状态下，并且向右轻扫不为"无"时才显示按钮
        !isLongPressing && !isTapped && hoffset > 0 && rightActionType != .none
    }

    var body: some View {
        // 完全重构SwipeItem主体，确保所有卡片的按钮高度一致
        GeometryReader { geo in
            ZStack {
                // 先创建内容视图的背景，用于确定正确的大小
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.clear)
                    .frame(width: geo.size.width, height: geo.size.height)
                    .zIndex(0)

                // 左侧修改按钮（向右轻扫）
                if shouldShowLeftButtons {
                    HStack(spacing: 0) {
                        // 修改按钮内容
                        left()
                            .frame(width: min(anchorWidth, geo.size.width / 4))
                            .frame(maxHeight: .infinity)

                        Spacer()
                    }
                    .frame(width: calculateButtonWidth(geo: geo, isEdit: true), height: geo.size.height)
                    .background(getRightBackgroundColor()) // 使用颜色计算方法
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .zIndex(1)
                    .position(x: calculateButtonWidth(geo: geo, isEdit: true)/2, y: geo.size.height/2)
                }

                // 右侧删除按钮（向左轻扫）
                if shouldShowRightButtons {
                    HStack(spacing: 0) {
                        Spacer()

                        // 删除按钮内容
                        right(deleteAction)
                            .frame(width: min(anchorWidth, geo.size.width / 4))
                            .frame(maxHeight: .infinity)
                    }
                    .frame(width: calculateButtonWidth(geo: geo, isEdit: false), height: geo.size.height)
                    .background(getLeftBackgroundColor()) // 使用颜色计算方法
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .zIndex(1)
                    .position(x: geo.size.width - calculateButtonWidth(geo: geo, isEdit: false)/2, y: geo.size.height/2)
                }

                // 内容视图 - 可轻扫层
                content()
                    .background(Color.clear)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .offset(x: hoffset)
                    .zIndex(2)
                    .simultaneousGesture(
                        // 水平拖动手势，仅处理水平方向
                        DragGesture(minimumDistance: 8, coordinateSpace: .local)
                            .updating($isDragging) { _, state, _ in
                                state = true
                            }
                            .onChanged { value in
                                // 更严格的水平轻扫判断
                                let horizontalAmount = abs(value.translation.width)
                                let verticalAmount = abs(value.translation.height)

                                // 以下任一条件满足，都视为非水平轻扫：
                                // 1. 垂直分量大于水平分量的0.8倍(原来是1.2倍)
                                // 2. 垂直分量超过25像素
                                // 3. 速度向量的垂直分量明显大于水平分量
                                let isVerticalDominant = verticalAmount > horizontalAmount * 0.8 ||
                                                        verticalAmount > 25 ||
                                                        abs(value.velocity.height) > abs(value.velocity.width) * 1.2

                                // 如果是垂直主导的移动，直接忽略
                                if isVerticalDominant {
                                    // 如果已经开始了轻扫，则平滑恢复
                                    if hoffset != anchor {
                                        withAnimation(.spring()) {
                                            hoffset = anchor
                                        }
                                    }
                                    return
                                }

                                // 检查轻扫操作是否被禁用
                                let isRightSwipe = value.translation.width > 0
                                let isLeftSwipe = value.translation.width < 0

                                // 如果右滑被禁用（rightActionType为none）且正在尝试右滑
                                if isRightSwipe && rightActionType == .none {
                                    // 不允许右滑，立即恢复到原位
                                    if hoffset != anchor {
                                        withAnimation(.spring()) {
                                            hoffset = anchor
                                        }
                                    }
                                    return
                                }

                                // 如果左滑被禁用（leftActionType为none）且正在尝试左滑
                                if isLeftSwipe && leftActionType == .none {
                                    // 不允许左滑，立即恢复到原位
                                    if hoffset != anchor {
                                        withAnimation(.spring()) {
                                            hoffset = anchor
                                        }
                                    }
                                    return
                                }

                                // 更新水平偏移
                                withAnimation(.interactiveSpring(response: 0.35, dampingFraction: 0.7, blendDuration: 0.2)) {
                                    hoffset = anchor + value.translation.width

                                    // 处理向右轻扫（修改）
                                    if hoffset > 0 {
                                        // 判断是否轻扫超过阈值
                                        leftPast = hoffset > (swipeThreshold + buttonSpacing/2)

                                        // 修改动作判断：应用非线性插值
                                        let normalizedOffset = hoffset / screenWidth // 将偏移标准化
                                        let dragFactorThreshold = dragThreshold / screenWidth // 标准化阈值

                                        // 使用平滑非线性过渡而不是突变
                                        if normalizedOffset > dragFactorThreshold * 0.85 {
                                            // 接近阈值时开始淡入修改状态
                                            let factor = (normalizedOffset - dragFactorThreshold * 0.85) / (dragFactorThreshold * 0.15)
                                            editAction = factor > 0.5 // 过渡带有平滑性
                                        } else {
                                            editAction = false
                                        }

                                        // 限制最大轻扫距离（仅当没有触发修改操作时）
                                        if !editAction && hoffset > (anchorWidth + buttonSpacing) {
                                            // 应用阻尼效果而不是硬限制，考虑间距
                                            let excess = hoffset - (anchorWidth + buttonSpacing)
                                            hoffset = (anchorWidth + buttonSpacing) + (excess * 0.2) // 20%的阻尼系数
                                        }
                                    }
                                    // 处理向左轻扫（删除）
                                    else {
                                        // 删除动作判断：应用非线性插值，使过渡更平滑
                                        let normalizedOffset = -hoffset / screenWidth // 将偏移标准化
                                        let dragFactorThreshold = dragThreshold / screenWidth // 标准化阈值

                                        // 使用平滑非线性过渡而不是突变
                                        if normalizedOffset > dragFactorThreshold * 0.85 {
                                            // 接近阈值时开始淡入删除状态
                                            let factor = (normalizedOffset - dragFactorThreshold * 0.85) / (dragFactorThreshold * 0.15)
                                            deleteAction = factor > 0.5 // 过渡带有平滑性
                                        } else {
                                            deleteAction = false
                                        }

                                        // 判断是否轻扫超过阈值
                                        if anchor < 0 {
                                            // 已经打开的状态
                                            rightPast = hoffset < -(anchorWidth + buttonSpacing) + swipeThreshold
                                        } else {
                                            // 初始关闭状态
                                            rightPast = hoffset < -(swipeThreshold + buttonSpacing/2)
                                        }

                                        // 限制最大轻扫距离（仅当没有触发删除操作时）
                                        if !deleteAction && hoffset < -(anchorWidth + buttonSpacing) {
                                            // 应用阻尼效果而不是硬限制，考虑间距
                                            let excess = -hoffset - (anchorWidth + buttonSpacing)
                                            hoffset = -(anchorWidth + buttonSpacing) - (excess * 0.2) // 20%的阻尼系数
                                        }
                                    }
                                }
                            }
                            .onEnded { value in
                                // 只有最终移动方向是水平的才处理
                                let horizontalAmount = abs(value.translation.width)
                                let verticalAmount = abs(value.translation.height)

                                // 如果是明显的垂直移动，取消所有操作
                                if verticalAmount > horizontalAmount * 1.2 {
                                    withAnimation(.spring()) {
                                        hoffset = anchor
                                    }
                                    return
                                }

                                // 检查轻扫操作是否被禁用（确保onEnded也不会触发禁用的操作）
                                let isRightSwipe = value.translation.width > 0
                                let isLeftSwipe = value.translation.width < 0

                                if (isRightSwipe && rightActionType == .none) ||
                                   (isLeftSwipe && leftActionType == .none) {
                                    // 如果尝试的轻扫方向被禁用，直接重置
                                    withAnimation(.spring()) {
                                        anchor = 0
                                        hoffset = 0
                                    }
                                    return
                                }

                                withAnimation(.spring()) {
                                    // 如果触发了删除动作
                                    if deleteAction {
                                        // 调用删除回调
                                        DispatchQueue.main.async {
                                            onDelete?()
                                            // 重置状态
                                            resetSwipeState()
                                        }
                                    }
                                    // 如果触发了修改动作
                                    else if editAction {
                                        // 调用修改回调
                                        DispatchQueue.main.async {
                                            onEdit?()
                                            // 重置状态
                                            resetSwipeState()
                                        }
                                    }
                                    // 处理普通的右滑打开修改菜单
                                    else if leftPast && value.translation.width > 0 && rightActionType != .none {
                                        // 添加间距到固定位置
                                        anchor = anchorWidth + buttonSpacing
                                    }
                                    // 处理普通的左滑打开删除菜单
                                    else if rightPast && value.translation.width < 0 && leftActionType != .none {
                                        // 添加间距到固定位置
                                        anchor = -(anchorWidth + buttonSpacing)
                                    }
                                    // 其他情况重置位置
                                    else {
                                        anchor = 0
                                    }
                                    hoffset = anchor
                                }
                            }
                    )
                    // 添加长按手势状态跟踪
                    .simultaneousGesture(
                        LongPressGesture(minimumDuration: 0.01)
                            .updating($isLongPressing) { _, state, _ in
                                state = true
                            }
                    )
                    // 修改这里，使用onTapGesture而不是highPriorityGesture，减少手势冲突
                    .onTapGesture {
                        // 点击时设置isTapped为true，阻止显示删除按钮
                        isTapped = true

                        if anchor != 0 {
                            // 如果菜单已打开，关闭它
                            withAnimation(.spring()) {
                                anchor = 0
                                hoffset = 0
                            }
                        }

                        // 延迟恢复isTapped状态，给足够时间完成其他动作
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            withAnimation {
                                isTapped = false
                            }
                        }
                    }
            }
            .frame(width: geo.size.width, height: geo.size.height)
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .contentShape(Rectangle())
            .onChange(of: resetTrigger) { newValue in
                if newValue {
                    resetSwipeState()
                    DispatchQueue.main.async {
                        resetTrigger = false
                    }
                }
            }
        }
    }

    // 计算按钮宽度的辅助方法
    private func calculateButtonWidth(geo: GeometryProxy, isEdit: Bool) -> CGFloat {
        if isEdit {
            // 计算修改按钮宽度
            if editAction {
                // 触发修改动作时，拉伸到整个宽度
                return geo.size.width
            } else if hoffset > (anchorWidth + buttonSpacing) {
                // 当轻扫超过按钮宽度时，跟随轻扫距离增加宽度
                return hoffset - buttonSpacing
            } else {
                // 正常状态，使用设定的按钮宽度
                return anchorWidth
            }
        } else {
            // 计算删除按钮宽度
            if deleteAction {
                // 触发删除动作时，拉伸到整个宽度
                return geo.size.width
            } else if hoffset < -(anchorWidth + buttonSpacing) {
                // 当轻扫超过按钮宽度时，跟随轻扫距离增加宽度
                // 注意这里减去了间距，因为按钮的实际宽度需要考虑间距
                return -hoffset - buttonSpacing
            } else {
                // 正常状态，使用设定的按钮宽度
                return anchorWidth
            }
        }
    }

    // 新增颜色计算方法
    private func getLeftBackgroundColor() -> Color {
        // 如果是对比操作且已选择，使用橙色表示"取消"状态
        if leftActionType == .compare && isLeftCompareSelected {
            return Color.orange
        }
        // 否则使用操作对应的默认颜色
        return leftActionType.color
    }

    private func getRightBackgroundColor() -> Color {
        // 如果是对比操作且已选择，使用橙色表示"取消"状态
        if rightActionType == .compare && isRightCompareSelected {
            return Color.orange
        }
        // 否则使用操作对应的默认颜色
        return rightActionType.color
    }
}

struct BrewLogListView: View {
    @StateObject private var viewModel: BrewLogViewModel = BrewLogViewModel()
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var showDateHeader = false
    @State private var currentDateHeader = ""
    @State private var scrollOffset: CGFloat = 0
    @State private var navigationPath = NavigationPath()  // 用于控制导航的路径
    @State private var resetSwipeStates: Bool = false // 用于重置所有轻扫状态

    // 修改相关状态变量
    @State private var recordToEdit: BrewingRecord? = nil

    // 删除相关的状态变量
    @State private var recordToDelete: BrewingRecord? = nil
    @State private var showDeleteAlert = false
    @State private var isDeleting = false
    @State private var deleteError: String? = nil
    @State private var showDeleteErrorAlert = false

    // 访问手势设置
    let gestureSettings = GestureSettings.shared

    // 添加对AppState的引用
    @EnvironmentObject private var appState: AppState

    // 记录日期的格式化器，用于显示分组标题
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月d日 EEEE"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()

    // 添加初始化方法，允许外部传入viewModel
    init(viewModel: BrewLogViewModel? = nil) {
        if let vm = viewModel {
            // 使用_viewModel来初始化@StateObject
            _viewModel = StateObject(wrappedValue: vm)
        }
    }

    var body: some View {
        NavigationStack(path: $navigationPath) {
            mainView
                .navigationTitle("冲煮记录")
                .navigationBarTitleDisplayMode(.large)
        }
    }

    // 主视图组件
    private var mainView: some View {
        ZStack(alignment: .top) {
            Color.secondaryBg
                .edgesIgnoringSafeArea(.all)

            contentWithNavigation

            // 浮动年月份指示条
            if showDateHeader && !viewModel.brewingRecords.isEmpty {
                dateHeaderView
            }
        }
        // 移除这个重复的sheet修饰符，使用contentWithNavigation中的修饰符
        // .sheet(item: $recordToEdit) { record in
        //     NavigationView {
        //         BrewLogFormView(record: record, isEditing: true)
        //     }
        //     .onDisappear {
        //         // 刷新数据
        //         Task {
        //             await viewModel.fetchFilteredRecords(forceRefresh: true)
        //         }
        //     }
        // }
        .sheet(isPresented: $viewModel.showingCompareView) {
            if viewModel.recordsForCompare.count == 2 {
                NavigationView {
                    CompareView(
                        record1: viewModel.recordsForCompare[0],
                        record2: viewModel.recordsForCompare[1]
                    )
                }
            }
        }
    }

    // 内容和导航组件
    private var contentWithNavigation: some View {
        mainContentView
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    toolbarButtons
                }
            }
            .sheet(isPresented: $viewModel.showingAddSheet, onDismiss: {
                // 添加表单关闭时刷新数据
                Task {
                    await viewModel.fetchFilteredRecords(forceRefresh: true)
                }
                // 清除模板记录
                viewModel.templateRecord = nil
            }) {
                NavigationView {
                    AddBrewLogView(templateRecord: viewModel.templateRecord)
                }
            }
            .sheet(item: $recordToEdit, onDismiss: {
                // 编辑表单关闭时刷新数据
                // 避免直接清空记录，使用短延迟确保平滑过渡
                Task {
                    // 短延迟避免直接清空列表造成闪烁
                    try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
                    await viewModel.fetchFilteredRecords(forceRefresh: true)
                }
            }) { record in
                NavigationView {
                    EditBrewLogView(record: record)
                }
            }
            .sheet(isPresented: $viewModel.showingFilterSheet) {
                BrewLogListFilterSheetView(viewModel: viewModel)
            }
            .navigationDestination(for: BrewingRecord.self) { record in
                BrewLogDetailView(record: record)
            }
            .navigationDestination(for: Int.self) { recordId in
                if let record = viewModel.brewingRecords.first(where: { $0.id == recordId }) {
                    BrewLogDetailView(record: record)
                } else {
                    // 如果在当前列表中找不到记录，尝试单独加载
                    BrewLogDetailLoadingView(recordId: recordId)
                }
            }
            .task {
                // 使用过滤API获取数据
                await viewModel.fetchFilteredRecords(forceRefresh: false)
            }
            .onChange(of: appState.selectedBrewLogId) { brewLogId in
                // 监听导航请求
                if let brewLogId = brewLogId {
                    print("📱 onChange监听到导航请求，冲煮记录ID: \(brewLogId)")
                    // 添加小延迟确保视图已经完全加载
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                        handleURLNavigation(brewLogId: brewLogId)
                    }
                } else {
                    print("📱 onChange监听到selectedBrewLogId被清除")
                }
            }
            .onChange(of: appState.pendingBrewLogNavigation) { pendingId in
                // 监听待处理的导航请求
                if let pendingId = pendingId {
                    print("📱 收到待处理的导航请求，记录ID: \(pendingId)")
                    checkAndProcessPendingNavigation()
                }
            }
            .onChange(of: viewModel.brewingRecords) { records in
                // 监听数据变化，如果有待处理的导航请求，立即处理
                if !records.isEmpty && appState.pendingBrewLogNavigation != nil {
                    print("📊 数据已加载，检查待处理的导航请求")
                    checkAndProcessPendingNavigation()
                }
            }
            .onChange(of: appState.shouldShowAddBrewLog) { shouldShow in
                // 监听冲煮提醒通知触发的添加记录请求
                if shouldShow {
                    // 检查是否需要复制上次记录
                    if appState.shouldCopyLastRecord {
                        // 获取最近一条记录作为模板
                        if let lastRecord = viewModel.brewingRecords.first {
                            viewModel.templateRecord = lastRecord
                        }
                        // 重置复制标志
                        appState.shouldCopyLastRecord = false
                    }

                    viewModel.showingAddSheet = true
                    // 重置标志
                    appState.shouldShowAddBrewLog = false
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("BrewLogListNeedsRefresh"))) { _ in
                print("⚡️ 收到BrewLogListNeedsRefresh通知，刷新冲煮记录列表")

                // 确保在主线程执行UI更新
                Task { @MainActor in
                    // 先清除缓存
                    URLCache.shared.removeAllCachedResponses()

                    // 短暂延迟确保缓存已清除
                    try? await Task.sleep(nanoseconds: 100_000_000) // 100毫秒

                    // 强制刷新数据
                    await viewModel.fetchFilteredRecords(forceRefresh: true)

                    // 再次延迟确保数据已更新
                    try? await Task.sleep(nanoseconds: 100_000_000) // 100毫秒

                    // 触发视图完全重新加载
                    viewModel.objectWillChange.send()

                    print("♻️ 冲煮记录列表数据刷新完成并触发视图重载")
                }
            }
            .confirmationDialog(
                "确认删除",
                isPresented: $showDeleteAlert,
                titleVisibility: .visible
            ) {
                Button("取消", role: .cancel) {
                    recordToDelete = nil
                }

                Button("删除", role: .destructive) {
                    if let record = recordToDelete {
                        deleteRecord(record)
                    }
                }
                .disabled(isDeleting) // 删除过程中禁用按钮
            } message: {
                if recordToDelete != nil {
                    if isDeleting {
                        Text("正在删除...")
                    } else {
                        Text("确定要删除这条冲煮记录吗？此操作不可撤销。删除后，所有与这条冲煮记录相关的统计也会被删除。")
                    }
                }
            }
            .alert(
                "删除失败",
                isPresented: $showDeleteErrorAlert,
                actions: {
                    Button("确定", role: .cancel) {}
                },
                message: {
                    Text(deleteError ?? "未知错误")
                }
            )
    }

    // 日期头部视图
    private var dateHeaderView: some View {
        VStack {
            HStack {
                Spacer()

                Text(currentDateHeader)
                    .font(.footnote)
                    .fontWeight(.semibold)
                    .foregroundColor(Color.primaryText)
                    .padding(.vertical, 4)
                    .padding(.horizontal, 12)
                    .background(Color.primaryBg.opacity(0.95))
                    .cornerRadius(16)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.primaryText.opacity(0.1), lineWidth: 1)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)

                Spacer()
            }
            .padding(.top, 4)  // 减小顶部边距，使其更接近导航栏

            Spacer()
        }
        .transition(.opacity)
        .animation(.easeInOut(duration: 0.2), value: showDateHeader)
        .zIndex(1)
    }

    // 工具栏按钮
    private var toolbarButtons: some View {
        HStack(spacing: 12) {
            // 筛选按钮
            Button {
                viewModel.showingFilterSheet = true
            } label: {
                ZStack {
                    Image(systemName: "line.3.horizontal.decrease")
                        .foregroundColor(Color.functionText)
                        .font(.system(size: 15))
                        .frame(width: 30, height: 30)
                        .background(Circle().fill(Color.navbarBg))

                    // 筛选指示器 - 当isFiltering为true时显示绿色小点
                    if viewModel.isFiltering && (
                        !viewModel.filterSearchQuery.isEmpty ||
                        !viewModel.filterBrewMethod.isEmpty ||
                        !viewModel.filterCoffeeBean.isEmpty ||
                        !viewModel.filterRatingRange.isEmpty ||
                        !viewModel.isDefaultDateRange // 添加日期范围条件
                    ) {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                            .offset(x: 10, y: -10)
                    }
                }
            }

            // 对比按钮 - 修改显示内容，显示已选择的对比数量
            Menu {
                if viewModel.recordsForCompare.isEmpty {
                    Button(action: {}) {
                        Text("未选择需对比的冲煮记录")
                        Text("💡长按卡片，选择添加到对比，即可标记为被对比数据。")
                    }
                } else {
                    // 显示已选择的记录
                    ForEach(viewModel.recordsForCompare) { record in
                        Button(action: {
                            _ = viewModel.removeFromCompare(record)
                        }) {
                            Label {
                                Text("\(record.coffeeBean.name) (\(formatCompareDate(record.createdAt)))")
                            } icon: {
                                Image(systemName: "minus.circle")
                            }
                        }
                    }

                    Divider()

                    // 清除所有
                    if !viewModel.recordsForCompare.isEmpty && !viewModel.canCompare {
                        Text("💡还差一个才能开始对比").foregroundColor(.noteText)
                    }

                    // 开始对比
                    if viewModel.canCompare {
                        Button(role: .destructive, action: {
                            viewModel.clearCompare()
                        }) {
                            Label {
                                Text("清除全部")
                            } icon: {
                                Image(systemName: "minus.circle")
                            }
                        }
                        Button(action: {
                            viewModel.startCompare()
                        }) {
                            Label("开始对比", systemImage: "arrow.left.arrow.right")
                        }
                    }
                }
            } label: {
                ZStack {
                    // 圆形背景
                    Circle()
                        .fill(Color.navbarBg)
                        .frame(width: 30, height: 30)

                    // 图标居中
                    Image(systemName: "arrow.left.arrow.right")
                        .font(.system(size: 12))
                        .foregroundColor(Color.functionText)

                    // 数量指示器（位于右上角）
                    if !viewModel.recordsForCompare.isEmpty {
                        Text("\(viewModel.recordsForCompare.count)")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.primaryBg)
                            .frame(width: 16, height: 16)
                            .background(Circle().fill(Color.primaryAccent))
                            .overlay(
                                Circle()
                                    .stroke(Color.navbarBg, lineWidth: 2)
                            )
                            .offset(x: 11, y: -11)
                    }
                }
            }

            // 添加按钮
            Button {
                viewModel.showingAddSheet = true
            } label: {
                Image(systemName: "plus")
                    .foregroundColor(Color.functionText)
                    .font(.system(size: 16))
                    .frame(width: 30, height: 30)
                    .background(Circle().fill(Color.navbarBg))
            }
        }
    }

    // 主要内容视图
    private var mainContentView: some View {
        Group {
            if viewModel.brewingRecords.isEmpty {
                if viewModel.isLoadingRecords {
                    // 添加VStack使loading指示器居中显示
                    VStack {
                        Spacer()

                        // 使用BlinkingLoader替换ProgressView
                        VStack(spacing: 12) {
                            BlinkingLoader(
                                color: .secondaryText,
                                width: 12,
                                height: 18,
                                duration: 1.5,
                                text: "正在加载记录..."
                            )
                        }

                        Spacer()
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.secondaryBg)
                } else if let error = viewModel.error {
                    // 错误视图也使用居中显示
                    VStack {
                        Spacer()

                        ErrorView(error: error) {
                            Task {
                                await viewModel.fetchFilteredRecords()
                            }
                        }

                        Spacer()
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.secondaryBg)
                } else {
                    emptyStateView
                }
            } else {
                // 有数据时总是显示列表，不管加载状态如何
                brewLogListView
            }
        }
    }

    // 空状态视图
    private var emptyStateView: some View {
        // 添加外层VStack并使用Spacer实现垂直居中
        VStack {
            Spacer()

            // 保持原有的内容布局
            VStack(spacing: 20) {
                Image(systemName: "cup.and.saucer")
                    .font(.system(size: 50))
                    .foregroundColor(Color.secondaryText)
                Text("暂无符合条件的记录")
                    .font(.title2)
                    .foregroundColor(Color.primaryText)
                Text("点击右上角 + 添加新的冲煮记录吧")
                    .font(.subheadline)
                    .foregroundColor(Color.detailText)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .padding()

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.secondaryBg)
    }

    // 冲煮记录列表视图
    private var brewLogListView: some View {
        ZStack(alignment: .top) {
            // 添加一个全屏的背景色视图
            Color.secondaryBg
                .edgesIgnoringSafeArea(.all)

            ScrollViewReader { scrollProxy in
                brewingRecordsList
            }
        }
    }

    // 冲煮记录列表
    private var brewingRecordsList: some View {
        List {
            // 统计信息部分
            VStack(spacing: 12) {
                // 统计卡片
                HStack(spacing: 0) {
                    // 连续打卡
                    VStack(spacing: 4) {
                        if viewModel.streakDays > 0 {
                            Text("连续打卡")
                                .font(.footnote)
                                .foregroundColor(Color.detailText)

                            Text("\(viewModel.streakDays)")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(Color.primaryText)

                            Text("天")
                                .font(.caption)
                                .foregroundColor(Color.archivedText)
                        } else {
                            Text("上次打卡")
                                .font(.footnote)
                                .foregroundColor(Color.detailText)

                            Text("\(viewModel.lastStreakDays)")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(Color.primaryText)

                            Text("天前")
                                .font(.caption)
                                .foregroundColor(Color.archivedText)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.primaryBg)
                    )

                    Spacer(minLength: 8)

                    // 本月冲煮
                    VStack(spacing: 4) {
                        Text("本月冲煮")
                            .font(.footnote)
                            .foregroundColor(Color.detailText)

                        Text("\(viewModel.monthCount)")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(Color.primaryText)

                        Text("次")
                            .font(.caption)
                            .foregroundColor(Color.archivedText)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.primaryBg)
                    )

                    Spacer(minLength: 8)

                    // 今年冲煮
                    VStack(spacing: 4) {
                        Text("今年冲煮")
                            .font(.footnote)
                            .foregroundColor(Color.detailText)

                        Text("\(viewModel.yearCount)")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(Color.primaryText)

                        Text("次")
                            .font(.caption)
                            .foregroundColor(Color.archivedText)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.primaryBg)
                    )
                }
                .padding(.horizontal, 12)
                .padding(.bottom, 4)
            }
            .listRowBackground(Color.secondaryBg)
            .listRowSeparator(.hidden)

            Text("共找到 \(viewModel.totalCount) 条符合条件的记录:")
                .font(.footnote)
                .foregroundColor(Color.noteText)
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
                .id("headerRow")
                .background(
                    // 使用隐藏的GeometryReader检测顶部视图的位置
                    GeometryReader { geo -> Color in
                        DispatchQueue.main.async {
                            // 顶部标题区域显示时隐藏年月指示
                            let frame = geo.frame(in: .global)
                            showDateHeader = frame.minY < 120 // 当顶部记录位置低于导航栏时显示

                            // 当滚动发生时，自动重置所有轻扫选项状态
                            if abs(scrollOffset - frame.minY) > 20 {
                                scrollOffset = frame.minY
                                resetSwipeStates = true
                            }
                        }
                        return Color.clear
                    }
                )

            ForEach(viewModel.brewingRecords) { record in
                brewLogRowView(for: record)
                    .id(record.id)
                    .onAppear {
                        // 计算当前项是否接近列表末尾（最后5项），提前开始加载下一页
                        let thresholdIndex = viewModel.brewingRecords.count - 5
                        let currentIndex = viewModel.brewingRecords.firstIndex(where: { $0.id == record.id }) ?? 0

                        if currentIndex >= thresholdIndex && !viewModel.isLoadingRecords {
                            Task {
                                await viewModel.loadMoreRecords()
                            }
                        }

                        // 当记录出现在屏幕上时，更新当前显示的日期标题
                        if currentIndex < 3 { // 只对前几条记录进行更新
                            updateDateHeader(for: record)
                        }
                    }
            }

            // 底部加载状态
            listFooterView
        }
        .id("brewLogList")
        .background(Color.secondaryBg)
        .scrollContentBackground(.hidden) // 关键: 隐藏默认的滚动内容背景
        .listStyle(PlainListStyle())
        .simultaneousGesture(
            // 监听列表的点击手势，点击空白区域时重置所有轻扫状态
            TapGesture().onEnded { _ in
                resetSwipeStates = true
            }
        )
    }

    // 列表底部视图
    private var listFooterView: some View {
        Group {
            if !viewModel.brewingRecords.isEmpty {
                HStack {
                    Spacer()
                    if viewModel.isLoadingRecords && viewModel.currentPage > 1 {
                        // 使用BlinkingLoader替换ProgressView
                        BlinkingLoader(
                            color: .secondaryText,
                            width: 12,
                            height: 15,
                            duration: 1.5,
                            text: "加载更多..."
                        )
                        .padding(.vertical, 12)
                    } else if !viewModel.hasMoreRecords && viewModel.brewingRecords.count > 0 {
                        Text("— 到底了 —")
                            .font(.footnote)
                            .foregroundColor(Color.noteText.opacity(0.6))
                            .padding()
                    }
                    Spacer()
                }
                .listRowBackground(Color.secondaryBg)
                .listRowSeparator(.hidden)
            }
        }
    }

    // 单行记录视图 - 修改逻辑
    private func brewLogRowView(for record: BrewingRecord) -> some View {
        // 使用SwipeItem来实现自定义左右轻扫菜单
        ZStack {
            // 获取左右轻扫设置
            let leftAction = gestureSettings.getAction(for: .brewLog, direction: .left)
            let rightAction = gestureSettings.getAction(for: .brewLog, direction: .right)

            // 根据记录是否已选择对比，动态调整显示的操作文本和颜色
            let isInCompare = viewModel.isSelectedForCompare(record)

            // 使用SwipeItem替换原生swipeActions
            SwipeItem(content: {
                ZStack {
                    CardWithPressEffect { isPressed in
                        recordRowContent(record, isPressed: isPressed)
                    } onTap: {
                        // 导航到详情页
                        showRecordDetail(record)
                    }
                    .contentShape(Rectangle())


                }
            }, left: {
                // 如果向右轻扫为"无"，返回空视图
                if rightAction == .none {
                    EmptyView()
                } else {
                    // 左侧按钮（向右轻扫）
                    VStack(spacing: 5) {
                        // 为对比操作动态更改图标
                        if rightAction == .compare && isInCompare {
                            Image(systemName: "minus.circle")
                                .foregroundColor(.primaryBg)
                                .font(.system(size: 24))
                                .symbolRenderingMode(.monochrome)
                        } else if rightAction == .edit || rightAction == .copyRecipe {
                            // 使用自定义图标
                            Image(rightAction.icon)
                                .foregroundColor(.primaryBg)
                                .font(.system(size: 24))
                        } else {
                            Image(systemName: rightAction.icon)
                                .foregroundColor(.primaryBg)
                                .font(.system(size: 24))
                                .symbolRenderingMode(.monochrome)
                        }

                        // 判断是否是对比操作且已在对比列表中
                        if rightAction == .compare && isInCompare {
                            Text("取消对比")
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(.primaryBg)
                        } else {
                            Text(rightAction.rawValue)
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(.primaryBg)
                        }
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.clear) // 确保背景透明
                    .contentShape(Rectangle()) // 确保整个区域可点击
                    .onTapGesture {
                        // 触觉反馈
                        let generator = UINotificationFeedbackGenerator()
                        generator.notificationOccurred(.warning)

                        // 根据设置的操作执行相应的动作
                        if rightAction == .edit {
                            // 打开修改表单
                            navigateToEditForm(record)
                        } else if rightAction == .delete {
                            // 调用删除确认方法
                            showDeleteConfirmation(for: record)
                        } else if rightAction == .compare {
                            // 处理对比操作
                            handleCompareAction(record)
                        } else if rightAction == .copyRecipe {
                            // 复制配方
                            copyRecipeToNewRecord(record)
                        }

                        // 点击后自动关闭轻扫菜单
                        resetSwipeStates = true
                    }
                }
            }, right: { deleteAction in
                // 如果向左轻扫为"无"，返回空视图
                if leftAction == .none {
                    EmptyView()
                } else {
                    // 右侧按钮（向左轻扫）- 更美观统一的设计
                    VStack(spacing: 5) {
                        // 为对比操作动态更改图标
                        if leftAction == .compare && isInCompare {
                            Image(systemName: "minus.circle")
                                .foregroundColor(.primaryBg)
                                .font(.system(size: 24))
                                .symbolRenderingMode(.monochrome)
                        } else if leftAction == .edit || leftAction == .copyRecipe {
                            // 使用自定义图标
                            Image(leftAction.icon)
                                .foregroundColor(.primaryBg)
                                .font(.system(size: 24))
                        } else {
                            Image(systemName: leftAction.icon)
                                .foregroundColor(.primaryBg)
                                .font(.system(size: 24))
                                .symbolRenderingMode(.monochrome)
                        }

                        // 根据轻扫状态和操作类型显示不同文本
                        if deleteAction && leftAction == .delete {
                            Text("删除")
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(.primaryBg)
                        } else if deleteAction && leftAction == .edit {
                            Text("修改")
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(.primaryBg)
                        } else if deleteAction && leftAction == .compare {
                            // 根据是否已在对比列表中显示不同文本
                            Text(isInCompare ? "取消对比" : "添加对比")
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(.primaryBg)
                        } else if leftAction == .compare && isInCompare {
                            Text("取消对比")
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(.primaryBg)
                        } else {
                            Text(leftAction.rawValue)
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(.primaryBg)
                        }
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.clear) // 确保背景透明
                    .contentShape(Rectangle()) // 确保整个区域可点击
                    .onTapGesture {
                        // 触觉反馈
                        let generator = UINotificationFeedbackGenerator()
                        generator.notificationOccurred(.warning)

                        // 根据设置的操作执行相应的动作
                        if leftAction == .delete {
                            // 调用删除确认方法
                            showDeleteConfirmation(for: record)
                        } else if leftAction == .edit {
                            // 打开修改表单
                            navigateToEditForm(record)
                        } else if leftAction == .compare {
                            // 处理对比操作
                            handleCompareAction(record)
                        } else if leftAction == .copyRecipe {
                            // 复制配方
                            copyRecipeToNewRecord(record)
                        }

                        // 点击后自动关闭轻扫菜单
                        resetSwipeStates = true
                    }
                }
            }, onDelete: {
                // 向左轻扫
                if leftAction == .delete {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.warning)

                    // 调用删除确认方法
                    showDeleteConfirmation(for: record)
                } else if leftAction == .edit {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 调用修改表单
                    navigateToEditForm(record)
                } else if leftAction == .compare {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 处理对比操作
                    handleCompareAction(record)
                } else if leftAction == .copyRecipe {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 复制配方
                    copyRecipeToNewRecord(record)
                }
            }, onEdit: {
                // 向右轻扫
                if rightAction == .edit {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 调用修改表单
                    navigateToEditForm(record)
                } else if rightAction == .delete {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.warning)

                    // 调用删除确认方法
                    showDeleteConfirmation(for: record)
                } else if rightAction == .compare {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 处理对比操作
                    handleCompareAction(record)
                } else if rightAction == .copyRecipe {
                    // 触觉反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 复制配方
                    copyRecipeToNewRecord(record)
                }
            }, resetTrigger: $resetSwipeStates,
               leftActionType: leftAction,
               rightActionType: rightAction,
               isLeftCompareSelected: leftAction == .compare && isInCompare,
               isRightCompareSelected: rightAction == .compare && isInCompare)
            .frame(height: hasExtraInfo(record) ? 180 : 160) // 根据是否有额外信息动态设置高度
        }
        .padding(.horizontal, 2)
        .contextMenu(menuItems: {
            recordContextMenu(record)
        }, preview: {
            BrewLogPreviewView(record: record, themeColors: themeManager.currentThemeColors)
        })
        .listRowBackground(Color.secondaryBg)
        .listRowSeparator(.hidden)
        .listRowInsets(EdgeInsets(top: 6, leading: 12, bottom: 6, trailing: 12))
    }

    // 记录行内容 - 修改显示逻辑
    private func recordRowContent(_ record: BrewingRecord, isPressed: Bool) -> some View {
        BrewLogRow(record: record, isPressed: isPressed, themeColors: themeManager.currentThemeColors)
            // 去掉多余的背景层，使用BrewLogRow内置的背景样式
            // 保留阴影效果
            .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
            .overlay(
                // 显示对比标记
                Group {
                    if viewModel.isSelectedForCompare(record) {
                        HStack {
                            Spacer()

                            Image(systemName: "arrow.left.arrow.right.circle.fill")
                                .font(.system(size: 16))
                                .foregroundColor(Color.primaryAccent)
                                .padding(8)
                        }
                        .padding(.top, 4)
                    }
                },
                alignment: .topTrailing
            )
    }

    // 记录上下文菜单 - 修改为新的对比菜单
    @ViewBuilder
    private func recordContextMenu(_ record: BrewingRecord) -> some View {
        // 查看详情选项
        NavigationLink(value: record) {
            Label("查看详情", systemImage: "eye")
        }

        // 分享选项
        Button {
            shareRecord(record)
        } label: {
            Label("分享文字版", systemImage: "square.and.arrow.up")
        }

        Divider()

        // 复制配方选项
        Button {
            copyRecipeToNewRecord(record)
        } label: {
            Label("复制配方", systemImage: "doc.on.doc")
        }

        // 对比功能 - 根据状态显示不同选项
        if viewModel.isSelectedForCompare(record) {
            // 如果记录已在对比列表中，显示取消对比选项
            Button {
                _ = viewModel.removeFromCompare(record)
            } label: {
                Label("取消对比", systemImage: "minus.circle")
            }
        } else {
            // 如果记录不在对比列表中，根据当前对比列表数量决定是否可添加
            if viewModel.recordsForCompare.count < 2 {
                Button {
                    _ = viewModel.addToCompare(record)
                } label: {
                    Label("添加到对比", systemImage: "arrow.left.arrow.right")
                }
            } else {
                // 如果对比列表已满，显示禁用状态的选项
                Button {} label: {
                    Label("对比列表已满", systemImage: "arrow.left.arrow.right")
                }
                .disabled(true)
            }
        }

        Divider()

        // 修改选项
        Button {
            navigateToEditForm(record)
        } label: {
            Label {
                Text("修改")
            } icon: {
                Image("edit.symbols")
            }
        }

        // 删除选项
        Button(role: .destructive) {
            // 显示确认对话框
            showDeleteConfirmation(for: record)
        } label: {
            Label("删除", systemImage: "trash")
        }
    }

    // 处理行点击
    private func handleRowTap(_ record: BrewingRecord) {
        // 使用 navigationPath 触发导航，添加延迟确保导航系统准备就绪
        DispatchQueue.main.async {
            navigationPath = NavigationPath()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                navigationPath.append(record.id)
                print("📍 点击行导航到记录: \(record.id)")
            }
        }
    }

    // 其他方法保持不变
    // ...

    // 显示详情页的封装方法
    private func showRecordDetail(_ record: BrewingRecord) {
        // 使用 navigationPath 触发导航，添加延迟确保导航系统准备就绪
        DispatchQueue.main.async {
            navigationPath = NavigationPath()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                navigationPath.append(record.id)
                print("📍 显示详情页导航到记录: \(record.id)")
            }
        }

        // 立即重置所有轻扫状态，确保平滑过渡
        resetSwipeStates = true
    }

    // 获取咖啡豆详细信息文本（参考BrewLogDetailView的getBeanDetailText）
    private func getBeanDetailText(_ bean: CoffeeBean) -> String {
        var text = "\(bean.roaster) \(bean.name)"

        // 添加烘焙度
        text += "，\(bean.roastLevelDisplay)"

        // 添加处理法
        if let process = bean.process {
            text += process
        }

        // 添加产地信息
        if let origin = bean.origin, !origin.isEmpty {
            text += "（\(origin)）"
        }

        return text
    }

    // 获取设备文本（包含品牌信息）
    private func getEquipmentText(_ equipment: Equipment) -> String {
        var text = ""

        if let brand = equipment.brand, !brand.isEmpty {
            if equipment.name.contains(brand) {
                text = equipment.name
            } else {
                text = "\(brand) \(equipment.name)"
            }
        } else {
            text = equipment.name
        }

        return text
    }

    // 分享记录
    private func shareRecord(_ record: BrewingRecord) {
        // 构建分享文本，按照表单字段顺序
        var shareText = "☕️ 冲煮记录信息 (ID: \(record.id))\n\n"

        // 1. 冲煮信息
        if let recipeName = record.recipeName, !recipeName.isEmpty {
            shareText += "配方名称: \(recipeName)\n"
        }
        shareText += "记录时间: \(DateFormatter.shareFormatter.string(from: record.createdAt))\n"
        shareText += "评分: \(record.ratingDisplay) (\(record.ratingLevel)/10)\n"
        if !record.notes.isEmpty {
            shareText += "备注: \(record.notes)\n"
        }
        shareText += "\n"

        // 2. 用豆和器具
        shareText += "咖啡豆: \(getBeanDetailText(record.coffeeBean))\n"
        shareText += "冲煮器具: \(getEquipmentText(record.brewingEquipment))"
        if let brewMethod = record.brewingEquipment.brewMethodDisplay {
            shareText += " \(brewMethod)"
        }
        shareText += "\n"

        if let grindingEquipment = record.grindingEquipment {
            shareText += "磨豆机: \(getEquipmentText(grindingEquipment))\n"
        }
        if !record.grindSize.isEmpty && record.grindSize != "-" {
            shareText += "研磨设置: \(record.grindSize)\n"
        }

        // 小工具组合或小工具
        if let gadgetKit = record.gadgetKit {
            shareText += "小工具组合: \(gadgetKit.name)\n"
        } else if let gadgets = record.gadgets, !gadgets.isEmpty {
            let gadgetNames = gadgets.map { $0.name }.joined(separator: "、")
            shareText += "小工具: \(gadgetNames)\n"
        }
        shareText += "\n"

        // 3. 测量数据
        shareText += "粉重: \(String(format: "%.1f", record.doseWeight))g\n"
        shareText += "液重: \(String(format: "%.1f", record.yieldWeight))g\n"
        shareText += "粉水比: 1:\(String(format: "%.1f", record.yieldWeight / record.doseWeight))\n"
        shareText += "水温: \(String(format: "%.0f", record.waterTemperature))℃\n"

        // 格式化萃取时间
        let hours = record.brewingTime / 3600
        let minutes = (record.brewingTime % 3600) / 60
        let seconds = record.brewingTime % 60

        if hours > 0 {
            shareText += "萃取时间: \(hours)小时\(minutes)分\(seconds)秒\n"
        } else if minutes > 0 {
            shareText += "萃取时间: \(minutes)分\(seconds)秒\n"
        } else {
            shareText += "萃取时间: \(seconds)秒\n"
        }
        shareText += "\n"

        // 4. 环境数据（如果有的话）
        var hasEnvironmentData = false
        if let waterQuality = record.waterQuality, !waterQuality.isEmpty {
            shareText += "水质: \(waterQuality)\n"
            hasEnvironmentData = true
        }
        if let roomTemp = record.roomTemperature {
            shareText += "室温: \(String(format: "%.0f", roomTemp))ºc\n"
            hasEnvironmentData = true
        }
        if let humidity = record.roomHumidity {
            shareText += "环境湿度: \(humidity)%rh\n"
            hasEnvironmentData = true
        }
        if hasEnvironmentData {
            shareText += "\n"
        }

        // 5. 配方步骤（如果有的话）
        if !record.steps.isEmpty {
            shareText += "配方步骤:\n"
            for (index, step) in record.steps.enumerated() {
                shareText += "\(index + 1). \(step.text)"
                if let timer = step.timer, !timer.isEmpty && timer != "0:00" {
                    shareText += " (\(timer))"
                }
                shareText += "\n"
            }
            shareText += "\n"
        }

        // 6. 品鉴笔记（如果有评分的话）
        let hasFlavorRatings = record.aroma > 0 || record.acidity > 0 || record.sweetness > 0 || record.body > 0 || record.aftertaste > 0
        if hasFlavorRatings || !record.flavorTags.isEmpty {
            shareText += "品鉴笔记:\n"
            if hasFlavorRatings {
                shareText += "香气 \(record.aroma)/5\n"
                shareText += "酸质 \(record.acidity)/5\n"
                shareText += "甜度 \(record.sweetness)/5\n"
                shareText += "醇厚 \(record.body)/5\n"
                shareText += "余韵 \(record.aftertaste)/5\n"
            }
            if !record.flavorTags.isEmpty {
                let tagNames = record.flavorTags.map { $0.name }.joined(separator: "、")
                shareText += "风味标签: \(tagNames)\n"
            }
        }

        // 创建分享活动视图控制器
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)

        // 获取当前的UIWindow
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true, completion: nil)
        }
    }

    // 复制配方到新记录
    private func copyRecipeToNewRecord(_ record: BrewingRecord) {
        // 设置模板记录
        viewModel.templateRecord = record

        // 打开添加表单
        viewModel.showingAddSheet = true

        // 添加触觉反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)
    }

    // 检查记录是否有额外信息需要显示
    private func hasExtraInfo(_ record: BrewingRecord) -> Bool {
        // 检查是否有可显示的额外信息
        let hasNotes = !record.notes.isEmpty
        let hasSteps = !record.steps.isEmpty
        let hasFlavorTags = !record.flavorTags.isEmpty
        let hasFlavorRadar = record.aroma > 0 || record.acidity > 0 || record.sweetness > 0 || record.body > 0 || record.aftertaste > 0

        // 环境数据
        let hasWaterQuality = record.waterQuality != nil && !record.waterQuality!.isEmpty
        let hasRoomTemperature = record.roomTemperature != nil && record.roomTemperature! > 0
        let hasRoomHumidity = record.roomHumidity != nil && record.roomHumidity! > 0
        let hasEnvironmentData = hasWaterQuality || hasRoomTemperature || hasRoomHumidity

        return hasNotes || hasSteps || hasFlavorTags || hasFlavorRadar || hasEnvironmentData
    }

    // 复制记录信息到剪贴板
    private func copyRecordInfo(_ record: BrewingRecord) {
        // 构建复制文本
        var copyText = "☕️ 冲煮记录\n"
        copyText += "咖啡豆: \(record.coffeeBean.name)\n"
        copyText += "冲煮器具: \(record.brewingEquipment.name) \(record.brewingEquipment.brewMethodDisplay ?? "")\n"
        copyText += "粉重: \(record.doseWeight)g  水重: \(record.yieldWeight)g\n"
        copyText += "水温: \(record.waterTemperature)°C\n"
        copyText += "萃取时间: \(formatBrewingTime(Double(record.brewingTime)))\n"
        copyText += "评分: \(record.ratingDisplay)\n"

        // 复制到剪贴板
        UIPasteboard.general.string = copyText

        // 添加触觉反馈
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)
    }

    // 格式化萃取时间
    private func formatBrewingTime(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60

        if minutes > 0 {
            return "\(minutes)分\(seconds)秒"
        } else {
            return "\(seconds)秒"
        }
    }

    // 格式化对比列表中的日期显示
    private func formatCompareDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }

    // 更新年月份指示条
    private func updateDateHeader(for record: BrewingRecord) {
        // createdAt在BrewingRecord中是Date类型，直接使用不需要类型转换
        let date = record.createdAt

        // 格式化日期为"年月"格式
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月"
        formatter.locale = Locale(identifier: "zh_CN")

        let dateString = formatter.string(from: date)
        if currentDateHeader != dateString {
            currentDateHeader = dateString
        }
    }

    // 显示删除确认弹窗
    private func showDeleteConfirmation(for record: BrewingRecord) {
        recordToDelete = record
        showDeleteAlert = true
    }

    // 导航到修改表单
    private func navigateToEditForm(_ record: BrewingRecord) {
        // 直接设置记录，触发sheet显示
        print("设置修改记录: \(record.id), 咖啡豆: \(record.coffeeBean.name)")
        recordToEdit = record
    }

    // 删除记录
    private func deleteRecord(_ record: BrewingRecord) {
        isDeleting = true

        // 显示删除中的状态反馈
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred(intensity: 0.7)

        Task {
            do {
                // 检查用户是否已登录
                guard APIService.shared.isLoggedIn else {
                    await MainActor.run {
                        isDeleting = false
                        deleteError = "您的登录已失效，请重新登录后再试"
                        showDeleteErrorAlert = true

                        // 添加错误反馈
                        let generator = UINotificationFeedbackGenerator()
                        generator.notificationOccurred(.error)
                    }
                    return
                }

                // 使用ViewModel的deleteRecord方法，它会负责更新UI和统计数据
                try await viewModel.deleteRecord(record)

                await MainActor.run {
                    isDeleting = false

                    // 添加成功删除的振动反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // 发送通知更新配方册缓存
                    NotificationCenter.default.post(name: NSNotification.Name("RecipeListNeedsRefresh"), object: nil)
                    print("📣 发送RecipeListNeedsRefresh通知 - 删除冲煮记录")

                    // 记录已被删除，清除记录引用
                    recordToDelete = nil

                    // 重置所有轻扫状态，提供更好的用户体验
                    resetSwipeStates = true
                }
            } catch let apiError as APIError where apiError == APIError.unauthorized {
                await MainActor.run {
                    isDeleting = false
                    deleteError = "您的登录已失效，请重新登录后再试"
                    showDeleteErrorAlert = true

                    // 添加错误反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.error)

                    // 可能需要在这里触发重新登录流程
                    // ... 登录相关代码
                }
            } catch {
                await MainActor.run {
                    isDeleting = false

                    // 设置错误并显示错误提示弹窗
                    if let apiError = error as? APIError {
                        deleteError = apiError.userFriendlyMessage
                    } else {
                        deleteError = error.localizedDescription
                    }
                    showDeleteErrorAlert = true

                    // 添加错误反馈
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.error)
                }
            }
        }
    }

    // 添加用于处理对比操作的方法
    private func handleCompareAction(_ record: BrewingRecord) {
        // 检查记录是否已在对比列表中
        if viewModel.isSelectedForCompare(record) {
            // 如果已在列表中，则移除
            _ = viewModel.removeFromCompare(record)

            // 添加移除时的触觉反馈
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(.success)
        } else {
            // 如果对比列表已满（2条记录）
            if viewModel.recordsForCompare.count >= 2 {
                // 添加错误触觉反馈
                let generator = UINotificationFeedbackGenerator()
                generator.notificationOccurred(.error)

                // 可以选择添加一个全局提示
                // ...

                return
            }

            // 如果列表未满，添加到对比列表
            _ = viewModel.addToCompare(record)

            // 添加成功时的触觉反馈
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(.success)

            // 如果添加后达到2条记录，自动开始对比
            if viewModel.recordsForCompare.count == 2 {
                // 自动打开对比视图
                viewModel.startCompare()
            }
        }
    }

    // 准备视图和处理URL导航
    func onAppearHandler() async {
        // 验证token并加载数据
        await viewModel.fetchInitialData()
    }

    // 处理导航请求
    private func handleURLNavigation(brewLogId: Int) {
        print("🔍 处理导航请求，冲煮记录ID: \(brewLogId)")
        print("🔍 当前列表中的记录数量: \(viewModel.brewingRecords.count)")

        // 强制导航函数
        func forceNavigateToRecord(_ record: BrewingRecord) {
            print("🚀 强制导航到记录: \(record.id)")
            DispatchQueue.main.async {
                // 清空导航路径
                navigationPath = NavigationPath()
                // 等待更长时间确保导航系统完全重置
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    navigationPath.append(record.id)
                    print("📍 已添加记录到导航路径: \(record.id)")
                    // 清除导航标识
                    appState.selectedBrewLogId = nil
                    print("🧹 清除appState.selectedBrewLogId")
                }
            }
        }

        // 查找对应的冲煮记录
        if let record = viewModel.brewingRecords.first(where: { $0.id == brewLogId }) {
            print("✅ 找到记录，直接导航到详情页，记录名称: \(record.recipeName ?? "无名配方")")
            forceNavigateToRecord(record)
        } else {
            print("❌ 未找到ID为\(brewLogId)的冲煮记录，直接尝试单独加载")

            // 直接尝试单独加载该记录，不依赖列表数据
            Task {
                if let record = await viewModel.fetchRecordById(brewLogId) {
                    print("✅ 单独加载成功，导航到详情页，记录名称: \(record.recipeName ?? "无名配方")")
                    // 将记录添加到列表开头，确保navigationDestination能找到它
                    await MainActor.run {
                        viewModel.brewingRecords.insert(record, at: 0)
                        print("📝 将记录添加到列表开头，确保导航能找到")
                    }
                    // 强制导航
                    forceNavigateToRecord(record)
                } else {
                    print("❌ 单独加载失败，尝试清除筛选条件重新搜索")

                    // 保存当前筛选条件
                    let originalDateFrom = viewModel.filterDateFrom
                    let originalDateTo = viewModel.filterDateTo
                    let originalSearchQuery = viewModel.filterSearchQuery
                    let originalBrewMethod = viewModel.filterBrewMethod
                    let originalCoffeeBean = viewModel.filterCoffeeBean
                    let originalRatingRange = viewModel.filterRatingRange

                    // 临时清除所有筛选条件
                    await viewModel.clearFilters()

                    // 重新加载数据
                    await viewModel.fetchFilteredRecords(forceRefresh: true)

                    // 查找记录
                    if let record = viewModel.brewingRecords.first(where: { $0.id == brewLogId }) {
                        print("✅ 清除筛选后找到记录，导航到详情页，记录名称: \(record.recipeName ?? "无名配方")")
                        forceNavigateToRecord(record)
                    } else {
                        print("❌ 所有方法都失败，无法找到记录")
                        DispatchQueue.main.async {
                            // 恢复原筛选条件
                            viewModel.filterDateFrom = originalDateFrom
                            viewModel.filterDateTo = originalDateTo
                            viewModel.filterSearchQuery = originalSearchQuery
                            viewModel.filterBrewMethod = originalBrewMethod
                            viewModel.filterCoffeeBean = originalCoffeeBean
                            viewModel.filterRatingRange = originalRatingRange

                            // 重新应用筛选
                            Task {
                                await viewModel.fetchFilteredRecords(forceRefresh: true)
                            }

                            appState.selectedBrewLogId = nil
                        }
                    }
                }
            }
        }
    }

    // 检查并处理待处理的导航请求
    private func checkAndProcessPendingNavigation() {
        guard let pendingId = appState.pendingBrewLogNavigation else {
            return
        }

        print("🔍 检查待处理的导航请求，记录ID: \(pendingId)")

        // 如果数据已经加载，立即处理导航
        if !viewModel.brewingRecords.isEmpty {
            print("✅ 数据已准备好，立即处理导航")
            handleURLNavigation(brewLogId: pendingId)
            // 清除待处理的导航请求
            appState.pendingBrewLogNavigation = nil
        } else {
            print("⏳ 数据尚未加载，等待数据加载完成")
            // 数据还没有加载，等待 onChange(of: viewModel.brewingRecords) 触发
        }
    }
}

// 用于加载单个冲煮记录详情的视图
struct BrewLogDetailLoadingView: View {
    let recordId: Int
    @StateObject private var viewModel = BrewLogViewModel()
    @State private var record: BrewingRecord?
    @State private var isLoading = true
    @State private var error: String?

    var body: some View {
        Group {
            if isLoading {
                VStack(spacing: 16) {
                    BlinkingLoader(
                        color: .secondaryText,
                        width: 12,
                        height: 18,
                        duration: 1.5,
                        text: "正在加载记录..."
                    )
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.primaryBg)
            } else if let record = record {
                BrewLogDetailView(record: record)
            } else {
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)

                    Text("记录加载失败")
                        .font(.title2)
                        .foregroundColor(.primaryText)

                    if let error = error {
                        Text(error)
                            .font(.subheadline)
                            .foregroundColor(.secondaryText)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }

                    Button("重试") {
                        loadRecord()
                    }
                    .buttonStyle(.borderedProminent)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.primaryBg)
            }
        }
        .navigationTitle("冲煮记录")
        .navigationBarTitleDisplayMode(.large)
        .task {
            loadRecord()
        }
    }

    private func loadRecord() {
        isLoading = true
        error = nil

        Task {
            if let loadedRecord = await viewModel.fetchRecordById(recordId) {
                await MainActor.run {
                    record = loadedRecord
                    isLoading = false
                }
            } else {
                await MainActor.run {
                    error = "未找到ID为\(recordId)的冲煮记录"
                    isLoading = false
                }
            }
        }
    }
}

// 带有按压状态的卡片视图
struct CardWithPressState<Content: View>: View {
    var content: (Bool) -> Content
    var onTap: () -> Void

    @State private var isPressed = false
    @GestureState private var isDragging: Bool = false
    @State private var isScrolling = false // 滚动状态检测
    @State private var lastDragPosition: CGPoint? // 记录拖动位置
    @State private var scrollTimer: Timer? // 新增：滚动计时器用于延长滚动状态

    init(content: @escaping (Bool) -> Content, onTap: @escaping () -> Void) {
        self.content = content
        self.onTap = onTap
    }

    var body: some View {
        return content(isPressed && !isDragging && !isScrolling) // 在滚动时不应用按压效果
            .contentShape(Rectangle()) // 确保整个区域可点击
            .gesture(
                // 检测滚动操作，减小最小距离以提高灵敏度
                DragGesture(minimumDistance: 1)
                    .updating($isDragging) { _, state, _ in
                        state = true
                    }
                    .onChanged { value in
                        // 检测是否为滚动操作
                        if let lastPosition = lastDragPosition {
                            // 计算拖动距离
                            let distance = hypot(value.location.x - lastPosition.x,
                                                value.location.y - lastPosition.y)

                            // 降低阈值，提高灵敏度，但避免过于敏感
                            if distance > 3 {
                                if !isScrolling {
                                    isScrolling = true

                                    // 如果正在按压，使用平滑过渡取消按压状态
                                    if isPressed {
                                        withAnimation(.easeOut(duration: 0.15)) {
                                            isPressed = false
                                        }
                                    }
                                }

                                // 重置滚动计时器
                                scrollTimer?.invalidate()
                                scrollTimer = Timer.scheduledTimer(withTimeInterval: 0.35, repeats: false) { _ in
                                    DispatchQueue.main.async {
                                        isScrolling = false
                                    }
                                }
                            }
                        }

                        // 更新上次位置
                        lastDragPosition = value.location
                    }
                    .onEnded { _ in
                        // 延长滚动状态以防止误触
                        scrollTimer?.invalidate()
                        scrollTimer = Timer.scheduledTimer(withTimeInterval: 0.25, repeats: false) { _ in
                            DispatchQueue.main.async {
                                isScrolling = false
                                lastDragPosition = nil
                            }
                        }
                    }
            )
            .simultaneousGesture(
                TapGesture()
                    .onEnded { _ in
                        // 只有在非滚动和非拖动状态下才触发点击
                        if !isDragging && !isScrolling {
                            onTap()
                        }
                    }
            )
            .onLongPressGesture(minimumDuration: 0.01, maximumDistance: 10, pressing: { pressing in
                // 仅当没有拖动和没有滚动时才应用按压效果
                if !isDragging && !isScrolling {
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isPressed = pressing
                    }
                    if pressing {
                        // 保留长按时的轻触觉反馈
                        let generator = UIImpactFeedbackGenerator(style: .light)
                        generator.impactOccurred()
                    }
                }
            }, perform: { })  // 空实现，让TapGesture处理点击
    }
}

#if DEBUG
struct BrewLogListView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            BrewLogListView()
                .environmentObject(ThemeManager.shared)
        }
    }
}
#endif

// 带有按压状态的NavigationLink卡片视图 - 实际上不再使用，因为无法与新的navigation API兼容
struct CardWithNavigationLink<Content: View, Destination>: View where Destination: Hashable {
    let destination: Destination
    let content: (Bool) -> Content

    @State private var isPressed = false

    init(destination: Destination, @ViewBuilder content: @escaping (Bool) -> Content) {
        self.destination = destination
        self.content = content
    }

    var body: some View {
        // 这个结构实际上不再使用，现在直接在brewLogRowView中实现功能
        content(isPressed)
            .contentShape(Rectangle())
    }
}

// 自定义卡片按钮样式，去除默认的NavigationLink外观
struct CustomCardStyle: ButtonStyle {
    @Binding var isPressed: Bool

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            // 移除NavigationLink的默认样式
            .padding(.trailing, 0)
            .contentShape(Rectangle())
            .background(
                // 使用空白背景覆盖默认的箭头区域
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle())
            )
            .onChange(of: configuration.isPressed) { pressed in
                isPressed = pressed

                // 移除触觉反馈
            }
            .animation(.easeOut(duration: 0.1), value: configuration.isPressed)
    }
}

// 自定义隐藏导航箭头的modifier
struct HideNavigationArrowModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .foregroundColor(.clear)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// 带有点击按压效果的卡片视图（触发时通过onTap回调）
struct CardWithPressEffect<Content: View>: View {
    var content: (Bool) -> Content
    var onTap: () -> Void

    @State private var isPressed = false
    @GestureState private var isLongPressing = false // 长按状态跟踪
    @State private var isScrolling = false // 滚动状态追踪
    @State private var lastDragPosition: CGPoint? // 记录上次拖动位置
    @State private var scrollTimer: Timer? // 新增：滚动计时器

    init(content: @escaping (Bool) -> Content, onTap: @escaping () -> Void) {
        self.content = content
        self.onTap = onTap
    }

    var body: some View {
        Button(action: {
            // 只有在非滚动状态下才执行点击操作
            if !isScrolling {
                // 执行点击操作
                DispatchQueue.main.async {
                    onTap()
                }
            }
        }) {
            // 简化ZStack结构，移除不透明背景层和透明度变化
            content((isPressed || isLongPressing) && !isScrolling) // 在滚动时不应用按压状态
                .contentShape(Rectangle()) // 确保整个区域可点击
        }
        .buttonStyle(PlainButtonStyle()) // 使用Plain样式避免视觉效果干扰
        .simultaneousGesture(
            // 增强滚动检测灵敏度
            DragGesture(minimumDistance: 1)
                .onChanged { value in
                    // 检测是否为滚动操作
                    if let lastPosition = lastDragPosition {
                        // 计算拖动距离，判断是否为滚动
                        let distance = hypot(value.location.x - lastPosition.x,
                                           value.location.y - lastPosition.y)

                        // 降低阈值，提高灵敏度，但避免过于敏感
                        if distance > 3 {
                            if !isScrolling {
                                isScrolling = true

                                // 如果正在按压，使用平滑过渡取消按压状态
                                if isPressed {
                                    withAnimation(.easeOut(duration: 0.15)) {
                                        isPressed = false
                                    }
                                }
                            }

                            // 重置滚动计时器
                            scrollTimer?.invalidate()
                            scrollTimer = Timer.scheduledTimer(withTimeInterval: 0.35, repeats: false) { _ in
                                DispatchQueue.main.async {
                                    isScrolling = false
                                }
                            }
                        }
                    }

                    // 更新上次位置
                    lastDragPosition = value.location
                }
                .onEnded { _ in
                    // 延长滚动状态以防止误触
                    scrollTimer?.invalidate()
                    scrollTimer = Timer.scheduledTimer(withTimeInterval: 0.25, repeats: false) { _ in
                        DispatchQueue.main.async {
                            isScrolling = false
                            lastDragPosition = nil
                        }
                    }
                }
        )
        .simultaneousGesture(
            // 普通按压效果（仅在非滚动状态下触发）
            LongPressGesture(minimumDuration: 0.01, maximumDistance: 10)
                .onChanged { _ in
                    if !isScrolling {
                        // 使用更平滑的动画过渡
                        withAnimation(.easeIn(duration: 0.1)) {
                            isPressed = true
                        }
                    }
                }
                .onEnded { _ in
                    // 使用更长的动画时间，避免状态切换太突然
                    withAnimation(.easeOut(duration: 0.2)) {
                        isPressed = false
                    }
                }
        )
        // 添加长按状态追踪，处理上下文菜单情况
        .simultaneousGesture(
            LongPressGesture(minimumDuration: 0.5)
                .updating($isLongPressing) { _, state, _ in
                    // 只有在非滚动状态下才更新长按状态
                    if !isScrolling {
                        state = true // 追踪长按状态
                    }
                }
        )
        .onDisappear {
            // 清理计时器
            scrollTimer?.invalidate()
            scrollTimer = nil
        }
    }
}

// 卡片按钮样式，应用于NavigationLink
struct CardStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            // 移除NavigationLink的默认样式
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(configuration.isPressed ? Color.focusBg : Color.clear)
            )
            // 确保整个区域可以接收点击事件
            .contentShape(Rectangle())
            .onChange(of: configuration.isPressed) { pressed in
                // 移除触觉反馈
            }
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// 可按压的卡片（不触发导航）
struct PressableCard<Content: View>: View {
    var isPressed: Bool
    var content: () -> Content

    init(isPressed: Bool, @ViewBuilder content: @escaping () -> Content) {
        self.isPressed = isPressed
        self.content = content
    }

    var body: some View {
        content()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isPressed ? Color.focusBg : Color.clear)
            )
    }
}

// 自定义的无箭头卡片样式
struct NoArrowCardStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(configuration.isPressed ? Color.focusBg : Color.clear)
            )
            .contentShape(Rectangle())
            .onChange(of: configuration.isPressed) { pressed in
                // 移除触觉反馈
            }
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// 添加新的按钮样式到CardWithPressState之前
struct CardButtonStyle: ButtonStyle {
    @State private var isPressed = false

    func makeBody(configuration: Configuration) -> some View {
        return configuration.label
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(configuration.isPressed ? Color.focusBg : Color.clear)
            )
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
            .contentShape(Rectangle())
    }
}

// 导航辅助类 - 用于处理复杂导航场景
class NavigationHelper: ObservableObject {
    static let shared = NavigationHelper()

    @Published var selectedRecordID: Int? = nil

    private init() {}

    func navigateTo(record: BrewingRecord) {
        print("NavigationHelper - 触发导航到记录: \(record.id)")
        DispatchQueue.main.async {
            self.selectedRecordID = record.id
        }
    }
}
// 注意：该类已不再使用，使用了更简单的导航方式

// SwipeAction自定义扩展 - 用于创建更美观的轻扫操作按钮
extension View {
    /// 适用于轻扫操作按钮的风格修饰符
    @ViewBuilder
    func swipeActionStyle() -> some View {
        self
            .font(.system(size: 14, weight: .semibold))
            .padding(.vertical, 10)
            .padding(.horizontal, 12)
    }
}
