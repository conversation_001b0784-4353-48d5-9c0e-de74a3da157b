import SwiftUI

struct WhatsNewView: View {
    @Environment(\.dismiss) var dismiss
    
    let updates = [
        Update(version: "1.0.0", date: "2024-04-01", changes: [
            "支持使用咖啡搭子账号登录",
            "支持查看、添加、修改和删除冲煮记录",
            "支持查看、添加、修改和删除咖啡设备",
            "支持查看、添加、修改和删除咖啡豆",
            "支持查看冲煮记录热力图",
            "支持查看后见之明统计",
            "支持查看咖啡豆日历",
            "支持自定义底部导航栏菜单顺序"
        ])
    ]
    
    var body: some View {
        NavigationView {
            List {
                ForEach(updates) { update in
                    Section {
                        ForEach(update.changes, id: \.self) { change in
                            Text("• \(change)")
                                .padding(.vertical, 2)
                        }
                    } header: {
                        Text("版本 \(update.version)")
                    } footer: {
                        Text("发布于 \(update.date)")
                            .font(.footnote)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("更新日志")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct Update: Identifiable {
    let id = UUID()
    let version: String
    let date: String
    let changes: [String]
} 