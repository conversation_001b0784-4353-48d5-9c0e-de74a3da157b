import Foundation
import StoreKit
import Combine

class SubscriptionService: NSObject, ObservableObject {
    static let shared = SubscriptionService()
    
    // 当前用户的订阅类型
    @Published var currentSubscriptionType: SubscriptionType = .free
    @Published var currentSubscriptionPeriod: SubscriptionPeriod = .none
    @Published var expirationDate: Date? = nil
    
    // 产品列表
    @Published var products: [Product] = []
    @Published var isLoadingProducts: Bool = false
    @Published var loadingError: String? = nil
    
    // 试用资格
    @Published var isEligibleForTrial: Bool = false
    
    // 交易状态
    @Published var isPurchasing: Bool = false
    @Published var isRestoring: Bool = false
    @Published var purchaseError: String? = nil
    
    // UserDefaults keys
    private let subscriptionTypeKey = "subscription_type"
    private let subscriptionPeriodKey = "subscription_period"
    private let expirationDateKey = "expiration_date"
    
    // 用于StoreKit更新的处理
    private var updateListenerTask: Task<Void, Error>? = nil
    
    // 事务监听器
    private var transactionListener: Task<Void, Error>? = nil
    
    // 验证结果
    private var verificationResult: VerificationResult<StoreKit.Transaction>? = nil
    
    // 延迟初始化标记
    private var hasInitializedStoreKit = false
    
    private override init() {
        super.init()
        
        // 加载保存的订阅状态 (从本地UserDefaults，不涉及StoreKit)
        loadSavedSubscriptionState()
        
        // 延迟设置事务监听器和请求产品，避免应用启动时立即触发StoreKit
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.initializeStoreKit()
        }
    }
    
    // 分离StoreKit初始化逻辑，便于延迟执行
    private func initializeStoreKit() {
        // 避免重复初始化
        guard !hasInitializedStoreKit else { return }
        hasInitializedStoreKit = true
        
        // 设置事务监听器
        setupTransactionListener()
        
        // 请求产品
        Task {
            await requestProducts()
        }
    }
    
    deinit {
        updateListenerTask?.cancel()
        transactionListener?.cancel()
    }
    
    // MARK: - 加载保存的订阅状态
    private func loadSavedSubscriptionState() {
        let defaults = UserDefaults.standard
        if let typeString = defaults.string(forKey: subscriptionTypeKey),
           let subscriptionType = SubscriptionType(rawValue: typeString) {
            self.currentSubscriptionType = subscriptionType
        }
        
        if let periodString = defaults.string(forKey: subscriptionPeriodKey),
           let subscriptionPeriod = SubscriptionPeriod(rawValue: periodString) {
            self.currentSubscriptionPeriod = subscriptionPeriod
        }
        
        if let expirationDate = defaults.object(forKey: expirationDateKey) as? Date {
            self.expirationDate = expirationDate
            
            // 如果过期日期在当前日期之前，重置订阅状态
            if expirationDate < Date() {
                resetSubscriptionState()
            }
        }
    }
    
    // MARK: - 手动初始化StoreKit（在需要时调用）
    public func ensureInitialized() {
        if !hasInitializedStoreKit {
            initializeStoreKit()
        }
    }
    
    // MARK: - 保存订阅状态
    private func saveSubscriptionState() {
        let defaults = UserDefaults.standard
        defaults.set(currentSubscriptionType.rawValue, forKey: subscriptionTypeKey)
        defaults.set(currentSubscriptionPeriod.rawValue, forKey: subscriptionPeriodKey)
        if let expirationDate = expirationDate {
            defaults.set(expirationDate, forKey: expirationDateKey)
        } else {
            defaults.removeObject(forKey: expirationDateKey)
        }
    }
    
    // MARK: - 重置订阅状态
    private func resetSubscriptionState() {
        self.currentSubscriptionType = .free
        self.currentSubscriptionPeriod = .none
        self.expirationDate = nil
        saveSubscriptionState()
    }
    
    // MARK: - 事务监听器设置
    private func setupTransactionListener() {
        // 先尝试同步历史交易
        Task {
            do {
                try await AppStore.sync()
            } catch {
                print("初始App Store同步失败: \(error)")
            }
        }
        
        transactionListener = Task.detached {
            // 开始处理交易
            for await result in Transaction.updates {
                do {
                    let transaction = try self.checkVerified(result)
                    
                    // 处理事务
                    await self.handle(transaction: transaction)
                    
                    // 完成事务
                    await transaction.finish()
                } catch {
                    // 处理验证错误
                    print("事务验证失败: \(error)")
                }
            }
        }
    }
    
    // MARK: - 交易验证
    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            // 可能是伪造的交易
            throw StoreError.failedVerification
        case .verified(let safe):
            // 交易已验证
            return safe
        }
    }
    
    // MARK: - 处理交易
    private func handle(transaction: StoreKit.Transaction) async {
        guard let productID = transaction.productID.components(separatedBy: ".").last else {
            return
        }
        
        // 检查交易状态
        if transaction.revocationDate != nil {
            // 这个购买已被撤销，重置状态
            await MainActor.run {
                resetSubscriptionState()
            }
            return
        }
        
        if let expirationDate = transaction.expirationDate, expirationDate < Date() {
            // 订阅已过期，重置状态
            await MainActor.run {
                resetSubscriptionState()
            }
            return
        }
        
        // 根据产品ID处理订阅
        if productID.contains("monthly") {
            await MainActor.run {
                self.currentSubscriptionType = .premium
                self.currentSubscriptionPeriod = .monthly
                self.expirationDate = transaction.expirationDate
                self.saveSubscriptionState()
            }
        } else if productID.contains("yearly") {
            await MainActor.run {
                self.currentSubscriptionType = .premium
                self.currentSubscriptionPeriod = .yearly
                self.expirationDate = transaction.expirationDate
                self.saveSubscriptionState()
            }
        }
    }
    
    // MARK: - 请求产品
    @MainActor
    func requestProducts() async {
        do {
            isLoadingProducts = true
            loadingError = nil
            
            // 获取所有产品ID
            let productIDs = SubscriptionProduct.allCases.map { $0.id }
            
            // 请求产品
            let storeProducts = try await Product.products(for: productIDs)
            
            // 如果没有产品，尝试恢复历史交易并重试
            if storeProducts.isEmpty {
                // 尝试恢复历史交易
                try? await AppStore.sync()
                
                // 等待一段时间再重试
                try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                
                // 重新请求产品
                let retryProducts = try await Product.products(for: productIDs)
                if !retryProducts.isEmpty {
                    // 处理重试成功的产品
                    await processLoadedProducts(retryProducts)
                    return
                }
            }
            
            await processLoadedProducts(storeProducts)
        } catch {
            isLoadingProducts = false
            loadingError = "无法加载产品信息: \(error.localizedDescription)"
            print("产品请求失败: \(error)")
            
            // 尝试重新获取历史交易
            do {
                try await AppStore.sync()
            } catch {
                print("同步App Store失败: \(error)")
            }
        }
    }
    
    // 处理加载的产品
    @MainActor
    private func processLoadedProducts(_ storeProducts: [Product]) async {
        // 检查试用资格
        if let product = storeProducts.first {
            isEligibleForTrial = await checkTrialEligibility(for: product)
        }
        
        // 按价格排序，年度套餐在前面
        self.products = storeProducts.sorted { product1, product2 in
            // 年度套餐的ID包含yearly
            if product1.id.contains("yearly") && !product2.id.contains("yearly") {
                return true
            } else if !product1.id.contains("yearly") && product2.id.contains("yearly") {
                return false
            } else {
                // 其他情况按价格排序
                return product1.price > product2.price
            }
        }
        
        isLoadingProducts = false
    }
    
    // MARK: - 检查试用资格
    @MainActor
    private func checkTrialEligibility(for product: Product) async -> Bool {
        guard let subscription = product.subscription else {
            return false
        }
        return await subscription.isEligibleForIntroOffer
    }
    
    // MARK: - 购买产品
    @MainActor
    func purchase(_ product: Product) async {
        do {
            isPurchasing = true
            purchaseError = nil
            
            // 创建支付选项
            let options: Set<Product.PurchaseOption> = [.appAccountToken(UUID())]
            
            // 执行购买
            let result = try await product.purchase(options: options)
            
            // 处理购买结果
            switch result {
            case .success(let verification):
                // 购买成功，处理验证结果
                do {
                    let transaction = try checkVerified(verification)
                    
                    // 更新订阅状态
                    if product.id.contains("monthly") {
                        currentSubscriptionType = .premium
                        currentSubscriptionPeriod = .monthly
                        expirationDate = transaction.expirationDate
                    } else if product.id.contains("yearly") {
                        currentSubscriptionType = .premium
                        currentSubscriptionPeriod = .yearly
                        expirationDate = transaction.expirationDate
                    }
                    
                    // 保存订阅状态
                    saveSubscriptionState()
                    
                    // 完成事务
                    await transaction.finish()
                    
                } catch {
                    purchaseError = "验证失败: \(error.localizedDescription)"
                }
                
            case .userCancelled:
                // 用户取消购买
                purchaseError = "您取消了购买"
                
            case .pending:
                // 等待外部操作，如父母批准
                purchaseError = "购买正在等待批准"
                
            default:
                // 其他状态
                purchaseError = "购买状态未知"
            }
            
            isPurchasing = false
        } catch {
            isPurchasing = false
            purchaseError = "购买失败: \(error.localizedDescription)"
            print("购买失败: \(error)")
        }
    }
    
    // MARK: - 恢复购买
    @MainActor
    func restorePurchases() async {
        do {
            isRestoring = true
            purchaseError = nil
            
            // 请求最新的订阅状态
            try await AppStore.sync()
            
            // 检查是否有历史交易
            var hasActiveSubscription = false
            
            // 查询所有交易历史
            for await result in Transaction.all {
                do {
                    let transaction = try checkVerified(result)
                    
                    // 检查是否是订阅交易和是否仍然有效
                    if let expirationDate = transaction.expirationDate, 
                       expirationDate > Date(),
                       transaction.revocationDate == nil {
                        // 处理有效的交易
                        await handle(transaction: transaction)
                        hasActiveSubscription = true
                    }
                } catch {
                    print("交易验证失败: \(error)")
                }
            }
            
            // 如果没有找到有效订阅，尝试重新加载产品
            if !hasActiveSubscription {
                Task {
                    await requestProducts()
                }
            }
            
            isRestoring = false
        } catch {
            isRestoring = false
            purchaseError = "恢复购买失败: \(error.localizedDescription)"
            print("恢复购买失败: \(error)")
            
            // 即使恢复失败，也尝试重新加载产品
            Task {
                await requestProducts()
            }
        }
    }
    
    // MARK: - 检查功能权限
    func hasAccess(to feature: SubscriptionFeature) -> Bool {
        // 如果功能不需要高级版，任何用户都可以访问
        if !feature.isPremiumOnly {
            return true
        }
        
        // 确保StoreKit已初始化
        ensureInitialized()
        
        // 检查用户是否有高级版订阅
        return currentSubscriptionType == .premium && (expirationDate == nil || expirationDate! > Date())
    }
}

// 自定义错误
enum StoreError: Error {
    case failedVerification
}

// 订阅功能扩展
extension Product {
    var subscriptionPeriod: String {
        if let subscription = subscription {
            switch subscription.subscriptionPeriod.unit {
            case .day:
                let count = subscription.subscriptionPeriod.value
                return "\(count)天"
            case .week:
                let count = subscription.subscriptionPeriod.value
                return "\(count)周"
            case .month:
                let count = subscription.subscriptionPeriod.value
                return "\(count)个月"
            case .year:
                let count = subscription.subscriptionPeriod.value
                return "\(count)年"
            @unknown default:
                return "未知"
            }
        }
        return "不适用"
    }
    
    var localizedPrice: String {
        return self.displayPrice
    }
} 