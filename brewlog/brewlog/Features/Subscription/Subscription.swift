import Foundation
import StoreKit

enum SubscriptionType: String, Codable {
    case free = "免费版"
    case premium = "高级版"
    
    var description: String {
        switch self {
        case .free:
            return "免费版"
        case .premium:
            return "高级版"
        }
    }
    
    var isPremium: Bool {
        switch self {
        case .premium:
            return true
        case .free:
            return false
        }
    }
}

enum SubscriptionPeriod: String, Codable {
    case none = "无"
    case monthly = "月付"
    case yearly = "年付"
    
    var description: String {
        switch self {
        case .none:
            return "无"
        case .monthly:
            return "月付"
        case .yearly:
            return "年付"
        }
    }
}

struct SubscriptionFeature: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let isPremiumOnly: Bool
    let icon: String
    
    static let allFeatures: [SubscriptionFeature] = [
        // 免费功能
        SubscriptionFeature(
            title: "冲煮记录",
            description: "记录和管理您的所有咖啡冲煮数据",
            isPremiumOnly: false,
            icon: "cup.and.saucer.fill"
        ),
        SubscriptionFeature(
            title: "设备管理",
            description: "管理您的咖啡设备",
            isPremiumOnly: false,
            icon: "gearshape.2.fill"
        ),
        SubscriptionFeature(
            title: "咖啡豆管理",
            description: "管理您的咖啡豆",
            isPremiumOnly: false,
            icon: "leaf.fill"
        ),
        SubscriptionFeature(
            title: "数据统计",
            description: "查看您的咖啡冲煮统计数据",
            isPremiumOnly: false,
            icon: "chart.bar.fill"
        ),
        
        // 高级功能
        SubscriptionFeature(
            title: "轻扫选项",
            description: "在卡片上添加自定义轻扫选项",
            isPremiumOnly: true,
            icon: "hand.tap"
        ),
        SubscriptionFeature(
            title: "自定义卡片信息",
            description: "自定义您的卡片显示信息",
            isPremiumOnly: true,
            icon: "rectangle.grid.1x2"
        ),
        SubscriptionFeature(
            title: "养豆期提醒",
            description: "接收咖啡豆养豆期提醒",
            isPremiumOnly: true,
            icon: "bell.badge"
        ),
        SubscriptionFeature(
            title: "小组件",
            description: "添加主屏幕小组件",
            isPremiumOnly: true,
            icon: "apps.iphone"
        ),
        SubscriptionFeature(
            title: "快捷方式",
            description: "添加iOS快捷方式",
            isPremiumOnly: true,
            icon: "square.stack.3d.up.fill"
        ),
        SubscriptionFeature(
            title: "咖啡因摄入统计",
            description: "跟踪与健康应用集成的咖啡因摄入量",
            isPremiumOnly: true,
            icon: "heart.text.square"
        ),
        SubscriptionFeature(
            title: "更换主题和图标",
            description: "自定义应用主题色和图标",
            isPremiumOnly: true,
            icon: "paintpalette"
        ),
        SubscriptionFeature(
            title: "URL Scheme访问",
            description: "通过URL Scheme深度链接访问应用功能",
            isPremiumOnly: true,
            icon: "link"
        )
    ]
}

enum SubscriptionProduct: String, CaseIterable {
    case monthly = "com.kafeidazi.brewlog.premium.monthly"
    case yearly = "com.kafeidazi.brewlog.premium.yearly"
    
    var id: String {
        return self.rawValue
    }
    
    var displayName: String {
        switch self {
        case .monthly:
            return "高级版(月付)"
        case .yearly:
            return "高级版(年付)"
        }
    }
} 