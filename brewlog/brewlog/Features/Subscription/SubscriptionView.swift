import SwiftUI
import StoreKit

struct SubscriptionView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var subscriptionService = SubscriptionService.shared
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var selectedProduct: Product? = nil
    @State private var showTrialAlert: Bool = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 标题
                Text("升级到高级版")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top, 20)
                
                // 图标
                Image(systemName: "star.circle.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 80, height: 80)
                    .foregroundColor(.orange)
                
                // 功能列表
                featuresSection
                
                // 价格选择
                pricingSection
                
                // 试用信息
                VStack(spacing: 8) {
                    if subscriptionService.isLoadingProducts {
                        // 加载中不显示任何试用信息
                    } else if subscriptionService.products.isEmpty {
                        // 产品未加载成功时不显示试用相关信息
                    } else if subscriptionService.isEligibleForTrial {
                        Text("首次订阅可享额外赠送的30天免费试用")
                            .font(.headline)
                            .multilineTextAlignment(.center)
                        
                        Text("之后按选择的计划自动续费")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    } else {
                        Text("您已使用过免费试用")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                }
                .padding()
                
                // 按钮
                buttonSection
                
                // 法律信息
                legalInformationSection
            }
            .padding()
        }
        .navigationBarTitle("高级版", displayMode: .inline)
        .navigationBarItems(trailing: Button("关闭") {
            dismiss()
        })
        .overlay(
            Group {
                if subscriptionService.isPurchasing || subscriptionService.isRestoring {
                    ZStack {
                        Color.black.opacity(0.4)
                            .edgesIgnoringSafeArea(.all)
                        
                        VStack {
                            BlinkingLoader(
                                color: .primaryBg,
                                width: 12,
                                height: 18,
                                duration: 1.5,
                                text: subscriptionService.isPurchasing ? "正在处理购买..." : "正在恢复购买..."
                            )
                            .padding()
                        }
                        .padding(30)
                        .background(Color(UIColor.systemBackground).opacity(0.9))
                        .cornerRadius(15)
                        .shadow(radius: 10)
                    }
                }
            }
        )
        .alert(isPresented: .init(
            get: { subscriptionService.purchaseError != nil },
            set: { if !$0 { subscriptionService.purchaseError = nil } }
        )) {
            Alert(
                title: Text("错误"),
                message: Text(subscriptionService.purchaseError ?? "未知错误"),
                dismissButton: .default(Text("确定"))
            )
        }
        .alert(isPresented: $showTrialAlert) {
            Alert(
                title: Text("开始免费试用"),
                message: Text("您将享受30天免费试用期。试用结束后，将按照所选计划自动续订。您可以随时在试用期内取消。"),
                primaryButton: .default(Text("继续")) {
                    if let product = selectedProduct {
                        Task {
                            await subscriptionService.purchase(product)
                        }
                    }
                },
                secondaryButton: .cancel(Text("取消"))
            )
        }
        .onAppear {
            // 如果产品列表为空，请求产品
            if subscriptionService.products.isEmpty {
                Task {
                    // 同时尝试恢复购买和请求产品
                    if !subscriptionService.isLoadingProducts {
                        // 先尝试恢复购买，这可能会触发现有订阅的恢复
                        await subscriptionService.restorePurchases()
                        
                        // 如果产品仍然为空，再尝试请求产品
                        if subscriptionService.products.isEmpty {
                            await subscriptionService.requestProducts()
                        }
                    }
                }
            }
            
            // 如果只有一个产品，默认选择它
            if subscriptionService.products.count == 1 {
                selectedProduct = subscriptionService.products.first
            } else if !subscriptionService.products.isEmpty {
                // 默认选择年度订阅（如果有）
                selectedProduct = subscriptionService.products.first(where: { $0.id.contains("yearly") })
                    ?? subscriptionService.products.first
            }
        }
    }
    
    // MARK: - 功能部分
    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 18) {
            Text("高级版功能")
                .font(.headline)
                .padding(.bottom, 5)
            
            ForEach(SubscriptionFeature.allFeatures.filter { $0.isPremiumOnly }) { feature in
                HStack(spacing: 16) {
                    Image(systemName: feature.icon)
                        .foregroundColor(.blue)
                        .frame(width: 25, height: 25)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(feature.title)
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text(feature.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - 价格部分
    private var pricingSection: some View {
        VStack(spacing: 15) {
            Text("选择您的计划")
                .font(.headline)
            
            if subscriptionService.isLoadingProducts {
                BlinkingLoader(text: "正在加载订阅选项...")
                    .padding()
            } else if subscriptionService.products.isEmpty {
                VStack(spacing: 12) {
                    Text("无法加载订阅选项")
                        .foregroundColor(.secondary)
                        .italic()
                    
                    if let errorMessage = subscriptionService.loadingError {
                        Text(errorMessage)
                            .font(.caption)
                            .foregroundColor(.red)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    
                    VStack(spacing: 8) {
                        Button(action: {
                            Task {
                                await subscriptionService.requestProducts()
                            }
                        }) {
                            HStack {
                                Text("重试加载")
                                Image(systemName: "arrow.clockwise")
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.blue)
                            .foregroundColor(.primaryBg)
                            .cornerRadius(8)
                        }
                        
                        Button(action: {
                            Task {
                                await subscriptionService.restorePurchases()
                            }
                        }) {
                            HStack {
                                Text("恢复购买")
                                Image(systemName: "arrow.triangle.2.circlepath")
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color(UIColor.secondarySystemBackground))
                            .foregroundColor(.primary)
                            .cornerRadius(8)
                        }
                    }
                    .padding(.top, 8)
                }
                .padding()
            } else {
                ForEach(subscriptionService.products, id: \.id) { product in
                    SubscriptionOptionView(
                        product: product,
                        isSelected: selectedProduct?.id == product.id,
                        onTap: {
                            selectedProduct = product
                        }
                    )
                }
            }
        }
        .padding()
    }
    
    // MARK: - 按钮部分
    private var buttonSection: some View {
        VStack(spacing: 15) {
            // 订阅按钮
            Button(action: {
                if selectedProduct != nil {
                    if subscriptionService.isEligibleForTrial {
                        showTrialAlert = true
                    } else {
                        Task {
                            await subscriptionService.purchase(selectedProduct!)
                        }
                    }
                }
            }) {
                HStack {
                    Text(subscriptionService.isEligibleForTrial ? "开始30天免费试用" : "立即订阅")
                    Image(systemName: "arrow.right.circle.fill")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.primaryBg)
                .cornerRadius(10)
                .font(.headline)
            }
            .disabled(selectedProduct == nil || subscriptionService.isPurchasing || subscriptionService.isRestoring)
            
            // 恢复购买按钮
            Button(action: {
                Task {
                    await subscriptionService.restorePurchases()
                }
            }) {
                Text("恢复购买")
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(UIColor.secondarySystemBackground))
                    .foregroundColor(.primary)
                    .cornerRadius(10)
                    .font(.subheadline)
            }
            .disabled(subscriptionService.isPurchasing || subscriptionService.isRestoring)
        }
        .padding(.vertical)
    }
    
    // MARK: - 法律信息部分
    private var legalInformationSection: some View {
        VStack(spacing: 10) {
            Text("订阅条款")
                .font(.footnote)
                .fontWeight(.medium)
            
            Text("除非在当前订阅期结束前至少24小时取消，否则订阅将自动续订。您可以在App Store的账户设置中管理订阅和取消。")
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            HStack(spacing: 10) {
                Link("隐私政策", destination: URL(string: "https://www.kafeidazi.com/privacy")!)
                    .font(.caption)
                
                Text("•")
                    .foregroundColor(.secondary)
                
                Link("使用条款", destination: URL(string: "https://www.kafeidazi.com/terms")!)
                    .font(.caption)
            }
        }
        .padding(.vertical)
    }
}

// MARK: - 订阅选项视图
struct SubscriptionOptionView: View {
    let product: Product
    let isSelected: Bool
    let onTap: () -> Void
    
    @State private var isBestValue: Bool = false
    @StateObject private var subscriptionService = SubscriptionService.shared
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text(getProductTitle(for: product))
                        .font(.headline)
                    
                    if isBestValue {
                        Text("节省17%")
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 3)
                            .background(Color.green)
                            .foregroundColor(.primaryBg)
                            .cornerRadius(10)
                    }
                }
                
                if product.subscription != nil {
                    Text("每\(product.subscriptionPeriod) \(product.localizedPrice)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    if subscriptionService.isEligibleForTrial {
                        Text("额外赠送30天免费试用（限首次购买）")
                            .font(.caption)
                            .foregroundColor(.blue)
                    }
                }
            }
            
            Spacer()
            
            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                .foregroundColor(isSelected ? .blue : .gray)
                .font(.title2)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
        )
        .onTapGesture {
            onTap()
        }
        .onAppear {
            // 检查是否是最优惠的选项（年度订阅）
            isBestValue = product.id.contains("yearly")
        }
    }
    
    // 获取产品标题
    private func getProductTitle(for product: Product) -> String {
        if product.id.contains("monthly") {
            return "月度订阅"
        } else if product.id.contains("yearly") {
            return "年度订阅"
        }
        return product.displayName
    }
    
    // 获取试用期信息
    private func getTrialPeriod(for product: Product) -> String? {
        // 这里简化实现，实际应该检查产品的折扣情况
        return "30天"
    }
}

#Preview {
    NavigationView {
        SubscriptionView()
    }
} 
