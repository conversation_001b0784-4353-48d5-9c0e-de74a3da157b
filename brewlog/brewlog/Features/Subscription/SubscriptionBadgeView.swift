import SwiftUI

struct SubscriptionBadgeView: View {
    @ObservedObject private var subscriptionService = SubscriptionService.shared
    @State private var showSubscriptionView = false
    
    var body: some View {
        Button(action: {
            showSubscriptionView = true
        }) {
            HStack(spacing: 6) {
                Image(systemName: subscriptionService.currentSubscriptionType == .premium ? "star.circle.fill" : "star.circle")
                    .foregroundColor(subscriptionService.currentSubscriptionType == .premium ? .yellow : .gray)
                
                Text(subscriptionService.currentSubscriptionType == .premium ? "高级版" : "免费版")
                    .font(.caption)
                    .fontWeight(.medium)
                
                if subscriptionService.currentSubscriptionType == .premium {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                }
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 5)
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(subscriptionService.currentSubscriptionType == .premium ? 
                          Color.yellow.opacity(0.2) : Color.gray.opacity(0.1))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 15)
                    .strokeBorder(subscriptionService.currentSubscriptionType == .premium ? 
                                  Color.yellow.opacity(0.5) : Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showSubscriptionView) {
            NavigationView {
                SubscriptionView()
            }
        }
    }
}

struct SubscriptionBadgeCompactView: View {
    @ObservedObject private var subscriptionService = SubscriptionService.shared
    @State private var showSubscriptionView = false
    
    var body: some View {
        Button(action: {
            showSubscriptionView = true
        }) {
            Image(systemName: subscriptionService.currentSubscriptionType == .premium ? "star.circle.fill" : "star.circle")
                .foregroundColor(subscriptionService.currentSubscriptionType == .premium ? .yellow : .gray)
                .font(.system(size: 22))
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showSubscriptionView) {
            NavigationView {
                SubscriptionView()
            }
        }
    }
}

// 订阅锁视图 - 用于阻止非高级用户访问高级功能
struct SubscriptionLockView: View {
    @State private var showSubscriptionView = false
    let featureTitle: String
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "lock.circle.fill")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 70, height: 70)
                .foregroundColor(.gray)
            
            Text("\(featureTitle)是高级功能")
                .font(.title3)
                .fontWeight(.bold)
            
            Text("升级到高级版以解锁所有功能")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button(action: {
                showSubscriptionView = true
            }) {
                HStack {
                    Text("查看高级版")
                    Image(systemName: "arrow.right")
                }
                .padding(.horizontal, 30)
                .padding(.vertical, 12)
                .background(Color.blue)
                .foregroundColor(.primaryBg)
                .cornerRadius(10)
            }
            .padding(.top, 10)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
        .sheet(isPresented: $showSubscriptionView) {
            NavigationView {
                SubscriptionView()
            }
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        SubscriptionBadgeView()
        SubscriptionBadgeCompactView()
        SubscriptionLockView(featureTitle: "轻扫选项")
            .frame(height: 300)
    }
    .padding()
} 
