import SwiftUI

struct RatingView: View {
    @Binding var rating: Int
    let title: String
    
    var body: some View {
        HStack {
            Text(title)
            Spacer()
            ForEach(1...5, id: \.self) { index in
                Image(systemName: index <= rating ? "star.fill" : "star")
                    .foregroundColor(.yellow)
                    .onTapGesture {
                        rating = index
                    }
            }
        }
    }
}

#if DEBUG
struct RatingView_Previews: PreviewProvider {
    static var previews: some View {
        RatingView(rating: .constant(3), title: "评分")
    }
}
#endif 