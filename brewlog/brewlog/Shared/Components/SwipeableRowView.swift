import SwiftUI

struct SwipeableRowView<Content: View>: View {
    @StateObject private var gestureSettings = GestureSettings.shared
    @ObservedObject private var subscriptionService = SubscriptionService.shared

    // 内容视图
    let content: Content

    // 回调函数（简化为只保留删除和修改）
    var onDelete: (() -> Void)? = nil
    var onEdit: (() -> Void)? = nil
    var onCompare: (() -> Void)? = nil  // 添加对比操作回调
    var onCopyRecipe: (() -> Void)? = nil  // 添加复制配方操作回调
    var onRestock: (() -> Void)? = nil  // 添加回购操作回调

    // 列表类型，默认为冲煮记录列表
    var listSectionType: ListSectionType = .brewLog

    // 拖动状态
    @State private var offset: CGFloat = 0
    @State private var prevOffset: CGFloat = 0
    @State private var isFingerOnScreen: Bool = false

    // 触发阈值
    private let swipeThreshold: CGFloat = 75

    // 自定义行的最大拖动距离
    private let maxLeftSwipeDistance: CGFloat = 150
    private let maxRightSwipeDistance: CGFloat = 150

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    // 添加操作的设置方法
    func onDelete(_ action: @escaping () -> Void) -> Self {
        var copy = self
        copy.onDelete = action
        return copy
    }

    func onEdit(_ action: @escaping () -> Void) -> Self {
        var copy = self
        copy.onEdit = action
        return copy
    }

    func onCompare(_ action: @escaping () -> Void) -> Self {
        var copy = self
        copy.onCompare = action
        return copy
    }

    func onCopyRecipe(_ action: @escaping () -> Void) -> Self {
        var copy = self
        copy.onCopyRecipe = action
        return copy
    }

    func onRestock(_ action: @escaping () -> Void) -> Self {
        var copy = self
        copy.onRestock = action
        return copy
    }

    // 设置列表类型的方法
    func listSection(_ type: ListSectionType) -> Self {
        var copy = self
        copy.listSectionType = type
        return copy
    }

    var body: some View {
        ZStack {
            // 背景层 - 向左轻扫
            leftActionsView
                .frame(width: abs(min(0, offset)), height: nil, alignment: .trailing)
                .opacity(offset < 0 ? 1 : 0)

            // 背景层 - 向右轻扫
            rightActionsView
                .frame(width: max(0, offset), height: nil, alignment: .leading)
                .opacity(offset > 0 ? 1 : 0)

            // 内容层
            content
                .offset(x: offset)
                .gesture(
                    DragGesture()
                        .onChanged { gesture in
                            // 只有当用户订阅了高级版，或者使用默认手势时才响应
                            if subscriptionService.currentSubscriptionType == .premium || !gestureSettings.canUseCustomGestures {
                                withAnimation(.interactiveSpring()) {
                                    // 获取当前方向的轻扫选项
                                    let leftAction = gestureSettings.getAction(for: listSectionType, direction: .left)
                                    let rightAction = gestureSettings.getAction(for: listSectionType, direction: .right)

                                    // 检查操作是否在可用列表中
                                    let availableActions = SwipeAction.availableActions(for: listSectionType)
                                    let leftActionAvailable = availableActions.contains(leftAction)
                                    let rightActionAvailable = availableActions.contains(rightAction)

                                    isFingerOnScreen = true

                                    let newOffset = gesture.translation.width + prevOffset

                                    // 限制最大拖动距离，并禁止对应方向的轻扫（当设置为"无"时或操作不可用时）
                                    if newOffset <= 0 {  // 左滑
                                        // 如果向左轻扫设为"无"或操作不可用，不允许左滑
                                        if leftAction == .none || !leftActionAvailable {
                                            offset = 0
                                        } else {
                                            offset = max(newOffset, -maxLeftSwipeDistance)
                                        }
                                    } else {  // 右滑
                                        // 如果向右轻扫设为"无"或操作不可用，不允许右滑
                                        if rightAction == .none || !rightActionAvailable {
                                            offset = 0
                                        } else {
                                            offset = min(newOffset, maxRightSwipeDistance)
                                        }
                                    }
                                }
                            }
                        }
                        .onEnded { _ in
                            // 只有当用户订阅了高级版，或者使用默认手势时才响应
                            if subscriptionService.currentSubscriptionType == .premium || !gestureSettings.canUseCustomGestures {
                                withAnimation(.spring()) {
                                    isFingerOnScreen = false

                                    if offset < -swipeThreshold {
                                        // 执行向左轻扫
                                        executeLeftSwipeAction()
                                    } else if offset > swipeThreshold {
                                        // 执行向右轻扫
                                        executeRightSwipeAction()
                                    }

                                    // 无论是否触发操作，都重置偏移量
                                    offset = 0
                                    prevOffset = 0
                                }
                            }
                        }
                )
        }
    }

    private var leftActionsView: some View {
        HStack(spacing: 0) {
            Spacer()

            // 获取向左轻扫设置并显示
            let leftAction = gestureSettings.getAction(for: listSectionType, direction: .left)
            // 检查操作是否在可用列表中
            let availableActions = SwipeAction.availableActions(for: listSectionType)

            if leftAction != .none && availableActions.contains(leftAction) {
                actionButton(for: leftAction)
            }
        }
        .background(Color.clear)
    }

    private var rightActionsView: some View {
        HStack(spacing: 0) {
            // 获取向右轻扫设置并显示
            let rightAction = gestureSettings.getAction(for: listSectionType, direction: .right)
            // 检查操作是否在可用列表中
            let availableActions = SwipeAction.availableActions(for: listSectionType)

            if rightAction != .none && availableActions.contains(rightAction) {
                actionButton(for: rightAction)
            }

            Spacer()
        }
        .background(Color.clear)
    }

    private func actionButton(for action: SwipeAction) -> some View {
        Button(action: {
            executeAction(action)
        }) {
            VStack {
                if action == .edit || action == .restock {
                    // 使用自定义图标
                    Image(action.icon)
                        .font(.title3)
                } else {
                    // 使用系统图标
                    Image(systemName: action.icon)
                        .font(.title3)
                }
                Text(action.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(.primaryBg)
            .frame(width: min(abs(offset), 75), height: nil)
            .padding(.vertical, 10)
            .background(action.color)
            .cornerRadius(8)
            .contentShape(Rectangle())
        }
    }

    private func executeLeftSwipeAction() {
        let leftAction = gestureSettings.getAction(for: listSectionType, direction: .left)
        // 检查操作是否在可用列表中
        let availableActions = SwipeAction.availableActions(for: listSectionType)

        if leftAction != .none && availableActions.contains(leftAction) {
            executeAction(leftAction)
        }
    }

    private func executeRightSwipeAction() {
        let rightAction = gestureSettings.getAction(for: listSectionType, direction: .right)
        // 检查操作是否在可用列表中
        let availableActions = SwipeAction.availableActions(for: listSectionType)

        if rightAction != .none && availableActions.contains(rightAction) {
            executeAction(rightAction)
        }
    }

    private func executeAction(_ action: SwipeAction) {
        // 检查操作是否在可用列表中
        let availableActions = SwipeAction.availableActions(for: listSectionType)
        guard availableActions.contains(action) else {
            return
        }

        switch action {
        case .delete:
            onDelete?()
        case .edit:
            onEdit?()
        case .compare:
            onCompare?()
        case .favorite:
            // 处理首选操作，如果需要可以添加回调函数
            break
        case .archive:
            // 处理归档操作，如果需要可以添加回调函数
            break
        case .copyRecipe:
            onCopyRecipe?()
        case .restock:
            onRestock?()
        case .quickBrew:
            // 处理速记操作，如果需要可以添加回调函数
            break
        case .portal:
            // 处理传送门操作，如果需要可以添加回调函数
            break
        case .tag:
            // 处理贴标签操作，如果需要可以添加回调函数
            break
        case .rename:
            // 处理重命名操作，如果需要可以添加回调函数
            break
        case .none:
            // 无操作
            break
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        SwipeableRowView {
            HStack {
                Image(systemName: "cup.and.saucer.fill")
                    .foregroundColor(.brown)

                VStack(alignment: .leading) {
                    Text("埃塞俄比亚耶加雪菲")
                        .font(.headline)
                    Text("日晒处理 - 2023-08-10")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }

                Spacer()
            }
            .padding()
            .background(Color.primaryBg)
        }
        .onDelete {
            print("删除动作")
        }
        .onEdit {
            print("修改动作")
        }
        .onCompare {
            print("对比动作")
        }
        .onCopyRecipe {
            print("复制配方动作")
        }
        .onRestock {
            print("回购动作")
        }
        .listSection(.brewLog)
    }
    .padding()
}
