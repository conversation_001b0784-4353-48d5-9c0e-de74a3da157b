import SwiftUI

struct PremiumFeatureWrapper<Content: View>: View {
    @StateObject private var subscriptionService = SubscriptionService.shared
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var showingSubscription = false
    let content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        ZStack {
            content
                .disabled(!subscriptionService.currentSubscriptionType.isPremium)
                .blur(radius: subscriptionService.currentSubscriptionType.isPremium ? 0 : 2)
                .environmentObject(themeManager)
            
            if !subscriptionService.currentSubscriptionType.isPremium {
                VStack {
                    Spacer()
                    Button(action: {
                        showingSubscription = true
                    }) {
                        HStack {
                            Image(systemName: "lock.fill")
                            Text("解锁高级功能")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.accentColor)
                        .foregroundColor(.primaryBg)
                        .cornerRadius(10)
                        .padding(.horizontal)
                    }
                }
                .padding(.bottom)
            }
        }
        .sheet(isPresented: $showingSubscription) {
            NavigationView {
                SubscriptionView()
                    .environmentObject(themeManager)
            }
        }
    }
} 
