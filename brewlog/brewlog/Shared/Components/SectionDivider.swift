import SwiftUI

/// 一个通用的章节标题分隔线组件，用于在视图中分隔不同的章节内容
struct SectionDivider: View {
    let title: String
    @Environment(\.colorScheme) var colorScheme // 添加对系统颜色方案的监听
    
    var body: some View {
        HStack(spacing: 8) {
            // 使用Rectangle替代Divider更可控
            Rectangle()
                .fill(Color.primaryText.opacity(0.2))
                .frame(height: 1)
                // 确保最小宽度，防止缩小到不可见
                .frame(minWidth: 20)
            
            Text(title)
                .font(.subheadline.weight(.medium))
                .foregroundColor(.primaryText.opacity(0.4))
                // 限制文本最大宽度，确保两侧线条有足够空间
                .fixedSize(horizontal: true, vertical: false)
            
            // 使用Rectangle替代Divider更可控
            Rectangle()
                .fill(Color.primaryText.opacity(0.2))
                .frame(height: 1)
                // 确保最小宽度，防止缩小到不可见
                .frame(minWidth: 20)
        }
        .padding(.vertical, 16)
        // 添加更明确的宽度约束
        .frame(maxWidth: .infinity)
    }
} 