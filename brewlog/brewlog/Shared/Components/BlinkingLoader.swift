import SwiftUI

/// 闪烁光标加载组件
/// 模拟闪烁的光标效果，用于加载状态的显示
struct BlinkingLoader: View {
    // 控制闪烁效果的状态
    @State private var isVisible = false
    
    // 自定义属性
    var color: Color = .primaryText
    var width: CGFloat = 12
    var height: CGFloat = 18
    var duration: Double = 2.0
    var showText: Bool = true
    var text: String = "加载中..."
    
    var body: some View {
        HStack(spacing: 6) {
            // 加载文本（可选）
            if showText {
                Text(text)
                    .foregroundColor(color)
                    .font(.system(size: height - 2))
                    .lineLimit(1)
            }
            
            // 闪烁的光标
            Rectangle()
                .fill(color)
                .frame(width: width, height: height)
                .cornerRadius(1)
                .opacity(isVisible ? 1.0 : 0.0)
                .animation(
                    Animation.easeInOut(duration: duration/2)
                        .repeatForever(autoreverses: true),
                    value: isVisible
                )
                .onAppear {
                    isVisible = true
                }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 20) {
        // 默认样式
        BlinkingLoader()
            .frame(height: 100)
            .background(Color.secondaryBg)
            .cornerRadius(8)
            
        // 自定义样式 - 无文本
        BlinkingLoader(
            color: .blue,
            width: 12,
            height: 24,
            duration: 1.0,
            showText: false
        )
        .frame(height: 100)
        .background(Color.secondaryBg)
        .cornerRadius(8)
        
        // 自定义样式 - 自定义文本
        BlinkingLoader(
            color: .green,
            width: 12,
            height: 16,
            duration: 2.0,
            text: "请等待..."
        )
        .frame(height: 100)
        .background(Color.secondaryBg)
        .cornerRadius(8)
    }
    .padding()
    .background(Color.primaryBg)
} 