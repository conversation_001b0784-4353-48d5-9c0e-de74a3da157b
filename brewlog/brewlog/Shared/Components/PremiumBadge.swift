import SwiftUI

struct PremiumBadge: View {
    var body: some View {
        Text("高级版")
            .font(.caption2)
            .fontWeight(.medium)
            .foregroundColor(.primaryBg)
            .padding(.horizontal, 6)
            .padding(.vertical, 3)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 105/255, green: 60/255, blue: 36/255),
                        Color(red: 212/255, green: 163/255, blue: 115/255)
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .clipShape(Capsule())
    }
}

#Preview {
    VStack(spacing: 20) {
        PremiumBadge()
        
        // 在列表项中的预览
        HStack {
            Label("测试项目", systemImage: "star")
            Spacer()
            PremiumBadge()
        }
        .padding()
        .background(Color(.systemBackground))
    }
    .padding()
} 
