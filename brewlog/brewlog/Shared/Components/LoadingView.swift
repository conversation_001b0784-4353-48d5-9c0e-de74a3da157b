import SwiftUI

/// 加载状态视图扩展
/// 提供统一的加载状态显示方法
extension View {
    /// 添加加载状态覆盖层
    /// - Parameters:
    ///   - isLoading: 是否正在加载
    ///   - text: 加载文本
    ///   - color: 加载指示器颜色
    /// - Returns: 添加了加载状态的视图
    func loading(
        _ isLoading: Bool,
        text: String = "加载中...",
        color: Color = .primaryText,
        blur: Bool = true
    ) -> some View {
        ZStack {
            self
                .disabled(isLoading)
                .blur(radius: isLoading && blur ? 2 : 0)
            
            if isLoading {
                BlinkingLoader(color: color, text: text)
                    .padding()
                    .background(Color.secondaryBg.opacity(0.8))
                    .cornerRadius(10)
                    .shadow(radius: 5)
            }
        }
    }
    
    /// 添加全屏加载状态
    /// - Parameters:
    ///   - isLoading: 是否正在加载
    ///   - text: 加载文本
    ///   - color: 加载指示器颜色
    ///   - showBackground: 是否显示背景、圆角和阴影
    /// - Returns: 添加了全屏加载状态的视图
    func loadingFullScreen(
        _ isLoading: Bool,
        text: String = "加载中...",
        color: Color = .primaryText,
        showBackground: Bool = true
    ) -> some View {
        ZStack {
            self
                .disabled(isLoading)

            if isLoading {
                Rectangle()
                    .fill(Color.black.opacity(0.4))
                    .ignoresSafeArea()

                if showBackground {
                    BlinkingLoader(color: color, text: text)
                        .padding()
                        .background(Color.secondaryBg)
                        .cornerRadius(10)
                        .shadow(radius: 5)
                } else {
                    BlinkingLoader(color: color, text: text)
                        .padding()
                }
            }
        }
    }
}

#Preview {
    VStack {
        Text("这是一个普通的视图")
            .padding()
            .loading(true)
        
        Text("这是一个全屏加载的视图")
            .padding()
            .loadingFullScreen(true, text: "正在处理...")
    }
} 