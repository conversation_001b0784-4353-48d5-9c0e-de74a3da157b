import SwiftUI
import UIKit

// 使用UIKit扩展，禁用特定UITextField的输入辅助工具栏
extension UITextField {
    open override var canBecomeFirstResponder: Bool {
        return true
    }
    
    open override func resignFirstResponder() -> <PERSON><PERSON> {
        return super.resignFirstResponder()
    }
    
    open override var inputAccessoryView: UIView? {
        return nil
    }
}

struct FlavorTagsInput: View {
    @Binding var selectedTags: [String]
    @State private var inputText = ""
    @State private var showSuggestions = false
    @StateObject private var viewModel = FlavorTagsViewModel()
    
    // 使用UIKit扩展禁用键盘工具栏的TextField
    struct HiddenToolbarTextField: UIViewRepresentable {
        @Binding var text: String
        var placeholder: String
        var onCommit: () -> Void
        var onChange: (String) -> Void
        
        func makeUIView(context: Context) -> UITextField {
            let textField = UITextField()
            textField.placeholder = placeholder
            textField.delegate = context.coordinator
            textField.borderStyle = .roundedRect
            textField.returnKeyType = .done
            textField.autocorrectionType = .no
            
            return textField
        }
        
        func updateUIView(_ uiView: UITextField, context: Context) {
            uiView.text = text
        }
        
        func makeCoordinator() -> Coordinator {
            Coordinator(self)
        }
        
        class Coordinator: NSObject, UITextFieldDelegate {
            let parent: HiddenToolbarTextField
            
            init(_ parent: HiddenToolbarTextField) {
                self.parent = parent
            }
            
            func textFieldDidChangeSelection(_ textField: UITextField) {
                parent.text = textField.text ?? ""
                parent.onChange(parent.text)
            }
            
            func textFieldShouldReturn(_ textField: UITextField) -> Bool {
                textField.resignFirstResponder()
                parent.onCommit()
                return true
            }
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            // 已选标签展示区域
            if !selectedTags.isEmpty {
                FlowLayout(spacing: 8) {
                    ForEach(selectedTags, id: \.self) { tag in
                        HStack(spacing: 4) {
                            Text(tag)
                                .font(.subheadline)
                            
                            Button(action: {
                                selectedTags.removeAll { $0 == tag }
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.horizontal, 10)
                        .padding(.vertical, 6)
                        .background(Color.primaryAccent.opacity(0.1))
                        .cornerRadius(15)
                    }
                }
            }

            // 标签输入框
            HStack {
                // 使用自定义TextField替换原生TextField
                HiddenToolbarTextField(
                    text: $inputText,
                    placeholder: "输入标签后，按下完成键添加",
                    onCommit: {
                        if !inputText.isEmpty {
                            addTag(inputText)
                        }
                    },
                    onChange: { newValue in
                        if !newValue.isEmpty {
                            viewModel.filterTags(newValue)
                            showSuggestions = true
                        } else {
                            showSuggestions = false
                        }
                    }
                )
                .frame(height: 36)

                if !inputText.isEmpty {
                    Button(action: {
                        inputText = ""
                        showSuggestions = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                    }
                }
            }
            
            // 下拉建议列表
            if showSuggestions && !viewModel.filteredTags.isEmpty {
                ScrollView {
                    VStack(alignment: .leading, spacing: 4) {
                        ForEach(viewModel.filteredTags, id: \.self) { tag in
                            Button(action: {
                                addTag(tag)
                            }) {
                                HStack {
                                    Text(tag)
                                        .foregroundColor(.primary)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                }
                                .padding(.vertical, 8)
                                .padding(.horizontal, 12)
                                .contentShape(Rectangle())
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                            if tag != viewModel.filteredTags.last {
                                Divider()
                            }
                        }
                    }
                    .padding(.vertical, 4)
                }
                .frame(maxHeight: 200)
                .background(Color.navbarBg)
                .cornerRadius(8)
            }
        }
        .onAppear {
            viewModel.loadTags()
        }
    }
    
    private func addTag(_ tag: String) {
        let trimmedTag = tag.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedTag.isEmpty && !selectedTags.contains(trimmedTag) {
            if viewModel.tagExists(trimmedTag) {
                selectedTags.append(trimmedTag)
            } else {
                // 如果是新标签，先创建
                viewModel.createTag(trimmedTag) { result in
                    switch result {
                    case .success(let tag):
                        selectedTags.append(tag.name)
                    case .failure(let error):
                        print("创建标签失败: \(error.localizedDescription)")
                    }
                }
            }
        }
        inputText = ""
        showSuggestions = false
    }
}

#if DEBUG
struct FlavorTagsInput_Previews: PreviewProvider {
    static var previews: some View {
        FlavorTagsInput(selectedTags: .constant([]))
            .padding()
    }
}
#endif 