import SwiftUI

/// FlowLayout 用于灵活布局视图，自动换行
struct FlowLayout: Layout {
    var alignment: Alignment = .center
    var spacing: CGFloat = 8
    
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let rows = computeRows(proposal: proposal, subviews: subviews)
        
        var height: CGFloat = 0
        for row in rows {
            height += row.height
        }
        
        height += spacing * CGFloat(max(0, rows.count - 1))
        
        return CGSize(width: proposal.width ?? 0, height: height)
    }
    
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let rows = computeRows(proposal: proposal, subviews: subviews)
        
        var y = bounds.minY
        
        for row in rows {
            var x = bounds.minX
            
            switch alignment {
            case .center:
                x = bounds.minX + (bounds.width - row.width) / 2
            case .trailing:
                x = bounds.minX + (bounds.width - row.width)
            default:
                break
            }
            
            for element in row.elements {
                let index = element.index
                subviews[index].place(
                    at: CGPoint(x: x, y: y),
                    proposal: ProposedViewSize(width: element.size.width, height: element.size.height)
                )
                x += element.size.width + spacing
            }
            
            y += row.height + spacing
        }
    }
    
    private func computeRows(proposal: ProposedViewSize, subviews: Subviews) -> [Row] {
        var rows: [Row] = []
        var currentRow = Row()
        let maxWidth = proposal.width ?? 0
        
        for (index, subview) in subviews.enumerated() {
            let size = subview.sizeThatFits(proposal)
            let element = Element(index: index, size: size)
            
            if currentRow.elements.isEmpty {
                currentRow.add(element)
            } else if currentRow.width + spacing + size.width <= maxWidth {
                currentRow.add(element, spacing: spacing)
            } else {
                rows.append(currentRow)
                currentRow = Row()
                currentRow.add(element)
            }
        }
        
        if !currentRow.elements.isEmpty {
            rows.append(currentRow)
        }
        
        return rows
    }
    
    private struct Element {
        let index: Int
        let size: CGSize
    }
    
    private struct Row {
        var elements: [Element] = []
        var width: CGFloat = 0
        var height: CGFloat = 0
        
        mutating func add(_ element: Element, spacing: CGFloat = 0) {
            if !elements.isEmpty {
                width += spacing
            }
            width += element.size.width
            height = max(height, element.size.height)
            elements.append(element)
        }
    }
} 