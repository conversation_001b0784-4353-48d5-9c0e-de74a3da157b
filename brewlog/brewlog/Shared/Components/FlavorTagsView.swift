import SwiftUI

struct FlavorTagsView: View {
    @Binding var selectedTags: [FlavorTag]
    @State private var showingTagPicker = false
    @StateObject private var viewModel = BrewLogViewModel()
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 已选标签
            FlowLayout(spacing: 8) {
                ForEach(selectedTags) { tag in
                    HStack {
                        Text(tag.name)
                        Button {
                            selectedTags.removeAll { $0.id == tag.id }
                        } label: {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.errorColor)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.accentColor.opacity(0.1))
                    .cornerRadius(15)
                }
            }
            .padding(.vertical, 4)
            
            // 添加标签按钮
            Button {
                showingTagPicker = true
            } label: {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("添加标签")
                }
            }
        }
        .sheet(isPresented: $showingTagPicker) {
            NavigationView {
                List {
                    ForEach(viewModel.flavorTags) { tag in
                        Button {
                            if !selectedTags.contains(where: { $0.id == tag.id }) {
                                selectedTags.append(tag)
                            }
                            showingTagPicker = false
                        } label: {
                            HStack {
                                Text(tag.name)
                                Spacer()
                                if selectedTags.contains(where: { $0.id == tag.id }) {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.accentColor)
                                }
                            }
                        }
                    }
                }
                .navigationTitle("选择风味标签")
                .navigationBarItems(trailing: Button("完成") {
                    showingTagPicker = false
                })
            }
        }
        .task {
            await viewModel.loadFlavorTags()
        }
    }
}

#if DEBUG
struct FlavorTagsView_Previews: PreviewProvider {
    static var previews: some View {
        FlavorTagsView(selectedTags: .constant([]))
            .padding()
    }
}
#endif 