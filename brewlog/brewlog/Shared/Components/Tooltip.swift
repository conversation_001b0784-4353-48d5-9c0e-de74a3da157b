import SwiftUI

// Tooltip方向枚举
enum TooltipDirection {
    case top
    case bottom
    case left
    case right
}

// Tooltip内容项模型
struct TooltipItem: Identifiable {
    let id = UUID().uuidString
    var icon: String? = nil
    let title: String
}

// 三角形指示器形状
struct Triangle: Shape {
    public func path(in rect: CGRect) -> Path {
        var path = Path()
        
        let topMiddle = CGPoint(x: rect.midX, y: rect.minY)
        let bottomLeft = CGPoint(x: rect.minX, y: rect.maxY)
        let bottomRight = CGPoint(x: rect.maxX, y: rect.maxY)
        
        path.move(to: topMiddle)
        path.addLine(to: bottomRight)
        path.addLine(to: bottomLeft)
        path.closeSubpath()
        
        return path
    }
}

// Tooltip组件
struct Tooltip: View {
    var items: [TooltipItem]
    var type: TooltipDirection
    var bgColor: Color = Color.primaryText
    var textColor: Color = .primaryBg
    var containerWidth: CGFloat?  // 被提示容器的宽度
    
    // 设置最大宽度，最大不超过250，最小不小于被提示容器宽度
    private var maxWidth: CGFloat {
        if let containerWidth = containerWidth {
            return min(max(containerWidth, 100), 250)
        }
        return 250
    }
    
    public var body: some View {
        Group {
            if type == .left || type == .right {
                HStack(spacing: 0) {
                    if type == .right {
                        triangleView()
                    }
                    
                    // 主体内容
                    tooltipContent()
                    
                    if type == .left {
                        triangleView()
                    }
                }
            } else {
                VStack(spacing: 0) {
                    if type == .bottom {
                        triangleView()
                    }
                    
                    // 主体内容
                    tooltipContent()
                    
                    if type == .top {
                        triangleView()
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: alignment())
    }
    
    private func tooltipContent() -> some View {
        VStack(spacing: 8) {
            ForEach(items, id: \.id) { item in
                TooltipItemView(item: item)
            }
        }
        .padding(10)
        .background(bgColor)
        .cornerRadius(8)
    }
    
    private func TooltipItemView(item: TooltipItem) -> some View {
        HStack(alignment: .top, spacing: 4) {
            if let icon = item.icon {
                Image(icon)
                    .resizable()
                    .frame(width: 16, height: 16)
            }
            
            Text(item.title)
                .font(.system(size: 14, weight: .medium))
                .foregroundStyle(textColor)
                .fixedSize(horizontal: false, vertical: true) // 允许文本换行
        }
        .frame(maxWidth: maxWidth)
    }
    
    private func triangleView() -> some View {
        Group {
            switch type {
            case .top:
                Triangle()
                    .fill(bgColor)
                    .frame(width: 16, height: 8)
                    .rotationEffect(.degrees(180))
            case .bottom:
                Triangle()
                    .fill(bgColor)
                    .frame(width: 16, height: 8)
            case .left:
                Triangle()
                    .fill(bgColor)
                    .frame(width: 8, height: 16)
                    .rotationEffect(.degrees(90))
            case .right:
                Triangle()
                    .fill(bgColor)
                    .frame(width: 8, height: 16)
                    .rotationEffect(.degrees(-90))
            }
        }
    }
    
    private func alignment() -> Alignment {
        switch type {
        case .top:
            return .top
        case .bottom:
            return .bottom
        case .left:
            return .leading
        case .right:
            return .trailing
        }
    }
}

// 适用于RoastLevelIndicator的可控Tooltip容器
struct TooltipContainer<Content: View>: View {
    @State private var showTooltip = false
    @State private var contentSize: CGSize = .zero
    
    let content: Content
    let tooltipItems: [TooltipItem]
    let direction: TooltipDirection
    let dismissAfter: Double?
    var bgColor: Color = Color.primaryText
    var textColor: Color = .primaryBg
    
    init(tooltipItems: [TooltipItem], 
         direction: TooltipDirection = .top, 
         dismissAfter: Double? = nil,
         bgColor: Color = Color.primaryText,
         textColor: Color = .primaryBg,
         @ViewBuilder content: () -> Content) {
        self.tooltipItems = tooltipItems
        self.direction = direction
        self.dismissAfter = dismissAfter
        self.bgColor = bgColor
        self.textColor = textColor
        self.content = content()
    }
    
    var body: some View {
        content
            .background(
                GeometryReader { geo in
                    Color.clear
                        .onAppear {
                            self.contentSize = geo.size
                        }
                }
            )
            .onTapGesture {
                withAnimation(.easeInOut(duration: 0.2)) {
                    showTooltip.toggle()
                    
                    if let dismissTime = dismissAfter, showTooltip {
                        DispatchQueue.main.asyncAfter(deadline: .now() + dismissTime) {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                showTooltip = false
                            }
                        }
                    }
                }
            }
            .overlay(
                ZStack {
                    if showTooltip {
                        tooltipPositioned()
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .allowsHitTesting(showTooltip)
                .animation(.easeInOut(duration: 0.2), value: showTooltip)
            )
    }
    
    @ViewBuilder
    private func tooltipPositioned() -> some View {
        Tooltip(
            items: tooltipItems, 
            type: direction, 
            bgColor: bgColor,
            textColor: textColor,
            containerWidth: contentSize.width
        )
        .zIndex(999)
        .transition(.opacity)
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.2)) {
                showTooltip = false
            }
        }
        .offset(offsetForDirection())
    }
    
    private func offsetForDirection() -> CGSize {
        switch direction {
        case .top:
            return CGSize(width: 0, height: -contentSize.height/2 - 30)
        case .bottom:
            return CGSize(width: 0, height: contentSize.height/2 + 30)
        case .left:
            return CGSize(width: -contentSize.width/2 - 30, height: 0)
        case .right:
            return CGSize(width: contentSize.width/2 + 30, height: 0)
        }
    }
}

// 预览
struct Tooltip_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            HStack(spacing: 20) {
                Tooltip(items: [TooltipItem(title: "提示信息")], type: .top)
                Tooltip(items: [TooltipItem(title: "底部提示")], type: .bottom)
            }
            
            HStack(spacing: 20) {
                Tooltip(items: [TooltipItem(title: "左侧提示")], type: .left)
                Tooltip(items: [TooltipItem(title: "右侧提示")], type: .right)
            }
            
            Tooltip(items: [
                TooltipItem(icon: "ic_Heart", title: "图标提示很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长")
            ], type: .top)
            
            TooltipContainer(
                tooltipItems: [TooltipItem(title: "点击查看提示")],
                direction: .top,
                dismissAfter: 2.0
            ) {
                Text("点击我")
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.primaryBg)
                    .cornerRadius(8)
            }
        }
        .padding(50)
    }
}
