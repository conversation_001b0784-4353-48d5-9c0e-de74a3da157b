import SwiftUI

struct StatisticCard: View {
    let title: String
    let value: String
    let icon: String
    var color: Color = .accentColor
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Text(title)
                    .foregroundColor(.secondary)
            }
            .font(.subheadline)
            
            Text(value)
                .font(.title2)
                .fontWeight(.semibold)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
}

#if DEBUG
struct StatisticCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            StatisticCard(
                title: "总冲煮",
                value: "123",
                icon: "cup.and.saucer.fill"
            )
            
            StatisticCard(
                title: "平均评分",
                value: "4.5",
                icon: "star.fill",
                color: .yellow
            )
        }
        .padding()
    }
}
#endif 