import SwiftUI
import UIKit

// 定义一个键盘工具栏隐藏包装器
public struct HideKeyboardToolbar<T: View>: ViewModifier {
    let content: T
    
    public init(@ViewBuilder content: () -> T) {
        self.content = content()
    }
    
    public func body(content: Content) -> some View {
        ZStack {
            content
            // 这个透明的TextField会捕获焦点，但没有工具栏
            TextField("", text: .constant(""))
                .frame(width: 0, height: 0)
                .opacity(0)
        }
    }
}

public extension View {
    func hideKeyboardToolbar() -> some View {
        modifier(HideKeyboardToolbar { self })
    }
} 