import SwiftUI

// MARK: - 简化的标签数据结构
struct SimpleTag: Identifiable {
    let id: Int
    let name: String
}

// MARK: - 标签流式布局组件
struct TagFlowView: View {
    let tags: [SimpleTag]
    let maxLines: Int

    var body: some View {
        if maxLines == 1 {
            // 单行模式：使用 HStack 确保不换行
            singleLineView
        } else {
            // 多行模式：使用简化的流式布局
            multiLineView
        }
    }
    
    // 单行视图
    private var singleLineView: some View {
        GeometryReader { geometry in
            HStack(spacing: 8) {
                // 计算可以显示的标签
                let visibleTags = calculateVisibleTags(availableWidth: geometry.size.width)
                
                ForEach(visibleTags) { tag in
                    createTagView(for: tag)
                }
                
                // 如果有剩余标签，显示 +n 指示器
                if visibleTags.count < tags.count {
                    let remainingCount = tags.count - visibleTags.count
                    Text("+\(remainingCount)")
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.secondary.opacity(0.1))
                        .cornerRadius(8)
                }
                
                Spacer(minLength: 0)
            }
        }
        .frame(height: 28) // 固定高度，防止换行
    }
    
    // 多行视图
    private var multiLineView: some View {
        VStack(alignment: .leading, spacing: 8) {
            let chunkedTags = tags.chunked(into: 3) // 每行最多3个标签
            ForEach(Array(chunkedTags.enumerated()), id: \.offset) { lineIndex, lineTags in
                if lineIndex < maxLines {
                    HStack(spacing: 8) {
                        ForEach(lineTags) { tag in
                            createTagView(for: tag)
                        }
                        Spacer()
                    }
                }
            }
        }
    }
    
    // 计算可见标签
    private func calculateVisibleTags(availableWidth: CGFloat) -> [SimpleTag] {
        var visibleTags: [SimpleTag] = []
        var currentWidth: CGFloat = 0
        let spacing: CGFloat = 8
        let moreTagWidth: CGFloat = 40 // "+n" 标签的估算宽度
        
        for (index, tag) in tags.enumerated() {
            let tagWidth = estimateTagWidth(for: tag)
            let needsMoreTag = index < tags.count - 1 // 还有剩余标签
            
            // 检查是否还能放下当前标签
            let requiredWidth = currentWidth + tagWidth + (needsMoreTag ? spacing + moreTagWidth : 0)
            
            if requiredWidth > availableWidth && !visibleTags.isEmpty {
                break
            }
            
            visibleTags.append(tag)
            currentWidth += tagWidth + (index > 0 ? spacing : 0)
        }
        
        return visibleTags
    }
    
    // 估算标签宽度
    private func estimateTagWidth(for tag: SimpleTag) -> CGFloat {
        // 简化估算：根据文字长度估算标签宽度
        return CGFloat(tag.name.count * 8 + 16) // 大概估算
    }
    
    // 创建标签视图
    private func createTagView(for tag: SimpleTag) -> some View {
        Text(tag.name)
            .font(.caption2)
            .fontWeight(.medium)
            .foregroundColor(.secondary)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(8)
    }
}

// MARK: - TagFlowView 的便利初始化器，用于兼容 RecipeTag
extension TagFlowView {
    init(recipeTags: [Any], maxLines: Int) {
        // 将 RecipeTag 转换为 SimpleTag
        let simpleTags = recipeTags.enumerated().map { index, tag in
            // 使用反射来获取 tag 的属性
            let mirror = Mirror(reflecting: tag)
            let id = mirror.children.first { $0.label == "id" }?.value as? Int ?? index
            let name = mirror.children.first { $0.label == "name" }?.value as? String ?? "Unknown"
            return SimpleTag(id: id, name: name)
        }
        
        self.tags = simpleTags
        self.maxLines = maxLines
    }
}

// MARK: - Array 扩展用于分块
extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}
