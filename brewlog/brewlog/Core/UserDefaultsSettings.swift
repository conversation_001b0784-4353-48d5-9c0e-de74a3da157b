import Foundation

/// 使用UserDefaults管理用户设置
class UserDefaultsSettings {
    static let shared = UserDefaultsSettings()
    
    private let defaults = UserDefaults.standard
    
    // 键名定义
    private enum Keys {
        static let defaultSortOrder = "defaultSortOrder"
        static let lastLoginDate = "lastLoginDate"
        static let themeMode = "themeMode"
        static let autoSyncEnabled = "autoSyncEnabled"
        static let notificationsEnabled = "notificationsEnabled"
    }
    
    // 记录排序顺序
    var defaultSortOrder: String? {
        get {
            return defaults.string(forKey: Keys.defaultSortOrder)
        }
        set {
            defaults.set(newValue, forKey: Keys.defaultSortOrder)
        }
    }
    
    // 上次登录日期
    var lastLoginDate: Date? {
        get {
            return defaults.object(forKey: Keys.lastLoginDate) as? Date
        }
        set {
            defaults.set(newValue, forKey: Keys.lastLoginDate)
        }
    }
    
    // 主题模式（浅色/深色/系统）
    var themeMode: String {
        get {
            return defaults.string(forKey: Keys.themeMode) ?? "system"
        }
        set {
            defaults.set(newValue, forKey: Keys.themeMode)
        }
    }
    
    // 自动同步
    var autoSyncEnabled: Bool {
        get {
            return defaults.bool(forKey: Keys.autoSyncEnabled)
        }
        set {
            defaults.set(newValue, forKey: Keys.autoSyncEnabled)
        }
    }
    
    // 通知
    var notificationsEnabled: Bool {
        get {
            return defaults.bool(forKey: Keys.notificationsEnabled)
        }
        set {
            defaults.set(newValue, forKey: Keys.notificationsEnabled)
        }
    }
    
    // 清除所有设置
    func clearAllSettings() {
        let domain = Bundle.main.bundleIdentifier!
        defaults.removePersistentDomain(forName: domain)
    }
} 