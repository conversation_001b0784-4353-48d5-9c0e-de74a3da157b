import Foundation
import Security

/// 密码生成器工具类
/// 用于生成符合django-allauth要求的复杂密码
class PasswordGenerator {
    
    /// 密码字符集
    private struct CharacterSets {
        static let lowercase = "abcdefghijklmnopqrstuvwxyz"
        static let uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        static let numbers = "0123456789"
        static let symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        
        // 避免容易混淆的字符
        static let safeLowercase = "abcdefghijkmnpqrstuvwxyz" // 去掉 l, o
        static let safeUppercase = "ABCDEFGHJKLMNPQRSTUVWXYZ" // 去掉 I, O
        static let safeNumbers = "23456789" // 去掉 0, 1
        static let safeSymbols = "!@#$%^&*()_+-=[]{}|;:,.<>?" // 保持原样
    }
    
    /// 密码强度等级
    enum PasswordStrength: CaseIterable {
        case medium
        case strong
        case veryStrong
        
        var length: Int {
            switch self {
            case .medium: return 12
            case .strong: return 16
            case .veryStrong: return 20
            }
        }
        
        var description: String {
            switch self {
            case .medium: return "中等强度 (12位)"
            case .strong: return "强密码 (16位)"
            case .veryStrong: return "超强密码 (20位)"
            }
        }
    }
    
    /// 生成安全的随机密码
    /// - Parameters:
    ///   - strength: 密码强度等级
    ///   - useSafeCharacters: 是否使用安全字符集（避免混淆字符）
    /// - Returns: 生成的密码
    static func generatePassword(strength: PasswordStrength = .strong, useSafeCharacters: Bool = true) -> String {
        let length = strength.length
        
        // 选择字符集
        let lowercase = useSafeCharacters ? CharacterSets.safeLowercase : CharacterSets.lowercase
        let uppercase = useSafeCharacters ? CharacterSets.safeUppercase : CharacterSets.uppercase
        let numbers = useSafeCharacters ? CharacterSets.safeNumbers : CharacterSets.numbers
        let symbols = CharacterSets.safeSymbols
        
        // 确保密码包含每种类型的字符
        var password = ""
        
        // 至少包含一个小写字母
        password += String(lowercase.randomElement()!)
        
        // 至少包含一个大写字母
        password += String(uppercase.randomElement()!)
        
        // 至少包含一个数字
        password += String(numbers.randomElement()!)
        
        // 至少包含一个符号
        password += String(symbols.randomElement()!)
        
        // 填充剩余长度
        let allCharacters = lowercase + uppercase + numbers + symbols
        for _ in 4..<length {
            password += String(allCharacters.randomElement()!)
        }
        
        // 打乱密码字符顺序
        return String(password.shuffled())
    }
    
    /// 验证密码强度
    /// - Parameter password: 要验证的密码
    /// - Returns: 密码强度评分 (0-100)
    static func validatePasswordStrength(_ password: String) -> Int {
        var score = 0
        
        // 长度评分 (最多30分)
        if password.count >= 8 {
            score += min(password.count * 2, 30)
        }
        
        // 字符类型评分
        var hasLowercase = false
        var hasUppercase = false
        var hasNumbers = false
        var hasSymbols = false
        
        for char in password {
            if char.isLowercase {
                hasLowercase = true
            } else if char.isUppercase {
                hasUppercase = true
            } else if char.isNumber {
                hasNumbers = true
            } else {
                hasSymbols = true
            }
        }
        
        // 每种字符类型15分
        if hasLowercase { score += 15 }
        if hasUppercase { score += 15 }
        if hasNumbers { score += 15 }
        if hasSymbols { score += 15 }
        
        // 复杂度加分
        let uniqueChars = Set(password).count
        if uniqueChars >= password.count * 3 / 4 {
            score += 10 // 字符多样性加分
        }
        
        return min(score, 100)
    }
    
    /// 检查密码是否符合django-allauth的要求
    /// - Parameter password: 要检查的密码
    /// - Returns: 是否符合要求
    static func isValidForDjangoAllauth(_ password: String) -> Bool {
        // django-allauth 默认要求：
        // 1. 至少8个字符
        // 2. 不能是纯数字
        // 3. 不能是常见密码
        // 4. 不能与用户信息太相似
        
        guard password.count >= 8 else { return false }
        
        // 检查是否为纯数字
        let isAllNumbers = password.allSatisfy { $0.isNumber }
        guard !isAllNumbers else { return false }
        
        // 检查是否包含字母和数字
        let hasLetters = password.contains { $0.isLetter }
        let hasNumbers = password.contains { $0.isNumber }
        
        return hasLetters && hasNumbers
    }
    
    /// 生成符合django-allauth要求的密码
    /// - Parameter strength: 密码强度
    /// - Returns: 符合要求的密码
    static func generateDjangoAllauthPassword(strength: PasswordStrength = .strong) -> String {
        var password: String
        var attempts = 0
        
        repeat {
            password = generatePassword(strength: strength)
            attempts += 1
        } while !isValidForDjangoAllauth(password) && attempts < 10
        
        return password
    }
    
    /// 获取密码强度描述
    /// - Parameter score: 密码强度评分
    /// - Returns: 强度描述和颜色
    static func getStrengthDescription(score: Int) -> (description: String, color: String) {
        switch score {
        case 0..<30:
            return ("弱", "red")
        case 30..<60:
            return ("中等", "orange")
        case 60..<80:
            return ("强", "blue")
        case 80...100:
            return ("很强", "green")
        default:
            return ("未知", "gray")
        }
    }
}

// MARK: - 扩展方法
extension PasswordGenerator {
    
    /// 生成多个密码选项供用户选择
    /// - Parameters:
    ///   - count: 生成密码的数量
    ///   - strength: 密码强度
    /// - Returns: 密码数组
    static func generateMultiplePasswords(count: Int = 3, strength: PasswordStrength = .strong) -> [String] {
        var passwords: [String] = []
        
        for _ in 0..<count {
            let password = generateDjangoAllauthPassword(strength: strength)
            passwords.append(password)
        }
        
        return passwords
    }
    
    /// 生成记忆友好的密码（使用单词组合）
    /// - Parameter includeNumbers: 是否包含数字
    /// - Returns: 记忆友好的密码
    static func generateMemorablePassword(includeNumbers: Bool = true) -> String {
        // 简单的单词列表（可以扩展）
        let adjectives = ["Quick", "Bright", "Happy", "Strong", "Smart", "Cool", "Fast", "Bold"]
        let nouns = ["Tiger", "Eagle", "River", "Mountain", "Ocean", "Forest", "Star", "Moon"]
        let symbols = ["!", "@", "#", "$", "%", "^", "&", "*"]
        
        let adjective = adjectives.randomElement()!
        let noun = nouns.randomElement()!
        let symbol = symbols.randomElement()!
        
        var password = adjective + noun
        
        if includeNumbers {
            let number = Int.random(in: 10...99)
            password += String(number)
        }
        
        password += symbol
        
        return password
    }
}
