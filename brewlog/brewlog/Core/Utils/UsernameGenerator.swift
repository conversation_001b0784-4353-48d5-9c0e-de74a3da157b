import Foundation

/// 用户名生成器工具类
/// 生成与咖啡相关的简短用户名
class UsernameGenerator {
    
    /// 咖啡相关的词汇组合
    private struct CoffeeTerms {
        // 咖啡豆产地（扩展）
        static let origins = ["eth", "ken", "col", "bra", "gua", "jam", "yem", "pan", "cos", "hon", "nic", "per", "bol", "ecu", "ven", "mex", "ind", "vie", "tha", "mya", "png", "haw", "rwa", "bur", "tan", "uga", "mal", "zim", "mad", "reu"]

        // 咖啡处理法（扩展）
        static let processes = ["wash", "nat", "honey", "wet", "dry", "semi", "carb", "anaer", "yeast", "lacto", "wine", "pulp", "black", "white", "yellow", "red", "orange", "mosaic"]

        // 咖啡器具（扩展）
        static let tools = ["v60", "aero", "chemex", "kalita", "hario", "clever", "french", "moka", "siphon", "cold", "espro", "bodum", "origami", "april", "fellow", "timemore", "comandante", "baratza", "eureka", "mazzer"]

        // 咖啡风味（扩展）
        static let flavors = ["berry", "citrus", "choco", "caramel", "floral", "nutty", "fruity", "sweet", "spicy", "herbal", "wine", "tropical", "stone", "vanilla", "honey", "maple", "toffee", "almond", "hazel", "peach", "apple", "grape", "cherry", "plum", "orange", "lemon", "lime", "grapefruit", "blackberry", "blueberry", "strawberry", "raspberry", "mango", "pineapple", "coconut", "banana", "papaya", "passion", "lychee", "jasmine", "rose", "lavender", "mint", "basil", "thyme", "sage", "cedar", "pine", "oak", "smoke", "earth", "mineral", "clay", "leather", "tobacco", "dark", "milk", "white", "bitter", "sour", "umami"]

        // 咖啡术语（扩展）
        static let terms = ["brew", "roast", "grind", "pour", "drip", "shot", "crema", "bloom", "extract", "dose", "yield", "ratio", "temp", "time", "flow", "pressure", "steam", "foam", "latte", "cappa", "flat", "cortado", "macchiato", "affogato", "lungo", "ristretto", "doppio", "single", "double", "triple", "quad", "decaf", "regular", "light", "medium", "dark", "city", "full", "french", "italian", "vienna", "espresso", "filter", "immersion", "percolation"]

        // 数字后缀（扩展）
        static let numbers = ["01", "02", "03", "07", "09", "12", "17", "19", "23", "27", "42", "88", "99", "13", "21", "24", "25", "26", "28", "29", "31", "33", "37", "41", "43", "47", "51", "53", "57", "61", "67", "71", "73", "77", "79", "83", "89", "91", "97", "101", "111", "123", "234", "345", "456", "567", "678", "789", "987", "876", "765", "654", "543", "432", "321", "2024", "2025", "2026"]

        // 特殊字符（可选）
        static let separators = ["", "_", ".", "-"]

        // 年份
        static let years = ["20", "21", "22", "23", "24", "25", "26"]
    }
    
    /// 生成咖啡相关的用户名
    /// - Parameter count: 生成用户名的数量
    /// - Returns: 用户名数组
    static func generateCoffeeUsernames(count: Int = 3) -> [String] {
        var usernames: [String] = []
        var attempts = 0
        let maxAttempts = count * 10 // 避免无限循环
        
        while usernames.count < count && attempts < maxAttempts {
            let username = generateSingleUsername()
            
            // 确保用户名长度至少5个字符且不重复
            if username.count >= 5 && !usernames.contains(username) {
                usernames.append(username)
            }
            
            attempts += 1
        }
        
        return usernames
    }
    
    /// 生成单个用户名
    /// - Returns: 生成的用户名
    private static func generateSingleUsername() -> String {
        let patterns = [
            // 基础二元组合
            { CoffeeTerms.tools.randomElement()! + CoffeeTerms.flavors.randomElement()! },
            { CoffeeTerms.origins.randomElement()! + CoffeeTerms.processes.randomElement()! },
            { CoffeeTerms.terms.randomElement()! + CoffeeTerms.numbers.randomElement()! },
            { CoffeeTerms.flavors.randomElement()! + CoffeeTerms.numbers.randomElement()! },
            { CoffeeTerms.tools.randomElement()! + CoffeeTerms.numbers.randomElement()! },
            { CoffeeTerms.origins.randomElement()! + CoffeeTerms.flavors.randomElement()! },
            { CoffeeTerms.terms.randomElement()! + CoffeeTerms.tools.randomElement()! },

            // 三元组合
            { CoffeeTerms.origins.randomElement()! + CoffeeTerms.tools.randomElement()! + CoffeeTerms.numbers.randomElement()! },
            { CoffeeTerms.flavors.randomElement()! + CoffeeTerms.processes.randomElement()! + CoffeeTerms.numbers.randomElement()! },
            { CoffeeTerms.terms.randomElement()! + CoffeeTerms.flavors.randomElement()! + CoffeeTerms.years.randomElement()! },

            // 带分隔符的组合
            { CoffeeTerms.tools.randomElement()! + CoffeeTerms.separators.randomElement()! + CoffeeTerms.flavors.randomElement()! },
            { CoffeeTerms.origins.randomElement()! + CoffeeTerms.separators.randomElement()! + CoffeeTerms.processes.randomElement()! },
            { CoffeeTerms.terms.randomElement()! + CoffeeTerms.separators.randomElement()! + CoffeeTerms.numbers.randomElement()! },

            // 复杂四元组合
            { CoffeeTerms.origins.randomElement()! + CoffeeTerms.tools.randomElement()! + CoffeeTerms.flavors.randomElement()! + CoffeeTerms.numbers.randomElement()! },
            { CoffeeTerms.terms.randomElement()! + CoffeeTerms.processes.randomElement()! + CoffeeTerms.tools.randomElement()! + CoffeeTerms.years.randomElement()! },

            // 年份组合
            { CoffeeTerms.flavors.randomElement()! + CoffeeTerms.years.randomElement()! + CoffeeTerms.numbers.randomElement()! },
            { CoffeeTerms.tools.randomElement()! + CoffeeTerms.years.randomElement()! },

            // 特殊组合（更长但更独特）
            { CoffeeTerms.origins.randomElement()! + CoffeeTerms.processes.randomElement()! + CoffeeTerms.tools.randomElement()! },
            { CoffeeTerms.flavors.randomElement()! + CoffeeTerms.terms.randomElement()! + CoffeeTerms.numbers.randomElement()! },
            { CoffeeTerms.tools.randomElement()! + CoffeeTerms.flavors.randomElement()! + CoffeeTerms.processes.randomElement()! },

            // 随机长度组合
            { generateRandomLengthUsername() }
        ]

        let selectedPattern = patterns.randomElement()!
        return selectedPattern()
    }

    /// 生成随机长度的用户名
    /// - Returns: 随机组合的用户名
    private static func generateRandomLengthUsername() -> String {
        let components = [
            CoffeeTerms.origins.randomElement()!,
            CoffeeTerms.processes.randomElement()!,
            CoffeeTerms.tools.randomElement()!,
            CoffeeTerms.flavors.randomElement()!,
            CoffeeTerms.terms.randomElement()!,
            CoffeeTerms.numbers.randomElement()!,
            CoffeeTerms.years.randomElement()!
        ]

        // 随机选择2-4个组件
        let componentCount = Int.random(in: 2...4)
        let selectedComponents = components.shuffled().prefix(componentCount)

        // 随机决定是否使用分隔符
        let useSeparator = Bool.random()
        let separator = useSeparator ? CoffeeTerms.separators.randomElement()! : ""

        return selectedComponents.joined(separator: separator)
    }
    
    /// 验证用户名是否符合要求
    /// - Parameter username: 要验证的用户名
    /// - Returns: 是否符合要求
    static func isValidUsername(_ username: String) -> Bool {
        // 检查长度（至少5个字符）
        guard username.count >= 5 else { return false }
        
        // 检查字符（只允许字母和数字）
        let allowedCharacters = CharacterSet.alphanumerics
        let usernameCharacterSet = CharacterSet(charactersIn: username)
        guard allowedCharacters.isSuperset(of: usernameCharacterSet) else { return false }
        
        // 检查是否以字母开头
        guard let firstChar = username.first, firstChar.isLetter else { return false }
        
        return true
    }
    
    /// 获取用户名建议
    /// - Returns: 用户名使用建议
    static func getUsernameGuidelines() -> String {
        return "用户名至少5个字符，只能包含字母和数字，必须以字母开头"
    }
    
    /// 生成带有咖啡主题的用户名建议
    /// - Returns: 用户名建议文本
    static func getCoffeeUsernameHint() -> String {
        let examples = ["v60berry", "ethwash", "brew23", "chemexhoney"]
        let randomExample = examples.randomElement()!
        return "建议使用咖啡相关的用户名，如：\(randomExample)"
    }
}

// MARK: - 扩展方法
extension UsernameGenerator {
    
    /// 检查用户名是否可能与咖啡相关
    /// - Parameter username: 要检查的用户名
    /// - Returns: 是否与咖啡相关
    static func isCoffeeRelated(_ username: String) -> Bool {
        let lowercaseUsername = username.lowercased()
        
        let allTerms = CoffeeTerms.origins + CoffeeTerms.processes + CoffeeTerms.tools + 
                      CoffeeTerms.flavors + CoffeeTerms.terms
        
        return allTerms.contains { lowercaseUsername.contains($0) }
    }
    
    /// 生成基于现有用户名的变体
    /// - Parameter baseUsername: 基础用户名
    /// - Returns: 变体用户名数组
    static func generateVariants(for baseUsername: String) -> [String] {
        var variants: [String] = []
        
        // 添加数字后缀
        for number in CoffeeTerms.numbers.prefix(3) {
            let variant = baseUsername + number
            if variant.count >= 5 && variant.count <= 15 {
                variants.append(variant)
            }
        }
        
        // 如果基础用户名太短，添加咖啡术语
        if baseUsername.count < 8 {
            for term in CoffeeTerms.terms.prefix(2) {
                let variant = baseUsername + term
                if variant.count >= 5 && variant.count <= 15 {
                    variants.append(variant)
                }
            }
        }
        
        return Array(variants.prefix(3)) // 最多返回3个变体
    }
}
