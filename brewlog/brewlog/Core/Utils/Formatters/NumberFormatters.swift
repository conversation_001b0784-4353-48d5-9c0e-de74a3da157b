import Foundation

/// 通用的数字格式化工具
struct NumberFormatters {
    
    /// 格式化数字，移除多余的小数位零
    /// - Parameters:
    ///   - number: 要格式化的数字
    ///   - precision: 最大小数位数
    /// - Returns: 格式化后的字符串，去除尾部多余的0
    static func formatWithPrecision(_ number: Double, precision: Int) -> String {
        // 先格式化为指定小数位数
        let format = "%.\(precision)f"
        let formattedValue = String(format: format, number)
        
        // 处理尾部的零
        var result = formattedValue
        if result.contains(".") {
            // 移除所有尾部的0
            while result.hasSuffix("0") {
                result.removeLast()
            }
            // 如果只剩下小数点，也移除
            if result.hasSuffix(".") {
                result.removeLast()
            }
        }
        
        return result
    }
    
    /// 格式化为重量值，添加单位g
    /// - Parameters:
    ///   - weight: 重量（克）
    ///   - precision: 精度（小数位数）
    /// - Returns: 带单位的重量字符串
    static func formatWeight(_ weight: Double, precision: Int = 2) -> String {
        return formatWithPrecision(weight, precision: precision) + "g"
    }
    
    /// 格式化为粉水比
    /// - Parameters:
    ///   - ratio: 粉水比值
    ///   - precision: 精度（小数位数）
    /// - Returns: 格式化后的粉水比字符串，格式为"1:X"
    static func formatBrewRatio(_ ratio: Double, precision: Int = 1) -> String {
        return "1:" + formatWithPrecision(ratio, precision: precision)
    }
    
    /// 格式化温度
    /// - Parameters:
    ///   - temperature: 温度值
    ///   - precision: 精度（小数位数）
    /// - Returns: 格式化后的温度字符串
    static func formatTemperature(_ temperature: Double, precision: Int = 1) -> String {
        return formatWithPrecision(temperature, precision: precision)
    }
    
    /// 格式化萃取时间
    /// - Parameter duration: 时间间隔（秒）
    /// - Returns: 格式化后的时间字符串（小时分钟秒）
    static func formatBrewingTime(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        let seconds = Int(duration) % 60
        
        var result = ""
        if hours > 0 {
            result += "\(hours)小时"
        }
        if minutes > 0 {
            // 当只有分钟时使用"分钟"，否则使用"分"
            if hours == 0 && seconds == 0 {
                result += "\(minutes)分钟"
            } else {
                result += "\(minutes)分"
            }
        }
        if seconds > 0 {
            result += "\(seconds)秒"
        }
        
        return result.isEmpty ? "0秒" : result
    }
    
    /// 格式化时间间隔为人性化可读格式
    /// - Parameter seconds: 时间间隔（秒）
    /// - Returns: 格式化后的时间差字符串（年、天、小时、分钟、秒）
    static func formatTimeInterval(_ seconds: Double) -> String {
        let totalSeconds = abs(seconds)
        let totalDays = Int(totalSeconds / 86400) // 一天有86400秒
        
        // 计算年和剩余天数（使用365天作为一年）
        let years = totalDays / 365
        let days = totalDays % 365
        
        let hours = Int(totalSeconds / 3600) % 24
        let minutes = Int(totalSeconds.truncatingRemainder(dividingBy: 3600) / 60)
        let remainingSeconds = Int(totalSeconds.truncatingRemainder(dividingBy: 60))
        
        // 构建人性化的时间差描述
        if years > 0 {
            if days > 0 {
                return "\(years)年\(days)天"
            }
            return "\(years)年"
        } else if days > 0 {
            if hours > 0 {
                return "\(days)天\(hours)小时"
            }
            return "\(days)天"
        } else if hours > 0 {
            if minutes > 0 {
                return "\(hours)小时\(minutes)分钟"
            }
            return "\(hours)小时"
        } else if minutes > 0 {
            if remainingSeconds > 0 {
                return "\(minutes)分钟\(remainingSeconds)秒"
            }
            return "\(minutes)分钟"
        } else {
            return "\(remainingSeconds)秒"
        }
    }
}