import Foundation

enum TimeUtils {
    static func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%d:%02d", minutes, remainingSeconds)
    }
    
    static func parseTime(_ timeString: String) -> Int? {
        let components = timeString.split(separator: ":")
        guard components.count == 2,
              let minutes = Int(components[0]),
              let seconds = Int(components[1]),
              seconds < 60
        else {
            return nil
        }
        return minutes * 60 + seconds
    }
    
    /// 格式化热点时刻显示
    /// - Parameter timeStr: 时间字符串 (格式: "HH:00") 或整数小时
    /// - Returns: 格式化后的时间字符串 (格式: "早上7点")
    static func formatPeakTime(_ timeStr: String?) -> String? {
        guard let timeStr = timeStr else {
            return nil
        }
        
        // 从时间字符串中提取小时数
        let hourStr = timeStr.split(separator: ":")[0]
        guard let hour = Int(hourStr) else {
            return nil
        }
        
        // 定义时间段
        let period: String
        if 0 <= hour && hour < 6 {
            period = "凌晨"
        } else if 6 <= hour && hour < 12 {
            period = "早上"
        } else if 12 <= hour && hour < 18 {
            period = "下午"
        } else {
            period = "晚上"
        }
        
        // 转换为12小时制
        let displayHour = hour <= 12 ? hour : hour - 12
        
        return "\(period)\(displayHour)点"
    }
    
    /// 格式化热点时刻显示
    /// - Parameter hour: 整数小时
    /// - Returns: 格式化后的时间字符串 (格式: "早上7点")
    static func formatPeakTime(_ hour: Int) -> String? {
        // 定义时间段
        let period: String
        if 0 <= hour && hour < 6 {
            period = "凌晨"
        } else if 6 <= hour && hour < 12 {
            period = "早上"
        } else if 12 <= hour && hour < 18 {
            period = "下午"
        } else {
            period = "晚上"
        }
        
        // 转换为12小时制
        let displayHour = hour <= 12 ? hour : hour - 12
        
        return "\(period)\(displayHour)点"
    }
} 