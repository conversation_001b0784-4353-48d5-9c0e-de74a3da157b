import Foundation

// 共享的日期格式化器
let sharedDateFormatters: [DateFormatter] = {
    // Django默认格式
    let djangoFormatter = DateFormatter()
    djangoFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSSSSSZ"
    djangoFormatter.locale = Locale(identifier: "en_US_POSIX")
    djangoFormatter.timeZone = TimeZone(secondsFromGMT: 0)
    
    // ISO8601格式（带毫秒）
    let iso8601Formatter = DateFormatter()
    iso8601Formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
    iso8601Formatter.locale = Locale(identifier: "en_US_POSIX")
    iso8601Formatter.timeZone = TimeZone(secondsFromGMT: 0)
    
    // ISO8601格式（不带毫秒）
    let simpleIso8601Formatter = DateFormatter()
    simpleIso8601Formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
    simpleIso8601Formatter.locale = Locale(identifier: "en_US_POSIX")
    simpleIso8601Formatter.timeZone = TimeZone(secondsFromGMT: 0)
    
    // 简单格式
    let simpleFormatter = DateFormatter()
    simpleFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
    simpleFormatter.locale = Locale(identifier: "en_US_POSIX")
    simpleFormatter.timeZone = TimeZone(secondsFromGMT: 0)
    
    return [djangoFormatter, iso8601Formatter, simpleIso8601Formatter, simpleFormatter]
}() 