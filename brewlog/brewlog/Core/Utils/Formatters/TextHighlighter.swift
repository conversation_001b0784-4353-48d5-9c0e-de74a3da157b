import SwiftUI
import RegexBuilder
#if canImport(UIKit)
import UIKit
#endif

/// 用于在文本中高亮显示度量单位的工具类
struct TextHighlighter {
    
    /// 测量单位高亮样式颜色
    struct HighlightColors {
        static let lightBackground = Color(red: 224/255, green: 247/255, blue: 250/255) // #E0F7FA
        static let darkBackground = Color(red: 0/255, green: 77/255, blue: 64/255) // #004D40
    }
    
    // 为保持API兼容，保留这个方法签名
    static func highlightMeasurements(_ text: String) -> AttributedString {
        return highlightMeasurementsSwiftUI(text)
    }

    /// 使用SwiftUI原生AttributedString实现高亮效果
    static func highlightMeasurementsSwiftUI(_ text: String) -> AttributedString {
        var attributedString = AttributedString(text)

        let ranges = getHighlightRanges(in: text)

        for range in ranges {
            // 将NSRange转换为AttributedString.Index范围
            if Range(range, in: text) != nil {
                let startIndex = attributedString.index(attributedString.startIndex, offsetByCharacters: range.location)
                let endIndex = attributedString.index(startIndex, offsetByCharacters: range.length)

                if startIndex < attributedString.endIndex && endIndex <= attributedString.endIndex {
                    let attributedRange = startIndex..<endIndex

                    // 设置高亮背景色
                    attributedString[attributedRange].backgroundColor = .init(TextHighlighter.HighlightColors.lightBackground)

                    // 设置高亮文本颜色
                    attributedString[attributedRange].foregroundColor = .primary

                    // 添加下划线
                    attributedString[attributedRange].underlineStyle = .single
                }
            }
        }

        return attributedString
    }
    
    /// 获取文本中的高亮范围，用于自定义视图
    static func getHighlightRanges(in text: String) -> [NSRange] {
        var ranges: [NSRange] = []
        
        // 定义相同的度量单位正则表达式
        let patterns = [
            // 容积单位模式，优先于时间单位
            #"(\d+(?:\.\d+)?)(?:[-~～至到]?\s*(\d+(?:\.\d+)?))?\s*(?:升|盎司|毫升|ml|ML|L|oz)"#,
            // 时间单位模式，包括范围如 10~15秒
            #"(\d+(?:\.\d+)?)(?:[-~～至到]?\s*(\d+(?:\.\d+)?))?\s*(?:秒钟|秒|分钟|分|小时|天|s|min|m|hrs|h)"#,
            // 重量单位模式，包括范围
            #"(\d+(?:\.\d+)?)(?:[-~～至到]?\s*(\d+(?:\.\d+)?))?\s*(?:克|g|G)"#,
            // 温度单位模式，包括范围
            #"(\d+(?:\.\d+)?)(?:[-~～至到]?\s*(\d+(?:\.\d+)?))?\s*(?:度|摄氏度|°C|℃)"#,
            // 压力单位模式，包括范围
            #"(\d+(?:\.\d+)?)(?:[-~～至到]?\s*(\d+(?:\.\d+)?))?\s*(?:bar|Bar|BAR|巴)"#
        ]
        
        for pattern in patterns {
            do {
                let regex = try NSRegularExpression(pattern: pattern, options: [.caseInsensitive])
                let nsRange = NSRange(text.startIndex..<text.endIndex, in: text)
                
                let matches = regex.matches(in: text, options: [], range: nsRange)
                for match in matches {
                    ranges.append(match.range)
                }
            } catch {
                print("正则表达式错误: \(error)")
            }
        }
        
        return ranges
    }
    
    /// 高亮文本中的度量单位（简化版本，仅用于SwiftUI）
    /// - Parameter text: 原始文本
    /// - Returns: 原始文本（暂时不进行高亮处理）
    static func highlightMeasurementsNative(_ text: String) -> String {
        return text
    }
}

/// 自定义高亮文本视图，同时支持亮/暗模式
struct AdaptiveHighlightedText: View {
    let text: String
    let font: Font
    let foregroundColor: Color
    let nonHighlightedColor: Color?
    let textAlignment: TextAlignment
    let cornerRadius: CGFloat
    @Environment(\.colorScheme) private var colorScheme
    
    // 原始构造器，接受AttributedString但现在只使用它的纯文本
    init(_ attributedText: AttributedString, font: Font = .body, foregroundColor: Color = .primary, textAlignment: TextAlignment = .leading, cornerRadius: CGFloat = 4) {
        self.text = attributedText.description
        self.font = font
        self.foregroundColor = foregroundColor
        self.nonHighlightedColor = nil
        self.textAlignment = textAlignment
        self.cornerRadius = cornerRadius
    }
    
    // 新增构造器，直接接受字符串
    init(_ text: String, font: Font = .body, foregroundColor: Color = .primary, textAlignment: TextAlignment = .leading, cornerRadius: CGFloat = 4) {
        self.text = text
        self.font = font
        self.foregroundColor = foregroundColor
        self.nonHighlightedColor = nil
        self.textAlignment = textAlignment
        self.cornerRadius = cornerRadius
    }
    
    // 新增构造器，支持设置非高亮文本颜色
    init(_ text: String, font: Font = .body, foregroundColor: Color = .primary, nonHighlightedColor: Color? = nil, textAlignment: TextAlignment = .leading, cornerRadius: CGFloat = 4) {
        self.text = text
        self.font = font
        self.foregroundColor = foregroundColor
        self.nonHighlightedColor = nonHighlightedColor
        self.textAlignment = textAlignment
        self.cornerRadius = cornerRadius
    }
    
    var body: some View {
        // 使用AttributedString实现高亮效果，同时确保文本完整显示
        Text(createAttributedText())
            .font(font)
            .multilineTextAlignment(textAlignment)
            .lineLimit(nil)
            .fixedSize(horizontal: false, vertical: true)
    }

    private func createAttributedText() -> AttributedString {
        var attributedString = AttributedString(text)

        // 设置基础文本颜色
        attributedString.foregroundColor = nonHighlightedColor ?? foregroundColor

        let ranges = TextHighlighter.getHighlightRanges(in: text)

        for range in ranges {
            // 将NSRange转换为AttributedString.Index范围
            if Range(range, in: text) != nil {
                let startIndex = attributedString.index(attributedString.startIndex, offsetByCharacters: range.location)
                let endIndex = attributedString.index(startIndex, offsetByCharacters: range.length)

                if startIndex < attributedString.endIndex && endIndex <= attributedString.endIndex {
                    let attributedRange = startIndex..<endIndex

                    // 根据颜色模式设置高亮背景色
                    let highlightColor = colorScheme == .dark ?
                        TextHighlighter.HighlightColors.darkBackground :
                        TextHighlighter.HighlightColors.lightBackground

                    attributedString[attributedRange].backgroundColor = highlightColor

                    // 设置高亮文本颜色
                    attributedString[attributedRange].foregroundColor = foregroundColor

                    // 添加下划线
                    attributedString[attributedRange].underlineStyle = .single
                }
            }
        }

        return attributedString
    }
}


