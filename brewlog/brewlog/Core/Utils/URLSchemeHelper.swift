import Foundation
import UIKit

/// URL Scheme 帮助类，专门用于外部深度链接（付费功能）
/// 内部导航请使用 AppState 的 InternalNavigationDelegate 方法
class URLSchemeHelper {

    // MARK: - 外部深度链接生成（付费功能）

    /// 生成外部深度链接 URL（付费功能）
    /// - Parameters:
    ///   - host: URL host（如 "equipment", "brew", "bean"）
    ///   - id: 可选的 ID 参数
    /// - Returns: 外部深度链接 URL 字符串
    static func generateExternalURL(host: String, id: Int? = nil) -> String {
        var urlString = "brewlog://\(host)"

        if let id = id {
            urlString += "?id=\(id)"
        }

        return urlString
    }

    // MARK: - 外部深度链接方法（付费功能）

    /// 获取外部深度链接 URL（用于分享或快捷指令）
    /// - Parameters:
    ///   - host: URL host
    ///   - id: 可选的 ID 参数
    /// - Returns: 外部深度链接 URL 字符串
    static func getExternalDeepLink(host: String, id: Int? = nil) -> String {
        return generateExternalURL(host: host, id: id)
    }

    // MARK: - URL 解析工具

    /// 从 URL 中提取 ID 参数
    /// - Parameter url: 要解析的 URL
    /// - Returns: 提取的 ID，如果没有则返回 nil
    static func extractID(from url: URL) -> Int? {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: true),
              let idParam = components.queryItems?.first(where: { $0.name == "id" }),
              let idString = idParam.value,
              let id = Int(idString) else {
            return nil
        }

        return id
    }
}
