import SwiftUI

extension Color {
    static var themeManager: ThemeManager {
        return ThemeManager.shared
    }
    
    // 文本颜色
    static var primaryText: Color {
        return themeManager.currentThemeColors.primaryTextColor
    }
    
    static var detailText: Color {
        return themeManager.currentThemeColors.detailTextColor
    }
    
    static var archivedText: Color {
        return themeManager.currentThemeColors.archivedTextColor
    }
    
    static var functionText: Color {
        return themeManager.currentThemeColors.functionTextColor
    }
    
    static var secondaryText: Color {
        return themeManager.currentThemeColors.secondaryTextColor
    }
    
    static var linkText: Color {
        return themeManager.currentThemeColors.linkTextColor
    }
    
    static var noteText: Color {
        return themeManager.currentThemeColors.noteTextColor
    }
    
    // 强调色
    static var primaryAccent: Color {
        return themeManager.currentThemeColors.primaryAccentColor
    }
    
    // 背景色
    static var primaryBg: Color {
        return themeManager.currentThemeColors.primaryBgColor
    }
    
    static var secondaryBg: Color {
        return themeManager.currentThemeColors.secondaryBgColor
    }
    
    static var navbarBg: Color {
        return themeManager.currentThemeColors.navbarBgColor
    }
    
    static var focusBg: Color {
        return themeManager.currentThemeColors.focusBgColor
    }
} 