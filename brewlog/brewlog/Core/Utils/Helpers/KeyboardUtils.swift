import SwiftUI
import UIKit

/// 键盘工具类 - 用于处理键盘相关操作和优化
class KeyboardUtils {
    /// 单例实例
    static let shared = KeyboardUtils()
    
    /// 是否已预加载键盘
    private var isKeyboardPreloaded = false
    
    private init() {}
    
    /// 预加载键盘资源以避免首次使用TextField时的延迟
    /// - Parameter force: 是否强制重新加载，默认为false
    func preloadKeyboard(force: Bool = false) {
        // 如果已经预加载过且不是强制重新加载，则直接返回
        if isKeyboardPreloaded && !force {
            return
        }
        
        DispatchQueue.main.async {
            // 创建一个临时的UITextField
            let lagFreeField = UITextField()
            
            // 添加到当前窗口（如果可用）
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first {
                window.addSubview(lagFreeField)
                
                // 激活并随后取消激活以加载键盘资源
                lagFreeField.becomeFirstResponder()
                lagFreeField.resignFirstResponder()
                
                // 移除临时视图
                lagFreeField.removeFromSuperview()
                
                // 标记已预加载
                self.isKeyboardPreloaded = true
                
                print("键盘资源预加载完成")
            }
        }
    }
    
    /// 重新加载键盘资源
    func reloadKeyboard() {
        preloadKeyboard(force: true)
    }
}

// MARK: - SwiftUI视图扩展
extension View {
    /// 为视图添加键盘预加载功能
    /// - Parameter preload: 是否在视图出现时预加载键盘
    /// - Returns: 修改后的视图
    func preloadKeyboardOnAppear(_ preload: Bool = true) -> some View {
        self.onAppear {
            if preload {
                KeyboardUtils.shared.preloadKeyboard()
            }
        }
    }
} 