import Foundation

// 通用解码工具，用于处理混合类型
struct AnyCodable: Codable {
    let value: Any
    
    init(_ value: Any) {
        self.value = value
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if let value = try? container.decode(String.self) {
            self.value = value
        } else if let value = try? container.decode(Bool.self) {
            self.value = value
        } else if let value = try? container.decode(Int.self) {
            self.value = value
        } else if let value = try? container.decode(Double.self) {
            self.value = value
        } else if let value = try? container.decode([String: AnyCodable].self) {
            self.value = value.mapValues { $0.value }
        } else if let value = try? container.decode([AnyCodable].self) {
            self.value = value.map { $0.value }
        } else if container.decodeNil() {
            self.value = NSNull()
        } else {
            throw DecodingError.dataCorruptedError(
                in: container, 
                debugDescription: "AnyCodable cannot decode value"
            )
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        do {
            switch value {
            case let value as String:
                try container.encode(value)
            case let value as Bool:
                try container.encode(value)
            case let value as Int:
                try container.encode(value)
            case let value as Double:
                try container.encode(value)
            case let value as [String: Any]:
                // 修复警告：直接使用if条件检查代替try?表达式
                let mappedValues = value.mapValues { AnyCodable($0) }
                if mappedValues.isEmpty && !value.isEmpty {
                    throw EncodingError.invalidValue(
                        value,
                        EncodingError.Context(
                            codingPath: container.codingPath,
                            debugDescription: "字典包含无法编码的值"
                        )
                    )
                }
                try container.encode(mappedValues)
            case let value as [Any]:
                // 修复警告：直接使用if条件检查代替try?表达式
                let mappedValues = value.map { AnyCodable($0) }
                if mappedValues.isEmpty && !value.isEmpty {
                    throw EncodingError.invalidValue(
                        value,
                        EncodingError.Context(
                            codingPath: container.codingPath,
                            debugDescription: "数组包含无法编码的值"
                        )
                    )
                }
                try container.encode(mappedValues)
            case is NSNull:
                try container.encodeNil()
            default:
                // 对于无法编码的值，提供更详细的错误信息，并安全处理异常
                print("⚠️ AnyCodable无法编码: \(String(describing: value)) (类型: \(type(of: value)))")
                try container.encodeNil()  // 尝试使用nil代替无法编码的值，避免崩溃
            }
        } catch {
            // 记录错误信息，但使用nil作为默认值，避免崩溃
            print("❌ 编码错误: \(error)")
            let pathDescription = container.codingPath.map { $0.stringValue }.joined(separator: ".")
            print("📋 编码路径: \(pathDescription)")
            // 尝试编码为nil值
            try container.encodeNil()
        }
    }
}

// MARK: - Coffee Bean
struct CoffeeBean: Identifiable, Codable, Hashable {
    let id: Int
    let name: String
    let type: String
    let typeDisplay: String
    let roaster: String
    let roastLevel: Int
    let roastLevelDisplay: String
    let origin: String?
    let region: String?
    let finca: String?
    let variety: String?
    let process: String?
    let barcode: String?
    let notes: String?
    
    // 库存信息
    let bagWeight: Double?
    let bagRemain: Double?
    let purchasePrice: Double?
    
    // 初始包装信息
    let initialBagWeight: Double?
    let initialBagRemain: Double?
    let initialPurchasePrice: Double?
    let initialRoastDate: Date?
    let initialCreatedAt: Date?
    let initialRestPeriodMin: Int?
    let initialRestPeriodMax: Int?
    
    // 日期信息
    let roastDate: Date?
    let createdAt: Date
    let deletedAt: Date?
    
    // 状态标记
    let isFavorite: Bool
    let isArchived: Bool
    let isDeleted: Bool
    let isDecaf: Bool
    
    // 海拔信息
    let altitudeType: String
    let altitudeSingle: Int?
    let altitudeMin: Int?
    let altitudeMax: Int?
    
    // 养豆期信息
    let restPeriodMin: Int?
    let restPeriodMax: Int?
    let restPeriodProgress: Int?
    
    // 库存状态
    let stockStatus: String
    
    // 平均评分
    let avgRating: Double?
    
    // 风味标签
    let tasteNotes: [String]?
    
    // 拼配组件信息 - 当type为BLEND时使用
    let blendComponents: [BlendComponent]?
    
    // 回购记录信息
    let occurrences: [BeanOccurrence]?
    
    // 使用数据相关字段
    let usageCount: Int?
    let lastUsed: Date?
    let daysSinceLastUse: Int?
    let mostUsedEquipment: EquipmentSummary?
    let remainingUses: Int?
    let occurrencesCount: Int?
    let avgRepurchaseInterval: Double?
    let dimensionsAvg: [String: Double]?
    let tastingCount: Int?
    let uniqueFlavorTags: [FlavorTag]?
    let flavorAccuracy: Int?
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case type
        case typeDisplay = "type_display"
        case roaster
        case roastLevel = "roast_level"
        case roastLevelDisplay = "roast_level_display"
        case origin
        case region
        case finca
        case variety
        case process
        case barcode
        case notes
        
        // 库存信息
        case bagWeight = "bag_weight"
        case bagRemain = "bag_remain"
        case purchasePrice = "price"
        // 添加weight字段映射 - API可能返回weight而非bag_weight
        case weight = "weight"
        
        // 初始包装信息
        case initialBagWeight = "initial_bag_weight"
        case initialBagRemain = "initial_bag_remain"
        case initialPurchasePrice = "initial_purchase_price"
        case initialRoastDate = "initial_roast_date"
        case initialCreatedAt = "initial_created_at"
        case initialRestPeriodMin = "initial_rest_period_min"
        case initialRestPeriodMax = "initial_rest_period_max"
        
        // 日期信息
        case roastDate = "roast_date"
        case createdAt = "created_at"
        case deletedAt = "deleted_at"
        
        // 状态标记
        case isFavorite = "is_favorite"
        case isArchived = "is_archived"
        case isDeleted = "is_deleted"
        case isDecaf = "is_decaf"
        
        // 海拔信息
        case altitudeType = "altitude_type"
        case altitudeSingle = "altitude_single"
        case altitudeMin = "altitude_min" 
        case altitudeMax = "altitude_max"
        
        // 养豆期信息
        case restPeriodMin = "rest_period_min"
        case restPeriodMax = "rest_period_max"
        case restPeriodProgress = "rest_period_progress"
        
        // 库存状态
        case stockStatus = "stock_status"
        
        // 平均评分
        case avgRating = "avg_rating"
        
        // 风味标签
        case tasteNotes = "taste_notes"
        
        // 拼配组件信息
        case blendComponents = "blend_components"
        
        // 回购记录信息
        case occurrences = "occurrences"
        
        // 使用数据相关字段
        case usageCount = "usage_count"
        case lastUsed = "last_used"
        case daysSinceLastUse = "days_since_last_use"
        case mostUsedEquipment = "most_used_equipment"
        case remainingUses = "remaining_uses"
        case occurrencesCount = "occurrences_count"
        case avgRepurchaseInterval = "avg_repurchase_interval"
        case dimensionsAvg = "dimensions_avg"
        case tastingCount = "tasting_count"
        case uniqueFlavorTags = "unique_flavor_tags"
        case flavorAccuracy = "flavor_accuracy"
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 解码ID字段并添加详细错误处理
        do {
            self.id = try container.decode(Int.self, forKey: .id)
        } catch {
            print("❌ ID字段解码失败: \(error)")
            // 直接输出codingPath的描述，避免JSONSerialization序列化
            let pathDescription = decoder.codingPath.map { $0.stringValue }.joined(separator: ".")
            print("📋 编码路径: \(pathDescription)")
            
            // 尝试额外调试信息
            if let decodingError = error as? DecodingError {
                switch decodingError {
                case .keyNotFound(let key, let context):
                    print("🔍 未找到键: \(key.stringValue), 上下文: \(context.debugDescription)")
                    // 检查是否在nested对象中 - 特别处理coffee_bean可能没有id的情况
                    if pathDescription.contains("coffee_bean") || pathDescription.contains("results") {
                        print("⚠️ 检测到嵌套对象中缺少ID字段，尝试使用默认ID")
                        // 使用一个默认ID
                        self.id = Int.random(in: 100000...999999)
                    } else {
                        throw decodingError
                    }
                case .valueNotFound(let type, let context):
                    print("🔍 未找到值: 期望类型 \(type), 上下文: \(context.debugDescription)")
                    throw decodingError
                case .typeMismatch(let type, let context):
                    print("🔍 类型不匹配: 期望类型 \(type), 上下文: \(context.debugDescription)")
                    throw decodingError
                case .dataCorrupted(let context):
                    print("🔍 数据损坏: 上下文: \(context.debugDescription)")
                    throw decodingError
                @unknown default:
                    print("🔍 未知解码错误: \(decodingError)")
                    throw decodingError
                }
            } else {
                // 如果无法确定类型，重新抛出错误
                throw error
            }
        }
        
        // 解码基本属性
        name = try container.decode(String.self, forKey: .name)
        type = try container.decode(String.self, forKey: .type)
        typeDisplay = try container.decode(String.self, forKey: .typeDisplay)
        roaster = try container.decode(String.self, forKey: .roaster)
        roastLevel = try container.decode(Int.self, forKey: .roastLevel)
        roastLevelDisplay = try container.decode(String.self, forKey: .roastLevelDisplay)
        origin = try container.decodeIfPresent(String.self, forKey: .origin)
        region = try container.decodeIfPresent(String.self, forKey: .region)
        finca = try container.decodeIfPresent(String.self, forKey: .finca)
        variety = try container.decodeIfPresent(String.self, forKey: .variety)
        process = try container.decodeIfPresent(String.self, forKey: .process)
        barcode = try container.decodeIfPresent(String.self, forKey: .barcode)
        notes = try container.decodeIfPresent(String.self, forKey: .notes)
        
        // 尝试从bagWeight获取值
        var finalBagWeight: Double? = {
            if let value = try? container.decode(Double.self, forKey: .bagWeight) {
                return value
            } else if let stringValue = try? container.decode(String.self, forKey: .bagWeight),
                      let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        
        // 如果bagWeight为nil，尝试从weight字段获取值
        if finalBagWeight == nil {
            finalBagWeight = {
                if let value = try? container.decode(Double.self, forKey: .weight) {
                    return value
                } else if let stringValue = try? container.decode(String.self, forKey: .weight),
                          let doubleValue = Double(stringValue) {
                    return doubleValue
                }
                return nil
            }()
        }
        
        bagWeight = finalBagWeight
        
        // 处理bag_remain字段，支持字符串或Double类型
        bagRemain = {
            if let value = try? container.decode(Double.self, forKey: .bagRemain) {
                return value
            } else if let stringValue = try? container.decode(String.self, forKey: .bagRemain),
                      let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        
        // 处理purchasePrice字段，支持字符串或Double类型
        purchasePrice = {
            if let value = try? container.decode(Double.self, forKey: .purchasePrice) {
                return value
            } else if let stringValue = try? container.decode(String.self, forKey: .purchasePrice),
                      let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        
        // 处理roast_date字段，支持字符串日期或Double时间戳
        if let roastDateTimestamp = try? container.decode(Double.self, forKey: .roastDate) {
            roastDate = Date(timeIntervalSince1970: roastDateTimestamp)
        } else if let roastDateString = try? container.decode(String.self, forKey: .roastDate) {
            // 尝试解析ISO8601格式的日期字符串
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
            if let date = dateFormatter.date(from: roastDateString) {
                roastDate = date
            } else {
                // 尝试解析简单的日期格式
                dateFormatter.dateFormat = "yyyy-MM-dd"
                roastDate = dateFormatter.date(from: roastDateString)
            }
        } else {
            roastDate = nil
        }
        
        createdAt = {
            if let timestamp = try? container.decode(Double.self, forKey: .createdAt) {
                return Date(timeIntervalSince1970: timestamp)
            } else {
                do {
                    return try container.decode(Date.self, forKey: .createdAt)
                } catch {
                    return Date()
                }
            }
        }()
        
        if let deletedAtString = try container.decodeIfPresent(String.self, forKey: .deletedAt) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
            deletedAt = formatter.date(from: deletedAtString)
        } else {
            deletedAt = nil
        }
        
        isFavorite = try container.decode(Bool.self, forKey: .isFavorite)
        isArchived = try container.decode(Bool.self, forKey: .isArchived)
        isDeleted = try container.decode(Bool.self, forKey: .isDeleted)
        isDecaf = try container.decode(Bool.self, forKey: .isDecaf)
        altitudeType = try container.decode(String.self, forKey: .altitudeType)
        altitudeSingle = try container.decodeIfPresent(Int.self, forKey: .altitudeSingle)
        altitudeMin = try container.decodeIfPresent(Int.self, forKey: .altitudeMin)
        altitudeMax = try container.decodeIfPresent(Int.self, forKey: .altitudeMax)
        restPeriodMin = try container.decodeIfPresent(Int.self, forKey: .restPeriodMin)
        restPeriodMax = try container.decodeIfPresent(Int.self, forKey: .restPeriodMax)
        restPeriodProgress = try container.decodeIfPresent(Int.self, forKey: .restPeriodProgress)
        stockStatus = try container.decodeIfPresent(String.self, forKey: .stockStatus) ?? ""
        
        // 处理avgRating字段，支持字符串或Double类型
        avgRating = {
            if let value = try? container.decode(Double.self, forKey: .avgRating) {
                return value
            } else if let stringValue = try? container.decode(String.self, forKey: .avgRating),
                     let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        
        // 解码风味标签
        tasteNotes = try container.decodeIfPresent([String].self, forKey: .tasteNotes)
        
        // 解码拼配组件信息，仅当类型为BLEND时
        blendComponents = try container.decodeIfPresent([BlendComponent].self, forKey: .blendComponents)
        
        // 解码回购记录信息
        occurrences = try container.decodeIfPresent([BeanOccurrence].self, forKey: .occurrences)
        
        // 解码使用数据相关字段
        usageCount = try container.decodeIfPresent(Int.self, forKey: .usageCount)
        
        // 解码lastUsed日期
        if let lastUsedTimestamp = try container.decodeIfPresent(Double.self, forKey: .lastUsed) {
            lastUsed = Date(timeIntervalSince1970: lastUsedTimestamp)
        } else {
            lastUsed = nil
        }
        
        daysSinceLastUse = try container.decodeIfPresent(Int.self, forKey: .daysSinceLastUse)
        mostUsedEquipment = try container.decodeIfPresent(EquipmentSummary.self, forKey: .mostUsedEquipment)
        remainingUses = try container.decodeIfPresent(Int.self, forKey: .remainingUses)
        occurrencesCount = try container.decodeIfPresent(Int.self, forKey: .occurrencesCount)
        avgRepurchaseInterval = {
            if let value = try? container.decodeIfPresent(Double.self, forKey: .avgRepurchaseInterval) {
                return value
            }
            if let intValue = try? container.decodeIfPresent(Int.self, forKey: .avgRepurchaseInterval) {
                return Double(intValue)
            }
            if let stringValue = try? container.decodeIfPresent(String.self, forKey: .avgRepurchaseInterval),
               let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        dimensionsAvg = {
            // 尝试直接解码为[String: Double]
            if let dict = try? container.decode([String: Double].self, forKey: .dimensionsAvg) {
                return dict
            }
            
            // 尝试解码为[String: String]，然后转换
            if let stringDict = try? container.decode([String: String].self, forKey: .dimensionsAvg) {
                var result = [String: Double]()
                for (key, value) in stringDict {
                    if let doubleValue = Double(value) {
                        result[key] = doubleValue
                    }
                }
                return result.isEmpty ? nil : result
            }
            
            // 尝试解码为[String: Any]类型的自定义方式
            if let anyDict = try? container.decodeIfPresent(AnyCodable.self, forKey: .dimensionsAvg)?.value as? [String: Any] {
                return convertToDimensionsDict(anyDict)
            }
            
            return nil
        }()
        tastingCount = try container.decodeIfPresent(Int.self, forKey: .tastingCount)
        uniqueFlavorTags = try container.decodeIfPresent([FlavorTag].self, forKey: .uniqueFlavorTags)
        flavorAccuracy = try container.decodeIfPresent(Int.self, forKey: .flavorAccuracy)
        
        // 解码初始包装信息字段
        initialBagWeight = {
            if let value = try? container.decode(Double.self, forKey: .initialBagWeight) {
                return value
            } else if let stringValue = try? container.decode(String.self, forKey: .initialBagWeight),
                      let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        
        initialBagRemain = {
            if let value = try? container.decode(Double.self, forKey: .initialBagRemain) {
                return value
            } else if let stringValue = try? container.decode(String.self, forKey: .initialBagRemain),
                      let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        
        initialPurchasePrice = {
            if let value = try? container.decode(Double.self, forKey: .initialPurchasePrice) {
                return value
            } else if let stringValue = try? container.decode(String.self, forKey: .initialPurchasePrice),
                      let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        
        // 处理initial_roast_date字段
        if let initialRoastDateTimestamp = try? container.decode(Double.self, forKey: .initialRoastDate) {
            initialRoastDate = Date(timeIntervalSince1970: initialRoastDateTimestamp)
        } else if let initialRoastDateString = try? container.decode(String.self, forKey: .initialRoastDate) {
            // 尝试解析ISO8601格式的日期字符串
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
            if let date = dateFormatter.date(from: initialRoastDateString) {
                initialRoastDate = date
            } else {
                // 尝试解析简单的日期格式
                dateFormatter.dateFormat = "yyyy-MM-dd"
                initialRoastDate = dateFormatter.date(from: initialRoastDateString)
            }
        } else {
            initialRoastDate = nil
        }
        
        // 处理initial_created_at字段
        if let initialCreatedAtTimestamp = try? container.decode(Double.self, forKey: .initialCreatedAt) {
            initialCreatedAt = Date(timeIntervalSince1970: initialCreatedAtTimestamp)
        } else if let initialCreatedAtString = try? container.decode(String.self, forKey: .initialCreatedAt) {
            // 尝试解析ISO8601格式的日期字符串
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
            if let date = dateFormatter.date(from: initialCreatedAtString) {
                initialCreatedAt = date
            } else {
                // 尝试解析简单的日期格式
                dateFormatter.dateFormat = "yyyy-MM-dd"
                initialCreatedAt = dateFormatter.date(from: initialCreatedAtString)
            }
        } else {
            initialCreatedAt = nil
        }
        
        initialRestPeriodMin = try container.decodeIfPresent(Int.self, forKey: .initialRestPeriodMin)
        initialRestPeriodMax = try container.decodeIfPresent(Int.self, forKey: .initialRestPeriodMax)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        // 编码基本属性
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(type, forKey: .type)
        try container.encode(typeDisplay, forKey: .typeDisplay)
        try container.encode(roaster, forKey: .roaster)
        try container.encode(roastLevel, forKey: .roastLevel)
        try container.encode(roastLevelDisplay, forKey: .roastLevelDisplay)
        try container.encodeIfPresent(origin, forKey: .origin)
        try container.encodeIfPresent(region, forKey: .region)
        try container.encodeIfPresent(finca, forKey: .finca)
        try container.encodeIfPresent(variety, forKey: .variety)
        try container.encodeIfPresent(process, forKey: .process)
        try container.encodeIfPresent(barcode, forKey: .barcode)
        try container.encodeIfPresent(notes, forKey: .notes)
        
        // 编码库存信息
        try container.encodeIfPresent(bagWeight, forKey: .bagWeight)
        try container.encodeIfPresent(bagRemain, forKey: .bagRemain)
        try container.encodeIfPresent(purchasePrice, forKey: .purchasePrice)
        
        // 编码初始包装信息
        try container.encodeIfPresent(initialBagWeight, forKey: .initialBagWeight)
        try container.encodeIfPresent(initialBagRemain, forKey: .initialBagRemain)
        try container.encodeIfPresent(initialPurchasePrice, forKey: .initialPurchasePrice)
        try container.encodeIfPresent(initialRestPeriodMin, forKey: .initialRestPeriodMin)
        try container.encodeIfPresent(initialRestPeriodMax, forKey: .initialRestPeriodMax)
        
        // 编码初始烘焙日期和创建日期
        if let initialRoastDate = initialRoastDate {
            try container.encode(initialRoastDate.timeIntervalSince1970, forKey: .initialRoastDate)
        }
        
        if let initialCreatedAt = initialCreatedAt {
            try container.encode(initialCreatedAt.timeIntervalSince1970, forKey: .initialCreatedAt)
        }
        
        // 对于roastDate，如果存在，转换为时间戳编码
        if let roastDate = roastDate {
            try container.encode(roastDate.timeIntervalSince1970, forKey: .roastDate)
        }
        
        // 编码创建日期
        try container.encode(createdAt, forKey: .createdAt)
        
        // 编码删除日期
        if let deletedAt = deletedAt {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
            try container.encode(formatter.string(from: deletedAt), forKey: .deletedAt)
        }
        
        // 编码状态标记
        try container.encode(isFavorite, forKey: .isFavorite)
        try container.encode(isArchived, forKey: .isArchived)
        try container.encode(isDeleted, forKey: .isDeleted)
        try container.encode(isDecaf, forKey: .isDecaf)
        
        // 编码海拔信息
        try container.encode(altitudeType, forKey: .altitudeType)
        try container.encodeIfPresent(altitudeSingle, forKey: .altitudeSingle)
        try container.encodeIfPresent(altitudeMin, forKey: .altitudeMin)
        try container.encodeIfPresent(altitudeMax, forKey: .altitudeMax)
        
        // 编码养豆期信息
        try container.encodeIfPresent(restPeriodMin, forKey: .restPeriodMin)
        try container.encodeIfPresent(restPeriodMax, forKey: .restPeriodMax)
        try container.encodeIfPresent(restPeriodProgress, forKey: .restPeriodProgress)
        
        // 编码库存状态
        try container.encode(stockStatus, forKey: .stockStatus)
        
        // 编码平均评分
        try container.encodeIfPresent(avgRating, forKey: .avgRating)
        
        // 编码风味标签
        try container.encodeIfPresent(tasteNotes, forKey: .tasteNotes)
        
        // 编码拼配组件信息
        try container.encodeIfPresent(blendComponents, forKey: .blendComponents)
        
        // 编码回购记录信息
        try container.encodeIfPresent(occurrences, forKey: .occurrences)
        
        // 编码使用数据相关字段
        try container.encodeIfPresent(usageCount, forKey: .usageCount)
        
        // 对于lastUsed，如果存在，转换为时间戳编码
        if let lastUsed = lastUsed {
            try container.encode(lastUsed.timeIntervalSince1970, forKey: .lastUsed)
        }
        
        try container.encodeIfPresent(daysSinceLastUse, forKey: .daysSinceLastUse)
        try container.encodeIfPresent(mostUsedEquipment, forKey: .mostUsedEquipment)
        try container.encodeIfPresent(remainingUses, forKey: .remainingUses)
        try container.encodeIfPresent(occurrencesCount, forKey: .occurrencesCount)
        try container.encodeIfPresent(avgRepurchaseInterval, forKey: .avgRepurchaseInterval)
        try container.encodeIfPresent(dimensionsAvg, forKey: .dimensionsAvg)
        try container.encodeIfPresent(tastingCount, forKey: .tastingCount)
        try container.encodeIfPresent(uniqueFlavorTags, forKey: .uniqueFlavorTags)
        try container.encodeIfPresent(flavorAccuracy, forKey: .flavorAccuracy)
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: CoffeeBean, rhs: CoffeeBean) -> Bool {
        lhs.id == rhs.id
    }
    
    // 添加初始化方法
    init(id: Int, name: String, type: String, typeDisplay: String, roaster: String, roastLevel: Int, roastLevelDisplay: String, origin: String?, region: String?, finca: String?, variety: String?, process: String?, barcode: String?, notes: String?, bagWeight: Double?, bagRemain: Double?, purchasePrice: Double?, roastDate: Date?, createdAt: Date, deletedAt: Date?, isFavorite: Bool, isArchived: Bool, isDeleted: Bool, isDecaf: Bool, altitudeType: String, altitudeSingle: Int?, altitudeMin: Int?, altitudeMax: Int?, restPeriodMin: Int?, restPeriodMax: Int?, restPeriodProgress: Int?, stockStatus: String, avgRating: Double?, tasteNotes: [String]? = nil, blendComponents: [BlendComponent]? = nil, occurrences: [BeanOccurrence]? = nil, usageCount: Int? = nil, lastUsed: Date? = nil, daysSinceLastUse: Int? = nil, mostUsedEquipment: EquipmentSummary? = nil, remainingUses: Int? = nil, occurrencesCount: Int? = nil, avgRepurchaseInterval: Double? = nil, dimensionsAvg: [String: Double]? = nil, tastingCount: Int? = nil, uniqueFlavorTags: [FlavorTag]? = nil, flavorAccuracy: Int? = nil, initialBagWeight: Double? = nil, initialBagRemain: Double? = nil, initialPurchasePrice: Double? = nil, initialRoastDate: Date? = nil, initialCreatedAt: Date? = nil, initialRestPeriodMin: Int? = nil, initialRestPeriodMax: Int? = nil) {
        self.id = id
        self.name = name
        self.type = type
        self.typeDisplay = typeDisplay
        self.roaster = roaster
        self.roastLevel = roastLevel
        self.roastLevelDisplay = roastLevelDisplay
        self.origin = origin
        self.region = region
        self.finca = finca
        self.variety = variety
        self.process = process
        self.barcode = barcode
        self.notes = notes
        self.bagWeight = bagWeight
        self.bagRemain = bagRemain
        self.purchasePrice = purchasePrice
        self.roastDate = roastDate
        self.createdAt = createdAt
        self.deletedAt = deletedAt
        self.isFavorite = isFavorite
        self.isArchived = isArchived
        self.isDeleted = isDeleted
        self.isDecaf = isDecaf
        self.altitudeType = altitudeType
        self.altitudeSingle = altitudeSingle
        self.altitudeMin = altitudeMin
        self.altitudeMax = altitudeMax
        self.restPeriodMin = restPeriodMin
        self.restPeriodMax = restPeriodMax
        self.restPeriodProgress = restPeriodProgress
        self.stockStatus = stockStatus
        self.avgRating = avgRating
        self.tasteNotes = tasteNotes
        self.blendComponents = blendComponents
        self.occurrences = occurrences
        self.usageCount = usageCount
        self.lastUsed = lastUsed
        self.daysSinceLastUse = daysSinceLastUse
        self.mostUsedEquipment = mostUsedEquipment
        self.remainingUses = remainingUses
        self.occurrencesCount = occurrencesCount
        self.avgRepurchaseInterval = avgRepurchaseInterval
        self.dimensionsAvg = dimensionsAvg
        self.tastingCount = tastingCount
        self.uniqueFlavorTags = uniqueFlavorTags
        self.flavorAccuracy = flavorAccuracy
        self.initialBagWeight = initialBagWeight
        self.initialBagRemain = initialBagRemain
        self.initialPurchasePrice = initialPurchasePrice
        self.initialRoastDate = initialRoastDate
        self.initialCreatedAt = initialCreatedAt
        self.initialRestPeriodMin = initialRestPeriodMin
        self.initialRestPeriodMax = initialRestPeriodMax
    }
    
    // 获取咖啡豆的海拔表示
    var altitudeDisplay: String {
        if altitudeType == "SINGLE", let alt = altitudeSingle {
            return "\(alt)m"
        } else if altitudeType == "RANGE", let min = altitudeMin, let max = altitudeMax {
            return "\(min)-\(max)m"
        }
        return "未知"
    }
    
    // 判断咖啡豆是否即将耗尽
    var isLowStock: Bool {
        guard let remain = bagRemain, let total = bagWeight else { return false }
        return remain > 0 && remain < (total * 0.2) // 小于20%视为低库存
    }
    
    // 判断是否为陈豆
    var isAged: Bool {
        guard let roastDate = roastDate else { return false }
        let days = Calendar.current.dateComponents([.day], from: roastDate, to: Date()).day ?? 0
        return days > 60 // 超过60天视为陈豆
    }
    
    // 判断是否为拼配咖啡
    var isBlend: Bool {
        return type == "BLEND"
    }
    
    // 获取拼配组件的字符串表示
    var blendComponentsText: String? {
        guard let components = blendComponents, !components.isEmpty else { return nil }
        
        let formattedComponents = components.map { component in
            let originInfo = component.origin ?? ""
            let processInfo = component.process ?? ""
            let ratio = String(format: "%.0f%%", component.blendRatio)
            return "\(originInfo) \(processInfo) \(ratio)"
        }
        
        return formattedComponents.joined(separator: "、")
    }
    
    // 获取购买历史次数
    var purchaseCount: Int {
        return (occurrences?.count ?? 0) + 1 // 当前记录加上历史记录
    }
}

// MARK: - 拼配组件模型
struct BlendComponent: Identifiable, Codable, Hashable {
    let id: Int
    let coffeeBeanId: Int
    let origin: String?
    let region: String?
    let finca: String?
    let variety: String?
    let process: String?
    let roastLevel: Int
    let roastLevelDisplay: String?
    var blendRatio: Double
    let order: Int
    
    // 海拔信息
    let altitudeType: String
    let altitudeSingle: Int?
    let altitudeMin: Int?
    let altitudeMax: Int?
    
    enum CodingKeys: String, CodingKey {
        case id
        case coffeeBeanId = "coffee_bean_id"
        case origin
        case region
        case finca
        case variety
        case process
        case roastLevel = "roast_level"
        case roastLevelDisplay = "roast_level_display"
        case blendRatio = "blend_ratio"
        case order
        case altitudeType = "altitude_type"
        case altitudeSingle = "altitude_single"
        case altitudeMin = "altitude_min"
        case altitudeMax = "altitude_max"
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: BlendComponent, rhs: BlendComponent) -> Bool {
        lhs.id == rhs.id
    }
    
    // 获取组件的海拔表示
    var altitudeDisplay: String {
        if altitudeType == "SINGLE", let alt = altitudeSingle {
            return "\(alt)m"
        } else if altitudeType == "RANGE", let min = altitudeMin, let max = altitudeMax {
            return "\(min)-\(max)m"
        }
        return "未知"
    }
    
    // 添加一个完整的初始化方法
    init(id: Int, coffeeBeanId: Int, origin: String?, region: String?, finca: String?, variety: String?, process: String?, roastLevel: Int, roastLevelDisplay: String?, blendRatio: Double, order: Int, altitudeType: String, altitudeSingle: Int?, altitudeMin: Int?, altitudeMax: Int?) {
        self.id = id
        self.coffeeBeanId = coffeeBeanId
        self.origin = origin
        self.region = region
        self.finca = finca
        self.variety = variety
        self.process = process
        self.roastLevel = roastLevel
        self.roastLevelDisplay = roastLevelDisplay
        self.blendRatio = blendRatio
        self.order = order
        self.altitudeType = altitudeType
        self.altitudeSingle = altitudeSingle
        self.altitudeMin = altitudeMin
        self.altitudeMax = altitudeMax
    }
}

// MARK: - 咖啡豆回购模型
struct BeanOccurrence: Identifiable, Codable, Hashable {
    let id: Int
    let coffeeBeanId: Int
    let bagWeight: Double?
    let bagRemain: Double?
    let purchasePrice: Double?
    let roastDate: Date?
    let createdAt: Date
    let restPeriodMin: Int?
    let restPeriodMax: Int?
    var timeGap: TimeInterval?
    
    enum CodingKeys: String, CodingKey {
        case id
        case coffeeBeanId = "coffee_bean_id"
        case bagWeight = "bag_weight"
        case bagRemain = "bag_remain"
        case purchasePrice = "purchase_price"
        case roastDate = "roast_date"
        case createdAt = "created_at"
        case restPeriodMin = "rest_period_min"
        case restPeriodMax = "rest_period_max"
        case timeGap = "time_gap"
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(Int.self, forKey: .id)
        coffeeBeanId = try container.decode(Int.self, forKey: .coffeeBeanId)
        
        // 处理bag_weight字段，支持字符串或Double类型
        bagWeight = {
            if let value = try? container.decode(Double.self, forKey: .bagWeight) {
                return value
            } else if let stringValue = try? container.decode(String.self, forKey: .bagWeight),
                     let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        
        // 处理bag_remain字段，支持字符串或Double类型
        bagRemain = {
            if let value = try? container.decode(Double.self, forKey: .bagRemain) {
                return value
            } else if let stringValue = try? container.decode(String.self, forKey: .bagRemain),
                     let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        
        // 处理purchase_price字段，支持字符串或Double类型
        purchasePrice = {
            if let value = try? container.decode(Double.self, forKey: .purchasePrice) {
                return value
            } else if let stringValue = try? container.decode(String.self, forKey: .purchasePrice),
                     let doubleValue = Double(stringValue) {
                return doubleValue
            }
            return nil
        }()
        
        // 处理roast_date字段，支持字符串日期或Double时间戳
        if let roastDateTimestamp = try? container.decode(Double.self, forKey: .roastDate) {
            roastDate = Date(timeIntervalSince1970: roastDateTimestamp)
        } else if let roastDateString = try? container.decode(String.self, forKey: .roastDate) {
            // 尝试解析ISO8601格式的日期字符串
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"
            if let date = dateFormatter.date(from: roastDateString) {
                roastDate = date
            } else {
                // 尝试解析简单的日期格式
                dateFormatter.dateFormat = "yyyy-MM-dd"
                roastDate = dateFormatter.date(from: roastDateString)
            }
        } else {
            roastDate = nil
        }
        
        // 增强created_at字段的解码，处理多种可能的格式
        createdAt = {
            // 首先尝试Double时间戳
            if let timestamp = try? container.decode(Double.self, forKey: .createdAt) {
                return Date(timeIntervalSince1970: timestamp)
            } 
            // 尝试字符串日期时间格式
            else if let dateString = try? container.decode(String.self, forKey: .createdAt) {
                let dateFormatter = DateFormatter()
                
                // 尝试多种可能的日期格式
                let formats = [
                    "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ",
                    "yyyy-MM-dd'T'HH:mm:ssZ",
                    "yyyy-MM-dd'T'HH:mm:ss",
                    "yyyy-MM-dd HH:mm:ss",
                    "yyyy-MM-dd"
                ]
                
                for format in formats {
                    dateFormatter.dateFormat = format
                    if let date = dateFormatter.date(from: dateString) {
                        return date
                    }
                }
                
                // 如果所有格式都解析失败，使用当前日期，但记录错误
                print("⚠️ 无法解析日期字符串: \(dateString)")
                return Date()
            } 
            // 尝试直接解码Date类型
            else {
                do {
                    return try container.decode(Date.self, forKey: .createdAt)
                } catch {
                    return Date()
                }
            }
        }()
        
        restPeriodMin = try container.decodeIfPresent(Int.self, forKey: .restPeriodMin)
        restPeriodMax = try container.decodeIfPresent(Int.self, forKey: .restPeriodMax)
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: BeanOccurrence, rhs: BeanOccurrence) -> Bool {
        lhs.id == rhs.id
    }
    
    // 添加一个完整的初始化方法
    init(id: Int, coffeeBeanId: Int, bagWeight: Double?, bagRemain: Double?, purchasePrice: Double?, roastDate: Date?, createdAt: Date, restPeriodMin: Int?, restPeriodMax: Int?) {
        self.id = id
        self.coffeeBeanId = coffeeBeanId
        self.bagWeight = bagWeight
        self.bagRemain = bagRemain
        self.purchasePrice = purchasePrice
        self.roastDate = roastDate
        self.createdAt = createdAt
        self.restPeriodMin = restPeriodMin
        self.restPeriodMax = restPeriodMax
    }
}

// MARK: - Brewing Record
struct BrewingRecord: Identifiable, Codable, Hashable {
    let id: Int
    let recipeName: String?
    let coffeeBean: CoffeeBean
    let brewingEquipment: Equipment
    let grindingEquipment: Equipment?
    let grindSize: String
    let doseWeight: Double
    let yieldWeight: Double
    let waterTemperature: Double
    let brewingTime: Int
    let ratingLevel: Int
    let ratingDisplay: String
    let aroma: Int
    let acidity: Int
    let sweetness: Int
    let body: Int
    let aftertaste: Int
    let waterQuality: String?
    let roomTemperature: Double?
    let roomHumidity: Int?
    let steps: [BrewingStepItem]
    let flavorTags: [FlavorTag]
    let tagOverlap: Int?
    let notes: String
    let createdAt: Date
    let gadgets: [Equipment]?
    // 服务器端有时会返回空对象{}而不是null导致解析错误，已通过自定义Equipment的Codable初始化方法处理
    let gadgetKit: Equipment?
    
    enum CodingKeys: String, CodingKey {
        case id
        case recipeName = "recipe_name"
        case coffeeBean = "coffee_bean"
        case brewingEquipment = "brewing_equipment"
        case grindingEquipment = "grinding_equipment"
        case grindSize = "grind_size"
        case doseWeight = "dose_weight"
        case yieldWeight = "yield_weight"
        case waterTemperature = "water_temperature"
        case brewingTime = "brewing_time"
        case ratingLevel = "rating_level"
        case ratingDisplay = "rating_display"
        case aroma
        case acidity
        case sweetness
        case body
        case aftertaste
        case waterQuality = "water_quality"
        case roomTemperature = "room_temperature"
        case roomHumidity = "room_humidity"
        case steps
        case flavorTags = "flavor_tags"
        case tagOverlap = "tag_overlap"
        case notes
        case createdAt = "created_at"
        case gadgets
        case gadgetKit = "gadget_kit"
    }
    
    // 用于显示的步骤文本
    var stepsText: [String] {
        return steps.map { $0.text }
    }
    
    // Hashable实现
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: BrewingRecord, rhs: BrewingRecord) -> Bool {
        lhs.id == rhs.id
    }
    
    // 添加初始化方法
    init(id: Int, recipeName: String?, coffeeBean: CoffeeBean, brewingEquipment: Equipment, grindingEquipment: Equipment?, grindSize: String, doseWeight: Double, yieldWeight: Double, waterTemperature: Double, brewingTime: Int, ratingLevel: Int, ratingDisplay: String, aroma: Int, acidity: Int, sweetness: Int, body: Int, aftertaste: Int, waterQuality: String?, roomTemperature: Double?, roomHumidity: Int?, steps: [BrewingStepItem], flavorTags: [FlavorTag], tagOverlap: Int?, notes: String, createdAt: Date, gadgets: [Equipment]?, gadgetKit: Equipment?) {
        self.id = id
        self.recipeName = recipeName
        self.coffeeBean = coffeeBean
        self.brewingEquipment = brewingEquipment
        self.grindingEquipment = grindingEquipment
        self.grindSize = grindSize
        self.doseWeight = doseWeight
        self.yieldWeight = yieldWeight
        self.waterTemperature = waterTemperature
        self.brewingTime = brewingTime
        self.ratingLevel = ratingLevel
        self.ratingDisplay = ratingDisplay
        self.aroma = aroma
        self.acidity = acidity
        self.sweetness = sweetness
        self.body = body
        self.aftertaste = aftertaste
        self.waterQuality = waterQuality
        self.roomTemperature = roomTemperature
        self.roomHumidity = roomHumidity
        self.steps = steps
        self.flavorTags = flavorTags
        self.tagOverlap = tagOverlap
        self.notes = notes
        self.createdAt = createdAt
        self.gadgets = gadgets
        self.gadgetKit = gadgetKit
    }
}

// 冲煮步骤项目（服务器返回的字典格式）
struct BrewingStepItem: Codable, Identifiable, Equatable, Hashable {
    var id: Int { order }
    var text: String
    var timer: String?
    var order: Int
    var uuid: UUID = UUID() // 添加唯一标识符用于拖拽排序
    
    enum CodingKeys: String, CodingKey {
        case text
        case timer
        case order
    }
    
    // 添加初始化方法
    init(text: String, timer: String?, order: Int) {
        // 直接赋值，不检查空值
        self.text = text
        self.timer = timer
        self.order = order
        self.uuid = UUID()
    }
    
    // 添加可以指定uuid的初始化方法
    init(uuid: UUID, text: String, timer: String?, order: Int) {
        // 直接赋值，不检查空值
        self.text = text
        self.timer = timer
        self.order = order
        self.uuid = uuid
    }
    
    static func == (lhs: BrewingStepItem, rhs: BrewingStepItem) -> Bool {
        lhs.uuid == rhs.uuid
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(uuid)
    }
}

struct BrewingStep: Codable, Identifiable, Equatable {
    let id: Int
    let stepNumber: Int
    let description: String
    let time: Int?
    let waterAmount: Double?
    
    enum CodingKeys: String, CodingKey {
        case id
        case stepNumber = "step_number"
        case description
        case time
        case waterAmount = "water_amount"
    }
    
    static func == (lhs: BrewingStep, rhs: BrewingStep) -> Bool {
        lhs.id == rhs.id &&
        lhs.stepNumber == rhs.stepNumber &&
        lhs.description == rhs.description &&
        lhs.time == rhs.time &&
        lhs.waterAmount == rhs.waterAmount
    }
}

// MARK: - API Response Types
struct APIResponse<T: Codable>: Codable {
    let count: Int
    let next: AnyCodable?
    let previous: AnyCodable?
    let results: [T]
    let dataVersion: Int?
    let totalPages: Int?
    let currentPage: Int?
    
    enum CodingKeys: String, CodingKey {
        case count
        case next
        case previous
        case results
        case dataVersion = "data_version"
        case totalPages = "total_pages"
        case currentPage = "current_page"
        case records // 添加records键以支持BrewingRecordsResponse格式
    }
    
    // 修复JSON解析错误:
    // 这个自定义初始化方法允许APIResponse既可以解码分页格式的响应
    // {"count": 10, "results": [...], "next": ...}
    // 也可以直接解码数组格式的响应 [...]
    // 还可以解码冲煮记录特有的格式 {"records": [...], "total_pages": ..., "current_page": ...}
    init(from decoder: Decoder) throws {
        // 首先尝试将响应解析为数组
        if let singleValueContainer = try? decoder.singleValueContainer(),
           let items = try? singleValueContainer.decode([T].self) {
            // 成功解析为数组，使用数组作为结果
            self.results = items
            self.count = items.count
            self.next = nil
            self.previous = nil
            self.dataVersion = nil
            self.totalPages = nil
            self.currentPage = nil
            return
        }
        
        // 如果不是数组，尝试解析为标准分页格式或冲煮记录特有格式
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 尝试解析冲煮记录特有的格式 {"records": [...], "total_pages": ..., "current_page": ...}
        if container.contains(.records) {
            print("🔍 检测到冲煮记录特有格式响应，使用records字段")
            let records = try container.decode([T].self, forKey: .records)
            self.results = records
            self.count = records.count
            
            // 修复"No calls to throwing functions occur within 'try' expression"警告
            // 使用条件检查取代直接使用try表达式
            if container.contains(.totalPages) {
                self.totalPages = try container.decode(Int.self, forKey: .totalPages)
            } else {
                self.totalPages = nil
            }
            
            if container.contains(.currentPage) {
                self.currentPage = try container.decode(Int.self, forKey: .currentPage)
            } else {
                self.currentPage = nil
            }
            
            self.next = nil
            self.previous = nil
            self.dataVersion = nil
            return
        }
        
        // 尝试解析为标准分页格式
        do {
            let count = try container.decode(Int.self, forKey: .count)
            let next = try container.decodeIfPresent(AnyCodable.self, forKey: .next)
            let previous = try container.decodeIfPresent(AnyCodable.self, forKey: .previous)
            let results = try container.decode([T].self, forKey: .results)
            let dataVersion = try container.decodeIfPresent(Int.self, forKey: .dataVersion)
            let totalPages = try container.decodeIfPresent(Int.self, forKey: .totalPages)
            let currentPage = try container.decodeIfPresent(Int.self, forKey: .currentPage)
            
            // 成功解析后再赋值给属性
            self.count = count
            self.next = next
            self.previous = previous
            self.results = results
            self.dataVersion = dataVersion
            self.totalPages = totalPages
            self.currentPage = currentPage
        } catch {
            // 如果所有格式都解析失败，提供详细的错误信息
            #if DEBUG
            print("❌ API响应解析错误: \(error)")
            if let debugError = error as? DecodingError {
                switch debugError {
                case .typeMismatch(let type, let context):
                    print("  类型不匹配: 期望 \(type)，位置: \(String(describing: context.codingPath.map { $0.stringValue }.joined(separator: ".")))")
                case .valueNotFound(let type, let context):
                    print("  值未找到: 期望 \(type)，位置: \(String(describing: context.codingPath.map { $0.stringValue }.joined(separator: ".")))")
                case .keyNotFound(let key, let context):
                    print("  键未找到: \(key)，位置: \(String(describing: context.codingPath.map { $0.stringValue }.joined(separator: ".")))")
                default:
                    print("  其他解码错误: \(debugError)")
                }
            }
            #endif
            
            // 修复"No calls to throwing functions occur within 'try' expression"警告
            // 直接创建一个新的singleValueContainer，确保try表达式内确实调用了可能抛出异常的函数
            throw DecodingError.dataCorruptedError(
                in: try decoder.singleValueContainer(),
                debugDescription: "无法将响应解析为分页格式、数组格式或冲煮记录特有格式：\(error.localizedDescription)"
            )
        }
    }
    
    // 添加encode方法以符合Encodable协议
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(count, forKey: .count)
        try container.encodeIfPresent(next, forKey: .next)
        try container.encodeIfPresent(previous, forKey: .previous)
        try container.encode(results, forKey: .results)
        try container.encodeIfPresent(dataVersion, forKey: .dataVersion)
        try container.encodeIfPresent(totalPages, forKey: .totalPages)
        try container.encodeIfPresent(currentPage, forKey: .currentPage)
        
        // 注意：我们不编码records字段，因为它只用于解码。当编码时，我们始终使用results字段。
    }
}

// MARK: - Flavor Tag
struct FlavorTag: Codable, Identifiable, Hashable {
    let id: Int
    let name: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
    }
    
    // 添加增强的自定义解码器
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        do {
            id = try container.decode(Int.self, forKey: .id)
        } catch {
            // 打印调试信息
            print("❌ FlavorTag ID字段解码错误: \(error)")
            
            // 检查是否在嵌套对象中
            let pathDescription = decoder.codingPath.map { $0.stringValue }.joined(separator: ".")
            print("📋 编码路径: \(pathDescription)")
            
            if pathDescription.contains("flavor_tags") || pathDescription.contains("results") {
                print("⚠️ 在嵌套结构中发现风味标签ID缺失，使用默认ID")
                // 使用随机ID作为默认值
                id = Int.random(in: 500000...599999)
            } else {
                throw error
            }
        }
        
        name = try container.decode(String.self, forKey: .name)
    }
    
    // 添加初始化方法
    init(id: Int, name: String) {
        self.id = id
        self.name = name
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: FlavorTag, rhs: FlavorTag) -> Bool {
        lhs.id == rhs.id
    }
}

struct ModelsEmptyResponse: Codable {}

// MARK: - Calendar Models
struct BeanCalendarData: Codable {
    let dailyBeans: [String: [BeanUsageData]]
    let year: Int
    let month: Int
    let daysInMonth: Int
    
    enum CodingKeys: String, CodingKey {
        case dailyBeans = "daily_beans"
        case year
        case month
        case daysInMonth = "days_in_month"
    }
}

struct BeanUsageData: Identifiable, Codable {
    let id: Int
    let name: String
    let roaster: String?
    let brewCount: Int
    let rating: Double?
    let roastLevelDisplay: String
    let origin: String?
    let region: String?
    let process: String?
    let variety: String?
    let type: String
    let typeDisplay: String
    let isDecaf: Bool
    let isFinished: Bool
    let isArchived: Bool
    let stockStatus: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case roaster
        case brewCount = "brew_count"
        case rating
        case roastLevelDisplay = "roast_level_display"
        case origin
        case region
        case process
        case variety
        case type
        case typeDisplay = "type_display"
        case isDecaf = "is_decaf"
        case isFinished = "is_finished"
        case isArchived = "is_archived"
        case stockStatus = "stock_status"
    }
}

struct CalendarDayData {
    let date: Date
    let beanCount: Int
    let beans: [BeanUsageData]
}

// MARK: - User Model
struct UserData: Codable {
    let id: Int
    let username: String
    let email: String
    let token: String?
}

// MARK: - API Response
struct BrewingRecordsResponse: Codable {
    let records: [BrewingRecord]
    let totalPages: Int
    let currentPage: Int
    
    enum CodingKeys: String, CodingKey {
        case records
        case totalPages = "total_pages"
        case currentPage = "current_page"
    }
}

// MARK: - Supporting Types
enum BrewingTimeRange: String, CaseIterable, Identifiable {
    case all = "全部"
    case today = "今天"
    case week = "本周"
    case month = "本月"
    case year = "今年"
    
    var id: String { rawValue }
    var title: String { rawValue }
}

enum BrewingSortOption: String, CaseIterable, Identifiable {
    case dateDesc = "时间降序"
    case dateAsc = "时间升序"
    case ratingDesc = "评分降序"
    case ratingAsc = "评分升序"
    
    var id: String { rawValue }
    var title: String { rawValue }
}

// MARK: - Preview Data
#if DEBUG
extension BrewingRecord {
    static var preview: BrewingRecord {
        BrewingRecord(
            id: UUID().hashValue,
            recipeName: "测试配方",
            coffeeBean: CoffeeBean.preview,
            brewingEquipment: Equipment.previewBrewing,
            grindingEquipment: Equipment.previewGrinding,
            grindSize: "20",
            doseWeight: 15.0,
            yieldWeight: 250.0,
            waterTemperature: 93.0,
            brewingTime: 180,
            ratingLevel: 4,
            ratingDisplay: "⭐️",
            aroma: 4,
            acidity: 3,
            sweetness: 4,
            body: 3,
            aftertaste: 4,
            waterQuality: "矿泉水",
            roomTemperature: 25.0,
            roomHumidity: 60,
            steps: [
                BrewingStepItem(text: "注水60g，搅拌均匀", timer: "0:00", order: 1),
                BrewingStepItem(text: "30秒后注水至150g", timer: "0:30", order: 2),
                BrewingStepItem(text: "1分钟后注水至250g", timer: "1:00", order: 3)
            ],
            flavorTags: [FlavorTag(id: 1, name: "柑橘"), FlavorTag(id: 2, name: "焦糖"), FlavorTag(id: 3, name: "巧克力")],
            tagOverlap: 75,
            notes: "这是一次测试记录",
            createdAt: Date(),
            gadgets: [Equipment.previewGadget],
            gadgetKit: nil
        )
    }
}

extension CoffeeBean {
    static var preview: CoffeeBean {
        CoffeeBean(
            id: UUID().hashValue,
            name: "埃塞俄比亚耶加雪菲",
            type: "SINGLE",
            typeDisplay: "单品",
            roaster: "某某烘焙",
            roastLevel: 4,
            roastLevelDisplay: "中烘",
            origin: "埃塞俄比亚",
            region: "耶加雪菲",
            finca: "花蝴蝶庄园",
            variety: "埃塞原生种",
            process: "水洗",
            barcode: "6901234567890",
            notes: "浓郁的花香和柑橘风味，酸质明亮",
            bagWeight: 200.0,
            bagRemain: 150.0,
            purchasePrice: 128.0,
            roastDate: Date().addingTimeInterval(-14 * 86400),
            createdAt: Date().addingTimeInterval(-15 * 86400),
            deletedAt: nil,
            isFavorite: true,
            isArchived: false,
            isDeleted: false,
            isDecaf: false,
            altitudeType: "SINGLE",
            altitudeSingle: 1800,
            altitudeMin: nil,
            altitudeMax: nil,
            restPeriodMin: 10,
            restPeriodMax: 30,
            restPeriodProgress: 15,
            stockStatus: "充足",
            avgRating: 4.5,
            tasteNotes: ["花香", "柑橘", "巧克力"],
            blendComponents: nil,
            occurrences: nil,
            usageCount: nil,
            lastUsed: nil,
            daysSinceLastUse: nil,
            mostUsedEquipment: nil,
            remainingUses: nil,
            occurrencesCount: nil,
            avgRepurchaseInterval: nil,
            dimensionsAvg: nil,
            tastingCount: nil,
            uniqueFlavorTags: nil,
            flavorAccuracy: nil,
            initialBagWeight: 200.0,
            initialBagRemain: 200.0,
            initialPurchasePrice: 128.0,
            initialRoastDate: Date().addingTimeInterval(-15 * 86400),
            initialCreatedAt: Date().addingTimeInterval(-15 * 86400),
            initialRestPeriodMin: 10,
            initialRestPeriodMax: 30
        )
    }
    
    static var previewBlend: CoffeeBean {
        CoffeeBean(
            id: UUID().hashValue,
            name: "经典拼配",
            type: "BLEND",
            typeDisplay: "拼配",
            roaster: "某某烘焙",
            roastLevel: 5,
            roastLevelDisplay: "中深烘",
            origin: nil,
            region: nil,
            finca: nil,
            variety: nil,
            process: nil,
            barcode: "6901234567891",
            notes: "平衡的口感，坚果与巧克力风味",
            bagWeight: 250.0,
            bagRemain: 200.0,
            purchasePrice: 158.0,
            roastDate: Date().addingTimeInterval(-7 * 86400),
            createdAt: Date().addingTimeInterval(-8 * 86400),
            deletedAt: nil,
            isFavorite: false,
            isArchived: false,
            isDeleted: false,
            isDecaf: false,
            altitudeType: "SINGLE",
            altitudeSingle: nil,
            altitudeMin: nil,
            altitudeMax: nil,
            restPeriodMin: 5,
            restPeriodMax: 10,
            restPeriodProgress: 70,
            stockStatus: "充足",
            avgRating: nil,
            tasteNotes: nil,
            blendComponents: [
                BlendComponent.previewComponent1,
                BlendComponent.previewComponent2
            ],
            occurrences: nil,
            usageCount: nil,
            lastUsed: nil,
            daysSinceLastUse: nil,
            mostUsedEquipment: nil,
            remainingUses: nil,
            occurrencesCount: nil,
            avgRepurchaseInterval: nil,
            dimensionsAvg: nil,
            tastingCount: nil,
            uniqueFlavorTags: nil,
            flavorAccuracy: nil,
            initialBagWeight: 250.0,
            initialBagRemain: 250.0,
            initialPurchasePrice: 158.0,
            initialRoastDate: Date().addingTimeInterval(-8 * 86400),
            initialCreatedAt: Date().addingTimeInterval(-8 * 86400),
            initialRestPeriodMin: 5,
            initialRestPeriodMax: 10
        )
    }
    
    static var previewWithOccurrences: CoffeeBean {
        CoffeeBean(
            id: UUID().hashValue,
            name: "肯尼亚AA",
            type: "SINGLE",
            typeDisplay: "单品",
            roaster: "某某烘焙",
            roastLevel: 3,
            roastLevelDisplay: "浅中烘",
            origin: "肯尼亚",
            region: "基里尼亚加",
            finca: "AA庄园",
            variety: "SL28",
            process: "水洗",
            barcode: "6901234567892",
            notes: "明亮的酸质，黑醋栗风味",
            bagWeight: 200.0,
            bagRemain: 50.0,
            purchasePrice: 168.0,
            roastDate: Date().addingTimeInterval(-5 * 86400),
            createdAt: Date().addingTimeInterval(-6 * 86400),
            deletedAt: nil,
            isFavorite: true,
            isArchived: false,
            isDeleted: false,
            isDecaf: false,
            altitudeType: "RANGE",
            altitudeSingle: nil,
            altitudeMin: 1700,
            altitudeMax: 1900,
            restPeriodMin: 10,
            restPeriodMax: 21,
            restPeriodProgress: 50,
            stockStatus: "低库存",
            avgRating: nil,
            tasteNotes: nil,
            blendComponents: nil,
            occurrences: [
                BeanOccurrence.previewOccurrence1,
                BeanOccurrence.previewOccurrence2
            ],
            usageCount: nil,
            lastUsed: nil,
            daysSinceLastUse: nil,
            mostUsedEquipment: nil,
            remainingUses: nil,
            occurrencesCount: nil,
            avgRepurchaseInterval: nil,
            dimensionsAvg: nil,
            tastingCount: nil,
            uniqueFlavorTags: nil,
            flavorAccuracy: nil,
            initialBagWeight: 200.0,
            initialBagRemain: 200.0,
            initialPurchasePrice: 168.0,
            initialRoastDate: Date().addingTimeInterval(-6 * 86400),
            initialCreatedAt: Date().addingTimeInterval(-6 * 86400),
            initialRestPeriodMin: 10,
            initialRestPeriodMax: 21
        )
    }
}

extension BlendComponent {
    static var previewComponent1: BlendComponent {
        return BlendComponent(
            id: UUID().hashValue,
            coffeeBeanId: UUID().hashValue,
            origin: "埃塞俄比亚",
            region: "西达摩",
            finca: nil,
            variety: "原生种",
            process: "水洗",
            roastLevel: 4,
            roastLevelDisplay: "中烘",
            blendRatio: 60.0,
            order: 1,
            altitudeType: "SINGLE",
            altitudeSingle: 1850,
            altitudeMin: nil,
            altitudeMax: nil
        )
    }
    
    static var previewComponent2: BlendComponent {
        return BlendComponent(
            id: UUID().hashValue,
            coffeeBeanId: UUID().hashValue,
            origin: "巴西",
            region: "南米纳斯",
            finca: "圣安东尼奥庄园",
            variety: "波旁",
            process: "日晒",
            roastLevel: 5,
            roastLevelDisplay: "中深烘",
            blendRatio: 40.0,
            order: 2,
            altitudeType: "RANGE",
            altitudeSingle: nil,
            altitudeMin: 1000,
            altitudeMax: 1200
        )
    }
}

extension BeanOccurrence {
    static var previewOccurrence1: BeanOccurrence {
        return BeanOccurrence(
            id: UUID().hashValue,
            coffeeBeanId: UUID().hashValue,
            bagWeight: 200.0,
            bagRemain: 0.0,
            purchasePrice: 158.0,
            roastDate: Date().addingTimeInterval(-65 * 86400),
            createdAt: Date().addingTimeInterval(-66 * 86400),
            restPeriodMin: 7,
            restPeriodMax: 14
        )
    }
    
    static var previewOccurrence2: BeanOccurrence {
        return BeanOccurrence(
            id: UUID().hashValue,
            coffeeBeanId: UUID().hashValue,
            bagWeight: 250.0,
            bagRemain: 0.0,
            purchasePrice: 178.0,
            roastDate: Date().addingTimeInterval(-30 * 86400),
            createdAt: Date().addingTimeInterval(-31 * 86400),
            restPeriodMin: 10,
            restPeriodMax: 21
        )
    }
}

extension Equipment {
    static var previewBrewing: Equipment {
        Equipment(
            id: Int.random(in: 1...Int.max),
            name: "Hario V60",
            type: Equipment.typeBrewingEquipment,
            typeDisplay: "冲煮器具",
            brand: "Hario",
            model: "VDG-02",
            description: "这是一个测试器具",
            purchaseDate: Date(),
            purchasePrice: 68.0,
            notes: "经典的日式滤杯，适合清淡风味",
            createdAt: Date(),
            updatedAt: Date(),
            isDeleted: false,
            isArchived: false,
            isActive: true,
            isFavorite: true,
            isDisabled: false,
            brewMethod: "pour_over",
            brewMethodDisplay: "手冲",
            gadgetComponents: nil,
            grindSizePreset: nil,
            grinderPurpose: nil,
            grinderPurposeDisplay: nil,
            usageCount: 25,
            lastUsed: Date().addingTimeInterval(-2 * 24 * 3600),
            breakEvenProgress: 73.5,
            depreciationRate: 85.2
        )
    }
    
    static var previewGrinding: Equipment {
        Equipment(
            id: Int.random(in: 1...Int.max),
            name: "1Zpresso K-Plus",
            type: Equipment.typeGrindingEquipment,
            typeDisplay: "磨豆机",
            brand: "1Zpresso",
            model: "K-Plus",
            description: "这是一个测试磨豆机",
            purchaseDate: Date(),
            purchasePrice: 1280.0,
            notes: "高性能手摇磨豆机，研磨均匀度好",
            createdAt: Date(),
            updatedAt: Date(),
            isDeleted: false,
            isArchived: false,
            isActive: true,
            isFavorite: true,
            isDisabled: false,
            brewMethod: nil,
            brewMethodDisplay: nil,
            gadgetComponents: nil,
            grindSizePreset: "18刻度",
            grinderPurpose: "ALL_PURPOSE",
            grinderPurposeDisplay: "通用",
            usageCount: 45,
            lastUsed: Date().addingTimeInterval(-1 * 24 * 3600),
            breakEvenProgress: 42.8,
            depreciationRate: 92.5
        )
    }
    
    static var previewGadget: Equipment {
        Equipment(
            id: Int.random(in: 1...Int.max),
            name: "咖啡秤",
            type: Equipment.typeGadget,
            typeDisplay: "小工具",
            brand: "TimeMore",
            model: "Black Mirror",
            description: "这是一个测试小工具",
            purchaseDate: Date(),
            purchasePrice: 299.0,
            notes: "0.1g高精度咖啡秤，支持计时功能",
            createdAt: Date(),
            updatedAt: Date(),
            isDeleted: false,
            isArchived: false,
            isActive: true,
            isFavorite: false,
            isDisabled: false,
            brewMethod: nil,
            brewMethodDisplay: nil,
            gadgetComponents: nil,
            grindSizePreset: nil,
            grinderPurpose: nil,
            grinderPurposeDisplay: nil,
            usageCount: 60,
            lastUsed: Date(),
            breakEvenProgress: 65.3,
            depreciationRate: 88.0
        )
    }
    
    static var previewGadgetKit: Equipment {
        Equipment(
            id: Int.random(in: 1...Int.max),
            name: "基础手冲套装",
            type: Equipment.typeGadgetKit,
            typeDisplay: "小工具组合",
            brand: nil,
            model: nil,
            description: "包含基础手冲所需的所有工具",
            purchaseDate: nil,
            purchasePrice: nil,
            notes: "组合了咖啡秤、温度计等基础工具",
            createdAt: Date(),
            updatedAt: Date(),
            isDeleted: false,
            isArchived: false,
            isActive: true,
            isFavorite: false,
            isDisabled: false,
            brewMethod: nil,
            brewMethodDisplay: nil,
            gadgetComponents: [
                ["id": 1, "name": "温度计", "brand": "Timemore"],
                ["id": 2, "name": "滤纸", "brand": "Hario"],
                ["id": 3, "name": "分水器", "brand": "Brewista"]
            ],
            grindSizePreset: nil,
            grinderPurpose: nil,
            grinderPurposeDisplay: nil,
            usageCount: 12,
            lastUsed: Date().addingTimeInterval(-5 * 24 * 3600),
            breakEvenProgress: nil,
            depreciationRate: nil
        )
    }
}

// 添加EquipmentSummary结构体，用于mostUsedEquipment字段
struct EquipmentSummary: Codable, Hashable {
    let id: Int?
    let name: String?
    let type: String?
    let brand: String?
    let model: String?
    let brewMethod: String?
    let brewMethodDisplay: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case type
        case brand
        case model
        case brewMethod = "brew_method"
        case brewMethodDisplay = "brew_method_display"
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: EquipmentSummary, rhs: EquipmentSummary) -> Bool {
        lhs.id == rhs.id
    }
}

// 辅助方法：将混合类型的字典转换为[String: Double]
private func convertToDimensionsDict(_ mixedDict: [String: Any]) -> [String: Double]? {
    var result = [String: Double]()
    
    for (key, value) in mixedDict {
        if let doubleValue = value as? Double {
            result[key] = doubleValue
        } else if let stringValue = value as? String, let doubleValue = Double(stringValue) {
            result[key] = doubleValue
        } else if let intValue = value as? Int {
            result[key] = Double(intValue)
        }
    }
    
    return result.isEmpty ? nil : result
}
#endif 
