import SwiftUI

// 主题定义
enum ThemeType: String, CaseIterable, Codable {
    case latte = "拿铁"
    case espresso = "浓缩"
    case matcha = "抹茶"
    case caramel = "焦糖"
    case mocha = "摩卡"
    case coldBrew = "冷萃"
    case americano = "美式"
    case macchiato = "玛奇朵"
    case system = "跟随系统"
}

// 主题颜色属性
struct ThemeColors: Codable {
    // 文本颜色
    var primaryTextColor: Color
    var detailTextColor: Color
    var archivedTextColor: Color
    var functionTextColor: Color
    var secondaryTextColor: Color
    var linkTextColor: Color
    var noteTextColor: Color
    var errorTextColor: Color
    
    // 强调色
    var primaryAccentColor: Color
    
    // 背景色
    var primaryBgColor: Color
    var secondaryBgColor: Color
    var navbarBgColor: Color
    var focusBgColor: Color
    
    // 用于编码和解码 Color
    enum CodingKeys: String, CodingKey {
        case primaryTextColor, detailTextColor, archivedTextColor, functionTextColor
        case secondaryTextColor, linkTextColor, noteTextColor, primaryAccentColor
        case primaryBgColor, secondaryBgColor, navbarBgColor, focusBgColor
        case errorTextColor
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        primaryTextColor = try Color(hex: container.decode(String.self, forKey: .primaryTextColor))
        detailTextColor = try Color(hex: container.decode(String.self, forKey: .detailTextColor))
        archivedTextColor = try Color(hex: container.decode(String.self, forKey: .archivedTextColor))
        functionTextColor = try Color(hex: container.decode(String.self, forKey: .functionTextColor))
        secondaryTextColor = try Color(hex: container.decode(String.self, forKey: .secondaryTextColor))
        linkTextColor = try Color(hex: container.decode(String.self, forKey: .linkTextColor))
        noteTextColor = try Color(hex: container.decode(String.self, forKey: .noteTextColor))
        
        primaryAccentColor = try Color(hex: container.decode(String.self, forKey: .primaryAccentColor))
        
        primaryBgColor = try Color(hex: container.decode(String.self, forKey: .primaryBgColor))
        secondaryBgColor = try Color(hex: container.decode(String.self, forKey: .secondaryBgColor))
        navbarBgColor = try Color(hex: container.decode(String.self, forKey: .navbarBgColor))
        focusBgColor = try Color(hex: container.decode(String.self, forKey: .focusBgColor))
        
        // 如果不存在errorTextColor（向后兼容），则使用默认值
        if container.contains(.errorTextColor) {
            errorTextColor = try Color(hex: container.decode(String.self, forKey: .errorTextColor))
        } else {
            errorTextColor = Color("ErrorColor")
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(primaryTextColor.toHex(), forKey: .primaryTextColor)
        try container.encode(detailTextColor.toHex(), forKey: .detailTextColor)
        try container.encode(archivedTextColor.toHex(), forKey: .archivedTextColor)
        try container.encode(functionTextColor.toHex(), forKey: .functionTextColor)
        try container.encode(secondaryTextColor.toHex(), forKey: .secondaryTextColor)
        try container.encode(linkTextColor.toHex(), forKey: .linkTextColor)
        try container.encode(noteTextColor.toHex(), forKey: .noteTextColor)
        
        try container.encode(primaryAccentColor.toHex(), forKey: .primaryAccentColor)
        
        try container.encode(primaryBgColor.toHex(), forKey: .primaryBgColor)
        try container.encode(secondaryBgColor.toHex(), forKey: .secondaryBgColor)
        try container.encode(navbarBgColor.toHex(), forKey: .navbarBgColor)
        try container.encode(focusBgColor.toHex(), forKey: .focusBgColor)
        
        try container.encode(errorTextColor.toHex(), forKey: .errorTextColor)
    }
    
    // 标准初始化器
    init(
        primaryTextColor: Color,
        detailTextColor: Color,
        archivedTextColor: Color,
        functionTextColor: Color,
        secondaryTextColor: Color,
        linkTextColor: Color,
        noteTextColor: Color,
        errorTextColor: Color,
        primaryAccentColor: Color,
        primaryBgColor: Color,
        secondaryBgColor: Color,
        navbarBgColor: Color,
        focusBgColor: Color
    ) {
        self.primaryTextColor = primaryTextColor
        self.detailTextColor = detailTextColor
        self.archivedTextColor = archivedTextColor
        self.functionTextColor = functionTextColor
        self.secondaryTextColor = secondaryTextColor
        self.linkTextColor = linkTextColor
        self.noteTextColor = noteTextColor
        self.errorTextColor = errorTextColor
        self.primaryAccentColor = primaryAccentColor
        self.primaryBgColor = primaryBgColor
        self.secondaryBgColor = secondaryBgColor
        self.navbarBgColor = navbarBgColor
        self.focusBgColor = focusBgColor
    }
}

// 颜色扩展，用于十六进制颜色转换
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    func toHex() -> String {
        guard let components = UIColor(self).cgColor.components, components.count >= 3 else {
            return "000000"
        }
        
        let r = Float(components[0])
        let g = Float(components[1])
        let b = Float(components[2])
        
        return String(format: "%02lX%02lX%02lX", lroundf(r * 255), lroundf(g * 255), lroundf(b * 255))
    }
} 