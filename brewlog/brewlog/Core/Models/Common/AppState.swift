import Foundation
import SwiftUI

// 内部导航协议 - 用于安全的应用内导航
protocol InternalNavigationDelegate: AnyObject {
    func navigateToEquipment(id: Int)
    func navigateToBrewLog(id: Int)
    func navigateToBean(id: Int)
    func navigateToAccountSettings()
    func navigateToEquipmentList()
    func navigateToBrewLogList()
    func navigateToBeanList()
}

class AppState: ObservableObject {
    static let shared = AppState()

    @Published var selectedTab: Tab
    @Published var tabOrder: [Tab]
    @Published var moreItems: [Tab] = []

    // 添加用于导航到特定咖啡豆详情的属性
    @Published var selectedBeanId: Int?

    // 添加冲煮记录详情导航属性
    @Published var selectedBrewLogId: Int?

    // 添加待处理的冲煮记录导航请求
    @Published var pendingBrewLogNavigation: Int?

    // 添加设备详情导航属性
    @Published var selectedEquipmentId: Int?

    // 添加账号设置导航标志
    @Published var shouldNavigateToAccountSettings: Bool = false

    // 添加显示添加冲煮记录页面的标志
    @Published var shouldShowAddBrewLog: Bool = false

    // 添加复制上次记录的标志
    @Published var shouldCopyLastRecord: Bool = false

    // 添加应用初始化完成标志
    @Published var isAppInitialized: Bool = false

    private let tabOrderKey = "tab_order"
    private let moreItemsKey = "more_items"
    private let userDefaults = UserDefaults.standard

    enum Tab: String, CaseIterable {
        case brewLog = "冲煮记录"
        case equipment = "设备"
        case beans = "咖啡豆"
        case recipes = "配方册"
        case hindsight = "后见之明"
        case heatmap = "冲煮热图"
        case beanCalendar = "养豆日历"
        case more = "更多"

        var icon: Image {
            switch self {
            case .brewLog:
                return Image("record.symbols")
            case .equipment:
                return Image("equipment.symbols")
            case .beans:
                return Image("paperbag.symbols")
            case .recipes:
                return Image("recipes.symbols")
            case .hindsight:
                return Image("hindsight.symbols")
            case .heatmap:
                return Image("heatmap.symbols")
            case .beanCalendar:
                return Image("calendar.symbols")
            case .more:
                return Image("more.symbols")
            }
        }

        // 保留系统图标作为备选
        var systemIcon: String {
            switch self {
            case .brewLog:
                return "note.text"
            case .equipment:
                return "gearshape.2.fill"
            case .beans:
                return "leaf.fill"
            case .recipes:
                return "book.fill"
            case .hindsight:
                return "clock.arrow.circlepath"
            case .heatmap:
                return "chart.bar.fill"
            case .beanCalendar:
                return "calendar"
            case .more:
                return "ellipsis"
            }
        }

        var title: String {
            return self.rawValue
        }

        static var mainTabs: [Tab] {
            [.brewLog, .equipment, .beans, .recipes, .hindsight, .heatmap, .beanCalendar]
        }
    }

    private init() {
        // 先初始化存储属性
        var initialTabOrder = [Tab.brewLog, .equipment, .beans, .heatmap]

        // 加载 tab 顺序
        if let savedOrder = userDefaults.array(forKey: tabOrderKey) as? [String],
           let tabs = savedOrder.compactMap({ Tab(rawValue: $0) }) as? [Tab],
           !tabs.isEmpty {
            initialTabOrder = tabs
        }

        // 初始化存储属性
        tabOrder = initialTabOrder
        selectedTab = initialTabOrder.first ?? .brewLog

        // 加载更多项目
        if let savedMoreItems = userDefaults.array(forKey: moreItemsKey) as? [String] {
            moreItems = savedMoreItems.compactMap { Tab(rawValue: $0) }

            // 确保不会有重复项，移除任何已存在于tabOrder中的项
            moreItems.removeAll { tab in
                tabOrder.contains(tab)
            }
        } else {
            // 默认将配方册、后见之明和日历放在更多中
            moreItems = [.recipes, .hindsight, .beanCalendar]
        }

        // 设置一个延迟，将应用标记为已初始化
        // 这样可以确保在应用完全加载后再处理URL Scheme
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            self.isAppInitialized = true
        }
    }

    func saveTabOrder() {
        let orderStrings = tabOrder.map { $0.rawValue }
        userDefaults.set(orderStrings, forKey: tabOrderKey)

        let moreStrings = moreItems.map { $0.rawValue }
        userDefaults.set(moreStrings, forKey: moreItemsKey)
    }

    func moveTab(from source: IndexSet, to destination: Int) {
        tabOrder.move(fromOffsets: source, toOffset: destination)
        saveTabOrder()
    }

    func swapTab(_ tab: Tab, with newTab: Tab) {
        if let index = tabOrder.firstIndex(of: tab) {
            tabOrder[index] = newTab
            moreItems.removeAll { $0 == newTab }
            moreItems.append(tab)
            saveTabOrder()
        }
    }

    func resetTabOrder() {
        tabOrder = [.brewLog, .equipment, .beans, .heatmap]
        moreItems = [.recipes, .hindsight, .beanCalendar]
        saveTabOrder()
    }

    var availableTabs: [Tab] {
        Tab.mainTabs.filter { !tabOrder.contains($0) && !moreItems.contains($0) }
    }
}

// MARK: - InternalNavigationDelegate Implementation
extension AppState: InternalNavigationDelegate {
    func navigateToEquipment(id: Int) {
        selectedTab = .equipment
        selectedEquipmentId = id
        print("🔗 内部导航到设备详情页: ID \(id)")
    }

    func navigateToBrewLog(id: Int) {
        selectedTab = .brewLog
        // 延迟设置导航ID，确保tab切换完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.selectedBrewLogId = id
            print("🔗 内部导航到冲煮记录详情页: ID \(id)")
        }
    }

    func navigateToBean(id: Int) {
        selectedTab = .beans
        selectedBeanId = id
        print("🔗 内部导航到咖啡豆详情页: ID \(id)")
    }

    func navigateToAccountSettings() {
        selectedTab = .more
        shouldNavigateToAccountSettings = true
        print("🔗 内部导航到账号设置页")
    }

    func navigateToEquipmentList() {
        selectedTab = .equipment
        print("🔗 内部导航到设备列表页")
    }

    func navigateToBrewLogList() {
        selectedTab = .brewLog
        print("🔗 内部导航到冲煮记录列表页")
    }

    func navigateToBeanList() {
        selectedTab = .beans
        print("🔗 内部导航到咖啡豆列表页")
    }
}
