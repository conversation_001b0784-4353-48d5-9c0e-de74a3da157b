import Foundation

// MARK: - Hindsight统计API
extension APIService {

    // 异步获取API数据的泛型方法
    func hindsightGet<T: Decodable>(_ endpoint: String, parameters: [String: String]? = nil) async throws -> T {
        guard let baseURLString = await getBaseURL() else {
            throw APIError.invalidURL
        }

        var urlComponents = URLComponents(string: "\(baseURLString)\(endpoint)")

        // 添加查询参数
        if let parameters = parameters, !parameters.isEmpty {
            urlComponents?.queryItems = parameters.map { URLQueryItem(name: $0, value: $1) }
        }

        guard let url = urlComponents?.url else {
            throw APIError.invalidURL
        }
        
        // 打印完整请求URL和参数
        print("hindsightGet请求: \(url.absoluteString)")
        if let parameters = parameters {
            print("请求参数: \(parameters)")
        }

        var request = URLRequest(url: url)
        request.httpMethod = "GET"

        // 添加认证信息 - 使用当前实例的isLoggedIn检查是否已登录
        if self.isLoggedIn {
            // 从KeychainManager获取令牌
            if let token = try? KeychainManager.load(key: "auth_token") {
                request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            }
        }

        // 添加语言和客户端版本头
        request.addValue("ios", forHTTPHeaderField: "X-Client-Type")
        if let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            request.addValue(appVersion, forHTTPHeaderField: "X-App-Version")
        }

        do {
            // 发送请求并获取响应
            let (data, response) = try await URLSession.shared.data(for: request)

            // DEBUG: 打印原始JSON数据
            #if DEBUG
            if let jsonString = String(data: data, encoding: .utf8) {
                print("hindsightGet收到的JSON数据: \(jsonString)")
            }
            #endif

            // 验证HTTP响应状态码
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }

            // 处理HTTP错误
            guard (200...299).contains(httpResponse.statusCode) else {
                // 尝试解析错误消息
                if let errorInfo = try? JSONDecoder().decode([String: String].self, from: data),
                   let errorMessage = errorInfo["error"] {
                    throw APIError.serverErrorSimple(errorMessage)
                } else {
                    throw APIError.httpError(httpResponse.statusCode)
                }
            }

            // 解码响应数据
            let decoder = JSONDecoder()
            // 不使用自动 snake_case 转换，因为 HindsightResponse 已经定义了 CodingKeys
            // decoder.keyDecodingStrategy = .convertFromSnakeCase

            // 添加日期解码策略
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
            dateFormatter.locale = Locale(identifier: "en_US_POSIX")
            dateFormatter.timeZone = TimeZone(secondsFromGMT: 0)

            decoder.dateDecodingStrategy = .custom { decoder in
                let container = try decoder.singleValueContainer()

                if let dateString = try? container.decode(String.self) {
                    // 尝试不同的日期格式
                    let formats = [
                        "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
                        "yyyy-MM-dd'T'HH:mm:ssZ",
                        "yyyy-MM-dd HH:mm:ss",
                        "yyyy-MM-dd"
                    ]

                    for format in formats {
                        dateFormatter.dateFormat = format
                        if let date = dateFormatter.date(from: dateString) {
                            return date
                        }
                    }

                    throw DecodingError.dataCorruptedError(
                        in: container,
                        debugDescription: "无法解析日期: \(dateString)"
                    )
                }

                // 如果是时间戳
                if let timestamp = try? container.decode(Double.self) {
                    return Date(timeIntervalSince1970: timestamp)
                }

                throw DecodingError.dataCorruptedError(
                    in: container,
                    debugDescription: "预期为日期格式的数据"
                )
            }

            do {
                let result = try decoder.decode(T.self, from: data)
                return result
            } catch {
                // DEBUG: 详细的解析错误信息
                #if DEBUG
                print("解析错误: \(error)")
                if let decodingError = error as? DecodingError {
                    switch decodingError {
                    case .keyNotFound(let key, let context):
                        print("未找到键: \(key), 路径: \(context.codingPath)")
                    case .valueNotFound(let type, let context):
                        print("未找到值, 类型: \(type), 路径: \(context.codingPath)")
                    case .typeMismatch(let type, let context):
                        print("类型不匹配, 期望类型: \(type), 路径: \(context.codingPath)")
                    case .dataCorrupted(let context):
                        print("数据已损坏: \(context.debugDescription)")
                    @unknown default:
                        print("未知解码错误")
                    }
                }
                #endif
                throw APIError.decodingError(error as? DecodingError ?? DecodingError.dataCorrupted(DecodingError.Context(codingPath: [], debugDescription: "未知解码错误")))
            }
        } catch let error as DecodingError {
            // 处理解码错误
            throw APIError.decodingError(error)
        } catch let error as APIError {
            // 直接抛出API错误
            throw error
        } catch {
            // 处理其他错误
            throw APIError.networkError(error)
        }
    }

    // 获取baseURL（异步方法）
    private func getBaseURL() async -> String? {
        return APIService.environment.baseURL
    }

    // 获取后见之明数据
    func getHindsightData(year: Int? = nil, timeRange: TimeRange? = nil, forceRefresh: Bool = false) async throws -> HindsightResponse {
        var parameters: [String: String] = [:]
        
        // 详细记录参数
        print("getHindsightData被调用 - 参数: year=\(year.map { "\($0)" } ?? "nil"), timeRange=\(timeRange?.rawValue ?? "nil"), forceRefresh=\(forceRefresh)")
        
        // 检查参数是否互斥
        if year != nil && timeRange != nil {
            print("警告: 同时提供了year和timeRange参数，这可能导致服务器端优先使用其中一个")
        }
        
        if let year = year {
            parameters["year"] = "\(year)"
            print("添加year参数: \(year)")
        }
        
        if let timeRange = timeRange {
            parameters["time_range"] = timeRange.rawValue
            print("添加time_range参数: \(timeRange.rawValue)")
        }
        
        // 添加强制刷新参数
        if forceRefresh {
            // 添加一个随机参数以避免缓存
            parameters["_refresh"] = UUID().uuidString
            print("添加强制刷新参数")
        }
        
        // 调试输出请求参数
        print("发送Hindsight请求，最终参数: \(parameters)")
        
        let response: HindsightResponse = try await hindsightGet("/ios/api/brewlog/hindsight/", parameters: parameters)
        
        // 检查响应中的timeRange字段
        print("响应中的timeRange: \(response.timeRange ?? "nil")")
        
        return response
    }

    // 调试方法：获取原始响应数据
    func getHindsightRawResponse(year: Int? = nil, timeRange: TimeRange? = nil) async throws -> Data {
        guard let baseURLString = await getBaseURL() else {
            throw APIError.invalidURL
        }

        var urlComponents = URLComponents(string: "\(baseURLString)/ios/api/brewlog/hindsight/")
        
        // 添加查询参数
        var parameters: [String: String] = [:]
        if let year = year {
            parameters["year"] = "\(year)"
        }
        if let timeRange = timeRange {
            parameters["time_range"] = timeRange.rawValue
        }
        
        if !parameters.isEmpty {
            urlComponents?.queryItems = parameters.map { URLQueryItem(name: $0, value: $1) }
        }
        
        guard let url = urlComponents?.url else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // 添加认证信息
        if self.isLoggedIn {
            if let token = try? KeychainManager.load(key: "auth_token") {
                request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            }
        }
        
        // 添加标准头
        request.addValue("ios", forHTTPHeaderField: "X-Client-Type")
        if let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            request.addValue(appVersion, forHTTPHeaderField: "X-App-Version")
        }
        
        // 打印完整请求URL
        print("完整请求URL: \(url.absoluteString)")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        // 验证HTTP响应
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        guard (200...299).contains(httpResponse.statusCode) else {
            throw APIError.httpError(httpResponse.statusCode)
        }
        
        // 打印原始响应
        if let jsonString = String(data: data, encoding: .utf8) {
            print("原始响应数据: \(jsonString)")
        }
        
        return data
    }

    // 年份响应结构体
    struct YearsResponse: Codable {
        let years: [Int]
        let currentYear: Int
        
        enum CodingKeys: String, CodingKey {
            case years
            case currentYear = "current_year"
        }
    }

    // 获取可用年份
    func getAvailableYears() async throws -> [Int] {
        // 获取完整的YearsResponse对象
        let response: YearsResponse = try await hindsightGet("/ios/api/brewlog/available-years/")
        // 返回年份数组
        return response.years
    }
    
    // 获取可用年份和当前年份
    func getAvailableYearsWithCurrent() async throws -> (years: [Int], currentYear: Int) {
        // 获取完整的YearsResponse对象
        let response: YearsResponse = try await hindsightGet("/ios/api/brewlog/available-years/")
        // 返回年份数组和当前年份
        return (years: response.years, currentYear: response.currentYear)
    }

    // 测试时间范围参数处理
    func testTimeRangeParameter(timeRange: TimeRange) async throws -> Bool {
        guard let baseURLString = await getBaseURL() else {
            throw APIError.invalidURL
        }
        
        // 构建仅包含time_range参数的URL
        var urlComponents = URLComponents(string: "\(baseURLString)/ios/api/brewlog/hindsight/")
        urlComponents?.queryItems = [URLQueryItem(name: "time_range", value: timeRange.rawValue)]
        
        guard let url = urlComponents?.url else {
            throw APIError.invalidURL
        }
        
        print("测试时间范围参数 - 请求URL: \(url.absoluteString)")
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // 添加认证信息
        if self.isLoggedIn {
            if let token = try? KeychainManager.load(key: "auth_token") {
                request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            }
        }
        
        // 添加标准头
        request.addValue("ios", forHTTPHeaderField: "X-Client-Type")
        if let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            request.addValue(appVersion, forHTTPHeaderField: "X-App-Version")
        }
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        // 验证HTTP响应
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        guard (200...299).contains(httpResponse.statusCode) else {
            throw APIError.httpError(httpResponse.statusCode)
        }
        
        // 打印原始响应
        if let jsonString = String(data: data, encoding: .utf8) {
            print("测试时间范围参数 - 原始响应: \(jsonString)")
            
            // 检查响应中是否包含相同的time_range值
            return jsonString.contains(timeRange.rawValue)
        }
        
        return false
    }
    
    // MARK: - 调试方法
    // 测试所有时间范围参数 - 仅在需要调试时手动调用
    func testAllTimeRangeParameters() async {
        print("开始测试所有时间范围参数")
        
        for timeRange in TimeRange.allCases {
            do {
                let result = try await testTimeRangeParameter(timeRange: timeRange)
                print("测试时间范围 \(timeRange.rawValue): \(result ? "成功" : "失败")")
            } catch {
                print("测试时间范围 \(timeRange.rawValue) 出错: \(error.localizedDescription)")
            }
        }
        
        print("时间范围参数测试完成")
    }
}