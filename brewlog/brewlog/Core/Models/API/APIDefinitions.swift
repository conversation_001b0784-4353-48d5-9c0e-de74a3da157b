import Foundation

// MARK: - API请求结构体
struct APIRequest {
    let endpoint: String
    let method: HTTPMethod
    let parameters: [String: String]?
    let body: [String: Any]?
    let forceRefresh: Bool
    
    enum HTTPMethod: String {
        case get = "GET"
        case post = "POST"
        case put = "PUT"
        case delete = "DELETE"
    }
    
    init(endpoint: String, 
         method: HTTPMethod = .get, 
         parameters: [String: String]? = nil, 
         body: [String: Any]? = nil, 
         forceRefresh: Bool = false) {
        self.endpoint = endpoint
        self.method = method
        self.parameters = parameters
        self.body = body
        self.forceRefresh = forceRefresh
    }
}

// MARK: - 咖啡豆相关响应模型
struct BeanListResponse: Decodable {
    let count: Int
    let next: String?
    let previous: String?
    let results: [CoffeeBean]
}

// MARK: - 归档咖啡豆响应
struct ArchiveBeanResponse: Decodable {
    let success: Bool
    let message: String
    let updated_at: Int?
} 