import Foundation
import SwiftUI

// 导入InternalEmptyResponse类型
typealias InternalEmptyResponse = APIService.InternalEmptyResponse

class BrewingService {
    private let apiService: APIService

    init(apiService: APIService = .shared) {
        self.apiService = apiService
    }

    func fetchCoffeeBeans() async throws -> [CoffeeBean] {
        do {
            // 首先尝试直接解析为数组
            return try await apiService.get("/ios/api/beans/")
        } catch let error as APIError where error.isDecodingError {
            // 如果解析为数组失败，尝试解析为包含results字段的字典
            print("⚠️ 直接解析为咖啡豆数组失败，尝试解析为包含results字段的字典")
            struct BeanResponse: Codable {
                let results: [CoffeeBean]
            }
            let response: BeanResponse = try await apiService.get("/ios/api/beans/")
            return response.results
        }
    }

    func fetchEquipments() async throws -> [Equipment] {
        do {
            // 首先尝试使用设备API获取数据
            print("🔍 尝试从设备API获取数据")
            do {
                return try await apiService.get("/ios/api/equipment/")
            } catch let error as APIError where error.isDecodingError {
                // 如果解析为数组失败，记录错误并尝试其他方式
                print("⚠️ 直接解析为设备数组失败，尝试其他方法: \(error.message)")

                // 尝试解析为包含results字段的分页响应格式
                print("🔍 尝试解析为包含results字段的分页响应格式")
                struct EquipmentResponse: Codable {
                    let results: [Equipment]
                    let count: Int?
                    let next: String?
                    let previous: String?
                }

                do {
                    let response: EquipmentResponse = try await apiService.get("/ios/api/equipment/")
                    print("✅ 成功解析为分页响应格式，获取到\(response.results.count)个设备")
                    return response.results
                } catch let resultsError as APIError where resultsError.isDecodingError {
                    print("⚠️ 解析为分页响应格式失败: \(resultsError.message)")

                    // 尝试一个更通用的数据结构，仅提取data字段
                    print("🔍 尝试解析为包含data字段的响应格式")
                    struct DataResponse: Codable {
                        let data: [Equipment]
                    }

                    do {
                        let dataResponse: DataResponse = try await apiService.get("/ios/api/equipment/")
                        print("✅ 成功解析为data字段响应格式，获取到\(dataResponse.data.count)个设备")
                        return dataResponse.data
                    } catch {
                        print("❌ 所有解析方法都失败，将尝试直接访问原始JSON")
                        throw error
                    }
                }
            }
        } catch {
            print("❌ 获取设备失败: \(error)")

            // 尝试备用API路径
            do {
                print("🔍 尝试使用备用API路径")
                return try await apiService.get("/ios/api/gadget-list/")
            } catch {
                print("❌ 备用API路径也失败: \(error)")
                throw error
            }
        }
    }

    func fetchFlavorTags() async throws -> [FlavorTag] {
        do {
            // 首先尝试直接解析为数组
            return try await apiService.get("/ios/api/flavor-tags/")
        } catch let error as APIError where error.isDecodingError {
            // 如果解析为数组失败，尝试解析为包含results字段的字典
            print("⚠️ 直接解析为风味标签数组失败，尝试解析为包含results字段的字典")
            struct TagResponse: Codable {
                let results: [FlavorTag]
            }
            let response: TagResponse = try await apiService.get("/ios/api/flavor-tags/")
            return response.results
        }
    }

    @available(*, deprecated, message: "Use fetchFilteredRecords instead")
    func fetchBrewingRecords() async throws -> [BrewingRecord] {
        print("⚠️ 警告：此方法已弃用，请使用 fetchFilteredRecords")

        // 调用 fetchFilteredRecords 以确保一致性
        let result = try await fetchFilteredRecords()
        return result.records
    }

    func fetchFilteredRecords(
        dateFrom: Date? = nil,
        dateTo: Date? = nil,
        searchQuery: String? = nil,
        brewMethod: String? = nil,
        coffeeBean: String? = nil,
        ratingRange: String? = nil,
        page: Int = 1,
        pageSize: Int = 20
    ) async throws -> (records: [BrewingRecord], totalCount: Int, totalPages: Int, currentPage: Int) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        var parameters: [String: String] = [
            "page": String(page),
            "page_size": String(pageSize),
            "force_refresh": "true"  // 添加强制刷新参数，绕过服务器端缓存
        ]

        if let dateFrom = dateFrom {
            parameters["date_from"] = dateFormatter.string(from: dateFrom)
        }

        if let dateTo = dateTo {
            parameters["date_to"] = dateFormatter.string(from: dateTo)
        }

        if let searchQuery = searchQuery, !searchQuery.isEmpty {
            parameters["search_query"] = searchQuery
        }

        if let brewMethod = brewMethod, !brewMethod.isEmpty {
            parameters["brew_method"] = brewMethod
            print("🔍 传递冲煮方式参数: brew_method=\(brewMethod)")
        }

        if let coffeeBean = coffeeBean, !coffeeBean.isEmpty {
            parameters["coffee_bean"] = coffeeBean
            print("🔍 传递咖啡豆参数: coffee_bean=\(coffeeBean)")
        }

        if let ratingRange = ratingRange, !ratingRange.isEmpty {
            parameters["rating_range"] = ratingRange
        }

        // 打印请求参数用于调试
        print("🔍 发送筛选请求: \(parameters)")

        // 使用专门的响应结构体来接收数据
        struct FilteredResponse: Codable {
            let count: Int
            let next: Bool?
            let previous: Bool?
            let totalPages: Int
            let currentPage: Int
            let results: [BrewingRecord]
            let filters: [String: String]?

            enum CodingKeys: String, CodingKey {
                case count
                case next
                case previous
                case totalPages = "total_pages"
                case currentPage = "current_page"
                case results
                case filters
            }
        }

        let filteredResponse: FilteredResponse = try await apiService.get("/ios/api/brewlog/filtered-records/", parameters: parameters)

        // 打印响应结果
        print("📊 服务器响应: 总记录数=\(filteredResponse.count), 总页数=\(filteredResponse.totalPages)")
        if let filters = filteredResponse.filters {
            print("📋 服务器处理的筛选条件: \(filters)")

            // 检查服务器返回的筛选条件是否与请求的匹配
            if let requestBrewMethod = brewMethod, let serverBrewMethod = filters["brew_method"], requestBrewMethod != serverBrewMethod {
                print("⚠️ 警告: 服务器处理的冲煮方式(\(serverBrewMethod))与请求的(\(requestBrewMethod))不匹配")
            }

            if let requestCoffeeBean = coffeeBean, let serverCoffeeBean = filters["coffee_bean"], requestCoffeeBean != serverCoffeeBean {
                print("⚠️ 警告: 服务器处理的咖啡豆ID(\(serverCoffeeBean))与请求的(\(requestCoffeeBean))不匹配")
            }
        }

        if filteredResponse.results.isEmpty {
            print("⚠️ 警告: 服务器返回空记录集! 检查参数是否正确，或者该筛选条件下确实没有数据")
        }

        return (
            records: filteredResponse.results,
            totalCount: filteredResponse.count,
            totalPages: filteredResponse.totalPages,
            currentPage: filteredResponse.currentPage
        )
    }

    func createRecord(_ record: BrewingRecord) async throws {
        // 使用时间戳格式发送created_at，类似于设备更新的处理方式
        let createdAtTimestamp = record.createdAt.timeIntervalSince1970

        let body: [String: Any] = [
            "recipe_name": record.recipeName as Any,
            "coffee_bean": record.coffeeBean.id,
            "brewing_equipment": record.brewingEquipment.id,
            "grinding_equipment": record.grindingEquipment?.id as Any,
            "grind_size": record.grindSize as Any,
            "dose_weight": record.doseWeight,
            "yield_weight": record.yieldWeight,
            "water_temperature": record.waterTemperature,
            "brewing_time": record.brewingTime,
            "rating_level": record.ratingLevel,
            "aroma": record.aroma,
            "acidity": record.acidity,
            "sweetness": record.sweetness,
            "body": record.body,
            "aftertaste": record.aftertaste,
            "water_quality": record.waterQuality as Any,
            "room_temperature": record.roomTemperature as Any,
            "room_humidity": record.roomHumidity as Any,
            "steps": record.steps.map { ["text": $0.text, "timer": $0.timer ?? "", "order": $0.order] },
            "flavor_tags": record.flavorTags.map { $0.id },
            "notes": record.notes as Any,
            "created_at": createdAtTimestamp,
            "gadgets": record.gadgets?.map { $0.id } ?? [],
            "gadget_kit": record.gadgetKit?.id as Any
        ]

        let _: BrewingRecord = try await apiService.post("/ios/api/brewlog/records/", body: body)
    }

    func updateRecord(_ record: BrewingRecord) async throws -> BrewingRecord {
        // 使用时间戳格式发送created_at，类似于设备更新的处理方式
        let createdAtTimestamp = record.createdAt.timeIntervalSince1970

        let body: [String: Any] = [
            "recipe_name": record.recipeName as Any,
            "coffee_bean": record.coffeeBean.id,
            "brewing_equipment": record.brewingEquipment.id,
            "grinding_equipment": record.grindingEquipment?.id as Any,
            "grind_size": record.grindSize as Any,
            "dose_weight": record.doseWeight,
            "yield_weight": record.yieldWeight,
            "water_temperature": record.waterTemperature,
            "brewing_time": record.brewingTime,
            "rating_level": record.ratingLevel,
            "aroma": record.aroma,
            "acidity": record.acidity,
            "sweetness": record.sweetness,
            "body": record.body,
            "aftertaste": record.aftertaste,
            "water_quality": record.waterQuality as Any,
            "room_temperature": record.roomTemperature as Any,
            "room_humidity": record.roomHumidity as Any,
            "steps": record.steps.map { ["text": $0.text, "timer": $0.timer ?? "", "order": $0.order] },
            "flavor_tags": record.flavorTags.map { $0.id },
            "notes": record.notes as Any,
            "created_at": createdAtTimestamp,
            "gadgets": record.gadgets?.map { $0.id } ?? [],
            "gadget_kit": record.gadgetKit?.id as Any
        ]

        let updatedRecord: BrewingRecord = try await apiService.put("/ios/api/brewlog/records/\(record.id)/", body: body)
        return updatedRecord
    }

    func deleteRecord(_ record: BrewingRecord) async throws {
        let _: InternalEmptyResponse = try await apiService.delete("/ios/api/brewlog/delete/\(record.id)/")
    }

    func deleteBean(_ bean: CoffeeBean) async throws {
        let _: InternalEmptyResponse = try await apiService.delete("/ios/api/beans/\(bean.id)/")
    }
}