import Foundation
import Network
import SwiftUI
// 导入通知扩展
import UIKit

// 添加网络状态变化通知名称
extension Notification.Name {
    static let networkStatusChanged = Notification.Name("NetworkStatusChanged")
}

class NetworkMonitor: ObservableObject {
    static let shared = NetworkMonitor()
    
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    // 物理网络连接状态
    @Published var isConnected = true {
        didSet {
            if oldValue != isConnected {
                // 当状态发生变化时，显示一个系统通知（仅在DEBUG模式下）
                #if DEBUG
                showSystemNotification(
                    isConnected ? "网络已连接" : "网络已断开",
                    body: isConnected ? "您的设备已重新连接到网络" : "您的设备网络连接已断开"
                )
                #endif
            }
        }
    }
    @Published var wasConnected = true
    
    @Published var connectionType: ConnectionType = .wifi
    
    // 添加开发调试变量
    #if DEBUG
    private var isSimulatingDisconnection = false
    #endif
    
    enum ConnectionType {
        case wifi
        case cellular
        case ethernet
        case unavailable
    }
    
    private init() {
        // 初始化时，监听物理网络状态变化
        monitor.pathUpdateHandler = { [weak self] path in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                // 如果正在模拟断网，不响应实际网络变化
                #if DEBUG
                if self.isSimulatingDisconnection {
                    print("📶 正在模拟断网，忽略物理网络变化")
                    return
                }
                #endif
                
                let newConnectionStatus = path.status == .satisfied
                
                #if DEBUG
                print("📶 物理网络接口状态变化: \(newConnectionStatus ? "已连接" : "未连接")")
                print("📶 当前接口类型: \(self.getInterfaceType(path))")
                #endif
                
                // 如果接口已断开，直接更新为断网状态
                if !newConnectionStatus {
                    // 保存之前状态用于判断是否为重连
                    let previousStatus = self.isConnected
                    if self.isConnected {
                        self.wasConnected = previousStatus
                        self.isConnected = false
                        
                        // 发送网络状态变化通知
                        self.postNetworkStatusChangedNotification()
                        
                        // 强制触发objectWillChange，确保UI更新
                        self.objectWillChange.send()
                        
                        #if DEBUG
                        print("📶 物理接口已断开，更新为断网状态")
                        #endif
                    }
                } else {
                    // 如果接口连接，执行实际的网络连接测试
                    #if DEBUG
                    print("📶 物理接口已连接，进行实际网络测试...")
                    #endif
                    
                    // 使用延迟执行，避免频繁触发
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        self.performActiveCheck()
                    }
                }
                
                // 更新连接类型
                self.updateConnectionType(path)
            }
        }
        
        monitor.start(queue: queue)
        
        #if DEBUG
        print("📶 NetworkMonitor 已启动，当前连接状态: \(isConnected)")
        #endif
        
        // 初始化后立即执行一次实际的网络连接测试
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else { return }
            #if DEBUG
            print("📶 执行初始网络连接测试...")
            #endif
            
            // 直接执行活跃检查，包含真实网络测试
            self.performActiveCheck()
        }
    }
    
    #if DEBUG
    // 显示系统通知，便于调试
    private func showSystemNotification(_ title: String, body: String) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = UNNotificationSound.default
        
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: UNTimeIntervalNotificationTrigger(timeInterval: 0.1, repeats: false)
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("⚠️ 系统通知错误: \(error.localizedDescription)")
            }
        }
    }
    #endif
    
    // 获取接口类型的描述
    private func getInterfaceType(_ path: NWPath) -> String {
        if path.usesInterfaceType(.wifi) {
            return "WiFi"
        } else if path.usesInterfaceType(.cellular) {
            return "蜂窝数据"
        } else if path.usesInterfaceType(.wiredEthernet) {
            return "有线网络"
        } else if path.usesInterfaceType(.loopback) {
            return "本地回环"
        } else {
            return "未知"
        }
    }
    
    deinit {
        monitor.cancel()
        
        #if DEBUG
        print("📶 NetworkMonitor 已停止")
        #endif
    }
    
    // 发送网络状态变化通知
    private func postNetworkStatusChangedNotification() {
        NotificationCenter.default.post(
            name: .networkStatusChanged,
            object: self,
            userInfo: ["isConnected": isConnected]
        )
        
        // 同时发送UIKit级别的全局通知，以提高可靠性
        DispatchQueue.main.async {
            UIApplication.shared.sendAction(#selector(UIApplication.networkStatusDidChange), to: nil, from: self, for: nil)
        }
        
        #if DEBUG
        print("📡 网络状态变化通知已发送: \(isConnected ? "已连接" : "已断开")")
        #endif
    }
    
    // 更新连接类型
    private func updateConnectionType(_ path: NWPath) {
        let oldType = connectionType
        
        if path.usesInterfaceType(.wifi) {
            connectionType = .wifi
        } else if path.usesInterfaceType(.cellular) {
            connectionType = .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            connectionType = .ethernet
        } else {
            connectionType = .unavailable
        }
        
        #if DEBUG
        if oldType != connectionType {
            print("📡 连接类型变更: \(oldType) -> \(connectionType)")
        }
        #endif
    }
    
    #if DEBUG
    // 模拟网络断开连接（仅在DEBUG环境可用）
    func simulateDisconnection(_ shouldDisconnect: Bool) {
        #if DEBUG
        print("📡 模拟\(shouldDisconnect ? "断开" : "恢复")网络连接")
        #endif
        
        DispatchQueue.main.async {
            self.isSimulatingDisconnection = shouldDisconnect
            
            if shouldDisconnect {
                self.wasConnected = self.isConnected
                self.isConnected = false
                self.postNetworkStatusChangedNotification()
                self.objectWillChange.send()
            } else {
                // 模拟恢复连接，要重新检测实际网络状态
                self.performActiveCheck()
            }
        }
    }
    #endif
    
    // 用户手动检测网络状态
    func performActiveCheck() {
        #if DEBUG
        print("📡 执行用户触发的网络状态检查...")
        #endif
        
        // 如果正在模拟断网，就不进行实际的网络测试
        #if DEBUG
        if isSimulatingDisconnection {
            print("📡 当前处于模拟断网状态，跳过实际网络测试")
            
            // 直接更新UI
            DispatchQueue.main.async {
                self.postNetworkStatusChangedNotification()
                self.objectWillChange.send()
                
                // 强制显示网络状态横幅
                NotificationCenter.default.post(
                    name: .init("ForceShowNetworkBanner"),
                    object: nil,
                    userInfo: ["isConnected": false]
                )
            }
            return
        }
        #endif
        
        // 获取当前实际的物理网络状态
        let currentPath = monitor.currentPath
        let interfaceStatus = currentPath.status == .satisfied
        
        #if DEBUG
        print("📡 物理接口状态: \(interfaceStatus ? "已连接" : "未连接")")
        print("📡 接口类型: \(getInterfaceType(currentPath))")
        #endif
        
        // 如果物理接口已断开，无需进行网络测试
        if !interfaceStatus {
            DispatchQueue.main.async {
                let previousStatus = self.isConnected
                if self.isConnected {
                    self.wasConnected = previousStatus
                    self.isConnected = false
                    self.postNetworkStatusChangedNotification()
                }
                
                // 更新UI
                self.objectWillChange.send()
                
                // 强制显示网络状态横幅
                NotificationCenter.default.post(
                    name: .init("ForceShowNetworkBanner"),
                    object: nil,
                    userInfo: ["isConnected": false]
                )
                
                #if DEBUG
                print("📡 物理接口已断开，确认为断网状态")
                #endif
            }
            return
        }
        
        // 添加实际网络连接测试
        let testURL = URL(string: "https://www.baidu.com")!
        
        // 创建配置并设置超时时间
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForResource = 5.0
        configuration.timeoutIntervalForRequest = 5.0
        
        // 使用自定义配置创建会话
        let session = URLSession(configuration: configuration)
        
        // 创建并启动网络测试任务
        let task = session.dataTask(with: testURL) { [weak self] (_, response, error) in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                // 判断是否真实连接到互联网
                let isReallyConnected: Bool
                
                if let httpResponse = response as? HTTPURLResponse, 
                   (200...299).contains(httpResponse.statusCode) {
                    isReallyConnected = true
                    #if DEBUG
                    print("📡 网络连接测试成功：HTTP状态码 \(httpResponse.statusCode)")
                    #endif
                } else {
                    isReallyConnected = false
                    #if DEBUG
                    if let error = error {
                        print("📡 网络连接测试失败：\(error.localizedDescription)")
                    } else {
                        print("📡 网络连接测试失败：未知错误")
                    }
                    #endif
                }
                
                // 更新连接状态
                let previousStatus = self.isConnected
                if self.isConnected != isReallyConnected {
                    self.wasConnected = previousStatus
                    self.isConnected = isReallyConnected
                    
                    // 发送网络状态变化通知
                    self.postNetworkStatusChangedNotification()
                }
                
                // 不论是否有状态变化，都手动触发一次通知，确保UI更新
                self.postNetworkStatusChangedNotification()
                self.objectWillChange.send()
                
                // 同时强制显示网络状态横幅，当恢复连接时标记为重连
                let isReconnected = isReallyConnected && !previousStatus
                NotificationCenter.default.post(
                    name: .init("ForceShowNetworkBanner"),
                    object: nil,
                    userInfo: [
                        "isConnected": self.isConnected,
                        "isReconnected": isReconnected
                    ]
                )
                
                #if DEBUG
                print("📡 网络检测完成：状态 = \(isReallyConnected ? "已连接" : "未连接")")
                #endif
            }
        }
        
        // 启动任务
        task.resume()
    }
}

// 添加UIApplication的扩展
extension UIApplication {
    @objc func networkStatusDidChange() {
        // 仅作为一个可被sendAction调用的目标方法
        #if DEBUG
        print("📡 UIApplication.networkStatusDidChange 被调用")
        #endif
    }
} 