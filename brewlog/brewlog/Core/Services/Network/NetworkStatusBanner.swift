import SwiftUI
import Combine

/// 网络状态横幅视图
struct NetworkStatusBanner: View {
    // 背景颜色常量
    static let disconnectedColor = Color.black.opacity(0.95)
    static let reconnectedColor = Color(red: 16/255, green: 117/255, blue: 24/255).opacity(0.95)
    
    // 网络状态
    var isConnected: Bool
    var isReconnected: Bool
    
    // 添加动画状态
    @State private var isAnimating = false
    
    var body: some View {
        HStack(spacing: 8) {
            // 根据网络状态显示不同图标并添加动画
            if isConnected {
                Image(systemName: "wifi")
                    .font(.system(size: 13))
            } else {
                Image(systemName: "wifi.slash")
                    .font(.system(size: 13))
                    .opacity(isAnimating ? 0.6 : 1.0)
                    .animation(Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true), value: isAnimating)
                    .onAppear {
                        self.isAnimating = true
                    }
            }
            
            Text(statusText)
                .font(.system(size: 13, weight: .medium))
        }
        .foregroundColor(.white)
        .frame(maxWidth: .infinity)
        .padding(.vertical, 7)
        .background(isConnected ? Self.reconnectedColor : Self.disconnectedColor)
        .onTapGesture {
            // 添加点击横幅时触发主动网络检测
            NetworkMonitor.shared.performActiveCheck()
        }
    }
    
    // 根据不同状态显示不同文本
    private var statusText: String {
        if isConnected && isReconnected {
            return "已重新连接到网络"
        } else if !isConnected {
            return "无网络连接"
        } else {
            // 已连接但不是重连状态，不显示文本
            return ""
        }
    }
}

/// 网络状态管理器 - 单例模式的观察者
class NetworkStatusManager: ObservableObject {
    static let shared = NetworkStatusManager()
    
    @Published var isNetworkBannerVisible = false
    @Published var isConnected = true
    @Published var isReconnected = false
    
    private var networkMonitor = NetworkMonitor.shared
    private var cancellables = Set<AnyCancellable>()
    private var hideTask: DispatchWorkItem? = nil
    private var initialCheckDone = false
    
    private init() {
        // 添加UI调试记录
        let publisher = objectWillChange.sink { [weak self] _ in
            guard let self = self else { return }
            #if DEBUG
            print("🌐 NetworkStatusManager状态更新 - 连接:\(self.isConnected) 重连:\(self.isReconnected) 横幅可见:\(self.isNetworkBannerVisible)")
            #endif
        }
        cancellables.insert(publisher)
        
        // 订阅网络状态变化
        networkMonitor.$isConnected
            .receive(on: RunLoop.main)
            .sink { [weak self] connected in
                guard let self = self else { return }
                
                #if DEBUG
                print("🌐 NetworkStatusManager: 收到网络状态更新 - 连接状态: \(connected)")
                #endif
                
                if connected && self.networkMonitor.wasConnected == false {
                    // 网络重新连接
                    #if DEBUG
                    print("🌐 网络重新连接：显示重连横幅")
                    #endif
                    
                    self.isConnected = true
                    self.isReconnected = true
                    self.isNetworkBannerVisible = true
                    
                    // 取消之前的隐藏任务
                    self.hideTask?.cancel()
                    
                    // 3秒后隐藏重连信息
                    let task = DispatchWorkItem {
                        #if DEBUG
                        print("🌐 重连横幅定时隐藏")
                        #endif
                        
                        withAnimation {
                            self.isNetworkBannerVisible = false
                        }
                    }
                    self.hideTask = task
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0, execute: task)
                    
                } else if !connected {
                    // 网络断开 - 立即显示
                    #if DEBUG
                    print("🌐 网络断开：显示断网横幅")
                    #endif
                    
                    withAnimation(.easeInOut(duration: 0.3)) {
                        self.isConnected = false
                        self.isReconnected = false
                        self.isNetworkBannerVisible = true
                    }
                    
                    // 取消之前的隐藏任务，保持横幅可见
                    self.hideTask?.cancel()
                    self.hideTask = nil
                } else if !self.initialCheckDone {
                    // 初始状态为已连接，但我们需要确保UI一致性
                    #if DEBUG
                    print("🌐 初始网络状态设置：已连接")
                    #endif
                    
                    self.isConnected = true
                    self.isReconnected = false
                    self.isNetworkBannerVisible = false
                    self.initialCheckDone = true
                } else {
                    // 已经连接且不是重连状态，不显示横幅
                    #if DEBUG
                    print("🌐 网络已连接状态，不显示横幅")
                    #endif
                    
                    self.isConnected = true
                    self.isReconnected = false
                    self.isNetworkBannerVisible = false
                }
                
                // 强制发送状态变更通知，确保UI更新
                self.objectWillChange.send()
            }
            .store(in: &cancellables)
        
        // 额外订阅通知中心的网络状态变化通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleNetworkStatusChange),
            name: .networkStatusChanged,
            object: nil
        )
        
        // 订阅强制显示网络状态横幅的通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleForceShowNetworkBanner),
            name: Notification.Name("ForceShowNetworkBanner"),
            object: nil
        )
        
        // 额外检查初始网络状态
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let initialConnected = self.networkMonitor.isConnected
            #if DEBUG
            print("🌐 NetworkStatusManager: 初始网络状态检查 - \(initialConnected ? "已连接" : "未连接")")
            #endif
            
            if !initialConnected {
                #if DEBUG
                print("🌐 初始状态为未连接，显示断网横幅")
                #endif
                
                self.isConnected = false
                self.isReconnected = false
                self.isNetworkBannerVisible = true
                self.objectWillChange.send()
            }
            self.initialCheckDone = true
        }
        
        // 延迟1秒后再次检查网络状态，确保正确显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            #if DEBUG
            print("🌐 NetworkStatusManager: 延迟检查网络状态")
            #endif
            self?.networkMonitor.performActiveCheck()
        }
    }
    
    // 通知中心网络状态变化处理
    @objc private func handleNetworkStatusChange(_ notification: Notification) {
        guard let isConnected = notification.userInfo?["isConnected"] as? Bool else {
            return
        }
        
        #if DEBUG
        print("🌐 NetworkStatusManager: 收到网络通知 - 连接状态: \(isConnected)")
        #endif
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if isConnected && !self.isConnected {
                // 网络重新连接
                #if DEBUG
                print("🌐 [通知] 网络重新连接：显示重连横幅")
                #endif
                
                self.isConnected = true
                self.isReconnected = true
                self.isNetworkBannerVisible = true
                
                // 取消之前的隐藏任务
                self.hideTask?.cancel()
                
                // 3秒后隐藏重连信息
                let task = DispatchWorkItem {
                    withAnimation {
                        self.isNetworkBannerVisible = false
                    }
                }
                self.hideTask = task
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0, execute: task)
                
            } else if !isConnected && self.isConnected {
                // 网络断开 - 立即显示
                #if DEBUG
                print("🌐 [通知] 网络断开：显示断网横幅")
                #endif
                
                withAnimation(.easeInOut(duration: 0.3)) {
                    self.isConnected = false
                    self.isReconnected = false
                    self.isNetworkBannerVisible = true
                }
                
                // 取消之前的隐藏任务，保持横幅可见
                self.hideTask?.cancel()
                self.hideTask = nil
            } else if isConnected && self.isConnected {
                // 已经是连接状态，无需显示横幅
                #if DEBUG
                print("🌐 [通知] 已处于连接状态，不显示横幅")
                #endif
                
                self.isConnected = true
                self.isReconnected = false
                self.isNetworkBannerVisible = false
            }
            
            // 强制发送状态变更通知，确保UI更新
            self.objectWillChange.send()
        }
    }
    
    // 接收强制显示横幅的通知
    @objc private func handleForceShowNetworkBanner(_ notification: Notification) {
        let isConnected = notification.userInfo?["isConnected"] as? Bool ?? NetworkMonitor.shared.isConnected
        let isReconnected = notification.userInfo?["isReconnected"] as? Bool ?? false
        
        #if DEBUG
        print("🌐 NetworkStatusManager: 收到强制显示横幅通知 - 连接状态: \(isConnected), 重连状态: \(isReconnected)")
        #endif
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if !isConnected {
                // 断网状态 - 显示断网横幅
                withAnimation {
                    self.isConnected = false
                    self.isReconnected = false
                    self.isNetworkBannerVisible = true
                    
                    // 强制发送变更通知
                    self.objectWillChange.send()
                }
            } else if isConnected && isReconnected {
                // 网络重连状态 - 显示重连横幅
                withAnimation {
                    self.isConnected = true
                    self.isReconnected = true
                    self.isNetworkBannerVisible = true
                    
                    // 强制发送变更通知
                    self.objectWillChange.send()
                }
                
                // 3秒后隐藏
                self.hideTask?.cancel()
                let task = DispatchWorkItem {
                    withAnimation {
                        self.isNetworkBannerVisible = false
                    }
                }
                self.hideTask = task
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0, execute: task)
            } else {
                // 已经是连接状态，无需显示横幅
                #if DEBUG
                print("🌐 已处于连接状态，不显示横幅")
                #endif
                
                self.isConnected = true
                self.isReconnected = false
                self.isNetworkBannerVisible = false
            }
        }
    }
    
    // 测试用：手动设置网络状态（仅用于调试）
    func forceShowNetworkStatus(connected: Bool) {
        #if DEBUG
        print("🌐 强制设置网络状态: \(connected ? "已连接" : "未连接")")
        #endif
        
        withAnimation {
            self.isConnected = connected
            self.isReconnected = connected && !self.isConnected
            self.isNetworkBannerVisible = true
            
            if connected {
                // 连接状态3秒后自动隐藏
                self.hideTask?.cancel()
                let task = DispatchWorkItem {
                    withAnimation {
                        self.isNetworkBannerVisible = false
                    }
                }
                self.hideTask = task
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0, execute: task)
            }
        }
    }
}

/// 应用于整个TabView的视图修饰符
struct NetworkBannerTabBarModifier: ViewModifier {
    @ObservedObject private var statusManager = NetworkStatusManager.shared
    
    func body(content: Content) -> some View {
        ZStack(alignment: .bottom) {
            // 主要内容（TabView）
            content
                .padding(.bottom, statusManager.isNetworkBannerVisible ? 32 : 0)
            
            // 网络状态横幅（如果可见）
            if statusManager.isNetworkBannerVisible {
                VStack(spacing: 0) {
                    NetworkStatusBanner(
                        isConnected: statusManager.isConnected,
                        isReconnected: statusManager.isReconnected
                    )
                }
                .transition(.move(edge: .bottom))
                .zIndex(1000) // 确保横幅始终在最上层
            }
        }
        .animation(.easeInOut(duration: 0.3), value: statusManager.isNetworkBannerVisible)
        .onAppear {
            #if DEBUG
            print("🌐 NetworkBannerTabBarModifier 已加载")
            #endif
            
            // 检查一次网络状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                NetworkMonitor.shared.performActiveCheck()
            }
        }
    }
}

extension View {
    /// 添加网络状态横幅作为TabBar的扩展
    func withNetworkBanner() -> some View {
        self.modifier(NetworkBannerTabBarModifier())
    }
}

