import SwiftUI
import Combine

class ThemeManager: ObservableObject {
    static let shared = ThemeManager()
    
    // 发布当前主题和颜色方案
    @Published var currentLightTheme: ThemeType = .latte
    @Published var currentDarkTheme: ThemeType = .espresso
    @Published var currentThemeColors: ThemeColors
    @Published var themeMode: ThemeMode = .system
    
    // 添加主题切换状态追踪
    @Published var isChangingTheme: Bool = false
    
    // 主题色字典
    private var themeColors: [ThemeType: ThemeColors] = [:]
    
    // UserDefaults keys
    private let lightThemeKey = "light_theme"
    private let darkThemeKey = "dark_theme"
    private let themeModeKey = "theme_mode"
    
    // 持有对TraitChangeObserver的强引用
    private let traitObserver = TraitChangeObserver.shared
    
    // 环境对象
    @ObservedObject private var subscriptionService = SubscriptionService.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // 初始化默认主题颜色 - 先创建一个默认的颜色方案，稍后会更新
        let defaultColors = ThemeColors(
            primaryTextColor: Color(hex: "4D4D4D"),
            detailTextColor: Color(hex: "787878"),
            archivedTextColor: Color(hex: "BFBFBF"),
            functionTextColor: Color(hex: "693C24"),
            secondaryTextColor: Color(hex: "CFAA7E"),
            linkTextColor: Color(hex: "2B68E8"),
            noteTextColor: Color(hex: "808080"),
            errorTextColor: Color("ErrorColor"),
            primaryAccentColor: Color(hex: "CF5551"),
            primaryBgColor: Color(hex: "FFFFFF"),
            secondaryBgColor: Color(hex: "F9F7F5"),
            navbarBgColor: Color(hex: "EFEAE7"),
            focusBgColor: Color(hex: "D9D5D2")
        )
        
        // 初始化存储属性
        self.currentThemeColors = defaultColors
        
        // 初始化所有主题的颜色设置
        initializeThemeColors()
        
        // 加载保存的主题设置
        loadThemeSettings()
        
        // 根据当前外观模式和订阅状态设置当前主题颜色
        updateInitialThemeColors()
        
        // 监听进程相关的主题变化
        setupAppearanceMonitoring()
        
        // 监听订阅状态变化
        subscriptionService.$currentSubscriptionType
            .sink { [weak self] _ in
                self?.updateThemeColors()
            }
            .store(in: &cancellables)
    }
    
    // 设置监听系统外观变化的通知
    private func setupAppearanceMonitoring() {
        // 监听系统特性变化通知
        NotificationCenter.default.addObserver(
            self, 
            selector: #selector(handleSystemAppearanceChange), 
            name: TraitChangeObserver.traitChangeNotification, 
            object: nil
        )
        
        // 应用激活时检查系统主题是否变化
        NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)
            .sink { [weak self] _ in
                DispatchQueue.main.async {
                    if self?.themeMode == .system {
                        self?.updateThemeColorsOnly()
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    // 处理系统外观变化通知
    @objc private func handleSystemAppearanceChange(_ notification: Notification) {
        if themeMode == .system {
            DispatchQueue.main.async { [weak self] in
                self?.updateThemeColorsOnly()
            }
        }
    }
    
    // 当前系统外观模式
    private var colorScheme: ColorScheme {
        switch themeMode {
        case .system:
            // 每次获取时都直接读取当前系统的userInterfaceStyle，确保总是最新的
            return UITraitCollection.current.userInterfaceStyle == .dark ? .dark : .light
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
    
    // 初始化主题颜色后更新当前主题颜色
    private func updateInitialThemeColors() {
        // 如果用户是高级版用户，则根据外观模式选择相应主题
        if subscriptionService.currentSubscriptionType == .premium {
            if colorScheme == .dark {
                currentThemeColors = themeColors[currentDarkTheme]!
            } else {
                currentThemeColors = themeColors[currentLightTheme]!
            }
        } else {
            // 免费版用户只能使用默认主题色
            if colorScheme == .dark {
                currentThemeColors = themeColors[.espresso]!
            } else {
                currentThemeColors = themeColors[.latte]!
            }
        }
    }
    
    // 初始化所有主题的颜色设置
    private func initializeThemeColors() {
        // 默认主题色（拿铁）
        themeColors[.latte] = ThemeColors(
            primaryTextColor: Color(hex: "291334"),
            detailTextColor: Color(hex: "6A5F56"),
            archivedTextColor: Color(hex: "BDB6B0"),
            functionTextColor: Color(hex: "693C24"),
            secondaryTextColor: Color(hex: "C7A279"),
            linkTextColor: Color(hex: "2D6AD3"),
            noteTextColor: Color(hex: "8A7F76"),
            errorTextColor: Color(hex: "CF5551"),
            primaryAccentColor: Color(hex: "CF5551"),
            primaryBgColor: Color(hex: "FFFFFF"),
            secondaryBgColor: Color(hex: "FAF7F4"),
            navbarBgColor: Color(hex: "F0EAE4"),
            focusBgColor: Color(hex: "E4DCD3")
        )
        
        // 浓缩主题色
        themeColors[.espresso] = ThemeColors(
            primaryTextColor: Color(hex: "E9DED2"),
            detailTextColor: Color(hex: "BBA996"),
            archivedTextColor: Color(hex: "887C6F"),
            functionTextColor: Color(hex: "D38B5D"),
            secondaryTextColor: Color(hex: "AF6839"),
            linkTextColor: Color(hex: "63A2FF"),
            noteTextColor: Color(hex: "B2A594"),
            errorTextColor: Color(hex: "ED7069"),
            primaryAccentColor: Color(hex: "ED7069"),
            primaryBgColor: Color(hex: "1A150F"),
            secondaryBgColor: Color(hex: "241D15"),
            navbarBgColor: Color(hex: "32281D"),
            focusBgColor: Color(hex: "3E3225")
        )
        
        // 抹茶主题色
        themeColors[.matcha] = ThemeColors(
            primaryTextColor: Color(hex: "30423A"),
            detailTextColor: Color(hex: "5C6D64"),
            archivedTextColor: Color(hex: "A5B8AF"),
            functionTextColor: Color(hex: "2D6E4C"),
            secondaryTextColor: Color(hex: "7DB896"),
            linkTextColor: Color(hex: "357ABD"),
            noteTextColor: Color(hex: "7A8B83"),
            errorTextColor: Color(hex: "CE4A46"),
            primaryAccentColor: Color(hex: "CE4A46"),
            primaryBgColor: Color(hex: "FFFFFF"),
            secondaryBgColor: Color(hex: "F4F9F6"),
            navbarBgColor: Color(hex: "E5F0EA"),
            focusBgColor: Color(hex: "D2E3D8")
        )
        
        // 焦糖主题色
        themeColors[.caramel] = ThemeColors(
            primaryTextColor: Color(hex: "3E2E20"),
            detailTextColor: Color(hex: "6D5C4A"),
            archivedTextColor: Color(hex: "B8A99A"),
            functionTextColor: Color(hex: "9E4E00"),
            secondaryTextColor: Color(hex: "D1884A"),
            linkTextColor: Color(hex: "2C69D9"),
            noteTextColor: Color(hex: "7D6B5C"),
            errorTextColor: Color(hex: "CB4945"),
            primaryAccentColor: Color(hex: "2C69D9"),
            primaryBgColor: Color(hex: "FFFFFF"),
            secondaryBgColor: Color(hex: "FCF7F2"),
            navbarBgColor: Color(hex: "F2E7DB"),
            focusBgColor: Color(hex: "E9D7C3")
        )
        
        // 摩卡主题色
        themeColors[.mocha] = ThemeColors(
            primaryTextColor: Color(hex: "372822"),
            detailTextColor: Color(hex: "634D42"),
            archivedTextColor: Color(hex: "A99A91"),
            functionTextColor: Color(hex: "7D3A29"),
            secondaryTextColor: Color(hex: "93624D"),
            linkTextColor: Color(hex: "2E69C8"),
            noteTextColor: Color(hex: "705A50"),
            errorTextColor: Color(hex: "C84640"),
            primaryAccentColor: Color(hex: "C84640"),
            primaryBgColor: Color(hex: "FFFBF7"),
            secondaryBgColor: Color(hex: "F8EFE8"),
            navbarBgColor: Color(hex: "EFE0D5"),
            focusBgColor: Color(hex: "E5D2C2")
        )
        
        // 冷萃主题色 - 深蓝色调
        themeColors[.coldBrew] = ThemeColors(
            primaryTextColor: Color(hex: "E8EDF1"),
            detailTextColor: Color(hex: "B5C0CC"),
            archivedTextColor: Color(hex: "8A98A8"),
            functionTextColor: Color(hex: "6A93C2"),
            secondaryTextColor: Color(hex: "4B7AB9"),
            linkTextColor: Color(hex: "7FBDFF"),
            noteTextColor: Color(hex: "A3B0BD"),
            errorTextColor: Color(hex: "F27874"),
            primaryAccentColor: Color(hex: "F27874"),
            primaryBgColor: Color(hex: "1B2838"),
            secondaryBgColor: Color(hex: "253545"),
            navbarBgColor: Color(hex: "304354"),
            focusBgColor: Color(hex: "3B5064")
        )
        
        // 美式主题色 - 深褐灰色调
        themeColors[.americano] = ThemeColors(
            primaryTextColor: Color(hex: "EAE6E2"),
            detailTextColor: Color(hex: "BDB8B3"),
            archivedTextColor: Color(hex: "8F8A85"),
            functionTextColor: Color(hex: "BC9871"),
            secondaryTextColor: Color(hex: "9A7652"),
            linkTextColor: Color(hex: "6FB5FF"),
            noteTextColor: Color(hex: "AEA9A5"),
            errorTextColor: Color(hex: "F17A76"),
            primaryAccentColor: Color(hex: "6FB5FF"),
            primaryBgColor: Color(hex: "25211E"),
            secondaryBgColor: Color(hex: "302B27"),
            navbarBgColor: Color(hex: "3C3632"),
            focusBgColor: Color(hex: "4A423D")
        )
        
        // 玛奇朵主题色 - 深棕奶色调
        themeColors[.macchiato] = ThemeColors(
            primaryTextColor: Color(hex: "EAE2DC"),
            detailTextColor: Color(hex: "C0B5AE"),
            archivedTextColor: Color(hex: "968C85"),
            functionTextColor: Color(hex: "C29478"),
            secondaryTextColor: Color(hex: "B5785A"),
            linkTextColor: Color(hex: "6FA3F5"),
            noteTextColor: Color(hex: "B3A8A1"),
            errorTextColor: Color(hex: "EE7A76"),
            primaryAccentColor: Color(hex: "6FA3F5"),
            primaryBgColor: Color(hex: "2B201F"),
            secondaryBgColor: Color(hex: "372C2A"),
            navbarBgColor: Color(hex: "463937"),
            focusBgColor: Color(hex: "564642")
        )
        
        // 默认系统主题（根据亮暗模式使用拿铁或浓缩）
        themeColors[.system] = themeColors[.latte]!
    }
    
    // 加载保存的主题设置
    private func loadThemeSettings() {
        let defaults = UserDefaults.standard
        
        if let lightThemeStr = defaults.string(forKey: lightThemeKey),
           let lightTheme = ThemeType(rawValue: lightThemeStr) {
            currentLightTheme = lightTheme
        }
        
        if let darkThemeStr = defaults.string(forKey: darkThemeKey),
           let darkTheme = ThemeType(rawValue: darkThemeStr) {
            currentDarkTheme = darkTheme
        }
        
        // 加载主题模式设置
        if let themeModeStr = defaults.string(forKey: themeModeKey),
           let mode = ThemeMode(rawValue: themeModeStr) {
            themeMode = mode
        }
    }
    
    // 保存主题设置
    private func saveThemeSettings() {
        let defaults = UserDefaults.standard
        defaults.set(currentLightTheme.rawValue, forKey: lightThemeKey)
        defaults.set(currentDarkTheme.rawValue, forKey: darkThemeKey)
        defaults.set(themeMode.rawValue, forKey: themeModeKey)
    }
    
    // 更新当前主题颜色
    @MainActor
    func updateThemeColors() {
        // 如果用户是高级版用户，则根据外观模式选择相应主题
        if subscriptionService.currentSubscriptionType == .premium {
            if colorScheme == .dark {
                currentThemeColors = themeColors[currentDarkTheme]!
            } else {
                currentThemeColors = themeColors[currentLightTheme]!
            }
        } else {
            // 免费版用户只能使用默认主题色
            if colorScheme == .dark {
                currentThemeColors = themeColors[.espresso]!
            } else {
                currentThemeColors = themeColors[.latte]!
            }
        }
    }
    
    // 设置新的主题
    @MainActor
    func setTheme(light: ThemeType, dark: ThemeType) {
        // 开始主题切换过程
        isChangingTheme = true
        
        // 更新主题
        currentLightTheme = light
        currentDarkTheme = dark
        saveThemeSettings()
        
        // 强制应用外观到整个应用
        forceApplyAppearance()
        
        // 使用计时器延迟结束过渡状态，给界面足够的重新渲染时间
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) { [weak self] in
            self?.isChangingTheme = false
        }
    }
    
    // 获取特定主题的颜色方案
    func themeColorsFor(_ theme: ThemeType) -> ThemeColors {
        return themeColors[theme]!
    }
    
    // 设置新的主题模式
    @MainActor
    func setThemeMode(_ mode: ThemeMode) {
        // 开始主题切换过程
        isChangingTheme = true
        
        // 更新模式
        themeMode = mode
        saveThemeSettings()
        
        // 强制应用外观到整个应用
        forceApplyAppearance()
        
        // 使用计时器延迟结束过渡状态，给界面足够的重新渲染时间
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) { [weak self] in
            self?.isChangingTheme = false
        }
    }
    
    // 获取指定模式对应的外观
    private func getAppearanceForMode(_ mode: ThemeMode) -> ColorScheme {
        switch mode {
        case .system:
            return UITraitCollection.current.userInterfaceStyle == .dark ? .dark : .light
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
    
    // 根据指定的外观强制更新主题颜色
    @MainActor
    func forceUpdateThemeColors(forAppearance appearance: ColorScheme) {
        if subscriptionService.currentSubscriptionType == .premium {
            if appearance == .dark {
                currentThemeColors = themeColors[currentDarkTheme]!
            } else {
                currentThemeColors = themeColors[currentLightTheme]!
            }
        } else {
            if appearance == .dark {
                currentThemeColors = themeColors[.espresso]!
            } else {
                currentThemeColors = themeColors[.latte]!
            }
        }
    }
    
    // 获取当前应该应用的ColorScheme
    func preferredColorScheme() -> ColorScheme? {
        switch themeMode {
        case .system:
            return nil  // 跟随系统
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
    
    // 强制应用UI样式到整个应用
    @MainActor
    func forceApplyAppearance() {
        // 确定当前应该使用的外观
        let appearance: UIUserInterfaceStyle
        switch themeMode {
        case .system:
            // 系统模式下不覆盖系统设置，设为unspecified让系统自己决定
            appearance = .unspecified
        case .light:
            appearance = .light
        case .dark:
            appearance = .dark
        }
        
        // 应用到所有窗口
        for scene in UIApplication.shared.connectedScenes {
            if let windowScene = scene as? UIWindowScene {
                for window in windowScene.windows {
                    window.overrideUserInterfaceStyle = appearance
                }
            }
        }
        
        // 获取实际生效的外观（如果是系统模式，则读取当前的系统设置）
        let effectiveAppearance = themeMode == .system ? UITraitCollection.current.userInterfaceStyle : appearance
        let colorScheme: ColorScheme = effectiveAppearance == .dark ? .dark : .light
        
        // 强制更新主题颜色
        forceUpdateThemeColors(forAppearance: colorScheme)
        
        // 发送主题变化通知
        NotificationCenter.default.post(name: Notification.Name("ThemeDidChange"), object: nil)
    }
    
    // 只更新主题颜色，不设置overrideUserInterfaceStyle和不发送全局通知
    // 用于处理系统主题变化时的轻量级更新
    @MainActor
    func updateThemeColorsOnly() {
        // 获取当前系统外观
        let currentAppearance = UITraitCollection.current.userInterfaceStyle
        let colorScheme: ColorScheme = currentAppearance == .dark ? .dark : .light
        
        // 仅更新颜色方案
        forceUpdateThemeColors(forAppearance: colorScheme)
    }
}

// 主题模式枚举
enum ThemeMode: String, CaseIterable {
    case system = "跟随系统"
    case light = "浅色模式"
    case dark = "深色模式"
} 