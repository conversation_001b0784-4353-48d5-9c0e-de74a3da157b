import UIKit
import SwiftUI

// 系统特性变化监听器
class TraitChangeObserver: NSObject {
    static let shared = TraitChangeObserver()
    
    // 通知名称
    static let traitChangeNotification = Notification.Name("AppTraitCollectionDidChange")
    
    // 当前的系统外观模式
    private(set) var currentUserInterfaceStyle: UIUserInterfaceStyle = .unspecified
    
    // 持有TraitWatcherViewController的强引用
    private var watcher: TraitWatcherViewController?
    
    // 初始化观察者
    override init() {
        super.init()
        // 初始化时读取当前系统外观模式
        currentUserInterfaceStyle = UITraitCollection.current.userInterfaceStyle
        
        // 设置通知观察
        setupObservers()
    }
    
    // 设置通知观察者
    private func setupObservers() {
        // 监听应用激活状态
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(checkForTraitChanges),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
        
        // 监听显著时间变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(checkForTraitChanges),
            name: UIApplication.significantTimeChangeNotification,
            object: nil
        )
        
        // 添加一个隐藏的控制器来监听特性变化
        setupTraitWatcher()
    }
    
    // 检查系统特性是否变化
    @objc internal func checkForTraitChanges() {
        let newStyle = UITraitCollection.current.userInterfaceStyle
        if newStyle != currentUserInterfaceStyle {
            currentUserInterfaceStyle = newStyle
            notifyTraitChanged()
        }
    }
    
    // 通知系统特性变化
    private func notifyTraitChanged() {
        NotificationCenter.default.post(
            name: TraitChangeObserver.traitChangeNotification,
            object: nil,
            userInfo: ["userInterfaceStyle": currentUserInterfaceStyle]
        )
    }
    
    // 设置特性监视器
    private func setupTraitWatcher() {
        // 创建视图控制器并保持强引用
        watcher = TraitWatcherViewController()
    }
}

// 用于监听系统特性变化的视图控制器
private class TraitWatcherViewController: UIViewController {
    init() {
        super.init(nibName: nil, bundle: nil)
        setupInvisibleView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupInvisibleView() {
        view = UIView(frame: .zero)
        view.isUserInteractionEnabled = false
        view.backgroundColor = .clear
        
        // 将视图添加到主窗口，但不会被看到
        DispatchQueue.main.async {
            // 使用UIWindowScene.windows替代废弃的UIApplication.shared.windows
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first {
                window.addSubview(self.view)
                self.view.frame = .zero
                self.view.isHidden = true
            }
        }
    }
    
    override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        super.traitCollectionDidChange(previousTraitCollection)
        
        // 仅在界面风格变化时发送通知
        if previousTraitCollection?.userInterfaceStyle != traitCollection.userInterfaceStyle {
            // 直接通知外观变化，不再调用私有方法
            TraitChangeObserver.shared.checkForTraitChanges()
        }
    }
} 