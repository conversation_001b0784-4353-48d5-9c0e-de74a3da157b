import Foundation
import UserNotifications
// 确保导入所有需要的模型类型
import SwiftUI

class BeanNotificationService {
    static let shared = BeanNotificationService()
    
    private let notificationCenter = UNUserNotificationCenter.current()
    private let userDefaults = UserDefaults.standard
    
    // 通知相关的键
    private let reminderEnabledKey = "bean_reminder_enabled"
    private let reminderDaysBeforeKey = "bean_reminder_days_before"
    private let notifiedBeansKey = "notified_beans_record"
    
    // 通知分类
    private let beanReminderCategory = "bean_reminder_category"
    
    // 通知锁，防止重复调度
    private var isScheduling = false
    
    private init() {
        // 注册通知分类和操作
        registerNotificationCategories()
    }
    
    // 注册通知分类和操作
    private func registerNotificationCategories() {
        // 定义"查看"操作
        let viewAction = UNNotificationAction(
            identifier: "VIEW_BEAN_ACTION",
            title: "查看详情",
            options: .foreground
        )
        
        // 定义"忽略"操作
        let ignoreAction = UNNotificationAction(
            identifier: "IGNORE_ACTION",
            title: "忽略",
            options: .destructive
        )
        
        // 创建通知分类
        let beanCategory = UNNotificationCategory(
            identifier: beanReminderCategory,
            actions: [viewAction, ignoreAction],
            intentIdentifiers: [],
            options: []
        )
        
        // 注册通知分类
        notificationCenter.setNotificationCategories([beanCategory])
    }
    
    // 保存通知设置
    func saveNotificationSettings(enabled: Bool, daysBefore: Int) {
        userDefaults.set(enabled, forKey: reminderEnabledKey)
        userDefaults.set(daysBefore, forKey: reminderDaysBeforeKey)
        
        // 如果启用了通知，立即检查是否有需要提醒的咖啡豆
        if enabled {
            // 集成实现：检查所有需要提醒的咖啡豆
            scheduleBeanNotifications()
        } else {
            // 如果禁用了通知，移除所有相关的待发送通知
            removeAllBeanNotifications()
            // 清除已通知记录
            clearNotifiedBeansRecord()
        }
    }
    
    // 获取通知设置
    func getNotificationSettings() -> (enabled: Bool, daysBefore: Int) {
        let enabled = userDefaults.bool(forKey: reminderEnabledKey)
        let daysBefore = userDefaults.integer(forKey: reminderDaysBeforeKey)
        return (enabled, daysBefore)
    }
    
    // 移除所有咖啡豆相关的通知
    func removeAllBeanNotifications() {
        notificationCenter.getPendingNotificationRequests { requests in
            let beanNotificationIdentifiers = requests
                .filter { $0.identifier.hasPrefix("bean_notification_") }
                .map { $0.identifier }
            
            if !beanNotificationIdentifiers.isEmpty {
                self.notificationCenter.removePendingNotificationRequests(withIdentifiers: beanNotificationIdentifiers)
            }
        }
    }
    
    // 移除特定咖啡豆的通知
    func removeBeanNotification(beanId: Int) {
        let identifier = "bean_notification_\(beanId)"
        notificationCenter.removePendingNotificationRequests(withIdentifiers: [identifier])
        // 同时移除即时通知标识符
        let immediateIdentifier = "bean_notification_immediate_\(beanId)"
        notificationCenter.removePendingNotificationRequests(withIdentifiers: [immediateIdentifier])
    }
    
    // 处理通知操作响应
    func handleNotificationResponse(_ response: UNNotificationResponse, completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        
        // 提取豆子ID
        if let beanId = userInfo["bean_id"] as? Int {
            // 记录该豆子已被通知
            markBeanAsNotified(beanId: beanId)
        }
        
        // 处理不同的操作
        switch response.actionIdentifier {
        case "VIEW_BEAN_ACTION":
            // 如果用户选择查看详情，提取豆子ID并导航到详情页面
            if let beanId = userInfo["bean_id"] as? Int {
                // 在这里实现导航到豆子详情页面的逻辑
                print("用户选择查看咖啡豆ID为\(beanId)的详情")
                
                // 这里应该发送一个通知或者使用其他方式来通知应用导航到相应页面
                NotificationCenter.default.post(
                    name: Notification.Name("NavigateToBeanDetail"),
                    object: nil,
                    userInfo: ["bean_id": beanId]
                )
            }
            
        case "IGNORE_ACTION":
            // 用户选择忽略，不需要额外操作
            print("用户选择忽略通知")
            
        default:
            // 默认操作（用户点击了通知或者划动查看）
            if let beanId = userInfo["bean_id"] as? Int {
                // 也导航到豆子详情页面
                print("用户点击了咖啡豆ID为\(beanId)的通知")
                
                NotificationCenter.default.post(
                    name: Notification.Name("NavigateToBeanDetail"),
                    object: nil,
                    userInfo: ["bean_id": beanId]
                )
            }
        }
        
        // 调用完成处理器
        completionHandler()
    }
    
    // 安排示例通知（仅用于开发和测试）
    func scheduleExampleNotification(daysBefore: Int = 0) {
        // 创建通知内容
        let content = UNMutableNotificationContent()
        content.title = "养豆期提醒"
        
        if daysBefore > 0 {
            content.body = "您的示例咖啡豆将在\(daysBefore)天后达到最佳赏味期"
        } else {
            content.body = "示例咖啡豆已到达最佳赏味期！现在是享用的最佳时机。"
        }
        
        content.sound = UNNotificationSound.default
        content.badge = 1
        // 使用应用统一的养豆提醒类别
        content.categoryIdentifier = "BEAN_REMINDER_CATEGORY"
        content.userInfo = ["bean_id": -1] // 示例ID使用负数
        
        // 尝试设置通知为持续类型 (iOS 15+)
        if #available(iOS 15.0, *) {
            // 设置中断级别为活跃（可能促使系统将其显示为持续通知）
            content.interruptionLevel = .active
        }
        
        // 创建触发器
        // 测试用，5秒后触发
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 5, repeats: false)
        
        // 创建通知请求
        let identifier = "example_bean_notification"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        
        // 清除旧的示例通知
        notificationCenter.removePendingNotificationRequests(withIdentifiers: ["example_bean_notification"])
        
        // 添加通知请求
        notificationCenter.add(request) { error in
            if let error = error {
                print("添加示例通知失败: \(error)")
            } else {
                print("示例通知已安排，将在5秒后发送")
            }
        }
    }
    
    // 新增方法：为所有活跃的咖啡豆安排通知
    func scheduleBeanNotifications() {
        // 防止重复调度，使用isScheduling标记
        if isScheduling {
            print("通知调度已在进行中，避免重复调度")
            return
        }
        
        // 设置调度标记
        isScheduling = true
        
        // 首先获取通知设置
        let settings = getNotificationSettings()
        
        // 如果通知未启用，直接返回
        if !settings.enabled {
            print("养豆期通知未启用，不安排通知")
            isScheduling = false
            return
        }
        
        // 获取所有活跃的咖啡豆
        fetchActiveBeans { beans in
            // 检查每一个咖啡豆
            for bean in beans {
                self.checkAndScheduleNotificationForBean(bean, daysBefore: settings.daysBefore)
            }
            // 完成调度
            self.isScheduling = false
        }
    }
    
    // 检查并为单个咖啡豆安排通知（确保不重复通知）
    private func checkAndScheduleNotificationForBean(_ bean: CoffeeBean, daysBefore: Int) {
        // 检查该豆子是否设置了休息期和烘焙日期
        guard let roastDate = bean.roastDate, 
              let minRestDays = bean.restPeriodMin else {
            print("咖啡豆ID为\(bean.id)的没有设置烘焙日期或休息期，无法安排通知")
            return
        }
        
        // 计算最佳赏味期开始日期
        let bestFlavorStartDate = Calendar.current.date(byAdding: .day, value: minRestDays, to: roastDate)!
        
        // 根据预设的提前天数计算实际通知日期
        let notificationDate = Calendar.current.date(byAdding: .day, value: -daysBefore, to: bestFlavorStartDate)!
        
        // 检查通知日期是否已经过去
        if notificationDate <= Date() {
            // 如果已经过了计划通知日期
            if bean.isInBestFlavorPeriod && !bean.baseStatus.rawValue.contains("已用完") {
                // 如果正处于最佳赏味期并且还有库存，检查是否已经通知过
                if !hasBeanBeenNotified(beanId: bean.id) {
                    // 如果没有通知过，发送一次即时通知
                    scheduleImmediateNotificationForBean(bean)
                    // 将该豆子标记为已通知
                    markBeanAsNotified(beanId: bean.id)
                }
            }
            return
        }
        
        // 检查是否已经通知过，如果已通知，则不再重复通知
        if hasBeanBeenNotified(beanId: bean.id) {
            print("咖啡豆「\(bean.name)」(ID: \(bean.id))已经通知过，跳过")
            return
        }
        
        // 清除该豆子的所有现有通知，然后重新调度
        removeBeanNotification(beanId: bean.id)
        
        // 创建日期组件用于触发器
        let dateComponents = Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: notificationDate)
        
        // 创建触发器 - 在指定日期的早上9点触发
        var triggerComponents = dateComponents
        triggerComponents.hour = 9  // 早上9点
        triggerComponents.minute = 0
        let trigger = UNCalendarNotificationTrigger(dateMatching: triggerComponents, repeats: false)
        
        // 创建通知内容
        let content = UNMutableNotificationContent()
        content.title = "养豆期提醒"
        
        // 根据提前天数设置不同的通知内容
        if daysBefore > 0 {
            content.body = "您的咖啡豆「\(bean.name)」将在\(daysBefore)天后达到最佳赏味期"
        } else {
            content.body = "您的咖啡豆「\(bean.name)」今天已达到最佳赏味期！现在是享用的最佳时机。"
        }
        
        content.sound = UNNotificationSound.default
        content.badge = 1
        content.categoryIdentifier = beanReminderCategory
        content.userInfo = ["bean_id": bean.id]
        
        // 设置通知为持续类型 (iOS 15+)
        if #available(iOS 15.0, *) {
            content.interruptionLevel = .active
        }
        
        // 创建通知请求
        let identifier = "bean_notification_\(bean.id)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        
        // 添加通知请求
        notificationCenter.add(request) { error in
            if let error = error {
                print("为咖啡豆「\(bean.name)」(ID: \(bean.id))添加通知失败: \(error)")
            } else {
                print("为咖啡豆「\(bean.name)」(ID: \(bean.id))安排通知成功，将在\(notificationDate)触发")
            }
        }
    }
    
    // 为已经到达最佳赏味期的咖啡豆立即发送通知
    private func scheduleImmediateNotificationForBean(_ bean: CoffeeBean) {
        // 创建通知内容
        let content = UNMutableNotificationContent()
        content.title = "养豆期提醒"
        content.body = "您的咖啡豆「\(bean.name)」已达到最佳赏味期！现在是享用的最佳时机。"
        content.sound = UNNotificationSound.default
        content.badge = 1
        content.categoryIdentifier = beanReminderCategory
        content.userInfo = ["bean_id": bean.id]
        
        // 设置通知为持续类型 (iOS 15+)
        if #available(iOS 15.0, *) {
            content.interruptionLevel = .active
        }
        
        // 创建触发器 - 立即触发，通过设置极短的延时实现
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        
        // 创建通知请求，使用特殊标识符避免重复
        let identifier = "bean_notification_immediate_\(bean.id)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        
        // 检查是否有待发送的相同通知
        notificationCenter.getPendingNotificationRequests { pendingRequests in
            // 如果已经有相同ID的即时通知在等待，则不再添加
            if pendingRequests.contains(where: { $0.identifier == identifier }) {
                print("已存在咖啡豆「\(bean.name)」(ID: \(bean.id))的即时通知，不重复添加")
                return
            }
            
            // 添加通知请求
            self.notificationCenter.add(request) { error in
                if let error = error {
                    print("为咖啡豆「\(bean.name)」(ID: \(bean.id))添加即时通知失败: \(error)")
                } else {
                    print("为咖啡豆「\(bean.name)」(ID: \(bean.id))安排即时通知成功")
                }
            }
        }
    }
    
    // 从API获取活跃的咖啡豆列表
    private func fetchActiveBeans(completion: @escaping ([CoffeeBean]) -> Void) {
        // 使用APIService获取咖啡豆列表
        // 注意：实际集成时需要替换为真实的API调用
        
        // 使用BeanListViewModel中的逻辑来获取咖啡豆
        if let viewModel = SharedViewModels.shared.beanListViewModel {
            // 使用已加载的数据 - 需要在主线程访问MainActor隔离的属性
            Task { @MainActor in
                let activeBeans = viewModel.coffeeBeans.filter { bean in
                    // 仅选择非归档、非删除且还有库存的咖啡豆
                    !bean.isArchived && !bean.isDeleted && (bean.bagRemain ?? 0) > 0
                }
                completion(activeBeans)
            }
        } else {
            // 如果ViewModel不可用或尚未加载数据
            // 创建临时ViewModel并加载数据
            // 这只是一个示例，实际集成时需要替换为适当的API调用或服务集成
            print("BeanListViewModel不可用，尝试直接获取咖啡豆数据")
            let apiService = APIService.shared
            
            // 构建URL
            var urlComponents = URLComponents(string: "\(apiService.getBaseURL())/ios/api/beans/")!
            let queryItems = [URLQueryItem(name: "page_size", value: "100")]
            urlComponents.queryItems = queryItems
            
            // 创建请求
            var request = URLRequest(url: urlComponents.url!)
            request.addValue("Bearer \(apiService.getAuthToken() ?? "")", forHTTPHeaderField: "Authorization")
            
            // 发起网络请求
            let task = URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    print("获取咖啡豆数据失败: \(error)")
                    completion([])
                    return
                }
                
                guard let data = data else {
                    print("获取咖啡豆数据返回为空")
                    completion([])
                    return
                }
                
                // 解析数据
                do {
                    let decoder = JSONDecoder()
                    let dateFormatter = DateFormatter()
                    dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
                    decoder.dateDecodingStrategy = .formatted(dateFormatter)
                    
                    // 注意：这里使用已定义的BeanListResponse，不再重复定义
                    let response = try decoder.decode(BeanListResponse.self, from: data)
                    
                    // 过滤出活跃的豆子
                    let activeBeans = response.results.filter { bean in
                        !bean.isArchived && !bean.isDeleted && (bean.bagRemain ?? 0) > 0
                    }
                    
                    print("成功获取\(activeBeans.count)个活跃咖啡豆")
                    completion(activeBeans)
                } catch {
                    print("解析咖啡豆数据失败: \(error)")
                    completion([])
                }
            }
            
            task.resume()
        }
    }
    
    // MARK: - 通知记录管理
    
    // 检查豆子是否已经通知过
    private func hasBeanBeenNotified(beanId: Int) -> Bool {
        let notifiedBeans = getNotifiedBeansRecord()
        return notifiedBeans.contains(beanId)
    }
    
    // 标记豆子为已通知
    private func markBeanAsNotified(beanId: Int) {
        var notifiedBeans = getNotifiedBeansRecord()
        if !notifiedBeans.contains(beanId) {
            notifiedBeans.append(beanId)
            userDefaults.set(notifiedBeans, forKey: notifiedBeansKey)
            print("咖啡豆ID \(beanId) 已标记为已通知")
        }
    }
    
    // 获取已通知的豆子ID列表
    private func getNotifiedBeansRecord() -> [Int] {
        return userDefaults.array(forKey: notifiedBeansKey) as? [Int] ?? []
    }
    
    // 清除已通知记录
    private func clearNotifiedBeansRecord() {
        userDefaults.removeObject(forKey: notifiedBeansKey)
        print("已清除所有通知记录")
    }
    
    // 为特定咖啡豆重置通知状态（如当用户手动强制通知时）
    func resetBeanNotificationStatus(beanId: Int) {
        var notifiedBeans = getNotifiedBeansRecord()
        if let index = notifiedBeans.firstIndex(of: beanId) {
            notifiedBeans.remove(at: index)
            userDefaults.set(notifiedBeans, forKey: notifiedBeansKey)
            print("已重置咖啡豆ID \(beanId) 的通知状态")
        }
    }
    
    // 清除过期的通知记录
    func cleanupNotificationRecords(currentBeanIds: [Int]) {
        var notifiedBeans = getNotifiedBeansRecord()
        let originalCount = notifiedBeans.count
        
        // 只保留当前存在的豆子ID
        notifiedBeans = notifiedBeans.filter { beanId in
            return currentBeanIds.contains(beanId)
        }
        
        if notifiedBeans.count != originalCount {
            userDefaults.set(notifiedBeans, forKey: notifiedBeansKey)
            print("已清理通知记录，从\(originalCount)个减少到\(notifiedBeans.count)个")
        }
    }
    
    // 公开方法：为单个咖啡豆安排通知，供外部调用（如手动强制通知）
    func scheduleNotificationForBean(_ bean: CoffeeBean, daysBefore: Int) {
        // 先重置该豆子的通知状态
        resetBeanNotificationStatus(beanId: bean.id)
        
        // 然后使用私有方法检查并安排通知
        checkAndScheduleNotificationForBean(bean, daysBefore: daysBefore)
    }
}

// 添加一个SharedViewModels类，用于在不同服务之间共享视图模型
class SharedViewModels {
    static let shared = SharedViewModels()
    
    private var _beanListViewModel: BeanListViewModel?
    
    var beanListViewModel: BeanListViewModel? {
        get {
            return _beanListViewModel
        }
        set {
            // 如果已存在视图模型实例且新设置的不是同一个实例
            if let existingViewModel = _beanListViewModel, existingViewModel !== newValue {
                // 临时保存旧的实例
                let oldViewModel = existingViewModel
                
                // 用新实例替换旧实例
                _beanListViewModel = newValue
                
                // 如果设置了新的实例且新实例不为nil，则执行清理工作
                if let newViewModel = newValue {
                    print("🔄 更新SharedViewModels中的BeanListViewModel实例，清除旧实例缓存")
                    
                    // 清除URLSession缓存
                    URLCache.shared.removeAllCachedResponses()
                    
                    // 在主线程触发新视图模型对象变化通知，确保UI更新
                    DispatchQueue.main.async {
                        newViewModel.objectWillChange.send()
                    }
                }
            } else {
                // 如果没有现有实例或新设置的是同一个实例，直接设置
                _beanListViewModel = newValue
            }
        }
    }
    
    private init() {}
} 