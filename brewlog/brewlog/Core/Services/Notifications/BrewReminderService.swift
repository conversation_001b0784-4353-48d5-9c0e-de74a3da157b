import Foundation
import UserNotifications
import SwiftUI

class BrewReminderService {
    static let shared = BrewReminderService()

    private let notificationCenter = UNUserNotificationCenter.current()
    private let userDefaults = UserDefaults.standard

    // 通知相关的键
    private let brewReminderEnabledKey = "brew_reminder_enabled"
    private let lastPeakTimeKey = "last_peak_time"
    private let lastTimeRangeKey = "last_time_range"
    private let lastNotificationDateKey = "last_brew_reminder_date"
    private let notificationSentTodayKey = "brew_reminder_sent_today"
    private let lastAutoCheckDateKey = "last_auto_check_date"

    // 通知分类
    private let brewReminderCategory = "brew_reminder_category"

    // 通知标识符
    private let brewReminderIdentifier = "brew_reminder_notification"

    private init() {
        // 注册通知分类和操作
        registerNotificationCategories()
    }

    // MARK: - 通知分类注册

    private func registerNotificationCategories() {
        // 创建新增记录操作
        let addBrewAction = UNNotificationAction(
            identifier: "ADD_BREW_ACTION",
            title: "新增记录",
            options: [.foreground]
        )

        // 创建复制上次记录操作
        let copyLastAction = UNNotificationAction(
            identifier: "COPY_LAST_BREW_ACTION",
            title: "复制上次记录",
            options: [.foreground]
        )

        // 创建冲煮提醒分类
        let brewReminderCategory = UNNotificationCategory(
            identifier: self.brewReminderCategory,
            actions: [addBrewAction, copyLastAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        // 注册分类
        notificationCenter.setNotificationCategories([brewReminderCategory])
    }

    // MARK: - 设置管理

    // 保存冲煮提醒设置
    func saveBrewReminderSettings(enabled: Bool) {
        userDefaults.set(enabled, forKey: brewReminderEnabledKey)

        if enabled {
            // 如果启用了提醒，尝试调度通知
            scheduleBrewReminder()
        } else {
            // 如果禁用了提醒，移除所有相关通知
            removeAllBrewReminders()
        }
    }

    // 获取冲煮提醒设置
    func getBrewReminderSettings() -> Bool {
        return userDefaults.bool(forKey: brewReminderEnabledKey)
    }

    // MARK: - 通知调度

    // 调度冲煮提醒
    func scheduleBrewReminder() {
        print("🔔 开始调度冲煮提醒...")

        // 首先移除现有的通知
        removeAllBrewReminders()

        // 检查是否有有效的 peakTime 数据
        guard let peakTimeData = getBestPeakTimeData() else {
            print("❌ 无法获取有效的 peakTime 数据，取消调度")
            return
        }

        print("📊 获取到 peakTime 数据: \(peakTimeData.peakTime), timeRange: \(peakTimeData.timeRange)")

        // 解析时间字符串
        guard let timeComponents = parseTimeString(peakTimeData.peakTime) else {
            print("❌ 无法解析时间字符串: \(peakTimeData.peakTime)")
            return
        }

        print("⏰ 解析时间成功: \(timeComponents.hour ?? 0):\(timeComponents.minute ?? 0)")

        // 检查今天是否已经发送过通知
        if hasNotificationSentToday() {
            print("⚠️ 今天已经发送过冲煮提醒，跳过调度")
            // 仍然调度明天的通知
            scheduleNextDayNotification(timeComponents: timeComponents, peakTimeData: peakTimeData)
            return
        }

        // 创建通知内容
        let content = UNMutableNotificationContent()
        content.title = "冲煮提醒"
        content.body = "根据您的习惯，现阶段是冲煮咖啡的好时机！"
        content.sound = UNNotificationSound.default
        content.badge = 1
        content.categoryIdentifier = brewReminderCategory
        content.userInfo = [
            "type": "brew_reminder",
            "peak_time": peakTimeData.peakTime,
            "time_range": peakTimeData.timeRange
        ]

        // 设置中断级别 (iOS 15+)
        if #available(iOS 15.0, *) {
            content.interruptionLevel = .active
        }

        // 检查当前时间是否已经过了今天的提醒时间
        let now = Date()
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: now)

        var todayComponents = calendar.dateComponents([.year, .month, .day], from: today)
        todayComponents.hour = timeComponents.hour
        todayComponents.minute = timeComponents.minute

        if let todayNotificationTime = calendar.date(from: todayComponents) {
            if now > todayNotificationTime {
                print("⏰ 今天的提醒时间已过，调度明天的通知")
                scheduleNextDayNotification(timeComponents: timeComponents, peakTimeData: peakTimeData)
                return
            }
        }

        // 创建今天的触发器
        let trigger = UNCalendarNotificationTrigger(
            dateMatching: timeComponents,
            repeats: false  // 改为不重复，避免每天重复提醒
        )

        // 创建通知请求
        let request = UNNotificationRequest(
            identifier: brewReminderIdentifier,
            content: content,
            trigger: trigger
        )

        // 添加通知
        notificationCenter.add(request) { error in
            if let error = error {
                print("❌ 冲煮提醒调度失败: \(error.localizedDescription)")
            } else {
                print("✅ 冲煮提醒调度成功，时间: \(timeComponents.hour ?? 0):\(String(format: "%02d", timeComponents.minute ?? 0))")
            }
        }
    }

    // 调度明天的通知
    private func scheduleNextDayNotification(timeComponents: DateComponents, peakTimeData: (peakTime: String, timeRange: String)) {
        let calendar = Calendar.current
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: Date())!

        var tomorrowComponents = calendar.dateComponents([.year, .month, .day], from: tomorrow)
        tomorrowComponents.hour = timeComponents.hour
        tomorrowComponents.minute = timeComponents.minute

        // 创建通知内容
        let content = UNMutableNotificationContent()
        content.title = "冲煮提醒"
        content.body = "根据您的习惯，现在是冲煮咖啡的好时机！"
        content.sound = UNNotificationSound.default
        content.badge = 1
        content.categoryIdentifier = brewReminderCategory
        content.userInfo = [
            "type": "brew_reminder",
            "peak_time": peakTimeData.peakTime,
            "time_range": peakTimeData.timeRange
        ]

        // 设置中断级别 (iOS 15+)
        if #available(iOS 15.0, *) {
            content.interruptionLevel = .active
        }

        // 创建明天的触发器
        let trigger = UNCalendarNotificationTrigger(
            dateMatching: tomorrowComponents,
            repeats: false
        )

        // 创建通知请求
        let request = UNNotificationRequest(
            identifier: brewReminderIdentifier,
            content: content,
            trigger: trigger
        )

        // 添加通知
        notificationCenter.add(request) { error in
            if let error = error {
                print("❌ 明天冲煮提醒调度失败: \(error.localizedDescription)")
            } else {
                print("✅ 明天冲煮提醒调度成功，时间: \(timeComponents.hour ?? 0):\(String(format: "%02d", timeComponents.minute ?? 0))")
            }
        }
    }

    // 移除所有冲煮提醒
    func removeAllBrewReminders() {
        print("🗑️ 移除所有冲煮提醒")
        notificationCenter.removePendingNotificationRequests(withIdentifiers: [brewReminderIdentifier])
    }

    // MARK: - 一天只提醒一次控制

    // 检查今天是否已经发送过通知
    private func hasNotificationSentToday() -> Bool {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        if let lastNotificationDate = userDefaults.object(forKey: lastNotificationDateKey) as? Date {
            let lastNotificationDay = calendar.startOfDay(for: lastNotificationDate)
            return calendar.isDate(today, inSameDayAs: lastNotificationDay)
        }

        return false
    }

    // 标记今天已发送通知
    private func markNotificationSentToday() {
        userDefaults.set(Date(), forKey: lastNotificationDateKey)
        userDefaults.set(true, forKey: notificationSentTodayKey)
        print("📝 标记今天已发送冲煮提醒")
    }

    // 清除今天的通知记录（用于测试或重置）
    func clearTodayNotificationRecord() {
        userDefaults.removeObject(forKey: lastNotificationDateKey)
        userDefaults.removeObject(forKey: notificationSentTodayKey)
        print("🗑️ 清除今天的通知记录")
    }

    // MARK: - 数据处理

    // 获取最佳的 peakTime 数据
    private func getBestPeakTimeData() -> (peakTime: String, timeRange: String)? {
        // 从 BrewLogViewModel 获取 Hindsight 数据
        guard let hindsightData = getHindsightData() else {
            return nil
        }

        // 检查是否有 peakTime 数据
        guard let peakTime = hindsightData.peakTime, !peakTime.isEmpty else {
            return nil
        }

        // 获取对应的 timeRange
        let timeRange = hindsightData.timeRange?.rawValue ?? "unknown"

        return (peakTime: peakTime, timeRange: timeRange)
    }

    // 从共享的 ViewModel 获取 Hindsight 数据
    private func getHindsightData() -> HindsightData? {
        // 从 UserDefaults 获取缓存的数据
        let lastPeakTime = userDefaults.string(forKey: lastPeakTimeKey)
        let lastTimeRange = userDefaults.string(forKey: lastTimeRangeKey)

        guard let peakTime = lastPeakTime, !peakTime.isEmpty else {
            return nil
        }

        // 创建一个简化的 HindsightData 对象用于获取 peakTime
        let timeRange = TimeRange.fromString(lastTimeRange)

        // 返回包含必要信息的数据
        return HindsightData(
            totalBrews: nil,
            totalRecords: nil,
            activeDays: nil,
            totalDays: nil,
            averageBrewsPerDay: 0,
            averageRating: 0,
            equipmentStats: [],
            beanStats: [],
            monthlyBrews: [],
            ratingDistribution: [],
            activeRate: nil,
            timeRange: timeRange,
            totalDose: nil,
            avgDose: nil,
            days250gLasts: nil,
            tastedBeansCount: nil,
            totalBrewingCost: nil,
            avgCostPerCup: nil,
            brewingCostRecordsCount: nil,
            mostUsedMethod: nil,
            mostUsedBrewer: nil,
            mostUsedBrewerBrand: nil,
            mostUsedGrinder: nil,
            mostUsedGrinderBrand: nil,
            peakPeriod: nil,
            peakWeekday: nil,
            peakTime: peakTime,
            brewsPerActiveDay: nil,
            mostRepurchasedBean: nil,
            mostRepurchasedBeanRoaster: nil,
            mostVisitedRoaster: nil,
            mostVisitedRoasterAmount: nil,
            totalBeanCosts: nil,
            equipmentCosts: nil,
            favoriteBeanName: nil,
            favoriteBeanRoaster: nil,
            favoriteBeanRating: nil,
            favoriteBeanCount: nil,
            mostUsedBeanName: nil,
            mostUsedBeanRoaster: nil,
            mostUsedBeanCount: nil,
            mostUsedBeanRating: nil,
            mostCommonOrigin: nil,
            mostCommonVariety: nil,
            mostCommonFlavors: nil,
            favoriteRecipeName: nil,
            favoriteRecipeRating: nil,
            favoriteRecipeCount: nil,
            mostUsedRecipeName: nil,
            mostUsedRecipeCount: nil,
            mostUsedRecipeRating: nil,
            maxStreak: nil,
            totalAllTimeRecords: nil,
            daysSinceRegistration: nil
        )
    }

    // 解析时间字符串 (例如: "14:30", "2:30 PM", "07:00")
    private func parseTimeString(_ timeString: String) -> DateComponents? {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone.current

        // 尝试不同的时间格式
        let formats = ["HH:mm", "H:mm", "hh:mm", "h:mm", "h:mm a", "h:mm A", "hh:mm a", "hh:mm A"]

        for format in formats {
            formatter.dateFormat = format
            if let date = formatter.date(from: timeString) {
                let calendar = Calendar.current
                let components = calendar.dateComponents([.hour, .minute], from: date)
                return components
            }
        }

        // 如果所有格式都失败，尝试手动解析简单的 HH:mm 格式
        let parts = timeString.split(separator: ":")
        if parts.count == 2,
           let hour = Int(parts[0]),
           let minute = Int(parts[1]),
           hour >= 0 && hour <= 23,
           minute >= 0 && minute <= 59 {

            var components = DateComponents()
            components.hour = hour
            components.minute = minute
            return components
        }

        return nil
    }

    // MARK: - 通知响应处理

    // 处理通知操作响应
    func handleNotificationResponse(_ response: UNNotificationResponse, completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo

        // 检查是否是冲煮提醒通知
        guard let type = userInfo["type"] as? String, type == "brew_reminder" else {
            completionHandler()
            return
        }

        // 标记今天已发送通知（用户已与通知交互）
        markNotificationSentToday()

        // 调度明天的通知
        if let peakTime = userInfo["peak_time"] as? String,
           let timeRange = userInfo["time_range"] as? String,
           let timeComponents = parseTimeString(peakTime) {
            scheduleNextDayNotification(
                timeComponents: timeComponents,
                peakTimeData: (peakTime: peakTime, timeRange: timeRange)
            )
        }

        // 处理不同的操作
        switch response.actionIdentifier {
        case "ADD_BREW_ACTION":
            // 用户选择新增记录
            print("👆 用户选择新增冲煮记录")

            // 发送通知到应用，导航到添加记录页面
            NotificationCenter.default.post(
                name: Notification.Name("NavigateToAddBrewLog"),
                object: nil,
                userInfo: ["source": "brew_reminder"]
            )

        case "COPY_LAST_BREW_ACTION":
            // 用户选择复制上次记录
            print("👆 用户选择复制上次冲煮记录")

            // 发送通知到应用，导航到添加记录页面并复制上次记录
            NotificationCenter.default.post(
                name: Notification.Name("NavigateToAddBrewLog"),
                object: nil,
                userInfo: [
                    "source": "brew_reminder",
                    "copy_last_record": true
                ]
            )

        default:
            // 默认操作（点击通知本身）
            print("👆 用户点击了冲煮提醒通知")

            // 也导航到添加记录页面
            NotificationCenter.default.post(
                name: Notification.Name("NavigateToAddBrewLog"),
                object: nil,
                userInfo: ["source": "brew_reminder"]
            )
        }

        completionHandler()
    }

    // MARK: - 数据更新

    // 当 Hindsight 数据更新时调用
    func updateWithHindsightData(_ hindsightData: HindsightData) {
        print("🔄 BrewReminderService.updateWithHindsightData 被调用")
        print("   输入数据 - peakTime: \(hindsightData.peakTime ?? "nil"), timeRange: \(hindsightData.timeRange?.rawValue ?? "nil")")

        // 检查是否有 peakTime 数据
        guard let peakTime = hindsightData.peakTime, !peakTime.isEmpty else {
            print("❌ 无有效的 peakTime 数据，跳过更新")
            return
        }

        // 获取当前保存的数据
        let lastPeakTime = userDefaults.string(forKey: lastPeakTimeKey)
        let lastTimeRange = userDefaults.string(forKey: lastTimeRangeKey)
        let currentTimeRange = hindsightData.timeRange?.rawValue

        // 检查数据是否有变化
        let dataChanged = lastPeakTime != peakTime || lastTimeRange != currentTimeRange

        if dataChanged {
            print("📊 Hindsight 数据发生变化，更新冲煮提醒")
            print("   旧 peakTime: \(lastPeakTime ?? "nil") -> 新 peakTime: \(peakTime)")
            print("   旧 timeRange: \(lastTimeRange ?? "nil") -> 新 timeRange: \(currentTimeRange ?? "nil")")

            // 保存新的数据
            userDefaults.set(peakTime, forKey: lastPeakTimeKey)
            userDefaults.set(currentTimeRange, forKey: lastTimeRangeKey)

            // 如果冲煮提醒已启用，重新调度通知
            if getBrewReminderSettings() {
                print("🔄 重新调度冲煮提醒通知")
                scheduleBrewReminder()
            } else {
                print("⚠️ 冲煮提醒未启用，跳过调度")
            }
        } else {
            print("📊 Hindsight 数据无变化，保持现有通知调度")
        }
    }

    // 检查是否有足够的数据来启用冲煮提醒
    func hasValidBrewData() -> Bool {
        // 检查是否有保存的 peakTime 数据
        let lastPeakTime = userDefaults.string(forKey: lastPeakTimeKey)

        // 如果有 peakTime 数据，说明用户有冲煮记录
        return lastPeakTime != nil && !lastPeakTime!.isEmpty
    }

    // 检查用户近一年是否有冲煮记录（用于UI状态判断）
    func hasRecentBrewRecords() -> Bool {
        // 首先检查是否有缓存的 peakTime 数据
        if hasValidBrewData() {
            return true
        }

        // 如果没有缓存数据，尝试主动获取 Hindsight 数据
        // 这里我们使用一个简单的方法：检查是否有任何冲煮记录
        // 通过检查 BrewLogViewModel 的统计数据
        return checkForAnyBrewRecords()
    }

    // 检查是否有任何冲煮记录的简单方法
    private func checkForAnyBrewRecords() -> Bool {
        // 这里我们可以通过多种方式检查：
        // 1. 检查 UserDefaults 中是否有其他相关的统计数据
        // 2. 或者返回 true 让用户尝试启用，如果没有数据会在调度时失败

        // 暂时返回 true，让用户可以尝试启用
        // 如果真的没有数据，在 scheduleBrewReminder() 时会失败并给出提示
        return true
    }

    // 主动获取 Hindsight 数据以更新 peakTime
    func fetchHindsightDataForBrewReminder() async {
        // 发送通知请求获取 Hindsight 数据
        NotificationCenter.default.post(
            name: Notification.Name("RequestHindsightDataForBrewReminder"),
            object: nil
        )
    }

    // MARK: - 自动检查机制

    // 检查是否需要进行每日自动检查
    func performDailyAutoCheckIfNeeded() {
        // 只有在冲煮提醒启用时才进行检查
        guard getBrewReminderSettings() else {
            print("⚠️ 冲煮提醒未启用，跳过每日自动检查")
            return
        }

        // 检查今天是否已经进行过自动检查
        if hasPerformedAutoCheckToday() {
            print("✅ 今天已进行过自动检查，跳过")
            return
        }

        print("🔄 开始每日自动检查 peak_time 数据...")

        // 异步执行检查，避免阻塞主线程
        Task {
            await performAutoCheck()
        }
    }

    // 执行自动检查
    private func performAutoCheck() async {
        print("📡 自动检查：获取最新的 Hindsight 数据")

        // 标记今天已进行检查
        markAutoCheckPerformedToday()

        // 获取最新的 Hindsight 数据
        await fetchHindsightDataForBrewReminder()

        print("✅ 每日自动检查完成")
    }

    // 检查今天是否已进行过自动检查
    private func hasPerformedAutoCheckToday() -> Bool {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())

        if let lastCheckDate = userDefaults.object(forKey: lastAutoCheckDateKey) as? Date {
            let lastCheckDay = calendar.startOfDay(for: lastCheckDate)
            return calendar.isDate(today, inSameDayAs: lastCheckDay)
        }

        return false
    }

    // 标记今天已进行自动检查
    private func markAutoCheckPerformedToday() {
        userDefaults.set(Date(), forKey: lastAutoCheckDateKey)
        print("📝 标记今天已进行自动检查")
    }

    // 强制执行自动检查（忽略今日是否已检查）
    func forceAutoCheck() async {
        print("🔄 强制执行自动检查...")
        await performAutoCheck()
    }

    // MARK: - 调试和状态检查

    // 检查当前通知调度状态
    func checkNotificationStatus() {
        print("🔍 检查冲煮提醒状态...")

        // 检查设置状态
        let isEnabled = getBrewReminderSettings()
        print("📱 冲煮提醒设置: \(isEnabled ? "已启用" : "已禁用")")

        // 检查今天是否已发送
        let sentToday = hasNotificationSentToday()
        print("📅 今天是否已发送: \(sentToday ? "是" : "否")")

        // 检查今天是否已进行自动检查
        let autoCheckedToday = hasPerformedAutoCheckToday()
        print("🔍 今天是否已自动检查: \(autoCheckedToday ? "是" : "否")")

        // 检查 peakTime 数据
        if let peakTimeData = getBestPeakTimeData() {
            print("⏰ 热点时刻: \(peakTimeData.peakTime)")
            print("📊 时间范围: \(peakTimeData.timeRange)")
        } else {
            print("❌ 无 peakTime 数据")
        }

        // 检查待发送的通知
        notificationCenter.getPendingNotificationRequests { requests in
            let brewReminders = requests.filter { $0.identifier == self.brewReminderIdentifier }
            print("📋 待发送的冲煮提醒数量: \(brewReminders.count)")

            for request in brewReminders {
                if let trigger = request.trigger as? UNCalendarNotificationTrigger {
                    let components = trigger.dateComponents
                    print("⏰ 计划时间: \(components.hour ?? 0):\(String(format: "%02d", components.minute ?? 0))")

                    // 计算下次触发时间
                    if let nextTriggerDate = trigger.nextTriggerDate() {
                        let formatter = DateFormatter()
                        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                        print("📅 下次触发: \(formatter.string(from: nextTriggerDate))")
                    }
                }
            }
        }
    }

}
