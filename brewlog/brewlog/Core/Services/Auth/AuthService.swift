import Foundation
import UIKit
import SwiftUI

enum AuthError: Error {
    case invalidCredentials
    case networkError(String)
    case serverError(String)
    case csrfError
    case tokenExpired
    case unknown
    
    var localizedDescription: String {
        switch self {
        case .invalidCredentials:
            return "用户名或密码错误"
        case .networkError(let message):
            return "网络连接失败：\(message)"
        case .serverError(let message):
            return "服务器错误：\(message)"
        case .csrfError:
            return "安全验证失败，请重试"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        case .unknown:
            return "未知错误，请稍后重试"
        }
    }
}

@MainActor
final class AuthService: ObservableObject {
    static let shared = AuthService()
    
    @Published private(set) var isAuthenticated = false
    @Published var isLoading = false
    @Published var lastTokenRefresh: Date? = nil
    
    private let defaults = UserDefaults.standard
    private var environment: APIEnvironment {
        APIService.environment
    }
    private(set) var sessionCookies: [HTTPCookie]?
    private(set) var csrfToken: String?
    
    private let tokenKey = "auth_token"
    private let deviceInfoKey = "device_info"
    
    // 网络监控器
    private let networkMonitor = NetworkMonitor.shared
    
    // 令牌刷新定时器
    private var refreshTimer: Timer?
    
    // 令牌刷新的间隔（每25分钟刷新一次）
    private let refreshInterval: TimeInterval = 25 * 60
    
    // 令牌恢复队列
    private var tokenRefreshAttempts = 0
    private let maxRefreshAttempts = 3
    
    private init() {
        isAuthenticated = defaults.string(forKey: tokenKey) != nil
        print("🌍 Current Auth Environment: \(environment), Base URL: \(environment.baseURL)")
        // 允许不安全的HTTP连接（仅用于开发环境）
        #if DEBUG
        if environment == .development {
            URLSession.allowsInsecureRequests()
        }
        #endif
        
        // 设置网络状态变化监听
        setupNetworkMonitoring()
        
        // 如果已认证，启动令牌刷新定时器
        if isAuthenticated {
            startTokenRefreshTimer()
        }
    }
    
    // 设置网络监控
    private func setupNetworkMonitoring() {
        // 添加网络状态变化通知观察
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleNetworkStatusChange),
            name: .networkStatusChanged,
            object: nil
        )
    }
    
    // 处理网络状态变化
    @objc private func handleNetworkStatusChange(_ notification: Notification) {
        guard let isConnected = notification.userInfo?["isConnected"] as? Bool else {
            return
        }
        
        if isConnected {
            // 网络重新连接，验证令牌
            if isAuthenticated {
                print("🔄 网络已恢复，验证令牌...")
                Task {
                    await verifyAndRefreshTokenIfNeeded()
                }
            }
        } else {
            // 网络断开，暂停令牌刷新定时器
            refreshTimer?.invalidate()
            refreshTimer = nil
        }
    }
    
    // 验证并在需要时刷新令牌
    func verifyAndRefreshTokenIfNeeded() async {
        guard isAuthenticated else { return }
        
        // 验证令牌有效性
        await APIService.shared.checkTokenValidity()
        // 如果验证成功，重启定时器
        startTokenRefreshTimer()
        
        // 尝试刷新令牌如果需要的话 (这部分逻辑已经在 checkTokenValidity 中处理了)
    }
    
    // 启动令牌刷新定时器
    private func startTokenRefreshTimer() {
        // 取消现有定时器
        refreshTimer?.invalidate()
        
        // 创建新定时器
        refreshTimer = Timer.scheduledTimer(withTimeInterval: refreshInterval, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            Task {
                await self.verifyAndRefreshTokenIfNeeded()
            }
        }
    }
    
    // 停止令牌刷新定时器
    private func stopTokenRefreshTimer() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }
    
    var currentUser: User? {
        get {
            guard let userData = defaults.data(forKey: "currentUser"),
                  let user = try? JSONDecoder().decode(User.self, from: userData) else {
                return nil
            }
            return user
        }
        set {
            if let user = newValue,
               let userData = try? JSONEncoder().encode(user) {
                defaults.set(userData, forKey: "currentUser")
            } else {
                defaults.removeObject(forKey: "currentUser")
            }
        }
    }
    
    private func getCSRFToken() async throws -> String {
        guard let url = URL(string: "\(environment.baseURL)/ios/api/csrf-token/") else {
            throw AuthError.unknown
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        print("🔐 获取CSRF Token - 请求URL: \(url)")
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw AuthError.unknown
            }
            
            print("🔐 CSRF响应状态码: \(httpResponse.statusCode)")
            print("🔐 CSRF响应头: \(httpResponse.allHeaderFields)")
            
            // 保存响应中的Cookie
            if let headerFields = httpResponse.allHeaderFields as? [String: String] {
                sessionCookies = HTTPCookie.cookies(withResponseHeaderFields: headerFields, for: url)
                print("🍪 保存会话Cookie: \(sessionCookies ?? [])")
            }
            
            guard (200...299).contains(httpResponse.statusCode) else {
                let responseString = String(data: data, encoding: .utf8) ?? "无响应内容"
                print("❌ CSRF请求失败: \(responseString)")
                throw AuthError.serverError(responseString)
            }
            
            // 从JSON响应中获取CSRF Token
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let token = json["csrfToken"] as? String {
                print("✅ 成功获取CSRF Token: \(token)")
                csrfToken = token
                return token
            }
            
            print("❌ 响应中没有找到CSRF Token")
            throw AuthError.csrfError
        } catch {
            print("❌ CSRF请求异常: \(error)")
            throw AuthError.networkError(error.localizedDescription)
        }
    }
    
    private func addCookies(to request: inout URLRequest) {
        if let cookies = sessionCookies, !cookies.isEmpty {
            let cookieHeaders = HTTPCookie.requestHeaderFields(with: cookies)
            for (field, value) in cookieHeaders {
                request.setValue(value, forHTTPHeaderField: field)
            }
        }
    }
    
    // 获取设备信息
    private func getDeviceInfo() -> [String: String] {
        let deviceID = UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString
        
        return [
            "device_id": deviceID,
            "device_model": UIDevice.current.model,
            "device_os_version": UIDevice.current.systemVersion,
            "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
        ]
    }
    
    // 保存设备信息到本地
    private func saveDeviceInfo(_ deviceInfo: [String: String]) {
        if let deviceData = try? JSONEncoder().encode(deviceInfo) {
            defaults.set(deviceData, forKey: deviceInfoKey)
        }
    }
    
    // 获取保存的设备信息
    private func getSavedDeviceInfo() -> [String: String]? {
        guard let deviceData = defaults.data(forKey: deviceInfoKey),
              let deviceInfo = try? JSONDecoder().decode([String: String].self, from: deviceData) else {
            return nil
        }
        return deviceInfo
    }
    
    @MainActor
    func login(username: String, password: String) async throws {
        isLoading = true
        defer { isLoading = false }

        do {
            // 获取设备信息
            let deviceInfo = getDeviceInfo()

            // 保存设备信息
            saveDeviceInfo(deviceInfo)

            let response = try await APIService.shared.login(username: username, password: password)
            defaults.set(response.access, forKey: tokenKey)
            isAuthenticated = true

            // 登录成功后获取用户信息
            await fetchUserProfile()

            // 启动令牌刷新定时器
            startTokenRefreshTimer()

            // 重置令牌刷新尝试计数
            tokenRefreshAttempts = 0
        } catch let error as APIError {
            print("❌ 登录失败: \(error.userFriendlyMessage)")
            throw AuthError.serverError(error.userFriendlyMessage)
        } catch {
            print("❌ 登录失败: \(error.localizedDescription)")
            throw AuthError.networkError(error.localizedDescription)
        }
    }

    @MainActor
    func register(username: String, email: String, password: String, firstName: String? = nil) async throws {
        isLoading = true
        defer { isLoading = false }

        do {
            // 获取设备信息
            let deviceInfo = getDeviceInfo()

            // 保存设备信息
            saveDeviceInfo(deviceInfo)

            let response = try await APIService.shared.register(username: username, email: email, password: password, firstName: firstName)
            defaults.set(response.access, forKey: tokenKey)
            isAuthenticated = true

            // 注册成功后获取用户信息
            await fetchUserProfile()

            // 启动令牌刷新定时器
            startTokenRefreshTimer()

            // 重置令牌刷新尝试计数
            tokenRefreshAttempts = 0
        } catch let error as APIError {
            print("❌ 注册失败: \(error.userFriendlyMessage)")
            throw AuthError.serverError(error.userFriendlyMessage)
        } catch {
            print("❌ 注册失败: \(error.localizedDescription)")
            throw AuthError.networkError(error.localizedDescription)
        }
    }

    @MainActor
    func resetPassword(email: String) async throws -> String {
        isLoading = true
        defer { isLoading = false }

        do {
            let response = try await APIService.shared.resetPassword(email: email)
            return response.message
        } catch let error as APIError {
            print("❌ 密码重置失败: \(error.userFriendlyMessage)")
            throw AuthError.serverError(error.userFriendlyMessage)
        } catch {
            print("❌ 密码重置失败: \(error.localizedDescription)")
            throw AuthError.networkError(error.localizedDescription)
        }
    }
    
    @MainActor
    func fetchUserProfile() async {
        do {
            let user = try await APIService.shared.getCurrentUserProfile()
            self.currentUser = user
            print("✅ 成功获取用户信息: \(user)")
        } catch {
            print("❌ 获取用户信息失败: \(error)")
        }
    }
    
    func logout() {
        // 停止令牌刷新定时器
        stopTokenRefreshTimer()
        
        // 尝试向服务器发送登出请求
        Task {
            do {
                let success = try await APIService.shared.logout()
                print("✅ 服务器登出状态: \(success ? "成功" : "失败")")
            } catch {
                print("❌ 服务器登出失败: \(error.localizedDescription)")
            }
            
            // 无论服务器响应如何，都清除本地状态
            await MainActor.run {
                currentUser = nil
                sessionCookies = nil
                csrfToken = nil
                isAuthenticated = false
                defaults.removeObject(forKey: tokenKey)
                APIService.shared.clearAuthToken()
            }
        }
    }
    
    func isLoggedIn() -> Bool {
        defaults.string(forKey: tokenKey) != nil
    }
    
    func getStoredToken() -> String? {
        return defaults.string(forKey: tokenKey)
    }
    
    func restoreSession() {
        if let token = getStoredToken() {
            APIService.shared.setAuthToken(token)
            isAuthenticated = true
            
            // 恢复会话后，异步获取用户信息
            Task {
                await fetchUserProfile()
            }
            
            // 启动令牌刷新定时器
            startTokenRefreshTimer()
        }
    }
    
    // 更新用户昵称
    func updateNickname(_ nickname: String) {
        // 如果当前没有用户信息，直接返回
        guard var user = currentUser else { return }
        
        // 更新用户昵称
        user.firstName = nickname
        
        // 保存更新后的用户信息
        currentUser = user
        
        // 发送通知以便UI更新
        NotificationCenter.default.post(name: Notification.Name("UserProfileUpdated"), object: nil)
    }
}

extension URLSession {
    static func allowsInsecureRequests() {
        let allowsArbitraryLoads = [
            "NSAppTransportSecurity": ["NSAllowsArbitraryLoads": true]
        ]
        UserDefaults.standard.register(defaults: allowsArbitraryLoads)
    }
} 