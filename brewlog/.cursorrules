!重要：每次修改代码前请先了解相关代码结构，不要重复造轮子

- 项目简介
  - 项目名称：咖啡札记（Brewlog）
  - 项目简介：kfdz网站项目my应用brewlog模块的iPhone版本应用，在web版的基础上实现更多iOS特性功能
  - 技术栈：
    - 前端：SwiftUI，遵循iOS原生设计规范
    - 后端：Swift
    - 最低支持 iOS 16.6（因为热力图用的chart需要高于iOS15）
  - 项目结构：
    - web端版本：/Users/<USER>/Workbench/website/kfdz/
    - 微信小程序版本：/Users/<USER>/Workbench/website/kfdz_miniapp/
    - iOS版本：/Users/<USER>/Workbench/apps/brewlog_ios/

- 接口处理原则
  - 由于iOS版是基于web端制作，除了iOS特有的设备功能外，其他的数据都应该直接从最成熟的web端获取
  - 优先使用Web端已有的模型方法和类
  - 避免重复实现业务逻辑，减少代码维护成本
  - 保持数据一致性，确保iOS和Web端使用相同的数据计算逻辑
  - /kfdz/iosapp 是连接web端（/kfdz/my/）和ios端（/brewlog_ios/）的桥梁，而不是重复造轮子

- 设计风格：
  - 基于iOS原生组件开发，尽可能还原Web版UI/UX（TailwindCSS+DaisyUI+Alpine.js）
  - 秉持简单好用的理念，设计风格参考App：GitHub、Drafts、DEVONthink、Narwhal
  - 所有页面需要通过主题控制，并且每个组件都优先使用划分好的主题色作用域
  - 保持MVVM结构清晰，共享可复用组件

- 主要功能模块:
  - 登录页
  - 冲煮记录
    - 冲煮记录列表页
    - 冲煮记录对比页
    - 冲煮记录详情页
    - 新增冲煮记录页
    - 修改冲煮记录页
  - 设备
    - 设备列表页
    - 设备详情页
    - 新增设备页
    - 修改设备页
  - 咖啡豆
    - 咖啡豆列表页
    - 咖啡豆详情页
    - 新增咖啡豆页
    - 修改咖啡豆页
  - 冲煮记录热力图页
  - 咖啡豆日历页
  - 配方册页
  - 后见之明页
  - 新用户引导页
  - 更新日志页
  - 更多页（iOS特有）
    - dock放不下的其他页面
    - 自定义底部菜单栏布局（主界面底部dock栏放哪4个页面+用户配置页）
    - 账号信息（订阅状态、资料信息、修改密码、导出数据、退出登录）
    - 高级功能（个性化、自动化）
    - 其他信息（关于、评分、反馈等）

- 关键技术点：
  - 数据模型、业务逻辑与web端（/kfdz/my/）保持一致，除非iOS特有功能（付费订阅等），否则应与web端保持一致。
  - 网络请求与API集成，单独接口需求在web端处理（/kfdz/iosapp），读取数据后在本地存储和操作（例如统计、日历等业务逻辑可以本地生成），增删改POST才对接web端。注意优化数据同步中断的问题。
  - 离线存储：缓存接口数据，避免每次切换页面都重复找接口要数据。但是增删改需要联网同步到web端才可操作
  - 状态管理
  - UI组件复用
  - 付费模型：免费版可用核心功能。高级版可解锁更多功能，AppStore 订阅制，可免费试用30天，订阅费9元/月，年费99元/年。
    - 免费核心功能：完整的记录、管理、基础统计
    - 高级付费功能：
      - 优先实现：轻扫选项，通知和提醒，菜单顺序，高级统计，更换主题色和图标
      - 其次实现：小组件，快捷方式，健康数据咖啡因摄入，接入WeatherKit/Homekit（自动填写环境数据）