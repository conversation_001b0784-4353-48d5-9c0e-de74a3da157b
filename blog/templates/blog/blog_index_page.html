{% extends 'base.html' %}
{% load wagtailcore_tags wagtailimages_tags %}

{% block content %}
<div class="md:grid md:grid-cols-3 md:gap-12 p-6 md:py-24 max-w-[56rem] mx-auto prose">
    <h1 class="mb-12">最新博文</h1>
    <div class="md:col-span-2 container flex flex-col gap-8">
        {% for post in page.get_posts %}
        <div class="border-solid border-black border-b-[1px] last-of-type:border-b-0 pb-4 md:pb-8">
            <h2 class="mt-0 mb-4 text-xl"><a href="{% pageurl post %}" class="link-hover">{{ post.title }}</a></h2>

            {% if post.specific.intro %}
            <h3 class="italic text-lg font-normal">{{ post.specific.intro }}</h3>
            {% endif %}

            <p class="font-mono font-light text-sm">
                {{ post.specific.date|date:"Y年m月d日" }}
            </p>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock content %}