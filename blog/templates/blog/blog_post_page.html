{% extends 'base.html' %}
{% load wagtailcore_tags wagtailimages_tags wagtailmarkdown %}

{% block content %}
    {% image page.main_image width-1000 as main_image %}
    {% if main_image %}
        <img class="h-56 w-full object-cover sm:h-72 md:h-96 lg:w-full" src="{{ main_image.url }}" alt="">
    {% endif %}

<div class="p-6 md:py-12 max-w-[44rem] mx-auto prose">
    <h1 class="mb-4">{{ page.title }}</h1>
    {% if post.specific.intro %}
    <h2 class="font-light italic !my-4">{{ page.intro }}</h2>
    {% endif %}
    <p class="text-sm font-mono font-light">
        发布日期：{{ page.date|date:"Y年m月d日" }}
    </p>
</div>

<div class="p-6 md:py-8 max-w-[44rem] mx-auto break-words">
    <div class="prose">
        {{ page.body|markdown }}
    </div>
</div>

{% endblock content %}