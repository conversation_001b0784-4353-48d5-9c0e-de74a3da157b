from django.db import models

from modelcluster.fields import Parental<PERSON>ey

from wagtail.models import Page, Orderable
from wagtail.fields import Rich<PERSON><PERSON>t<PERSON>ield, StreamField
from wagtail.admin.panels import FieldPanel, InlinePanel
from .blocks import <PERSON><PERSON><PERSON>
from datetime import datetime
from wagtailmarkdown.fields import MarkdownField

class BlogIndexPage(Page):
    intro = RichTextField(blank=True)

    @property
    def get_posts(self):
        return (
            BlogPostPage.objects.live().descendant_of(self).order_by('-date')
        )
    
    content_panels = Page.content_panels + [
        FieldPanel('intro', classname="full")
    ]


class BlogPostPage(Page):
    date = models.DateTimeField("发布日期", blank=True, null=True, default=datetime.today)
    intro = models.CharField("导读", blank=True, null=True, max_length=250)

    body = MarkdownField(blank=True)

    def main_image(self):
        gallery_item = self.gallery_images.first()
        if gallery_item:
            return gallery_item.image
        else:
            return None

    content_panels = Page.content_panels + [
        FieldPanel('date'),
        FieldPanel('intro'),
        FieldPanel('body'),
        InlinePanel('gallery_images', label="Gallery images"),
    ]


class BlogPostGalleryImage(Orderable):
    page = ParentalKey(BlogPostPage, on_delete=models.CASCADE, related_name='gallery_images')
    image = models.ForeignKey('wagtailimages.Image', on_delete=models.CASCADE, related_name="+")
    caption = models.CharField(blank=True, max_length=250)

    panels = [
        FieldPanel('image'),
        FieldPanel('caption')
    ]

