# Generated by Django 4.2.7 on 2024-11-19 09:55

from django.db import migrations
from django.db import models  # 添加这行
from django.contrib.postgres.fields import J<PERSON>NField
import wagtailmarkdown.fields

def convert_body_data(apps, schema_editor):
    BlogPostPage = apps.get_model('blog', 'BlogPostPage')
    for page in BlogPostPage.objects.all():
        if page.body:
            try:
                if isinstance(page.body, str):
                    continue
                
                markdown_content = []
                for block in page.body.raw_data:
                    if block['type'] == 'richtext':
                        content = block['value']
                        if hasattr(content, 'source'):
                            content = content.source
                        markdown_content.append(str(content))
                
                page.body = '\n\n'.join(markdown_content)
                page.save()
            except (AttributeError, TypeError):
                if hasattr(page.body, 'raw_data'):
                    page.body = str(page.body.raw_data)
                else:
                    page.body = str(page.body)
                page.save()

class Migration(migrations.Migration):

    dependencies = [
        ('blog', '0001_initial'),  # 替换为实际的前一个迁移
    ]

    operations = [
        migrations.AlterField(
            model_name='blogpostpage',
            name='body',
            field=models.TextField(blank=True, null=True),  # 使用 models.TextField
        ),
        migrations.RunPython(convert_body_data),
        migrations.AlterField(
            model_name='blogpostpage',
            name='body',
            field=wagtailmarkdown.fields.MarkdownField(blank=True),
        ),
    ]