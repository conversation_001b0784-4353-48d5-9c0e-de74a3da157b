import '../css/brewlog.css'

// 获取 CSRF token 的函数
function getCsrfToken() {
    const name = 'csrftoken';
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

document.addEventListener('alpine:init', () => {
    // 添加筛选状态管理
    Alpine.store('filterState', {
        isOpen: window.innerWidth >= 768,
        
        toggle() {
            this.isOpen = !this.isOpen;
        },
        
        init() {
            // 设置初始状态
            this.isOpen = window.innerWidth >= 768;
            
            // 添加防抖处理的 resize 事件监听
            let resizeTimer;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(() => {
                    this.isOpen = window.innerWidth >= 768;
                }, 100);
            });
        }
    });

    Alpine.store('recipeList', {
        async toggleRecipeTag(recipeId, tagId) {
            // 找到对应的按钮并添加loading状态
            const button = document.querySelector(`#manage_recipe_tags_modal_${recipeId} button[data-tag-id="${tagId}"]`);
            if (button) {
                button.classList.add('loading');
                button.disabled = true;
            }

            try {
                const response = await fetch(`/my/brewlog/recipes/${recipeId}/tag/${tagId}/toggle/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    // 更新按钮状态
                    if (button) {
                        if (data.action === 'added') {
                            button.classList.remove('btn-outline', 'loading');
                            button.classList.add('btn-primary');
                        } else {
                            button.classList.remove('btn-primary', 'loading');
                            button.classList.add('btn-outline');
                        }
                        button.disabled = false;
                    }
                    
                    // 更新配方卡片上的标签显示
                    const recipeCard = document.querySelector(`#recipe_${recipeId}`);
                    if (recipeCard) {
                        const tagsContainer = recipeCard.querySelector('.tags-container');
                        if (tagsContainer) {
                            if (data.tags && data.tags.length > 0) {
                                tagsContainer.innerHTML = data.tags.map(tag => 
                                    `<span class="badge badge-soft rounded-full shadow-xs badge-info">${tag.name}</span>`
                                ).join('');
                            } else {
                                tagsContainer.innerHTML = '';
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                // 移除loading状态
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            }
        },

        async previewQuickBrew(recipeId) {
            try {
                const response = await fetch(`/my/brewlog/recipes/${recipeId}/preview/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                
                if (response.ok) {
                    const html = await response.text();
                    const previewContent = document.getElementById('preview_content');
                    previewContent.innerHTML = html;
                    document.getElementById('preview_quick_brew_modal').showModal();
                } else {
                    alert('预览加载失败');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('预览加载失败');
            }
        },

        quickBrew(recipeId) {
            fetch(`/my/brewlog/recipes/${recipeId}/quick-brew/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.href = data.redirect_url;
                } else {
                    alert(data.message || '操作失败');
                }
            });
        },

        async renameRecipe(recipeId, newName) {
            try {
                const response = await fetch(`/my/brewlog/recipes/${recipeId}/rename/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: new URLSearchParams({
                        'new_name': newName
                    })
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.reload();
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                throw error;
            }
        },

        async editTag(tagId, newName) {
            const modal = document.getElementById('manage_tags_modal');
            const submitBtn = modal.querySelector(`button[data-tag-id="${tagId}"]`);
            
            try {
                if (submitBtn) {
                    submitBtn.classList.add('loading');
                    submitBtn.disabled = true;
                }
                
                const response = await fetch('/my/brewlog/recipes/tag/manage/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: new URLSearchParams({
                        'action': 'edit',
                        'tag_id': tagId,
                        'tag_name': newName
                    })
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.reload();
                }
            } catch (error) {
                console.error('Error:', error);
                alert('标签修改失败');
            } finally {
                if (submitBtn) {
                    submitBtn.classList.remove('loading');
                    submitBtn.disabled = false;
                }
            }
        },

        async deleteTag(tagId) {
            if (!confirm('确定要删除这个标签吗？')) return;
            
            const modal = document.getElementById('manage_tags_modal');
            const deleteBtn = modal.querySelector(`button[data-delete-tag-id="${tagId}"]`);
            
            try {
                if (deleteBtn) {
                    deleteBtn.classList.add('loading');
                    deleteBtn.disabled = true;
                }
                
                const response = await fetch('/my/brewlog/recipes/tag/manage/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: new URLSearchParams({
                        'action': 'delete',
                        'tag_id': tagId
                    })
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.reload();
                }
            } catch (error) {
                console.error('Error:', error);
                alert('删除标签失败');
            } finally {
                if (deleteBtn) {
                    deleteBtn.classList.remove('loading');
                    deleteBtn.disabled = false;
                }
            }
        }
    });

    Alpine.data('tagManager', () => ({
        newTagName: '',
        isSubmitting: false,
        
        async addTag() {
            if (!this.newTagName.trim() || this.isSubmitting) return;
            
            this.isSubmitting = true;
            
            try {
                const response = await fetch('/my/brewlog/recipes/tag/manage/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: new URLSearchParams({
                        'action': 'add',
                        'tag_name': this.newTagName
                    })
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.reload();
                }
            } catch (error) {
                console.error('Error:', error);
                alert('添加标签失败');
            } finally {
                this.isSubmitting = false;
            }
            
            this.newTagName = '';
        },

        editTag(tagId, newName) {
            fetch('/my/brewlog/recipes/tag/manage/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCsrfToken()
                },
                body: new URLSearchParams({
                    'action': 'edit',
                    'tag_id': tagId,
                    'tag_name': newName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.reload();
                }
            });
        },

        deleteTag(tagId) {
            if (!confirm('确定要删除这个标签吗？')) return;
            
            fetch('/my/brewlog/recipes/tag/manage/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCsrfToken()
                },
                body: new URLSearchParams({
                    'action': 'delete',
                    'tag_id': tagId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.reload();
                }
            });
        }
    }));
}); 