import '../css/brewlog.css'

// 获取 CSRF token 的函数
function getCsrfToken() {
    const name = 'csrftoken';
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

document.addEventListener('DOMContentLoaded', function() {
    // 处理下拉菜单的点击事件
    document.addEventListener('click', function(event) {
        const dropdowns = document.querySelectorAll('[data-dropdown-toggle]');
        dropdowns.forEach(dropdown => {
            const trigger = dropdown.previousElementSibling;
            if (!dropdown.contains(event.target) && !trigger.contains(event.target)) {
                dropdown.parentElement.removeAttribute('open');
            }
        });
    });

    const exportButton = document.getElementById('exportButton');
    if (exportButton) {
        exportButton.addEventListener('click', function(e) {
            e.preventDefault();
            // 显示导出确认对话框
            const exportDialog = document.getElementById('exportDialog');
            if (exportDialog) {
                exportDialog.showModal();
            }
        });
    }

    // 处理导出确认对话框中的确认按钮
    const confirmExportButton = document.querySelector('#exportDialog a.btn-success');
    if (confirmExportButton) {
        confirmExportButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 创建一个隐藏的 form 来处理下载
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = this.href;
            document.body.appendChild(form);
            
            // 提交表单触发下载
            form.submit();
            document.body.removeChild(form);
            
            // 关闭对话框
            document.getElementById('exportDialog').close();
            
            // 延迟刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });
    }

    // 处理筛选表单
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        const dateInputs = filterForm.querySelectorAll('input[type="date"]');
        const searchInput = filterForm.querySelector('input[name="q"]');
        
        // 移除日期输入的自动提交
        dateInputs.forEach(input => {
            input.addEventListener('change', function(e) {
                e.preventDefault();
            });
        });
        
        // 监听搜索框回车
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                filterForm.submit();
            }
        });
    }

    // 监听日期输入框变化
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            // 添加短暂延迟,让用户有机会选择第二个日期
            setTimeout(() => {
                const form = document.getElementById('filterForm');
                form.submit();
            }, 1000);
        });
    });
});

// deletion and archiving in brewlog equipment list
document.addEventListener('alpine:init', () => {
    // 修改 filterState store
    Alpine.store('filterState', {
        isOpen: false,
        init() {
            // 移除自动收起的定时器
            this.isOpen = false;
        },
        toggle() {
            this.isOpen = !this.isOpen;
        }
    });

    Alpine.store('brewlogList', {
        // 添加对比功能相关的状态和方法
        compareMode: false,
        selectedRecords: [],
        maxCompareCount: 2,

        init() {
            // 从 localStorage 恢复状态
            const savedState = localStorage.getItem('compareState');
            if (savedState) {
                const state = JSON.parse(savedState);
                this.selectedRecords = state.selectedRecords;
                this.compareMode = state.compareMode;
            }
        },

        toggleCompare(recordId) {
            if (this.selectedRecords.includes(recordId)) {
                this.selectedRecords = this.selectedRecords.filter(id => id !== recordId);
            } else if (this.selectedRecords.length < this.maxCompareCount) {
                this.selectedRecords.push(recordId);
            }
            
            if (this.selectedRecords.length === this.maxCompareCount) {
                window.location.href = `/my/brewlog/compare/${this.selectedRecords[0]}/${this.selectedRecords[1]}/`;
            }
            
            this.compareMode = this.selectedRecords.length > 0;
            // 保存状态到 localStorage
            localStorage.setItem('compareState', JSON.stringify({
                selectedRecords: this.selectedRecords,
                compareMode: this.compareMode
            }));
        },
        
        clearCompare() {
            this.selectedRecords = [];
            this.compareMode = false;
            // 清除 localStorage 中的状态
            localStorage.removeItem('compareState');
        },

        async deleteEquipment(id) {
            try {
                const response = await fetch(`/my/brewlog/equipment/${id}/delete/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.href = '/my/brewlog/equipment/';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('删除失败');
            }
        },
        async deleteRecord(id) {
            try {
                const response = await fetch(`/my/brewlog/record/${id}/delete/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.href = '/my/brewlog';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('删除失败');
            }
        },
        async archiveEquipment(id) {
            try {
                const response = await fetch(`/my/brewlog/equipment/${id}/archive/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.href = '/my/brewlog/equipment/';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('归档失败');
            }
        },
        async unarchiveEquipment(id) {
            try {
                const response = await fetch(`/my/brewlog/equipment/${id}/unarchive/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.href = '/my/brewlog/equipment/';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('取消归档失败');
            }
        },
        async toggleFavoriteEquipment(id) {
            try {
                // 获取设备元素以检查是否归档
                const equipmentCard = document.querySelector(`[data-equipment-id="${id}"]`);
                const isArchived = equipmentCard && equipmentCard.closest('.collapse-content') !== null;
                
                // 如果已归档，先取消归档
                if (isArchived) {
                    await this.unarchiveEquipment(id);
                }
                
                // 然后设置首选状态
                const response = await fetch(`/my/brewlog/equipment/${id}/toggle-favorite/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.href = '/my/brewlog/equipment/';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('操作失败，请稍后再试');
            }
        }
    });
});