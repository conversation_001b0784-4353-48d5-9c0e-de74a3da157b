import Alpine from 'alpinejs'
import 'htmx.org'
import '../css/main.css';

window.htmx = require('htmx.org')

// 为所有 HTMX 请求添加 CSRF token
document.body.addEventListener('htmx:configRequest', (event) => {
    // 从 cookie 中获取 CSRF token
    const csrfToken = document.cookie
        .split('; ')
        .find(row => row.startsWith('csrftoken='))
        ?.split('=')[1];
    
    if (csrfToken) {
        event.detail.headers['X-CSRFToken'] = csrfToken;
    }
});

window.Alpine = Alpine
Alpine.start()