// 全局函数，用于处理回购记录按钮点击
window.handleOccurrenceAction = function(event, action, id) {
    // 阻止事件冒泡和默认行为
    event.preventDefault();
    event.stopPropagation();
    
    // 阻止事件传播到父元素
    event.stopImmediatePropagation();
    
    // 根据动作类型调用相应的函数
    if (action === 'edit') {
        editOccurrence(id);
    } else if (action === 'delete') {
        deleteOccurrence(id);
    }
};

// 添加显示 toast 提示的函数
function showSuccessToast() {
    const toast = document.createElement('div');
    toast.className = 'toast toast-bottom toast-center';
    toast.innerHTML = `
        <div class="alert alert-success">
            <span>回购信息保存成功。</span>
        </div>
    `;
    document.body.appendChild(toast);
    
    // 3秒后自动移除
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 添加一个公共函数来更新Alpine.js状态和UI
function updateAlpineRestPeriodUI(modal, isRange, restPeriodLabel, maxPeriodContainer) {
    // 手动控制容器的显示/隐藏（作为备用方案）
    if (maxPeriodContainer && !window.Alpine) {
        maxPeriodContainer.style.display = isRange ? 'flex' : 'none';
    }
    
    // 仅当标签没有x-text属性时才手动更新标签文本
    if (restPeriodLabel && !restPeriodLabel.hasAttribute('x-text')) {
        restPeriodLabel.textContent = isRange ? '最短养豆期(天)' : '养豆天数';
    }
    
    try {
        const modalBox = modal.querySelector('.modal-box');
        if (modalBox && typeof Alpine !== 'undefined') {
            // 检查是否有x-data属性表明使用了Alpine.js
            if (modalBox.hasAttribute('x-data')) {
                // Alpine v3
                if (typeof Alpine.$data === 'function') {
                    const data = Alpine.$data(modalBox);
                    if (data) {
                        data.isRangePeriod = isRange;
                    }
                } 
                // Alpine v2
                else if (modalBox.__x) {
                    modalBox.__x.$data.isRangePeriod = isRange;
                }
            }
        }
    } catch (e) {
        console.warn('Alpine更新失败:', e);
        // 当Alpine更新失败时，确保标签文本仍然正确
        if (restPeriodLabel) {
            restPeriodLabel.textContent = isRange ? '最短养豆期(天)' : '养豆天数';
        }
    }
}

// 添加回购记录时的养豆期切换事件处理
function setupRestPeriodToggle(restPeriodTypeToggle, restPeriodLabel, modal, maxPeriodContainer) {
    if (restPeriodTypeToggle) {
        // 移除旧的事件监听器 (通过克隆节点的方式)
        const oldToggle = restPeriodTypeToggle;
        const newToggle = oldToggle.cloneNode(true);
        oldToggle.parentNode.replaceChild(newToggle, oldToggle);
        restPeriodTypeToggle = newToggle;
        
        // 创建事件处理函数
        function handleToggleChange() {
            const isChecked = this.checked;
            
            // 如果不使用Alpine.js，则需要手动控制显示/隐藏
            if (maxPeriodContainer && !window.Alpine) {
                maxPeriodContainer.style.display = isChecked ? 'flex' : 'none';
            }
            
            // 当切换回单一值模式时，清空最大值输入框
            if (!isChecked) {
                const form = modal.querySelector('form');
                if (form) {
                    const maxInput = form.querySelector('input[name="rest_period_max"]');
                    if (maxInput) {
                        maxInput.value = '';
                    }
                }
            }
            
            // 在状态变化后，尝试强制更新Alpine.js绑定
            try {
                const modalBox = modal.querySelector('.modal-box');
                if (modalBox && typeof Alpine !== 'undefined') {
                    // Alpine v3
                    if (typeof Alpine.$data === 'function') {
                        const data = Alpine.$data(modalBox);
                        if (data) {
                            data.isRangePeriod = isChecked;
                        }
                    } 
                    // Alpine v2
                    else if (modalBox.__x) {
                        modalBox.__x.$data.isRangePeriod = isChecked;
                    }
                    
                    // 如果Alpine.js没能正确更新DOM，手动更新标签文本
                    if (restPeriodLabel && !restPeriodLabel.hasAttribute('x-text')) {
                        restPeriodLabel.textContent = isChecked ? '最短养豆期(天)' : '养豆天数';
                    }
                }
            } catch (e) {
                console.warn('Alpine更新失败:', e);
                // 回退到直接设置标签文本
                if (restPeriodLabel) {
                    restPeriodLabel.textContent = isChecked ? '最短养豆期(天)' : '养豆天数';
                }
            }
        }
        
        // 添加事件监听器
        restPeriodTypeToggle.addEventListener('change', handleToggleChange);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // 定义关闭模态框的函数
    window.closeOccurrenceModal = function() {
        const modal = document.getElementById('occurrence-modal');
        if (modal) {
            modal.close();
        }
    };

    // 使用事件委托处理回购按钮点击
    document.addEventListener('click', async function(e) {
        if (e.target.matches('.occurrence-btn[data-action="repurchase"]') || 
            e.target.closest('.occurrence-btn[data-action="repurchase"]')) {
            e.preventDefault();
            
            // 获取按钮元素（可能是子元素被点击）
            const button = e.target.matches('.occurrence-btn') ? e.target : e.target.closest('.occurrence-btn');
            const beanId = button.getAttribute('data-bean-id');
            
            // 检查咖啡豆是否已归档
            const beanCard = button.closest('[data-bean-id]');
            const isArchived = beanCard && beanCard.getAttribute('data-archived') === 'true';
            
            // 如果已归档，先取消归档
            if (isArchived) {
                try {
                    const response = await fetch(`/my/brewlog/bean/${beanId}/unarchive/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    });
                    
                    if (!response.ok) {
                        throw new Error('取消归档失败');
                    }
                } catch (error) {
                    console.error('取消归档失败:', error);
                    alert('无法取消归档，请重试');
                    return;
                }
            }
            
            const modal = document.getElementById('occurrence-modal');
            const form = document.getElementById('occurrence-form');
            
            // 清除之前可能存在的数据属性
            form.removeAttribute('data-occurrence-id');
            form.setAttribute('data-bean-id', beanId);
            
            // 获取当前咖啡豆的信息并填充表单
            try {
                const response = await fetch(`/my/bean/${beanId}/occurrence/add/`);
                
                if (!response.ok) {
                    const text = await response.text();
                    throw new Error(`HTTP ${response.status}: ${text}`);
                }
                
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }
                
                // 填充表单字段
                const bagWeightInput = form.querySelector('input[name="bag_weight"]');
                const bagRemainInput = form.querySelector('input[name="bag_remain"]');
                const purchasePriceInput = form.querySelector('input[name="purchase_price"]');
                const roastDateInput = form.querySelector('input[name="roast_date"]');
                const createdAtInput = form.querySelector('input[name="created_at"]');
                
                // 设置默认值
                bagWeightInput.value = data.bag_weight;
                bagRemainInput.value = data.bag_remain;
                purchasePriceInput.value = data.purchase_price;
                
                // 设置当前日期为默认烘焙日期
                const today = new Date();
                const formattedDate = today.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
                roastDateInput.value = formattedDate;
                
                // 设置当前时间为回购时间
                createdAtInput.value = data.created_at;
                
                // 设置养豆期字段
                const restPeriodMinInput = form.querySelector('input[name="rest_period_min"]');
                const restPeriodMaxInput = form.querySelector('input[name="rest_period_max"]');
                const restPeriodTypeToggle = form.querySelector('input[data-rest-period-type]');
                const restPeriodLabel = form.querySelector('.rest-period-label');
                const maxPeriodContainer = form.querySelector('.max-period-container');
                
                if (restPeriodMinInput && restPeriodMaxInput) {
                    restPeriodMinInput.value = data.rest_period_min || '';
                    restPeriodMaxInput.value = data.rest_period_max || '';
                    
                    // 修改判断逻辑：当rest_period_max存在且大于0，并且与rest_period_min不同时，设置为范围模式
                    const isRange = data.rest_period_max !== null && 
                                  data.rest_period_max !== undefined && 
                                  data.rest_period_max > 0 &&
                                  data.rest_period_max !== data.rest_period_min;
                    
                    // 必须设置复选框状态
                    if (restPeriodTypeToggle) {
                        restPeriodTypeToggle.checked = isRange;
                    }
                    
                    // 设置Alpine.js的isRangePeriod值
                    try {
                        const modalBox = modal.querySelector('.modal-box');
                        if (modalBox && typeof Alpine !== 'undefined') {
                            // 检查是否有x-data属性表明使用了Alpine.js
                            if (modalBox.hasAttribute('x-data')) {
                                // Alpine v3
                                if (typeof Alpine.$data === 'function') {
                                    const data = Alpine.$data(modalBox);
                                    if (data) {
                                        data.isRangePeriod = isRange;
                                    }
                                } 
                                // Alpine v2
                                else if (modalBox.__x) {
                                    modalBox.__x.$data.isRangePeriod = isRange;
                                }
                            }
                        }
                    } catch (e) {
                        console.warn('Alpine设置失败:', e);
                    }
                    
                    // 更新标签文本
                    if (restPeriodLabel && !restPeriodLabel.hasAttribute('x-text')) {
                        restPeriodLabel.textContent = isRange ? '最短养豆期(天)' : '养豆天数';
                    }
                    
                    // 显示模态框
                    modal.showModal();
                    
                    // 立即尝试更新一次
                    updateAlpineRestPeriodUI(modal, isRange, restPeriodLabel, maxPeriodContainer);
                    
                    // 设置养豆期切换事件
                    setupRestPeriodToggle(restPeriodTypeToggle, restPeriodLabel, modal, maxPeriodContainer);
                    
                    // 延迟再次更新以确保Alpine.js状态正确
                    setTimeout(() => {
                        updateAlpineRestPeriodUI(modal, isRange, restPeriodLabel, maxPeriodContainer);
                    }, 50);
                } else {
                    // 如果找不到必要的输入字段，仍然显示模态框
                    modal.showModal();
                }
            } catch (error) {
                console.error('Error:', error);
                alert('获取数据失败，请重试');
            }
        } else if (e.target.matches('.occurrence-btn[data-action="edit"]')) {
            e.preventDefault();
            const occurrenceId = e.target.getAttribute('data-bean-id');
            editOccurrence(occurrenceId);
        } else if (e.target.matches('.occurrence-btn[data-action="delete"]')) {
            e.preventDefault();
            const occurrenceId = e.target.getAttribute('data-bean-id');
            deleteOccurrence(occurrenceId);
        } else if (e.target.matches('.modal-close-btn')) {
            e.preventDefault();
            closeOccurrenceModal();
        }
    });

    const occurrenceForm = document.getElementById('occurrence-form');
    if (occurrenceForm) {
        const defaultSubmitHandler = async function(e) {
            e.preventDefault();
            const beanId = this.getAttribute('data-bean-id');
            const formData = new FormData(this);
            
            // 检查养豆期类型
            const restPeriodTypeToggle = this.querySelector('input[data-rest-period-type]');
            if (restPeriodTypeToggle) {
                // 如果是单一值模式，无论max是什么值，都清空
                if (!restPeriodTypeToggle.checked) {
                    formData.delete('rest_period_max');
                } 
                // 如果是范围模式但max值为空或0，确保也删除防止后端错误
                else {
                    const maxValue = formData.get('rest_period_max');
                    if (!maxValue || maxValue === '0') {
                        formData.delete('rest_period_max');
                    }
                }
            }
            
            try {
                const response = await fetch(`/my/bean/${beanId}/occurrence/add/`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                });
                
                if (response.ok) {
                    closeOccurrenceModal();
                    showSuccessToast();  // 添加成功提示
                    window.location.reload();
                } else {
                    const data = await response.json();
                    // 处理错误信息
                    let errorMessage = '';
                    if (data.errors) {
                        errorMessage = Object.entries(data.errors)
                            .map(([field, errors]) => `${field}: ${errors.join(', ')}`)
                            .join('\n');
                    } else {
                        errorMessage = '提交失败，请检查输入';
                    }
                    alert(errorMessage);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('发生错误，请重试');
            }
        };
        
        occurrenceForm.addEventListener('submit', defaultSubmitHandler);
        occurrenceForm.defaultSubmitHandler = defaultSubmitHandler;
    }

    // 为删除模态框的关闭按钮添加事件处理
    const deleteModalCloseBtns = document.querySelectorAll('#delete-occurrence-modal .modal-close-btn');
    deleteModalCloseBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            closeDeleteModal();
        });
    });
});

async function editOccurrence(occurrenceId) {
    try {
        const response = await fetch(`/my/occurrence/${occurrenceId}/edit/`);
        if (response.ok) {
            const data = await response.json();
            
            const modal = document.getElementById('occurrence-modal');
            const form = document.getElementById('occurrence-form');
            
            if (!form) {
                console.error('Form element not found');
                return;
            }
            
            // 重置表单状态
            form.reset();
            
            // 设置 Alpine.js 数据
            const formScope = Alpine.evaluate(form, 'occurrenceId');
            if (formScope) {
                formScope.occurrenceId = occurrenceId;
            }
            
            form.removeAttribute('data-bean-id');
            form.setAttribute('data-occurrence-id', occurrenceId);
            
            form.querySelector('input[name="bag_weight"]').value = data.bag_weight;
            form.querySelector('input[name="bag_remain"]').value = data.bag_remain;
            form.querySelector('input[name="purchase_price"]').value = data.purchase_price;
            form.querySelector('input[name="roast_date"]').value = data.roast_date;
            form.querySelector('input[name="created_at"]').value = data.created_at;
            
            // 设置养豆期字段
            const restPeriodMinInput = form.querySelector('input[name="rest_period_min"]');
            const restPeriodMaxInput = form.querySelector('input[name="rest_period_max"]');
            const restPeriodTypeToggle = form.querySelector('input[data-rest-period-type]');
            const restPeriodLabel = form.querySelector('.rest-period-label');
            const maxPeriodContainer = form.querySelector('.max-period-container');
            
            if (restPeriodMinInput && restPeriodMaxInput) {
                restPeriodMinInput.value = data.rest_period_min || '';
                restPeriodMaxInput.value = data.rest_period_max || '';
                
                // 修改判断逻辑：当rest_period_max存在且大于0，并且与rest_period_min不同时，设置为范围模式
                const isRange = data.rest_period_max !== null && 
                              data.rest_period_max !== undefined && 
                              data.rest_period_max > 0 &&
                              data.rest_period_max !== data.rest_period_min;
                
                // 必须设置复选框状态
                if (restPeriodTypeToggle) {
                    restPeriodTypeToggle.checked = isRange;
                }
                
                // 设置Alpine.js的isRangePeriod值
                try {
                    const modalBox = modal.querySelector('.modal-box');
                    if (modalBox && typeof Alpine !== 'undefined') {
                        // 检查是否有x-data属性表明使用了Alpine.js
                        if (modalBox.hasAttribute('x-data')) {
                            // Alpine v3
                            if (typeof Alpine.$data === 'function') {
                                const data = Alpine.$data(modalBox);
                                if (data) {
                                    data.isRangePeriod = isRange;
                                }
                            } 
                            // Alpine v2
                            else if (modalBox.__x) {
                                modalBox.__x.$data.isRangePeriod = isRange;
                            }
                        }
                    }
                } catch (e) {
                    console.warn('Alpine设置失败:', e);
                }
                
                // 更新标签文本
                if (restPeriodLabel && !restPeriodLabel.hasAttribute('x-text')) {
                    restPeriodLabel.textContent = isRange ? '最短养豆期(天)' : '养豆天数';
                }
                
                // 显示模态框
                modal.showModal();
                
                // 立即尝试更新一次
                updateAlpineRestPeriodUI(modal, isRange, restPeriodLabel, maxPeriodContainer);
                
                // 设置养豆期切换事件
                setupRestPeriodToggle(restPeriodTypeToggle, restPeriodLabel, modal, maxPeriodContainer);
                
                // 延迟再次更新以确保Alpine.js状态正确
                setTimeout(() => {
                    updateAlpineRestPeriodUI(modal, isRange, restPeriodLabel, maxPeriodContainer);
                }, 50);
            } else {
                // 如果找不到必要的输入字段，仍然显示模态框
                modal.showModal();
            }
            
            const editSubmitHandler = async function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                
                // 检查养豆期类型
                const restPeriodTypeToggle = this.querySelector('input[data-rest-period-type]');
                if (restPeriodTypeToggle) {
                    // 如果是单一值模式，无论max是什么值，都清空
                    if (!restPeriodTypeToggle.checked) {
                        formData.delete('rest_period_max');
                    } 
                    // 如果是范围模式但max值为空或0，确保也删除防止后端错误
                    else {
                        const maxValue = formData.get('rest_period_max');
                        if (!maxValue || maxValue === '0') {
                            formData.delete('rest_period_max');
                        }
                    }
                }
                
                try {
                    const response = await fetch(`/my/occurrence/${occurrenceId}/edit/`, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    });
                    
                    if (response.ok) {
                        closeOccurrenceModal();
                        showSuccessToast();  // 添加成功提示
                        window.location.reload();
                    } else {
                        const data = await response.json();
                        alert(Object.values(data.errors).join('\n'));
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('发生错误，请重试');
                }
            };
            
            // 移除所有现有的提交事件监听器
            form.replaceWith(form.cloneNode(true));
            const newForm = document.getElementById('occurrence-form');
            newForm.addEventListener('submit', editSubmitHandler);
        } else {
            alert('获取数据失败，请重试');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('发生错误，请重试');
    }
}

async function deleteOccurrence(occurrenceId) {
    const modal = document.getElementById('delete-occurrence-modal');
    const confirmBtn = document.getElementById('confirm-delete-btn');
    
    // 存储当前的点击处理函数
    const currentClickHandler = async () => {
        try {
            const response = await fetch(`/my/occurrence/${occurrenceId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });
            
            if (response.ok) {
                closeDeleteModal();
                window.location.reload();
            } else {
                const data = await response.json();
                alert(data.error || '删除失败，请重试');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('发生错误，请重试');
        }
    };

    // 显示模态框
    modal.showModal();

    // 绑定确认按钮点击事件
    confirmBtn.onclick = currentClickHandler;
}

// 添加关闭删除模态框的函数
function closeDeleteModal() {
    const modal = document.getElementById('delete-occurrence-modal');
    if (modal) {
        modal.close();
        // 清除确认按钮的点击事件处理函数
        document.getElementById('confirm-delete-btn').onclick = null;
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

document.addEventListener('alpine:init', () => {
    Alpine.data('repurchaseModal', () => ({
        isRangePeriod: false,
        init() {
            // 监听模态框显示事件
            this.$el.addEventListener('show', (event) => {
                const data = event.detail;
                if (data) {
                    // 设置养豆期类型
                    this.isRangePeriod = data.rest_period_max ? true : false;
                }
            });

            // 监听表单提交
            const form = this.$el.querySelector('#occurrence-form');
            if (form) {
                form.addEventListener('submit', (event) => {
                    event.preventDefault();
                    const formData = new FormData(form);
                    
                    // 如果不是范围模式，清除最大值
                    if (!this.isRangePeriod) {
                        formData.delete('rest_period_max');
                    }

                    // 发送请求
                    fetch(form.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            showSuccessMessage();
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            // 显示错误信息
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'alert alert-error';
                            errorDiv.innerHTML = `
                                <span>${data.message || '保存失败，请重试。'}</span>
                            `;
                            document.body.appendChild(errorDiv);
                            setTimeout(() => {
                                errorDiv.remove();
                            }, 3000);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
                });
            }
        }
    }));
}); 