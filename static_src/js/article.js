// Table of Contents
const articleContent = document.getElementById('articleContent');
const headings = articleContent.querySelectorAll('h1, h2, h3, h4, h5, h6');

if (headings.length > 0) {
  const tocContainer = document.createElement('details');
  tocContainer.classList.add('collapse', 'bg-base-200');

  const tocTitle = document.createElement('summary');
  tocTitle.textContent = '❖ 展开文章目录';
  tocTitle.classList.add('collapse-title');
  tocContainer.appendChild(tocTitle);

  const tocDiv = document.createElement('div');
  tocDiv.classList.add('collapse-content');
  const tocList = document.createElement('ul');
  let previousLevel = 1;
  let listParents = [tocList];

  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1));

    while (level <= previousLevel) {
      listParents.pop();
      previousLevel--;
    }

    const anchor = document.createElement('a');
    anchor.setAttribute('href', `#section-${index + 1}`);
    anchor.textContent = heading.textContent;

    const listItem = document.createElement('li');
    listItem.appendChild(anchor);

    listParents[listParents.length - 1].appendChild(listItem);

    if (level > previousLevel) {
      const newList = document.createElement('ul');
      listItem.appendChild(newList);
      listParents.push(newList);
      previousLevel++;
    }

    heading.setAttribute('id', `section-${index + 1}`);
  });

  tocDiv.appendChild(tocList);
  tocContainer.appendChild(tocDiv);
  articleContent.insertBefore(tocContainer, articleContent.firstChild);

  // 监听 details 元素的 toggle 事件
  tocContainer.addEventListener('toggle', (event) => {
    // 如果 details 元素是打开状态，显示 '👉 收起文章目录'
    if (tocContainer.open) {
      tocTitle.textContent = '◈ 收起文章目录';
    } else {
      // 如果 details 元素是关闭状态，显示 '👉 展开文章目录'
      tocTitle.textContent = '❖ 展开文章目录';
    }
  });

}