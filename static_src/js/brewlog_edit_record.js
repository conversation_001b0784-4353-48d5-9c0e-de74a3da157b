import '../css/brewlogeditrecord.css'

// brewlog record, edit/add, brewing time input
document.addEventListener('DOMContentLoaded', () => {
    // Brewing Time Inputs
    function initBrewingTimeInputs() {
        const hoursInput = document.getElementById('brewing_hours');
        const minutesInput = document.getElementById('brewing_minutes');
        const secondsInput = document.getElementById('brewing_seconds');
        const hiddenInput = document.querySelector('input[name="brewing_time"]');
    
        if (!hoursInput || !minutesInput || !secondsInput || !hiddenInput) return;
    
        // 初始化时设置默认值
        if (hiddenInput.value) {
            const timeParts = hiddenInput.value.split(':');
            if (timeParts.length === 3) {
                hoursInput.value = parseInt(timeParts[0], 10).toString().padStart(2, '0');
                minutesInput.value = parseInt(timeParts[1], 10).toString().padStart(2, '0');
                secondsInput.value = parseInt(timeParts[2], 10).toString().padStart(2, '0');
                updateHiddenInput();
            }
        }
    
        function updateHiddenInput() {
            const hours = parseInt(hoursInput.value || 0);
            const minutes = parseInt(minutesInput.value || 0);
            const seconds = parseInt(secondsInput.value || 0);
            
            // 格式化为 HH:MM:SS
            const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            hiddenInput.value = formattedTime;
        }
    
        // 确保表单提交前更新隐藏输入框
        const form = hiddenInput.closest('form');
        if (form) {
            form.addEventListener('submit', (e) => {
                updateHiddenInput();
            });
        }
    
        // 监听输入变化
        [hoursInput, minutesInput, secondsInput].forEach(input => {
            input.addEventListener('input', () => {
                let value = parseInt(input.value) || 0;
                if (input === hoursInput) {
                    value = Math.max(value, 0); // 小时不设上限
                } else {
                    value = Math.min(Math.max(value, 0), 59); // 分钟和秒限制在 0-59
                }
                input.value = value;
                updateHiddenInput();
            });
    
            // 处理失焦事件，确保显示两位数
            input.addEventListener('blur', () => {
                input.value = (parseInt(input.value) || 0).toString().padStart(2, '0');
                updateHiddenInput();
            });
        });
    }

    // Rating Input
    function initRatingInput() {
        const rangeInput = document.querySelector('input[type="range"]');
        const hiddenInput = document.querySelector('input[name="rating_level"]');

        if (rangeInput && hiddenInput) {
            rangeInput.addEventListener('input', () => {
                hiddenInput.value = rangeInput.value;
            });
            // 初始化隐藏输入值
            hiddenInput.value = rangeInput.value;
        }
    }

    // 初始化磨豆机选择和研磨度自动填充
    function initGrinderSelection() {
        const grindingEquipmentSelect = document.querySelector('select[name="grinding_equipment"]');
        const grindSizeInput = document.querySelector('input[name="grind_size"]');
        
        if (grindingEquipmentSelect && grindSizeInput) {
            // 判断是否为纯新增记录（不是编辑也不是复制）
            const form = document.querySelector('form');
            const formAction = form?.getAttribute('action') || '';
            const isNewRecord = (formAction.includes('/add/') || formAction.includes('add_record_page')) 
                && !formAction.includes('copy');  // 排除复制记录的情况
            
            // 如果是纯新增记录，且已选择磨豆机，则获取预设值
            if (isNewRecord && !grindSizeInput.value) {  // 只在研磨度为空时自动填充
                const initialGrinderId = grindingEquipmentSelect.value;
                // 确保在DOM完全加载后执行
                setTimeout(() => {
                    if (initialGrinderId) {
                        fetchAndSetGrindSize(initialGrinderId, grindSizeInput);
                    }
                }, 0);
            }
            
            // 监听磨豆机选择变化
            grindingEquipmentSelect.addEventListener('change', async function() {
                const grinderId = this.value;
                if (grinderId) {
                    fetchAndSetGrindSize(grinderId, grindSizeInput);
                } else {
                    // 修改这里，不要直接设置空值
                    if (!grindSizeInput._preventEmptySet) {
                        grindSizeInput.value = '';
                    }
                }
            });
        }
    }

    // 获取并设置研磨度
    async function fetchAndSetGrindSize(grinderId, grindSizeInput) {
        try {
            const response = await fetch(`/my/brewlog/equipment/${grinderId}/info/`);
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            const data = await response.json();
            if (data.grind_size_preset) {
                grindSizeInput.value = data.grind_size_preset;
            } else {
                grindSizeInput.value = '';
            }
        } catch (error) {
            console.error('Error fetching grinder preset:', error);
            grindSizeInput.value = '';
        }
    }

    // Initialize Brewing Time and Rating Inputs
    initBrewingTimeInputs();
    initRatingInput();
    initGrinderSelection();
});

// brewlog record, edit/add, steps
document.addEventListener('alpine:init', () => {
    Alpine.data('brewingSteps', () => ({
        stepsEnabled: false,
        steps: [],
        sections: {
            brewing: true,
            equipment: true,
            measurement: true,
            steps: true,
            environment: false
        },
        
        init() {
            // 从数据属性中获取初始值
            this.stepsEnabled = this.$el.dataset.stepsEnabled === 'true';
            try {
                const stepsData = this.$el.dataset.steps;
                if (stepsData && stepsData !== '[]') {
                    const steps = JSON.parse(stepsData);
                    if (Array.isArray(steps) && steps.length > 0) {
                        this.steps = steps.map(step => ({
                            text: step.text || '',
                            order: step.order || 1,
                            hasTimer: step.hasTimer || false,
                            minutes: parseInt(step.minutes) || 0,
                            seconds: parseInt(step.seconds) || 0
                        }));
                        this.stepsEnabled = true;
                        // 如果有步骤数据，自动展开步骤区域
                        this.sections.steps = true;
                    }
                }
            } catch (e) {
                console.error('Error parsing steps:', e, this.$el.dataset.steps);
                this.steps = [];
            }
            
            // 添加对 stepsEnabled 的监听
            this.$watch('stepsEnabled', (value) => {
                if (value) {
                    // 如果启用步骤且没有步骤，添加一个空步骤
                    if (this.steps.length === 0) {
                        this.addStep();
                    }
                    // 自动展开步骤区域
                    this.sections.steps = true;
                }
            });

            // 修改表单提交处理
            const form = this.$el.querySelector('form');
            if (form) {
                form.addEventListener('submit', (e) => {
                    // 阻止表单默认提交
                    e.preventDefault();
                    
                    // 处理步骤中的计时数据
                    this.steps.forEach((step, index) => {
                        // 获取实际的表单输入元素
                        const minutesInput = form.querySelector(`input[name="steps[${index}][minutes]"]`);
                        const secondsInput = form.querySelector(`input[name="steps[${index}][seconds]"]`);
                        const hasTimerInput = form.querySelector(`input[name="steps[${index}][has_timer]"]`);
                        
                        if (hasTimerInput && hasTimerInput.checked) {
                            const minutes = minutesInput ? minutesInput.value.trim() : '';
                            const seconds = secondsInput ? secondsInput.value.trim() : '';
                            
                            // 如果分钟和秒都为空，或者都为0，取消计时选项
                            if ((minutes === '' && seconds === '') || 
                                ((!minutes || parseInt(minutes) === 0) && (!seconds || parseInt(seconds) === 0))) {
                                hasTimerInput.checked = false;
                                if (minutesInput) minutesInput.value = '0';
                                if (secondsInput) secondsInput.value = '0';
                            } else {
                                // 如果其中一个为空，设为0
                                if (minutes === '') minutesInput.value = '0';
                                if (seconds === '') secondsInput.value = '0';
                            }
                            
                            // 更新 Alpine 数据
                            step.hasTimer = hasTimerInput.checked;
                            step.minutes = parseInt(minutesInput.value) || 0;
                            step.seconds = parseInt(secondsInput.value) || 0;
                        }
                    });
                    
                    // 手动提交表单
                    form.submit();
                });
            }
        },
        
        addStep() {
            this.steps.push({
                text: '',
                hasTimer: false,
                minutes: 0,
                seconds: 0,
                order: this.steps.length + 1
            });
            // 添加步骤时确保步骤区域展开
            this.sections.steps = true;
        },
        
        removeStep(index) {
            this.steps.splice(index, 1);
            this.steps.forEach((step, i) => step.order = i + 1);
            
            // 如果步骤列表为空，自动关闭记录详细步骤
            if (this.steps.length === 0) {
                this.stepsEnabled = false;
            }
        },
        
        moveStep(index, direction) {
            const newIndex = index + direction;
            if (newIndex >= 0 && newIndex < this.steps.length) {
                const temp = this.steps[index];
                this.steps[index] = this.steps[newIndex];
                this.steps[newIndex] = temp;
                this.steps.forEach((step, i) => step.order = i + 1);
            }
        },

        toggleSteps() {
            this.sections.steps = !this.sections.steps;
        }
    }));
});

// 处理小工具组合选择
function initGadgetKitSelection() {
    const gadgetKitSelect = document.querySelector('select[name="gadget_kit"]');
    const gadgetCheckboxes = document.querySelectorAll('input[name="gadgets"]');
    
    if (gadgetKitSelect && gadgetCheckboxes.length > 0) {
        // 处理小工具组合选择变化
        gadgetKitSelect.addEventListener('change', async function() {
            const kitId = this.value;
            
            // 先清除所有小工具的选中状态
            gadgetCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            
            if (kitId) {
                try {
                    // 获取组合中的小工具
                    const response = await fetch(`/my/brewlog/equipment/${kitId}/gadgets/`);
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    const gadgets = await response.json();
                    
                    // 更新小工具选择状态
                    gadgetCheckboxes.forEach(checkbox => {
                        checkbox.checked = gadgets.includes(parseInt(checkbox.value));
                    });
                } catch (error) {
                    console.error('Error fetching gadget kit components:', error);
                }
            }
        });

        // 处理小工具复选框变化
        gadgetCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                // 当手动改变小工具选择时，清除小工具组合的选择
                gadgetKitSelect.value = '';
            });
        });
    }
}

// 风味标签管理
class TagManager {
    constructor() {
        this.input = document.querySelector('input[data-tags-url]');
        this.hiddenInput = document.querySelector('#flavor-tags-data');
        this.dropdown = document.querySelector('#tags-dropdown');
        this.selectedTagsContainer = document.querySelector('#selected-tags');
        this.selectedTags = new Map(); // 使用 Map 存储 id-name 对
        this.allTags = [];
        
        if (this.input) {
            this.init();
        }
    }

    async init() {
        try {
            // 等待加载所有可用标签
            await this.loadExistingTags();
            
            // 从预渲染的标签中获取初始数据
            const existingTags = this.selectedTagsContainer.querySelectorAll('.bg-primary\\/10');
            existingTags.forEach(tag => {
                const tagName = tag.querySelector('span').textContent.trim();
                const deleteButton = tag.querySelector('button');
                const tagId = deleteButton.dataset.tagId;
                if (tagId && tagName) {
                    this.selectedTags.set(tagId, tagName);
                    // 为删除按钮添加事件监听器
                    deleteButton.addEventListener('click', () => this.removeTag(tagId));
                }
            });

            // 从隐藏输入字段中获取初始标签数据
            const hiddenValue = this.hiddenInput.value;
            if (hiddenValue) {
                const tagIds = hiddenValue.split(',');
                tagIds.forEach(id => {
                    const tag = this.allTags.find(t => t.id.toString() === id);
                    if (tag && !this.selectedTags.has(id)) {
                        this.selectedTags.set(id, tag.name);
                    }
                });
            }
            
            // 更新隐藏输入字段的值
            this.updateHiddenInput();
            
            // 设置事件监听器
            this.input.addEventListener('keydown', this.handleKeydown.bind(this));
            this.input.addEventListener('input', this.handleInput.bind(this));
            this.input.addEventListener('blur', () => {
                // 使用setTimeout以允许点击下拉菜单项
                setTimeout(() => this.hideDropdown(), 200);
            });
        } catch (error) {
            console.error('Error in TagManager initialization:', error);
        }
    }

    async loadExistingTags() {
        try {
            const response = await fetch(this.input.dataset.tagsUrl);
            if (!response.ok) throw new Error('Network response was not ok');
            const data = await response.json();
            this.allTags = data;
        } catch (error) {
            console.error('Error loading existing tags:', error);
            this.allTags = [];
        }
    }

    handleKeydown(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            const inputValue = this.input.value.trim();
            if (inputValue) {
                // 查找匹配的标签
                const matchingTag = this.allTags.find(tag => 
                    tag.name.toLowerCase() === inputValue.toLowerCase()
                );
                if (matchingTag) {
                    this.addTag(matchingTag.id, matchingTag.name);
                } else {
                    // 如果标签不存在，创建新标签
                    this.createNewTag(inputValue);
                }
                this.input.value = '';
                this.hideDropdown();
            }
        }
    }

    async createNewTag(tagName) {
        try {
            const response = await fetch('/my/brewlog/flavor-tags/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({ name: tagName })
            });

            if (!response.ok) throw new Error('Failed to create tag');
            
            const newTag = await response.json();
            // 添加到可用标签列表
            this.allTags.push(newTag);
            // 添加到已选标签
            this.addTag(newTag.id, newTag.name);
        } catch (error) {
            console.error('Error creating new tag:', error);
        }
    }

    handleInput() {
        const searchText = this.input.value.trim().toLowerCase();
        if (searchText) {
            const matchingTags = this.allTags.filter(tag =>
                tag.name.toLowerCase().includes(searchText) &&
                !this.selectedTags.has(tag.id.toString())
            );
            this.showDropdown(matchingTags);
        } else {
            this.hideDropdown();
        }
    }

    addTag(id, name) {
        if (!this.selectedTags.has(id.toString())) {
            this.selectedTags.set(id.toString(), name);
            
            const tagElement = document.createElement('div');
            tagElement.className = 'bg-primary/10 text-primary px-2 py-1 rounded-lg flex items-center gap-2';
            tagElement.innerHTML = `
                <span>${name}</span>
                <button type="button" class="hover:text-error" data-tag-id="${id}">×</button>
            `;
            
            tagElement.querySelector('button').addEventListener('click', () => this.removeTag(id));
            this.selectedTagsContainer.appendChild(tagElement);
            this.updateHiddenInput();
        }
    }

    removeTag(id) {
        this.selectedTags.delete(id.toString());
        const tagElement = Array.from(this.selectedTagsContainer.children).find(el => 
            el.querySelector(`button[data-tag-id="${id}"]`) || 
            el.querySelector(`button`).dataset.tagId === id.toString()
        );
        
        if (tagElement) {
            tagElement.remove();
        }
        this.updateHiddenInput();
    }

    showDropdown(matchingTags) {
        this.dropdown.innerHTML = matchingTags.map(tag => `
            <li>
                <button type="button" class="w-full text-left" data-tag-id="${tag.id}" data-tag-name="${tag.name}">
                    ${tag.name}
                </button>
            </li>
        `).join('');

        this.dropdown.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', () => {
                const id = button.dataset.tagId;
                const name = button.dataset.tagName;
                this.addTag(id, name);
                this.input.value = '';
                this.hideDropdown();
            });
        });

        this.dropdown.classList.remove('hidden');
    }

    hideDropdown() {
        this.dropdown.classList.add('hidden');
    }

    updateHiddenInput() {
        // 将选中的标签ID以逗号分隔的形式存储
        const tagIds = Array.from(this.selectedTags.keys())
            .filter(id => id && id.toString().trim())  // 确保ID有效
            .map(id => id.toString().trim());  // 转换为字符串并去除空格
        
        if (this.hiddenInput) {
            this.hiddenInput.value = tagIds.join(',');
        } else {
            console.error('Hidden input element not found');
        }
    }
}

// 在文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // ... existing code ...
    
    // 初始化小工具组合选择
    initGadgetKitSelection();
    
    // 初始化标签管理器
    new TagManager();
});