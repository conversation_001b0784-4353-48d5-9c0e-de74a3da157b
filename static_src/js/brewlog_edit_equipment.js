document.addEventListener('DOMContentLoaded', function() {
    function checkTypeSelection() {
        const typeSelect = document.querySelector('select[name="type"]');
        const typeInput = document.querySelector('input[name="type"]');  // 为编辑页面添加
        const brewMethodGroup = document.querySelector('.brew-method-group');
        const brewMethodSelect = document.querySelector('select[name="brew_method"]');
        const grinderPurposeGroup = document.querySelector('.grinder-purpose-group');
        const grinderPurposeSelect = document.querySelector('select[name="grinder_purpose"]');
        const gadgetKitGroup = document.querySelector('.gadget-kit-group');
        const grindSizePresetGroup = document.querySelector('.grind-size-preset-group');
        
        // 获取需要根据类型隐藏的字段组
        const brandGroup = document.querySelector('input[name="brand"]').closest('.form-group');
        const createdAtGroup = document.querySelector('input[name="created_at"]').closest('.form-control');
        const priceGroup = document.querySelector('input[name="purchase_price"]').closest('.form-group');
        
        // 获取设备类型值（支持select和input）
        const selectedType = typeSelect ? typeSelect.value : (typeInput ? typeInput.value : null);
        
        // 处理冲煮设备的赛道选项
        if (selectedType === 'BREWER') {
            brewMethodGroup.style.display = 'block';
            grinderPurposeGroup.style.display = 'none';
            grindSizePresetGroup.style.display = 'none';
            gadgetKitGroup.style.display = 'none';
            brandGroup.style.display = 'block';
            createdAtGroup.style.display = 'block';
            priceGroup.style.display = 'block';
            if (grinderPurposeSelect) {
                grinderPurposeSelect.value = '';
            }
        } 
        // 处理磨豆机的用途选项
        else if (selectedType === 'GRINDER') {
            brewMethodGroup.style.display = 'none';
            grinderPurposeGroup.style.display = 'block';
            grindSizePresetGroup.style.display = 'block';
            gadgetKitGroup.style.display = 'none';
            brandGroup.style.display = 'block';
            createdAtGroup.style.display = 'block';
            priceGroup.style.display = 'block';
            if (brewMethodSelect) {
                brewMethodSelect.value = '';
            }
        }
        // 处理小工具组合
        else if (selectedType === 'GADGET_KIT') {
            brewMethodGroup.style.display = 'none';
            grinderPurposeGroup.style.display = 'none';
            grindSizePresetGroup.style.display = 'none';
            gadgetKitGroup.style.display = 'block';
            brandGroup.style.display = 'none';
            createdAtGroup.style.display = 'none';
            priceGroup.style.display = 'none';
            if (brewMethodSelect) {
                brewMethodSelect.value = '';
            }
            if (grinderPurposeSelect) {
                grinderPurposeSelect.value = '';
            }
        }
        // 其他类型设备
        else {
            brewMethodGroup.style.display = 'none';
            grinderPurposeGroup.style.display = 'none';
            grindSizePresetGroup.style.display = 'none';
            gadgetKitGroup.style.display = 'none';
            brandGroup.style.display = 'block';
            createdAtGroup.style.display = 'block';
            priceGroup.style.display = 'block';
            if (brewMethodSelect) {
                brewMethodSelect.value = '';
            }
            if (grinderPurposeSelect) {
                grinderPurposeSelect.value = '';
            }
        }
    }

    // 获取设备类型元素（支持select和input）
    const typeSelect = document.querySelector('select[name="type"]');
    const typeInput = document.querySelector('input[name="type"]');
    
    if (typeSelect || typeInput) {
        // 页面加载时初始化
        checkTypeSelection();
        
        // 如果是select元素，添加change事件监听器
        if (typeSelect) {
            typeSelect.addEventListener('change', checkTypeSelection);
        }
    }
});