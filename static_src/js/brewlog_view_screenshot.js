import { domToPng } from 'modern-screenshot'

document.addEventListener('DOMContentLoaded', () => {
    const shareBtn = document.getElementById('shareBtn')
    if (!shareBtn) return

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)

    const getFilenameFromTitle = () => {
        const title = document.title.replace(' - 咖啡搭子', '')  // 移除网站名称
        // 替换不合法的文件名字符
        return title.replace(/[<>:"/\\|?*]/g, '_')
    }

    const generateScreenshot = async (content, retries = 3) => {
        for (let i = 0; i < retries; i++) {
            try {
                const dataUrl = await domToPng(content, {
                    type: 'image/png',
                    scale: 2,  // 可以考虑降低scale值到1.5或1来减少内存占用
                    quality: 1,  // 可以稍微降低质量以减少内存占用
                    backgroundColor: '#ffffff',
                    features: {
                        copyScrollbar: true,
                        removeAbnormalAttributes: true,
                        removeControlCharacter: true,
                        fixSvgXmlDecode: true,
                        restoreScrollPosition: true
                    }
                })
                
                // 立即释放不需要的内存
                URL.revokeObjectURL(dataUrl)
                return dataUrl
                
            } catch (error) {
                console.error('截图重试失败:', error)
                if (i === retries - 1) throw error
                await new Promise(resolve => setTimeout(resolve, 500))
            }
        }
    }

    shareBtn.addEventListener('click', async () => {
        if (shareBtn.dataset.processing === 'true') return
        
        // 检查页面是否准备好
        const content = document.querySelector('.mx-auto.xl\\:max-w-3xl')
        if (!content) {
            const toast = document.createElement('div')
            toast.className = 'toast toast-top toast-center z-[9999]'
            toast.innerHTML = `
                <div class="alert alert-warning">
                    <span>页面内容未准备好，请刷新后重试</span>
                </div>
            `
            document.body.appendChild(toast)
            setTimeout(() => toast.remove(), 3000)
            return
        }
        
        // 检查内存使用情况
        if (performance && performance.memory) {
            const memoryUsage = performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit
            if (memoryUsage > 0.8) {  // 如果内存使用超过80%
                const toast = document.createElement('div')
                toast.className = 'toast toast-top toast-center z-[9999]'
                toast.innerHTML = `
                    <div class="alert alert-warning">
                        <span>系统资源不足，请关闭其他标签页后重试</span>
                    </div>
                `
                document.body.appendChild(toast)
                setTimeout(() => toast.remove(), 3000)
                return
            }
        }
        
        shareBtn.dataset.processing = 'true'
        
        const originalBtnContent = shareBtn.innerHTML
        shareBtn.innerHTML = `<span class="loading loading-spinner text-lg"></span><span class="hidden md:block">生成中</span>`
        
        // 根据页面类型选择不同的截图区域
        const isRecordPage = document.title.includes('我喝了什么咖啡')
        if (!content) return

        // 保存需要临时隐藏的元素
        const elementsToHide = []
        
        const originalTheme = document.documentElement.getAttribute('data-theme')
        const computedStyle = window.getComputedStyle(content)
        const originalStyles = {
            width: content.style.width || computedStyle.width,
            maxWidth: content.style.maxWidth || computedStyle.maxWidth,
            backgroundColor: content.style.backgroundColor || computedStyle.backgroundColor,
            padding: content.style.padding || computedStyle.padding,
            margin: content.style.margin || computedStyle.margin
        }

        try {
            // 处理需要隐藏的元素
            if (isRecordPage) {
                const stepGuideArea = content.querySelector('[x-data="stepGuide"]')?.parentElement
                if (stepGuideArea) {
                    elementsToHide.push({
                        element: stepGuideArea,
                        parent: stepGuideArea.parentElement,
                        nextSibling: stepGuideArea.nextSibling
                    })
                    stepGuideArea.parentElement.removeChild(stepGuideArea)
                }
            } else {
                // 在咖啡豆页面需要隐藏的元素
                const dividers = content.querySelectorAll('.divider')
                dividers.forEach(divider => {
                    if (divider.textContent.includes('附加信息') || divider.textContent.includes('提要')) {
                        elementsToHide.push({
                            element: divider,
                            parent: divider.parentElement,
                            nextSibling: divider.nextSibling
                        })
                        divider.parentElement.removeChild(divider)
                    }
                })

                // 隐藏其他元素
                const otherSelectors = [
                    'dl', // 附加信息内容
                    '#repurchase-history', // 回购历史
                    '.alert', // 备注
                    '.stats', // 提要内容
                ]

                otherSelectors.forEach(selector => {
                    const elements = content.querySelectorAll(selector)
                    elements.forEach(element => {
                        elementsToHide.push({
                            element,
                            parent: element.parentElement,
                            nextSibling: element.nextSibling
                        })
                        element.parentElement.removeChild(element)
                    })
                })
            }

            document.documentElement.setAttribute('data-theme', 'cupcake')
            Object.assign(content.style, {
                width: isMobile ? '100%' : '430px',
                maxWidth: '430px',
                backgroundColor: '#ffffff',
                padding: isMobile ? '15px' : '20px',
                margin: '0 auto'
            })

            // 移动设备特别处理
            if (isMobile) {
                Object.assign(content.style, {
                    width: '100%',
                    maxWidth: '100%',  // 移动设备使用全宽
                    padding: '10px',   // 减小内边距
                    backgroundColor: '#ffffff'
                })
                
                // 移动设备降低图片质量
                const options = {
                    scale: 2,
                    quality: 1,
                    features: {
                        copyScrollbar: false,  // 移动设备不需要
                        removeAbnormalAttributes: true,
                        removeControlCharacter: true,
                        fixSvgXmlDecode: true,
                        restoreScrollPosition: false  // 移动设备不需要
                    }
                }
            }

            await new Promise(resolve => setTimeout(resolve, 200))
            const dataUrl = await generateScreenshot(content)
            
            // 将dataURL转换为Blob
            const response = await fetch(dataUrl)
            const blob = await response.blob()
            const blobUrl = URL.createObjectURL(blob)

            const link = document.createElement('a')
            link.download = `${getFilenameFromTitle()}.png`
            link.href = blobUrl
            
            link.addEventListener('click', () => {
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl)
                    link.remove()
                }, 1000)
            })
            
            link.click()

            shareBtn.innerHTML = `<span class="icon-[material-symbols--check-circle-outline] text-lg"></span><span class="hidden md:block">已导出</span>`

        } catch (error) {
            console.error('截图失败:', error)
            
            // 确保 toast 显示在最上层
            const toast = document.createElement('div')
            toast.className = 'toast toast-top toast-center z-[9999]'
            toast.innerHTML = `
                <div class="alert alert-warning">
                    <span>${error.message || '截图失败，请稍候重试'}</span>
                </div>
            `
            document.body.appendChild(toast)
            
            // 确保 toast 显示时间足够
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    toast.remove()
                }
            }, 3000)
            
            shareBtn.innerHTML = `<span class="icon-[material-symbols--error-outline] text-lg"></span><span class="hidden md:block">失败</span>`

        } finally {
            setTimeout(() => {
                shareBtn.innerHTML = originalBtnContent
            }, 2000)
            
            document.documentElement.setAttribute('data-theme', originalTheme)
            Object.keys(originalStyles).forEach(key => {
                if (originalStyles[key] === 'auto' || !originalStyles[key]) {
                    content.style[key] = ''
                } else {
                    content.style[key] = originalStyles[key]
                }
            })
            
            // 恢复被隐藏的元素
            elementsToHide.forEach(({element, parent, nextSibling}) => {
                if (nextSibling) {
                    parent.insertBefore(element, nextSibling)
                } else {
                    parent.appendChild(element)
                }
            })
            
            // 确保清理资源
            if (window.gc) window.gc()  // 建议垃圾回收
            shareBtn.dataset.processing = 'false'
        }
    })
}) 