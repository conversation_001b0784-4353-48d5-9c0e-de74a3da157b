// 设置 Chart.js 全局默认配置
// Chart.defaults.color = getComputedStyle(document.documentElement).getPropertyValue('--base-content');
// Chart.defaults.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--b3');

import { Chart } from 'chart.js/auto';

document.addEventListener('alpine:init', () => {
    Alpine.data('stepGuide', () => ({
        showGuide: false,
        currentStep: 0,
        stepsCount: 0,
        timerRefs: [],
        isCompleted: false,

        init() {
            // 获取模态框内的步骤总数
            this.stepsCount = document.querySelectorAll('[x-ref="stepsList"] li').length;
            // 初始化计时器引用数组
            this.timerRefs = new Array(this.stepsCount).fill(null);
        },
        
        open() {
            this.showGuide = true;
            this.currentStep = 0;
            this.isCompleted = false;
            document.body.style.overflow = 'hidden';
            // 打开时自动开始第一步的计时器（如果有）
            this.$nextTick(() => {
                this.startCurrentTimer();
                this.scrollToCurrentStep();
            });
        },
        
        close() {
            this.showGuide = false;
            this.isCompleted = false;
            document.body.style.overflow = '';
            // 关闭时重置所有计时器
            this.timerRefs.forEach(ref => {
                if (ref) {
                    ref.resetTimer();
                }
            });
            this.timerRefs = new Array(this.stepsCount).fill(null);
        },
        
        next() {
            if (this.currentStep < this.stepsCount - 1) {
                // 停止当前计时器
                if (this.timerRefs[this.currentStep]) {
                    this.timerRefs[this.currentStep].pauseTimer();
                }
                this.currentStep++;
                // 重置完成状态
                this.isCompleted = false;
                // 自动开始下一步的计时器并滚动到当前步骤
                this.$nextTick(() => {
                    this.startCurrentTimer();
                    this.scrollToCurrentStep();
                });
            }
        },
        
        prev() {
            if (this.currentStep > 0) {
                // 停止当前计时器
                if (this.timerRefs[this.currentStep]) {
                    this.timerRefs[this.currentStep].pauseTimer();
                }
                this.currentStep--;
                // 重置完成状态
                this.isCompleted = false;
                // 自动开始上一步的计时器并滚动到当前步骤
                this.$nextTick(() => {
                    this.startCurrentTimer();
                    this.scrollToCurrentStep();
                });
            }
        },
        
        goToStep(step) {
            if (step !== this.currentStep) {
                // 停止当前计时器
                if (this.timerRefs[this.currentStep]) {
                    this.timerRefs[this.currentStep].pauseTimer();
                }
                this.currentStep = step;
                // 重置完成状态
                this.isCompleted = false;
                // 自动开始目标步骤的计时器并滚动到当前步骤
                this.$nextTick(() => {
                    this.startCurrentTimer();
                    this.scrollToCurrentStep();
                });
            }
        },

        startCurrentTimer() {
            // 获取当前步骤的所有计时器组件（包括模态框卡片和原始步骤列表中的）
            const timerElements = document.querySelectorAll(`[data-step="${this.currentStep}"]`);
            timerElements.forEach(timerEl => {
                const timerComponent = Alpine.$data(timerEl);
                if (!timerComponent.isCompleted) {
                    this.timerRefs[this.currentStep] = timerComponent;
                    timerComponent.startTimer(true); // 传入 true 表示自动启动
                }
            });
        },

        scrollToCurrentStep() {
            const stepEl = document.getElementById(`step-${this.currentStep}`);
            if (stepEl) {
                stepEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        },

        complete() {
            this.isCompleted = true;
            
            // 停止所有计时器
            this.timerRefs.forEach(ref => {
                if (ref && ref.isRunning) {
                    ref.pauseTimer();
                }
            });

            // 创建完成提示元素
            const toast = document.createElement('div');
            toast.className = 'toast toast-center z-[9999]';
            toast.innerHTML = `
                <div class="alert alert-success">
                    <span class="icon-[material-symbols--check-circle-outline]"></span>
                    <span>恭喜！所有步骤已完成</span>
                </div>
            `;

            // 将提示添加到模态框内部
            const modalContent = this.$el.querySelector('.flex-1.flex.flex-col');
            if (modalContent) {
                modalContent.appendChild(toast);
                setTimeout(() => toast.remove(), 3000);
            }
        },

        isFirst() {
            return this.currentStep === 0;
        },

        isLast() {
            return this.currentStep === this.stepsCount - 1;
        }
    }));

    Alpine.data('stepTimer', (timerStr) => ({
        time: 0,
        initialTime: 0,
        isRunning: false,
        timer: null,
        isCompleted: false,
        
        init() {
            const [minutes, seconds] = timerStr.split(':').map(Number);
            this.time = minutes * 60 + seconds;
            this.initialTime = this.time;
        },
        
        startTimer(autoStart = false) {
            if (!this.isRunning && !this.isCompleted && this.time > 0) {
                // 如果不是自动启动，或者是自动启动且时间小于等于60秒，则启动计时器
                if (!autoStart || (autoStart && this.time <= 60)) {
                    this.isRunning = true;
                    this.timer = setInterval(() => {
                        if (this.time > 0) {
                            this.time--;
                            if (this.time === 0) {
                                this.completeTimer();
                            }
                        }
                    }, 1000);
                }
            }
        },
        
        pauseTimer() {
            if (this.isRunning) {
                this.isRunning = false;
                clearInterval(this.timer);
                this.timer = null;
            }
        },
        
        resetTimer() {
            this.pauseTimer();
            this.time = this.initialTime;
            this.isCompleted = false;
        },

        completeTimer() {
            this.pauseTimer();
            this.isCompleted = true;

            // 获取父级 stepGuide 组件
            const stepGuideEl = this.$el.closest('[x-data="stepGuide"]');
            if (stepGuideEl) {
                const stepGuide = Alpine.$data(stepGuideEl);
                if (stepGuide.isLast()) {
                    // 如果是最后一步，重置计时器并显示完成提示
                    this.resetTimer();
                    stepGuide.complete();
                } else {
                    // 如果不是最后一步，自动进入下一步
                    stepGuide.next();
                }
            }
        },
        
        formatTime() {
            const minutes = Math.floor(this.time / 60);
            const seconds = this.time % 60;
            return { minutes, seconds };
        }
    }));
});

// 在页面完全加载后初始化图表
window.addEventListener('load', () => {
    initTrendChart();
    initDimensionChart();
});

// 初始化维度图
function initDimensionChart() {
    const canvas = document.getElementById('dimensionChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const chart = new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['香气', '酸质', '甜度', '余韵', '醇厚'],
            datasets: [{
                data: JSON.parse(canvas.dataset.values || '[]'),
                fill: true,
                backgroundColor: 'rgba(15, 142, 243, 0.2)',
                borderColor: 'rgb(33, 53, 48)',
                pointBackgroundColor: 'rgb(192, 166, 126)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgb(192, 166, 126)',
            }]
        },
        options: {
            elements: {
                line: {
                    borderWidth: 2
                }
            },
            scales: {
                r: {
                    angleLines: {
                        display: false
                    },
                    grid: {
                        color: 'rgba(156, 39, 176, 0.1)',
                        circular: true,
                        lineWidth: 1
                    },
                    min: 0,
                    max: 5,
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                        backdropColor: 'transparent',
                        display: true,
                        showLabelBackdrop: false,
                        count: 6,
                        maxTicksLimit: 6,
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            if (value === 0) return '';
                            return value;
                        },
                        padding: -10,
                        align: 'start',
                        z: 1
                    },
                    pointLabels: {
                        color: 'currentColor',
                        font: {
                            size: 14
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // 监听 Alpine store 的主题变化
    Alpine.effect(() => {
        const isDark = Alpine.store('theme').isDark;
        requestAnimationFrame(() => {
            updateChartColors(chart);
        });
    });
}

// 获取主题颜色的函数
function getThemeColors() {
    const isDark = document.documentElement.getAttribute('data-theme') === 'dracula';
    return {
        text: isDark ? '#ffffff' : '#1f2937',  // 固定深浅色文本颜色
        border: getComputedStyle(document.documentElement).getPropertyValue('--b3'),
        tooltip: {
            bg: isDark ? '#374151' : '#ffffff',  // 深色模式下使用较亮的灰色
            text: isDark ? '#ffffff' : '#1f2937',
            borderColor: isDark ? '#4b5563' : '#e5e7eb'
        }
    };
}

// 更新图表颜色的函数
function updateChartColors(chart) {
    const colors = getThemeColors();
    const isDark = document.documentElement.getAttribute('data-theme') === 'dracula';
    
    // 更新全局颜色
    chart.options.color = colors.text;
    
    // 更新图例颜色
    chart.options.plugins.legend.labels.color = colors.text;
    
    // 更新提示框颜色
    chart.options.plugins.tooltip.backgroundColor = colors.tooltip.bg;
    chart.options.plugins.tooltip.titleColor = colors.tooltip.text;
    chart.options.plugins.tooltip.bodyColor = colors.tooltip.text;
    chart.options.plugins.tooltip.borderColor = colors.tooltip.borderColor;
    
    // 更新所有坐标轴颜色
    Object.values(chart.options.scales).forEach(scale => {
        if (scale.title) {
            scale.title.color = colors.text;
        }
        if (scale.ticks) {
            scale.ticks.color = colors.text;
        }
        if (scale.grid) {
            scale.grid.color = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
        }
        // 更新雷达图的 pointLabels 颜色
        if (scale.pointLabels) {
            scale.pointLabels.color = colors.text;
        }
    });
    
    // 强制更新图表
    chart.update('none');  // 使用 'none' 模式避免动画
}

// 趋势图表相关代码
function initTrendChart() {
    const historyDataEl = document.getElementById('history-data');
    if (!historyDataEl) {
        return;
    }
    
    try {
        const historyData = JSON.parse(historyDataEl.textContent);
        if (!Array.isArray(historyData) || historyData.length === 0) {
            return;
        }
        const canvas = document.getElementById('trendChart');
        const ctx = canvas.getContext('2d');
        if (!ctx) return;
        
        // 准备数据
        const dates = historyData.map(r => r.date);
        const ratings = historyData.map(r => r.rating);
        const grinds = historyData.map(r => {
            if (!r.grind_size) return null;
            const numStr = r.grind_size.toString().replace(/[^0-9.,]/g, '').replace(',', '.');
            return numStr ? parseFloat(numStr) : null;
        });
        const temps = historyData.map(r => {
            if (!r.water_temp) return null;
            return r.water_temp / 10;
        });
        const yields = historyData.map(r => {
            if (!r.yield) return null;
            // 根据数值大小决定除数，确保第一位数字对齐
            const value = r.yield;
            if (value >= 100) {
                return value / 100;  // 例如：225 -> 2.25
            } else if (value >= 10) {
                return value / 10;   // 例如：36 -> 3.6
            }
            return value;            // 例如：8 -> 8.0
        });
        const doses = historyData.map(r => r.dose);
        const times = historyData.map(r => {
            if (!r.time) return null;
            // 处理时分秒格式 "HH:MM:SS" 或 "MM:SS"
            const parts = r.time.split(':').map(Number);
            if (parts.length === 3) {
                // 时分秒格式
                return parts[0] * 3600 + parts[1] * 60 + parts[2];
            } else if (parts.length === 2) {
                // 分秒格式
                return parts[0] * 60 + parts[1];
            }
            return null;
        });
        
        // 创建图表
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: '评分',
                        data: ratings,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgb(255, 99, 132)',  // 实心点
                        pointStyle: 'circle',  // 圆形点
                        pointRadius: 4,  // 点的大小
                        pointHoverRadius: 6,  // 悬停时点的大小
                        yAxisID: 'y',
                        tension: 0.1,
                        borderWidth: 2
                    },
                    {
                        label: '研磨度',
                        data: grinds,
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.1,
                        borderWidth: 2
                    },
                    {
                        label: '水温',
                        data: temps,
                        borderColor: 'rgb(255, 159, 64)',
                        backgroundColor: 'rgba(255, 159, 64, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.1,
                        borderWidth: 2,
                        originalData: historyData.map(r => r.water_temp)
                    },
                    {
                        label: '粉重',
                        data: doses,
                        borderColor: 'rgb(75, 192, 192)',  // 青色
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.1,
                        borderWidth: 2
                    },
                    {
                        label: '液重',
                        data: yields,
                        borderColor: 'rgb(153, 102, 255)',
                        backgroundColor: 'transparent',  // 空心点
                        pointStyle: 'circle',  // 圆形点
                        pointRadius: 4,  // 点的大小
                        pointHoverRadius: 6,  // 悬停时点的大小
                        pointBorderWidth: 2,  // 点的边框宽度
                        yAxisID: 'y1',
                        tension: 0.1,
                        borderWidth: 2,
                        originalData: historyData.map(r => r.yield)
                    },
                    {
                        label: '萃取时间',
                        data: times,
                        borderColor: 'rgb(255, 205, 86)',  // 改用黄色
                        backgroundColor: 'rgba(255, 205, 86, 0.1)',
                        yAxisID: 'y2',
                        tension: 0.1,
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                    events: ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove']
                },
                stacked: false,
                plugins: {
                    legend: {
                        position: window.innerWidth < 768 ? 'bottom' : 'top',
                        labels: {
                            boxWidth: window.innerWidth < 768 ? 12 : 40,
                            padding: window.innerWidth < 768 ? 10 : 20,
                        }
                    },
                    tooltip: {
                        enabled: true,
                        borderWidth: 1,
                        boxPadding: 8,
                        caretSize: 6,
                        caretPadding: 10,
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        padding: 12,
                        titleFont: {
                            weight: 'bold'
                        },
                        bodySpacing: 6,
                        displayColors: true,
                        multiKeyBackground: 'transparent',
                        callbacks: {
                            label: function(context) {
                                const dataset = context.dataset;
                                const dataIndex = context.dataIndex;
                                const value = context.raw;
                                
                                if (dataset.originalData) {
                                    const originalValue = dataset.originalData[dataIndex];
                                    return `${dataset.label}: ${originalValue}`;
                                }
                                
                                return `${dataset.label}: ${value}`;
                            },
                            afterBody: function(context) {
                                const idx = context[0].dataIndex;
                                const record = historyData[idx];
                                const lines = [];
                                context[0].label = `${record.date}`;
                                if (record.dose && record.yield) {
                                    lines.push(`粉水比: 1:${(record.yield/record.dose).toFixed(1)}`);
                                }
                                if (record.notes) lines.push(`备注: ${record.notes}`);
                                return lines;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '评分',
                        },
                        min: 0,
                        max: 10,
                        grid: {
                        },
                        ticks: {
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '参数值',
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            callback: function(value) {
                                // 只显示整数部分
                                return Math.floor(value);
                            }
                        }
                    },
                    y2: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '萃取时间',
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            callback: function(value) {
                                // 格式化时间显示
                                if (value >= 3600) {
                                    // 超过1小时，显示时分秒
                                    const hours = Math.floor(value / 3600);
                                    const minutes = Math.floor((value % 3600) / 60);
                                    const seconds = value % 60;
                                    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                                } else if (value >= 60) {
                                    // 超过1分钟，显示分秒
                                    const minutes = Math.floor(value / 60);
                                    const seconds = value % 60;
                                    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
                                } else {
                                    // 不足1分钟，显示秒
                                    return `0:${value.toString().padStart(2, '0')}`;
                                }
                            }
                        }
                    },
                    x: {
                        ticks: {
                        },
                        grid: {
                        }
                    }
                }
            }
        });

        // 添加点击事件监听器来隐藏 tooltip
        document.addEventListener('click', (event) => {
            // 检查点击是否在图表区域外
            const rect = canvas.getBoundingClientRect();
            const clickedOutside = 
                event.clientX < rect.left ||
                event.clientX > rect.right ||
                event.clientY < rect.top ||
                event.clientY > rect.bottom;
            
            if (clickedOutside) {
                // 隐藏 tooltip
                chart.tooltip.setActiveElements([], { x: 0, y: 0 });
                chart.update();
            }
        });

        // 初始化颜色
        updateChartColors(chart);

        // 监听 Alpine store 的主题变化
        Alpine.effect(() => {
            const isDark = Alpine.store('theme').isDark;
            requestAnimationFrame(() => {
                updateChartColors(chart);
            });
        });

    } catch (error) {
        console.error('Error initializing trend chart:', error);
    }
} 