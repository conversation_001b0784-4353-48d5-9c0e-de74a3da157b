document.addEventListener('DOMContentLoaded', function() {
    // 获取表单和切换按钮
    const latteculatorForm = document.getElementById('app-Latteculator-form');
    const cupSizeForm = document.getElementById('app-Cupsize-form');
    const sproSizeForm = document.getElementById('app-Sprosize-form');
    
    // 计算器切换逻辑
    function setupTabSwitching() {
        const tabs = document.querySelectorAll('.app-tab');
        const forms = [latteculatorForm, cupSizeForm, sproSizeForm];
        const results = [
            document.querySelector('.latteculator-results'),
            document.querySelector('.cupsize-results'),
            document.querySelector('.sprosize-results')
        ];
        const calcInfos = document.querySelectorAll('#calculator-info [data-calculator]');
        
        // 映射tab id到collapse data-calculator值
        const tabToCollapse = {
            'app-Latteculator-tab': 'latteculator',
            'app-Cupsize-tab': 'cupsize',
            'app-Sprosize-tab': 'sprosize'
        };
        
        // 初始化时隐藏非当前tab的说明
        const activeTab = document.querySelector('.tab-active');
        calcInfos.forEach(info => {
            if (info.dataset.calculator === tabToCollapse[activeTab.id]) {
                info.classList.remove('hidden');
            } else {
                info.classList.add('hidden');
            }
        });
        
        tabs.forEach((tab, index) => {
            tab.addEventListener('click', () => {
                // 更新标签样式
                tabs.forEach(t => t.classList.remove('tab-active'));
                tab.classList.add('tab-active');
                
                // 显示对应表单和结果
                forms.forEach(form => form.classList.add('hidden'));
                results.forEach(result => result.classList.add('hidden'));
                forms[index].classList.remove('hidden');
                results[index].classList.remove('hidden');
                
                // 显示对应的说明
                calcInfos.forEach(info => {
                    if (info.dataset.calculator === tabToCollapse[tab.id]) {
                        info.classList.remove('hidden');
                    } else {
                        info.classList.add('hidden');
                    }
                });
                
                // 切换后重新计算
                setTimeout(checkAndCalculate, 0);
            });
        });
    }

    // Latteculator主计算逻辑
    function calculateLatteculator(e) {
        // 防止表单提交
        e?.preventDefault();
        
        // 获取输入值
        const coffeeWeight = parseFloat(document.getElementById('coffeeweight').value);
        const TDS = parseFloat(document.getElementById('TDS').value) / 100;
        const milkWeight = parseFloat(document.getElementById('milkweight').value);
        const fat = parseFloat(document.getElementById('fat').value) / 100;
        const lactose = parseFloat(document.getElementById('lactose').value) / 100;
        const protein = parseFloat(document.getElementById('protein').value) / 100;
        const condensation = parseFloat(document.getElementById('condensation').value);
        const aeration = parseFloat(document.getElementById('aeration').value);

        // 验证充气量是否大于等于冷凝量
        const latteResults = document.querySelector('.latteculator-results');
        if (condensation > aeration) {
            latteResults.innerHTML = '<div role="alert" class="alert alert-warning"><span class="icon-[ph--warning-bold]"></span><span>错误：充气量不能低于冷凝量</span></div>';
            return;
        }
        
        // 如果验证通过，移除错误状态
        latteResults.classList.remove('opacity-50');
        
        // 恢复结果表格
        if (!document.querySelector('.latteculator-results table')) {
            latteResults.innerHTML = `
                <div class="overflow-x-auto rounded-lg border border-base-300">
                    <table class="table table-zebra w-full text-lg">
                        <tbody>
                            <tr class="hover">
                                <td class="opacity-70 border-b border-base-300">咖啡含量</td>
                                <td class="font-medium border-b border-base-300"><span id="coffeeweightoutput">0.00</span>g (<span id="coffeestrengthoutput">0.00</span>%)</td>
                            </tr>
                            <tr>
                                <td class="opacity-70">脂肪含量</td>
                                <td class="font-medium"><span id="fatweightoutput">0.00</span>g (<span id="fatstrengthoutput">0.00</span>%)</td>
                            </tr>
                            <tr>
                                <td class="opacity-70">乳糖含量</td>
                                <td class="font-medium"><span id="lactoseweightoutput">0.00</span>g (<span id="lactosestrengthoutput">0.00</span>%)</td>
                            </tr>
                            <tr>
                                <td class="opacity-70">蛋白质含量</td>
                                <td class="font-medium"><span id="proteinweightoutput">0.00</span>g (<span id="proteinstrengthoutput">0.00</span>%)</td>
                            </tr>
                            <tr>
                                <td class="opacity-70">总重量</td>
                                <td class="font-medium"><span id="totalweightoutput">0.00</span>g</td>
                            </tr>
                            <tr>
                                <td class="opacity-70">总体积</td>
                                <td class="font-medium"><span id="totalvolumeoutput">0.00</span>ml (<span id="totalvolumeoutput_oz">0.00</span>oz)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>`;
        }

        // 计算咖啡溶解固形物
        const coffeeContent = coffeeWeight * TDS;

        // 计算牛奶成分
        const fatContent = milkWeight * fat + coffeeWeight * 0.0018;  // 加入咖啡中的脂肪含量
        const lactoseContent = milkWeight * lactose;
        const proteinContent = milkWeight * protein + coffeeWeight * 0.0012;  // 加入咖啡中的蛋白质含量

        // 计算总重量和体积
        const totalWeight = coffeeWeight + milkWeight + 13.5;
        // 使用原始体积计算公式
        const totalVolume = coffeeWeight + (milkWeight * (1 + aeration/100));  // 直接使用原始公式
        const totalVolumeOz = totalVolume / 28.41;  // 使用原始的转换系数

        // 计算各成分百分比
        const coffeeStrength = (coffeeContent / totalWeight) * 100;
        const fatStrength = (fatContent / totalWeight) * 100;
        const lactoseStrength = (lactoseContent / totalWeight) * 100;
        const proteinStrength = (proteinContent / totalWeight) * 100;

        // 更新输出结果
        document.getElementById('coffeeweightoutput').textContent = coffeeContent.toFixed(2);
        document.getElementById('coffeestrengthoutput').textContent = coffeeStrength.toFixed(2);
        document.getElementById('fatweightoutput').textContent = fatContent.toFixed(2);
        document.getElementById('fatstrengthoutput').textContent = fatStrength.toFixed(2);
        document.getElementById('lactoseweightoutput').textContent = lactoseContent.toFixed(2);
        document.getElementById('lactosestrengthoutput').textContent = lactoseStrength.toFixed(2);
        document.getElementById('proteinweightoutput').textContent = proteinContent.toFixed(2);
        document.getElementById('proteinstrengthoutput').textContent = proteinStrength.toFixed(2);
        document.getElementById('totalweightoutput').textContent = totalWeight.toFixed(2);
        document.getElementById('totalvolumeoutput').textContent = Math.round(totalVolume * 100) / 100;  // 使用原始的四舍五入方式
        document.getElementById('totalvolumeoutput_oz').textContent = Math.round(totalVolumeOz * 100) / 100;  // 使用原始的四舍五入方式
    }

    // Cup Size计算逻辑
    function calculateCupSize(e) {
        // 防止表单提交
        e?.preventDefault();
        
        const coffeeWeight = parseFloat(document.getElementById('cupsizecoffeeweight').value);
        const TDS = parseFloat(document.getElementById('cupsizeTDS').value);  // 不除以100
        const aeration = parseFloat(document.getElementById('cupsizeaeration').value);
        const desiredTDS = parseFloat(document.getElementById('desiredTDS').value);  // 不除以100
        const condensation = parseFloat(document.getElementById('cupsizecondensation').value);

        // 计算所需牛奶重量 - 使用原始公式
        const requiredMilkWeight = (coffeeWeight * (TDS/desiredTDS) - coffeeWeight) / (1 + (condensation/100));

        // 计算最终体积
        const cupVolume = coffeeWeight + requiredMilkWeight * (1 + aeration/100);
        const cupVolumeOz = cupVolume / 28.41;

        // 更新输出
        document.getElementById('milkweightoutput').textContent = Math.round(requiredMilkWeight * 10) / 10;
        document.getElementById('cupvolumeoutput').textContent = Math.round(cupVolume * 10) / 10;
        document.getElementById('cupvolumeoutput_oz').textContent = Math.round(cupVolumeOz * 10) / 10;
    }

    // Reverse Latteculator计算逻辑
    function calculateSproSize(e) {
        // 防止表单提交
        e?.preventDefault();
        
        const TDS = parseFloat(document.getElementById('sproTDS').value);  // 不除以100
        const desiredTDS = parseFloat(document.getElementById('sprodesiredTDS').value);  // 不除以100
        const aeration = parseFloat(document.getElementById('sproaeration').value) * 0.01;  // 转换为小数
        const condensation = parseFloat(document.getElementById('sprocondensation').value) * 0.01;  // 转换为小数
        const cupVolume = parseFloat(document.getElementById('sprocupvolume').value);
        const isOz = document.getElementById('cupvolpicker').value === 'oz';

        // 转换体积到毫升
        const cupVolumeMl = isOz ? cupVolume * 28.41 : cupVolume;

        // 使用原始计算公式
        const dilution = (TDS - desiredTDS) / TDS;
        const volIncrease = (aeration - condensation) / (1 + condensation);
        const aEffect = dilution * volIncrease;
        const effectiveVol = cupVolumeMl / (1 + aEffect);
        const requiredCoffeeWeight = (1 - dilution) * effectiveVol;

        // 更新输出
        document.getElementById('sproweightoutput').textContent = Math.round(requiredCoffeeWeight * 100) / 100;
    }

    // 输入验证和结果更新函数
    function checkAndCalculate() {
        // 获取当前激活的计算器
        const activeTab = document.querySelector('.tab-active').id;
        
        // 根据不同计算器进行验证和计算
        switch(activeTab) {
            case 'app-Latteculator-tab':
                const condensation = parseFloat(document.getElementById('condensation').value);
                const aeration = parseFloat(document.getElementById('aeration').value);
                const latteResults = document.querySelector('.latteculator-results');
                
                if (condensation > aeration) {
                    latteResults.innerHTML = '<div role="alert" class="alert alert-warning"><span class="icon-[ph--warning-bold]"></span><span>错误：充气量不能低于冷凝量</span></div>';
                } else {
                    // 如果之前显示了错误，恢复结果表格
                    if (!document.querySelector('.latteculator-results table')) {
                        latteResults.innerHTML = `
                            <div class="overflow-x-auto">
                                <table class="table w-full text-lg">
                                <tbody>
                                    <tr>
                                    <td class="opacity-70">咖啡固形物</td>
                                    <td class="font-medium"><span id="coffeeweightoutput">0.00</span>g (<span id="coffeestrengthoutput">0.00</span>%)</td>
                                    </tr>
                                    <tr>
                                    <td class="opacity-70">脂肪含量</td>
                                    <td class="font-medium"><span id="fatweightoutput">0.00</span>g (<span id="fatstrengthoutput">0.00</span>%)</td>
                                    </tr>
                                    <tr>
                                    <td class="opacity-70">乳糖含量</td>
                                    <td class="font-medium"><span id="lactoseweightoutput">0.00</span>g (<span id="lactosestrengthoutput">0.00</span>%)</td>
                                    </tr>
                                    <tr>
                                    <td class="opacity-70">蛋白质含量</td>
                                    <td class="font-medium"><span id="proteinweightoutput">0.00</span>g (<span id="proteinstrengthoutput">0.00</span>%)</td>
                                    </tr>
                                    <tr>
                                    <td class="opacity-70">总重量</td>
                                    <td class="font-medium"><span id="totalweightoutput">0.00</span>g</td>
                                    </tr>
                                    <tr>
                                    <td class="opacity-70">
                                        总体积
                                        <label for="modal-totalvolume" class="cursor-pointer">
                                        <span class="icon-[material-symbols--info-outline] text-info"></span>
                                        </label>
                                    </td>
                                    <td class="font-medium"><span id="totalvolumeoutput">0.00</span>ml (<span id="totalvolumeoutput_oz">0.00</span>oz)</td>
                                    </tr>
                                </tbody>
                                </table>
                            </div>`;
                    }
                    calculateLatteculator();
                }
                break;
            case 'app-Cupsize-tab':
                const cupsizeTDS = parseFloat(document.getElementById('cupsizeTDS').value);
                const desiredTDS = parseFloat(document.getElementById('desiredTDS').value);
                const cupResults = document.querySelector('.cupsize-results');
                
                if (cupsizeTDS < desiredTDS) {
                    cupResults.innerHTML = '<div role="alert" class="alert alert-warning"><span class="icon-[ph--warning-bold]"></span><span>错误：目标浓度必须小于等于TDS</span></div>';
                } else {
                    // 如果之前显示了错误，恢复结果表格
                    if (!document.querySelector('.cupsize-results table')) {
                        cupResults.innerHTML = `
                            <div class="overflow-x-auto rounded-lg border border-base-300">
                                <table class="table table-zebra w-full text-lg">
                                    <tbody>
                                        <tr class="hover">
                                            <td class="opacity-70 border-b border-base-300">所需牛奶</td>
                                            <td class="font-medium border-b border-base-300"><span id="milkweightoutput">0.0</span>g</td>
                                        </tr>
                                        <tr class="hover">
                                            <td class="opacity-70">最终体积</td>
                                            <td class="font-medium"><span id="cupvolumeoutput">0.0</span>ml (<span id="cupvolumeoutput_oz">0.0</span>oz)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>`;
                    }
                    calculateCupSize();
                }
                break;
            case 'app-Sprosize-tab':
                const sproTDS = parseFloat(document.getElementById('sproTDS').value);
                const sprodesiredTDS = parseFloat(document.getElementById('sprodesiredTDS').value);
                const sproResults = document.querySelector('.sprosize-results');
                
                if (sproTDS < sprodesiredTDS) {
                    sproResults.innerHTML = '<div role="alert" class="alert alert-warning"><span class="icon-[ph--warning-bold]"></span><span>错误：目标浓度必须小于等于TDS</span></div>';
                } else {
                    // 如果之前显示了错误，恢复结果表格
                    if (!document.querySelector('.sprosize-results table')) {
                        sproResults.innerHTML = `
                            <div class="overflow-x-auto rounded-lg border border-base-300">
                                <table class="table table-zebra w-full text-lg">
                                    <tbody>
                                        <tr class="hover">
                                            <td class="opacity-70">所需咖啡量</td>
                                            <td class="font-medium"><span id="sproweightoutput">0.00</span>g</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>`;
                    }
                    calculateSproSize();
                }
                break;
        }
    }

    // 为所有输入框添加事件监听
    const allInputs = document.querySelectorAll('input[type="number"], select');
    allInputs.forEach(input => {
        input.addEventListener('input', checkAndCalculate);
        input.addEventListener('change', checkAndCalculate);
    });

    // 移除表单提交事件
    [latteculatorForm, cupSizeForm, sproSizeForm].forEach(form => {
        if(form) form.onsubmit = (e) => e.preventDefault();
    });

    // 初始化
    setupTabSwitching();
    checkAndCalculate();
}); 