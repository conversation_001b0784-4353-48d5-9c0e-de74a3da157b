document.addEventListener('DOMContentLoaded', function() {
    // 检查是否为iOS设备
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

    if (isIOS) {
        const popupKey = 'ios_popup_shown';
        const popupShown = localStorage.getItem(popupKey);
        const popupDelay = 15000; // 本地测试时为5秒，生产环境为15000毫秒

        if (!popupShown) {
            setTimeout(() => {
                // 创建弹框元素
                const popup = document.createElement('div');
                popup.className = 'fixed inset-0 z-50 flex items-center justify-center'; // 弹框容器
                popup.innerHTML = `
                    <div class="absolute inset-0 bg-black/50 z-40"></div> <!-- 半透明背景 -->
                    <div class="bg-base-100 rounded-lg shadow-lg w-80 p-4 z-50"> <!-- 弹框 -->
                        <div class="text-xl font-semibold text-center mb-4">添加 咖啡搭子 到桌面</div>
                        <div class="mb-4">
                            <div class="text-base font-medium mb-2">1. 点击浏览器分享图标</div>
                            <img 
                                class="mx-auto" 
                                loading="lazy" 
                                width="227" 
                                height="51" 
                                decoding="async" 
                                src="https://pic.kafeidazi.com/base/add-to-screen-1%403x.png" 
                            />
                        </div>
                        <div class="mb-4">
                            <div class="text-base font-medium mb-2">2. 选择添加到主屏幕</div>
                            <img 
                                class="mx-auto" 
                                loading="lazy" 
                                width="227" 
                                height="40" 
                                decoding="async" 
                                src="https://pic.kafeidazi.com/base/add-to-screen-2%403x.png" 
                            />
                        </div>
                        <div 
                            class="btn hover:bg-primary-focus transition duration-200 w-full" 
                            id="popup-close">
                            <span>知道了</span>
                        </div>
                    </div>
                `;

                document.body.appendChild(popup);

                // 关闭弹框的事件
                popup.addEventListener('click', function(event) {
                    if (event.target === popup || event.target.id === 'popup-close') {
                        popup.remove();
                        localStorage.setItem(popupKey, 'true'); // 标记为已显示
                    }
                });

                // 关闭弹框的事件（点击背景）
                const mask = popup.querySelector('.absolute');
                mask.addEventListener('click', function() {
                    popup.remove();
                    localStorage.setItem(popupKey, 'true'); // 标记为已显示
                });
            }, popupDelay);
        }
    }
});
