// 获取 CSRF token 的函数
function getCsrfToken() {
    const name = 'csrftoken';
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

document.addEventListener('alpine:init', () => {
    Alpine.store('brewlogList', {
        currentStatus: null,
        
        filterByStatus(status) {
            if (this.currentStatus === status) {
                // 如果点击当前激活的状态，则取消筛选
                this.currentStatus = null;
                this.showAllBeans();
            } else {
                this.currentStatus = status;
                this.filterBeans(status);
            }
        },
        
        filterBeans(status) {
            const beanCards = document.querySelectorAll('.grid-cols-1.grid-rows-1 > div');
            
            beanCards.forEach(card => {
                const shouldShow = this.checkBeanStatus(card, status);
                card.style.display = shouldShow ? '' : 'none';
            });
            
            // 隐藏已归档部分
            const archivedSection = document.querySelector('.collapse-plus');
            if (archivedSection) {
                archivedSection.style.display = 'none';
            }
            
            // 处理空分组
            this.handleEmptyGroups();
        },
        
        showAllBeans() {
            const beanCards = document.querySelectorAll('.grid-cols-1.grid-rows-1 > div');
            beanCards.forEach(card => {
                card.style.display = '';
            });
            
            // 显示已归档部分
            const archivedSection = document.querySelector('.collapse-plus');
            if (archivedSection) {
                archivedSection.style.display = '';
            }
            
            // 处理空分组
            this.handleEmptyGroups();
        },
        
        checkBeanStatus(card, status) {
            const icons = card.querySelectorAll('.tooltip');
            
            switch (status) {
                case 'resting':
                    return Array.from(icons).some(icon => 
                        icon.getAttribute('data-tip') === '养豆中'
                    );
                    
                case 'out_of_stock':
                    return Array.from(icons).some(icon => 
                        icon.getAttribute('data-tip') === '已用完（未归档）'
                    );
                    
                case 'in_use':
                    return Array.from(icons).some(icon => 
                        icon.getAttribute('data-tip') === '使用中'
                    );
                    
                default:
                    return true;
            }
        },
        
        handleEmptyGroups() {
            const groups = document.querySelectorAll('.grid-cols-1.grid-rows-1');
            groups.forEach(group => {
                const visibleCards = Array.from(group.children).filter(card => 
                    card.style.display !== 'none'
                );
                
                // 获取对应的分隔线
                const divider = group.previousElementSibling;
                if (divider && divider.classList.contains('divider')) {
                    divider.style.display = visibleCards.length ? '' : 'none';
                }
                
                // 如果分组没有可见的卡片，则隐藏整个分组
                group.style.display = visibleCards.length ? '' : 'none';
            });
        },

        // 从brewlog.js迁移的操作功能
        async deleteBean(id) {
            try {
                const response = await fetch(`/my/brewlog/bean/${id}/delete/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.href = '/my/brewlog/bean/';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('删除失败');
            }
        },
        
        async archiveBean(id) {
            try {
                const response = await fetch(`/my/brewlog/bean/${id}/archive/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.href = '/my/brewlog/bean/';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('归档失败');
            }
        },
        
        async unarchiveBean(id) {
            try {
                const response = await fetch(`/my/brewlog/bean/${id}/unarchive/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.href = '/my/brewlog/bean/';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('取消归档失败');
            }
        },
        
        async toggleFavoriteBean(id) {
            try {
                // 获取咖啡豆元素以检查是否归档
                const beanCard = document.querySelector(`[data-bean-id="${id}"]`);
                const isArchived = beanCard && beanCard.closest('.collapse-content') !== null;
                
                // 如果已归档，先取消归档
                if (isArchived) {
                    await this.unarchiveBean(id);
                }
                
                // 然后设置首选状态
                const response = await fetch(`/my/brewlog/bean/${id}/toggle-favorite/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                const data = await response.json();
                if (data.status === 'success') {
                    window.location.href = '/my/brewlog/bean/';
                }
            } catch (error) {
                console.error('Error:', error);
                alert('操作失败，请稍后再试');
            }
        }
    });
}); 