class TagManager {
    constructor() {
        this.input = document.querySelector('input[name="flavor_tags"]');
        this.hiddenInput = document.querySelector('#flavor-tags-data');
        this.dropdown = document.querySelector('#tags-dropdown');
        this.selectedTagsContainer = document.querySelector('#selected-tags');
        this.selectedTags = new Set();
        this.allTags = [];
        
        if (this.input) {
            this.init();
        }
    }

    async init() {
        // 从 data-initial-tags 属性获取初始标签
        const initialTags = this.input.dataset.initialTags || '';
        const existingTags = initialTags.split(',')
            .map(tag => tag.trim())
            .filter(tag => tag);
        
        // 设置事件监听器
        this.setupEventListeners();
        
        // 等待加载所有可用标签
        await this.loadExistingTags();
        
        // 在加载完所有标签后，添加已有的标签
        existingTags.forEach(tag => this.addTag(tag));
        
        // 清空输入框
        this.input.value = '';
        
        // 更新隐藏输入框的值
        this.updateHiddenInput();
    }

    async loadExistingTags() {
        const url = this.input.dataset.tagsUrl;
        if (!url) {
            console.error('Tags URL not found');
            return;
        }

        try {
            const response = await fetch(url);
            const tags = await response.json();
            this.allTags = tags.map(tag => ({
                ...tag,
                canDelete: tag.usage_count === 0 // 添加是否可删除的标记
            }));
        } catch (error) {
            console.error('Error loading tags:', error);
        }
    }

    setupEventListeners() {
        // 输入框事件
        this.input.addEventListener('input', () => this.handleInput());
        this.input.addEventListener('keydown', (e) => this.handleKeydown(e));
        
        // 点击事件处理
        document.addEventListener('click', (e) => {
            if (!this.input.contains(e.target) && !this.dropdown.contains(e.target)) {
                this.hideDropdown();
            }
        });
    }

    handleInput() {
        const value = this.input.value.trim();
        if (value) {
            this.showDropdown(value);
        } else {
            this.hideDropdown();
        }
    }

    handleKeydown(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const value = this.input.value.trim();
            if (value) {
                // 检查是否存在匹配的标签
                const matchingTag = this.allTags.find(tag => 
                    tag.name.toLowerCase() === value.toLowerCase()
                );
                if (matchingTag) {
                    this.addTag(matchingTag.name);
                } else {
                    // 如果是新标签，创建新标签
                    this.createNewTag(value);
                }
                this.input.value = '';
                this.hideDropdown();
            }
        }
    }

    showDropdown(searchText) {
        const matches = this.allTags.filter(tag => 
            tag.name.toLowerCase().includes(searchText.toLowerCase()) &&
            !this.selectedTags.has(tag.name)
        );

        if (matches.length) {
            this.dropdown.innerHTML = matches
                .map(tag => `<li>
                    <div class="w-full flex items-center justify-between hover:bg-base-300">
                        <a href="#" data-tag="${tag.name}" class="flex-grow">${tag.name}</a>
                        ${tag.canDelete ? 
                            `<button type="button" class="px-3 py-2 text-error hover:bg-error hover:text-error-content" data-delete-tag="${tag.name}" data-tag-id="${tag.id}">×</button>` : 
                            '<span class="w-10"></span>'
                        }
                    </div>
                </li>`)
                .join('');
            
            this.dropdown.querySelectorAll('a').forEach(a => {
                a.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.addTag(e.target.dataset.tag);
                    this.input.value = '';
                    this.hideDropdown();
                });
            });
            
            this.dropdown.querySelectorAll('button[data-delete-tag]').forEach(button => {
                button.addEventListener('click', async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const tagName = button.dataset.deleteTag;
                    const tagId = button.dataset.tagId;
                    
                    if (tagId) {
                        const success = await this.deleteTag(tagId);
                        if (!success) {
                            window.dispatchEvent(new CustomEvent('show-warning', { 
                                detail: '无法删除该标签，可能已被其他冲煮记录使用' 
                            }));
                            return;
                        }
                    }
                    
                    // 从可选标签列表中移除
                    this.allTags = this.allTags.filter(tag => tag.name !== tagName);
                    // 重新显示下拉菜单
                    this.showDropdown(this.input.value.trim());
                });
            });
            
            this.dropdown.classList.remove('hidden');
        } else {
            this.hideDropdown();
        }
    }

    hideDropdown() {
        this.dropdown.classList.add('hidden');
    }

    addTag(tagName) {
        if (this.selectedTags.has(tagName)) return;
        
        const tagElement = document.createElement('div');
        tagElement.className = 'bg-primary/10 text-primary px-2 py-1 rounded-lg flex items-center gap-2';
        tagElement.innerHTML = `
            <span>${tagName}</span>
            <button type="button" class="hover:text-error">×</button>
        `;
        
        tagElement.querySelector('button').addEventListener('click', () => {
            this.removeTag(tagName);
            tagElement.remove();
        });
        
        this.selectedTagsContainer.appendChild(tagElement);
        this.selectedTags.add(tagName);
        this.updateHiddenInput();
    }

    removeTag(tagName) {
        this.selectedTags.delete(tagName);
        this.updateHiddenInput();
    }

    updateHiddenInput() {
        this.hiddenInput.value = Array.from(this.selectedTags).join(',');
    }

    async createNewTag(tagName) {
        try {
            const response = await fetch('/my/brewlog/flavor-tags/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({ name: tagName })
            });

            if (!response.ok) throw new Error('Failed to create tag');
            
            const newTag = await response.json();
            // 添加到可用标签列表
            this.allTags.push(newTag);
            // 添加到已选标签
            this.addTag(newTag.name);
        } catch (error) {
            console.error('Error creating new tag:', error);
        }
    }

    async deleteTag(tagId) {
        try {
            const response = await fetch(`/my/brewlog/flavor-tags/${tagId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            });

            if (!response.ok) {
                throw new Error('Failed to delete tag');
            }
            
            return true;
        } catch (error) {
            console.error('Error deleting tag:', error);
            return false;
        }
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化标签管理器
    new TagManager();
    
    // 初始化烘焙程度滑块
    const slider = document.querySelector('input[type="range"][data-field="roast_level"]');
    const roastLevelInput = document.querySelector('input[name="roast_level"]');
    
    if (slider && roastLevelInput) {
        // 设置初始值
        slider.value = roastLevelInput.value || 3;
        
        // 监听滑块变化
        slider.addEventListener('input', () => {
            roastLevelInput.value = slider.value;
        });
    }
});

// 等待 Alpine.js 加载完成
if (window.Alpine) {
    initializeAlpine();
} else {
    document.addEventListener('alpine:init', initializeAlpine);
}

function initializeAlpine() {
    Alpine.data('beanForm', () => ({
        sections: {
            general: true,
            product: true,
            details: false
        },
        validationError: false,
        submitting: false,
        restPeriodType: 'SINGLE',
        roastDateEnabled: false,
        initialRoastDate: '',
        
        init() {
            // 从表单元素获取初始类型值
            this.typeValue = this.$el.querySelector('input[name="type"]:checked')?.value || 'SKIP';
            
            // 如果不是 SKIP，立即设置 details 为 true
            if (this.typeValue !== 'SKIP') {
                this.sections.details = true;
            }
            
            // 监听 typeValue 变化
            this.$watch('typeValue', value => {
                if (value !== 'SKIP') {
                    this.sections.details = true;
                }
            });
            
            // 初始化烘焙日期状态和值
            const roastDateWrapper = this.$refs.roastDateWrapper;
            if (roastDateWrapper) {
                const roastDateInput = roastDateWrapper.querySelector('input[name="roast_date"]');
                if (roastDateInput && roastDateInput.value) {
                    this.roastDateEnabled = true;
                    this.initialRoastDate = roastDateInput.value;
                }
            }
            
            // 监听烘焙日期启用状态变化
            this.$watch('roastDateEnabled', (value) => {
                const roastDateInput = this.$refs.roastDateInput;
                if (!roastDateInput) return;
                
                if (value && !roastDateInput.value) {
                    // 如果启用且没有值，设置为今天
                    const today = new Date().toISOString().split('T')[0];
                    roastDateInput.value = today;
                }
            });
            
            // 保存其他初始值
            this.initialRestPeriodMin = this.$el.querySelector('input[name="rest_period_min"]')?.value || '';
            this.initialRestPeriodMax = this.$el.querySelector('input[name="rest_period_max"]')?.value || '';
            
            // 监听养豆期类型变化
            this.$watch('restPeriodType', value => {
                const restPeriodMin = this.$el.querySelector('input[name="rest_period_min"]');
                const restPeriodMax = this.$el.querySelector('input[name="rest_period_max"]');
                
                if (value === 'SINGLE') {
                    // 单一值模式：清空最大值
                    if (restPeriodMax) {
                        restPeriodMax.value = '';
                    }
                }
            });

            // 监听养豆期字段的输入，实时移除错误状态
            const restPeriodMinInput = document.querySelector('input[name="rest_period_min"]');
            const restPeriodMaxInput = document.querySelector('input[name="rest_period_max"]');
            
            [restPeriodMinInput, restPeriodMaxInput].forEach(input => {
                if (input) {
                    input.addEventListener('input', () => {
                        const errorMsg = input.parentElement.querySelector('.error-message');
                        if (errorMsg) {
                            errorMsg.remove();
                        }
                        input.classList.remove('input-error');
                        this.validationError = false;
                    });
                }
            });
        },

        handleSubmit(e) {
            // 首先验证库存和包装规格
            const bagWeight = parseFloat(document.querySelector('input[name="bag_weight"]')?.value || 0);
            const bagRemain = parseFloat(document.querySelector('input[name="bag_remain"]')?.value || 0);
            
            // 处理烘焙日期相关字段
            if (!this.roastDateEnabled) {
                const roastDateInput = this.$el.querySelector('input[name="roast_date"]');
                const restPeriodMin = this.$el.querySelector('input[name="rest_period_min"]');
                const restPeriodMax = this.$el.querySelector('input[name="rest_period_max"]');
                
                if (roastDateInput) roastDateInput.value = '';
                if (restPeriodMin) restPeriodMin.value = '';
                if (restPeriodMax) restPeriodMax.value = '';
            }
            
            // 处理养豆期字段
            const restPeriodMin = document.querySelector('input[name="rest_period_min"]');
            const restPeriodMax = document.querySelector('input[name="rest_period_max"]');
            
            // 清除之前的错误状态
            this.validationError = false;
            if (restPeriodMax) {
                restPeriodMax.classList.remove('input-error');
                const errorMsg = restPeriodMax.parentElement.querySelector('.error-message');
                if (errorMsg) {
                    errorMsg.remove();
                }
            }
            
            // 获取当前界面上的养豆期类型选择状态
            const restPeriodToggle = document.querySelector('input[type="checkbox"].toggle[x-model="restPeriodType"]');
            const isRangeMode = restPeriodToggle && restPeriodToggle.checked;
            
            // 如果是单一值模式，清除最大养豆期的值
            if (!isRangeMode && restPeriodMax) {
                restPeriodMax.value = '';
            }
            
            // 只在养豆期类型为范围值时验证最大最小值
            if (isRangeMode && restPeriodMin && restPeriodMax && 
                restPeriodMin.value && restPeriodMax.value) {
                const minVal = parseInt(restPeriodMin.value);
                const maxVal = parseInt(restPeriodMax.value);
                
                if (maxVal < minVal) {
                    e.preventDefault();
                    this.validationError = true;
                    
                    restPeriodMax.classList.add('input-error');
                    let errorMsg = restPeriodMax.parentElement.querySelector('.error-message');
                    if (!errorMsg) {
                        errorMsg = document.createElement('div');
                        errorMsg.className = 'text-error text-sm mt-1 error-message';
                        restPeriodMax.parentElement.appendChild(errorMsg);
                    }
                    errorMsg.textContent = '最长养豆期不能小于最短养豆期';
                    restPeriodMax.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    return false;
                }
            }
            
            if (bagRemain > bagWeight) {
                e.preventDefault();
                this.validationError = true;
                
                // 找到 bag_remain 输入框并添加错误样式
                const bagRemainInput = document.querySelector('input[name="bag_remain"]');
                bagRemainInput.classList.add('input-error');
                
                // 创建或更新错误消息
                let errorMsg = bagRemainInput.parentElement.querySelector('.error-message');
                if (!errorMsg) {
                    errorMsg = document.createElement('div');
                    errorMsg.className = 'text-error text-sm mt-1 error-message';
                    bagRemainInput.parentElement.appendChild(errorMsg);
                }
                errorMsg.textContent = '库存余量不能大于包装规格';
                
                // 滚动到错误位置
                bagRemainInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                return false;
            }

            // 验证拼配比例
            const totalRatio = parseFloat(Alpine.store('blendComponents').totalRatio || 0);
            const typeValue = this.typeValue;
            const hasBlendComponents = this.$store.blendComponents.components.length > 1;
            
            if (typeValue === 'BLEND' && hasBlendComponents) {
                if (Math.abs(totalRatio - 100) > 0.02) {
                    e.preventDefault();
                    window.dispatchEvent(new CustomEvent('show-warning', { 
                        detail: '拼配比例总和必须等于100%，请检查您的拼配方案。'
                    }));
                    return false;
                }
            }
            
            // 如果是单一组件的拼配豆，自动设置比例为100%
            if (typeValue === 'BLEND' && !hasBlendComponents) {
                this.$store.blendComponents.components[0].blend_ratio = '100';
            }
            
            // 如果没有验证错误，提交表单
            if (!this.validationError) {
                this.submitting = true;
                return true;
            }
            return false;
        },

        get showDetails() {
            return this.typeValue !== 'SKIP';
        }
    }));

    Alpine.data('blendComponents', () => ({
        addComponent() {
            if (this.components.length < 5) {
                const newId = this.components.length + 1;
                this.components.push({
                    id: newId,
                    blend_ratio: '0',
                    roast_level: 4,
                    origin: '',
                    region: '',
                    finca: '',
                    variety: '',
                    altitude: '',
                    process: '',
                    isExpanded: true
                });
                this.$nextTick(() => {
                    this.updateRatios();
                });
            }
        },
        
        removeComponent(index) {
            if (this.components.length > 1) {
                this.components.splice(index, 1);
                this.$nextTick(() => {
                    this.updateRatios();
                });
            }
        },
        
        updateRatios() {
            const equalRatio = (100 / (this.$store?.blendComponents?.components?.length || this.components.length)).toFixed(2);
            if (this.$store?.blendComponents?.components) {
                this.$store.blendComponents.components.forEach((comp, idx) => {
                    comp.blend_ratio = equalRatio;
                });
                this.$store.blendComponents.totalRatio = this.getTotalRatio();
            } else {
                this.components.forEach((comp, idx) => {
                    const input = document.querySelector(`input[name="blend_components-${idx}-blend_ratio"]`);
                    if (input) input.value = equalRatio;
                });
            }
        },
        
        getTotalRatio() {
            const total = (this.$store?.blendComponents?.components || this.components)
                .reduce((sum, comp) => sum + (parseFloat(comp.blend_ratio) || 0), 0)
                .toFixed(2);
            if (this.$store?.blendComponents) {
                this.$store.blendComponents.totalRatio = total;
            }
            return total;
        }
    }));
}