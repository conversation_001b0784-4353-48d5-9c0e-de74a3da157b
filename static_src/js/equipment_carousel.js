import '../css/equipmentcarousel.css'

class EspressoCarousel {
  constructor() {
    this.carousel = document.getElementById('main-carousel');
    this.prevBtn = document.getElementById('prev-btn');
    this.nextBtn = document.getElementById('next-btn');
    this.thumbnails = document.querySelectorAll('.thumbnail-btn');
    this.colorButtons = document.querySelectorAll('.btn[data-image-url]');
    this.currentSlide = 1;
    this.imagesLoaded = false;
    
    // 触摸事件相关变量
    this.touchStartX = 0;
    this.touchEndX = 0;
    this.minSwipeDistance = 50;

    // 获取总幻灯片数量
    this.totalSlides = this.carousel.querySelectorAll('.carousel-item').length;

    this.init();
  }

  init() {
    this.bindEvents();
    this.preloadImages();
    this.goToSlide(1);
  }

  bindEvents() {
    // 触摸事件
    this.carousel.addEventListener('touchstart', (e) => {
      this.touchStartX = e.touches[0].clientX;
    });

    this.carousel.addEventListener('touchmove', (e) => {
      e.preventDefault();
    });

    this.carousel.addEventListener('touchend', (e) => {
      this.touchEndX = e.changedTouches[0].clientX;
      this.handleSwipe();
    });

    // 按钮点击事件
    this.prevBtn.addEventListener('click', () => {
      if (!this.imagesLoaded) return;
      this.currentSlide = this.currentSlide > 1 ? this.currentSlide - 1 : this.totalSlides;
      this.goToSlide(this.currentSlide);
    });

    this.nextBtn.addEventListener('click', () => {
      if (!this.imagesLoaded) return;
      this.currentSlide = this.currentSlide < this.totalSlides ? this.currentSlide + 1 : 1;
      this.goToSlide(this.currentSlide);
    });

    // 缩略图点击事件
    this.thumbnails.forEach(thumb => {
      thumb.addEventListener('click', () => {
        if (!this.imagesLoaded) return;
        const slideNumber = parseInt(thumb.dataset.slide);
        this.goToSlide(slideNumber);
      });
    });

    // 颜色按钮点击事件
    this.colorButtons.forEach(button => {
      button.addEventListener('click', () => {
        const imageUrl = button.dataset.imageUrl;
        if (imageUrl) {
          this.updateMainImage(imageUrl);
        }
      });
    });
  }

  handleSwipe() {
    if (!this.imagesLoaded) return;
    
    const swipeDistance = this.touchEndX - this.touchStartX;
    
    if (Math.abs(swipeDistance) > this.minSwipeDistance) {
      if (swipeDistance > 0) {
        this.currentSlide = this.currentSlide > 1 ? this.currentSlide - 1 : this.totalSlides;
      } else {
        this.currentSlide = this.currentSlide < this.totalSlides ? this.currentSlide + 1 : 1;
      }
      this.goToSlide(this.currentSlide);
    }
  }

  preloadImages() {
    const imageUrls = Array.from(document.querySelectorAll('.carousel-item img')).map(img => img.src);

    // 添加颜色变体图片到预加载列表
    this.colorButtons.forEach(btn => {
      const colorImageUrl = btn.dataset.imageUrl;
      if (colorImageUrl && !imageUrls.includes(colorImageUrl)) {
        imageUrls.push(colorImageUrl);
      }
    });

    let loadedImages = 0;
    imageUrls.forEach(url => {
      const img = new Image();
      img.onload = () => {
        loadedImages++;
        if (loadedImages === imageUrls.length) {
          this.imagesLoaded = true;
          // 移除所有skeleton
          document.querySelectorAll('.skeleton').forEach(skeleton => {
            skeleton.style.display = 'none';
          });
          // 显示所有图片
          document.querySelectorAll('.carousel-item img').forEach(img => {
            img.style.opacity = '1';
          });
        }
      };
      img.src = url;
    });
  }

  updateMainImage(imageUrl) {
    if (!this.imagesLoaded) return;
    const slides = this.carousel.querySelectorAll('.carousel-item');
    
    // 更新第一张图片的src
    const firstSlideImg = slides[0].querySelector('img');
    if (firstSlideImg) {
      firstSlideImg.src = imageUrl;
    }
    
    // 更新第一个缩略图
    const firstThumbnail = document.querySelector('.thumbnail-btn[data-slide="1"] img');
    if (firstThumbnail) {
      firstThumbnail.src = imageUrl;
    }
    
    // 确保显示第一张图片
    this.goToSlide(1);
  }

  goToSlide(slideNumber) {
    if (!this.imagesLoaded) return;
    const slides = this.carousel.querySelectorAll('.carousel-item');
    
    slides.forEach(slide => {
      slide.style.display = 'none';
    });
    
    slides[slideNumber - 1].style.display = 'block';
    
    this.currentSlide = slideNumber;
    
    this.thumbnails.forEach(thumb => {
      if (parseInt(thumb.dataset.slide) === slideNumber) {
        thumb.classList.add('active');
        thumb.classList.add('outline-base-content');
      } else {
        thumb.classList.remove('active');
        thumb.classList.remove('outline-base-content');
      }
    });
  }
}

// 当 DOM 加载完成时初始化轮播图
document.addEventListener('DOMContentLoaded', function() {
  new EspressoCarousel();
}); 