import '../css/brewloghindsight.css'

// 在文件开头定义并初始化 Alpine store
document.addEventListener('alpine:init', () => {
    // 添加一个全局状态来跟踪是否有隐藏的面板
    Alpine.store('globalState', {
        hiddenPanels: {},
        checkHiddenState(panelId) {
            const isHidden = localStorage.getItem(`${panelId}-hidden`) === 'true'
            this.hiddenPanels[panelId] = isHidden
        },
        hasAnyHiddenPanels() {
            return Object.values(this.hiddenPanels).some(isHidden => isHidden)
        },
        showAllPanels() {
            // 清除所有面板的隐藏状态
            Object.keys(this.hiddenPanels).forEach(panelId => {
                localStorage.removeItem(`${panelId}-hidden`)
                this.hiddenPanels[panelId] = false
            })
            // 添加 show=true 参数并重新加载页面
            const url = new URL(window.location.href)
            url.searchParams.set('show', 'true')
            window.location.href = url.toString()
        }
    })

    Alpine.data('brewlogHindsight', (panelId) => ({
        isHidden: false,
        isCollapsed: false,

        init() {
            // 检查 URL 参数，如果有 show=true，则移除隐藏状态
            const urlParams = new URLSearchParams(window.location.search)
            if (urlParams.get('show') === 'true') {
                this.isHidden = false
                localStorage.removeItem(`${panelId}-hidden`)
                Alpine.store('globalState').hiddenPanels[panelId] = false
                // 移除 show 参数，避免刷新时重复处理
                if (window.history.replaceState) {
                    const url = new URL(window.location.href)
                    url.searchParams.delete('show')
                    window.history.replaceState({}, document.title, url.toString())
                }
            } else {
                // 从 localStorage 获取面板状态
                this.isHidden = localStorage.getItem(`${panelId}-hidden`) === 'true'
                this.isCollapsed = localStorage.getItem(`${panelId}-collapsed`) === 'true'
            }
            Alpine.store('globalState').checkHiddenState(panelId)
        },

        hidePanel() {
            this.isHidden = true
            localStorage.setItem(`${panelId}-hidden`, 'true')
            Alpine.store('globalState').hiddenPanels[panelId] = true
        },

        showPanel() {
            this.isHidden = false
            localStorage.removeItem(`${panelId}-hidden`)
            Alpine.store('globalState').hiddenPanels[panelId] = false
        },

        toggleCollapse() {
            this.isCollapsed = !this.isCollapsed
            if (this.isCollapsed) {
                localStorage.setItem(`${panelId}-collapsed`, 'true')
            } else {
                localStorage.removeItem(`${panelId}-collapsed`)
            }
        },

        expand() {
            this.isCollapsed = false
            localStorage.removeItem(`${panelId}-collapsed`)
            this.showPanel()
        },

        collapse() {
            this.isCollapsed = true
            localStorage.setItem(`${panelId}-collapsed`, 'true')
        }
    }))
})
