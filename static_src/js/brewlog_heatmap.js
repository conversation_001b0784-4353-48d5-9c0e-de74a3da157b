import '../css/brewlogheatmap.css'

document.addEventListener('alpine:init', () => {
    // 年份选择器组件
    Alpine.data('yearSelector', () => ({
        selectedYear: null,
        availableYears: [],
        
        init() {
            // 从URL中获取当前年份
            const pathMatch = window.location.pathname.match(/\/brewlog\/heatmap\/(\d{4})\//);
            this.selectedYear = pathMatch ? parseInt(pathMatch[1]) : new Date().getFullYear();
            
            // 从服务器获取可用年份
            fetch('/my/brewlog/heatmap/years/')
                .then(response => response.json())
                .then(data => {
                    // 确保当前年份在列表中
                    if (!data.years.includes(this.selectedYear)) {
                        data.years.push(this.selectedYear);
                    }
                    this.availableYears = data.years.sort((a, b) => b - a);  // 降序排列，最新年份在前
                    
                    // 确保选择器显示正确的年份
                    this.$nextTick(() => {
                        const select = this.$el.querySelector('select');
                        if (select) {
                            select.value = this.selectedYear;
                        }
                    });
                })
                .catch(error => {
                    console.error('Error fetching available years:', error);
                    // 如果获取失败，至少显示当前年份
                    this.availableYears = [this.selectedYear];
                });
        },
        
        changeYear() {
            // 获取新的热力图数据
            window.location.href = `/my/brewlog/heatmap/${this.selectedYear}/`;
        }
    }));

    // 热力图数据组件
    Alpine.data('heatmapData', () => ({
        init() {
            const container = this.$el;
            this.cells = JSON.parse(container.dataset.calendar);
            this.currentMonthIndex = parseInt(container.dataset.currentMonth);
            
            // 移除系统主题监听，改为监听网站主题变化
            this.$watch('$store.theme.isDark', () => {
                this.updateColorScale();
            });
            
            // 监听年份变更事件
            window.addEventListener('update-heatmap', (event) => {
                this.cells = event.detail.data;
                this.$nextTick(() => this.scrollToCurrentMonth());
            });
            
            this.updateColorScale();
        },
        cells: [],
        currentMonthIndex: 0,
        colorScale: [],
        
        updateColorScale() {
            // 使用 Alpine store 中的 isDark 来判断主题
            const isDark = this.$store.theme.isDark;
            this.colorScale = isDark ? [
                'rgb(100, 105, 100)',      // 0次 - 保持灰色基调
                'rgb(200, 170, 140)',      // 1次 - 浅棕色
                'rgb(220, 140, 100)',      // 2次 - 橙棕色
                'rgb(240, 110, 60)',       // 3次 - 橙红色
                'rgb(250, 80, 30)',        // 4次或以上 - 深橙红色
            ] : [
                'rgb(240, 242, 240)',      // 0次
                'rgb(255, 243, 221)',      // 1次
                'rgb(254, 225, 177)',      // 2次
                'rgb(253, 189, 111)',      // 3次
                'rgb(252, 141, 49)',       // 4次或以上
            ];
        },
        
        getCellStyle(cell) {
            const colorIndex = this.getColorIndex(cell.count);
            const date = new Date(cell.date);
            let column = cell.week;
            if (date.getMonth() === 11 && cell.week === 1) {
                column = 53;
            }
            
            return {
                'background-color': this.colorScale[colorIndex],
                'grid-row': cell.weekday + 1,
                'grid-column': column
            };
        },
        
        getColorIndex(count) {
            if (count === 0) return 0;
            if (count === 1) return 1;
            if (count === 2) return 2;
            if (count === 3) return 3;
            if (count === 4) return 4;
            return 4;  // 5及以上次数
        },
        
        showDetails(cell) {
            this.$dispatch('show-details', {
                date: cell.date,
                count: cell.count
            });
        },
        
        scrollToCurrentMonth() {
            if (window.innerWidth < 1024) {
                const scrollContainer = this.$el;
                const totalWidth = scrollContainer.scrollWidth;
                const monthWidth = totalWidth / 12;
                const scrollPosition = monthWidth * this.currentMonthIndex;
                scrollContainer.scrollLeft = scrollPosition;
            }
        }
    }));
});