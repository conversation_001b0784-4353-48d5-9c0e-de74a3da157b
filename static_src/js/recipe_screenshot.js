import { domToPng } from 'modern-screenshot'

document.addEventListener('DOMContentLoaded', () => {
    const printBtn = document.querySelector('.btn.btn-outline .icon-\\[ps--printer\\]')?.closest('button')
    if (!printBtn) return

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)

    const generateScreenshot = async (content, retries = 3) => {
        for (let i = 0; i < retries; i++) {
            try {
                const dataUrl = await domToPng(content, {
                    type: 'image/png',
                    scale: 2,
                    quality: 1,
                    backgroundColor: '#ffffff',
                    features: {
                        copyScrollbar: true,
                        removeAbnormalAttributes: true,
                        removeControlCharacter: true,
                        fixSvgXmlDecode: true,
                        restoreScrollPosition: true
                    }
                })
                return dataUrl
            } catch (error) {
                if (i === retries - 1) throw error
                await new Promise(resolve => setTimeout(resolve, 500))
            }
        }
    }

    const createPrintFrame = (dataUrl) => {
        const existingFrame = document.getElementById('print-frame')
        if (existingFrame) {
            existingFrame.remove()
        }

        const iframe = document.createElement('iframe')
        iframe.id = 'print-frame'
        iframe.style.cssText = 'position: fixed; top: -9999px; left: -9999px; width: 0; height: 0; border: 0;'
        document.body.appendChild(iframe)

        const doc = iframe.contentDocument || iframe.contentWindow.document
        doc.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>打印预览</title>
                <style>
                    @page {
                        margin: 1cm;
                        size: auto;
                    }
                    body {
                        margin: 0;
                        padding: 0;
                    }
                    img {
                        width: 100%;
                        height: auto;
                        display: block;
                        page-break-inside: avoid;
                    }
                    @media print {
                        img {
                            max-width: 100% !important;
                        }
                    }
                </style>
            </head>
            <body>
                <img src="${dataUrl}" onload="window.parent.postMessage('print-ready', '*')" />
            </body>
            </html>
        `)
        doc.close()

        return iframe
    }

    printBtn.addEventListener('click', async () => {
        if (printBtn.dataset.processing === 'true') return
        
        const content = document.querySelector('.container.mx-auto.px-4:has(.flex.flex-col-reverse)')
        if (!content) {
            const toast = document.createElement('div')
            toast.className = 'toast toast-top toast-center z-[9999]'
            toast.innerHTML = `
                <div class="alert alert-warning">
                    <span>页面内容未准备好，请刷新后重试</span>
                </div>
            `
            document.body.appendChild(toast)
            setTimeout(() => toast.remove(), 3000)
            return
        }

        printBtn.dataset.processing = 'true'
        
        const originalBtnContent = printBtn.innerHTML
        printBtn.innerHTML = `<span class="loading loading-spinner text-lg"></span><span class="hidden md:block">生成中</span>`
        
        const elementsToHide = []
        
        const originalTheme = document.documentElement.getAttribute('data-theme')
        const computedStyle = window.getComputedStyle(content)
        const originalStyles = {
            width: content.style.width || computedStyle.width,
            maxWidth: content.style.maxWidth || computedStyle.maxWidth,
            backgroundColor: content.style.backgroundColor || computedStyle.backgroundColor,
            padding: content.style.padding || computedStyle.padding,
            margin: content.style.margin || computedStyle.margin
        }

        try {
            const elementsToRemove = [
                '.flex.flex-row.flex-wrap.gap-4',
                'a[x-show]',
                '.divider.menu-title.max-w-md.mx-auto.mt-8',
                '.join.join-vertical.w-full',
                '.space-y-6.justify-center'
            ]

            elementsToRemove.forEach(selector => {
                const element = content.querySelector(selector)
                if (element) {
                    elementsToHide.push({
                        element,
                        parent: element.parentElement,
                        nextSibling: element.nextSibling
                    })
                    element.parentElement.removeChild(element)
                }
            })

            document.documentElement.setAttribute('data-theme', 'cupcake')
            Object.assign(content.style, {
                width: isMobile ? '100%' : '800px',
                maxWidth: '800px',
                backgroundColor: '#ffffff',
                padding: isMobile ? '15px' : '20px',
                margin: '0 auto'
            })

            await new Promise(resolve => setTimeout(resolve, 200))
            const dataUrl = await generateScreenshot(content)

            const printFrame = createPrintFrame(dataUrl)
            
            const messageHandler = (event) => {
                if (event.data === 'print-ready') {
                    window.removeEventListener('message', messageHandler)
                    printFrame.contentWindow.print()
                }
            }
            window.addEventListener('message', messageHandler)

            printBtn.innerHTML = `<span class="icon-[material-symbols--check-circle-outline] text-lg"></span><span class="hidden md:block">已打印</span>`

        } catch (error) {
            const toast = document.createElement('div')
            toast.className = 'toast toast-top toast-center z-[9999]'
            toast.innerHTML = `
                <div class="alert alert-warning">
                    <span>${error.message || '打印失败，请稍候重试'}</span>
                </div>
            `
            document.body.appendChild(toast)
            setTimeout(() => toast.remove(), 3000)
            
            printBtn.innerHTML = `<span class="icon-[material-symbols--error-outline] text-lg"></span><span class="hidden md:block">失败</span>`

        } finally {
            setTimeout(() => {
                printBtn.innerHTML = originalBtnContent
            }, 2000)
            
            document.documentElement.setAttribute('data-theme', originalTheme)
            Object.keys(originalStyles).forEach(key => {
                if (originalStyles[key] === 'auto' || !originalStyles[key]) {
                    content.style[key] = ''
                } else {
                    content.style[key] = originalStyles[key]
                }
            })
            
            elementsToHide.forEach(({element, parent, nextSibling}) => {
                if (nextSibling) {
                    parent.insertBefore(element, nextSibling)
                } else {
                    parent.appendChild(element)
                }
            })
            
            if (window.gc) window.gc()
            printBtn.dataset.processing = 'false'
        }
    })
}) 