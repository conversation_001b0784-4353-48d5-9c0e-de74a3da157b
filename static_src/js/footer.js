// 在 Alpine.js 加载前就初始化主题
;(function() {
  const isDark = localStorage.theme === 'dark';
  const theme = isDark ? 'dracula' : 'cupcake';
  document.documentElement.setAttribute('data-theme', theme);
})();

document.addEventListener('alpine:init', () => {
  // global theme store
  Alpine.store('theme', {
    isDark: localStorage.theme === 'dark',
    
    toggle() {
      this.isDark = !this.isDark;
      const theme = this.isDark ? 'dracula' : 'cupcake';
      document.documentElement.setAttribute('data-theme', theme);
      localStorage.theme = this.isDark ? 'dark' : 'light';
    }
  });

  // current year
  Alpine.data('year', () => ({
    currentYear: new Date().getFullYear()
  }))

  // global theme toggle
  Alpine.data('themeController', () => ({
    get isDark() {
      return this.$store.theme.isDark;
    },
    
    toggleTheme() {
      this.$store.theme.toggle();
    },

    init() {
    }
  }))

  // 微信群二维码时间检查
  Alpine.data('wechatQr', () => ({
    isVisible: true,

    init() {
      const qrImg = this.$el.querySelector('img');
      if (!qrImg) return;

      const url = new URL(qrImg.src);
      const timestamp = url.searchParams.get('updatedAt');
      if (!timestamp) {
        this.isVisible = true;  // 如果没有时间戳参数，保持显示
        return;
      }

      const now = Date.now();
      const sevenDays = 7 * 24 * 60 * 60 * 1000; // 7天的毫秒数
      
      this.isVisible = (now - parseInt(timestamp)) <= sevenDays;

      // console.log('QR Visibility Check:', {
      //   now,
      //   timestamp,
      //   diff: now - parseInt(timestamp),
      //   sevenDays,
      //   isVisible: this.isVisible
      // });
    }
  }))
}) 