/* 隐藏 DaisyUI 默认的灰色圆点 */
.mockup-browser-toolbar:before {
    content: none !important;
}

/* 确保所有父容器都不裁剪内容 */
.mockup-browser,
.mockup-browser .mockup-browser-toolbar,
.mockup-browser .mockup-browser-toolbar > div {
    overflow: visible !important;
}

/* 调整 tooltip 的样式和位置 */
.mockup-browser .mockup-browser-toolbar .tooltip:before {
    z-index: 999;
}

.mockup-browser .mockup-browser-toolbar .tooltip:after {
    z-index: 999;
}

/* 为按钮添加相对定位，确保 tooltip 正确定位 */
.mockup-browser .mockup-browser-toolbar button {
    position: relative;
    cursor: pointer;
    transition: opacity 0.2s;
}

.mockup-browser .mockup-browser-toolbar button:hover {
    opacity: 0.8;
}

/* 修改mockup-browser的圆点颜色 */
.mockup-browser .mockup-browser-toolbar .items-center .icon {
    /* macOS风格的颜色 */
    color: #ff605c; /* 红色 */
}

.mockup-browser .mockup-browser-toolbar .items-center .icon:nth-child(2) {
    color: #ffbd44; /* 黄色 */
}

.mockup-browser .mockup-browser-toolbar .items-center .icon:nth-child(3) {
    color: #00ca4e; /* 绿色 */
}

/* 导航栏宽度变量 */
:root {
    --nav-width: 134px;  /* 左侧导航栏的宽度 */
}