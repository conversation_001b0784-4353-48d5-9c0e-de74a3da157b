/* 环形进度条基础样式 */
[style*="conic-gradient"] {
    transform: rotate(0deg);
}

/* 细线条 */
.progress-ring-thin {
    background: conic-gradient(currentColor var(--value), transparent 0);
    border-radius: 50%;
}

.progress-ring-thin::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    border: 1px solid currentColor;
    opacity: 0.1;
}

/* 粗线条 */
.progress-ring-thick {
    background: conic-gradient(currentColor var(--value), transparent 0);
    border-radius: 50%;
}

.progress-ring-thick::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    border: 3px solid currentColor;
    opacity: 0.1;
}

/* 添加平滑效果 */
.progress-ring-thin,
.progress-ring-thick {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transform: rotate(0deg) translateZ(0);
}

/* 养豆期进度环样式 */
.progress-ring-rest {
    color: oklch(65% 0.2 220);  /* 蓝色 */
}

/* Tooltip 换行样式 */
.tooltip:before {
    white-space: pre-line;
    border-radius: 1rem;
}

/* 导航栏宽度变量 */
:root {
    --nav-width: 134px;  /* 左侧导航栏的宽度 */
}