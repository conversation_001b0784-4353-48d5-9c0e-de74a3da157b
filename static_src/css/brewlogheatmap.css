.heatmap-container {
    background: transparent;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    position: relative;
    display: flex;
    overflow: hidden;
}

.heatmap-fixed-left {
    position: absolute;
    left: 1.5rem;
    top: 3.5rem;
    width: 3rem;
    z-index: 10;
    background: inherit;
}

.heatmap-wrapper {
    position: relative;
    padding: 2rem 0 0 2rem;
    min-width: 900px;
    height: 160px;
}

.heatmap-scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
}

.heatmap-grid {
    display: grid;
    grid-template-rows: repeat(7, 1fr);
    grid-template-columns: repeat(53, 1fr);  /* 改为53列以容纳额外的一周 */
    gap: 2px;
}

.heatmap-cell {
    width: 15px;
    height: 15px;
    border-radius: 2px;
    cursor: pointer;
    transition: transform 0.1s ease;
}

.heatmap-cell:hover {
    transform: scale(1.2);
}

.month-labels {
    position: absolute;
    top: 0;
    left: 1rem;
    right: 0;
    display: grid;
    grid-template-columns: repeat(53, 1fr);
    gap: 3px;
    padding: 0 0.5rem;
}

.month-label:nth-child(1) { grid-column: 1 / span 5; }     /* 1月 */
.month-label:nth-child(2) { grid-column: 6 / span 4; }     /* 2月 */
.month-label:nth-child(3) { grid-column: 10 / span 4; }    /* 3月 */
.month-label:nth-child(4) { grid-column: 14 / span 5; }    /* 4月 */
.month-label:nth-child(5) { grid-column: 19 / span 4; }    /* 5月 */
.month-label:nth-child(6) { grid-column: 23 / span 4; }    /* 6月 */
.month-label:nth-child(7) { grid-column: 27 / span 5; }    /* 7月 */
.month-label:nth-child(8) { grid-column: 32 / span 4; }    /* 8月 */
.month-label:nth-child(9) { grid-column: 36 / span 4; }    /* 9月 */
.month-label:nth-child(10) { grid-column: 40 / span 5; }   /* 10月 */
.month-label:nth-child(11) { grid-column: 45 / span 4; }   /* 11月 */
.month-label:nth-child(12) { grid-column: 49 / span 5; }   /* 12月 */

.month-label {
    font-size: 0.75rem;
    color: var(--tw-text-black/60);
    text-align: center;
}

.weekday-labels {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0 0 0.4rem 0;
}

.weekday-label {
    font-size: 0.75rem;
    color: var(--tw-text-black/60);
}

@media (max-width: 1023px) {
    .heatmap-fixed-left {
        position: absolute;
        left: 1.5rem;
        top: 3.5rem;
        background: inherit;
        padding-left: 0.5rem;
        width: 2.5rem;
    }
    
    .heatmap-scroll-container {
        padding-left: 2.5rem;
        position: relative;
    }
    
    .heatmap-container::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4rem;
        background: inherit;
        z-index: 5;
    }
    
    .month-labels {
        display: grid;
        grid-template-columns: repeat(53, 1fr);
        gap: 3px;
        padding: 0 0.5rem;
    }
    
    .month-label {
        transform: none;
        width: auto;
        flex: none;
    }
}