@import "tailwindcss";
@config "../../tailwind.config.js";

@plugin "@tailwindcss/typography" {
  className: "prose";
  target: "modern";
}
@plugin "@tailwindcss/forms";
@plugin "@tailwindcss/aspect-ratio";
@plugin "@iconify/tailwind4";

@plugin "daisyui" {
  themes: light --default, dracula --prefersdark;
}
@plugin "daisyui/theme" {
  name: "cupcake";
  default: true;
  --color-primary: '#693C24';
  --color-primary-content: '#FFFFFF';
  --color-secondary: '#D4A373';
  --color-secondary-content: '#2D1810';
  --color-accent: '#CFAA7E';
  --color-accent-content: '#2D1810';
  --color-info: '#2B68E8';
  --color-info-content: '#FFFFFF';
  --color-error: '#CF5551';
  --color-error-content: '#FFFFFF';
  --border: 1px;
  --root-bg: transparent;
}
@plugin "daisyui/theme" {
  name: "dracula";
  default: false;
  --color-primary: '#A6814C';
  --color-primary-content: '#1D1810';
  --color-secondary: '#F2E2C9';
  --color-secondary-content: '#2D1810';
  --color-accent: '#E3BB8F';
  --color-accent-content: '#2D1810';
  --color-error: '#FF7F7B';
  --color-error-content: '#1D1810';
  --root-bg: oklch(28.822% .022 277.508);
}
@custom-variant dark (&:where([data-theme=dracula], [data-theme=dracula] *));

[data-theme="cupcake"] {
  .btn-square {
    border-start-start-radius: 0.5rem;
    border-start-end-radius: 0.5rem;
    border-end-end-radius: 0.5rem;
    border-end-start-radius: 0.5rem;
  }
  .stat-title {
    font-size: 1rem;
  }
  .textarea {
    border-radius: var(--radius-selector);
  }
}

/* remove summary's default icon */
details summary::-webkit-details-marker {
  display: none;
}

/* remove footnote decoration, and hightlight ref */
.footnote-ref {
  border-bottom: none;
  color: #b94a48;
}

.footnote-backref {
  border-bottom: none;
}

/* override navbar menu dropdown title text-wrap */
.menu :where(li:not(.menu-title)>:not(ul):not(details):not(.menu-title)) {
  text-wrap: nowrap;
}

/* https: //talk.commonmark.org/t/css-for-admonition-extension/3545/2 */
.admonition {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
  text-align: left;
}

.admonition.note {
  color: #3a87ad;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.admonition.warning {
  color: #c09853;
  background-color: #fcf8e3;
  border-color: #fbeed5;
}

.admonition.danger {
  color: #b94a48;
  background-color: #f2dede;
  border-color: #eed3d7;
}

.admonition-title {
  font-weight: bold;
  text-align: left;
}

/* 添加主题切换过渡效果 */
html {
  transition: background-color 0.15s ease-in-out;
}

/* 马克笔效果高亮文本 */
.bg-light-highlight {
  background-color: #E0F7FA;
  border-radius: 4px;
  padding: 2px;
}

.bg-dark-highlight {
  background-color: #004D40;
  border-radius: 4px;
  padding: 2px;
}