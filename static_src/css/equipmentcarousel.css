.carousel {
    display: flex;
    width: 100%;
    height: 100%;
  }
  
  .carousel-item {
    position: relative;
    flex: 0 0 100%;
    width: 100%;
    height: 100%;
    display: none; /* 默认隐藏所有轮播项 */
  }
  
  .carousel-item:first-child {
    display: block; /* 默认显示第一个轮播项 */
  }
  
  .carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    position: relative;
    opacity: 0; /* 初始隐藏图片 */
  }
  
  .skeleton {
    z-index: 1;
  }
  
  .thumbnail-btn.active {
    border-color: currentColor;
    opacity: 1;
  }
  
  .thumbnail-btn:not(.active) {
    opacity: 0.6;
  }
  
  .thumbnail-btn:hover {
    opacity: 0.8;
  }