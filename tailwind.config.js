/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./**/templates/**/*.html"
  ],
  theme: {
    extend: {
      typography: {
        DEFAULT: {
          css: {
            figcaption: {
              'text-align': 'center',
              'margin-top': '0',
            },
            img: {
              margin: 'auto',
            },
            a: {
              'text-decoration': 'none',
              'font-weight': '600',
              'border-bottom': '1px solid #2B68E8',
              '&:hover': {
                'border-bottom-width': '2px',
              },
              'img': {
                'outline-style': 'dotted',
                'outline-width': '1px',
                'padding': '2px',
              },
            },
            sup: {
              '&::before': {
                'content': '"["',
                'color': '#b94a48',
              },
              '&::after': {
                'content': '"]"',
                'color': '#b94a48',
              },
            },
            h2: {
              '&::before': {
                'content': '"# "',
                'opacity': '.2',
                'font-weight': '700',
                'font-size': '1rem',
                'line-height': '1.5rem',
                'vertical-align': 'middle',
                'display': 'inline-block',
                'margin-right': '.25rem',
                'margin-top': '-.25rem',
                'position': 'relative',
              },
            },
            h3: {
              '&::before': {
                'content': '"## "',
                'opacity': '.2',
                'font-weight': '700',
                'font-size': '1rem',
                'line-height': '1.5rem',
                'vertical-align': 'middle',
                'display': 'inline-block',
                'margin-right': '.25rem',
                'margin-top': '-.25rem',
                'position': 'relative',
              },
            },
          },
        },
      },
    },
  },
}