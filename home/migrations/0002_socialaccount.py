# Generated by Django 4.2.7 on 2025-02-24 14:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SocialAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(help_text='例如：微信、小红书等', max_length=50, verbose_name='平台名称')),
                ('name', models.CharField(blank=True, help_text='用户名或UID', max_length=100, verbose_name='平台账号')),
                ('link', models.URLField(blank=True, help_text='平台主页或内容链接', verbose_name='链接地址')),
                ('qrimg', models.URLField(blank=True, default='', help_text='完整的图片URL地址，包含updatedAt参数', verbose_name='二维码图片地址')),
            ],
            options={
                'verbose_name': '社交账号',
                'verbose_name_plural': '社交账号',
            },
        ),
    ]
