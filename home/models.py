from django.db import models
from wagtail.models import Page
from wagtail.admin.panels import FieldPanel
from wagtailmarkdown.fields import MarkdownField
from article.models import ArticlePage
from bean.models import RoastedBeanPage
from recipe.models import RecipePage
from equipment.models import EspressoPage
from wagtail.snippets.models import register_snippet
from wagtail.images.models import Image

class HomePage(Page):
    body = MarkdownField(blank=True)

    def get_context(self, request):
        context = super().get_context(request)
        articles = ArticlePage.objects.live().order_by('-date')[:30]
        beans = RoastedBeanPage.objects.live().order_by('-date')[:5]
        recipes = RecipePage.objects.live().order_by('-date')[:5]
        espressomachines = EspressoPage.objects.live().order_by('-date')[:5]
        context['articles'] = articles
        context['beans'] = beans
        context['recipes'] = recipes
        context['espressomachines'] = espressomachines
        return context

    content_panels = Page.content_panels + [
        FieldPanel('body', classname="full"),
    ]

@register_snippet
class SocialAccount(models.Model):
    platform = models.CharField(
        max_length=50,
        verbose_name="平台名称",
        help_text="例如：微信、小红书等"
    )
    name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="平台账号",
        help_text="用户名或UID"
    )
    link = models.URLField(
        blank=True,
        verbose_name="链接地址",
        help_text="平台主页或内容链接"
    )
    qrimg = models.URLField(
        verbose_name="二维码图片地址",
        help_text="完整的图片URL地址，包含updatedAt参数",
        blank=True,
        default=""
    )

    panels = [
        FieldPanel('platform'),
        FieldPanel('name'),
        FieldPanel('link'),
        FieldPanel('qrimg'),
    ]

    def __str__(self):
        return f"{self.platform} - {self.name or '未设置账号'}"

    class Meta:
        verbose_name = "社交账号"
        verbose_name_plural = verbose_name

    @classmethod
    def get_by_id(cls, id):
        """根据ID获取社交账号"""
        try:
            return cls.objects.get(id=id)
        except cls.DoesNotExist:
            return None

    @classmethod
    def get_all(cls):
        """获取所有社交账号"""
        return cls.objects.all()