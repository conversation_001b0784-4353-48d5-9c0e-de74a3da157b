{% extends "base.html" %}
{% load static %}
{% load wagtailcore_tags wagtailimages_tags wagtailmarkdown %}
{% load custom_timesince remove_md_format %}

{% block body_class %}bg-base-100{% endblock %}

{% block content %}

<div class="max-w-screen-xl mx-auto p-2 md:p-4 lg:p-8 grid grid-cols-1 gap-4 lg:gap-8 lg:grid-cols-3 bg-base-100">
    <!-- Articles -->
    <div class="lg:col-span-2">
        <div class="mx-auto lg:w-full">
            <ul class="divide-y divide-base-300">
                {% for article in articles %}
                {% with post=article.specific %}
                <li class="p-4 hover:bg-base-200 text-base-content">
                    <div class="flex">
                        <a href="{% pageurl post %}" class="link link-hover hover:text-info" target="_blank">
                            <h3 class="text-lg inline">{{ post.title }}</h3>
                            <span class="text-sm opacity-40 ml-2 inline-block">{{ post.date|custom_timesince }}</span>
                        </a>
                    </div>
                    <p class="hidden sm:block mt-2 opacity-60">{{ post.body|remove_markdown|truncatechars:120 }}</p>
                </li>
                {% endwith %}
                {% endfor %}
            </ul>
        </div>
    </div>
    <!-- End Articles -->
    <!-- Right Col -->
    <div class="px-4 pb-8 lg:px-5 lg:col-span-1">
        <!-- Recipes -->
        <div class="md:max-w-md">
            <div class="divider divider-start menu-title">最新配方</div>
            <div class="flex flex-col gap-8 lg:gap-4 md:gap-4">
            {% for recipe in recipes %}
            <a href="{% pageurl recipe %}" class="group relative flex h-48 items-end justify-end overflow-hidden rounded-lg bg-gray-100 shadow-lg md:h-96">
                <img src="{{ recipe.cover_image }}" loading="lazy" alt="{{ recipe.title }}的做法" class="absolute inset-0 h-full w-full object-cover object-center transition duration-200 group-hover:scale-110" />
                <div class="pointer-events-none absolute inset-0 bg-gradient-to-t from-gray-800 via-transparent to-transparent opacity-50"></div>
                <span class="relative mr-3 mb-3 inline-block rounded-lg border border-gray-500 px-2 py-1 text-xs text-gray-200 backdrop-blur-sm md:px-3 md:text-sm">{{ recipe.title }}</span>
            </a>
            {% endfor %}
            </div>
        </div>
        <!-- End Recipes -->
        <!-- EspressoMachines -->
        <div class="md:max-w-md">
            <div class="divider divider-start menu-title">意式机</div>
            <div class="flex flex-col gap-8 lg:gap-4 md:gap-4">
                {% for espresso in espressomachines %}
                <a href="{% pageurl espresso %}" class="flex bg-white dark:bg-base-300 transition hover:shadow-xl">
                    <div class="rotate-180 p-2 [writing-mode:_vertical-lr] opacity-50">
                        <time x-data="{ datetime: '{{ espresso.date }}' }"
                            class="flex items-center justify-between gap-4 text-xs font-bold uppercase text-base-content">
                            <span x-text="datetime.split('年')[0]"></span>
                            <span class="w-px flex-1 bg-base-content opacity-60"></span>
                            <span x-text="datetime.split('年')[1].trim().split('月')[0]"></span>
                        </time>
                    </div>
                    <div class="hidden sm:block sm:basis-56">
                        <div class="border-s border-gray-900/10 p-4 sm:border-l-transparent sm:p-6 text-base-content">
                            <h3>
                                {{ espresso.product_name }}
                            </h3>
                            <p class="mt-2 text-sm opacity-60">
                                {{ espresso.zh_brand }} · {{ espresso.en_brand }}
                            </p>
                        </div>
                    </div>
                    <div class="flex flex-1 flex-col justify-between">
                        <img alt="{{ espresso.title }}" src="{% with first_image_url=espresso.images.splitlines|first %}{{ first_image_url }}{% endwith %}" class="aspect-square h-full w-full object-cover" decoding="async" loading="lazy" />
                    </div>
                </a>
                {% endfor %}
            </div>
        </div>
        <!-- End EspressoMachines -->
        <!-- Beans -->
        <div class="md:max-w-md">
            <div class="divider divider-start menu-title">新品速递</div>
            <div class="flex flex-col gap-8 lg:gap-4 md:gap-4">
                {% for bean in beans %}
                <a href="{% pageurl bean %}" class="flex bg-white dark:bg-base-300 transition hover:shadow-xl">
                    <div class="rotate-180 p-2 [writing-mode:_vertical-lr] opacity-50">
                        <time x-data="{ datetime: '{{ bean.date }}' }"
                            class="flex items-center justify-between gap-4 text-xs font-bold uppercase text-base-content">
                            <span x-text="datetime.split('年')[0]"></span>
                            <span class="w-px flex-1 bg-base-content opacity-60"></span>
                            <span x-text="datetime.split('年')[1].trim().split(' ')[0].replace('月', '.').replace('日', '')"></span>
                        </time>
                    </div>
                    <div class="hidden sm:block sm:basis-56">
                        <div class="border-s border-gray-900/10 p-4 sm:border-l-transparent sm:p-6 text-base-content">
                            <h3>
                                {{ bean.product_name }}
                            </h3>
                            <p class="mt-2 text-sm opacity-60">
                                {{ bean.roaster }}
                            </p>
                        </div>
                    </div>
                    <div class="flex flex-1 flex-col justify-between">
                        <img alt="{{ bean.title }}" src="{{ bean.package_image }}" class="aspect-square h-full w-full object-cover" decoding="async" loading="lazy" />
                    </div>
                </a>
                {% endfor %}
            </div>
        </div>
        <!-- End Beans -->
        {% if not user.is_authenticated %}
        <!-- ggad_hp_vrt -->
        <div class="pt-4 lg:sticky lg:top-0">
            <ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-2270384440206851" data-ad-slot="7724865510"
                data-ad-format="auto" data-full-width-responsive="true"></ins>
        </div>
        <!-- End ggad_hp_vrt -->
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener("DOMContentLoaded", function () {
        (adsbygoogle = window.adsbygoogle || []).push({});
    });
</script>
{% endblock %}